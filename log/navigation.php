<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jyotish 案例导航中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .header h1 {
            font-size: 3em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }
        
        .header p {
            font-size: 1.2em;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        
        .nav-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .nav-card:hover {
            transform: translateY(-8px);
            border-color: #667eea;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
        }
        
        .nav-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .nav-card:hover:before {
            left: 100%;
        }
        
        .nav-icon {
            font-size: 3.5em;
            margin-bottom: 20px;
            display: block;
        }
        
        .nav-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .nav-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .nav-card .features {
            list-style: none;
            text-align: left;
            margin: 15px 0;
        }
        
        .nav-card .features li {
            color: #555;
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
            font-size: 0.9em;
        }
        
        .nav-card .features li:before {
            content: "▶";
            color: #667eea;
            position: absolute;
            left: 0;
            font-size: 0.8em;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }
        
        .quick-access {
            background: linear-gradient(135deg, #e8f4f8, #f0f8ff);
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            text-align: center;
        }
        
        .quick-access h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .quick-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        
        .status-indicator.warning {
            background: #ffc107;
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
        }
        
        .footer p {
            color: #666;
            margin-bottom: 20px;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #764ba2;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2.2em;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧭 Jyotish 案例导航中心</h1>
            <p>
                欢迎来到 Jyotish 占星学案例展示中心！这里汇集了完整的印度占星学编程案例，
                从基础模块到高级应用，助您快速掌握占星学编程技能。
            </p>
        </div>
        
        <div class="nav-grid">
            <div class="nav-card" onclick="location.href='jyotish_showcase.php'">
                <div class="status-indicator"></div>
                <span class="nav-icon">🌟</span>
                <h3>主展示页面</h3>
                <p>完整的案例展示中心，包含所有功能模块的详细介绍和演示。</p>
                <ul class="features">
                    <li>美观的界面设计</li>
                    <li>完整的功能介绍</li>
                    <li>实时演示效果</li>
                    <li>统计信息展示</li>
                </ul>
                <a href="jyotish_showcase.php" class="btn">进入主页</a>
            </div>
            
            <div class="nav-card" onclick="location.href='simple_jyotish_examples.php'">
                <div class="status-indicator"></div>
                <span class="nav-icon">📚</span>
                <h3>简化案例库</h3>
                <p>精简版的案例展示，包含核心功能的代码示例和使用说明。</p>
                <ul class="features">
                    <li>Base 基础模块</li>
                    <li>Calendar 日历系统</li>
                    <li>Draw 图形绘制</li>
                    <li>简洁的代码示例</li>
                </ul>
                <a href="simple_jyotish_examples.php" class="btn">查看案例</a>
            </div>
            
            <div class="nav-card" onclick="location.href='correct_natal_chart.php'">
                <div class="status-indicator"></div>
                <span class="nav-icon">🎨</span>
                <h3>精确占星图表</h3>
                <p>完全按照传统印度占星学格式绘制的图表，内部正方形角点正确靠边。</p>
                <ul class="features">
                    <li>传统12宫位布局</li>
                    <li>精确的行星位置</li>
                    <li>专业的图表样式</li>
                    <li>完整的出生数据</li>
                </ul>
                <a href="correct_natal_chart.php" class="btn">查看图表</a>
            </div>
            
            <div class="nav-card" onclick="location.href='custom_natal_chart.php'">
                <div class="status-indicator"></div>
                <span class="nav-icon">🛠️</span>
                <h3>自定义图表生成</h3>
                <p>支持用户输入自定义数据，实时生成个性化的占星图表。</p>
                <ul class="features">
                    <li>自定义出生信息</li>
                    <li>可调整行星位置</li>
                    <li>实时图表预览</li>
                    <li>多种参数设置</li>
                </ul>
                <a href="custom_natal_chart.php" class="btn">自定义图表</a>
            </div>
            
            <div class="nav-card" onclick="location.href='test_fix.php'">
                <div class="status-indicator"></div>
                <span class="nav-icon">🧪</span>
                <h3>功能测试中心</h3>
                <p>全面测试系统的各项功能，确保图表生成的准确性和稳定性。</p>
                <ul class="features">
                    <li>渲染器功能测试</li>
                    <li>图表绘制验证</li>
                    <li>错误修复检查</li>
                    <li>性能基准测试</li>
                </ul>
                <a href="test_fix.php" class="btn">运行测试</a>
            </div>
            
            <div class="nav-card" onclick="location.href='jyotish_summary.php'">
                <div class="status-indicator"></div>
                <span class="nav-icon">📋</span>
                <h3>项目总结</h3>
                <p>查看完整的项目概览，包括所有模块的功能介绍和技术架构。</p>
                <ul class="features">
                    <li>模块功能总览</li>
                    <li>技术架构说明</li>
                    <li>使用指南文档</li>
                    <li>最佳实践建议</li>
                </ul>
                <a href="jyotish_summary.php" class="btn">查看总结</a>
            </div>
            
            <div class="nav-card" onclick="location.href='debug_comparison.php'">
                <div class="status-indicator warning"></div>
                <span class="nav-icon">🔧</span>
                <h3>调试工具</h3>
                <p>专业的调试和对比工具，帮助分析图表生成过程和结果差异。</p>
                <ul class="features">
                    <li>图表对比分析</li>
                    <li>布局修正验证</li>
                    <li>错误诊断工具</li>
                    <li>性能监控面板</li>
                </ul>
                <a href="debug_comparison.php" class="btn">使用工具</a>
            </div>
            
            <div class="nav-card" onclick="location.href='test_jyotish_examples.php'">
                <div class="status-indicator"></div>
                <span class="nav-icon">⚡</span>
                <h3>快速测试</h3>
                <p>快速检查系统状态，验证所有文件是否正常，语法是否正确。</p>
                <ul class="features">
                    <li>文件存在性检查</li>
                    <li>PHP语法验证</li>
                    <li>系统信息显示</li>
                    <li>快速链接导航</li>
                </ul>
                <a href="test_jyotish_examples.php" class="btn">快速测试</a>
            </div>
        </div>
        
        <div class="quick-access">
            <h3>🚀 快速访问</h3>
            <div class="quick-buttons">
                <a href="jyotish_showcase.php" class="btn">🌟 主展示页</a>
                <a href="correct_natal_chart.php" class="btn">🎨 查看图表</a>
                <a href="simple_jyotish_examples.php" class="btn">📚 学习案例</a>
                <a href="test_fix.php" class="btn secondary">🧪 运行测试</a>
            </div>
        </div>
        
        <div class="footer">
            <p>
                🌟 感谢您使用 Jyotish 占星学案例中心！我们致力于提供最专业、最完整的印度占星学编程解决方案。
            </p>
            <div class="footer-links">
                <a href="jyotish_showcase.php">主展示页</a>
                <a href="simple_jyotish_examples.php">案例库</a>
                <a href="jyotish_summary.php">项目总结</a>
                <a href="test_fix.php">功能测试</a>
                <a href="debug_comparison.php">调试工具</a>
            </div>
        </div>
    </div>
    
    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.nav-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // 添加点击统计
        document.querySelectorAll('.nav-card, .btn').forEach(element => {
            element.addEventListener('click', function() {
                console.log('用户访问:', this.querySelector('h3')?.textContent || this.textContent);
            });
        });
        
        // 检查页面状态
        function checkPageStatus() {
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(indicator => {
                // 这里可以添加实际的状态检查逻辑
                indicator.style.animation = 'pulse 2s infinite';
            });
        }
        
        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        checkPageStatus();
    </script>
</body>
</html>
