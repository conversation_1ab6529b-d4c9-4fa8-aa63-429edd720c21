<?php
/**
 * 简化版 Jyotish 案例演示
 * 位置: E:\www\log\simple_jyotish_examples.php
 * 
 * 这是一个简化版本，确保没有语法错误
 */

// 检查请求的案例类型
$example = $_GET['example'] ?? 'index';

switch ($example) {
    case 'base':
        showBaseExamples();
        break;
    case 'calendar':
        showCalendarExamples();
        break;
    case 'draw':
        showDrawExamples();
        break;
    default:
        showIndex();
        break;
}

/**
 * 显示主索引页面
 */
function showIndex() {
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Jyotish 简化案例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .example-card {
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .example-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        .example-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Jyotish 简化案例演示</h1>
        
        <div class="examples-grid">
            <div class="example-card" onclick="location.href='?example=base'">
                <h3>🏗️ Base 基础模块</h3>
                <p>提供核心的数据结构、常量定义和基础功能类</p>
                <ul>
                    <li>Data - 基础数据类</li>
                    <li>Constants - 常量定义</li>
                    <li>Utils - 工具函数</li>
                </ul>
                <a href="?example=base" class="btn">查看案例</a>
            </div>
            
            <div class="example-card" onclick="location.href='?example=calendar'">
                <h3>📅 Calendar 日历系统</h3>
                <p>处理各种日历系统的转换和计算</p>
                <ul>
                    <li>Gregorian - 公历</li>
                    <li>Hindu - 印度历</li>
                    <li>Panchanga - 五要素</li>
                </ul>
                <a href="?example=calendar" class="btn">查看案例</a>
            </div>
            
            <div class="example-card" onclick="location.href='?example=draw'">
                <h3>🎨 Draw 图形绘制</h3>
                <p>生成各种占星图表和可视化图形</p>
                <ul>
                    <li>Chart - 星盘绘制</li>
                    <li>Renderer - 渲染器</li>
                    <li>Style - 样式设置</li>
                </ul>
                <a href="?example=draw" class="btn">查看案例</a>
            </div>
        </div>
        
        <div style="text-align: center; margin: 40px 0;">
            <h2>🚀 开始探索</h2>
            <p>选择上面任意一个模块开始学习 Jyotish 占星学编程</p>
            <a href="correct_natal_chart.php" class="btn" style="font-size: 18px; padding: 15px 30px;">查看占星图表</a>
        </div>
    </div>
</body>
</html>
<?php
}

/**
 * Base 基础模块案例
 */
function showBaseExamples() {
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Base 基础模块案例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        h1 { color: #333; text-align: center; border-bottom: 3px solid #007cba; padding-bottom: 15px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; margin: 15px 0; overflow-x: auto; font-family: 'Courier New', monospace; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ Base 基础模块案例</h1>
        
        <div class="section">
            <h2>📋 模块概述</h2>
            <p>Base 模块提供了 Jyotish 库的核心基础设施，包括数据结构、常量定义、工具函数等。</p>
        </div>
        
        <div class="section">
            <h2>💻 代码案例</h2>
            
            <h3>1. Data 基础数据类</h3>
            <div class="code-block">
&lt;?php
namespace Jyotish\Base;

class Data {
    protected $data = [];
    
    public function setData($key, $value) {
        $this->data[$key] = $value;
        return $this;
    }
    
    public function getData($key = null) {
        if (is_null($key)) {
            return $this->data;
        }
        return isset($this->data[$key]) ? $this->data[$key] : null;
    }
}

// 使用示例
$data = new Data();
$data->setData('name', 'Jyotish Chart')
     ->setData('date', '2024-01-15');
echo $data->getData('name');
?&gt;
            </div>
            
            <h3>2. Utils 工具函数</h3>
            <div class="code-block">
&lt;?php
namespace Jyotish\Base;

class Utils {
    public static function normalizeDegree($degree) {
        while ($degree < 0) {
            $degree += 360;
        }
        while ($degree >= 360) {
            $degree -= 360;
        }
        return $degree;
    }
    
    public static function dmsToDecimal($degrees, $minutes, $seconds) {
        return $degrees + ($minutes / 60) + ($seconds / 3600);
    }
}

// 使用示例
$normalized = Utils::normalizeDegree(375); // 结果: 15
$decimal = Utils::dmsToDecimal(25, 30, 45); // 结果: 25.5125
?&gt;
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="?example=calendar" class="btn">下一个：Calendar 模块</a>
            <a href="?" class="btn">返回主页</a>
        </div>
    </div>
</body>
</html>
<?php
}

/**
 * Calendar 日历系统案例
 */
function showCalendarExamples() {
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Calendar 日历系统案例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        h1 { color: #333; text-align: center; border-bottom: 3px solid #007cba; padding-bottom: 15px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; margin: 15px 0; overflow-x: auto; font-family: 'Courier New', monospace; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📅 Calendar 日历系统案例</h1>
        
        <div class="section">
            <h2>📋 模块概述</h2>
            <p>Calendar 模块处理各种日历系统之间的转换，包括公历、儒略历、印度历等。</p>
        </div>
        
        <div class="section">
            <h2>💻 代码案例</h2>
            
            <h3>1. Gregorian 公历处理</h3>
            <div class="code-block">
&lt;?php
namespace Jyotish\Calendar;

class Gregorian {
    protected $year, $month, $day;
    
    public function __construct($year, $month, $day) {
        $this->year = $year;
        $this->month = $month;
        $this->day = $day;
    }
    
    public function toJulianDay() {
        $a = floor((14 - $this->month) / 12);
        $y = $this->year - $a;
        $m = $this->month + 12 * $a - 3;
        
        return $this->day + floor((153 * $m + 2) / 5) + 365 * $y + 
               floor($y / 4) - floor($y / 100) + floor($y / 400) + 1721119;
    }
}

// 使用示例
$date = new Gregorian(2024, 1, 15);
$julianDay = $date->toJulianDay();
?&gt;
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="?example=draw" class="btn">下一个：Draw 模块</a>
            <a href="?" class="btn">返回主页</a>
        </div>
    </div>
</body>
</html>
<?php
}

/**
 * Draw 图形绘制案例
 */
function showDrawExamples() {
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Draw 图形绘制案例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        h1 { color: #333; text-align: center; border-bottom: 3px solid #007cba; padding-bottom: 15px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .demo-section { text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Draw 图形绘制案例</h1>
        
        <div class="section">
            <h2>📋 模块概述</h2>
            <p>Draw 模块提供强大的图形绘制功能，用于生成各种占星图表、星盘和可视化图形。</p>
        </div>
        
        <div class="demo-section">
            <h3>🖼️ 占星图表演示</h3>
            <p>查看我们生成的占星图表：</p>
            <p>
                <a href="correct_natal_chart.php" target="_blank" class="btn">查看占星图表</a>
                <a href="perfect_natal_chart.php" target="_blank" class="btn">查看完美版本</a>
            </p>
        </div>
        
        <div class="section">
            <h2>🎯 功能特点</h2>
            <ul>
                <li>支持北印度和南印度风格的星盘</li>
                <li>可自定义颜色和样式</li>
                <li>支持多种输出格式</li>
                <li>精确的行星位置计算</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="?" class="btn">返回主页</a>
            <a href="correct_natal_chart.php" class="btn">查看图表</a>
        </div>
    </div>
</body>
</html>
<?php
}
?>
