*{
    padding: 0;
    margin: 0;
    -webkit-font-smoothing: antialiased; /*chrome、safari字体平滑*/
    -moz-osx-font-smoothing: grayscale;/*firefox*/
    -webkit-touch-callout:none;
 
}
a{
    text-decoration:none ;
}
body{
    background: #e4f3fc;
    min-width: 940px;
    min-height: 930px;
    width: 100%;
    height: 100%;
}
#title{
    background: #fff;
    position: relative;
    height: 70px;
    display: block;
    padding-top: 0;
    padding-right: 10px;
    padding-left: 10px;
    padding-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    justify-content: space-around;
    align-items: center;
    min-width: 930px;
}
#title_k{
    position: absolute;
    min-width: 930px;
    height: 70px;
}
#title_logo{
    display: block;
    position: absolute;
    width: auto;
    height: 47px;
    padding: 1px;
    left: 0px;
    top: 10px;
}
#title_phone{
    display: block;
    position: absolute;
    width: auto;
    height: 25px;
    padding: 1px;
    left: 755px;
    top:20px;
}
#pui-contact-label{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    display: block;
    position: absolute;
    font-weight: bold;
    color: #333333;
    left: 783px;
    top:13px;
}
#pui-contact-phone{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    display: block;
    position: absolute;
    font-size: 14px;
    color: #333333;
    left: 783px;
    top:35px;
}
#html-is-parent{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    display: block;
    position: absolute;
    border-radius: 2px;
    color: #2574a9;
    right: 0px;
    top:10px;
    font-size: 16px;
    text-decoration: none;
    padding: 14PX 10PX;
}
#html-is-parent:hover{
    background: rgba(156, 192, 236, 0.1);
    cursor:pointer;

}
#content{
    background-color: #e4f3fc;
    position: relative;
    min-height: 700px;
    display: block;
    padding-top: 0;
    padding-right: 10px;
    padding-left: 10px;
    padding-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    justify-content: space-around;
    align-items: center; 
    min-width: 930px;  
}
#content_k{
    position: absolute;
    min-width: 930px;
    min-height: 700px;
}
#content_p{
    position: absolute;
    top:15px;
}
#html-heading-main{
    position: absolute;
    left: 70px;
    top:5px;
    margin: 0 0 5px 0;
    color: #003366;
    font-weight: bold;
    line-height: 1.4;
    font-size: 23px;
    font-weight: normal;
    display: block;
    margin-block-start: 0.67em;
    margin-block-end: 0.67em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;

}
#content_w{
    position: absolute;
    top:15px;
    right: 50px;
}
#content_help{
    position: absolute;
    top:20px;
    right: 0px;
    color: #2574a9;
    display: inline-block;
    vertical-align: middle;
    font-size: 15px;
}
#content_help:hover{
    cursor:pointer;
    border-bottom: 1px solid #5ba3c0;
    color: #44a2c7;
}
#content_pc{
    z-index: 1;
    position: absolute;
    top: 70px;
    width: 240px;
    height: 50px;
    background: #fff;
    text-align: center;
    display: flex;
    justify-content: space-around;
    align-items: center; 
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;

}
#content_pc span{
    padding: 15px 24px;
    cursor: pointer;
}
#content_pc span:nth-child(2){
    background: #2574a9;
    color: #fff;
}
#content_np{
    background: #fff;
    width: 458px;
    height: 230px;
    position: absolute;
    top:120px
}
input{
    z-index: 1;
    display: inline-block;
    line-height: 1;
    padding: 0 10px;
    background-color: #ffffff;
    border: 1px solid #949494;
    color: #333333;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-weight: normal;
    font-size: 14px;
    vertical-align: middle;
    border-radius: 3px;
    outline: none;
    width: 220px;
    height: 35px;
}
#content_np_div1{
    position: absolute;
    left: 22px;
    top: 35px;
    width: 435px;
    height: 40px;
    user-select:none;
}
#content_np_div1 span{
    position: absolute;
    top: 9px;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
    color: #333333;
}
#content_np_div1 input{
    position: absolute;
    left: 140px;
}
#content_np_div1 img{
    z-index: 10;
    position: absolute;
    left: 385px;
    top: 3px;
    cursor: pointer;
}
#content_np_div2{
    position: absolute;
    top: 90px;
    left: 22px;
    width: 435px;
    height: 40px;
    user-select:none;
}
#content_np_div2 span{
    position: absolute;
    top: 9px;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
    color: #333333;
}
#content_np_div2 input{
    position: absolute;
    left: 140px;
}
#content_np_div2 img{
    z-index: 10;
    position: absolute;
    left: 385px;
    top: 3px;
    cursor: pointer;
}
#content_np_div3{
    position: absolute;
    top: 160px;
    left: 22px;
    width: 435px;
    height: 50px;
}
#content_np_div3 a{
    position: absolute;
    top: 9px;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
    color: #2574a9;
}
#content_np_div3 a:hover{
    border-bottom:1px solid #2574a9;
    cursor: pointer;
    color: #44a2c7;
}
#content_np_div3 button{
    position: absolute;
    top: 3px;
    left: 300px;
    color: #ffffff;
    width: 120px;
    min-width: 60px;
    height: 36px;
    padding: 7px 15px;
    text-align: center;
    cursor: pointer;
    border: 0 none;
    font-size: 16px;
    font-weight: normal;
    text-decoration: none;
    border-radius: 3px;
    transition: box-shadow 250ms;
}
#b1{
    background-color: #ff6000;
}
#b2{
    display: none;
    background-color: rgb(51, 51, 51,0.4);
}
#content_ph{
    position: absolute;
    left: 475px;
    top:70px;
    width: 415px;
    height: 115px;
    padding-top: 25px;
    padding-bottom: 30px;
    padding-right: 20px;
    padding-left: 20px;
    background-color: #ffffff;
}
#content_ph h3{
    position: absolute;
    top:5px;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 16px;
    margin: 0 0 5px 0;
    color: #003366;
    font-weight: bold;
    line-height: 1.4;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
}
#content_ph div{
    position: absolute;
    top:50px;
    width: 415px;
    height: auto;
}
#content_ph p{
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
    color: #333333;
    display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    margin: 0 0 20px 0;
    line-height: 1.4;
}
#content_ph a{
    color: #2574a9;
}
#content_ph a:hover{
border-bottom:1px solid #2574a9;
cursor: pointer;
color: #44a2c7;
}
#a1,#a2{
    padding: 0;
    margin: 0;
    position: absolute;
    top:370px;
    text-decoration: none;
    color: #2574a9;
    padding: 0;
    line-height: 1.4;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
}
#a1:hover,#a2:hover{
    border-bottom:1px solid #2574a9;
    cursor: pointer;
    color: #44a2c7;
    }
#a1{
   left: 142px;
}
#a2{
    left: 255px;
}
#d1{
    width: 150px;
    position: absolute;
    top:410px;  
}
#d1 p{
    display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
    color: #333333;
}
#i1,#i2{
    position: absolute;
    top:425px; 
    border-radius: 7px; 
}
#i1{
    left: 185px;
}
#i2{
    left: 320px;
}
#d2{
    position: absolute;
    top:500px;
    width: 930px;
    height: 150px;
    border-top: 1px solid #d8d8d8;
}
#i3{
    position: absolute;
    top: 20px;
}
#d2_d1{
    position: absolute;
    top: 75px;
}
#d2_d1 p{
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
    color: #333333;
}
#d2a1,#d2a2,#d2a3,#d2a4,#d2a5,#d2a6,#d2a7,#d2a8{
    position: absolute;
    color: #2574a9;
    font-family: "Roboto", "Arial", "Helvetica", sans-serif;
    font-size: 14px;
}
#d2a1:hover,#d2a2:hover,#d2a3:hover,#d2a4:hover,#d2a5:hover,#d2a6:hover,#d2a7:hover,#d2a8:hover{
    border-bottom:1px solid #2574a9;
    cursor: pointer;
    color: #44a2c7;
}

#d2a1,#d2a2,#d2a3,#d2a4{
    left: 455px;
}
#d2a5,#d2a6,#d2a7,#d2a8{
    left: 680px;
}
#d2a1{
    top:20px;
}
#d2a2{
    top:40px;
}
#d2a3{
    top:60px;
}
#d2a4{
    top:80px;
}
#d2a5{
    top:20px;
}
#d2a6{
    top:40px;
}
#d2a7{
    top:60px;
}
#d2a8{
    top:80px;
}
#d3{
    position: fixed;
    bottom: 0px;
    left: 15px;
}
#d4{
    display: none;
    position: fixed;
    bottom: 20px;
    left:75px; 
}