function div() {
    var link = document.createElement("div");
    var head = document.getElementsByTagName("body");
    var l = document.createElement("style");
    head[0].appendChild(l);
    l.innerHTML = ".loading { position: absolute;top: 0; left: 0;width: 100%; height: 100%;background-color: rgba(255, 255, 255, 0.8) } .loading-img {width: 50px;height: 50px;position: absolute; left: 0; right: 0;top: 0;bottom: 0;margin: auto;}@keyframes turn { 0% { -webkit-transform: rotate(0deg); }25% { -webkit-transform: rotate(90deg); }50% { -webkit-transform: rotate(180deg);} 75% { -webkit-transform: rotate(270deg); } 100% {-webkit-transform: rotate(360deg); }}.loading-img img { user-select:none; width: 100%; animation: turn 2s linear infinite;} .loading - none { display: none; } ";
    head[0].appendChild(link);
    link.setAttribute("style", "position:absolute;left:0px;top:0px;width:100%;height:100%;background-color:#ffffff;z-index:999;display: flex;justify-content: space-around;align-items: center; ");
    link.setAttribute("id", "divq");
    link.style.width = window.getComputedStyle(head[0]).width;
    link.style.height = "70%";
    link.style.opacity = "0.4";
    link.style.top = "25%";
    link.innerHTML = "<div class='loading'><div class='loading-img'><img src='" + path + "/api/img/qq.png' /></div></div>"
}
function api_name_paswd(name, paswd) {
    div();
    var url = path + "/api/api.php";
    var data = "adname=" + name + "&adpaswd=" + paswd;
    function f(d) {
        if (d != '') {
            y(path+JSON.parse(d)["url"]);
        }
    }
    ajax("post", url, data, f, true);
}