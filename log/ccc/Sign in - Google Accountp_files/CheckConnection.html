
<!-- saved from url=(0126)https://accounts.youtube.com/accounts/CheckConnection?pmpo=https%3A%2F%2Faccounts.google.com&v=5894779&timestamp=************* -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script nonce="">"use strict";this.default_AccountsDomaincookiesCheckconnectionJs=this.default_AccountsDomaincookiesCheckconnectionJs||{};(function(_){var window=this;
try{
var n=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,n);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)},aa=function(a,b){b=p(a,b);var c;(c=0<=b)&&Array.prototype.splice.call(a,b,1);return c},r=function(){var a=q.navigator;return a&&(a=a.userAgent)?a:""},t=function(a){return-1!=r().indexOf(a)},u=function(){return t("Firefox")||t("FxiOS")},v=function(){return(t("Chrome")||t("CriOS"))&&!t("Edge")||t("Silk")},ba=function(a){var b=
{};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}},ca=function(a){var b=r();if("Internet Explorer"===a){if(t("Trident")||t("MSIE"))if((a=/rv: *([\d\.]*)/.exec(b))&&a[1])b=a[1];else{a="";var c=/MSIE +([\d\.]+)/.exec(b);if(c&&c[1])if(b=/Trident\/(\d.\d)/.exec(b),"7.0"==c[1])if(b&&b[1])switch(b[1]){case "4.0":a="8.0";break;case "5.0":a="9.0";break;case "6.0":a="10.0";break;case "7.0":a="11.0"}else a="7.0";else a=c[1];b=a}else b="";return b}var d=
RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g");c=[];for(var e;e=d.exec(b);)c.push([e[1],e[2],e[3]||void 0]);b=ba(c);switch(a){case "Opera":if(t("Opera"))return b(["Version","Opera"]);if(t("OPR"))return b(["OPR"]);break;case "Microsoft Edge":if(t("Edge"))return b(["Edge"]);if(t("Edg/"))return b(["Edg"]);break;case "Chromium":if(v())return b(["Chrome","CriOS","HeadlessChrome"])}return"Firefox"===a&&u()||"Safari"===a&&t("Safari")&&!(v()||t("Coast")||t("Opera")||t("Edge")||t("Edg/")||t("OPR")||
u()||t("Silk")||t("Android"))||"Android Browser"===a&&t("Android")&&!(v()||u()||t("Opera")||t("Silk"))||"Silk"===a&&t("Silk")?(b=c[2])&&b[1]||"":""},w=function(a){a=ca(a);if(""===a)return NaN;a=a.split(".");return 0===a.length?NaN:Number(a[0])},ea=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<da.length;f++)c=da[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}},fa=function(a){x(a)},ja=function(){var a={};a.location=document.location.toString();
if(ha())try{a["top.location"]=top.location.toString()}catch(c){a["top.location"]="[external]"}else a["top.location"]="[external]";for(var b in ia)try{a[b]=ia[b].call()}catch(c){a[b]="[error] "+c.message}return a},ka=function(a,b){-1!=b.message.indexOf("Error in protected function: ")||(b.error&&b.error.stack?x(b.error):a||x(b))},la=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},y=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];
return b?b.call(a):{next:la(a)}},ma="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},z="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},na=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==
Math)return c}throw Error("a");},oa=na(this),A=function(a,b){if(b)a:{var c=oa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&z(c,a,{configurable:!0,writable:!0,value:b})}},B;
if("function"==typeof Object.setPrototypeOf)B=Object.setPrototypeOf;else{var pa;a:{var qa={a:!0},ra={};try{ra.__proto__=qa;pa=ra.a;break a}catch(a){}pa=!1}B=pa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError("b`"+a);return a}:null}var sa=B;
A("Symbol",function(a){if(a)return a;var b=function(f,h){this.g=f;z(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("c");return new b(c+(f||"")+"_"+d++,f)};return e});
A("Symbol.iterator",function(a){if(a)return a;a=Symbol("d");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=oa[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&z(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ta(la(this))}})}return a});
var ta=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},C=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
A("WeakMap",function(a){function b(){}function c(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function d(k){if(!C(k,f)){var l=new b;z(k,f,{value:l})}}function e(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof b)return m;Object.isExtensible(m)&&d(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(D){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,g=function(k){this.g=(h+=Math.random()+1).toString();if(k){k=y(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};g.prototype.set=function(k,l){if(!c(k))throw Error("e");d(k);if(!C(k,f))throw Error("f`"+k);k[f][this.g]=l;return this};g.prototype.get=function(k){return c(k)&&C(k,f)?k[f][this.g]:void 0};g.prototype.has=function(k){return c(k)&&C(k,f)&&C(k[f],this.g)};g.prototype.delete=function(k){return c(k)&&
C(k,f)&&C(k[f],this.g)?delete k[f][this.g]:!1};return g});
A("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var g=Object.seal({x:4}),k=new a(y([[g,"s"]]));if("s"!=k.get(g)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=g||"s"!=m.value[1])return!1;m=l.next();return m.done||4!=m.value[0].x||"t"!=m.value[1]||!l.next().done?!1:!0}catch(D){return!1}}())return a;var b=new WeakMap,c=function(g){this.h={};this.g=f();
this.size=0;if(g){g=y(g);for(var k;!(k=g.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(g,k){g=0===g?0:g;var l=d(this,g);l.list||(l.list=this.h[l.id]=[]);l.j?l.j.value=k:(l.j={next:this.g,o:this.g.o,head:this.g,key:g,value:k},l.list.push(l.j),this.g.o.next=l.j,this.g.o=l.j,this.size++);return this};c.prototype.delete=function(g){g=d(this,g);return g.j&&g.list?(g.list.splice(g.index,1),g.list.length||delete this.h[g.id],g.j.o.next=g.j.next,g.j.next.o=g.j.o,g.j.head=null,this.size--,
!0):!1};c.prototype.clear=function(){this.h={};this.g=this.g.o=f();this.size=0};c.prototype.has=function(g){return!!d(this,g).j};c.prototype.get=function(g){return(g=d(this,g).j)&&g.value};c.prototype.entries=function(){return e(this,function(g){return[g.key,g.value]})};c.prototype.keys=function(){return e(this,function(g){return g.key})};c.prototype.values=function(){return e(this,function(g){return g.value})};c.prototype.forEach=function(g,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=m.value,
g.call(k,m[1],m[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(g,k){var l=k&&typeof k;"object"==l||"function"==l?b.has(k)?l=b.get(k):(l=""+ ++h,b.set(k,l)):l="p_"+k;var m=g.h[l];if(m&&C(g.h,l))for(g=0;g<m.length;g++){var D=m[g];if(k!==k&&D.key!==D.key||k===D.key)return{id:l,list:m,index:g,j:D}}return{id:l,list:m,index:-1,j:void 0}},e=function(g,k){var l=g.g;return ta(function(){if(l){for(;l.head!=g.g;)l=l.o;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,
value:void 0}})},f=function(){var g={};return g.o=g.next=g.head=g},h=0;return c});A("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var h=d[f];if(b.call(c,h,f,d)){b=h;break a}}b=void 0}return b}});
var ua=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};A("Array.prototype.keys",function(a){return a?a:function(){return ua(this,function(b){return b})}});
A("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(g){return g};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var va=va||{},q=this||self,wa="closure_uid_"+(1E9*Math.random()>>>0),xa=0,ya=function(a,b,c){return a.call.apply(a.bind,arguments)},za=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},E=function(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?
E=ya:E=za;return E.apply(null,arguments)},Aa=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}},Ba=function(a){(0,eval)(a)},Ca=function(a,b){a=a.split(".");var c=q;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b},F=function(a,b){function c(){}c.prototype=b.prototype;a.v=b.prototype;
a.prototype=new c;a.prototype.constructor=a;a.Y=function(d,e,f){for(var h=Array(arguments.length-2),g=2;g<arguments.length;g++)h[g-2]=arguments[g];return b.prototype[e].apply(d,h)}};
F(n,Error);n.prototype.name="CustomError";
var p=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},Da=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
var Ea=function(a,b,c){c=c||q;var d=c.onerror,e=!!b;c.onerror=function(f,h,g,k,l){d&&d(f,h,g,k,l);a({message:f,fileName:h,line:g,lineNumber:g,Z:k,error:l});return e}},Ha=function(a){a:{var b=["window","location","href"];var c=q;for(var d=0;d<b.length;d++)if(c=c[b[d]],null==c){c=null;break a}}null==a&&(a='Unknown Error of type "null/undefined"');if("string"===typeof a)return{message:a,name:"Unknown error",lineNumber:"Not available",fileName:c,stack:"Not available"};b=!1;try{var e=a.lineNumber||a.line||
"Not available"}catch(h){e="Not available",b=!0}try{var f=a.fileName||a.filename||a.sourceURL||q.$googDebugFname||c}catch(h){f="Not available",b=!0}c=Fa(a);if(!(!b&&a.lineNumber&&a.fileName&&a.stack&&a.message&&a.name))return b=a.message,null==b&&(b=a.constructor&&a.constructor instanceof Function?'Unknown Error of type "'+(a.constructor.name?a.constructor.name:Ga(a.constructor))+'"':"Unknown Error of unknown type","function"===typeof a.toString&&Object.prototype.toString!==a.toString&&(b+=": "+a.toString())),
{message:b,name:a.name||"UnknownError",lineNumber:e,fileName:f,stack:c||"Not available"};a.stack=c;return{message:a.message,name:a.name,lineNumber:a.lineNumber,fileName:a.fileName,stack:a.stack}},Fa=function(a,b){b||(b={});b[Ia(a)]=!0;var c=a.stack||"";(a=a.cause)&&!b[Ia(a)]&&(c+="\nCaused by: ",a.stack&&0==a.stack.indexOf(a.toString())||(c+="string"===typeof a?a:a.message+"\n"),c+=Fa(a,b));return c},Ia=function(a){var b="";"function"===typeof a.toString&&(b=""+a);return b+a.stack},Ja=function(a){var b=
Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||Ja),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=Ka(a||arguments.callee.caller,[]));return b},Ka=function(a,b){var c=[];if(0<=p(b,a))c.push("[...circular reference...]");else if(a&&50>b.length){c.push(Ga(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){0<e&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=
f?"true":"false";break;case "function":f=(f=Ga(f))?f:"[fn]";break;default:f=typeof f}40<f.length&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(Ka(a.caller,b))}catch(h){c.push("[exception trying to get caller]\n")}}else a?c.push("[...long stack...]"):c.push("[end]");return c.join("")},Ga=function(a){if(G[a])return G[a];a=String(a);if(!G[a]){var b=/function\s+([^\(]+)/m.exec(a);G[a]=b?b[1]:"[Anonymous]"}return G[a]},G={};
var H=function(){this.s=this.s;this.H=this.H};H.prototype.s=!1;H.prototype.P=function(){this.s||(this.s=!0,this.h())};H.prototype.h=function(){if(this.H)for(;this.H.length;)this.H.shift()()};
var La=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]},Ma=function(a,b){return a<b?-1:a>b?1:0};
var I=[],J=[],Na=!1,Oa=function(a){I[I.length]=a;if(Na)for(var b=0;b<J.length;b++)a(E(J[b].i,J[b]))},Pa=function(a){Na=!0;for(var b=E(a.i,a),c=0;c<I.length;c++)I[c](b);J.push(a)};
var Qa=function(a){Qa[" "](a);return a};Qa[" "]=function(){};var Sa=function(a,b){var c=Ra;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};
var Ta=t("Opera"),K=t("Trident")||t("MSIE"),Ua=t("Edge"),Va=t("Gecko")&&!(-1!=r().toLowerCase().indexOf("webkit")&&!t("Edge"))&&!(t("Trident")||t("MSIE"))&&!t("Edge"),Wa=-1!=r().toLowerCase().indexOf("webkit")&&!t("Edge"),Xa;
a:{var Ya="",Za=function(){var a=r();if(Va)return/rv:([^\);]+)(\)|;)/.exec(a);if(Ua)return/Edge\/([\d\.]+)/.exec(a);if(K)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(Wa)return/WebKit\/(\S+)/.exec(a);if(Ta)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Za&&(Ya=Za?Za[1]:"");if(K){var L,$a=q.document;L=$a?$a.documentMode:void 0;if(null!=L&&L>parseFloat(Ya)){Xa=String(L);break a}}Xa=Ya}
var ab=Xa,Ra={},bb=function(a){return Sa(a,function(){for(var b=0,c=La(String(ab)).split("."),d=La(String(a)).split("."),e=Math.max(c.length,d.length),f=0;0==b&&f<e;f++){var h=c[f]||"",g=d[f]||"";do{h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==h[0].length&&0==g[0].length)break;b=Ma(0==h[1].length?0:parseInt(h[1],10),0==g[1].length?0:parseInt(g[1],10))||Ma(0==h[2].length,0==g[2].length)||Ma(h[2],g[2]);h=h[3];g=g[3]}while(0==b)}return 0<=b})};
var da="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");
Oa(function(){});
/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var M=function(){H.call(this)};M.prototype=ma(H.prototype);M.prototype.constructor=M;if(sa)sa(M,H);else for(var N in H)if("prototype"!=N)if(Object.defineProperties){var cb=Object.getOwnPropertyDescriptor(H,N);cb&&Object.defineProperty(M,N,cb)}else M[N]=H[N];M.v=H.prototype;
var fb=function(a){var b=db;b.i=a;eb(b)},x=function(a){var b=db,c=null;if(b.m){c="Potentially sensitive message stripped for security reasons.";var d=Error("h");d.columnNumber=a.columnNumber;d.lineNumber=a.lineNumber;d.name=a.name;d.fileName=a.fileName;if(28<=w("Chromium")||14<=w("Firefox"))d.stack=a.stack;a=d}b.s||(b.i?gb(b.i,a,c):b.g&&10>b.g.length&&b.g.push([c,a]))},eb=function(a){a.g&&(Da(a.g,function(b){gb(this.i,b[1],b[0])},a),a.g=null)},db=new M;
var ha=function(){var a=window;if(!a.location)try{JSON.stringify(a)}catch(c){}var b=a.location&&a.location.ancestorOrigins;if(void 0!==b)return b&&b.length?b[b.length-1]==a.location.origin:!0;try{return void 0!==a.top.location.href}catch(c){return!1}};
var ia={};
var hb=function(a){this.g=a;this.i={};this.h=[]},gb=function(a,b,c){var d=ja();c&&(d.message=c);a:{c=Ja();d["call-stack"]=c;b=b instanceof Error?b:b||"";for(c=0;c<a.h.length;c++)if(!1===a.h[c](b,d))break a;c="";if(b){c=b.message||"unknown";for(var e=0,f=0;f<c.length;++f)e=31*e+c.charCodeAt(f)>>>0;c=e}e="";for(h in d)e=e+h+":"+d[h]+":";var h=c+"::"+e;c=a.i[h];c||(c={time:0,count:0},a.i[h]=c);1E4>Date.now()-c.time?(c.count++,1==c.count&&(d=ja(),d.message="Throttling: "+h,a.g.i(b,d))):(c.count&&(d["dropped-instances"]=
c.count),c.time=Date.now(),c.count=0,a.g.i(b,d))}};
var O=function(a){H.call(this);this.l=a;this.m=!0;this.g=!1};F(O,H);O.prototype.i=function(a){return ib(this,a)};
var P=function(a,b){a=Object.prototype.hasOwnProperty.call(a,wa)&&a[wa]||(a[wa]=++xa);return(b?"__wrapper_":"__protected_")+a+"__"},ib=function(a,b){var c=P(a,!0);b[c]||((b[c]=jb(a,b))[P(a,!1)]=b);return b[c]},jb=function(a,b){var c=function(){if(a.s)return b.apply(this,arguments);try{return b.apply(this,arguments)}catch(d){kb(a,d)}};c[P(a,!1)]=b;return c},kb=function(a,b){if(!(b&&"object"===typeof b&&"string"===typeof b.message&&0==b.message.indexOf("Error in protected function: ")||"string"===typeof b&&
0==b.indexOf("Error in protected function: "))){a.l(b);if(!a.m)throw a.g&&("object"===typeof b&&b&&"string"===typeof b.message?b.message="Error in protected function: "+b.message:b="Error in protected function: "+b),b;throw new lb(b);}},mb=function(a){var b=b||q.window;"onunhandledrejection"in b&&(b.onunhandledrejection=function(c){kb(a,c&&c.reason?c.reason:Error("i"))})},nb=function(a){for(var b=q.window,c=["requestAnimationFrame","mozRequestAnimationFrame","webkitAnimationFrame","msRequestAnimationFrame"],
d=0;d<c.length;d++){var e=c[d];c[d]in b&&Q(a,e)}},Q=function(a,b){var c=q.window,d=c[b];if(!d)throw Error("j`"+b);c[b]=function(e,f){"string"===typeof e&&(e=Aa(Ba,e));e&&(arguments[0]=e=ib(a,e));if(d.apply)return d.apply(this,arguments);var h=e;if(2<arguments.length){var g=Array.prototype.slice.call(arguments,2);h=function(){e.apply(this,g)}}return d(h,f)};c[b][P(a,!1)]=d};
O.prototype.h=function(){var a=q.window;var b=a.setTimeout;b=b[P(this,!1)]||b;a.setTimeout=b;b=a.setInterval;b=b[P(this,!1)]||b;a.setInterval=b;O.v.h.call(this)};var lb=function(a){n.call(this,"Error in protected function: "+(a&&a.message?String(a.message):String(a)),a);(a=a&&a.stack)&&"string"===typeof a&&(this.stack=a)};F(lb,n);
var R=function(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1};R.prototype.h=function(){this.defaultPrevented=!0};
var S=function(a,b){R.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.i=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;if(b=a.relatedTarget){if(Va){a:{try{Qa(b.nodeName);var e=!0;break a}catch(f){}e=
!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=
a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:ob[a.pointerType]||"";this.state=a.state;this.i=a;a.defaultPrevented&&S.v.h.call(this)}};F(S,R);var ob={2:"touch",3:"pen",4:"mouse"};S.prototype.h=function(){S.v.h.call(this);var a=this.i;a.preventDefault?a.preventDefault():a.returnValue=!1};
var pb="closure_listenable_"+(1E6*Math.random()|0);
var qb=0;
var rb=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.G=e;this.key=++qb;this.D=this.F=!1},T=function(a){a.D=!0;a.listener=null;a.proxy=null;a.src=null;a.G=null};
var sb=function(a){this.src=a;this.g={};this.h=0};sb.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var h=tb(a,b,d,e);-1<h?(b=a[h],c||(b.F=!1)):(b=new rb(b,this.src,f,!!d,e),b.F=c,a.push(b));return b};var ub=function(a,b){var c=b.type;c in a.g&&aa(a.g[c],b)&&(T(b),0==a.g[c].length&&(delete a.g[c],a.h--))},tb=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.D&&f.listener==b&&f.capture==!!c&&f.G==d)return e}return-1};
var vb="closure_lm_"+(1E6*Math.random()|0),wb={},xb=0,yb=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)yb(a,b[f],c,d,e);else(f=typeof d,d="object"==f&&null!=d||"function"==f?!!d.capture:!!d,c=zb(c),a&&a[pb])?(a=a.m,b=String(b).toString(),b in a.g&&(f=a.g[b],c=tb(f,c,d,e),-1<c&&(T(f[c]),Array.prototype.splice.call(f,c,1),0==f.length&&(delete a.g[b],a.h--)))):a&&(a=Ab(a))&&(b=a.g[b.toString()],a=-1,b&&(a=tb(b,c,d,e)),(c=-1<a?b[a]:null)&&Bb(c))},Bb=function(a){if("number"!==typeof a&&
a&&!a.D){var b=a.src;if(b&&b[pb])ub(b.m,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(c in wb?wb[c]:wb[c]="on"+c,d):b.addListener&&b.removeListener&&b.removeListener(d);xb--;(c=Ab(b))?(ub(c,a),0==c.h&&(c.src=null,b[vb]=null)):T(a)}}},Cb=function(a,b){if(a.D)a=!0;else{b=new S(b,this);var c=a.listener,d=a.G||a.src;a.F&&Bb(a);a=c.call(d,b)}return a},Ab=function(a){a=a[vb];return a instanceof sb?a:null},Db="__closure_events_fn_"+
(1E9*Math.random()>>>0),zb=function(a){if("function"===typeof a)return a;a[Db]||(a[Db]=function(b){return a.handleEvent(b)});return a[Db]};Oa(function(a){Cb=a(Cb)});
var U=function(){H.call(this);this.m=new sb(this);this.V=this;this.M=null};F(U,H);U.prototype[pb]=!0;U.prototype.removeEventListener=function(a,b,c,d){yb(this,a,b,c,d)};
var W=function(a,b){var c,d=a.M;if(d)for(c=[];d;d=d.M)c.push(d);a=a.V;d=b.type||b;if("string"===typeof b)b=new R(b,a);else if(b instanceof R)b.target=b.target||a;else{var e=b;b=new R(d,a);ea(b,e)}e=!0;if(c)for(var f=c.length-1;0<=f;f--){var h=b.g=c[f];e=V(h,d,!0,b)&&e}h=b.g=a;e=V(h,d,!0,b)&&e;e=V(h,d,!1,b)&&e;if(c)for(f=0;f<c.length;f++)h=b.g=c[f],e=V(h,d,!1,b)&&e};
U.prototype.h=function(){U.v.h.call(this);if(this.m){var a=this.m,b=0,c;for(c in a.g){for(var d=a.g[c],e=0;e<d.length;e++)++b,T(d[e]);delete a.g[c];a.h--}}this.M=null};var V=function(a,b,c,d){b=a.m.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var h=b[f];if(h&&!h.D&&h.capture==c){var g=h.listener,k=h.G||h.src;h.F&&ub(a.m,h);e=!1!==g.call(k,d)&&e}}return e&&!d.defaultPrevented};
var Eb=function(){};Eb.prototype.g=null;var Gb=function(a){var b;(b=a.g)||(b={},Fb(a)&&(b[0]=!0,b[1]=!0),b=a.g=b);return b};
var Hb,Ib=function(){};F(Ib,Eb);var Jb=function(a){return(a=Fb(a))?new ActiveXObject(a):new XMLHttpRequest},Fb=function(a){if(!a.h&&"undefined"==typeof XMLHttpRequest&&"undefined"!=typeof ActiveXObject){for(var b=["MSXML2.XMLHTTP.6.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],c=0;c<b.length;c++){var d=b[c];try{return new ActiveXObject(d),a.h=d}catch(e){}}throw Error("k");}return a.h};Hb=new Ib;
var Kb=function(a,b,c){if("function"===typeof a)c&&(a=E(a,c));else if(a&&"function"==typeof a.handleEvent)a=E(a.handleEvent,a);else throw Error("l");return 2147483647<Number(b)?-1:q.setTimeout(a,b||0)};
var Lb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Mb=function(a,b){if(!b)return a;var c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]},Nb=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)Nb(a,String(b[d]),c);else null!=b&&c.push(a+(""===
b?"":"="+encodeURIComponent(String(b))))},Ob=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)Nb(a[b],a[b+1],c);return c.join("&")},Pb=function(a){var b=[],c;for(c in a)Nb(c,a[c],b);return b.join("&")},Qb=function(a,b){var c=2==arguments.length?Ob(arguments[1],0):Ob(arguments,1);return Mb(a,c)};
var X=function(a){U.call(this);this.headers=new Map;this.C=a||null;this.i=!1;this.B=this.g=null;this.K="";this.l=this.J=this.u=this.I=!1;this.N=0;this.A=null;this.S="";this.O=this.U=!1};F(X,U);var Rb=/^https?$/i,Sb=["POST","PUT"],Tb=[];X.prototype.W=function(){this.P();aa(Tb,this)};
var Xb=function(a,b,c,d,e){if(a.g)throw Error("m`"+a.K+"`"+b);c=c?c.toUpperCase():"GET";a.K=b;a.I=!1;a.i=!0;a.g=a.C?Jb(a.C):Jb(Hb);a.B=a.C?Gb(a.C):Gb(Hb);a.g.onreadystatechange=E(a.R,a);try{a.J=!0,a.g.open(c,String(b),!0),a.J=!1}catch(g){Ub(a);return}b=d||"";d=new Map(a.headers);if(e)if(Object.getPrototypeOf(e)===Object.prototype)for(var f in e)d.set(f,e[f]);else if("function"===typeof e.keys&&"function"===typeof e.get){f=y(e.keys());for(var h=f.next();!h.done;h=f.next())h=h.value,d.set(h,e.get(h))}else throw Error("n`"+
String(e));e=Array.from(d.keys()).find(function(g){return"content-type"==g.toLowerCase()});f=q.FormData&&b instanceof q.FormData;!(0<=p(Sb,c))||e||f||d.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");c=y(d);for(e=c.next();!e.done;e=c.next())d=y(e.value),e=d.next().value,d=d.next().value,a.g.setRequestHeader(e,d);a.S&&(a.g.responseType=a.S);"withCredentials"in a.g&&a.g.withCredentials!==a.U&&(a.g.withCredentials=a.U);try{Vb(a),0<a.N&&(a.O=Wb(a.g),a.O?(a.g.timeout=a.N,a.g.ontimeout=
E(a.T,a)):a.A=Kb(a.T,a.N,a)),a.u=!0,a.g.send(b),a.u=!1}catch(g){Ub(a)}},Wb=function(a){return K&&bb(9)&&"number"===typeof a.timeout&&void 0!==a.ontimeout};X.prototype.T=function(){"undefined"!=typeof va&&this.g&&(W(this,"timeout"),this.abort(8))};var Ub=function(a){a.i=!1;a.g&&(a.l=!0,a.g.abort(),a.l=!1);Yb(a);Y(a)},Yb=function(a){a.I||(a.I=!0,W(a,"complete"),W(a,"error"))};
X.prototype.abort=function(){this.g&&this.i&&(this.i=!1,this.l=!0,this.g.abort(),this.l=!1,W(this,"complete"),W(this,"abort"),Y(this))};X.prototype.h=function(){this.g&&(this.i&&(this.i=!1,this.l=!0,this.g.abort(),this.l=!1),Y(this,!0));X.v.h.call(this)};X.prototype.R=function(){this.s||(this.J||this.u||this.l?Zb(this):this.L())};X.prototype.L=function(){Zb(this)};
var Zb=function(a){if(a.i&&"undefined"!=typeof va&&(!a.B[1]||4!=(a.g?a.g.readyState:0)||2!=$b(a)))if(a.u&&4==(a.g?a.g.readyState:0))Kb(a.R,0,a);else if(W(a,"readystatechange"),4==(a.g?a.g.readyState:0)){a.i=!1;try{var b=$b(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}var d;if(!(d=c)){var e;if(e=0===b){var f=String(a.K).match(Lb)[1]||null;!f&&q.self&&q.self.location&&(f=q.self.location.protocol.slice(0,-1));e=!Rb.test(f?f.toLowerCase():
"")}d=e}d?(W(a,"complete"),W(a,"success")):Yb(a)}finally{Y(a)}}},Y=function(a,b){if(a.g){Vb(a);var c=a.g,d=a.B[0]?function(){}:null;a.g=null;a.B=null;b||W(a,"ready");try{c.onreadystatechange=d}catch(e){}}},Vb=function(a){a.g&&a.O&&(a.g.ontimeout=null);a.A&&(q.clearTimeout(a.A),a.A=null)};X.prototype.isActive=function(){return!!this.g};var $b=function(a){try{return 2<(a.g?a.g.readyState:0)?a.g.status:-1}catch(b){return-1}};Oa(function(a){X.prototype.L=a(X.prototype.L)});
var Z=function(a,b,c){U.call(this);this.u=b||null;this.l={};this.A=ac;this.B=a;c||(this.g=null,K&&!bb("10")?Ea(E(this.i,this),!1,null):(this.g=new O(E(this.i,this)),Q(this.g,"setTimeout"),Q(this.g,"setInterval"),nb(this.g),Pa(this.g)))};F(Z,U);var bc=function(a){R.call(this,"a");this.error=a};F(bc,R);
var ac=function(a,b,c,d){if(d instanceof Map){var e={};d=y(d);for(var f=d.next();!f.done;f=d.next()){var h=y(f.value);f=h.next().value;h=h.next().value;e[f]=h}}else e=d;d=new X;Tb.push(d);d.m.add("ready",d.W,!0,void 0,void 0);Xb(d,a,b,c,e)};
Z.prototype.i=function(a,b){a=a.error||a;if(b){var c={};for(d in b)c[d]=b[d];b=c}else b={};a instanceof Error&&ea(b,a.__closure__error__context__984382||{});a=Ha(a);if(this.u)try{this.u(a,b)}catch(m){}var d=a.message.substring(0,1900);c=a.stack;try{var e=Qb(this.B,"script",a.fileName,"error",d,"line",a.lineNumber);a:{for(var f in this.l){var h=!1;break a}h=!0}if(!h){h=e;var g=Pb(this.l);e=Mb(h,g)}g={};g.trace=c;if(b)for(var k in b)g["context."+k]=b[k];var l=Pb(g);this.A(e,"POST",l,this.C)}catch(m){}try{W(this,
new bc(a,b))}catch(m){}};Z.prototype.h=function(){var a=this.g;a&&"function"==typeof a.P&&a.P();Z.v.h.call(this)};
var cc=function(){};cc.prototype.g=null;cc.prototype.h=null;var dc=new cc;
var ec=function(){this.g=("undefined"==typeof document?null:document)||{cookie:""}};
ec.prototype.set=function(a,b,c){var d=!1;if("object"===typeof c){var e=c.aa;d=c.ba||!1;var f=c.domain||void 0;var h=c.path||void 0;var g=c.X}if(/[;=\s]/.test(a))throw Error("o`"+a);if(/[;\r\n]/.test(b))throw Error("p`"+b);void 0===g&&(g=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(h?";path="+h:"")+(0>g?"":0==g?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+1E3*g)).toUTCString())+(d?";secure":"")+(null!=e?";samesite="+e:"")};
ec.prototype.get=function(a,b){for(var c=a+"=",d=(this.g.cookie||"").split(";"),e=0,f;e<d.length;e++){f=La(d[e]);if(0==f.lastIndexOf(c,0))return f.slice(c.length);if(f==a)return""}return b};var fc=new ec;
(function(a,b,c,d,e,f,h,g){db.g=[];c&&(a=new Z(c,void 0,!0),g&&(a.l=g),e&&(a.A=e),e=new hb(a),dc.h=e,fb(e));var k=null;e=function(l){q.$googDebugFname&&l&&l.message&&!l.fileName&&(l.message+=" in "+q.$googDebugFname);k?l&&l.message&&(l.message+=" [Possibly caused by: "+k+"]"):k=String(l);x(l)};Ca("_DumpException",e);Ca("_B_err",e);Da([q].concat(d||[]),Aa(Ea,Aa(ka,!!f),!0));d=28<=w("Chromium")||14<=w("Firefox")||11<=w("Internet Explorer")||10<=w("Safari");h&&d||9>=w("Internet Explorer")||(h=new O(fa),
h.m=!0,h.g=!0,nb(h),Q(h,"setTimeout"),Q(h,"setInterval"),mb(h),Pa(h),dc.g=h);return dc})("",void 0,"/_/AccountsDomainCookiesCheckConnectionHttp/jserror");Ca("google.checkconnection.getMsgToSend",function(a,b){if(!a)throw Error("q");!b||0>=b?b=0:(b=(new Date).getTime()-b,b=!b||0>b?0:b);var c="CheckConnectionTempCookie"+String(Math.floor(1E3*Math.random()));var d=String(Math.floor(1E6*Math.random()));fc.set(c,d,{X:10});c=fc.get(c)==d?!0:!1;return a+":"+String(b)+":"+(c?"1":"0")});

}catch(e){_._DumpException(e)}
}).call(this,this.default_AccountsDomaincookiesCheckconnectionJs);
// Google Inc.

//# sourceURL=/_/mss/boq-identity/_/js/k=boq-identity.AccountsDomaincookiesCheckconnectionJs.zh_CN.na8DWiPJrws.es5.O/d=1/rs=AOaEmlHKcduJgtZjWwJNAP8_FtOjUTw9zg/m=base
if (window.parent && window.parent.postMessage) {window.parent.postMessage( google.checkconnection.getMsgToSend('youtube', '*************'), 'https:\/\/accounts.google.com');}</script></head><body></body></html>