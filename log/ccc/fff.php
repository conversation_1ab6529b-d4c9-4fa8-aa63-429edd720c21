<?php  
  
// 初始化CURL会话  
$ch = curl_init();  
  
// 设置CURL选项  
$url = 'http://xingpan.vip/astrology/chart/lunarreturn'; // 替换为你的API URL  
$postData = [  
    'access_token' => '989f888c4283e2cc2d8a5aa4af60932c',  
    'h_sys' => 'K',  
    'user_list' => [  
        [  
            'longitude' => 100.08,  
            'latitude' => 23.88,  
            'tz' => 8.00,  
            'birthday' => '2020-10-10 12:12',  
        ],  
        [  
            'longitude' => 103.77,  
            'latitude' => 29.57,  
            'tz' => 8.00,  
            'birthday' => '2021-02-17 12:20',  
        ],  
    ],  
    'planets' => [0, 1, 2, 3],  
    'virtual' => 10, // 如果需要多个值，可以改为数组，如 [10, 20, 30]  
   
];  
  
curl_setopt($ch, CURLOPT_URL, $url);  
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  
curl_setopt($ch, CURLOPT_POST, true);  
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);  
  
// 执行CURL请求并获取响应  
$response = curl_exec($ch);  
  
// 检查是否有错误发生  
if (curl_errno($ch)) {  
    echo 'Curl error: ' . curl_error($ch);  
}  
  
// 关闭CURL会话  
curl_close($ch);  
  
// 打印响应  
echo $response;