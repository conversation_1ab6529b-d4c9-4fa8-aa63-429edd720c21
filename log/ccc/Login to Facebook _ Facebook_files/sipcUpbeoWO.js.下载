if (self.CavalryLogger) { CavalryLogger.start_js_script(document.currentScript); }/*FB_PKG_DELIM*/

__d("XAsyncRequest",["MRequest"],(function(a,b,c,d,e,f,g){a=function(){function a(a){var b=this;this.setAllowCrossPageTransition=function(a){return b};this.$1=new(c("MRequest"))(a)}var b=a.prototype;b.setURI=function(a){this.$1.setURI(a);return this};b.setMethod=function(a){this.$1.setMethod(a);return this};b.setType=function(a){this.$1.setType(a);return this};b.setAutoProcess=function(a){this.$1.setAutoProcess(a);return this};b.setData=function(a,b){if(b!==void 0&&b){b={};for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&a[c]!==void 0&&(b[c]=a[c]);this.$1.setData(b)}else this.$1.setData(a);return this};b.setRawData=function(a){this.$1.setRawData(a);return this};b.setHandler=function(a){this.$1.listen("done",a);return this};b.setPayloadHandler=function(a){this.setHandler(function(b){return a(b.payload)});return this};b.setTimeoutHandler=function(a,b){this.$1.setTimeout(a);this.$1.listen("error",b);return this};b.setErrorHandler=function(a){this.$1.listen("error",a);return this};b.setResponseHandler=function(a){this.$1.listen("response",a);return this};b.setReadOnly=function(a){this.$1.setReadOnly(a);return this};b.send=function(){this.$1.send();return this};b.abort=function(){this.$1.abort()};b.setAllowCrossOrigin=function(a){this.$1.setCORS(a);return this};b.setAllowCredentials=function(a){this.$1.setAllowCredentials(a);return this};b.setRequestHeader=function(a,b){this.$1.setRequestHeader(a,b);return this};b.setUploadProgressHandler=function(a){this.$1.listen("uploadprogress",a);return this};return a}();g["default"]=a}),98);
__d("XTaggingCounterController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/composer/tagging/counter/",{event:{type:"Enum",required:!0,enumType:1}})}),null);
__d("MTaggingCounter",["AsyncSignal","XTaggingCounterController"],(function(a,b,c,d,e,f){a={logEvent:function(a,c){c=b("XTaggingCounterController").getURIBuilder().setEnum("event",a).getURI();new(b("AsyncSignal"))(c).send()}};e.exports=a}),null);
__d("RemoteDevice",["Banzai","Cookie","GeneratedLoggerUtils","MViewport","Run","isInIframe"],(function(a,b,c,d,e,f,g){var h=!1;function a(a){if(!c("isInIframe")()&&!window.APP_ENABLED&&!window.FW_ENABLED){if(/\((?:iPad|iPhone|iPod(?: touch));/.test(navigator.userAgent)){var b=Math.min(screen.width,screen.height),e=Math.max(screen.width,screen.height);b&&e&&(c("Cookie").set("wd",b+"x"+e),c("Cookie").set("m_pixel_ratio",window.devicePixelRatio));a&&a.performHardwareDetection&&a.hashId!==null&&d("Run").onAfterLoad(function(){i(a.hashId||"")});return}b=d("MViewport").getWidth();e=d("MViewport").getScreenHeight();if(!b||!e)return;c("Cookie").set("wd",b+"x"+e)}}function i(a){if(h)return;h=!0;var b=document.createElement("canvas");if(!b)return;b=b.getContext("webgl")||b.getContext("experimental-webgl");if(!b)return;var e=b.getExtension("WEBGL_debug_renderer_info");if(!e)return;var f="unknown",g="unknown";e!=null&&(f=b.getParameter(e.UNMASKED_RENDERER_WEBGL),g=b.getParameter(e.UNMASKED_VENDOR_WEBGL));b=0;window.navigator&&(b=window.navigator.hardwareConcurrency);e=window.screen.width;var i=window.screen.height,j=Math.min(e,i);e=Math.max(e,i);i={gpu_renderer:f,gpu_vendor:g,logical_cpu_count:b,screen_width_pixel:j,screen_height_pixel:e,device_pixel_ratio:window.devicePixelRatio,hashid:a};d("GeneratedLoggerUtils").log("logger:MHardwareDetectorLoggerConfig",i,c("Banzai").VITAL)}g.init=a;g.logHardwareInfo=i}),98);
__d("MStoriesRing",["cx","CSS","DOM","Stratcom","ge"],(function(a,b,c,d,e,f,g,h){"use strict";var i="m:feed:story:all-stories-viewed",j="m:page:render:cache:complete-with-replays";function a(a){c("Stratcom").listen(i,null,function(b){var e=b.getData().ownerID;c("Stratcom").listen(j,null,function(){var b=c("ge")(a);if(!b)return;b=d("DOM").scry(b,"div","feed_story_ring".concat(e.toString()))||[];b.forEach(function(a){c("CSS").removeClass(a,"_847o")})})})}g.setupStoryRingForUpdates=a}),98);
__d("MFeedErrorDetection",["Banzai","DataAttributeUtils"],(function(a,b,c,d,e,f,g){"use strict";var h={};function i(a,b,d,e){a={event:a,shouldLogDetail:b,site:"mtouch"};b&&(a.intValues=d,a.normalValues=e);c("Banzai").post("feed_error_detection",a,{delay:0})}function a(a){var b=a.feedObjectElement;a=a.shouldLogDetail;if(!b)return;var d=c("DataAttributeUtils").getDataFt(b);b=b.getAttribute("data-dedupekey");if(!b||!d)return;if(h[b]){var e;a&&(e={dedupKey:b,ft_A:d,ft_B:h[b]});i("mtouch_duplicate_stories",a,{},{dup_field:e||{}})}else h[b]=d}g.registerFeedStory=a}),98);
__d("EventProfilerAdsSessionProvider",[],(function(a,b,c,d,e,f){"use strict";a=null;f.sessionID=a}),66);
__d("FeedbackReactionIDToTypeNumber",[],(function(a,b,c,d,e,f){"use strict";var g=Object.freeze({1635855486666999:1,1678524932434102:2,478547315650144:3,115940658764963:4,1667835766830853:5,1501783253447867:6,908563459236466:7,444813342392137:8,1536130110011063:10,1663186627268800:11,899779720071651:12,869508936487422:13,938644726258608:14,1609920819308489:15,613557422527858:16,1038933380267464:18,156697673289272:19});function a(a){if(a in g)return g[a];else return 17}f["default"]=a}),66);
__d("MReactionsBlingBar",["cx","fbt","FeedbackReactionIDToTypeNumber","MLiveData","MViewport","Stratcom","SubscriptionsHandler","UFIReactionIcons","UFIReactionTypes","URI","createIxElement","joinClasses"],(function(a,b,c,d,e,f,g,h){var i;b("UFIReactionTypes").ordering.unshift(17);var j=b("UFIReactionTypes").ordering.reduce(function(a,b,c){a[b]=c;return a},{}),k=function(){"use strict";function a(a){var c=a.elements;this.container=c.container;this.isPermalink=a.isPermalink;this.requestID=a.requestID;this.separateCommentsLink=a.separateCommentsLink;this.uri=new(i||(i=b("URI")))(a.uri);this.shouldAnchor=a.shouldAnchor;this.feedbackTarget=b("MLiveData").get(a.feedbackTargetID);this.isDarkMode=a.isDarkMode;this.darkModeLinks=a.darkModeLinks;this.subscriptions=new(b("SubscriptionsHandler"))();this.subscriptions.addSubscriptions(b("Stratcom").listen("m:page:unload",null,this.onUnload.bind(this)),this.feedbackTarget.listen("change",this.onChange.bind(this)));this.isPermalink&&this.shouldAnchor&&(a.isPageletified?this.anchorOnPageLoad():(this.pageLoadListener=b("Stratcom").listen(["m:page:render:complete"],null,this.anchorOnPageLoad.bind(this)),this.subscriptions.addSubscriptions(this.pageLoadListener)))}var c=a.prototype;c.onChange=function(){var a=this.feedbackTarget.getData(),c=a.reactioncountmap,d=a.reactioncountmapbyid,e=a.reactiondisplaystrategy,f=a.request_id,g=a.ufireactioniconsbyid;e=e==="use_reaction_sheet_string_only"||e==="hide_counts";if(f===this.requestID||!c)return;f=Object.keys(d).filter(function(a){a=d[a];return a&&a["default"]>0}).sort(function(a,c){var e=d[a]["default"],f=d[c]["default"];return e===f?j[b("FeedbackReactionIDToTypeNumber")(a)]-j[b("FeedbackReactionIDToTypeNumber")(c)]:f-e}).slice(0,3).map(function(a,c){var d=document.createElement("div");d.className=b("joinClasses")("_1g05","_77lc");d.style.zIndex=3-c;b("FeedbackReactionIDToTypeNumber")(a)===17?d.appendChild(b("createIxElement")(g[a][16])):d.appendChild(b("createIxElement")(b("UFIReactionIcons")[b("FeedbackReactionIDToTypeNumber")(a)][16]));return d});c=f.reduce(function(a,b){a.appendChild(b);return a},document.createElement("span"));c.className="_qfz";f=document.createElement("div");f.className="_1w1k"+(e?" _8l2a":"");if(this.separateCommentsLink){var i=document.createElement("a");this.uri.addQueryData("anchor_composer","false");i.href=this.uri;i.appendChild(c);f.appendChild(i)}else f.appendChild(c);i=a.comment_count;c=a.share_count;var k=a.reactionsentences.current&&a.reactionsentences.current.text,l=document.createElement("div");l.className="_1g06";l.setAttribute("aria-label",h._("{count}\u4e2a\u5fc3\u60c5",[h._param("count",k)]));l.textContent=k;if(this.isDarkMode){var m=document.createElement("a");m.href=this.darkModeLinks.likesLink;m.className="_8gqh";m.appendChild(l);f.appendChild(m)}else f.appendChild(l);m=this.container;l=document.createElement("div");l.className="_1fnt"+(e?" _8l2b":"");if(this.isDarkMode){e=document.createElement("a");e.className="_8gqh _1fnt";e.href=this.darkModeLinks.commentsLink;l=l.appendChild(e)}if(!this.isPermalink){if(i>0){e=document.createElement("span");e.className="_1j-c"+(this.separateCommentsLink?" _6hyu":"");e.textContent=i===1?h._("1 \u6761\u8bc4\u8bba"):h._("{count}\u6761\u8bc4\u8bba",[h._param("count",a.reduced_comment_count)]);if(this.separateCommentsLink){var n=document.createElement("a");this.uri.addQueryData("anchor_composer","true");n.href=this.uri;n.appendChild(e);l.appendChild(n)}else l.appendChild(e)}if(c>0){n=document.createElement("span");n.className="_1j-c";n.textContent=c===1?h._("1\u6b21\u5206\u4eab"):h._("{count}\u6b21\u5206\u4eab",[h._param("count",a.reduced_share_count)]);l.appendChild(n)}}while(m.hasChildNodes())m.removeChild(m.firstChild);if(k||i||c){e=document.createDocumentFragment();e.appendChild(f);e.appendChild(l);m.appendChild(e)}};c.onUnload=function(){this.subscriptions.release(),this.subscriptions=null};c.anchorOnPageLoad=function(){b("MViewport").scrollToNode(this.container)};return a}();e.exports={init:function(a){new k(a)}}}),null);
__d("SaveState",[],(function(a,b,c,d,e,f){a="saving";b="saved";c="unsaving";d="unsaved";f.SAVING=a;f.SAVED=b;f.UNSAVING=c;f.UNSAVED=d}),66);
__d("SaveStateHandler",["SaveState"],(function(a,b,c,d,e,f,g){var h=null;a=function(){function a(){this.$1={},this.$2={}}var b=a.prototype;b.addListener=function(a,b){this.$1[a]||(this.$1[a]=[]),this.$1[a].push(b)};b.setState=function(a,b){var c=this;a.forEach(function(a){c.$2[a]=b;if(!c.$1[a])return;a=c.$1[a];a.forEach(function(a){try{a.call(window,b)}catch(a){}})})};b.getState=function(a){return this.$2[a]};a.$3=function(){h||(h=new a());return h};a.listen=function(a,b){this.$3().addListener(a,b)};a.getState=function(a){return this.$3().getState(a)};a.saving=function(a){this.$3().setState(a,d("SaveState").SAVING)};a.saved=function(a){this.$3().setState(a,d("SaveState").SAVED)};a.unsaving=function(a){this.$3().setState(a,d("SaveState").UNSAVING)};a.unsaved=function(a){this.$3().setState(a,d("SaveState").UNSAVED)};a.isSaveAction=function(a){a=this.$3().getState(a);return a==d("SaveState").UNSAVED||a==d("SaveState").UNSAVING};a.isInTransition=function(a){a=this.$3().getState(a);return a==d("SaveState").SAVING||a==d("SaveState").UNSAVING};return a}();g["default"]=a}),98);
__d("MAsyncPageLoadWithGraphSearch",["Stratcom"],(function(a,b,c,d,e,f,g){"use strict";var h="graph-search-entry-point";function a(a){c("Stratcom").invoke("setSearchText",h,{title:a.searchTitle||""}),c("Stratcom").invoke("setCurrentProfileID",h,{profileID:a.currentProfileID})}g.pageLoad=a}),98);
__d("MExposeMore",["CSS","DOM","Stratcom"],(function(a,b,c,d,e,f,g){"use strict";var h=[];function i(){while(h.length)h.pop().remove()}var j="expose",k=!1,l=!0,m=!1;c("Stratcom").listen("click",[j,"more"],function(a){var b=a.getNode(j);c("CSS").conditionClass(b,"text_exposed",l);m?(l&&a.prevent(),l=!l):a.prevent()});function a(a){if(k)return;m=a.seeMoreSwitchExpandOnly;k=!0}function b(a,b){if(b.children.length!==0){h.length||h.push(c("Stratcom").listen("m:page:unload",null,i));a=d("DOM").scry(a,"*","more")[0];a&&h.push(d("DOM").listen(a,"click",null,function(a){c("CSS").show(b),a.prevent()}))}}g.init=a;g.showMessageSuffixOnClick=b}),98);
__d("XPlacesUpdateLocationController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/places/location_update/",{json_location:{type:"String"},source:{type:"Enum",defaultValue:"msite_unknown",enumType:1}})}),null);
__d("MLocationPrompt",["DOM","MRequest","MTaggingCounter","Stratcom","WebStorage","XPlacesUpdateLocationController"],(function(a,b,c,d,e,f,g){var h=["latitude","longitude","accuracy","altitude","altitudeAccuracy","heading","speed"],i={enableHighAccuracy:!0,timeout:6e4,maximumAge:3e5},j=6e4;a=function(){a.init=function(b,c,d,e){return new a(b,c,d,e)};a.getCoords=function(){return a.coords};a.isLocationAvailable=function(){return a.locationAvailable};function a(b,e,f,g){var h=this;this.$2=function(){d("MTaggingCounter").logEvent("geo_panel_accept"),h.$1(!0)};this.$3=function(){a.locationAvailable=!1,d("MTaggingCounter").logEvent("geo_panel_deny"),d("DOM").hide(h.container),c("Stratcom").invoke("m:jewel-set:notifications-jewel:refresh-flyout"),c("Stratcom").invoke(a.STRATCOM_DENY)};this.$5=function(b){a.locationAvailable=!1;h.clearWatch();var e=c("WebStorage").getLocalStorage();e&&e.setItem(a.GEOLOCATION_KEY,"0");d("DOM").show(h.container);d("DOM").hide(h.question);d("DOM").hide(h.loading);d("DOM").show(h.errored);c("Stratcom").invoke("m:jewel-set:notifications-jewel:refresh-flyout");c("Stratcom").invoke(a.STRATCOM_FAIL,null,{error:b});d("MTaggingCounter").logEvent("geo_panel_fail")};this.$4=function(){if(!h.listeners)return;for(var a=0;a<h.listeners.length;a++)h.listeners[a].remove();h.listeners=[]};this.lastUpdateTime=0;this.container=b;this.question=e;this.loading=f;this.errored=g;b=c("WebStorage").getLocalStorage();b&&b.getItem(a.GEOLOCATION_KEY)==="1"?this.$1():(a.locationAvailable=!1,d("DOM").show(this.question),d("DOM").hide(this.loading),d("DOM").hide(this.errored),d("DOM").show(this.container),c("Stratcom").invoke("m:jewel-set:notifications-jewel:refresh-flyout"),d("MTaggingCounter").logEvent("geo_panel_load"));this.listeners=[c("Stratcom").listen("click","accept-location",this.$2.bind(this)),c("Stratcom").listen("click","deny-location",this.$3.bind(this)),c("Stratcom").listen("m:page:unload",null,this.$4.bind(this))]}var b=a.prototype;b.clearWatch=function(){if(!this.watchID)return;navigator.geolocation.clearWatch(this.watchID);this.watchID=null};b.$1=function(b){var e=this;a.locationAvailable=!0;var f=c("WebStorage").getLocalStorage();f&&f.setItem(a.GEOLOCATION_KEY,"1");d("DOM").show(this.container);d("DOM").hide(this.question);d("DOM").show(this.loading);d("DOM").hide(this.errored);c("Stratcom").invoke("m:jewel-set:notifications-jewel:refresh-flyout");var g=setTimeout(this.$5,i.timeout);this.watchID=navigator.geolocation.watchPosition(function(a){b&&d("MTaggingCounter").logEvent("geo_panel_success"),clearTimeout(g),e.$6(a)},function(a){b&&d("MTaggingCounter").logEvent("geo_panel_fail"),clearTimeout(g),e.$5(a)},i);c("Stratcom").invoke(a.STRATCOM_ACCEPT)};b.$6=function(b){a.locationAvailable=!0;if(b){var e={},f;for(var g=0;g<h.length;g++)f=h[g],b.coords[f]&&(e[f]=b.coords[f]);b.timestamp&&(e.timestamp=b.timestamp/1e3);this.$7(JSON.stringify(e));d("DOM").hide(this.container);a.coords=e;c("Stratcom").invoke("m:jewel-set:notifications-jewel:refresh-flyout");c("Stratcom").invoke(a.STRATCOM_UPDATE,null,{position:b})}};b.$7=function(a){var b=Date.now();if(b-this.lastUpdateTime>j){this.lastUpdateTime=b;b=c("XPlacesUpdateLocationController").getURIBuilder().setString("json_location",a).setEnum("source","msite_location_prompt").getURI();new(c("MRequest"))(b).setMethod("POST").send()}};return a}();Object.assign(a,{STRATCOM_ACCEPT:"MLocationPrompt/accept",STRATCOM_DENY:"MLocationPrompt/deny",STRATCOM_UPDATE:"MLocationPrompt/update",STRATCOM_FAIL:"MLocationPrompt/fail",GEOLOCATION_KEY:"authorizeGeolocation",locationAvailable:void 0});g["default"]=a}),98);
__d("MMoreItemAutomatic",["MLocationPrompt","MPageCache","MPageControllerPath","MRequest","MRequestGateway","MRequestTypes","MResponseData","MStopNGo","MURI","MViewport","Stratcom","Vector","setTimeout","throttle"],(function(a,b,c,d,e,f,g){a=function(){function a(b){var e=this;this.$26=function(){e.$5?e.$27():!e.$6&&!e.$18&&e.$28()};this.$27=function(){!e.$6?(e.$17=!0,e.isElementVisible()&&c("Stratcom").invoke("m:more_item_automatic:spinner_visible",e.$9,e.$10),!e.$18?e.$28():e.$29()):e.$25()};this.$30=function(){e.$6||(e.$17=!1)};this.$31=function(){!e.$18&&(e.$8||e.isElementVisible())&&(e.$32(),e.$8=!1)};this.$32=function(){var b=c("MLocationPrompt").getCoords();e.$21&&b&&(e.$4=new(c("MURI"))(e.$4).addQueryData("lat",b.latitude).addQueryData("long",b.longitude).toString());b=new(c("MRequest"))(new(c("MURI"))(e.$4).toString());e.$3?b.setType(d("MRequestTypes").INDEPENDENT):b.setType(d("MRequestTypes").DEPENDENT);b.setAutoProcess(!1);var f=[],g=!1,h=function(){!g&&f.length&&(g=!0,f.shift().process())};e.$19&&(b.setTimeout(c("MRequestGateway").ERROR_TIMEOUT),b.setFinalizeUponError(!1),b.listen("error",function(b,d){c("setTimeout")(function(){navigator.onLine&&(d.reset(),d.addData({is_retry:1}),d.sendAfterProcessing())},a.TRY_AGAIN_DELAY)}));b.listen("response",function(a){a=new(c("MResponseData"))(a);a.listen("complete",function(){g=!1,h()});if(a.isPagelet()){f.push(a);h();return}g=!0;e.$15=a;e.$17&&e.$29()});b.send();e.$18=!0};this.$4=b.href;this.$21=b.sendGeolocation;this.$16=b.proximity_pages||7;this.$9=b.logger_id;this.$10=b.logger_name;this.$8=b.load_first_immediately;this.$19=b.retryOnError;this.$2=b.addToCache;this.$3=b.alwaysProcess;this.$20=b.scrollPrefetchThrottleFreq||0;this.$5=b.insertWhileScrolling;this.$18=!1;this.$17=!0;this.$13=0;this.$12=!1;this.$6=!1;this.$22=0;this.$7=null;this.$11=null;this.$14=!1;this.$15=null;this.$1=b.additionalEvent;var f=document.getElementById(b.id);f?(this.$22=c("Vector").getPos(f).y,this.$11=f,this.$14=b.persist_on_reload,this.$23()):this.uninstall()}var b=a.prototype;b.isElementVisible=function(){if(!this.$24())return!1;var a=this.$11;if(a&&"getBoundingClientRect"in a){var b=a.getBoundingClientRect(),e=b.width/10,f=b.height/10,g=b.left,h=b.right,i=b.top;b=b.bottom;var j=!1;while(!j&&g<=h&&i<=b){g=Math.round(g+e);i=Math.round(i+f);var k=document.elementFromPoint(g,i);j=a.contains(k)}k=j}else{e=c("Vector").getPos(a).y;f=e+c("Vector").getDim(a).y;g=d("MViewport").getScrollTop();h=g+d("MViewport").getUseableHeight();k=e<=h&&f>=g}return k};b.$25=function(){var a=this.$11;a&&(this.$22-d("MViewport").getScrollTop()<0&&(c("Stratcom").invoke("m:more_item_automatic:items_visible",this.$9),this.uninstall()))};b.$23=function(){!this.$18&&(this.$8||this.isElementVisible())&&(this.$32(),this.$8=!1);this.$7=[];this.$20>0&&this.$7.push(c("Stratcom").listen(["m:page:render:complete","scroll"],null,c("throttle").withBlocking(this.$26,this.$20,this)));this.$5||this.$7.push(c("MStopNGo").listen("go",this.$27.bind(this)),c("MStopNGo").listen("stop",this.$30.bind(this)));var a=this.$1;if(a!=null){var b;(b=this.$7)==null?void 0:b.push(c("Stratcom").listen(a,null,this.$32.bind(this)))}this.$7.push(c("Stratcom").listen("m:ajax:complete",null,this.$31.bind(this)))};b.$33=function(){var a=this.$11;if(a){var b=document;b=b.documentElement;if(b)return b.contains(a)}return!1};b.uninstall=function(){while(this.$7&&this.$7.length)this.$7.pop().remove();this.$18=!1;this.$15=null;this.$11=null;this.$14=!1};b.withinProximity=function(){var a=this.$11;return!!a&&c("Vector").getPos(a).y-d("MViewport").getScrollTop()<d("MViewport").getUseableHeight()*(1+this.$16)};b.$29=function(){var a=this.$15;a&&(this.$33()&&(a.process(),this.$2&&c("MPageCache").addCachedIUIResponse(d("MPageControllerPath").getRequestPath(),a)),c("Stratcom").invoke("m:more_item_automatic:items_loaded",this.$9,this.$10),this.$6=!0)};b.$24=function(){var a=this.$11;return!a||!a.offsetHeight?!1:!0};b.$28=function(){(this.isElementVisible()||this.$24()&&d("MViewport").getScrollTop()>100&&this.withinProximity())&&this.$32()};b.getPersistOnReload=function(){return this.$14};return a}();a.TRY_AGAIN_DELAY=1500;g["default"]=a}),98);
__d("InitMMoreItemAutomatic",["MMoreItemAutomatic","Stratcom"],(function(a,b,c,d,e,f,g){var h;function a(a){h||(h={},c("Stratcom").listen("m:page:loading",null,function(a){for(var a in h)h[a].getPersistOnReload()||(h[a].uninstall(),delete h[a])}));var b=a.id;h[b]&&h[b].uninstall();h[b]=new(c("MMoreItemAutomatic"))(a)}g.main=a}),98);
__d("MLayerHideOnScroll",["Stratcom"],(function(a,b,c,d,e,f){var g=5;a=function(){"use strict";function a(a){this.$1=null,this.$2=null,this.$3=a,this.$4=null}var c=a.prototype;c.enable=function(){this.$1=[this.$3.addListener("show",this.$5.bind(this)),this.$3.addListener("hide",this.$6.bind(this))],this.$3.isShown()&&this.$5()};c.disable=function(){this.$6();while(this.$1.length)this.$1.pop().remove();this.$1=null};c.$6=function(){this.$2&&this.$2.remove()};c.$5=function(){var a=this,c=window;this.$4=c.scrollY;this.$2=b("Stratcom").listen("scroll",null,function(){var b=c.scrollY;Math.abs(a.$4-b)>=g&&a.$3.hide()})};return a}();e.exports=a}),null);
__d("MUFISocialSentence",["cx","fbt","Bootloader","CSS","DOM","MLiveData","Stratcom","SubscriptionsHandler"],(function(a,b,c,d,e,f,g,h){a=function(){"use strict";function a(a,c,d,e,f,g){if(!g&&!d)return;this.$1=a;this.$2=b("MLiveData").get(c);this.$3=e;this.$4=f;this.$5=g;this.$6=d;this.$7=new(b("SubscriptionsHandler"))();this.$7.addSubscriptions(this.$2.listen("change",this.onChange.bind(this)),b("Stratcom").listen("m:page:unload",null,this.onUnload.bind(this)))}var c=a.prototype;c.onChange=function(){var a=this,c=this.$2.getData();if(c.request_id===this.$4)return;b("CSS").removeClass(this.$1,"like_opt");var d=this.$5&&c.like_count?c[this.$3][0]:null,e=this.$6&&c.comment_count?c.comment_count:null;if(d&&d.text){this.$8={text:d,ftid:c.ft_ent_identifier};d=d.text;this.$6||b("Bootloader").loadModules(["MReactComponentRenderer","MUFISocialSentenceTextWithEntities.react"],function(b,c){b(c,a.$1,a.$8)},"MUFISocialSentence");return}if(d){var f=this.$3!=="like_counts"&&!c.like_fallback&&e?"div":"span";d=b("DOM").create(f,null,d);b("CSS").addClass(d,"_28wy")}e&&(e=e>1?h._({"*":"{count}\u6761\u8bc4\u8bba"},[h._param("count",e,[0])]):h._({"*":"{count}\u6761\u8bc4\u8bba"},[h._param("count",e,[0])]),e=b("DOM").create("span",{},e),b("CSS").addClass(e,"_28wy"));b("DOM").setContent(this.$1,[d,e]);this.$9(c)};c.$9=function(a){var c=this.$5&&(a.like_count||a.reactioncount);a=this.$6&&a.comment_count;b("CSS").conditionShow(this.$1,!!(c||a))};c.onUnload=function(){this.$7.release(),this.$7=null};return a}();e.exports=a}),null);
__d("MActionBubbleHelper",["cx","CSS","ODS","Stratcom"],(function(a,b,c,d,e,f,g,h){var i="enabled_action";function a(a,b,e,f){d("ODS").bumpEntityKey(2966,"rrsurfaces","action_bubble.toggle_action."+String(b)+"."+e+"."+f),c("CSS").conditionClass(a,"_58a0",b),b?c("Stratcom").addSigil(a,i):c("Stratcom").removeSigil(a,i)}g.toggleActionBubbleItem=a}),98);
__d("MActionBubbleLayout",["cx","CSS"],(function(a,b,c,d,e,f,g){a=function(){"use strict";function a(a){this.$1=null,this.$2=a}var c=a.prototype;c.enable=function(){this.$1=this.$2.addListener("adjustDimensions",this.$3.bind(this)),this.$2.isShown()&&this.$2.updatePosition()};c.disable=function(){this.$1.remove(),this.$1=null,this.$2.isShown()&&this.$2.updatePosition()};c.$3=function(){var a=this.$2;this.$4(!0);var b=this.$2.getContentRoot();b.clientWidth>=a.getMaxWidth()&&this.$4(!1)};c.$4=function(a){var c=this.$2;c.setWidth(a?"auto":"wide");c.applyWidth();b("CSS").conditionClass(c.getRoot(),"_55i0",a)};return a}();e.exports=a}),null);
__d("AccessibilityWebAssistiveTechTypedLogger",["Banzai","GeneratedLoggerUtils","nullthrows"],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1={}}var c=a.prototype;c.log=function(a){b("GeneratedLoggerUtils").log("logger:AccessibilityWebAssistiveTechLoggerConfig",this.$1,b("Banzai").BASIC,a)};c.logVital=function(a){b("GeneratedLoggerUtils").log("logger:AccessibilityWebAssistiveTechLoggerConfig",this.$1,b("Banzai").VITAL,a)};c.logImmediately=function(a){b("GeneratedLoggerUtils").log("logger:AccessibilityWebAssistiveTechLoggerConfig",this.$1,{signal:!0},a)};c.clear=function(){this.$1={};return this};c.getData=function(){return babelHelpers["extends"]({},this.$1)};c.updateData=function(a){this.$1=babelHelpers["extends"]({},this.$1,a);return this};c.setIndicatedBrowsers=function(a){this.$1.indicated_browsers=b("GeneratedLoggerUtils").serializeVector(a);return this};c.setIsVirtualCursorAction=function(a){this.$1.is_virtual_cursor_action=a;return this};c.updateExtraData=function(a){a=b("nullthrows")(b("GeneratedLoggerUtils").serializeMap(a));b("GeneratedLoggerUtils").checkExtraDataFieldNames(a,g);this.$1=babelHelpers["extends"]({},this.$1,a);return this};c.addToExtraData=function(a,b){var c={};c[a]=b;return this.updateExtraData(c)};return a}();var g={indicated_browsers:!0,is_virtual_cursor_action:!0};f["default"]=a}),66);
__d("VirtualCursorStatus",["Event","UserAgent","emptyFunction","setImmediate"],(function(a,b,c,d,e,f){var g=null,h=null;function i(){h||(h=b("Event").listen(window,"blur",function(){g=null,j()}))}function j(){h&&(h.remove(),h=null)}function a(a){g=a.keyCode,i()}function c(){g=null,j()}if(typeof window!=="undefined"&&window.document&&window.document.createElement){d=document.documentElement;if(d)if(d.addEventListener)d.addEventListener("keydown",a,!0),d.addEventListener("keyup",c,!0);else if(d.attachEvent){f=d.attachEvent;f("onkeydown",a);f("onkeyup",c)}}var k={isKeyDown:function(){return!!g},getKeyDownCode:function(){return g}},l=!1,m=!1,n=null,o=!1;function p(a){var c=new Set(),d=k.isKeyDown(),e=a.clientX,f=a.clientY,g=a.isPrimary,h=a.isTrusted,i=a.offsetX,j=a.offsetY,n=a.pointerType,o=a.mozInputSource,p=a.WEBKIT_FORCE_AT_MOUSE_DOWN,q=a.webkitForce;a=a.target;var r=a.clientWidth;a=a.clientHeight;e===0&&f===0&&i>=0&&j>=0&&m&&h&&o==null&&c.add("Chrome");l&&m&&!d&&q!=null&&q<p&&i===0&&j===0&&o==null&&c.add("Safari-edge");e===0&&f===0&&i<0&&j<0&&m&&o==null&&c.add("Safari-old");!l&&!m&&d&&g===!1&&h&&n===""&&e===0&&f===0&&i===0&&j===0&&o==null;!l&&!m&&!d&&h&&b("UserAgent").isBrowser("IE >= 10")&&o==null&&(e<0&&f<0?c.add("IE"):(i<0||i>r)&&(j<0||j>a)&&c.add("MSIE"));o===0&&h&&c.add("Firefox");return c}function q(){l=!0,b("setImmediate")(function(){l=!1})}function r(){m=!0,b("setImmediate")(function(){m=!1})}function s(a,c){n===null&&(n=p(a));o=n.size>0;a=a.target.getAttribute("data-accessibilityid")==="virtual_cursor_trigger";c(o,n,a);b("setImmediate")(function(){o=!1,n=null})}d={isVirtualCursorTriggered:function(){return o},add:function(a,c){c===void 0&&(c=b("emptyFunction"));var d=function(a){return s(a,c)};a.addEventListener("click",d);var e=b("Event").listen(a,"mousedown",q),f=b("Event").listen(a,"mouseup",r);return{remove:function(){a.removeEventListener("click",d),e.remove(),f.remove()}}}};e.exports=d}),null);
__d("AccessibilityWebVirtualCursorClickLogger",["AccessibilityWebAssistiveTechTypedLogger","VirtualCursorStatus"],(function(a,b,c,d,e,f,g){function a(a){a.forEach(function(a){d("VirtualCursorStatus").add(a,h)})}function h(a,b,d){d===void 0&&(d=!1),a&&new(c("AccessibilityWebAssistiveTechTypedLogger"))().setIndicatedBrowsers(b).setIsVirtualCursorAction(d).log()}g.init=a;g._log=h}),98);
__d("GHLBox",["GhlTennisKnobsConfig","Promise","WebStorage","gkx","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("json-bigint").__setRef("GHLBox"),i=atob("YXJlc19sYXN0X3NpZ25hbF9mbHVzaA=="),j=atob("YmR6X3BsYXliYWNrX3N0YXRl"),k=36,l=c("gkx")("3499"),m=c("GhlTennisKnobsConfig").ghlbox_log_validity_in_mins*60*1e3,n=c("GhlTennisKnobsConfig").ghlbox_initialize_in_mins*60*1e3;a=3;d=function(){return new(b("Promise"))(function(a){h.onReady(function(b){var c=o(b,!0);b=o(b);if(c===0&&b===0)return a(!1);if(c!==b){p(Date.now());return a(!0)}return Date.now()-b<=m?a(!0):a(!1)})})};var o=function(a,b){b===void 0&&(b=!1);var d=c("WebStorage").getLocalStorageForRead();if(!d||!l)return 0;var e;if(!b){b=(b=d.getItem(i))!=null?b:"";e=Number.parseInt(b,10)}else{b=(d=(a=a.parse(d.getItem(j)))==null?void 0:a.session_key)!=null?d:"";e=Number.parseInt(b,k)}return!Number.isFinite(e)?0:e};e=function(){var a=c("WebStorage").getLocalStorage();if(!a||!l)return;if(a.getItem(i)!==null)return;if(a.getItem(j)!==null)return;p(Date.now()-n)};var p=function(a){h.onReady(function(b){var d=c("WebStorage").getLocalStorage();if(d&&l){d.setItem(i,a.toString());var e={session_key:a.toString(k),buffer_length:60};d.setItem(j,b.stringify(e))}})};f=p;d=d;e=e;g.MinimumHiddenAdsToUpdateLocalStorage=a;g.s=f;g.r=d;g.i=e}),98);
__d("GHLTestElement",["csx","invariant","ODS","Parent","URI","containsNode","getElementPosition","gkx"],(function(a,b,c,d,e,f,g,h){"use strict";var i;a=function(a,c){var d=Array.from(a.querySelectorAll("img"));if(b("gkx")("1059877")){var e=(i||(i=b("URI"))).getRequestURI();e=e!=null?e.getPath():"";var f="images"+c,g="length_"+String(d.length);m(f+"."+g);m(f+".path_"+e+"."+g)}d.length>0||h(0,13937);f=d.filter(j);if(f.length===0){b("gkx")("1059877")&&j(a)&&m("images_removed_but_element_exists");m("skipping_check_for_incompatible_element"+c);return!1}e=f.filter(function(a){a=b("getElementPosition")(a);return a.width>0||a.height>0});g=e.length===0;return g&&!k(a)};var j=function(a){var c;return b("containsNode")(a==null?void 0:(c=a.ownerDocument)==null?void 0:c.documentElement,a)},k=function(a){return!!b("Parent").bySelector(a,l)},l=[".hidden_elem","._5ds2","._i6m"].join(","),m=function(a){return b("ODS").bumpEntityKey(2966,"feed_ads","GHLTestElement.testElementI."+a)};e.exports={testElementI:a}}),null);
__d("ghlTestUBT",["cr:1543261","cr:334"],(function(a,b,c,d,e,f,g){"use strict";b("cr:1543261")&&b("cr:1543261")(),g["default"]=b("cr:334")}),98);
__d("CometLruCache",["recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h=function(){function a(a){this.$1=a,a<=0&&c("recoverableViolation")("CometLruCache: Unable to create instance of cache with zero or negative capacity.","CometLruCache"),this.$2=new Map()}var b=a.prototype;b.set=function(a,b){this.$2["delete"](a);this.$2.set(a,b);if(this.$2.size>this.$1){a=this.$2.keys().next();a.done||this.$2["delete"](a.value)}};b.get=function(a){var b=this.$2.get(a);b!=null&&(this.$2["delete"](a),this.$2.set(a,b));return b};b.has=function(a){return this.$2.has(a)};b["delete"]=function(a){this.$2["delete"](a)};b.size=function(){return this.$2.size};b.capacity=function(){return this.$1-this.$2.size};b.clear=function(){this.$2.clear()};return a}();function a(a){return new h(a)}g.create=a}),98);
__d("ConstUriUtils",["CometLruCache","FBLogger","PHPQuerySerializer","PHPQuerySerializerNoEncoding","URIRFC3986","URISchemes","UriNeedRawQuerySVConfig","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h=d("CometLruCache").create(5e3),i=new RegExp("(^|\\.)facebook\\.com$","i"),j=new RegExp("^(?:[^/]*:|[\\x00-\\x1f]*/[\\x00-\\x1f]*/)"),k=new RegExp("[\\x00-\\x2c\\x2f\\x3b-\\x40\\x5c\\x5e\\x60\\x7b-\\x7f\\uFDD0-\\uFDEF\\uFFF0-\\uFFFF\\u2047\\u2048\\uFE56\\uFE5F\\uFF03\\uFF0F\\uFF1F]"),l=c("UriNeedRawQuerySVConfig").uris.map(function(a){return{domain:a,valid:r(a)}}),m=[];function n(a,b){var d={};if(a!=null)for(var a=a.entries(),e=Array.isArray(a),f=0,a=e?a:a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]();;){var g;if(e){if(f>=a.length)break;g=a[f++]}else{f=a.next();if(f.done)break;g=f.value}g=g;d[g[0]]=g[1]}else c("FBLogger")("ConstUriUtils").warn("Passed a null query map in, this means poor client side flow coverage or client/server boundary type issue.");return b.serialize(d)}function o(a,b,d){var e=c("PHPQuerySerializer");if(["http","https"].includes(b)&&p(a)){if(a.includes("doubleclick.net")&&d!=null&&!d.startsWith("http"))return e;e=c("PHPQuerySerializerNoEncoding")}return e}function p(a){return a!=null&&l.some(function(b){return b.valid&&q(a,b.domain)})}function q(a,b){if(b===""||a==="")return!1;if(a.endsWith(b)){b=a.length-b.length-1;if(b===-1||a[b]===".")return!0}return!1}function r(a){return!k.test(a)}function s(a,b){var c=b.protocol!=null&&b.protocol!==""?b.protocol:a.getProtocol();c=b.domain!=null?o(b.domain,c):a.getSerializer();c={domain:a.getDomain(),fragment:a.getFragment(),fragmentSeparator:a.hasFragmentSeparator(),isGeneric:a.isGeneric(),originalRawQuery:a.getOriginalRawQuery(),path:a.getPath(),port:a.getPort(),protocol:a.getProtocol(),queryParams:a.getQueryParams(),serializer:c,subdomain:a.getSubdomain()};a=babelHelpers["extends"]({},c,b);c=b.queryParams!=null&&b.queryParams.size!==0;return x.getUribyObject(a,c)}function t(a,b,c,d){c===void 0&&(c=!1);var e=a.protocol!==""?a.protocol+":"+(a.isGeneric?"":"//"):"",f=a.domain!==""?a.domain:"",g=a.port!==""?":"+a.port:"",h=a.path!==""?a.path:e!==""&&e!=="mailto:"||f!==""||g!==""?"/":"";c=u(f,a.originalRawQuery,a.queryParams,b,c,(b=d)!=null?b:a.serializer);d=c.length>0?"?":"";b=a.fragment!==""?"#"+a.fragment:"";a=a.fragment===""&&a.fragmentSeparator?"#":"";return""+e+f+g+h+d+c+a+b}function u(a,b,c,d,e,f){e===void 0&&(e=!1);if(!d&&(e||p(a))){return(d=b)!=null?d:""}return n(c,f)}function v(a){var b=a.trim();b=d("URIRFC3986").parse(b)||{fragment:null,host:null,isGenericURI:!1,query:null,scheme:null,userinfo:null};var c=b.host||"",e=c.split(".");e=e.length>=3?e[0]:"";var f=o(c,b.scheme||"",b.query),g=f.deserialize(b.query||"")||{};g=new Map(Object.entries(g));g=w({domain:c,fragment:b.fragment||"",fragmentSeparator:b.fragment==="",isGeneric:b.isGenericURI,originalRawQuery:b.query,path:b.path||"",port:b.port!=null?String(b.port):"",protocol:(b.scheme||"").toLowerCase(),queryParams:g,serializer:f,subdomain:e,userInfo:(c=b==null?void 0:b.userinfo)!=null?c:""},a);return g}function w(a,b){var c={components:babelHelpers["extends"]({},a),error:"",valid:!0},e=c.components;if(!d("URISchemes").isAllowed(a.protocol)){c.valid=!1;c.error='The URI protocol "'+String(a.protocol)+'" is not allowed.';return c}if(!r(a.domain||"")){c.valid=!1;c.error="This is an unsafe domain "+String(a.domain);return c}e.port=a.port!=null&&String(a.port)||"";if(Boolean(a.userInfo)){c.valid=!1;c.error="Invalid URI: (userinfo is not allowed in a URI "+String(a.userInfo)+")";return c}a=b!=null&&b!==""?b:t(e,!1);if(e.domain===""&&e.path.indexOf("\\")!==-1){c.valid=!1;c.error="Invalid URI: (no domain but multiple back-slashes "+a+")";return c}if(!e.protocol&&j.test(a)){c.valid=!1;c.error="Invalid URI: (unsafe protocol-relative URI "+a+")";return c}if(e.domain!==""&&e.path!==""&&!e.path.startsWith("/")){c.valid=!1;c.error="Invalid URI: (domain and pathwhere path lacks leading slash "+a+")";return c}return c}var x=function(){function a(a){this.queryParams=new Map(),this.domain=a.domain,this.fragment=a.fragment,this.fragmentSeparator=Boolean(a.fragmentSeparator),this.isGenericProtocol=Boolean(a.isGeneric),this.path=a.path,this.originalRawQuery=a.originalRawQuery,this.port=a.port,this.protocol=a.protocol,this.queryParams=a.queryParams,this.serializer=a.serializer,this.subdomain=a.subdomain}var b=a.prototype;b.addQueryParam=function(a,b){if(Boolean(a)){var c=this.getQueryParams();c.set(a,b);return s(this,{queryParams:c})}return this};b.addQueryParams=function(a){if(a.size>0){var b=this.getQueryParams();a.forEach(function(a,c){b.set(c,a)});return s(this,{queryParams:b})}return this};b.addQueryParamString=function(a){if(Boolean(a)){a=a.startsWith("?")?a.slice(1):a;var b=this.getQueryParams();a.split("&").map(function(a){a=a.split("=");var c=a[0];a=a[1];b.set(c,a)});return s(this,{queryParams:b})}return this};b.addTrailingSlash=function(){var a=this.getPath();return a.length>0&&a[a.length-1]!=="/"?this.setPath(a+"/"):this};b.getDomain=function(){return this.domain};b.getFragment=function(){return this.fragment};b.getOrigin=function(){var a=this.getPort();return this.getProtocol()+"://"+this.getDomain()+(a?":"+a:"")};b.getOriginalRawQuery=function(){return this.originalRawQuery};b.getPath=function(){return this.path};b.getPort=function(){return this.port};b.getProtocol=function(){return this.protocol.toLowerCase()};b.getQualifiedUri=function(){if(!this.getDomain()){var b=String(window.location.href);b=b.slice(0,b.indexOf("/",b.indexOf(":")+3));return a.getUri(b+this.toString())}return this};b.getQueryParam=function(a){a=this.queryParams.get(a);if(typeof a==="string")return a;else{a=JSON.stringify(a);return a==null?a:JSON.parse(a)}};b.getQueryData=function(){return Object.fromEntries(this.getQueryParams())};b.getQueryParams=function(){return new Map(JSON.parse(JSON.stringify(Array.from(this.queryParams))))};b.getQueryString=function(a){a===void 0&&(a=!1);return u(this.domain,this.originalRawQuery,this.queryParams,!1,a,this.serializer)};b.getRegisteredDomain=function(){if(!this.getDomain())return"";if(!this.isFacebookUri())return null;var a=this.getDomain().split("."),b=a.indexOf("facebook");b===-1&&(b=a.indexOf("workplace"));return a.slice(b).join(".")};b.getSerializer=function(){return this.serializer};b.getSubdomain=function(){return this.subdomain};b.getUnqualifiedUri=function(){if(this.getDomain()){var b=this.toString();return a.getUri(b.slice(b.indexOf("/",b.indexOf(":")+3)))}return this};a.getUri=function(b){b=b.trim();var d=h.get(b);if(d==null){var e=v(b);if(e.valid)d=new a(e.components),h.set(b,d);else{c("FBLogger")("ConstUriUtils").blameToPreviousFrame().warn(e.error);return null}}return d};a.getUribyObject=function(b,d){var e=t(b,d),f=h.get(e);if(f==null){d&&(b.originalRawQuery=n(b.queryParams,b.serializer));d=w(b);if(d.valid)f=new a(d.components),h.set(e,f);else{c("recoverableViolation")(d.error,"ConstUri");return null}}return f};b.hasFragmentSeparator=function(){return this.fragmentSeparator};b.isEmpty=function(){return!(this.getPath()||this.getProtocol()||this.getDomain()||this.getPort()||this.queryParams.size>0||this.getFragment())};b.isFacebookUri=function(){var a=this.toString();if(a==="")return!1;return!this.getDomain()&&!this.getProtocol()?!0:["https","http"].indexOf(this.getProtocol())!==-1&&i.test(this.getDomain())};b.isGeneric=function(){return this.isGenericProtocol};b.isSameOrigin=function(a){if(this.getProtocol()&&this.getProtocol()!==a.getProtocol())return!1;if(this.getDomain()&&this.getDomain()!==a.getDomain())return!1;if(this.getPort()&&this.getPort()!==a.getPort())return!1;return this.toString()===""||a.toString()===""?!1:!0};b.isSubdomainOfDomain=function(b){var c=a.getUri(b);return c!=null&&q(this.domain,b)};b.isSecure=function(){return this.getProtocol()==="https"};b.removeQueryParams=function(a){if(Array.isArray(a)&&a.length>0){var b=this.getQueryParams();a.map(function(a){return b["delete"](a)});return s(this,{queryParams:b})}return this};b.removeQueryParam=function(a){if(Boolean(a)){var b=this.getQueryParams();b["delete"](a);return s(this,{queryParams:b})}return this};b.replaceQueryParam=function(a,b){if(Boolean(a)){var c=this.getQueryParams();c.set(a,b);return s(this,{queryParams:c})}return this};b.replaceQueryParams=function(a){return s(this,{queryParams:a})};b.replaceQueryParamString=function(a){if(a!=null){a=a.startsWith("?")?a.slice(1):a;var b=this.getQueryParams();a.split("&").map(function(a){a=a.split("=");var c=a[0];a=a[1];b.set(c,a)});return s(this,{queryParams:b})}return this};b.setDomain=function(a){if(Boolean(a)){var b=a.split(".");b=b.length>=3?b[0]:"";return s(this,{domain:a,subdomain:b})}return this};b.setFragment=function(a){return a==="#"?s(this,{fragment:"",fragmentSeparator:!0}):s(this,{fragment:a,fragmentSeparator:a!==""})};b.setPath=function(a){return a!=null?s(this,{path:a}):this};b.setPort=function(a){return Boolean(a)?s(this,{port:a}):this};b.setProtocol=function(a){return Boolean(a)?s(this,{protocol:a}):this};b.setSecure=function(a){return this.setProtocol(a?"https":"http")};b.setSubDomain=function(a){if(Boolean(a)){var b=this.domain.split(".");b.length>=3?b[0]=a:b.unshift(a);return s(this,{domain:b.join("."),subdomain:a})}return this};b.stripTrailingSlash=function(){return this.setPath(this.getPath().replace(/\/$/,""))};a.$1=function(a){a=a;for(var b=0;b<m.length;b++){var c=m[b];a=c(a)}return a};b.$2=function(b,c){c===void 0&&(c=!1);return t({domain:a.$1(this.domain),fragment:this.fragment,fragmentSeparator:this.fragmentSeparator,isGeneric:this.isGenericProtocol,originalRawQuery:this.originalRawQuery,path:this.path,port:this.port,protocol:this.protocol,queryParams:this.queryParams,serializer:b,subdomain:this.subdomain,userInfo:""},!1,c)};b.toStringRawQuery=function(){this.rawStringValue==null&&(this.rawStringValue=this.$2(c("PHPQuerySerializerNoEncoding")));return this.rawStringValue};b.toString=function(){this.stringValue==null&&(this.stringValue=this.$2(this.serializer));return this.stringValue};b.toStringPreserveQuery=function(){return this.$2(this.serializer,!0)};a.isValidUri=function(b){var c=h.get(b);if(c!=null)return!0;c=v(b);if(c.valid){h.set(b,new a(c.components));return!0}return!1};return a}();function a(a){if(a instanceof x)return a;else return null}function b(a){m.push(a)}e=x.getUri;f=x.isValidUri;g.isSubdomainOfDomain=q;g.isConstUri=a;g.registerDomainFilter=b;g.getUri=e;g.isValidUri=f}),98);
__d("ghlTestUBTFacebook",["ConstUriUtils","GHLBox","Promise","cr:1088657","ghlInternalBumpODSKey","promiseDone","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h=function(a){a=d("ConstUriUtils").getUri(a);if(a==null)throw c("unrecoverableViolation")("URI cannot be null in ghlTestUBTFacebook.","ad_blocker_defense_ghost_owl");return a},i=function(a){return new(b("Promise"))(function(b){var c=window.atob,d=document.body;if(c==null||d==null){b(!1);return}var e=document.createElement("div");e.className=a.map(c).join(" ");d.appendChild(e);c=l(e);if(c&&c.MozBinding&&c.MozBinding.startsWith("url")){setTimeout(function(){return b(e.clientWidth===0)},5e3);return}b((c==null?void 0:c.display)==="none")})},j=function(){return new(b("Promise"))(function(a){var b=document.body;if(b==null){a(!1);return}var c=document.createElement("img");c.setAttribute("alt","");b.appendChild(c);c.onload=function(){var b=l(c);a((b==null?void 0:b.display)==="none");c.parentNode&&c.parentNode.removeChild(c)};c.onerror=function(){a(!0),c.parentNode&&c.parentNode.removeChild(c)};c.src=h("https://scontent.xx.fbcdn.net/hads-ak-prn2/1487645_6012475414660_1439393861_n.png").toString()})},k=null;a=function(a){k==null&&(p("init"),k=new(b("Promise"))(function(a){return c("promiseDone")(b("Promise").all([i(m),j(),i([n(o)]),d("GHLBox").r()]),function(b){var c=b[0],e=b[1],f=b[2];b=b[3];p("done",c,e,f);c=c||e||f;c?d("GHLBox").s(Date.now()):d("GHLBox").i();a([c,b])})})),c("promiseDone")(k,function(b){return a(b[0],b[1])}),b("cr:1088657")&&b("cr:1088657").i()};var l=function(a){return window.getComputedStyle(a)},m=["QWRCb3g=","QWQ=","YWR2ZXJ0","cG9zdC1hZHM="],n=function(a){return a[Math.floor(Math.random()*a.length)]},o=["UmVjdGFuZ2xlQWQ=","YWR2ZXJ0aXNpbmdfd2lkZ2V0","ZG93bmxvYWRfbGlua19zcG9uc29yZWQ=","c3BvbnNvclBvc3Q=","d2lkZ2V0U3BvbnNvcnM=","b2ItaG92ZXI=","ZGZwX3VuaXQ="],p=function(){for(var a=arguments.length,b=new Array(a),d=0;d<a;d++)b[d]=arguments[d];return c("ghlInternalBumpODSKey")("ghlTestUBT",String(b.join(".")))};e=a;g["default"]=e}),98);
__d("EventProfilerSampler",["EventConfig"],(function(a,b,c,d,e,f){"use strict";var g=b("EventConfig").sampling||{},h={canSample:function(a){return!!g[a]},getEventSampleWeights:function(a){a.__samplingWeights==void 0&&(a.__samplingWeights={event:i(h.getEventWeight(a))});return a.__samplingWeights},getEventWeight:function(a){a=a.type in g?g[a.type]:1;return a*g.__eventDefault},getEventInteractionIDs:function(a,b){return[]}};function i(a){if(a===0)return 0;var b=g.__min||1;a=Math.round(Math.max(b,a));return Math.random()*a<1?a:0}e.exports=h}),null);
__d("getParentClassesForEventProfiler",["cx"],(function(a,b,c,d,e,f,g,h){"use strict";function i(a){if(!a||!(a instanceof HTMLElement))return"";var b=a.id,c=a.nodeName,d=a.getAttribute("class");c=c?c.replace(/^#/,""):"unknown";b=b?"#"+b:"";d=d?" "+d.replace(/\s+/g," ").trim():"";if(a.getAttribute("rel")==="theater"){a="_342u";d=d.length?d+" "+a:a}return":"+c+b+d}function a(a){var b=[];while(a&&a instanceof HTMLElement)b.push(i(a)),a=a.parentElement;b.reverse();return b}g["default"]=a}),98);
__d("EventProfilerImpl",["Bootloader","EventConfig","EventProfilerAdsSessionProvider","EventProfilerSampler","ScriptPath","TimeSlice","UserAgent","getParentClassesForEventProfiler","performanceAbsoluteNow","requestAnimationFrameAcrossTransitions","setTimeoutAcrossTransitions","uniqueID"],(function(a,b,c,d,e,f){var g,h={},i={},j={},k=!1,l=0,m=new Set(["click","keydown","mousewheel","scroll"]),n=null,o=null;c={__wrapEventListenHandler:function(a){return b("EventConfig").disable_event_profiler?a:function(c,d){var e=this;if(!b("EventProfilerSampler").canSample(c))return a.call(this,c,d);var f={event:0},s=(g||(g=b("performanceAbsoluteNow")))();d.id=d.id||b("uniqueID")();var t=d.id,u,v=j[t],w=null;i[t]===void 0&&!v?(w=b("getParentClassesForEventProfiler")(d.target),f=r(d),o!=null&&o.beforeHandlers(t,c),u=a.call(this,c,d),j[t]=b("TimeSlice").getGuardedContinuation("Event Bubble Continuation")):(f=r(d),u=v(function(){j[t]=b("TimeSlice").getGuardedContinuation("Event Bubble Continuation");return a.call(e,c,d)}));v=g();if(i[t]===void 0){w=w;var x=q(d);x=x||s;var y=Math.max(s-x,0),z=null;b("UserAgent").isBrowser("Chrome")&&(z=!!d.cancelable);var A=z&&(!!d.deliberateSync||b("UserAgent").isBrowser("Chrome < 51"));i[t]={event_name:c,event_start_ms:Math.round(x),main_thread_wait_ms:Math.round(y),event_handlers_runtime_ms:0,script_path:b("ScriptPath").getScriptPath()||"<unknown>",request_animation_frame_wait_ms:0,set_timeout_wait_ms:0};h[t]={event_target_raw:w,weight:f.event,cancelable:!!z,deliberate_sync:!!A,ad_account_id:n,event_end_ms:0};y=b("EventProfilerAdsSessionProvider").sessionID;y&&(h[t].ads_session_id=y);var B=!1;m.has(c)&&(!k&&l<x&&(k=!0,B=!0),h[t].is_first_in_frame=B,h[t].is_first_overlapping=B);b("requestAnimationFrameAcrossTransitions")(function(){var a=(g||(g=b("performanceAbsoluteNow")))();i[t].request_animation_frame_wait_ms=Math.round(a-h[t].event_end_ms);delete h[t].event_end_ms;var d=!1;function e(){if(d)return;d=!0;var e=(g||(g=b("performanceAbsoluteNow")))();i[t].set_timeout_wait_ms=Math.round(e-a);p(t,s,e);m.has(c)&&B&&(k=!1,l=e);e=j[t];e&&delete j[t];delete i[t];delete h[t]}b("requestAnimationFrameAcrossTransitions")(e);b("setTimeoutAcrossTransitions")(e,0)})}i[t].event_handlers_runtime_ms+=v-s;h[t].event_end_ms=v;o!=null&&o.afterEachHandler(t,i[t]);return u}},setCurrentAdAccountId:function(a){n=a},setAdsConfig:function(a){o=a}};function p(a,c,d){c=i[a];c.event_handlers_runtime_ms=Math.round(c.event_handlers_runtime_ms);var e=babelHelpers["extends"]({},i[a],h[a]);o!=null&&o.beforeLog(a,e);e.weight&&b("Bootloader").loadModules(["WebSpeedInteractionsTypedLogger","PerfXSharedFields"],function(a,b){b.addCommonValues(e),new a().updateData(e).log()},"EventProfilerImpl")}var q=function(){function b(a){return null}if(!a.performance||!a.performance.now||!a.performance.timing||!a.performance.timing.navigationStart)return b;var c=a.performance.timing.navigationStart,d=a.CustomEvent&&(typeof a.CustomEvent==="function"||a.CustomEvent.toString().indexOf("CustomEventConstructor")>-1);d=d?new a.CustomEvent("test").timeStamp:a.document.createEvent("KeyboardEvent").timeStamp;return d&&d<=a.performance.now()?function(a){return a.timeStamp+c}:b}();function r(a){return o!=null?o.getEventSampleWeights(a):b("EventProfilerSampler").getEventSampleWeights(a)}e.exports=c}),null);