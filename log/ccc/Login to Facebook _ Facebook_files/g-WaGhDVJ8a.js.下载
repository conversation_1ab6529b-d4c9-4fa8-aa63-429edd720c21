if (self.CavalryLogger) { CavalryLogger.start_js_script(document.currentScript); }/*FB_PKG_DELIM*/

__d("MLogoutClearCache",["MCache","Stratcom"],(function(a,b,c,d,e,f,g){c("Stratcom").listen("click","logout",d("MCache").clear)}),34);
__d("MSeoDirectoryLinks",["cx","CSS","MVisibilityToggle"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a){var b=a.visible,e=a.toggleButton;d("MVisibilityToggle").setupOnClick({visible:b,toggleButton:e,toggledElement:a.linksContainer,onToggle:function(a){a?c("CSS").addClass(e,"_97hz"):c("CSS").removeClass(e,"_97hz")}})}g.initLinks=a}),98);
__d("Typeahead",["CSS","DOM","MParent","MViewport","Vector","createDeprecatedProperties","eventsMixinDeprecated","setTimeoutAcrossTransitions","uniqueID"],(function(a,b,c,d,e,f,g){a=function(){function a(b,e){var f=this;this.showResults=function(a){f.invoke("render_start");var b={show:a};b=f.invoke("show",b);if(f._typeObjects&&f._typeObjectsOrder){var e={};for(var g=0;g<a.length;g++){var h=a[g].firstElementChild?a[g].firstElementChild.getAttribute("renderType"):null;if(!h)continue;h in e||(e[h]=[]);e[h].push(a[g])}g=[];var i=f._typeObjectsOrder,j=f._typeObjects;if(i)for(var k=0;k<i.length;k++){h=i[k];var l=e[h];if(l){var m=document.createElement("div");d("DOM").setContent(m,j[h].xhp);g.push(m.firstChild);g.push.apply(g,l)}}d("DOM").setContent(f._root,g)}else d("DOM").setContent(f._root,a);f._display=d("DOM").scry(f._root,"a","typeahead-result");if(f._display.length&&!b.getPrevented()){f._changeHighlight(Number.NEGATIVE_INFINITY);m=c("Vector").getDim(f._hardpoint);m.x=0;m.setPos(f._root);f._root.parentNode!==f._hardpoint&&f._hardpoint.appendChild(f._root);d("DOM").show(f._root);f._control.setAttribute("aria-expanded","true")}else f.hide(),d("DOM").setContent(f._root,null);f._refreshViewport()};this.waitForResults=function(){f.hide()};this.handleEvent=function(a){var b=a.getType();if(!b)return;f.invoke(b,a);if(f._stop||a.getPrevented())return;b=="blur"?(f._hasFocus=!1,c("setTimeoutAcrossTransitions")(function(){f._hasFocus||f.hide()},f.getBlurDefer())):b=="focus"?f._hasFocus=!0:f._update(a)};this._handleListEvent=function(a){a=a.getType();a==="blur"?(f._hasFocus=!1,c("setTimeoutAcrossTransitions")(function(){f._hasFocus||f.hide()},f.getBlurDefer())):a==="focus"&&(f._hasFocus=!0)};this._handleRootTouch=function(a){if(a.getType()==="touchstart"){var b=a.getTouch(),d=b.pageX;b=b.pageY;f._rootTouchPos={x:d,y:b};d=a.getNode("tag:a");d instanceof HTMLAnchorElement&&(f._rootTouchLink=d);b=f._focus;if(b){d=f._display[b];d&&(c("CSS").conditionClass(d,"focused",!1),f._focus=-1)}}else if(f._rootTouchPos){b=f._rootTouchLink;if(b&&b===a.getNode("tag:a")){d=a.getTouch();var e=d.pageX;d=d.pageY;var g=f._rootTouchPos,h=3;g&&Math.abs(e-g.x)<h&&Math.abs(d-g.y)<h&&f._choose(b,null)}delete f._rootTouchLink;delete f._rootTouchPos}a.getType()==="touchend"&&a.prevent()};this._hardpoint=b;this._control=e||d("DOM").find(b,"input");e=c("uniqueID")();this._control.setAttribute("aria-owns",e);this._root=d("DOM").create("div",{className:"jx-typeahead-results",sigil:"jx-typeahead-results",role:"listbox",id:e,tabIndex:-1});this._display=[];this._focus=-1;this._listener=d("DOM").listen(this._control,["focus","blur","keypress","keydown","input"],null,this.handleEvent.bind(this));this._touchListener=d("DOM").listen(this._root,["touchstart","touchend","touchcancel"],"tag:a",this._handleRootTouch.bind(this));this._listFocusListener=d("DOM").listen(this._root,["focus","blur"],null,this._handleListEvent.bind(this));b.id&&(a._instances[b.id]=this)}var b=a.prototype;b.start=function(){this.invoke("start")};b.setTypeObjects=function(a){this._typeObjects=a};b.setTypeObjectsOrder=function(a){this._typeObjectsOrder=a};b.setDatasource=function(a){this._datasource&&(this._datasource.unbindFromTypeahead(),this._waitingListener&&this._waitingListener.remove(),this._readyListener&&this._readyListener.remove()),this._waitingListener=a.listen("waiting",this.waitForResults.bind(this)),this._readyListener=a.listen("resultsready",this.showResults.bind(this)),a.bindToTypeahead(this),this._datasource=a};b.getDatasource=function(){return this._datasource};b.getTypeaheadType=function(){return"m_legacy"};b.setInputNode=function(a){this._control=a;return this};b.hide=function(){this._changeHighlight(Number.NEGATIVE_INFINITY),this._display=[],this._root&&d("DOM").hide(this._root),this._control.setAttribute("aria-expanded","false"),this._control.setAttribute("aria-activedescendant",""),this.invoke("hide",{hasFocus:this._hasFocus})};b.refresh=function(){this._refreshViewport();if(this._stop)return;this._value=this._control.value;this.invoke("update",this._value);this.invoke("change",this._value,this._control)};b._changeHighlight=function(a){a=Math.min(Math.max(-1,this._focus+a),this._display.length-1);this.getAllowNullSelection()||(a=Math.max(0,a));var b=this._focus,e=null,f=null;b>=0&&b<this._display.length&&(c("CSS").conditionClass(this._display[b],"focused",!1),e=d("MParent").bySigil(this._display[b],"jx-result"));this._focus=a;b=this._display[this._focus];a="";b&&(c("CSS").conditionClass(b,"focused",!0),f=d("MParent").bySigil(b,"jx-result"));e&&e.setAttribute("aria-selected","false");f&&(a=f.id,f.setAttribute("aria-selected","true"));this._control.setAttribute("aria-activedescendant",a);return!0};b._choose=function(a,b){b=this.invoke("choose",a,b);if(b.getPrevented())return;this._control.value=a.name;this.hide()};b.clear=function(a){a===void 0&&(a=!1),this._control.value="",this._value="",this.invoke("clear"),a&&this.hide()};b.enable=function(){this._control.disabled=!1,this._stop=!1};b.disable=function(){this._control.blur(),this._control.disabled=!0,this._stop=!0};b.submit=function(){var a=this._focus;if(a>=0&&this._display[a]){this._choose(this._display[a],a);return!0}else{a=this.invoke("query",this._control.value);if(a.getPrevented())return!0}return!1};b.setValue=function(a){this._control.value=a};b.getValue=function(){return this._control.value};b._refreshViewport=function(){var a=this.invoke("refresh-viewport");if(!a.getPrevented()){a=this._display&&this._display.length;if(a&&!this._heightConstraint){var b=c("Vector").getPos(this._root).y,e=c("Vector").getDim(this._root).y,f=d("MViewport").getBoundingRect().height;if(b+e>f){f=d("MViewport").getHeaderHeight()+b+e;this._heightConstraint=d("MViewport").addMinHeightConstraint(f)}}else this._heightConstraint&&!a&&(this._heightConstraint.release(),this._heightConstraint=null)}this.invoke("rendered")};b._update=function(a){var b=this,d=a&&a.getSpecialKey();if(d&&a.getType()=="keydown")switch(d){case"up":this._display.length&&this._changeHighlight(-1)&&a.prevent();break;case"down":this._display.length&&this._changeHighlight(1)&&a.prevent();break;case"return":if(this.submit()){a.prevent();return}break;case"esc":this._display.length&&this.getAllowNullSelection()&&(this.hide(),a.prevent());break;case"tab":return}c("setTimeoutAcrossTransitions")(function(){if(b._value==b._control.value)return;b.refresh()},0)};b.removeListener=function(){this._listener&&this._listener.remove(),this._listFocusListener&&this._listFocusListener.remove()};a.getInstance=function(b){return a._instances[b]};return a}();a._instances={};c("eventsMixinDeprecated")(a,["choose","query","start","change","show","focus","blur","keypressed","keypress","keydown","input","hide","clear","render_start","rendered","update","scroll","refresh-viewport"]);c("createDeprecatedProperties")(a,{allowNullSelection:!0,normalizer:null,blurDefer:0});g["default"]=a}),98);
__d("MTypeaheadStaticSource",["TypeaheadSource"],(function(a,b,c,d,e,f,g){a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this)||this;c&&(d.getTransformer=c);if(b)for(var c=0;c<b.length;++c)d.addResult(b[c]);return d}var c=b.prototype;c.didChange=function(a){this.matchResults(a)};return b}(c("TypeaheadSource"));g["default"]=a}),98);
__d("LoggedOutLocaleEventsClientLogger",["Banzai","BanzaiLogger"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){this.regInstance=b}var b=a.prototype;b.logLocaleEvent=function(a,b,d){a=a.getNode("change_language");if(a instanceof HTMLElement){a=a.dataset.locale;c("BanzaiLogger").create(c("Banzai").VITAL).log("LoggedOutLocaleEventsLoggerConfig",{event:"change_language_acquired",reg_instance:this.regInstance,surface:d,switch_info:{endLocale:a,step:b}})}};b.logMoreLanguage=function(a,b){c("BanzaiLogger").create(c("Banzai").VITAL).log("LoggedOutLocaleEventsLoggerConfig",{event:"more_language_clicked",reg_instance:this.regInstance,surface:b,switch_info:{step:a}})};b.logLocaleStepAcquired=function(a,b,d){c("BanzaiLogger").create(c("Banzai").VITAL).log("LoggedOutLocaleEventsLoggerConfig",{event:"step_locale_acquired",reg_instance:this.regInstance,surface:b,switch_info:{endLocale:d,step:a}})};return a}();g["default"]=a}),98);
__d("ForgetPasswordAutoSearch",["Event","findTag"],(function(a,b,c,d,e,f,g){function a(a,b,d){var e="";a=c("findTag")(a);var f=c("findTag")(b);b=c("findTag")(d);function g(){e=this.value}function h(){f.setAttribute("value",e)}c("Event").listen(a,"input",g);c("Event").listen(b,"click",h)}g.addListenerForgetPasswordAutoSearch=a}),98);
__d("LoginNativeButton",["BanzaiLogger","DOM"],(function(a,b,c,d,e,f){"use strict";var g="native_login_button_click_client";e.exports={init:function(a,c){var d=this;b("DOM").listen(a,"click",null,function(a){d._logClick(c)})},_logClick:function(a){var c=b("BanzaiLogger").create({retry:!0,delay:0});c.log("PlatformLoggedOutEventsLoggerConfig",{event:g,logger_id:a})}}}),null);
__d("MLoggedOutBannerGooglePlayRedirectWithFallback",["BanzaiLogger","DOM","DirectDownloadEvents","UserAgent","clearInterval","clearTimeout","goURI","setInterval","setTimeout"],(function(a,b,c,d,e,f,g){var h=200,i=300,j=350,k=1500,l=2500;a=function(){function a(a){this.$1=a.button,this.$2=a.native_app_url,this.$3=a.fallback_url,this.$4=a.logging_data,this.$5=null,this.$6=null,this.$7=null,this.$8=null,this.$9=0,this.$10=!0,this.$11=d("DOM").create("iframe",{style:{display:"none"}}),d("DOM").insertAfter(this.$1,this.$11),c("BanzaiLogger").create({signal:!0,delay:1e3}).log("DirectDownloadLoggerConfig",{event:c("DirectDownloadEvents").logged_out_banner_impression_client,app_referrer:this.$4.app_referrer,app:this.$4.app,country:this.$4.country}),this.$12()}var b=a.prototype;b.$12=function(){var a=this;this.$1.addEventListener("click",function(){a.$13()});this.$10=!0};b.$13=function(){this.$10&&(this.$10=!1,this.$14(),this.$15())};b.$15=function(){var a=this;a.$9=Date.now();var b=function(){a.$16(a.$3)},d=function(){a.$17(a.$2,b)},e=function(){a.$6=c("setTimeout")(d,k),a.$18(a.$2,b)};a.$5=c("setInterval")(function(){a.$19()?a.$20():a.$9=Date.now()},h);c("UserAgent").isBrowser("Firefox")?e():d()};b.$17=function(a,b){var d=this;if(d.$19())return;c("clearTimeout")(d.$8);var e=!1;d.$11.src="";d.$11.onload=function(){e=!0,c("BanzaiLogger").create({signal:!0,delay:1e3}).log("DirectDownloadLoggerConfig",{event:c("DirectDownloadEvents").logged_out_banner_google_play_redirect_failed,app_referrer:d.$4.app_referrer,app:d.$4.app,country:d.$4.country}),b()};d.$11.src=a;d.$8=c("setTimeout")(function(){var c=d.$11.contentDocument||d.$11.contentWindow.document;c.readyState=="complete"&&!e&&d.$18(a,b)},j)};b.$18=function(a,b){if(this.$19())return;this.$7=c("setTimeout")(b,l);this.$16(a)};b.$20=function(){c("clearInterval")(this.$5),c("clearTimeout")(this.$6),c("clearTimeout")(this.$7),c("clearTimeout")(this.$8)};b.$19=function(){if(document.hidden||Date.now()-this.$9>=i){this.$10=!0;return!0}return!1};b.$16=function(a){if(this.$19())return;c("goURI")(a)};b.$14=function(){this.$4||(this.$4={app_referrer:"",app:"",country:""}),c("BanzaiLogger").create({signal:!0,delay:1e3}).log("DirectDownloadLoggerConfig",{event:c("DirectDownloadEvents").logged_out_banner_upgrade_button_click,app_referrer:this.$4.app_referrer,app:this.$4.app,country:this.$4.country})};return a}();g["default"]=a}),98);
__d("HeaderTransparencyConsentEventParam",[],(function(a,b,c,d,e,f){a=Object.freeze({LOGIN_BUTTON_CLICK:"ln",REG_BUTTON_CLICK:"reg"});f["default"]=a}),66);
__d("IorgMaskHeaderPrefillTypedLogger",["Banzai","GeneratedLoggerUtils"],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1={}}var c=a.prototype;c.log=function(a){b("GeneratedLoggerUtils").log("logger:IorgMaskHeaderPrefillLoggerConfig",this.$1,b("Banzai").BASIC,a)};c.logVital=function(a){b("GeneratedLoggerUtils").log("logger:IorgMaskHeaderPrefillLoggerConfig",this.$1,b("Banzai").VITAL,a)};c.logImmediately=function(a){b("GeneratedLoggerUtils").log("logger:IorgMaskHeaderPrefillLoggerConfig",this.$1,{signal:!0},a)};c.clear=function(){this.$1={};return this};c.getData=function(){return babelHelpers["extends"]({},this.$1)};c.updateData=function(a){this.$1=babelHelpers["extends"]({},this.$1,a);return this};c.setEvent=function(a){this.$1.event=a;return this};c.setMobileContext=function(a){this.$1.mobile_context=a;return this};c.setNumDigitsShown=function(a){this.$1.num_digits_shown=a;return this};c.setPhoneNumberLength=function(a){this.$1.phone_number_length=a;return this};c.setUnmaskingAssociatedIdentifier=function(a){this.$1.unmasking_associated_identifier=a;return this};c.setUnmaskingDatr=function(a){this.$1.unmasking_datr=a;return this};c.setUnmaskingDatrAge=function(a){this.$1.unmasking_datr_age=a;return this};return a}();c={event:!0,mobile_context:!0,num_digits_shown:!0,phone_number_length:!0,unmasking_associated_identifier:!0,unmasking_datr:!0,unmasking_datr_age:!0};f["default"]=a}),66);
__d("MButton",["cx","invariant","CSS","DOM"],(function(a,b,c,d,e,f,g,h,i){var j="_2347";function k(a){d("DOM").isType(a,"button")||d("DOM").isType(a,"a")||i(0,2447,a.nodeType)}function l(a){k(a);a=a;a=a;return a.disabled===!1||d("DOM").isType(a,"a")&&!c("CSS").hasClass(a,j)}function a(a,b){k(a);if(l(a)===b)return;a=a;a=a;d("DOM").isType(a,"button")?a.disabled=!b:(c("CSS").conditionClass(a,j,!b),b?(a.removeAttribute("tabindex"),a.removeAttribute("aria-disabled")):(a.setAttribute("tabindex","-1"),a.setAttribute("aria-disabled","true")))}g.isEnabled=l;g.setEnabled=a}),98);
__d("MtouchRegClientEventsTypedLogger",["Banzai","GeneratedLoggerUtils"],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1={}}var c=a.prototype;c.log=function(a){b("GeneratedLoggerUtils").log("logger:MtouchRegClientEventsLoggerConfig",this.$1,b("Banzai").BASIC,a)};c.logVital=function(a){b("GeneratedLoggerUtils").log("logger:MtouchRegClientEventsLoggerConfig",this.$1,b("Banzai").VITAL,a)};c.logImmediately=function(a){b("GeneratedLoggerUtils").log("logger:MtouchRegClientEventsLoggerConfig",this.$1,{signal:!0},a)};c.clear=function(){this.$1={};return this};c.getData=function(){return babelHelpers["extends"]({},this.$1)};c.updateData=function(a){this.$1=babelHelpers["extends"]({},this.$1,a);return this};c.setEvent=function(a){this.$1.event=a;return this};c.setMtouchInstalledRelatedApps=function(a){this.$1.mtouch_installed_related_apps=b("GeneratedLoggerUtils").serializeVector(a);return this};c.setNumWordsFirstName=function(a){this.$1.num_words_first_name=a;return this};c.setNumWordsLastName=function(a){this.$1.num_words_last_name=a;return this};c.setRegImpressionID=function(a){this.$1.reg_impression_id=a;return this};c.setRegInstance=function(a){this.$1.reg_instance=a;return this};return a}();c={event:!0,mtouch_installed_related_apps:!0,num_words_first_name:!0,num_words_last_name:!0,reg_impression_id:!0,reg_instance:!0};f["default"]=a}),66);
__d("MGetInstalledRelatedApps",["FBLogger","MtouchRegClientEventsTypedLogger","regeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){return b("regeneratorRuntime").async(function(c){while(1)switch(c.prev=c.next){case 0:h=a;c.next=3;return b("regeneratorRuntime").awrap(i("lid_has_app_installed",a));case 3:case"end":return c.stop()}},null,this)}function d(){return b("regeneratorRuntime").async(function(a){while(1)switch(a.prev=a.next){case 0:if(!(h==null)){a.next=3;break}c("FBLogger")("mtouch_installed_related_apps").event("mtouch_related_apps.datr_not_stored").warn("Race condition may occur when trying to read the stored datr",",this happens if user starts registration before the login view renders completely");return a.abrupt("return");case 3:a.next=5;return b("regeneratorRuntime").awrap(i("lid_has_app_installed_on_reg_start",h));case 5:case"end":return a.stop()}},null,this)}function i(a,d){var e,f;return b("regeneratorRuntime").async(function(g){while(1)switch(g.prev=g.next){case 0:g.next=2;return b("regeneratorRuntime").awrap(j());case 2:e=g.sent,f=[],e!=null&&(e.forEach(function(a){f.push({package_name:a.id,mobile_store_platform:a.platform,app_version:a.version})}),new(c("MtouchRegClientEventsTypedLogger"))().setEvent(a).setMtouchInstalledRelatedApps(f).setRegInstance(d).setRegImpressionID(d).log());case 5:case"end":return g.stop()}},null,this)}function j(){var a;return b("regeneratorRuntime").async(function(d){while(1)switch(d.prev=d.next){case 0:if(!(typeof navigator.getInstalledRelatedApps!=="function")){d.next=3;break}c("FBLogger")("mtouch_installed_related_apps").event("mtouch_related_apps.function_not_found").warn("The chromium api getInstalledRelatedApps is not found in the lids   browser");return d.abrupt("return",null);case 3:d.prev=3;d.next=6;return b("regeneratorRuntime").awrap(navigator.getInstalledRelatedApps());case 6:a=d.sent;return d.abrupt("return",a);case 10:d.prev=10;d.t0=d["catch"](3);c("FBLogger")("mtouch_installed_related_apps").event("mtouch_related_apps.encountered_exception").catching(d.t0).warn("get installed related apps chrome api returned exception");return d.abrupt("return",null);case 14:case"end":return d.stop()}},null,this,[[3,10]])}g.logmTouchInstalledRelatedApps=a;g.logmTouchInstalledRelatedAppsOnRegStart=d}),98);
__d("MTypeaheadEmailDomainSource",["MTypeaheadStaticSource"],(function(a,b,c,d,e,f,g){var h=3;a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){b=a.call(this,b,c)||this;b.$MTypeaheadEmailDomainSource1="";return b}var c=b.prototype;c.getQueryExtractor=function(){var a=this;return function(b){b=b.toLowerCase().split("@");var c=b[0];b=b[1];if(b==null)return"";a.$MTypeaheadEmailDomainSource1=c;return"@"+b}};c.getSortHandler=function(){return function(a,b,c){b.splice(h,b.length-h)}};c.createNode=function(b){return a.prototype.createNode.call(this,babelHelpers["extends"]({},b,{name:""+this.$MTypeaheadEmailDomainSource1+b.name,display:""+this.$MTypeaheadEmailDomainSource1+b.name,resultReturnLimit:""+b.resultReturnLimit}))};return b}(c("MTypeaheadStaticSource"));g["default"]=a}),98);
__d("MLoginEmailDomainTypeahead",["DOM","MRegTopDomainsConfig","MTypeaheadEmailDomainSource","Style","Typeahead"],(function(a,b,c,d,e,f,g){"use strict";var h={};function a(a){if(c("MRegTopDomainsConfig").topEmailDomains.length===0)return;try{var b=d("DOM").find(a,"input"),e=b.parentNode;e=new(c("Typeahead"))(e,b);var f=0;e.setDatasource(new(c("MTypeaheadEmailDomainSource"))([].concat(c("MRegTopDomainsConfig").topEmailDomains),function(){return function(a){return{name:"@"+a,display:"@"+a,uri:"#",id:f++}}}));var g=null;e.listen("keydown",function(a){g=a});e.listen("show",function(b){b&&b.show&&b.show.length>0&&!h[a]&&(h[a]=!0)});e.listen("hide",function(){h[a]=!1});e.listen("choose",function(){g&&(g.kill(),g=null)});e.listen("rendered",function(){try{var e=d("DOM").find(a,"div","jx-typeahead-results"),f=b.parentNode.style.width;f&&c("Style").set(e,"width",f)}catch(a){}})}catch(a){}}g.init=a}),98);
__d("MLoginFormError",["cx","invariant","CSS","DOM"],(function(a,b,c,d,e,f,g,h,i){"use strict";var j="_3qsy",k="_3qs-";function a(a,b){b!==null||i(0,653),c("CSS").addClass(a,j),c("CSS").addClass(a,k),d("DOM").listen(a,"input",null,function(b){c("CSS").removeClass(a,k)})}function b(a){c("CSS").removeClass(a,j),c("CSS").removeClass(a,k)}g.set=a;g.unset=b}),98);
__d("ZeroJioHeaderTypedLogger",["Banzai","GeneratedLoggerUtils"],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1={}}var c=a.prototype;c.log=function(a){b("GeneratedLoggerUtils").log("logger:ZeroJioHeaderLoggerConfig",this.$1,b("Banzai").BASIC,a)};c.logVital=function(a){b("GeneratedLoggerUtils").log("logger:ZeroJioHeaderLoggerConfig",this.$1,b("Banzai").VITAL,a)};c.logImmediately=function(a){b("GeneratedLoggerUtils").log("logger:ZeroJioHeaderLoggerConfig",this.$1,{signal:!0},a)};c.clear=function(){this.$1={};return this};c.getData=function(){return babelHelpers["extends"]({},this.$1)};c.updateData=function(a){this.$1=babelHelpers["extends"]({},this.$1,a);return this};c.setAutoConfMatch=function(a){this.$1.auto_conf_match=a;return this};c.setAutoConfValidationParams=function(a){this.$1.auto_conf_validation_params=b("GeneratedLoggerUtils").serializeMap(a);return this};c.setCarrierID=function(a){this.$1.carrier_id=a;return this};c.setError=function(a){this.$1.error=a;return this};c.setEvent=function(a){this.$1.event=a;return this};c.setExceptionMessage=function(a){this.$1.exception_message=a;return this};c.setExceptionStackTrace=function(a){this.$1.exception_stack_trace=a;return this};c.setHeaderSignature=function(a){this.$1.header_signature=a;return this};c.setIsV2API=function(a){this.$1.is_v2_api=a;return this};c.setJioAPISurface=function(a){this.$1.jio_api_surface=a;return this};c.setLoggedOutID=function(a){this.$1.logged_out_id=a;return this};c.setMaskedDecryptedMsisdn=function(a){this.$1.masked_decrypted_msisdn=a;return this};c.setMaskedPrefill=function(a){this.$1.masked_prefill=a;return this};c.setPossibleSoftmatchUid=function(a){this.$1.possible_softmatch_uid=a;return this};c.setPrefillSource=function(a){this.$1.prefill_source=a;return this};c.setResponseTime=function(a){this.$1.response_time=a;return this};c.setTimeToPrefill=function(a){this.$1.time_to_prefill=a;return this};c.setUserRegisterPhoneNumber=function(a){this.$1.user_register_phone_number=a;return this};return a}();c={auto_conf_match:!0,auto_conf_validation_params:!0,carrier_id:!0,error:!0,event:!0,exception_message:!0,exception_stack_trace:!0,header_signature:!0,is_v2_api:!0,jio_api_surface:!0,logged_out_id:!0,masked_decrypted_msisdn:!0,masked_prefill:!0,possible_softmatch_uid:!0,prefill_source:!0,response_time:!0,time_to_prefill:!0,user_register_phone_number:!0};f["default"]=a}),66);
__d("MMsisdnJioApiModule",["DOM","DateConsts","KaiOSController","KaiOSSendMessageUtil","WebStorage","ZeroJioHeaderTypedLogger","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h="login_identify_search_placeholder",i=2;function j(a){var b=c("WebStorage").getLocalStorage();if(b){var d=a.get("msisdn");b.setItem("fb_jio_msisdn_value",d?d:"");d=a.get("timeStamp");b.setItem("fb_jio_msisdn_timestamp",d?d:"");d=a.get("signature");b.setItem("fb_jio_msisdn_signature",d?d:"");d=a.get("unique");b.setItem("fb_jio_msisdn_salt",d?d:"")}}function a(){var a=c("WebStorage").getLocalStorage();if(!a)return null;var b=a.getItem("fb_jio_msisdn_timestamp");if(!b||Date.now()>Number(b)+i*d("DateConsts").MS_PER_DAY)return null;b=a.getItem("fb_jio_msisdn_value");var e=a.getItem("fb_jio_msisdn_timestamp"),f=a.getItem("fb_jio_msisdn_signature");a=a.getItem("fb_jio_msisdn_salt");if(!b||!e||!f||!a)return null;var g=new Map();g.set("fb_jio_msisdn_value",b);g.set("fb_jio_msisdn_timestamp",e);g.set("fb_jio_msisdn_signature",f);g.set("fb_jio_msisdn_salt",a);return g}function b(){var a=Date.now();k();c("promiseDone")(d("KaiOSController").waitForKaiOS(),function(){d("KaiOSController").sendMessageToAppWithCallback(d("KaiOSSendMessageUtil").Type.FETCH_MSISDN,function(b){l(Date.now()-a);var c=b.get("msisdn");c&&j(b)})})}function k(a){new(c("ZeroJioHeaderTypedLogger"))().setEvent("jio_api_request").setJioAPISurface(a).log()}function l(a,b){new(c("ZeroJioHeaderTypedLogger"))().setEvent("jio_api_response").setResponseTime(a).setJioAPISurface(b).log()}function e(a){var b=Date.now(),e=d("DOM").find(a,"input",h);e.value||(k("account_recovery"),c("promiseDone")(d("KaiOSController").waitForKaiOS(),function(){d("KaiOSController").sendMessageToAppWithCallback(d("KaiOSSendMessageUtil").Type.FETCH_MSISDN,function(a){l(Date.now()-b,"account_recovery");a=a.get("msisdn");a&&(e.value=a,new(c("ZeroJioHeaderTypedLogger"))().setEvent("account_recovery_prefill").setTimeToPrefill(Date.now()-b).log())})}))}g.cacheMsisdn=j;g.getCachedMsisdn=a;g.fetchMsisdnAndCache=b;g.logAPIRequest=k;g.logAPIResponse=l;g.prefillMsisdnForAccountRecovery=e}),98);
__d("QPLE2ESessionMarkers",["$InternalEnum"],(function(a,b,c,d,e,f){a=b("$InternalEnum")({NONE:"none",START:"start",END:"end"});c=a;f["default"]=c}),66);
__d("QPLE2E",["QuickPerformanceLogger"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){}var b=a.prototype;b.logPoint=function(a,b,d,e){var f,g;e===void 0&&(e={});f=(f=e.timestamp)!=null?f:c("QuickPerformanceLogger").currentTimestamp();var h=babelHelpers["extends"]({},e.pointData);g=(g=e.action)!=null?g:12524;e.secondaryOrder!=void 0&&(h.string||(h.string={}),h.string.qpl_e2e__secondary_order=e.secondaryOrder);if(e.sessionMarker&&e.sessionMarker!=="none"){var i;h.bool||(h.bool={});h.bool=babelHelpers["extends"]({},h.bool,(i={},i.qpl_e2e__session_marker_start_point=e.sessionMarker==="start",i.qpl_e2e__session_marker_end_point=e.sessionMarker==="end",i))}c("QuickPerformanceLogger").markerStart(a,void 0,f,{samplingBasis:d});c("QuickPerformanceLogger").markerPoint(a,b,{data:h,timestamp:f*2});e.annotations&&c("QuickPerformanceLogger").markerAnnotate(a,e.annotations);c("QuickPerformanceLogger").markerAnnotate(a,{string:(i={},i.join_id=d,i.source="client",i),bool:(b={},b.qpl_e2e__align_points=!0,b)});c("QuickPerformanceLogger").markerEnd(a,g,void 0,f)};return a}();b=new a();g["default"]=b}),98);
__d("PlatformOAuthDialogLoginFunnelLogger",["FBLogger","QPLE2E","URI","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h=1;a=function(a,b,d,e,f,g){e===void 0&&(e=!0);if(null==d||""===d||e&&(null==b||""===b||0===b)){c("FBLogger")("platform_login_web_funnel_client_js").mustfix("\nerror: null_required_field\naction: "+a+"\nloggerId: "+String(d)+"\ncbt: "+String(b)+"\n        ");return}e="number"!==typeof b?parseInt(b,10):b;b=Date.now()-e;var l=i(),m=j();l={string:{logger_id:d},"int":{first_paint_time:l,response_start_time:m,cbt_delta:b}};m={"int":{client_funnel_version:h,cbt:e},string:{},bool:{duo_like_passwordless_user:g}};m.string.gdp_type=f;if(a==="client_logged_out_init_impression"){b=k();m.string.login_uri=b}c("QPLE2E").logPoint(c("qpl")._(195562276,"891"),a,d,{pointData:l,annotations:m})};var i=function(){if(l()){var a=window.performance.getEntriesByType("paint").filter(function(a){return"first-paint"===a.name});if(null==a||0<!a.length)return null;a=a[0].startTime+a[0].duration;return a}return null},j=function(){if(l()){var a=window.performance.getEntriesByType("navigation");return null==a||0<!a.length?null:a[0].responseStart}return null},k=function(){return c("URI").getRequestURI().setQueryString("").toString()},l=function(){return window.performance&&typeof window.performance.getEntriesByType==="function"};g.CLIENT_FUNNEL_VERSION=h;g.log=a}),98);
__d("SpatialNavigation",["Event","Keys","TabbableElements"],(function(a,b,c,d,e,f,g){"use strict";function h(a){var b=document;b=b.body;if(b===null)return;var e=a.keyCode;if(e===c("Keys").RETURN){var f;(f=document.activeElement)==null?void 0:f.click()}if(e!==c("Keys").UP&&e!==c("Keys").DOWN)return;a.preventDefault();f=d("TabbableElements").find(b);a=f.indexOf(document.activeElement);b=f.length;e=e===c("Keys").UP?-1:1;f=f[(b+a+e)%b];f&&f.focus()}function a(){if(!navigator.userAgent.includes("PortalTV"))return;document.body!==null&&d("Event").listen(document.body,"keyup",h)}g.registerNavigationHandlerForPortalTV=a}),98);
__d("XLoginProfileInfoFetcherController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/login/pp/",{contactpoint:{type:"String"},pwdprefill:{type:"Bool",defaultValue:!1}})}),null);
__d("XLoginWebviewDetectionAsyncController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/login/async/wvdp/",{})}),null);
__d("XOAuthErrorController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/oauth/error/",{error_code:{type:"Enum",enumType:1},encrypted_query_string:{type:"String"},display:{type:"Enum",enumType:1}})}),null);
__d("XZeroHeaderUserConsentProcessController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/mobile/zero/h_consent/",{ce:{type:"Enum",required:!0,enumType:1}})}),null);
__d("XZeroTokenHeaderUserConsentProcessController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/mobile/zero/TOS_process/",{})}),null);
__d("MLoginController",["cx","fbt","CSS","DOM","DataStore","DeferredCookie","Event","FBBrowserPasswordEncryption","FBLogger","IorgMaskHeaderPrefillTypedLogger","Keys","LoggedOutLocaleEventsClientLogger","LoginServicePasswordEncryptDecryptEventTypedLogger","MAjaxify","MAnimator","MButton","MGetInstalledRelatedApps","MHistory","MLoginEmailDomainTypeahead","MLoginFormError","MMsisdnJioApiModule","MRequest","MViewport","PlatformOAuthDialogLoginFunnelLogger","Run","SpatialNavigation","Stratcom","SubscriptionsHandler","ThisControllerNoLongerExists","URI","XAsyncRequest","XLoginProfileInfoFetcherController","XLoginWebviewDetectionAsyncController","XOAuthErrorController","XZeroHeaderUserConsentProcessController","XZeroTokenHeaderUserConsentProcessController","bx","ge","gkx","goURI","killswitch","promiseDone"],(function(a,b,c,d,e,f,g,h,i){"use strict";var j="_pg8",k="stepOneIdentification",l="stepTwoAuthenticaiton",m="auth",n=500,o=5,p=/^(\/)?(v\d+\.\d+)?\/dialog\/oauth.*$/;a={_initTwoStepsListeners:function(){var a=this,b=new(c("SubscriptionsHandler"))();b.addSubscriptions(c("Stratcom").listen("click","m_login_change_cp_link",function(b){b.prevent(),a._logAction("clicked_not_you"),a._showEditableCPFieldOnAuthStep()}),c("Stratcom").listen("m:history:change",null,function(b){var d=b.getData();if(d.path){d=new(c("URI"))(d.path);a._initialURI.getPath()===d.getPath()&&(b.prevent(),b.getData().soft===m?a._onEnteringCP():a._onBackToCP())}}),c("Stratcom").listen("click","m_login_next_btn",this._handleNextButtonClick.bind(this)));c("DOM").listen(this.oneTimePasswordLink,"click",null,this._onSendOneTimePassword.bind(this));c("Stratcom").listen("m:page:unload",null,function(){c("Stratcom").removeCurrentListener(),b.release()})},initRegButton:function(a){var b=a.root;this.regURI=a.regURI;this.regButton=c("DOM").find(b,"a","m_reg_button");this.regButton.removeAttribute("href");c("DOM").listen(this.regButton,"click",null,this._sendLoginRequestFromReg.bind(this))},initCloseButton:function(a){c("DOM").listen(a,"click",null,function(){window.close()})},initSendNotificationButton:function(a){var b=this;c("DOM").listen(a,"click",null,function(){b._sendLoginNotification()})},initEnterPasswordButton:function(a){var b=this;c("DOM").listen(a,"click",null,function(){b.inPasswordScreen=!0,b._hideAll(b.notificationScreenElements),b._showAll(b.passwordScreenElements),b.loginNotificationRequested&&(d("CSS").hide(b.passwordScreenNotificationButton),d("CSS").hide(b.orLine),d("CSS").show(b.passwordScreenNotificationSentMessage))})},initLoginForm:function(a){var b=this;this.root=a.root;this.isTwoStepsLogin=a.isTwoStepsLogin;this.isActionLoggingEnabled=a.isActionLoggingEnabled;this.isCredsManagerEnabled=a.isCredsManagerEnabled;this.isCredsSavingEnabled=a.isCredsSavingEnabled;this.isPasswordlessEnabled=a.isPasswordlessEnabled;this.doNotShowUserHeader=a.doNotShowUserHeader;this.shouldWaitForPasswordSave=a.shouldWaitForPasswordSave;this.shouldPrefillJioHeaderForRegFromLogin=a.shouldPrefillJioHeaderForRegFromLogin;this.jioPrefilled=!1;this.igCPLogin=!1;this.shouldProcessUserConsentForTokenHeader=a.shouldProcessUserConsentForTokenHeader;this.shouldProcessUserConsentForHeader=a.shouldProcessUserConsentForHeader;this.onErrorRegURI=a.onErrorRegURI;this.loginForm=c("DOM").find(this.root,"form","m_login_form");this.loginForm.action=a.ajaxURI;this.loginButton=c("DOM").find(this.root,"button","m_login_button");var e=c("DOM").scry(this.root,"a","m_login_button_alt");e=e.pop();this.loginButtonAlt=null;this.isSmartLock=c("ge")("is_smart_lock");this.clearPrefillMaskOnKeydown=a.clearPrefillMaskOnKeydown;this.pubKeyData=a.pubKeyData;this.shouldUseEmailDomainTypeahead=a.shouldUseEmailDomainTypeahead;this.showTwoStepDuoLikePasswordless=a.showDuoLikePasswordless;this.showSingleStepDuoLikePasswordless=a.showSingleStepDuoLikePasswordless;this.trackingPreference=a.trackingPreference;window.PublicKeyCredential&&window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable?window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable().then(function(a){a?b._authenticatorAvailableStatus="true":b._authenticatorAvailableStatus="false",b._logAction("authenticator_check")})["catch"](function(a){b._authenticatorAvailableStatus="error",b._logAction("authenticator_check")}):(this._authenticatorAvailableStatus="unsupported",this._logAction("authenticator_check"));e!==void 0&&(this.loginButtonAlt=e);(a.shouldShowSmartLockSelector||this._isCredentialsManagerEnabled())&&this._initSmartLockAccountChooser();this.isTwoStepsLogin?(this.hadCPPrefilled=!1,this.shouldClearPasswordIfOnlyPasswordPrefilled=!0,this._initialURI=c("URI").getRequestURI(),this.shouldAutoLandOnStep2=a.shouldAutoLandOnStep2,this.prefillContactpointHiddenInput=c("ge")("prefill_contact_point"),this.emailComponentsWrapper=c("ge")("email_components_wrapper"),this.passwordComponentWrapper=c("ge")("password_component_wrapper"),this.stepTwoCPDisplayContainer=c("ge")("step_two_cp_display"),this.nextButton=c("DOM").find(this.root,"button","m_login_next_btn"),this.cpText=c("DOM").scry(this.root,"span","m_login_cp_text").pop(),this.cpDisplayLabel=c("DOM").scry(this.root,"span","contactpoint_display_label").pop(),this.passwordFieldLabel=c("DOM").scry(this.root,"span","password_field_label").pop(),this.profilePicElem=c("DOM").scry(this.root,"*","profile_pic_elem").pop(),this.oneTimePasswordLink=c("DOM").scry(this.root,"a","one_time_password_link").pop(),this.otpSentMsg=c("DOM").scry(this.root,"div","otp_retrieve_desc").pop(),this.userName=c("DOM").scry(this.root,"span","m_recognized_user_name").pop(),this.moreLinks=c("DOM").scry(this.root,"div","m_login_more_links").pop(),this.progressBar=c("DOM").find(this.root.ownerDocument,"div","login_identify_step_progress_bar"),this.languageSelector=c("DOM").find(this.root.ownerDocument,"div","language_selector"),this.identifyStepElements=c("DOM").scry(this.root.ownerDocument,"div","login_identify_step_element"),this.identifyStepElements.push(this.languageSelector),this.passwordStepElements=c("DOM").scry(this.root.ownerDocument,"div","login_password_step_element"),this._initTwoStepsListeners(),d("DataStore").set(this.loginForm,"step",k)):this.prefillContactpointHiddenInput=c("ge")("m_login_email");this.loginButton.type="button";this.notice=c("DOM").find(this.root,"div","m_login_notice");this.emailInput=this._getElem("input","m_login_email");this.passwordInput=this._getElem("div","m_login_password");this.emailInputContainer=c("ge")("email_input_container");this.passwordInputDOM=c("DOM").find(this.passwordInput,"input");this.showTwoStepDuoLikePasswordless&&(this.sendLoginNotificationButton=c("DOM").find(this.root,"button","login_notification_button"),this.notificationSentMessage=c("DOM").find(this.root,"div","notification_sent_message"),this.sendNotificationSpinner=c("DOM").find(this.root,"div","send_notification_spinner"));this.showSingleStepDuoLikePasswordless&&(this.notificationScreenElements=c("DOM").scry(this.root.ownerDocument,"div","notification_screen_element"),this._initSendNotificationComponentsForPasswordless(),this.passwordScreenElements=c("DOM").scry(this.root.ownerDocument,"div","password_screen_element"),this.passwordScreenNotificationButton=c("DOM").find(this.root,"button","password_screen_notification_button"),this.passwordScreenNotificationSentMessage=c("DOM").find(this.root,"div","password_screen_notification_sent_message"),this.orLine=c("DOM").find(this.root,"div","single_screen_or_line"));this.tryNumber=this._getElem("input","m_login_try_number");this.unrecognizedTriesCountInput=this._getElem("input","m_login_unrecognized_tries");this.upsell=this._getElem("div","m_login_upsell");this.numPasswordErrorAttempts=0;this.unrecognizedContactpointErrorCount=0;this.originalPasswordPlaceholder=this.passwordInputDOM.getAttribute("placeholder");this.userInfoContainer=c("ge")("user_info_container");this.oauthLoginButtonContainer=c("ge")("oauth_login_button_container");this.oauthLoginDescContainer=c("ge")("oauth_login_desc_container");this.passwordLabelContainer=c("ge")("pwd_label_container");this.otpButtonContainer=c("ge")("otp_button_elem_container");this.otpRetrieveDescContainer=c("ge")("otp_retrieve_desc_container");this.userInfoAfterFailureElements=c("DOM").scry(this.root.ownerDocument,"div","user_info_after_failure_element");this.otpPasswordPlaceholder=i._("\u5bc6\u7801\u6216\u4e00\u6b21\u6027\u5bc6\u7801");this.shouldPrefillJioHeaderForRegFromLogin&&d("MMsisdnJioApiModule").fetchMsisdnAndCache();this.shouldUseEmailDomainTypeahead&&this._initTypeahead();c("DOM").listen(this.loginButton,"click",null,this._handleSubmitButtonClick.bind(this));this.loginButtonAlt!==null&&c("DOM").listen(this.loginButtonAlt,"click",null,this._handleSubmitButtonClick.bind(this));this.hiddenInputPrefillSource=c("ge")("prefill_source");this.hiddenInputPrefillSource!==null&&this.hiddenInputPrefillSource.value==="header"&&this.clearPrefillMaskOnKeydown===!0&&c("DOM").listen(this.emailInput,"keydown",null,function(a){b.clearPrefillMaskOnKeydown===!0&&(b.clearPrefillMaskOnKeydown=!1,b.emailInput.value="",new(c("IorgMaskHeaderPrefillTypedLogger"))().setEvent("clear_masked_prefill_login").log())});this.showSingleStepDuoLikePasswordless&&(c("DOM").listen(this.emailInput,"keyup",null,function(){b._toggleNotificationButtonState()}),this._passwordlessPollingTimer=window.setInterval(this._toggleNotificationButtonState.bind(this),200));e=a.listenKeyDown?"keydown":"keyup";this.isTwoStepsLogin?(this.nextButton.setAttribute("type","button"),this.passwordInputDOM.setAttribute("tabindex",-1),c("DOM").listen(this.loginForm,"keydown",null,function(a){a.getRawEvent().keyCode===c("Keys").RETURN?d("DataStore").get(b.loginForm,"step","")===k?b._handleNextButtonClick():b._handleSubmitButtonClick():a.getRawEvent().keyCode===c("Keys").TAB&&d("DataStore").get(b.loginForm,"step","")===k&&b._handleNextButtonClick()})):c("DOM").listen(this.loginForm,e,null,function(a){a.getRawEvent().keyCode===c("Keys").RETURN&&b._handleSubmitButtonClick()});a.clearOnDelete&&c("DOM").listen(this.emailInput,e,null,function(a){a.getRawEvent().keyCode===c("Keys").BACKSPACE&&(b.emailInput.value="")});if(this.isActionLoggingEnabled){c("Stratcom").listen("change","m_login_email",function(a){b._logAction("changed_user_identifier"),b.shouldDisplayOTPMsg=!1,b.displayOTPLink=!1,b._rerenderAuthStepCPDisplay()});c("Stratcom").listen("change","m_login_password",function(a){b._logAction("changed_password")});c("Stratcom").listen("click","m_reg_button",function(a){c("gkx")("3820"),d("MGetInstalledRelatedApps").logmTouchInstalledRelatedAppsOnRegStart(),b.emailInput.value===""?b._logAction("clicked_reg_identifier_blank"):b._logAction("clicked_reg_identifier_filled"),b.passwordInputDOM.value===""?b._logAction("clicked_reg_password_blank"):b._logAction("clicked_reg_password_filled")});c("Stratcom").listen("click","language_selector",function(a){b.clickedLanguageSelector||(b._logAction("clicked_language_selector"),b.clickedLanguageSelector=!0)});d("Event").listen(this.root,"click",function(){b._logAction("clicked_on_login_screen")});this.hasFocusedAlready=!1;this.loginNotificationRequested=!1;this.loginNotificationRequestProcessed=!1;d("Run").onAfterLoad(function(){b._logAction("form_load_client")});e=c("DOM").scry(this.root,"input",null);e.forEach(function(a){d("Event").listen(a,"focus",b._processFormFocus.bind(b))})}this.shouldAutoLandOnStep2&&this.emailInput.value.trim()!==""&&(this.hadCPPrefilled=!0,c("MHistory").pushSoftState(m));c("Stratcom").listen("click",["change_language"],function(d){new(c("LoggedOutLocaleEventsClientLogger"))(b.root,a.datrCookie).logLocaleEvent(d,"login","login")});c("Stratcom").listen("click",["more_language"],function(d){new(c("LoggedOutLocaleEventsClientLogger"))(b.root,a.datrCookie).logMoreLanguage("login","login")});if(!c("killswitch")("PLATFORM_LOGIN_ASYNC_WVDP_REQUEST")){this.usePasswordlessLoginForWvdp=!1;e=this._collectClientSignals();var f=c("ge")("bi_xrwh"),g=new(c("URI"))(window.location.href);g=new(c("URI"))(g.getQueryData().next);e!==null&&f!==null&&p.test(g.getPath())&&new(c("XAsyncRequest"))().setMethod("POST").setData({bi_wvdp:e.value,bi_xrwh:f.value,request_uri:window.location.href}).setURI(c("XLoginWebviewDetectionAsyncController").getURIBuilder().getURI()).setHandler(function(a){a=a.payload;a&&a.eligible_for_passwordless&&(b.usePasswordlessLoginForWvdp=!0,b.notificationScreenDescriptionText=c("DOM").find(b.root,"div","notification_screen_description_text"),b._initSendNotificationComponentsForPasswordless());if(a&&a.error_code){a=c("XOAuthErrorController").getURIBuilder().setEnum("error_code",a.error_code).setEnum("display","touch").getURI();c("URI").goURIOnWindow(a,void 0,!0)}}).send()}d("SpatialNavigation").registerNavigationHandlerForPortalTV()},initAccountRecoveryFunnelLogging:function(a,b,c){},_initTypeahead:function(){try{var a=this.emailInputContainer;d("MLoginEmailDomainTypeahead").init(a)}catch(a){}},_initSmartLockAccountChooser:function(a){var b=this;a===void 0&&(a="silent");window.PasswordCredential&&(this._logAction("smart_lock_supported"),navigator.credentials!==null&&(this._logAction("smart_lock_showed"),navigator.credentials.get({password:!0,mediation:a}).then(function(c){c!==null&&c.type==="password"&&c.password!==null&&c.id!==null?(b.emailInput.setAttribute("value",c.id),b.passwordInputDOM.setAttribute("value",c.password),b._logAction("smart_lock_selected"),b.isSmartLock.setAttribute("value",!0),a==="required"&&b._sendLoginRequest()):(b.passwordInputDOM.setAttribute("value",""),b._logAction("smart_lock_canceled"),a==="silent"&&b._initSmartLockAccountChooser("required"))})["catch"](function(a){c("FBLogger")("login").catching(a).warn("smart lock promise fail")})))},_processFormFocus:function(a){if(a.target===this.passwordInputDOM&&this.isPasswordlessEnabled&&!this.showTwoStepDuoLikePasswordless&&!this.showSingleStepDuoLikePasswordless){if(this.loginNotificationRequested)return;this.loginNotificationRequested=!0;this._sendLoginNotification()}this._logFormFocus()},_initSendNotificationComponentsForPasswordless:function(){this.notificationScreenLoginButton=c("DOM").find(this.root,"button","notification_screen_login_button"),this.sendNotificationSpinner=c("DOM").find(this.root,"div","send_notification_spinner"),this.notificationScreenNotificationSentMessage=c("DOM").find(this.root,"div","notification_screen_notification_sent_message")},_singleScreenDuoLikePasswordlessSendNotificationHandler:function(a){d("CSS").hide(this.sendNotificationSpinner),this.loginNotificationRequestProcessed=!0,a instanceof Object&&a.nonce!==""&&a.nonce!==void 0?(this.inPasswordScreen?(d("CSS").hide(this.passwordScreenNotificationButton),d("CSS").show(this.passwordScreenNotificationSentMessage),d("CSS").hide(this.orLine)):(d("CSS").hide(this.notificationScreenLoginButton),this.notificationScreenDescriptionText&&d("CSS").hide(this.notificationScreenDescriptionText),d("CSS").show(this.notificationScreenNotificationSentMessage)),this._passwordlessPollingData.nonce=a.nonce,this._passwordlessPollingTimer=window.setInterval(this._pollPasswordlessNonce.bind(this),2e3)):(c("DOM").setContent(this.notice,this._getBlankEmailErrorFBT()),d("CSS").show(this.notice),d("MLoginFormError").set(this.emailInput,"error"),this.loginNotificationRequested=!1)},_twoScreenDuoLikePasswordlessSendNotificationHandler:function(a){d("CSS").hide(this.sendNotificationSpinner),this.loginNotificationRequestProcessed=!0,a instanceof Object&&a.nonce!==""&&a.nonce!==void 0?(d("CSS").hide(this.sendLoginNotificationButton),d("CSS").show(this.notificationSentMessage),this._passwordlessPollingData.nonce=a.nonce,this._passwordlessPollingTimer=window.setInterval(this._pollPasswordlessNonce.bind(this),2e3)):(c("DOM").setContent(this.notice,this._getBlankEmailErrorFBT()),d("CSS").show(this.notice),d("MLoginFormError").set(this.emailInput,"error"),d("DataStore").set(this.emailInput,"error",!0),c("MHistory").popSoftState(m),this.nextButton.disabled=!1,this.loginNotificationRequested=!1)},_sendNotificationHandler:function(a){a instanceof Object&&a.nonce!==""&&a.nonce!==void 0&&(this._passwordlessPollingData.nonce=a.nonce,this._passwordlessPollingTimer=window.setInterval(this._pollPasswordlessNonce.bind(this),2e3))},_sendLoginNotification:function(){var a=this,b=this.emailInput.value,e=new(c("URI"))(window.location.href),f=e.getQueryData().app_id;if(f==null)return;e=new(c("URI"))(new(c("URI"))(this.loginForm.action).getQueryData().next);var g=e.getQueryData().logger_id;e=(e=e.getQueryData().scope)!=null?e:"";new(c("XAsyncRequest"))().setMethod("POST").setData({contact_point:b,app_id:f,logger_id:g,tp:this.trackingPreference,scope:e}).setURI(d("ThisControllerNoLongerExists").__DEADURI__("9c2zw6qz")).setHandler(function(b){b=b.payload;a._passwordlessPollingData={app_id:f,logger_id:g,tp:a.trackingPreference};a.showTwoStepDuoLikePasswordless?a._twoScreenDuoLikePasswordlessSendNotificationHandler(b):a.showSingleStepDuoLikePasswordless||a.usePasswordlessLoginForWvdp?a._singleScreenDuoLikePasswordlessSendNotificationHandler(b):a._sendNotificationHandler(b)}).send();(this.showTwoStepDuoLikePasswordless||this.showSingleStepDuoLikePasswordless||this.usePasswordlessLoginForWvdp)&&d("CSS").show(this.sendNotificationSpinner)},_pollPasswordlessNonce:function(){var a=this;new(c("XAsyncRequest"))().setMethod("POST").setURI(d("ThisControllerNoLongerExists").__DEADURI__("dlyihgxgj")).setData(this._passwordlessPollingData).setHandler(function(b){b=b.payload;if(b.result==="success"){window.clearInterval(a._passwordlessPollingTimer);var d=new(c("URI"))(new(c("URI"))(a.loginForm.action).getQueryData().next);d.addQueryData("l_nonce",a._passwordlessPollingData.nonce);c("goURI")(d.toString())}else b.result==="denied"&&window.clearInterval(a._passwordlessPollingTimer)}).send()},_logFormFocus:function(){if(this.hasFocusedAlready)return;this.hasFocusedAlready=!0;this._logAction("form_focus")},_getElem:function(a,b){a=c("DOM").scry(this.root,a,b);return a.length===0?null:a[0]},_animateScrollToElem:function(a){if(!a)return;var b=c("MViewport").getScrollPos().y,d=a.offsetTop;a=function(a){c("MViewport").scrollTo(0,b+a*(d-b-o))};new(c("MAnimator"))().start(a,null,function(){},n)},_autoFocusIfNeeded:function(a){if(!this.emailInput||!this.passwordInput)return;if(!this.passwordInputDOM)return;var b=this.emailInput.value===""||a.m_login_email.error;a=this.passwordInputDOM.value===""||a.m_login_password.error;(b||a)&&(b?this.emailInput.focus():a&&this.passwordInputDOM.focus())},_sendLoginRequest:function(){var a=this;c("DeferredCookie").flushAllCookiesWithoutRecordingConsentDONOTCALLBEFORECONSENT();d("CSS").hide(this.notice);d("MButton").setEnabled(this.loginButton,!1);this.isTwoStepsLogin&&d("MButton").setEnabled(this.nextButton,!1);window.setTimeout(d("MButton").setEnabled.bind(null,this.loginButton,!0),15e3);this.loginButtonAlt!==null&&(d("MButton").setEnabled(this.loginButtonAlt,!1),window.setTimeout(d("MButton").setEnabled.bind(null,this.loginButtonAlt,!0),15e3));this.tryNumber!==null&&this.tryNumber.setAttribute("value",this.numPasswordErrorAttempts);this.unrecognizedTriesCountInput!==null&&(this.unrecognizedTriesCountInput.value=this.unrecognizedContactpointErrorCount);var e=new(c("URI"))(this.loginForm.action).addQueryData("ig_cp_login",this.igCpLogin),f="#PWD_BROWSER",g=5,h=Math.floor(Date.now()/1e3).toString(),i=this._collectClientSignals();i!==null&&this.loginForm.appendChild(i);try{if((window.crypto||window.msCrypto)&&this.pubKeyData){i=b("FBBrowserPasswordEncryption");i=i.encryptPassword;var j=b("promiseDone");j(i(this.pubKeyData.keyId,this.pubKeyData.publicKey,this.passwordInputDOM.value,h),function(b){b=c("DOM").create("input",{type:"hidden",name:"encpass",value:b});a.loginForm.appendChild(b);a.passwordInputDOM.removeAttribute("name");a.loginForm.action=e.toString();d("MAjaxify").form(null,a.loginForm,null,"nocache",null,!0,[{response:a._onLoginResponse.bind(a)}])},function(c){var i=b("LoginServicePasswordEncryptDecryptEventTypedLogger");new i().setError("BrowserEncryptionFailure").setErrorMessage(c.message).setPasswordTag(f).setPasswordEncryptionVersion(g).setPasswordTimestamp(h).logVital();a.loginForm.action=e.toString();d("MAjaxify").form(null,a.loginForm,null,"nocache",null,!0,[{response:a._onLoginResponse.bind(a)}])});return}}catch(a){j=b("LoginServicePasswordEncryptDecryptEventTypedLogger");new j().setError("BrowserEncryptionClientFailure").setErrorMessage(a.message).setPasswordTag(f).setPasswordEncryptionVersion(g).setPasswordTimestamp(h).logVital()}if(!(window.crypto||window.msCrypto)&&this.pubKeyData){i=b("LoginServicePasswordEncryptDecryptEventTypedLogger");new i().setError("BrowserEncryptionFailure").setErrorMessage("Crypto object is undefined").setPasswordTag(f).setPasswordEncryptionVersion(g).setPasswordTimestamp(h).logVital()}this.loginForm.action=e.toString();d("MAjaxify").form(null,this.loginForm,null,"nocache",null,!0,[{response:this._onLoginResponse.bind(this)}])},_sendLoginRequestFromReg:function(){c("DeferredCookie").flushAllCookiesWithoutRecordingConsentDONOTCALLBEFORECONSENT();this.shouldProcessUserConsentForTokenHeader&&this._processUserConsentForTokenHeader();this.shouldProcessUserConsentForHeader&&this._processUserConsentForHeader("reg");if(this.emailInput.value===""||this.passwordInput.value===""){c("goURI")(this.regURI);return}d("CSS").hide(this.notice);d("MButton").setEnabled(this.loginButton,!1);window.setTimeout(d("MButton").setEnabled.bind(null,this.loginButton,!0),15e3);this.loginButtonAlt!==null&&(d("MButton").setEnabled(this.loginButtonAlt,!1),window.setTimeout(d("MButton").setEnabled.bind(null,this.loginButtonAlt,!0),15e3));d("MAjaxify").form(null,this.loginForm,null,"nocache",null,!0,[{response:this._onLoginFromRegResponse.bind(this)}])},_updatePagePrefillState:function(){var a=c("ge")("first_prefill_type"),b=null;a!=null&&a.value!==""&&(b=a.value);a=c("ge")("first_prefill_source");var d=null;a!=null&&a.value!==""&&(d=a.value);a=c("ge")("prefill_type");var e=null;a!=null&&a.value!==""&&(e=a.value);a=c("ge")("prefill_source");var f=null;a!=null&&a.value!==""&&(f=a.value);a=c("ge")("had_cp_prefilled");var g=null;a!=null&&a.value!==""&&(g=a.value);a=c("ge")("had_password_prefilled");var h=null;a!=null&&a.value!==""&&(h=a.value);this.pagePrefillState={first_prefill_type:b,first_prefill_source:d,prefill_type:e,prefill_source:f,had_cp_prefilled:g,had_password_prefilled:h}},_logAction:function(a){if(!this.isActionLoggingEnabled)return;this._updatePagePrefillState()},_handleSubmitButtonClick:function(){this._logAction("clicked_submit"),this.emailInput!==null&&this.emailInput.value===""?this._logAction("clicked_submit_identifier_blank"):this._logAction("clicked_submit_identifier_filled"),this.passwordInputDOM.value===""?this._logAction("clicked_submit_password_blank"):this._logAction("clicked_submit_password_filled"),this.shouldProcessUserConsentForTokenHeader&&this._processUserConsentForTokenHeader(),this.shouldProcessUserConsentForHeader&&this._processUserConsentForHeader("ln"),this._sendLoginRequest()},_processUserConsentForTokenHeader:function(){var a=c("XZeroTokenHeaderUserConsentProcessController").getURIBuilder().getURI();new(c("MRequest"))(a).setMethod("POST").send()},_processUserConsentForHeader:function(a){a=c("XZeroHeaderUserConsentProcessController").getURIBuilder().setEnum("ce",a).getURI();new(c("MRequest"))(a).setMethod("POST").send()},_showBlankPWError:function(a){c("DOM").setContent(this.notice,this._getBlankPasswordErrorFBT(a)),d("CSS").show(this.notice)},_onSendOneTimePassword:function(a){var b=this;a.prevent();d("MAjaxify").ajaxify(null,null,new(c("MRequest"))(this.otp_uri).setMethod("POST"),null,[{response:function(a){a=a.payload;if(a.msg===null)return;c("DOM").setContent(b.otpSentMsg,a.msg);b.shouldDisplayOTPMsg=!0;b._rerenderAuthStepCPDisplay()}}])},_populateProfileInfo:function(a){var b=this;if(this.doNotShowUserHeader){a();return}this._updatePagePrefillState();var e=c("XLoginProfileInfoFetcherController").getURIBuilder().setString("contactpoint",this.emailInput.value).setBool("pwdprefill","true"===this.pagePrefillState.had_password_prefilled).getURI();d("MAjaxify").ajaxify(null,null,new(c("MRequest"))(e).setMethod("GET"),null,[{response:function(d){b.nextButton.disabled=!1;if(!d.payload){a();return}d=d.payload;if(d.profile_pic_elem){var e=b.profilePicElem.parentNode;c("DOM").replace(b.profilePicElem,d.profile_pic_elem);b.profilePicElem=c("DOM").scry(e,"*","profile_pic_elem").pop();b.displayProfilePic=!0}d.user_name&&b.userName&&(c("DOM").setContent(b.userName,d.user_name||b.emailInput.value),b.displayUserName=!0);d.otp_uri&&b.oneTimePasswordLink&&(b.otp_uri=d.otp_uri,b.displayOTPLink=!0);a()}}])},_getBlankEmailErrorFBT:function(){var a=i._("\u6ce8\u518c\u5e10\u6237\u3002"),b=c("DOM").create("a");b.setAttribute("href",this.onErrorRegURI);c("DOM").setContent(b,a);return i._("\u627e\u4e0d\u5230\u4e0e\u8f93\u5165\u7684\u90ae\u7bb1\u6216\u624b\u673a\u53f7\u5bf9\u5e94\u7684\u5e10\u6237\u3002{reg_uri}",[i._param("reg_uri",b)])},_getBlankPasswordErrorFBT:function(a){var b=i._("\u662f\u4e0d\u662f\u5fd8\u8bb0\u5bc6\u7801\u4e86\uff1f"),d=c("DOM").create("a");d.setAttribute("href",a.forgot_password_uri);c("DOM").setContent(d,b);return i._("\u8bf7\u8f93\u5165\u5bc6\u7801\u3002{recovery_uri}",[i._param("recovery_uri",d)])},_onEnteringCP:function(){if(this.emailInput.value.trim()===""){c("DOM").setContent(this.notice,this._getBlankEmailErrorFBT());d("CSS").show(this.notice);d("MLoginFormError").set(this.emailInput,"error");d("DataStore").set(this.emailInput,"error",!0);c("MHistory").popSoftState(m);return}d("MLoginFormError").unset(this.emailInput);d("DataStore").set(this.emailInput,"error",!1);d("DataStore").get(this.passwordInput,"error",!1)||d("CSS").hide(this.notice);this.nextButton.disabled=!0;this._enableProgressBar();this._populateProfileInfo(this._goToPasswordStep.bind(this));return},_onBackToCP:function(){d("DataStore").get(this.passwordInput,"error",!1)&&(d("CSS").hide(this.notice),d("DataStore").set(this.passwordInput,"error",!1),d("CSS").removeClass(this.passwordInput,j)),this._goToIdentificationStep()},_hideAll:function(a){a.forEach(function(a){return d("CSS").hide(a)})},_showAll:function(a){a.forEach(function(a){return d("CSS").show(a)})},_rerenderAuthStepCPDisplay:function(){this.displayUserName&&(this.cpText&&d("CSS").hide(this.cpText),this.cpDisplayLabel&&d("CSS").hide(this.cpDisplayLabel),this.userName&&d("CSS").show(this.userName));this.profilePicElem&&d("CSS").conditionShow(this.profilePicElem,!!this.displayProfilePic);this.oneTimePasswordLink&&d("CSS").conditionShow(this.oneTimePasswordLink,!!this.displayOTPLink);this.otpSentMsg&&(d("CSS").conditionShow(this.otpSentMsg,!!this.shouldDisplayOTPMsg),this.shouldDisplayOTPMsg?(this.passwordInputDOM.setAttribute("placeholder",this.otpPasswordPlaceholder),this._animateScrollToElem(this.passwordFieldLabel)):this.passwordInputDOM.setAttribute("placeholder",this.originalPasswordPlaceholder));if(!this.stepTwoCPDisplayContainer||!this.emailComponentsWrapper)return;var a=this.displayUserName||this.displayProfilePic;d("CSS").conditionShow(this.stepTwoCPDisplayContainer,!!a);d("CSS").conditionShow(this.emailComponentsWrapper,!a)},_showEditableCPFieldOnAuthStep:function(){this.displayProfilePic=!1,this.displayUserName=!1,this.displayOTPLink=!1,this._rerenderAuthStepCPDisplay(),this.emailInput.focus()},_goToPasswordStep:function(){d("DataStore").set(this.loginForm,"step",l);this.prefillContactpointHiddenInput.value!==this.emailInput.value&&this.passwordInputDOM.setAttribute("value","");if(this.shouldClearPasswordIfOnlyPasswordPrefilled){var a=c("ge")("had_cp_prefilled"),b=c("ge")("had_password_prefilled");a!=null&&b!=null&&(a.value==null||a.value==="false")&&b.value==="true"&&(this.shouldClearPasswordIfOnlyPasswordPrefilled=!1)}this._hideAll(this.identifyStepElements);this._showAll(this.passwordStepElements);this._rerenderAuthStepCPDisplay();this.moreLinks&&d("CSS").addClass(this.moreLinks,"_2pie");d("CSS").removeClass(this.passwordInput,"_1-z5");d("CSS").removeClass(this.passwordComponentWrapper,"_1-z5");this.passwordInputDOM.removeAttribute("tabindex");this.showTwoStepDuoLikePasswordless&&(this.sendLoginNotificationButton.style.display="block");this.loginNotificationRequested?(d("CSS").hide(this.sendLoginNotificationButton),this.loginNotificationRequestProcessed&&d("CSS").hide(this.sendNotificationSpinner)):(d("CSS").hide(this.notificationSentMessage),d("CSS").hide(this.sendNotificationSpinner));this.cpText&&c("DOM").setContent(this.cpText,this.emailInput.value);this.passwordInputDOM.focus()},_resetProgressBar:function(){this.progressBar.style.transitionDuration="0ms",this.progressBar.style["-webkit-transition-duration"]="0ms",this.progressBar.style["-moz-transition-duration"]="0ms",this.progressBar.style.transform="translateX(0%)",this.progressBar.style["-webkit-transform"]="translateX(0%)",this.progressBar.style["-moz-transform"]="translateX(0%)"},_enableProgressBar:function(){this.progressBar.style.transitionDuration="1000ms",this.progressBar.style["-webkit-transition-duration"]="1000ms",this.progressBar.style["-moz-transition-duration"]="1000ms",this.progressBar.style.transform="translateX(100%)",this.progressBar.style["-webkit-transform"]="translateX(100%)",this.progressBar.style["-moz-transform"]="translateX(100%)"},_goToIdentificationStep:function(){d("DataStore").set(this.loginForm,"step",k),this._resetProgressBar(),this.displayUserName=!1,this.displayProfilePic=!1,this.displayOTPLink=!1,this.cpText&&d("CSS").show(this.cpText),this.cpDisplayLabel&&d("CSS").show(this.cpDisplayLabel),this.userName&&(c("DOM").setContent(this.userName,""),d("CSS").hide(this.userName)),d("CSS").addClass(this.passwordInput,"_1-z5"),d("CSS").addClass(this.passwordComponentWrapper,"_1-z5"),this.passwordInputDOM.setAttribute("tabindex",-1),this._hideAll(this.passwordStepElements),this._showAll(this.identifyStepElements),d("DataStore").get(this.emailInput,"error",!1)&&c("DOM").hide(this.upsell),this.moreLinks&&d("CSS").removeClass(this.moreLinks,"_2pie"),this.emailInput.focus(),this.nextButton.disabled=!1},_handleNextButtonClick:function(){this.passwordInputDOM.value.trim()!==""&&this.prefillContactpointHiddenInput&&this.prefillContactpointHiddenInput.value===this.emailInput.value&&(this.hadCPPrefilled=!0),c("MHistory").pushSoftState(m),this.showTwoStepDuoLikePasswordless&&this._logDuoLikePasswordlessLoginScreenImpression()},_mayBeShowTwoStepsLoginAfterPwdFailure:function(a){var b=this;a=a.two_steps_after_failuer_payload;if(!a)return;var e=a.profileInfo,f=a.passwordFieldLabelElem;if(!e||!f)return;this.userInfoContainer&&c("DOM").setContent(this.userInfoContainer,e);this.passwordLabelContainer&&c("DOM").setContent(this.passwordLabelContainer,f);this._showAll(this.userInfoAfterFailureElements);d("CSS").hide(this.emailInput);c("Stratcom").listen("click","m_login_change_cp_link",function(a){a.prevent(),d("CSS").show(b.emailInput),b._hideAll(b.userInfoAfterFailureElements)});e=a.otpButtonElem;f=a.otpURI;a=a.otpRetrieveDescElem;if(!e||!f||!a)return;this.otpButtonContainer&&c("DOM").setContent(this.otpButtonContainer,e);this.otpRetrieveDescContainer&&c("DOM").setContent(this.otpRetrieveDescContainer,a);this.otpSentMsg=c("DOM").find(this.otpRetrieveDescContainer,"div","otp_retrieve_desc");this.otp_uri=f;this.displayOTPLink=!0;this.oneTimePasswordLink=c("DOM").scry(this.otpButtonContainer,"a","one_time_password_link").pop();c("DOM").listen(this.oneTimePasswordLink,"click",null,this._onSendOneTimePassword.bind(this));this._rerenderAuthStepCPDisplay()},_mayBeShowOAuthLoginAfterPwdFailure:function(a){a=a.oauth_login_elem_payload;if(!a)return;var b=a.oAuthButton;a=a.oAuthDesc;if(!b)return;this.oauthLoginButtonContainer&&c("DOM").setContent(this.oauthLoginButtonContainer,b);d("CSS").show(this.oauthLoginButtonContainer);if(!a)return;this.oauthLoginDescContainer&&c("DOM").setContent(this.oauthLoginDescContainer,a);d("CSS").show(this.oauthLoginDescContainer)},_mayBeShowFamilyAuthLoginAfterPwdFailure:function(a){var b=this;this.igCpLogin=!0;a=a.familyAuthDesc;this.languageSelector=c("DOM").find(this.root.ownerDocument,"div","language_selector");this.footerElem=c("DOM").find(this.root.ownerDocument,"div","m_login_footer");this.root&&this.root.parentNode&&this.root.parentNode.parentNode&&(c("DOM").setContent(this.root,a),d("CSS").hide(this.languageSelector),d("CSS").hide(this.footerElem));var e=c("DOM").find(this.root,"button","family_auth_login_button");if(!a)return;d("Event").listen(e,"click",function(a){a.preventDefault(),b._sendLoginRequest()})},_isCredentialsManagerEnabled:function(){return Boolean(this.isCredsManagerEnabled&&this._isCredentialsSavingEnabled())},_isCredentialsSavingEnabled:function(){return Boolean(this.isCredsSavingEnabled&&window.PasswordCredential&&navigator.credentials&&navigator.credentials.get&&navigator.credentials.store)},_onLoginResponse:function(a){this.passwordInputDOM.setAttribute("name","pass");d("MButton").setEnabled(this.loginButton,!0);this.isTwoStepsLogin&&d("MButton").setEnabled(this.nextButton,!0);this.loginButtonAlt!==null&&d("MButton").setEnabled(this.loginButtonAlt,!0);if(!a.payload)return;var b=a.payload;this.isSmartLock.setAttribute("value",!1);b.m_login_password.error?this.numPasswordErrorAttempts++:this.numPasswordErrorAttempts=0;b.m_login_email.unrecognized&&this.unrecognizedContactpointErrorCount++;b.m_login_notice&&(c("DOM").setContent(this.notice,b.m_login_notice),b.m_login_password.error&&this.passwordInputDOM.value.trim()===""&&this._showBlankPWError(a.payload),d("CSS").show(this.notice));if(this.emailInput!==null){b.m_login_email.error?(d("MLoginFormError").set(this.emailInput,"error"),d("DataStore").set(this.emailInput,"error",!0),this.isTwoStepsLogin&&(this.shouldDisplayOTPMsg=!1,this._rerenderAuthStepCPDisplay())):(d("MLoginFormError").unset(this.emailInput),d("DataStore").set(this.emailInput,"error",!1));var e=b.m_login_email.value;e?(this.emailInput.value=e,this.isTwoStepsLogin&&this.cpText&&c("DOM").setContent(this.cpText,e)):this.emailInput.value=""}if(this.passwordInput!==null){e=b.family_auth_login_elem_payload;e===null&&(c("DOM").find(this.passwordInput,"input").value="");c("Stratcom").invoke("m:passwordinput:autoclear","password-plain-text-toggle-input");b.m_login_password.error?(d("DataStore").set(this.passwordInput,"error",!0),d("CSS").addClass(this.passwordInput,j),this.isTwoStepsLogin&&d("DataStore").get(this.loginForm,"step","")===k&&c("MHistory").pushSoftState(m),this.isTwoStepsLogin&&this._populateProfileInfo(this._rerenderAuthStepCPDisplay.bind(this)),this._mayBeShowTwoStepsLoginAfterPwdFailure(b),this._mayBeShowOAuthLoginAfterPwdFailure(b)):(d("DataStore").set(this.passwordInput,"error",!1),d("CSS").removeClass(this.passwordInput,j));e!==null&&this._mayBeShowFamilyAuthLoginAfterPwdFailure(e)}this._autoFocusIfNeeded(a.payload);this.upsell!==null&&c("DOM").hide(this.upsell);c("Stratcom").invoke("m:kaios:spatialnav:refresh",null,null)},_onLoginFromRegResponse:function(a){c("goURI")(this.regURI)},redirect:function(a){var b=this;if(!this._isCredentialsManagerEnabled())window.location.replace(a);else{var d=c("bx").getURL(c("bx")("875231"));d=new window.PasswordCredential({id:this.emailInput.value,password:this.passwordInputDOM.value,iconURL:d});try{var e=!1,f=0;navigator.credentials&&navigator.credentials.store(d).then(function(){b._logAction("creds_manager_finished"),e=!0})["catch"](function(a){b._logAction("creds_manager_error"),c("FBLogger")("creds_manager").catching(a).warn("creds_manager_error"),e=!0});if(this.shouldWaitForPasswordSave)var g=window.setInterval(function(){f>5?(window.clearInterval(g),e||b._logAction("creds_manager_incomplete"),window.location.replace(a)):f++},500);else window.location.replace(a)}catch(b){this._logAction("creds_manager_exception"),c("FBLogger")("creds_manager").catching(b).warn("creds_manager_exception"),window.location.replace(a)}}},_logDuoLikePasswordlessLoginScreenImpression:function(){var a=new(c("URI"))(new(c("URI"))(this.loginForm.action).getQueryData().next),b=a.getQueryData().logger_id;a=a.getQueryData().cbt;d("PlatformOAuthDialogLoginFunnelLogger").log("client_duo_like_passwordless_login_screen_impression",a,b,!0,null,!0)},_toggleNotificationButtonState:function(){this.emailInput.value.length>0?(this.notificationScreenLoginButton.disabled=!1,this.passwordScreenNotificationButton.disabled=!1):(this.notificationScreenLoginButton.disabled=!0,this.passwordScreenNotificationButton.disabled=!0)},_collectClientSignals:function(){try{return this._collectClientSignalsImpl()}catch(a){c("FBLogger")("login").catching(a).warn("error during signal collection");return null}},_collectClientSignalsImpl:function(){var a=document.getElementById("scetoggle");if(a===null)return null;a="bi_wvdp";var b=document.getElementById(a);b!==null&&b.remove();b=document.createElement("iframe");b.style.display="none";b.srcdoc="";c("MViewport").appendNode(b);var d=this._collectClientSignalsFromContentWindow(window);if(Object.getOwnPropertyDescriptors!==void 0){var e=Object.getOwnPropertyDescriptors(HTMLIFrameElement.prototype);d.iframeProto=e.contentWindow.get.toString()}else d.iframeProto="";d.remap=b.contentWindow===window;d.iframeData=this._collectClientSignalsFromContentWindow(b.contentWindow);e=c("DOM").create("input",{type:"hidden",name:a,id:a,value:JSON.stringify(d)});return e},_collectClientSignalsFromContentWindow:function(a){var b={};try{var d=a.navigator,e=a.chrome;b.hwc=e!==void 0;e!==void 0&&(b.hwcr=e.runtime!==void 0);b.has_dnt=d.doNotTrack!==void 0;b.has_standalone=d.standalone!==void 0;b.standalone=d.standalone;b.wnd_toStr_toStr=a.toString.toString();e=d.permissions;b.hasPerm=d.permissions!==void 0;e!==void 0&&(b.permission_query_toString=e.query.toString(),b.permission_query_toString_toString=e.query.toString.toString());try{b.has_seWo=d.serviceWorker!==void 0,b.has_meDe=d.mediaDevices!==void 0,b.has_creds=d.credentials!==void 0,b.has_hwi_bt=a.com_huawei_browser_translate_obj!==void 0,b.has_agjsi=a.AppsgeyserJSInterface!==void 0}catch(a){c("FBLogger")("login").catching(a).warn("Error checking additional props")}}catch(a){c("FBLogger")("login").catching(a).warn("When collecting signals in content window")}return b}};e=a;g["default"]=e}),98);
__d("MLoginView",["cx","CSS","DOM","DeferredCookie","MButton","Stratcom","ge"],(function(a,b,c,d,e,f,g,h){function a(a,b){d("DOM").listen(a,"submit",null,function(){c("DeferredCookie").flushAllCookiesWithoutRecordingConsentDONOTCALLBEFORECONSENT(),d("MButton").setEnabled(b,!1),window.setTimeout(d("MButton").setEnabled.bind(null,b,!0),15e3)})}function b(a){window.location.replace(a)}function e(a,b,e){d("DOM").listen(a,"click",null,function(e){d("DOM").remove(a);c("CSS").removeClass(b,"_7tb");e=d("DOM").scry(c("ge")("root"),"div","m_login_form_title");if(e.length>0){e=e[0];e instanceof HTMLElement&&d("DOM").remove(e)}window.setTimeout(function(){c("Stratcom").invoke("m:kaios:spatialnav:refresh",null,null),i()},150)})}function i(){var a=c("ge")("m_login_email");a=a?d("DOM").find(a,"input"):null;a instanceof HTMLElement&&a.focus()}g.disableOnSubmit=a;g.appSwitch=b;g.showForm=e;g.snapFocusToUsernameTextField=i}),98);
__d("XOauthDialogController",["XController"],(function(a,b,c,d,e,f){e.exports=b("XController").create("/dialog/oauth/",{display:{type:"Enum",enumType:1},auth_type:{type:"String"},logger_id:{type:"String"},redirect_uri:{type:"String"},sso_device:{type:"Enum",enumType:1},sdk:{type:"String"},ref:{type:"String"},ret:{type:"String"},scope:{type:"StringVector"},app_id:{type:"Int"},auth_method:{type:"Enum",enumType:1},facebook_sdk_version:{type:"String"},calling_package_key:{type:"String"},context_uri:{type:"String"},default_audience:{type:"Enum",enumType:1},domain:{type:"String"},encoded_state:{type:"String"},fallback_redirect_uri:{type:"String"},force_confirmation:{type:"Bool",defaultValue:!1},kid_directed_site:{type:"Bool",defaultValue:!1},install_nonce:{type:"String"},legacy_override:{type:"String"},loyalty_program_id:{type:"Int"},shop_id:{type:"Int"},native_login_button:{type:"Bool",defaultValue:!1},original_redirect_uri:{type:"String"},privacyx:{type:"String"},return_format:{type:"EnumVector",enumType:{member:1}},return_scopes:{type:"Bool",defaultValue:!1},scope_objects:{type:"String"},scope_objects_count:{type:"String"},sdk_version:{type:"String"},seen_scopes:{type:"String"},sheet_name:{type:"String"},state:{type:"String"},user_mobile_phone:{type:"String"},android_key:{type:"String"},sso:{type:"String"},sso_key:{type:"String"},nonce:{type:"String"},user_code:{type:"String"},auth_nonce:{type:"String"},fbs:{type:"Int"},fbapp_pres:{type:"Bool",defaultValue:!1},is_comet_compat:{type:"Bool",defaultValue:!1},response_type:{type:"String"},ignore_reentry:{type:"Bool",defaultValue:!1},type:{type:"Enum",enumType:1},l_nonce:{type:"String"},cbt:{type:"Int"},ies:{type:"Bool",defaultValue:!1},cct_over_app_switch:{type:"Bool",defaultValue:!1},cct_prefetching:{type:"Bool",defaultValue:!1},page_id_account_linking:{type:"Int"},messenger_page_id:{type:"Int"},reset_messenger_state:{type:"Bool",defaultValue:!1},aid:{type:"Int"},deferred_redirect_uri:{type:"String"},code_redirect_uri:{type:"String"},shared_id:{type:"String"},extras:{type:"String"},add_email_reauth_nonce:{type:"String"},tp:{type:"Enum",enumType:1},encrypted_query_string:{type:"String"},account_type:{type:"Enum",enumType:0},fx_app:{type:"Enum",enumType:1},account_deduplication_user_cipher:{type:"String"},skip_dedupe:{type:"Bool",defaultValue:!1},is_promote_auth:{type:"Bool",defaultValue:!1},window_width:{type:"Int"},window_height:{type:"Int"},code_challenge:{type:"String"},code_challenge_method:{type:"Enum",enumType:1},switched_accounts:{type:"Bool",defaultValue:!1},is_first_party_account_linking:{type:"Bool",defaultValue:!1},loyalty_ad_id:{type:"String"},loyalty_referrer:{type:"Enum",enumType:1},privacy_mutation_token:{type:"String"}})}),null);
__d("PlatformDialogCBTSetter",["PlatformOAuthDialogLoginFunnelLogger","URI","XOauthDialogController","uuid"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e){if(a.value===""){var f=Date.now();a.value=f.toString()}d("PlatformOAuthDialogLoginFunnelLogger").log(b,a.value,c,!0,e)}function b(a,b,e){var f=new(c("URI"))(a.action),g=f.getQueryData().next;if(!g)return;g=new(c("URI"))(g);var h=c("XOauthDialogController").getURIBuilder().getURI().getPath().toString();h.endsWith("/")&&(h=h.substr(0,h.length-1));if(!g.getPath().includes(h))return;h=g.getQueryData().cbt;var i=g.getQueryData().logger_id;h||(h=Date.now(),g.addQueryData("cbt",h),f.addQueryData("next",g.toString()),a.action=f.toString());i||(i=c("uuid")(),g.addQueryData("logger_id",i),f.addQueryData("next",g.toString()),a.action=f.toString());d("PlatformOAuthDialogLoginFunnelLogger").log(b,h,i,!0,null,e)}g.setCBTInFieldAndLog=a;g.setCBTInFormAndLog=b}),98);