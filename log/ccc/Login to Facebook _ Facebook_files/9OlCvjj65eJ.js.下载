if (self.CavalryLogger) { CavalryLogger.start_js_script(document.currentScript); }/*FB_PKG_DELIM*/

__d("routeBuilderUtils",[],(function(a,b,c,d,e,f){"use strict";function a(a){a=a.split("/");return a.filter(function(a){return a!==""}).map(function(a){var b=a.split(/{|}/);if(b.length<3)return{isToken:!1,part:a};else{a=b[0];var c=b[1];b=b[2];var d=c[0]==="?",e=c[d?1:0]==="*";c=c.substring((d?1:0)+(e?1:0));return{isToken:!0,optional:d,catchAll:e,prefix:a,suffix:b,token:c}}})}f.getPathParts=a}),66);
__d("jsRouteBuilder",["ConstUriUtils","FBLogger","routeBuilderUtils"],(function(a,b,c,d,e,f,g){"use strict";var h="#";function a(a,b,e,f,g){g===void 0&&(g=!1);var i=d("routeBuilderUtils").getPathParts(a);function j(j){try{var k=f!=null?babelHelpers["extends"]({},f,j):j,l={};j="";var m=!1;j=i.reduce(function(a,c){if(!c.isToken)return a+"/"+c.part;else{var d,e=c.optional,f=c.prefix,g=c.suffix;c=c.token;if(e&&m)return a;d=(d=k[c])!=null?d:b[c];if(d==null&&e){m=!0;return a}if(d==null)throw new Error("Missing required template parameter: "+c);if(d==="")throw new Error("Required template parameter is an empty string: "+c);l[c]=!0;return a+"/"+f+d+g}},"");a.slice(-1)==="/"&&(j+="/");j===""&&(j="/");var n=d("ConstUriUtils").getUri(j);for(var o in k){var p=k[o];!l[o]&&p!=null&&n!=null&&(e!=null&&e.has(o)?p!==!1&&(n=n.addQueryParam(o,null)):n=n.addQueryParam(o,p))}return[n,j]}catch(b){p=b==null?void 0:b.message;o=c("FBLogger")("JSRouteBuilder").blameToPreviousFrame().blameToPreviousFrame();g&&(o=o.blameToPreviousFrame());o.mustfix("Failed building URI for base path: %s message: %s",a,p);return[null,h]}}return{buildUri:function(a){a=(a=j(a)[0])!=null?a:d("ConstUriUtils").getUri(h);if(a==null)throw new Error("Not even the fallback URL parsed validly!");return a},buildUriNullable:function(a){return j(a)[0]},buildURLStringDEPRECATED:function(a){a=j(a);var b=a[0];a=a[1];return(b=b==null?void 0:b.toString())!=null?b:a},buildURL:function(a){a=j(a);var b=a[0];a=a[1];return(b=b==null?void 0:b.toString())!=null?b:a}}}g["default"]=a}),98);
__d("CSTXCookieRecordConsentControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/cookie/consent/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("DeferredCookie",["CSTXCookieRecordConsentControllerRouteBuilder","Cookie","CookieConsent","SubscriptionList","URLSearchParams","cr:1069930","cr:1083116","cr:1083117","flattenPHPQueryData","killswitch","nullthrows","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map(),i=!1,j=new Map(),k={addToQueue:function(a,b,d,e,f,g,i){if(c("CookieConsent").hasConsent(1)){f?c("Cookie").setWithoutChecksIfFirstPartyContext(a,b,d,e,i):c("Cookie").setWithoutChecks(a,b,d,e,i);return}if(h.has(a))return;h.set(a,{name:a,value:b,nMilliSecs:d,path:e,firstPartyOnly:f,secure:i})},flushAllCookiesWithoutRecordingConsentDONOTCALLBEFORECONSENT:function(){h.forEach(function(a,b){a.firstPartyOnly?c("Cookie").setWithoutChecksIfFirstPartyContext(a.name,a.value,a.nMilliSecs,a.path,a.secure):c("Cookie").setWithoutChecks(a.name,a.value,a.nMilliSecs,a.path,a.secure)});if(c("killswitch")("DEFERREDCOOKIE_EMPTY_COOKIES_BEFORE_CALLBACK")){c("CookieConsent").setConsented();for(var a=j,b=Array.isArray(a),d=0,a=b?a:a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]();;){var e;if(b){if(d>=a.length)break;e=a[d++]}else{d=a.next();if(d.done)break;e=d.value}e=e;e[1].fireCallbacks()}h.clear()}else{h.clear();c("CookieConsent").setConsented();for(var e=j,d=Array.isArray(e),b=0,e=d?e:e[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]();;){if(d){if(b>=e.length)break;a=e[b++]}else{b=e.next();if(b.done)break;a=b.value}a=a;a[1].fireCallbacks()}}},flushAllCookiesINTERNALONLY:function(a,d,e,f,g){a===void 0&&(a=!1);e===void 0&&(e=!1);f===void 0&&(f=!1);k.flushAllCookiesWithoutRecordingConsentDONOTCALLBEFORECONSENT();var h={accept_only_essential:f};d!=null&&(d=Object.fromEntries(d),h={optouts:d,accept_only_essential:f});d=c("flattenPHPQueryData")(h);if(!i){f=c("CSTXCookieRecordConsentControllerRouteBuilder").buildUri({});h=new(c("URLSearchParams"))(location.search).get("ig_3p_controls");if(h==="on"){h=f.addQueryParam("ig_3p_controls","on");f=(h=h)!=null?h:f}i=!0;var j=function(){g&&g();a&&location.reload();if(e){var b=document.getElementsByTagName("iframe");b.length>0&&location.reload()}};b("cr:1069930")!=null?c("promiseDone")(b("cr:1069930")(f.toString(),{data:d,method:"POST"}),function(){return j()},function(a){b("cr:1083117")&&b("cr:1083117")("Cookie consent has not been set successfully: "+a.errorMsg,"comet_infra")}):b("cr:1083116")!=null&&new(b("cr:1083116"))(f.toString()).setData(d).setHandler(function(){return j()}).send()}},registerCallbackOnCookieFlush:function(a,b){c("CookieConsent").hasConsent(a)?b():(j.has(a)||j.set(a,new(c("SubscriptionList"))()),c("nullthrows")(j.get(a)).add(b))},canEmbedThirdPartyPixel:function(){return c("CookieConsent").isCookiesBlocked()||!c("CookieConsent").hasConsent(2)?!1:h.size===0}};a=k;g["default"]=a}),98);
__d("DamerauLevenshtein",[],(function(a,b,c,d,e,f){function a(a,b){if(a.length===0)return b.length;if(b.length===0)return a.length;if(a===b)return 0;var c,d,e=[];e[0]=[];e[1]=[];e[2]=[];for(d=0;d<=b.length;d++)e[0][d]=d;for(c=1;c<=a.length;c++)for(d=1;d<=b.length;d++){e[c%3][0]=c;var f=a.charAt(c-1)===b.charAt(d-1)?0:1;e[c%3][d]=Math.min(e[(c-1)%3][d]+1,e[c%3][d-1]+1,e[(c-1)%3][d-1]+f);c>1&&d>1&&a.charAt(c-1)==b.charAt(d-2)&&a.charAt(c-2)==b.charAt(d-1)&&(e[c%3][d]=Math.min(e[c%3][d],e[(c-2)%3][d-2]+f))}return e[a.length%3][b.length]}f.DamerauLevenshteinDistance=a}),66);
__d("BrowserPrefillLogging",["DamerauLevenshtein","ge"],(function(a,b,c,d,e,f){"use strict";var g={initContactpointFieldLogging:function(a){g.contactpointFieldID=a.contactpointFieldID;g._updateContactpoint();g.serverPrefillContactpoint=a.serverPrefill;a=b("ge")(g.contactpointFieldID);if(a==null)return;a.addEventListener("input",g._mayLogContactpointPrefillViaDropdown.bind(g));window.addEventListener("load",g._mayLogContactpointPrefillOnload.bind(g));return},registerCallback:function(a){g.regeisteredCallbacks=g.regeisteredCallbacks||[],g.regeisteredCallbacks.push(a)},_invokeCallbacks:function(a,b){if(g.regeisteredCallbacks==null||g.regeisteredCallbacks.size===0)return;g.regeisteredCallbacks.forEach(function(c){c(a,b)})},initPasswordFieldLogging:function(a){g.passwordFieldID=a.passwordFieldID;g._updatePassword();a=b("ge")(g.passwordFieldID);if(a==null)return;a.addEventListener("input",g._mayLogPasswordPrefillViaDropdown.bind(g));window.addEventListener("load",g._mayLogPasswordPrefillOnload.bind(g))},updatePrefill:function(a,c,d){var e,f=(e=b("ge"))("prefill_source"),g=e("prefill_type"),h=e("first_prefill_source"),i=e("first_prefill_type"),j=e("had_cp_prefilled"),k=e("had_password_prefilled");e=e("prefill_contact_point");f!=null&&(f.value=c);g!=null&&(g.value=d);e!=null&&a!=null&&(e.value=a);i!=null&&(i.value==null||i.value=="")&&(i.value=d);h!=null&&(h.value==null||h.value=="")&&(h.value=c);j!=null&&(j.value==null||j.value==="false")&&d==="contact_point"&&(j.value="true");k!=null&&(k.value==null||k.value==="false")&&d==="password"&&(k.value="true")},_mayLogContactpointPrefillOnload:function(){g._updateContactpoint();if(g.previousContactpoint==null||g.previousContactpoint==="")return;var a=g.previousContactpoint===g.serverPrefillContactpoint?"server_prefill":"browser_onload";g._logBrowserPrefill(a,"contact_point");g._invokeCallbacks(a,"contact_point")},_mayLogPasswordPrefillOnload:function(){g._updatePassword();if(g.previousPassword==null||g.previousPassword==="")return;g._logBrowserPrefill("browser_onload","password");g._invokeCallbacks("browser_onload","password")},_mayLogContactpointPrefillViaDropdown:function(){var a=b("ge")(g.contactpointFieldID);if(a==null||a.value==null)return;if(g._isBrowserPrefill(g.previousContactpoint,a.value)===!1){g._updateContactpoint();return}g._updateContactpoint();g._logBrowserPrefill("browser_dropdown","contact_point");g._invokeCallbacks("browser_dropdown","contact_point")},_mayLogPasswordPrefillViaDropdown:function(){var a=b("ge")(g.passwordFieldID);if(a==null||a.value==null)return;if(g._isBrowserPrefill(g.previousPassword,a.value)===!1){g._updatePassword();return}g._updatePassword();g._logBrowserPrefill("browser_dropdown","password");g._invokeCallbacks("browser_dropdown","password")},_isBrowserPrefill:function(a,c){if(c==="")return!1;if(c===a)return!1;if(c.length===1||a.length===c.length+1||c.length===a.length+1)return!1;var d=b("DamerauLevenshtein").DamerauLevenshteinDistance(c,a);return d===a.length-c.length?!1:!0},_updateContactpoint:function(){var a=b("ge")(g.contactpointFieldID);g.previousContactpoint=a!=null&&a.value!=null?a.value:""},_updatePassword:function(){var a=b("ge")(g.passwordFieldID);g.previousPassword=a!=null&&a.value!=null?a.value:""},_logBrowserPrefill:function(a,b){var c=null;b==="contact_point"&&(c=g.previousContactpoint);a!=="server_prefill"&&g.updatePrefill(c,a,b)}};e.exports=g}),null);