<?php
 
require 'astrology/SweTest.php';    
use astrology\SweTest;   
 
 class index
{
    public $sweph = 'astrology/sweph/';
	
	/**
     * 构造方法
     */
    public function __construct()
    {
         $this->SweTest=new SweTest();
		 $this->planetNameObject =  new planetName();
    }
	
	function handler(){  
      
        $birthdayToTime=169903507;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $house = '121.47,31.23,k';
        $arr = [
            'b' => $utdatenow,
            'p' => '0123456789Hm',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec',

        ]; var_dump($arr);
		
        $planet_json = $this->SweTest->calculate($arr, ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'H']);
	
}


//一月内相位数据
      function astroCalendar()
    {
        $reg_data = array();
        $longitude = '121.47,31.23';

        $year = 2022;
        $month = 11;


        $start_time = mktime(0, 0, 0, $month, 0, $year);

        $lastdaytime = mktime(0, 0, 0, $month + 1, 1, $year);

        $step = (int)(($lastdaytime - $start_time) / 86400);
        $arr = [
            'b' => date('d.m.Y', $start_time),
            'ut' => '16:00:0',
            'p' => '1',
            'd' => '0',
            'n' => $step,
            // 'house' => '9.2,45.46666666666667,K',
            's' => '1440m',
            'f' => 'PTls',  //名字 时间 度数经度 速度 宫位
            'g' => ',',
            'head',
            'roundsec',

        ]; 

        //$planet_phase[] = ['p' => 0, 'd' => 1, 'phase' => [0 => array(), 180 => array()]];
        $planet = [1, 7];
        $planet = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, "D", "F", "E", "G", "H", "I"];

        //$planet = [0, 1, 2, 3, 4];

        //$planet = [0,3];

        $gsdgsdf = array();

        for ($i = 0; $i < count($planet); $i++) {
            for ($j = $i + 1; $j < count($planet); $j++) {
                //$planet_phase[] = ['p' => $planet[$i], 'd' => $planet[$j], 'phase' => [0, 60, 90, 120, 180]];
                $arr['p'] =  $planet[$i];
                $arr['d'] = $planet[$j];
                $phase_li = $this->getDayData($arr, [0, 60, 90, 120, 180]);
                if (!empty($phase_li)) {
                    $gsdgsdf = array_merge($gsdgsdf, $phase_li);
                }
            }
        }




        

        $planetChinese = $this->planetNameObject::$planetChinese;
        $planetFont = $this->planetNameObject::$planetFont;
        $signFont = $this->planetNameObject::$signFont;
        $signChinese = $this->planetNameObject::$signChinese;
        $planetEnglish = $this->planetNameObject::$planetEnglish;
        $planetOneChinese = $this->planetNameObject::$planetOneChinese;
        $signEnglish = $this->planetNameObject::$signEnglish;

        $SignNamesSimple = array("羊", "牛", "双", "蟹", "狮", "处", "秤", "蝎", "射", "摩", "瓶", "鱼");
        $allow_degree_cn = [
            '0' => '合',
            '30' => '十二分',
            '36' => '十分',
            '45' => '八分',
            '60' => '六合',
            '72' => '五分',
            '90' => '刑',
            '120' => '拱',
            '135' => '补八分',
            '144' => '补五分',
            '150' => '梅花',
            '180' => '冲'
        ];



        $corpusTwoWhere['chartType'] = 5;
        $corpusTwoWhere['type'] = ['in',[5,6]];
        $corpusConstellationColumn = $this->logicCorpusTwo->getCorpusTwoColumn($corpusTwoWhere, 'id,oneself,other,degree,keywords,content');


        //    1空亡 2相位 3星座  4新月 5满月 6逆
        foreach ($gsdgsdf as $keydate => $valuedate) {

            if (empty($valuedate) or $valuedate['time'] < 1682956800) {
                //  dump($valuedate);
            }
            empty($planet_all_data[strtotime(date('Y-m-d', $valuedate['time']))]) && $planet_all_data[strtotime(date('Y-m-d', $valuedate['time']))] = array();
            $valuedate['time'] += 60 * 60 * 8;
            $valuedate['type'] = 2;
            $valuedate['time_long'] = $valuedate['time'];
            $valuedate['time'] = date('H:i', $valuedate['time']);
            $valuedate['end_time'] = "2023-03-01 04:04";
            $valuedate['planet_english1'] = $planetEnglish[$valuedate['planet_code1']];
            $valuedate['planet_chinese1'] = $planetChinese[$valuedate['planet_code1']];
            $valuedate['planet_font1'] = $planetFont[$valuedate['planet_english1']];
            $valuedate['planet_english2'] = $planetEnglish[$valuedate['planet_code2']];
            $valuedate['planet_chinese2'] = $planetChinese[$valuedate['planet_code2']];
            $valuedate['planet_font2'] = $planetFont[$valuedate['planet_english2']];
            $valuedate['title'] = $planetOneChinese[$valuedate['planet_code1']] . $allow_degree_cn[$valuedate['allow']] . $planetOneChinese[$valuedate['planet_code2']];
            $valuedate['keywords'] ='';
            $valuedate['content'] ='';

            foreach ($corpusConstellationColumn as $keycr=>$valuecr){
                if((($valuedate['planet_chinese1']==$valuecr['oneself'] and $valuedate['planet_chinese2']==$valuecr['other']) or ($valuedate['planet_chinese1']==$valuecr['other'] and $valuedate['planet_chinese2']==$valuecr['oneself'])) and $valuecr['degree']==$valuedate['allow']){
                    $valuedate['keywords'] =$valuecr['keywords'];
                    $valuedate['content'] =$valuecr['content'];
                    break;
                }
            }

            $planet_all_data[strtotime(date('Y-m-d', $valuedate['time_long']))][] = $valuedate;

            if ($valuedate['planet_code1'] == 0 and $valuedate['planet_code2'] == 1) {
                if ($valuedate['allow'] == 0) {
                    $valuedate['type'] = 4;
                    $valuedate['title'] = '新月';
                    $planet_all_data[strtotime(date('Y-m-d', $valuedate['time_long']))][] = $valuedate;
                }
                if ($valuedate['allow'] == 180) {
                    $valuedate['type'] = 5;
                    $valuedate['title'] = '满月';
                    $planet_all_data[strtotime(date('Y-m-d', $valuedate['time_long']))][] = $valuedate;
                }
            }
        }

        ///星座落入计算
        $arr2 = [
            'b' => date('d.m.Y', $start_time),
            'ut' => '16:00:0',
            'p' => implode('', $planet),
            'n' => $step,
            // 'house' => '9.2,45.46666666666667,K',
            's' => '1440m',
            'f' => 'PTls',  //名字 时间 度数经度 速度 宫位
            'g' => ',',
            'head',
            'roundsec'
        ];

        $plan_in_signs = $this->getSignDayData($arr2);

        $plan_sign_in_sign=array();
        foreach ($plan_in_signs as $keups => $voleps) {
            $plan_sign_in_sign[$voleps['planet_code']][]=$voleps;
        }

        //顺逆

        $planet_in_list=[
            ["date"=>"2023-1-12 20:56:00","in_type"=>"1","planet_code"=>"4","sign"=>"2","longitude"=>"8.11666666666667"],
            ["date"=>"2023-1-18 13:12:00","in_type"=>"1","planet_code"=>"2","sign"=>"9","longitude"=>"8.13333333333333"],
            ["date"=>"2023-1-22 22:58:00","in_type"=>"1","planet_code"=>"7","sign"=>"1","longitude"=>"14.9333333333333"],
            ["date"=>"2023-4-21 8:35:00","in_type"=>"-1","planet_code"=>"2","sign"=>"1","longitude"=>"15.6166666666667"],
            ["date"=>"2023-5-1 17:08:00","in_type"=>"-1","planet_code"=>"9","sign"=>"10","longitude"=>"0.35"],
            ["date"=>"2023-5-15 3:17:00","in_type"=>"1","planet_code"=>"2","sign"=>"1","longitude"=>"5.83333333333333"],
            ["date"=>"2023-6-17 17:27:00","in_type"=>"-1","planet_code"=>"6","sign"=>"11","longitude"=>"7.2"],
            ["date"=>"2023-6-30 21:07:00","in_type"=>"-1","planet_code"=>"8","sign"=>"11","longitude"=>"27.6833333333333"],
            ["date"=>"2023-7-23 1:33:00","in_type"=>"-1","planet_code"=>"3","sign"=>"4","longitude"=>"28.6"],
            ["date"=>"2023-7-23 12:42:00","in_type"=>"-1","planet_code"=>"D","sign"=>"0","longitude"=>"19.95"],
            ["date"=>"2023-8-23 19:59:00","in_type"=>"-1","planet_code"=>"2","sign"=>"5","longitude"=>"21.85"],
            ["date"=>"2023-8-29 2:39:00","in_type"=>"-1","planet_code"=>"7","sign"=>"1","longitude"=>"23.0666666666667"],
            ["date"=>"2023-9-4 1:20:00","in_type"=>"1","planet_code"=>"3","sign"=>"4","longitude"=>"12.2"],
            ["date"=>"2023-9-4 14:11:00","in_type"=>"-1","planet_code"=>"5","sign"=>"1","longitude"=>"15.5666666666667"],
            ["date"=>"2023-9-15 20:21:00","in_type"=>"1","planet_code"=>"2","sign"=>"5","longitude"=>"8"],
            ["date"=>"2023-10-11 1:09:00","in_type"=>"1","planet_code"=>"9","sign"=>"9","longitude"=>"27.8833333333333"],
            ["date"=>"2023-11-4 7:03:00","in_type"=>"1","planet_code"=>"6","sign"=>"11","longitude"=>"0.5"],
            ["date"=>"2023-12-6 13:22:00","in_type"=>"1","planet_code"=>"8","sign"=>"11","longitude"=>"24.8833333333333"],
            ["date"=>"2023-12-13 7:09:00","in_type"=>"-1","planet_code"=>"2","sign"=>"9","longitude"=>"8.48333333333333"],
            ["date"=>"2023-12-27 3:10:00","in_type"=>"1","planet_code"=>"D","sign"=>"0","longitude"=>"15.45"],
            ["date"=>"2023-12-31 2:40:00","in_type"=>"1","planet_code"=>"5","sign"=>"1","longitude"=>"5.56666666666667"],
            ["date"=>"2024-1-2 3:07:00","in_type"=>"1","planet_code"=>"2","sign"=>"8","longitude"=>"22.1666666666667"],
            ["date"=>"2024-1-27 7:35:00","in_type"=>"1","planet_code"=>"7","sign"=>"1","longitude"=>"19.0833333333333"],
            ["date"=>"2024-4-1 22:14:00","in_type"=>"-1","planet_code"=>"2","sign"=>"0","longitude"=>"27.2166666666667"],
            ["date"=>"2024-4-25 12:54:00","in_type"=>"1","planet_code"=>"2","sign"=>"0","longitude"=>"15.9666666666667"],
            ["date"=>"2024-5-2 17:47:00","in_type"=>"-1","planet_code"=>"9","sign"=>"10","longitude"=>"2.1"],
            ["date"=>"2024-6-29 19:06:00","in_type"=>"-1","planet_code"=>"6","sign"=>"11","longitude"=>"19.4166666666667"],
            ["date"=>"2024-7-2 10:40:00","in_type"=>"-1","planet_code"=>"8","sign"=>"11","longitude"=>"29.9166666666667"],
            ["date"=>"2024-7-26 13:59:00","in_type"=>"-1","planet_code"=>"D","sign"=>"0","longitude"=>"23.5333333333333"],
            ["date"=>"2024-8-5 4:56:00","in_type"=>"-1","planet_code"=>"2","sign"=>"5","longitude"=>"4.1"],
            ["date"=>"2024-8-28 21:14:00","in_type"=>"1","planet_code"=>"2","sign"=>"4","longitude"=>"21.4"],
            ["date"=>"2024-9-1 15:18:00","in_type"=>"-1","planet_code"=>"7","sign"=>"1","longitude"=>"27.25"],
            ["date"=>"2024-10-9 7:04:00","in_type"=>"-1","planet_code"=>"5","sign"=>"2","longitude"=>"21.3333333333333"],
            ["date"=>"2024-10-12 0:32:00","in_type"=>"1","planet_code"=>"9","sign"=>"9","longitude"=>"29.6333333333333"],
            ["date"=>"2024-11-15 14:21:00","in_type"=>"1","planet_code"=>"6","sign"=>"11","longitude"=>"12.6833333333333"],
            ["date"=>"2024-11-26 2:42:00","in_type"=>"-1","planet_code"=>"2","sign"=>"8","longitude"=>"22.6666666666667"],
            ["date"=>"2024-12-6 23:33:00","in_type"=>"-1","planet_code"=>"4","sign"=>"4","longitude"=>"6.16666666666667"],
            ["date"=>"2024-12-7 23:43:00","in_type"=>"1","planet_code"=>"8","sign"=>"11","longitude"=>"27.1166666666667"],
            ["date"=>"2024-12-15 20:56:00","in_type"=>"1","planet_code"=>"2","sign"=>"8","longitude"=>"6.38333333333333"],
            ["date"=>"2024-12-29 21:13:00","in_type"=>"1","planet_code"=>"D","sign"=>"0","longitude"=>"19"],
            ["date"=>"2025-1-30 16:22:00","in_type"=>"1","planet_code"=>"7","sign"=>"1","longitude"=>"23.25"],
            ["date"=>"2025-2-4 9:40:00","in_type"=>"1","planet_code"=>"5","sign"=>"2","longitude"=>"11.2666666666667"],
            ["date"=>"2025-2-24 2:00:00","in_type"=>"1","planet_code"=>"4","sign"=>"3","longitude"=>"17"],
            ["date"=>"2025-3-2 0:36:00","in_type"=>"-1","planet_code"=>"3","sign"=>"0","longitude"=>"10.8333333333333"],
            ["date"=>"2025-3-15 6:46:00","in_type"=>"-1","planet_code"=>"2","sign"=>"0","longitude"=>"9.58333333333333"],
            ["date"=>"2025-4-7 11:08:00","in_type"=>"1","planet_code"=>"2","sign"=>"11","longitude"=>"26.8166666666667"],
            ["date"=>"2025-4-13 1:02:00","in_type"=>"1","planet_code"=>"3","sign"=>"11","longitude"=>"24.6166666666667"],
            ["date"=>"2025-5-4 15:27:00","in_type"=>"-1","planet_code"=>"9","sign"=>"10","longitude"=>"3.81666666666667"],
            ["date"=>"2025-7-4 21:33:00","in_type"=>"-1","planet_code"=>"8","sign"=>"0","longitude"=>"2.16666666666667"],
            ["date"=>"2025-7-13 4:07:00","in_type"=>"-1","planet_code"=>"6","sign"=>"0","longitude"=>"1.93333333333333"],
            ["date"=>"2025-7-18 4:45:00","in_type"=>"-1","planet_code"=>"2","sign"=>"4","longitude"=>"15.5666666666667"],
            ["date"=>"2025-7-30 14:42:00","in_type"=>"-1","planet_code"=>"D","sign"=>"0","longitude"=>"27.15"],
            ["date"=>"2025-8-11 7:30:00","in_type"=>"1","planet_code"=>"2","sign"=>"4","longitude"=>"4.23333333333333"],
            ["date"=>"2025-9-6 4:51:00","in_type"=>"-1","planet_code"=>"7","sign"=>"2","longitude"=>"1.45"],
            ["date"=>"2025-10-14 2:52:00","in_type"=>"1","planet_code"=>"9","sign"=>"10","longitude"=>"1.36666666666667"],
            ["date"=>"2025-11-9 19:01:00","in_type"=>"-1","planet_code"=>"2","sign"=>"8","longitude"=>"6.85"],
            ["date"=>"2025-11-11 16:41:00","in_type"=>"-1","planet_code"=>"5","sign"=>"3","longitude"=>"25.15"],
            ["date"=>"2025-11-28 3:51:00","in_type"=>"1","planet_code"=>"6","sign"=>"11","longitude"=>"25.15"],
            ["date"=>"2025-11-29 17:38:00","in_type"=>"1","planet_code"=>"2","sign"=>"7","longitude"=>"20.7"],
            ["date"=>"2025-12-10 12:23:00","in_type"=>"1","planet_code"=>"8","sign"=>"11","longitude"=>"29.3666666666667"],
            ["date"=>"2026-1-2 14:38:00","in_type"=>"1","planet_code"=>"D","sign"=>"0","longitude"=>"22.5833333333333"],
            ["date"=>"2026-2-4 2:33:00","in_type"=>"1","planet_code"=>"7","sign"=>"1","longitude"=>"27.45"],
            ["date"=>"2026-2-26 6:48:00","in_type"=>"-1","planet_code"=>"2","sign"=>"11","longitude"=>"22.55"],
            ["date"=>"2026-3-11 3:30:00","in_type"=>"1","planet_code"=>"5","sign"=>"3","longitude"=>"15.0833333333333"],
            ["date"=>"2026-3-20 19:33:00","in_type"=>"1","planet_code"=>"2","sign"=>"11","longitude"=>"8.48333333333333"],
            ["date"=>"2026-5-6 15:34:00","in_type"=>"-1","planet_code"=>"9","sign"=>"10","longitude"=>"5.5"],
            ["date"=>"2026-6-29 17:36:00","in_type"=>"-1","planet_code"=>"2","sign"=>"3","longitude"=>"26.25"],
            ["date"=>"2026-7-7 10:55:00","in_type"=>"-1","planet_code"=>"8","sign"=>"0","longitude"=>"4.41666666666667"],
            ["date"=>"2026-7-23 22:58:00","in_type"=>"1","planet_code"=>"2","sign"=>"3","longitude"=>"16.3"],
            ["date"=>"2026-7-26 19:56:00","in_type"=>"-1","planet_code"=>"6","sign"=>"0","longitude"=>"14.75"],
            ["date"=>"2026-8-3 20:10:00","in_type"=>"-1","planet_code"=>"D","sign"=>"1","longitude"=>"0.866666666666667"],
            ["date"=>"2026-9-10 18:27:00","in_type"=>"-1","planet_code"=>"7","sign"=>"2","longitude"=>"5.68333333333333"],
            ["date"=>"2026-10-3 7:16:00","in_type"=>"-1","planet_code"=>"3","sign"=>"7","longitude"=>"8.48333333333333"],
            ["date"=>"2026-10-16 2:40:00","in_type"=>"1","planet_code"=>"9","sign"=>"10","longitude"=>"3.06666666666667"],
            ["date"=>"2026-10-24 7:12:00","in_type"=>"-1","planet_code"=>"2","sign"=>"7","longitude"=>"20.9666666666667"],
            ["date"=>"2026-11-13 15:54:00","in_type"=>"1","planet_code"=>"2","sign"=>"7","longitude"=>"5.03333333333333"],
            ["date"=>"2026-11-14 0:27:00","in_type"=>"1","planet_code"=>"3","sign"=>"6","longitude"=>"22.85"],
            ["date"=>"2026-12-10 23:31:00","in_type"=>"1","planet_code"=>"6","sign"=>"0","longitude"=>"7.91666666666667"],
            ["date"=>"2026-12-12 22:17:00","in_type"=>"1","planet_code"=>"8","sign"=>"0","longitude"=>"1.6"],
            ["date"=>"2026-12-13 0:56:00","in_type"=>"-1","planet_code"=>"5","sign"=>"4","longitude"=>"27.0166666666667"],
            ["date"=>"2027-1-6 11:01:00","in_type"=>"1","planet_code"=>"D","sign"=>"0","longitude"=>"26.25"],
            ["date"=>"2027-1-10 12:59:00","in_type"=>"-1","planet_code"=>"4","sign"=>"5","longitude"=>"10.4166666666667"],
            ["date"=>"2027-2-8 12:29:00","in_type"=>"1","planet_code"=>"7","sign"=>"2","longitude"=>"1.66666666666667"],
            ["date"=>"2027-2-9 17:36:00","in_type"=>"-1","planet_code"=>"2","sign"=>"11","longitude"=>"5.96666666666667"],
            ["date"=>"2027-3-3 12:32:00","in_type"=>"1","planet_code"=>"2","sign"=>"10","longitude"=>"20.9166666666667"],
            ["date"=>"2027-4-1 14:08:00","in_type"=>"1","planet_code"=>"4","sign"=>"4","longitude"=>"20.9166666666667"],
            ["date"=>"2027-4-13 2:11:00","in_type"=>"1","planet_code"=>"5","sign"=>"4","longitude"=>"16.9833333333333"],
            ["date"=>"2027-5-8 12:54:00","in_type"=>"-1","planet_code"=>"9","sign"=>"10","longitude"=>"7.16666666666667"],
            ["date"=>"2027-6-10 18:15:00","in_type"=>"-1","planet_code"=>"2","sign"=>"3","longitude"=>"6.35"],
            ["date"=>"2027-7-4 19:39:00","in_type"=>"1","planet_code"=>"2","sign"=>"2","longitude"=>"27.4666666666667"],
            ["date"=>"2027-7-9 22:41:00","in_type"=>"-1","planet_code"=>"8","sign"=>"0","longitude"=>"6.65"],
            ["date"=>"2027-8-8 2:55:00","in_type"=>"-1","planet_code"=>"D","sign"=>"1","longitude"=>"4.66666666666667"],
            ["date"=>"2027-8-9 18:06:00","in_type"=>"-1","planet_code"=>"6","sign"=>"0","longitude"=>"27.8666666666667"],
            ["date"=>"2027-9-15 9:09:00","in_type"=>"-1","planet_code"=>"7","sign"=>"2","longitude"=>"9.95"],
            ["date"=>"2027-10-7 14:37:00","in_type"=>"-1","planet_code"=>"2","sign"=>"7","longitude"=>"4.91666666666667"],
            ["date"=>"2027-10-18 3:52:00","in_type"=>"1","planet_code"=>"9","sign"=>"10","longitude"=>"4.73333333333333"],
            ["date"=>"2027-10-28 14:11:00","in_type"=>"1","planet_code"=>"2","sign"=>"6","longitude"=>"19.3"],
            ["date"=>"2027-12-15 9:06:00","in_type"=>"1","planet_code"=>"8","sign"=>"0","longitude"=>"3.85"],
            ["date"=>"2027-12-24 2:47:00","in_type"=>"1","planet_code"=>"6","sign"=>"0","longitude"=>"21.0166666666667"]];
        $shun_ni_lit=array();
        foreach ($planet_in_list as $kegl=>$vjks){

            empty($plan_in_signs_two[strtotime($vjks['date'])]) && $plan_in_signs_two[strtotime($vjks['date'])] = array();

            $type_str='顺行';
            if($vjks['in_type']==-1){
                $type_str='逆行';
            }

            $vjks['date']=date('Y-m-d H:i:s', (strtotime($vjks['date'])+28800));
            $vjks['type'] = 6;
            $vjks['time_long'] = strtotime($vjks['date']);
            $vjks['time'] = date('H:i', $vjks['time_long']);
            $vjks['end_time'] = "";
            foreach ($planet_in_list as $keygt=>$valuegt){
                if($valuegt['planet_code']==$vjks['planet_code'] and $valuegt['in_type']!=$vjks['in_type']and strtotime($vjks['date'])<strtotime($valuegt['date'])){
                    $vjks['end_time']=$valuegt['date'];
                    break;
                }
            }
            $vjks['sign_font'] = $signFont[$vjks['sign']];
            $vjks['sign_chinese'] = $signChinese[$vjks['sign']];
            $vjks['sign_english'] = $signEnglish[$vjks['sign']];
            $vjks['planet_english'] = $planetEnglish[$vjks['planet_code']];
            $vjks['planet_chinese'] = $planetChinese[$vjks['planet_code']];
            $vjks['planet_font'] = $planetFont[$vjks['planet_english']];
            $vjks['title'] = $planetOneChinese[$vjks['planet_code']] . $type_str;
            $shun_ni_lit[strtotime(date('Y-m-d', $vjks['time_long']))][]=$vjks;

        }


//        $arr3 = [
//            'b' => date('d.m.Y', $start_time),
//            'ut' => '16:00:0',
//            'p' => implode('', $planet),
//            'n' => $step,
//            // 'house' => '9.2,45.46666666666667,K',
//            's' => '1440m',
//            'f' => 'PTls',  //名字 时间 度数经度 速度 宫位
//            'g' => ',',
//            'head',
//            'roundsec'
//        ];
        //$plan_in_speed = $this->getSpeedDayData($arr3);
        //exit();

       // dump($plan_sign_in_sign);



        foreach ($plan_in_signs as $keups => $voleps) {
            empty($plan_in_signs_two[strtotime(date('Y-m-d', $voleps['time']))]) && $plan_in_signs_two[strtotime(date('Y-m-d', $voleps['time']))] = array();
            $voleps['time'] += 60 * 60 * 8;
            $voleps['type'] = 3;
            $voleps['time_long'] = $voleps['time'];
            $voleps['time'] = date('H:i', $voleps['time']);

            $voleps['end_time'] = "";

            foreach ($plan_sign_in_sign[$voleps['planet_code']] as $keygt=>$valuegt){
                if($valuegt['time']>$voleps['time_long']){
                    $voleps['end_time']=date('Y-m-d H:i', $valuegt['time']);
                    break;
                }
            }

            $voleps['sign_font'] = $signFont[$voleps['sign']];
            $voleps['sign_chinese'] = $signChinese[$voleps['sign']];
            $voleps['sign_english'] = $signEnglish[$voleps['sign']];
            $voleps['planet_chinese'] = $planetChinese[$voleps['planet_code']];
            $voleps['planet_font'] = $planetFont[$voleps['planet_english']];
            $voleps['title'] = $planetOneChinese[$voleps['planet_code']] . $SignNamesSimple[$voleps['sign']];


            foreach ($corpusConstellationColumn as $keycr=>$valuecr){
                if($voleps['sign_chinese']==$valuecr['other'] and $voleps['planet_chinese']==$valuecr['oneself']){
                    $voleps['keywords'] =$valuecr['keywords'];
                    $voleps['content'] =$valuecr['content'];
                    break;
                }
            }

            $plan_in_signs_two[strtotime(date('Y-m-d', $voleps['time_long']))][] = $voleps;

        }

        $data_pland = array();
        $start_time_yue = $start_time + 86400;

        for ($i = 0; $i < $step; $i++) {
            if ($lastdaytime > $start_time_yue + $i * 86400) {
                empty($data_pland[$i + 1]) && $data_pland[$i + 1] = array();
                if (!empty($planet_all_data[$start_time_yue + $i * 86400])) {
                    //dump($planet_all_data[$start_time+$i*86400]);
                    $data_pland[$i + 1] = array_merge($data_pland[$i + 1], $planet_all_data[$start_time_yue + $i * 86400]);
                }
                if (!empty($plan_in_signs_two[$start_time_yue + $i * 86400])) {
                    $data_pland[$i + 1] = array_merge($data_pland[$i + 1], $plan_in_signs_two[$start_time_yue + $i * 86400]);
                }
                if (!empty($shun_ni_lit[$start_time_yue + $i * 86400])) {
                    $data_pland[$i + 1] = array_merge($data_pland[$i + 1], $shun_ni_lit[$start_time_yue + $i * 86400]);
                }
            }
        }

        $redis_array[($year.$month)]=$data_pland;
        Cache::store('redis')->set('astroCalendar',$redis_array);

        return $this->apiReturn($data_pland);
    }

    //天----相位
      function getDayData($arr, $phase)
    {

        $planet_json = $this->SweTest->SweTest($arr);

        $planet_json_count = count($planet_json);
        $plants = array();
        foreach ($phase as $keyp => $valuep) {
            foreach ($planet_json as $key => $value) {
                if ($key >= ($planet_json_count - 1)) {
                    break 1;
                }
                $new_lineInfo = explode(',', $value);
                $new_defug = trim($new_lineInfo[2], ' ');

                $next_lineInfo = explode(',', $planet_json[$key + 1]);
                $next_defug = trim($next_lineInfo[2], ' ');

                $valuep_cha = $valuep - $new_defug;
                if ($valuep_cha > 180) {
                    $valuep_cha = 360 - $valuep_cha;
                }

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }

                $xia_bu = $this->interval_value($new_defug, $valuep, $next_defug);

                if ($xia_bu) {
                    if (abs($valuep_cha) < 0.02) {
                        $plants[] = ['planet_code1' => $arr['p'], 'planet_code2' => $arr['d'], 'time' => $newMoon_time, 'allow_cha' => $valuep_cha, 'allow' => $valuep, 'longitude' => $new_defug];
                    } else {
                        $phase_xia = [$valuep];
                        $plants[] = $this->getHoursData($arr, $phase_xia);
                    }
                }

            }
        }

        if (!empty($plants)) {
            return $plants;
        }
    }

    //小时----相位
      function interval_value($shang, $value, $xia)
    {
        $xia_bu = false;
        if ($shang > 0 and $xia < 0) {
            if (($shang - $xia) > 180) {
                if ($value >= $shang and $value <= 180) {
                    $xia_bu = true;
                } else if (abs($value) > abs($xia)) {
                    $xia_bu = true;
                }
            } else {
                if ($value <= $shang and $value > 0) {
                    $xia_bu = true;
                } else if (abs($value) < abs($xia)) {
                    $xia_bu = true;
                }
            }
        } else if ($shang < 0 and $xia > 0) {
            //前负后正
            if (($xia - $shang) > 180) {
                if ($value <= $shang and $value < 0) {
                    $xia_bu = true;
                } else if ($shang > $xia and $value > 0) {
                    $xia_bu = true;
                }
            } else {
                if ($value >= $shang and $value <= 0) {
                    $xia_bu = true;
                } else if ($value < $xia and $value > 0) {
                    $xia_bu = true;
                }
            }
        } else {
            $shang_zheng = abs($shang);
            $xia_zheng = abs($xia);
            if (($value >= $shang_zheng and $value < $xia_zheng) or ($value <= $shang_zheng and $value > $xia_zheng)) {
                $xia_bu = true;
            }
        }
        return $xia_bu;
    }

    //分----相位
      function getHoursData($arr, $phase)
    {

        $arr['n'] = '25';
        $arr['s'] = '60m';
        $planet_json = $this->SweTest->SweTest($arr);
        $planet_json_count = count($planet_json);
        foreach ($phase as $keyp => $valuep) {
            foreach ($planet_json as $key => $value) {
                if ($key >= ($planet_json_count - 1)) {
                    break 1;
                }
                $dian = false;
                $new_lineInfo = explode(',', $value);
                $new_defug = trim($new_lineInfo[2], ' ');

                $next_lineInfo = explode(',', $planet_json[$key + 1]);
                $next_defug = trim($next_lineInfo[2], ' ');

                $valuep_cha = $valuep - $new_defug;
                if ($valuep_cha > 180) {
                    $valuep_cha = 360 - $valuep_cha;
                }

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }

                $xia_bu = $this->interval_value($new_defug, $valuep, $next_defug);
                if ($xia_bu) {
                    if (abs($valuep_cha) < 0.02) {
                        return ['planet_code1' => $arr['p'], 'planet_code2' => $arr['d'], 'time' => $newMoon_time, 'allow_cha' => $valuep_cha, 'allow' => $valuep, 'longitude' => $new_defug];
                    } else {
                        $phase_xia = [$valuep];
                        return $this->getMinutesData($arr, $phase_xia);
                    }
                }
            }
        }
    }

    //秒----相位
      function getMinutesData($arr, $phase)
    {
        $arr['n'] = '61';
        $arr['s'] = '1m';
        $planet_json = $this->SweTest->SweTest($arr);

        $planet_json_count = count($planet_json);

        foreach ($phase as $keyp => $valuep) {
            foreach ($planet_json as $key => $value) {
                if ($key >= ($planet_json_count - 1)) {
                    break 1;
                }
                $dian = false;
                $new_lineInfo = explode(',', $value);
                $new_defug = trim($new_lineInfo[2], ' ');

                $next_lineInfo = explode(',', $planet_json[$key + 1]);
                $next_defug = trim($next_lineInfo[2], ' ');

                $valuep_cha = $valuep - $new_defug;
                if ($valuep_cha > 180) {
                    $valuep_cha = 360 - $valuep_cha;
                }

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }

                $xia_bu = $this->interval_value($new_defug, $valuep, $next_defug);

                if ($xia_bu) {
                    if (abs($valuep_cha) < 0.02) {
                        return ['planet_code1' => $arr['p'], 'planet_code2' => $arr['d'], 'time' => $newMoon_time, 'allow_cha' => $valuep_cha, 'allow' => $valuep, 'longitude' => $new_defug];
                    } else {
                        $phase_xia = [$valuep];
                        return $this->getSecondsData($arr, $phase_xia);
                    }
                }
            }
        }
    }

    //计算----相位
      function getSecondsData($arr, $phase)
    {
        $arr['n'] = '61';
        $arr['s'] = '1s';
        $planet_json = $this->SweTest->SweTest($arr);

        $planet_json_count = count($planet_json);

        foreach ($phase as $keyp => $valuep) {
            foreach ($planet_json as $key => $value) {
                if ($key >= ($planet_json_count - 1)) {
                    break 1;
                }
                $dian = false;
                $new_lineInfo = explode(',', $value);
                $new_defug = trim($new_lineInfo[2], ' ');
                $next_lineInfo = explode(',', $planet_json[$key + 1]);
                $next_defug = trim($next_lineInfo[2], ' ');
                $valuep_cha = abs(abs($valuep) - abs($new_defug));
                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                $xia_bu = $this->interval_value($new_defug, $valuep, $next_defug);
                if ($xia_bu) {
                    if (abs($valuep_cha) < 0.05) {
                        return ['planet_code1' => $arr['p'], 'planet_code2' => $arr['d'], 'time' => $newMoon_time, 'allow_cha' => $valuep_cha, 'allow' => $valuep, 'longitude' => $new_defug];
                    }
                }
            }
        }
    }


    //计算星座  天
      function getSignDayData($arr)
    {
        $planet_json = $this->SweTest->SweTest($arr);
        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $arr_xia = $arr;

                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    $arr_xia['p'] = (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish);
                    $data_array[] = $this->getSignHoursData($arr_xia);

                }
            }
        }
        return $data_array;
    }

    //计算星座  小时
      function getSignHoursData($arr)
    {
        $arr['n'] = '25';
        $arr['s'] = '60m';

        $planet_json = $this->SweTest->SweTest($arr);
        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $arr_xia = $arr;
                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    $arr_xia['p'] = (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish);
                    return $this->getSignMinutesData($arr_xia);
                }
            }
        }

    }

    //计算星座  分
      function getSignMinutesData($arr)
    {
        $arr['n'] = '61';
        $arr['s'] = '1m';

        $planet_json = $this->SweTest->SweTest($arr);


        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $arr_xia = $arr;
                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    $arr_xia['p'] = (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish);
                    return $this->getSignSecondsData($arr_xia);
                }
            }
        }
    }

    //计算星座  秒
      function getSignSecondsData($arr)
    {
        $arr_xia['n'] = '61';
        $arr_xia['s'] = '1s';

        $planet_json = $this->SweTest->SweTest($arr);
        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    return ['planet_english' => str_replace(' ', '', $new_lineInfo[0]), 'planet_code' => (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish), 'sign' => $newsign_num+1, 'time' => $newMoon_time, 'longitude' => $new_defug];
                }
            }
        }
    }



    //计算顺逆  天
      function getSpeedDayData($arr)
    {
        $planet_json = $this->SweTest->SweTest($arr);
        dump($planet_json);
        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $arr_xia = $arr;

                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    $arr_xia['p'] = (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish);
                    //$data_array[] = $this->getSignHoursData($arr_xia);
                }
            }
        }
        return $data_array;
    }

    //计算顺逆  小时
      function getSpeedHoursData($arr)
    {
        $arr['n'] = '25';
        $arr['s'] = '60m';

        $planet_json = $this->SweTest->SweTest($arr);
        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $arr_xia = $arr;
                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    $arr_xia['p'] = (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish);
                    return $this->getSignMinutesData($arr_xia);
                }
            }
        }

    }

    //计算顺逆  分
      function getSpeedMinutesData($arr)
    {
        $arr['n'] = '61';
        $arr['s'] = '1m';

        $planet_json = $this->SweTest->SweTest($arr);


        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $arr_xia = $arr;
                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    $arr_xia['p'] = (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish);
                    return $this->getSignSecondsData($arr_xia);
                }
            }
        }
    }

    //计算顺逆  秒
      function getSpeedSecondsData($arr)
    {
        $arr_xia['n'] = '61';
        $arr_xia['s'] = '1s';

        $planet_json = $this->SweTest->SweTest($arr);
        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        for ($i = 0; $i < $step; $i++) {
            for ($j = 0; $j < $planet_count; $j++) {
                $value = $planet_json[$i * $planet_count + $j];
                $new_lineInfo = explode(',', $value);
                $new_defug = $this->SweTest->crunch(trim($new_lineInfo[2], ' '));
                $next_lineInfo = explode(',', $planet_json[($i + 1) * $planet_count + $j]);
                $next_defug = $this->SweTest->crunch(trim($next_lineInfo[2], ' '));
                $newsign_num = intval($new_defug / 30);

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $arr['b'] = date('d.m.Y', $newMoon_time);
                    $arr['ut'] = date('H:i:s', $newMoon_time);
                }
                if ($next_defug >= ($newsign_num + 1) * 30) {
                    $planetEnglish = $this->planetNameObject::$planetEnglish;
                    return ['planet_english' => str_replace(' ', '', $new_lineInfo[0]), 'planet_code' => (string)array_search(str_replace(' ', '', $new_lineInfo[0]), $planetEnglish), 'sign' => $newsign_num+1, 'time' => $newMoon_time, 'longitude' => $new_defug];
                }
            }
        }
    }
}

$indexs=new index();
$indexs=$indexs->astroCalendar();

