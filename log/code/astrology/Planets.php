<?php

namespace astrology;


class Planets
{
    public $sweph = './sweph/';
    public $tz = 8;
    public $birthday = '1999-10-17 21:00:00';

    public $longitude = '116.50';
    public $latitude = '31.77';
    public $h_sys = 'k';
    public $starsCode = '0123456789DAt';
    public $starsArray = array();

    public $planetOut = array();

    function __construct($mdymt)
    {
        !empty($mdymt['starsCode']) && $this->starsCode = $mdymt['starsCode'];
        !empty($mdymt['h_sys']) && $this->h_sys = $mdymt['h_sys'];
        !empty($mdymt['birthday']) && $this->birthday = $mdymt['birthday'];

        !empty($mdymt['longitude']) && $this->longitude = $mdymt['longitude'];
        !empty($mdymt['latitude']) && $this->latitude = $mdymt['latitude'];
        !empty($mdymt['tz']) && $this->tz = $mdymt['tz'];

        $this->starsArray = str_split($this->starsCode, 1);                  //拆分请求星体代码


        $birthdayToTime = strtotime($this->birthday) - $this->tz * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);
        $utnow = date('H:i:s', $birthdayToTime);

        if(!empty($mdymt['swet_query'])){
            exec($this->sweph . "swetest -edir".$this->sweph." ".$mdymt['swet_query'], $planetOut);
        }else{
            exec($this->sweph . "swetest -edir$this->sweph -b$utdatenow -ut$utnow -xs16 -p$this->starsCode -eswe -house$this->longitude,$this->latitude,$this->h_sys -fPlsj -roundsec -g, -head", $planetOut);
        }
        $planetOutData = array();
        foreach ($planetOut as $key => $line) {
            $planetOutData[$key] = explode(',', str_replace(' ', '', $line));
        }

        $this->planetOut = $planetOutData;

    }
    public function Natal()
    {
        $stars_cout = count($this->starsArray);
        $house_data = array();

        $planet_data = array();
        foreach ($this->planetOut as $key => $line) {
            if ($key >= $stars_cout and $key < ($stars_cout + 12)) {
                $house_info = $this->Convert_Longitude($line[1]);
                $house_info['longitude'] = $line[1];
                $house_data[$key - $stars_cout] = $house_info;
            } else {
                $planet_info['longitude'] = $line[1];
                $planet_info['longitude_array'] = $this->Convert_Longitude($line[1]);

                if($key >= $stars_cout){
                    $line[2]=360;
                }


                if(!empty( $line[2])){
                    $planet_info['speed'] = $line[2];
                }

                if (!empty($line[3])) {
                    $planet_info['latitude'] = $line[3];
                } else {
                    $planet_info['latitude'] = [];
                }
                if ($key < (count($this->planetOut) - 6)) {
                    $planet_data[] = $planet_info;
                }


            }

        };

        //计算福点位置
        if ($this->planetOut[$stars_cout][1] > $this->planetOut[$stars_cout + 6][1]) {
            if ($this->planetOut[0][1] <= $this->planetOut[$stars_cout][1] And $this->planetOut[0][1] > $this->planetOut[$stars_cout + 6][1]) {
                $day_chart = True;
            } else {
                $day_chart = False;
            }
        } else {
            if ($this->planetOut[0][1] > $this->planetOut[$stars_cout][1] And $this->planetOut[0][1] <= $this->planetOut[$stars_cout + 6][1]) {
                $day_chart = False;
            } else {
                $day_chart = True;
            }
        }
        if ($day_chart == True) {
            $planet_info['longitude'] = $this->planetOut[$stars_cout][1] + $this->planetOut[1][1] - $this->planetOut[0][1];
            $planet_info['speed'] = 360;
            // $planet_info['latitude'] = $this->planetOut[$stars_cout][2] + $this->planetOut[1][2] - $this->planetOut[0][2];
        } else {
            $planet_info['longitude'] = $this->planetOut[$stars_cout][1] - $this->planetOut[1][1] + $this->planetOut[0][1];
            $planet_info['speed'] = 360;
            //  $planet_info['latitude'] = $this->planetOut[$stars_cout][2] - $this->planetOut[1][2] + $this->planetOut[0][2];
        }

        if ($planet_info['longitude'] >= 360) {
            $planet_info['longitude'] = $planet_info['longitude'] - 360;
            $planet_info['speed'] = 360;
            //$planet_info['latitude'] = $planet_info['latitude'] - 360;
        }

        if ($planet_info['longitude'] < 0) {
            $planet_info['longitude'] = $planet_info['longitude'] + 360;
            $planet_info['speed'] = 360;
            // $planet_info['latitude'] = $planet_info['latitude'] + 360;
        }


        $planet_info['longitude_array'] = $this->Convert_Longitude($planet_info['longitude']);

        $planet_data[] = $planet_info;

        $this->starsArray = array_merge($this->starsArray, ['10', '11', 'pFortune']);

        foreach ($planet_data as $key => &$vale) {
            $vale['code_name'] = $this->starsArray[$key];
            $vale['chinese_name'] = planetName::$planetChinese[$this->starsArray[$key]];
            $vale['english_name'] = planetName::$planetEnglish[$this->starsArray[$key]];
        }
        $data['planet'] = $planet_data;
        $data['stars_count'] = $this->starsArray;

        $data['planet_chinese'] = planetName::$planetChinese;

        $data['planet_english'] = planetName::$planetEnglish;

        $data['house'] = $house_data;

        return $data;

//        $longitude1[LAST_PLANET] = $longitude1[LAST_PLANET + 16];        //Asc = +13, MC = +14, RAMC = +15, Vertex = +16

    }

    function Convert_Longitude($longitude)
    {

        $signs = planetName::$signsEnglish;

        $sign_num = floor($longitude / 30);
        $pos_in_sign = $longitude - ($sign_num * 30);
        $deg = floor($pos_in_sign);
        $full_min = ($pos_in_sign - $deg) * 60;
        $min = floor($full_min);
        $full_sec = round(($full_min - $min) * 60);

        if ($deg < 10) {
            $deg = "0" . $deg;
        }

        if ($min < 10) {
            $min = "0" . $min;
        }

        if ($full_sec < 10) {
            $full_sec = "0" . $full_sec;
        }

        return ['deg' => $deg, 'min' => $min, 'full_sec' => $full_sec, 'sign_num' => $sign_num];
    }

    function DecToLat($dec_lat)
    {
        if ($dec_lat < 0)
            $dir = 'S';
        else
            $dir = 'N';
        $dec_lat = abs($dec_lat);
        $lat_deg = (int)$dec_lat;
        $dec_lat -= $lat_deg;
        $dec_lat = $dec_lat * 60.0 + .5;
        $lat_min = (int)$dec_lat;
        $txtLat['lat_deg'] = $lat_deg;
        $txtLat['dir'] = $dir;
        $txtLat['lat_min'] = $lat_min;
        // $txtLat = sprintf("%d%s%02d", $lat_deg, $dir, $lat_min);
        return $txtLat;
    }

    function basis()
    {
        return $this->planetOut;
    }

    function impactHouse($longitude)
    {
        for ($x = 1; $x <= 12; $x++) {
            for ($y = 0; $y <= LAST_PLANET; $y++) {
                $pl = $longitude1[$y] + (1 / 36000);
                if ($x < 12 And $longitude1[$x + LAST_PLANET] > $longitude1[$x + LAST_PLANET + 1]) {
                    If (($pl >= $longitude1[$x + LAST_PLANET] And $pl < 360) Or ($pl < $longitude1[$x + LAST_PLANET + 1] And $pl >= 0)) {
                        $house_pos1[$y] = $x;
                        continue;
                    }
                }

                if ($x == 12 And ($longitude1[$x + LAST_PLANET] > $longitude1[LAST_PLANET + 1])) {
                    if (($pl >= $longitude1[$x + LAST_PLANET] And $pl < 360) Or ($pl < $longitude1[LAST_PLANET + 1] And $pl >= 0)) {
                        $house_pos1[$y] = $x;
                    }
                    continue;
                }

                if (($pl >= $longitude1[$x + LAST_PLANET]) And ($pl < $longitude1[$x + LAST_PLANET + 1]) And ($x < 12)) {
                    $house_pos1[$y] = $x;
                    continue;
                }

                if (($pl >= $longitude1[$x + LAST_PLANET]) And ($pl < $longitude1[LAST_PLANET + 1]) And ($x == 12)) {
                    $house_pos1[$y] = $x;
                }
            }
        }
    }
}


