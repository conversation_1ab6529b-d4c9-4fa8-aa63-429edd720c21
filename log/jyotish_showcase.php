<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jyotish 占星学案例展示中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            text-decoration: none;
        }
        
        .nav {
            display: flex;
            gap: 30px;
        }
        
        .nav a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav a:hover {
            color: #667eea;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .hero {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 60px 40px;
            border-radius: 20px;
            margin-bottom: 50px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        
        .hero h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero p {
            font-size: 1.3em;
            color: #666;
            max-width: 800px;
            margin: 0 auto 30px;
            line-height: 1.6;
        }
        
        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            color: #333;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            text-align: left;
            margin: 20px 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 50px 40px;
            border-radius: 20px;
            margin: 50px 0;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        
        .stat-item {
            padding: 20px;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            color: #666;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 50px 40px;
            border-radius: 20px;
            margin: 50px 0;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        
        .demo-item {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .demo-item:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }
        
        .demo-item h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .demo-item p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            margin-top: 50px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .footer h3 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .footer p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #764ba2;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5em;
            }
            
            .hero p {
                font-size: 1.1em;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .nav {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="#" class="logo">🌟 Jyotish</a>
            <nav class="nav">
                <a href="#features">功能特色</a>
                <a href="#demos">案例演示</a>
                <a href="#stats">统计信息</a>
                <a href="#contact">联系我们</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <section class="hero">
            <h1>🌟 Jyotish 占星学案例中心</h1>
            <p>
                探索印度占星学的奥秘，体验最先进的占星计算和图表生成技术。
                我们提供完整的 Jyotish 编程案例，从基础概念到高级应用，助您掌握占星学编程的精髓。
            </p>
            <div class="hero-buttons">
                <a href="simple_jyotish_examples.php" class="btn">🚀 开始探索</a>
                <a href="correct_natal_chart.php" class="btn secondary">🎨 查看图表</a>
                <a href="jyotish_summary.php" class="btn secondary">📋 案例总结</a>
            </div>
        </section>

        <section id="features" class="features-grid">
            <div class="feature-card" onclick="location.href='simple_jyotish_examples.php?example=base'">
                <span class="feature-icon">🏗️</span>
                <h3>Base 基础模块</h3>
                <p>提供核心的数据结构、常量定义和基础功能类，是整个系统的基石。</p>
                <ul class="feature-list">
                    <li>Data 基础数据类</li>
                    <li>Constants 常量定义</li>
                    <li>Utils 工具函数</li>
                    <li>Traits 特性类</li>
                </ul>
                <a href="simple_jyotish_examples.php?example=base" class="btn">查看详情</a>
            </div>

            <div class="feature-card" onclick="location.href='simple_jyotish_examples.php?example=calendar'">
                <span class="feature-icon">📅</span>
                <h3>Calendar 日历系统</h3>
                <p>处理各种日历系统的转换和计算，支持公历、儒略历、印度历等。</p>
                <ul class="feature-list">
                    <li>Gregorian 公历处理</li>
                    <li>Hindu 印度历计算</li>
                    <li>Panchanga 五要素</li>
                    <li>日历转换算法</li>
                </ul>
                <a href="simple_jyotish_examples.php?example=calendar" class="btn">查看详情</a>
            </div>

            <div class="feature-card" onclick="location.href='simple_jyotish_examples.php?example=draw'">
                <span class="feature-icon">🎨</span>
                <h3>Draw 图形绘制</h3>
                <p>生成各种占星图表和可视化图形，支持多种图表样式和自定义选项。</p>
                <ul class="feature-list">
                    <li>Chart 星盘绘制</li>
                    <li>Renderer 渲染器</li>
                    <li>Style 样式设置</li>
                    <li>多种图表格式</li>
                </ul>
                <a href="simple_jyotish_examples.php?example=draw" class="btn">查看详情</a>
            </div>

            <div class="feature-card" onclick="location.href='correct_natal_chart.php'">
                <span class="feature-icon">🌙</span>
                <h3>占星图表演示</h3>
                <p>查看精确复制的印度占星学图表，完全按照传统格式绘制。</p>
                <ul class="feature-list">
                    <li>传统12宫位布局</li>
                    <li>精确的行星位置</li>
                    <li>完整的出生数据</li>
                    <li>专业的图表样式</li>
                </ul>
                <a href="correct_natal_chart.php" class="btn">查看图表</a>
            </div>

            <div class="feature-card" onclick="location.href='custom_natal_chart.php'">
                <span class="feature-icon">🧘</span>
                <h3>自定义图表</h3>
                <p>创建您自己的占星图表，支持自定义出生数据和行星位置。</p>
                <ul class="feature-list">
                    <li>自定义出生信息</li>
                    <li>可调整行星位置</li>
                    <li>实时图表生成</li>
                    <li>多种参数设置</li>
                </ul>
                <a href="custom_natal_chart.php" class="btn">自定义图表</a>
            </div>

            <div class="feature-card" onclick="location.href='test_fix.php'">
                <span class="feature-icon">⏰</span>
                <h3>功能测试</h3>
                <p>测试所有功能模块，确保系统正常运行和图表生成质量。</p>
                <ul class="feature-list">
                    <li>渲染器测试</li>
                    <li>图表生成测试</li>
                    <li>错误修复验证</li>
                    <li>性能测试</li>
                </ul>
                <a href="test_fix.php" class="btn">运行测试</a>
            </div>
        </section>

        <section id="stats" class="stats-section">
            <h2 style="font-size: 2.5em; margin-bottom: 20px; color: #333;">📊 项目统计</h2>
            <p style="font-size: 1.2em; color: #666; margin-bottom: 30px;">
                我们的 Jyotish 案例库包含了丰富的功能和完整的代码示例
            </p>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">9</div>
                    <div class="stat-label">核心模块</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">代码示例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">函数方法</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">∞</div>
                    <div class="stat-label">应用可能</div>
                </div>
            </div>
        </section>

        <section id="demos" class="demo-section">
            <h2 style="font-size: 2.5em; margin-bottom: 20px; color: #333;">🎯 实时演示</h2>
            <p style="font-size: 1.2em; color: #666; margin-bottom: 30px;">
                体验我们的占星图表生成和分析功能
            </p>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>🌟 精确占星图表</h4>
                    <p>查看完全按照传统印度占星学格式绘制的图表，内部正方形角点正确靠边。</p>
                    <div style="text-align: center; margin: 20px 0;">
                        <img src="correct_natal_chart.php" alt="占星图表" style="max-width: 100%; border: 2px solid #ddd; border-radius: 8px;">
                    </div>
                    <a href="correct_natal_chart.php" class="btn">查看大图</a>
                </div>

                <div class="demo-item">
                    <h4>🎨 自定义图表生成</h4>
                    <p>输入您的出生信息，生成个性化的占星图表，支持实时参数调整。</p>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>自定义出生日期和时间</li>
                        <li>设置地理位置坐标</li>
                        <li>调整行星位置参数</li>
                        <li>选择不同的图表样式</li>
                    </ul>
                    <a href="custom_natal_chart.php" class="btn">开始自定义</a>
                </div>

                <div class="demo-item">
                    <h4>🧪 功能测试中心</h4>
                    <p>全面测试系统的各项功能，确保图表生成的准确性和稳定性。</p>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>渲染器功能测试</li>
                        <li>图表绘制验证</li>
                        <li>错误修复检查</li>
                        <li>性能基准测试</li>
                    </ul>
                    <a href="test_fix.php" class="btn">运行测试</a>
                </div>

                <div class="demo-item">
                    <h4>📚 完整案例库</h4>
                    <p>浏览所有模块的详细代码示例和使用说明，学习占星学编程。</p>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>Base 基础模块案例</li>
                        <li>Calendar 日历系统</li>
                        <li>Draw 图形绘制</li>
                        <li>Panchanga 五要素</li>
                    </ul>
                    <a href="simple_jyotish_examples.php" class="btn">浏览案例</a>
                </div>

                <div class="demo-item">
                    <h4>📋 项目总结</h4>
                    <p>查看完整的项目概览，包括所有模块的功能介绍和使用指南。</p>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>模块功能总览</li>
                        <li>技术架构说明</li>
                        <li>使用指南文档</li>
                        <li>最佳实践建议</li>
                    </ul>
                    <a href="jyotish_summary.php" class="btn">查看总结</a>
                </div>

                <div class="demo-item">
                    <h4>🔧 调试工具</h4>
                    <p>使用我们的调试和对比工具，分析图表生成过程和结果差异。</p>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>图表对比分析</li>
                        <li>布局修正验证</li>
                        <li>错误诊断工具</li>
                        <li>性能监控面板</li>
                    </ul>
                    <a href="debug_comparison.php" class="btn">使用工具</a>
                </div>
            </div>
        </section>

        <section class="demo-section">
            <h2 style="font-size: 2.5em; margin-bottom: 20px; color: #333;">🚀 快速开始</h2>
            <p style="font-size: 1.2em; color: #666; margin-bottom: 30px;">
                选择您感兴趣的功能，立即开始探索 Jyotish 占星学的世界
            </p>
            <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap; margin: 30px 0;">
                <a href="simple_jyotish_examples.php" class="btn" style="font-size: 1.1em; padding: 18px 35px;">
                    📚 学习案例
                </a>
                <a href="correct_natal_chart.php" class="btn secondary" style="font-size: 1.1em; padding: 18px 35px;">
                    🌟 查看图表
                </a>
                <a href="custom_natal_chart.php" class="btn secondary" style="font-size: 1.1em; padding: 18px 35px;">
                    🎨 自定义图表
                </a>
                <a href="test_fix.php" class="btn secondary" style="font-size: 1.1em; padding: 18px 35px;">
                    🧪 运行测试
                </a>
            </div>
        </section>

        <footer id="contact" class="footer">
            <h3>🌟 感谢您的关注</h3>
            <p>
                Jyotish 占星学案例中心致力于提供最专业、最完整的印度占星学编程解决方案。
                我们希望这些案例能够帮助您深入理解占星学的奥秘，掌握相关的编程技能。
            </p>
            <div class="footer-links">
                <a href="simple_jyotish_examples.php">完整案例</a>
                <a href="jyotish_summary.php">项目总结</a>
                <a href="correct_natal_chart.php">占星图表</a>
                <a href="test_fix.php">功能测试</a>
                <a href="debug_comparison.php">调试工具</a>
            </div>
            <p style="margin-top: 30px; color: #999; font-size: 0.9em;">
                © 2024 Jyotish 案例中心 | 专业的印度占星学编程平台
            </p>
        </footer>
    </div>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 添加滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有卡片元素
        document.querySelectorAll('.feature-card, .demo-item, .stat-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // 添加点击统计
        document.querySelectorAll('.btn, .feature-card').forEach(element => {
            element.addEventListener('click', function() {
                console.log('用户点击了:', this.textContent.trim());
            });
        });
    </script>
</body>
</html>
