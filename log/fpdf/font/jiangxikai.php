<?php
$type = 'TrueType';
$name = 'jiangxizhuokai-Regular';
$desc = array('Ascent'=>766,'Descent'=>-238,'CapHeight'=>700,'Flags'=>32,'FontBBox'=>'[-41 -219 1274 821]','ItalicAngle'=>0,'StemV'=>70,'MissingWidth'=>500);
$up = -75;
$ut = 50;
$cw = array(
	chr(0)=>500,chr(1)=>500,chr(2)=>500,chr(3)=>500,chr(4)=>500,chr(5)=>500,chr(6)=>500,chr(7)=>500,chr(8)=>500,chr(9)=>500,chr(10)=>500,chr(11)=>500,chr(12)=>500,chr(13)=>500,chr(14)=>500,chr(15)=>500,chr(16)=>500,chr(17)=>500,chr(18)=>500,chr(19)=>500,chr(20)=>500,chr(21)=>500,
	chr(22)=>500,chr(23)=>500,chr(24)=>500,chr(25)=>500,chr(26)=>500,chr(27)=>500,chr(28)=>500,chr(29)=>500,chr(30)=>500,chr(31)=>500,' '=>350,'!'=>247,'"'=>240,'#'=>600,'$'=>600,'%'=>600,'&'=>600,'\''=>137,'('=>325,')'=>325,'*'=>382,'+'=>600,
	','=>225,'-'=>456,'.'=>228,'/'=>354,'0'=>650,'1'=>300,'2'=>600,'3'=>600,'4'=>686,'5'=>628,'6'=>650,'7'=>656,'8'=>650,'9'=>600,':'=>237,';'=>243,'<'=>600,'='=>600,'>'=>600,'?'=>500,'@'=>600,'A'=>700,
	'B'=>621,'C'=>613,'D'=>650,'E'=>650,'F'=>572,'G'=>650,'H'=>553,'I'=>450,'J'=>450,'K'=>650,'L'=>630,'M'=>650,'N'=>650,'O'=>700,'P'=>650,'Q'=>750,'R'=>650,'S'=>650,'T'=>650,'U'=>650,'V'=>650,'W'=>800,
	'X'=>620,'Y'=>570,'Z'=>600,'['=>338,'\\'=>324,']'=>338,'^'=>500,'_'=>429,'`'=>500,'a'=>450,'b'=>513,'c'=>450,'d'=>459,'e'=>450,'f'=>469,'g'=>494,'h'=>450,'i'=>210,'j'=>265,'k'=>417,'l'=>194,'m'=>620,
	'n'=>450,'o'=>470,'p'=>462,'q'=>446,'r'=>349,'s'=>418,'t'=>450,'u'=>468,'v'=>473,'w'=>669,'x'=>477,'y'=>433,'z'=>520,'{'=>299,'|'=>500,'}'=>309,'~'=>600,chr(127)=>500,chr(128)=>600,chr(129)=>500,chr(130)=>159,chr(131)=>500,
	chr(132)=>182,chr(133)=>600,chr(134)=>500,chr(135)=>500,chr(136)=>500,chr(137)=>600,chr(138)=>500,chr(139)=>372,chr(140)=>500,chr(141)=>500,chr(142)=>500,chr(143)=>500,chr(144)=>500,chr(145)=>169,chr(146)=>160,chr(147)=>300,chr(148)=>298,chr(149)=>256,chr(150)=>448,chr(151)=>691,chr(152)=>500,chr(153)=>1300,
	chr(154)=>500,chr(155)=>372,chr(156)=>500,chr(157)=>500,chr(158)=>500,chr(159)=>500,chr(160)=>500,chr(161)=>237,chr(162)=>600,chr(163)=>600,chr(164)=>500,chr(165)=>600,chr(166)=>500,chr(167)=>500,chr(168)=>500,chr(169)=>600,chr(170)=>500,chr(171)=>544,chr(172)=>500,chr(173)=>500,chr(174)=>600,chr(175)=>500,
	chr(176)=>600,chr(177)=>600,chr(178)=>500,chr(179)=>500,chr(180)=>500,chr(181)=>500,chr(182)=>500,chr(183)=>184,chr(184)=>500,chr(185)=>500,chr(186)=>500,chr(187)=>542,chr(188)=>500,chr(189)=>500,chr(190)=>500,chr(191)=>460,chr(192)=>500,chr(193)=>500,chr(194)=>500,chr(195)=>500,chr(196)=>500,chr(197)=>500,
	chr(198)=>500,chr(199)=>500,chr(200)=>500,chr(201)=>500,chr(202)=>500,chr(203)=>500,chr(204)=>500,chr(205)=>500,chr(206)=>500,chr(207)=>500,chr(208)=>500,chr(209)=>500,chr(210)=>500,chr(211)=>500,chr(212)=>500,chr(213)=>500,chr(214)=>500,chr(215)=>600,chr(216)=>500,chr(217)=>500,chr(218)=>500,chr(219)=>500,
	chr(220)=>500,chr(221)=>500,chr(222)=>500,chr(223)=>500,chr(224)=>500,chr(225)=>500,chr(226)=>500,chr(227)=>500,chr(228)=>500,chr(229)=>500,chr(230)=>500,chr(231)=>500,chr(232)=>500,chr(233)=>500,chr(234)=>500,chr(235)=>500,chr(236)=>500,chr(237)=>500,chr(238)=>500,chr(239)=>500,chr(240)=>500,chr(241)=>500,
	chr(242)=>500,chr(243)=>500,chr(244)=>500,chr(245)=>500,chr(246)=>500,chr(247)=>600,chr(248)=>500,chr(249)=>500,chr(250)=>500,chr(251)=>500,chr(252)=>600,chr(253)=>500,chr(254)=>500,chr(255)=>500);
$enc = 'cp1252';
$uv = array(0=>array(0,128),128=>8364,130=>8218,131=>402,132=>8222,133=>8230,134=>array(8224,2),136=>710,137=>8240,138=>352,139=>8249,140=>338,142=>381,145=>array(8216,2),147=>array(8220,2),149=>8226,150=>array(8211,2),152=>732,153=>8482,154=>353,155=>8250,156=>339,158=>382,159=>376,160=>array(160,96));
$file = 'jiangxikai.z';
$originalsize = 48552;
$subsetted = true;
?>
