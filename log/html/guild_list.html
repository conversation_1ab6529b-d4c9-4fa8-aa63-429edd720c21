<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>自动刷新页面</title>
    <script src="guild_channel.json"></script>
    <script src="article.json"></script>
    <script>

        var user_list_port = ['5700'];
		message="[CQ:text,text=这是一条测试帖子。]"; 
const encodedMessage = encodeURIComponent(message);
console.log('http://127.0.0.1:5700/send_discuss_msg?guild_id=84475101671168490&channel_id=39026299&content=' + encodedMessage);
        fetch('http://127.0.0.1:5700/send_discuss_msg?guild_id=84475101671168490&channel_id=39026299&content=' + encodedMessage)
                .then(response => {
if (response.ok) {
      return response.json();
    } else {
      throw new Error('获取频道列表失败');
    }
                })
                .then(data => {
 console.log('频道列表:', data);
                })
                .catch(error => {
				 console.error('发生错误:', error);
                })
      
    </script>


</head>
<body>
</body>
</html>
