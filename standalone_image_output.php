<?php
/**
 * 独立渲染器图像输出测试
 */

// 包含独立渲染器
require_once 'standalone_image_renderer.php';

use <PERSON><PERSON><PERSON><PERSON>\Draw\Renderer\StandaloneDraw;

try {
    // 创建 400x300 像素的图像
    $draw = new StandaloneDraw(400, 300);
    
    // 设置选项 - 现在完全不依赖外部类！
    $draw->setOptions([
        'fontColor' => '#000080',
        'strokeColor' => '#FF0000',
        'strokeWidth' => 2,
        'projectName' => '独立渲染器成功!',
        'version' => '1.0',
        'status' => 'working',
        'theme' => 'success'
    ]);
    
    // 第二批选项
    $draw->setOptions([
        'textAlign' => 'center',
        'debugMode' => true,
        'author' => 'Standalone Renderer'
    ]);
    
    // 获取渲染器以访问存储的选项
    $renderer = $draw->getRenderer();
    
    // 获取存储的变量
    $projectName = $renderer->getStoredOption('projectName', 'Unknown');
    $version = $renderer->getStoredOption('version', '0.0');
    $status = $renderer->getStoredOption('status', 'unknown');
    $author = $renderer->getStoredOption('author', 'Unknown');
    
    // 绘制标题
    $draw->drawText($projectName, 200, 40, [
        'fontColor' => '#000080',
        'textAlign' => 'center'
    ]);
    
    // 绘制版本和状态信息
    $draw->drawText("版本: " . $version . " | 状态: " . $status, 200, 70, [
        'fontColor' => '#666666',
        'textAlign' => 'center'
    ]);
    
    // 绘制作者信息
    $draw->drawText("作者: " . $author, 200, 95, [
        'fontColor' => '#666666',
        'textAlign' => 'center'
    ]);
    
    // 绘制成功标志（对勾）
    $checkPoints = [
        150, 130,  // 起点
        180, 160,  // 中点
        250, 110   // 终点
    ];
    $draw->drawPolygon($checkPoints, [
        'strokeColor' => '#00CC00',
        'strokeWidth' => 4
    ]);
    
    // 绘制说明文字
    $draw->drawText("不依赖外部类，完全独立运行!", 200, 190, [
        'fontColor' => '#00CC00',
        'textAlign' => 'center'
    ]);
    
    // 绘制功能展示矩形
    $rectanglePoints = [
        50, 210,   // 左上
        350, 210,  // 右上
        350, 260,  // 右下
        50, 260    // 左下
    ];
    $draw->drawPolygon($rectanglePoints, [
        'strokeColor' => '#0066CC',
        'strokeWidth' => 2
    ]);
    
    // 显示存储的选项数量
    $allOptions = $renderer->getStoredOptions();
    $optionCount = count($allOptions);
    
    $draw->drawText("成功存储了 " . $optionCount . " 个选项变量", 200, 235, [
        'fontColor' => '#0066CC',
        'textAlign' => 'center'
    ]);
    
    // 绘制庆祝图形（星形）
    $starPoints = [
        200, 280,  // 顶点
        210, 300,  // 右上
        230, 300,  // 右
        215, 315,  // 右下
        220, 335,  // 右底
        200, 325,  // 底
        180, 335,  // 左底
        185, 315,  // 左下
        170, 300,  // 左
        190, 300   // 左上
    ];
    $draw->drawPolygon($starPoints, [
        'strokeColor' => '#FF8800',
        'strokeWidth' => 2
    ]);
    
    // 显示调试信息
    if ($renderer->getStoredOption('debugMode', false)) {
        $draw->drawText("调试: 无外部依赖", 50, 360, [
            'fontColor' => '#999999'
        ]);
    }
    
    // 添加解决方案说明
    $draw->drawText("解决方案: 内联 Utility 功能", 50, 380, [
        'fontColor' => '#999999'
    ]);
    
    // 添加时间戳
    $draw->drawText("生成时间: " . date('H:i:s'), 350, 380, [
        'fontColor' => '#999999',
        'textAlign' => 'right'
    ]);
    
    // 渲染并输出图像
    $draw->render();
    
} catch (Exception $e) {
    // 如果出错，输出错误信息
    header('Content-Type: text/plain');
    echo "生成图像时发生错误: " . $e->getMessage() . "\n";
    echo "错误详情:\n" . $e->getTraceAsString();
}
?>
