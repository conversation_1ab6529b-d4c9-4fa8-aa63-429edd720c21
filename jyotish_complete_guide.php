<?php
/**
 * <PERSON><PERSON><PERSON>h Draw 完整使用指南和案例演示
 * 
 * 这个文件展示了如何请求和使用 Jyotish Draw 的所有方法
 */

// 包含独立渲染器
require_once 'standalone_image_renderer.php';
use Jyotish\Draw\Renderer\StandaloneDraw;

// 处理不同的请求
$action = $_GET['action'] ?? 'guide';

switch ($action) {
    case 'basic_demo':
        generateBasicDemo();
        break;
    case 'advanced_demo':
        generateAdvancedDemo();
        break;
    case 'chart_demo':
        generateChartDemo();
        break;
    case 'text_demo':
        generateTextDemo();
        break;
    case 'shapes_demo':
        generateShapesDemo();
        break;
    case 'variables_demo':
        generateVariablesDemo();
        break;
    default:
        showGuide();
        break;
}

/**
 * 显示完整使用指南
 */
function showGuide() {
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jyotish Draw 完整使用指南</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 20px 0;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .demo-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .demo-card h3 {
            color: #2d3748;
            margin-bottom: 10px;
        }
        .demo-card p {
            color: #4a5568;
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        .method-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .method-table th,
        .method-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .method-table th {
            background: #667eea;
            color: white;
        }
        .method-table tr:hover {
            background: #f7fafc;
        }
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .preview-item {
            text-align: center;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
        }
        .preview-item img {
            max-width: 100%;
            border: 2px solid #e2e8f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Jyotish Draw 完整使用指南</h1>
            <p>掌握 PHP 图形绘制的强大功能</p>
        </div>

        <!-- 快速开始 -->
        <div class="section">
            <h2>🚀 快速开始</h2>
            <div class="code-block">
&lt;?php
// 1. 包含渲染器
require_once 'standalone_image_renderer.php';
use Jyotish\Draw\Renderer\StandaloneDraw;

// 2. 创建画布
$draw = new StandaloneDraw(400, 300);

// 3. 设置选项
$draw->setOptions([
    'fontColor' => '#000000',
    'strokeColor' => '#FF0000',
    'strokeWidth' => 2
]);

// 4. 绘制内容
$draw->drawText('Hello World!', 200, 50, ['textAlign' => 'center']);

// 5. 输出图像
$draw->render();
?&gt;
            </div>
        </div>

        <!-- 所有可用方法 -->
        <div class="section">
            <h2>📋 所有可用方法</h2>
            <table class="method-table">
                <thead>
                    <tr>
                        <th>方法名</th>
                        <th>参数</th>
                        <th>描述</th>
                        <th>返回值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>__construct()</code></td>
                        <td>$width, $height, $renderer</td>
                        <td>创建指定尺寸的绘图画布</td>
                        <td>StandaloneDraw 实例</td>
                    </tr>
                    <tr>
                        <td><code>setOptions()</code></td>
                        <td>array $options</td>
                        <td>设置绘图选项和自定义变量</td>
                        <td>$this (链式调用)</td>
                    </tr>
                    <tr>
                        <td><code>drawText()</code></td>
                        <td>$text, $x, $y, $options</td>
                        <td>在指定位置绘制文本</td>
                        <td>void</td>
                    </tr>
                    <tr>
                        <td><code>drawPolygon()</code></td>
                        <td>array $points, $options</td>
                        <td>绘制多边形图形</td>
                        <td>void</td>
                    </tr>
                    <tr>
                        <td><code>getRenderer()</code></td>
                        <td>无</td>
                        <td>获取底层渲染器对象</td>
                        <td>StandaloneImage 实例</td>
                    </tr>
                    <tr>
                        <td><code>getStoredOptions()</code></td>
                        <td>无</td>
                        <td>获取所有存储的选项</td>
                        <td>array</td>
                    </tr>
                    <tr>
                        <td><code>getStoredOption()</code></td>
                        <td>$name, $default</td>
                        <td>获取特定选项值</td>
                        <td>mixed</td>
                    </tr>
                    <tr>
                        <td><code>render()</code></td>
                        <td>无</td>
                        <td>渲染并输出最终图像</td>
                        <td>void</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 演示案例 -->
        <div class="section">
            <h2>🎯 演示案例</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>🔰 基础演示</h3>
                    <p>展示基本的文本和图形绘制功能</p>
                    <a href="?action=basic_demo" target="_blank" class="btn">查看演示</a>
                </div>
                
                <div class="demo-card">
                    <h3>⚡ 高级功能</h3>
                    <p>展示复杂图形和高级选项设置</p>
                    <a href="?action=advanced_demo" target="_blank" class="btn">查看演示</a>
                </div>
                
                <div class="demo-card">
                    <h3>📊 图表绘制</h3>
                    <p>创建简单的数据可视化图表</p>
                    <a href="?action=chart_demo" target="_blank" class="btn">查看演示</a>
                </div>
                
                <div class="demo-card">
                    <h3>✏️ 文本处理</h3>
                    <p>展示各种文本对齐和样式选项</p>
                    <a href="?action=text_demo" target="_blank" class="btn">查看演示</a>
                </div>
                
                <div class="demo-card">
                    <h3>🔷 图形绘制</h3>
                    <p>绘制各种几何图形和复杂形状</p>
                    <a href="?action=shapes_demo" target="_blank" class="btn">查看演示</a>
                </div>
                
                <div class="demo-card">
                    <h3>🎛️ 变量管理</h3>
                    <p>展示自定义变量存储和使用</p>
                    <a href="?action=variables_demo" target="_blank" class="btn">查看演示</a>
                </div>
            </div>
        </div>

        <!-- 实时预览 -->
        <div class="section">
            <h2>🖼️ 实时预览</h2>
            <div class="preview-grid">
                <div class="preview-item">
                    <h4>基础演示</h4>
                    <img src="?action=basic_demo" alt="基础演示">
                </div>
                <div class="preview-item">
                    <h4>高级功能</h4>
                    <img src="?action=advanced_demo" alt="高级功能">
                </div>
                <div class="preview-item">
                    <h4>图表绘制</h4>
                    <img src="?action=chart_demo" alt="图表绘制">
                </div>
                <div class="preview-item">
                    <h4>文本处理</h4>
                    <img src="?action=text_demo" alt="文本处理">
                </div>
                <div class="preview-item">
                    <h4>图形绘制</h4>
                    <img src="?action=shapes_demo" alt="图形绘制">
                </div>
                <div class="preview-item">
                    <h4>变量管理</h4>
                    <img src="?action=variables_demo" alt="变量管理">
                </div>
            </div>
        </div>

        <!-- 常用选项 -->
        <div class="section">
            <h2>⚙️ 常用选项参数</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>🎨 颜色选项</h3>
                    <ul>
                        <li><code>fontColor</code> - 字体颜色</li>
                        <li><code>strokeColor</code> - 描边颜色</li>
                        <li><code>fillColor</code> - 填充颜色</li>
                    </ul>
                </div>
                
                <div class="demo-card">
                    <h3>✏️ 文本选项</h3>
                    <ul>
                        <li><code>textAlign</code> - 水平对齐 (left/center/right)</li>
                        <li><code>textValign</code> - 垂直对齐 (top/middle/bottom)</li>
                        <li><code>fontSize</code> - 字体大小</li>
                    </ul>
                </div>
                
                <div class="demo-card">
                    <h3>🔷 图形选项</h3>
                    <ul>
                        <li><code>strokeWidth</code> - 描边宽度</li>
                        <li><code>strokeColor</code> - 描边颜色</li>
                        <li><code>fillColor</code> - 填充颜色</li>
                    </ul>
                </div>
                
                <div class="demo-card">
                    <h3>🎛️ 自定义变量</h3>
                    <ul>
                        <li>任意键值对</li>
                        <li>支持字符串、数字、布尔值</li>
                        <li>可用于条件逻辑</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 最佳实践 -->
        <div class="section">
            <h2>💡 最佳实践</h2>
            <div class="code-block">
// 1. 错误处理
try {
    $draw = new StandaloneDraw(400, 300);
    // ... 绘图代码
    $draw->render();
} catch (Exception $e) {
    error_log("绘图错误: " . $e->getMessage());
    // 显示默认图像或错误信息
}

// 2. 性能优化
$draw->setOptions([
    'fontColor' => '#000000',
    'strokeColor' => '#FF0000'
]); // 一次性设置多个选项

// 3. 代码组织
function createChart($data, $options = []) {
    $draw = new StandaloneDraw(400, 300);
    $draw->setOptions($options);
    // ... 图表绘制逻辑
    return $draw;
}

// 4. 变量管理
$config = [
    'theme' => 'blue',
    'showLabels' => true,
    'title' => 'Sales Report'
];
$draw->setOptions($config);
            </div>
        </div>
    </div>
</body>
</html>
<?php
}

/**
 * 生成基础演示图像
 */
function generateBasicDemo() {
    try {
        $draw = new StandaloneDraw(400, 300);
        
        // 设置基础选项
        $draw->setOptions([
            'title' => '基础演示',
            'fontColor' => '#2D3748',
            'strokeColor' => '#E53E3E'
        ]);
        
        // 绘制标题
        $draw->drawText('基础演示', 200, 30, [
            'textAlign' => 'center',
            'fontColor' => '#2D3748'
        ]);
        
        // 绘制简单文本
        $draw->drawText('Hello Jyotish Draw!', 200, 70, [
            'textAlign' => 'center',
            'fontColor' => '#4A5568'
        ]);
        
        // 绘制三角形
        $triangle = [200, 100, 150, 180, 250, 180];
        $draw->drawPolygon($triangle, [
            'strokeColor' => '#E53E3E',
            'strokeWidth' => 2
        ]);
        
        // 绘制矩形
        $rectangle = [100, 200, 300, 200, 300, 250, 100, 250];
        $draw->drawPolygon($rectangle, [
            'strokeColor' => '#3182CE',
            'strokeWidth' => 2
        ]);
        
        // 添加说明
        $draw->drawText('基础图形绘制', 200, 270, [
            'textAlign' => 'center',
            'fontColor' => '#718096'
        ]);
        
        $draw->render();
    } catch (Exception $e) {
        header('Content-Type: text/plain');
        echo "错误: " . $e->getMessage();
    }
}

/**
 * 生成高级演示图像
 */
function generateAdvancedDemo() {
    try {
        $draw = new StandaloneDraw(500, 400);
        
        // 设置高级选项
        $draw->setOptions([
            'title' => '高级功能演示',
            'theme' => 'professional',
            'showGrid' => true,
            'version' => '2.0'
        ]);
        
        $renderer = $draw->getRenderer();
        
        // 绘制标题
        $title = $renderer->getStoredOption('title');
        $draw->drawText($title, 250, 30, [
            'textAlign' => 'center',
            'fontColor' => '#2D3748'
        ]);
        
        // 绘制复杂图形 - 六边形
        $hexagon = [250, 80, 290, 100, 290, 140, 250, 160, 210, 140, 210, 100];
        $draw->drawPolygon($hexagon, [
            'strokeColor' => '#805AD5',
            'strokeWidth' => 3
        ]);
        
        // 绘制星形
        $star = [
            250, 180, 260, 210, 290, 210, 270, 230,
            280, 260, 250, 245, 220, 260, 230, 230,
            210, 210, 240, 210
        ];
        $draw->drawPolygon($star, [
            'strokeColor' => '#D69E2E',
            'strokeWidth' => 2
        ]);
        
        // 显示版本信息
        $version = $renderer->getStoredOption('version');
        $draw->drawText("版本: {$version}", 450, 380, [
            'textAlign' => 'right',
            'fontColor' => '#A0AEC0'
        ]);
        
        $draw->render();
    } catch (Exception $e) {
        header('Content-Type: text/plain');
        echo "错误: " . $e->getMessage();
    }
}

/**
 * 生成图表演示
 */
function generateChartDemo() {
    try {
        $draw = new StandaloneDraw(500, 350);

        // 设置图表配置
        $draw->setOptions([
            'chartTitle' => '销售数据图表',
            'chartType' => 'bar',
            'showValues' => true
        ]);

        // 绘制标题
        $draw->drawText('销售数据图表', 250, 30, [
            'textAlign' => 'center',
            'fontColor' => '#2D3748'
        ]);

        // 模拟数据
        $data = [30, 45, 60, 25, 80, 35];
        $labels = ['一月', '二月', '三月', '四月', '五月', '六月'];
        $maxValue = max($data);

        // 绘制柱状图
        $barWidth = 40;
        $barSpacing = 70;
        $startX = 60;
        $baseY = 280;

        foreach ($data as $index => $value) {
            $height = ($value / $maxValue) * 150;
            $x = $startX + ($index * $barSpacing);
            $y = $baseY - $height;

            // 绘制柱子
            $bar = [$x, $y, $x + $barWidth, $y, $x + $barWidth, $baseY, $x, $baseY];
            $draw->drawPolygon($bar, [
                'strokeColor' => '#3182CE',
                'strokeWidth' => 2
            ]);

            // 添加数值标签
            $draw->drawText($value, $x + $barWidth/2, $y - 15, [
                'textAlign' => 'center',
                'fontColor' => '#2D3748'
            ]);

            // 添加月份标签
            $draw->drawText($labels[$index], $x + $barWidth/2, $baseY + 20, [
                'textAlign' => 'center',
                'fontColor' => '#4A5568'
            ]);
        }

        // 绘制坐标轴
        $yAxis = [50, 80, 50, 280];
        $xAxis = [50, 280, 480, 280];
        $draw->drawPolygon($yAxis, ['strokeColor' => '#718096']);
        $draw->drawPolygon($xAxis, ['strokeColor' => '#718096']);

        $draw->render();
    } catch (Exception $e) {
        header('Content-Type: text/plain');
        echo "错误: " . $e->getMessage();
    }
}

/**
 * 生成文本演示
 */
function generateTextDemo() {
    try {
        $draw = new StandaloneDraw(450, 350);

        // 设置文本选项
        $draw->setOptions([
            'demoTitle' => '文本对齐演示',
            'showGuides' => true
        ]);

        // 绘制标题
        $draw->drawText('文本对齐演示', 225, 30, [
            'textAlign' => 'center',
            'fontColor' => '#2D3748'
        ]);

        // 绘制参考线
        $centerLine = [225, 60, 225, 320];
        $draw->drawPolygon($centerLine, [
            'strokeColor' => '#E2E8F0',
            'strokeWidth' => 1
        ]);

        // 左对齐文本
        $draw->drawText('左对齐文本 (left)', 225, 80, [
            'textAlign' => 'left',
            'fontColor' => '#E53E3E'
        ]);

        // 居中对齐文本
        $draw->drawText('居中对齐文本 (center)', 225, 120, [
            'textAlign' => 'center',
            'fontColor' => '#3182CE'
        ]);

        // 右对齐文本
        $draw->drawText('右对齐文本 (right)', 225, 160, [
            'textAlign' => 'right',
            'fontColor' => '#38A169'
        ]);

        // 不同颜色的文本
        $colors = ['#E53E3E', '#D69E2E', '#38A169', '#3182CE', '#805AD5'];
        $texts = ['红色文本', '黄色文本', '绿色文本', '蓝色文本', '紫色文本'];

        for ($i = 0; $i < count($texts); $i++) {
            $draw->drawText($texts[$i], 225, 200 + ($i * 25), [
                'textAlign' => 'center',
                'fontColor' => $colors[$i]
            ]);
        }

        $draw->render();
    } catch (Exception $e) {
        header('Content-Type: text/plain');
        echo "错误: " . $e->getMessage();
    }
}

/**
 * 生成图形演示
 */
function generateShapesDemo() {
    try {
        $draw = new StandaloneDraw(500, 400);

        // 设置图形选项
        $draw->setOptions([
            'title' => '几何图形演示',
            'strokeWidth' => 2
        ]);

        // 绘制标题
        $draw->drawText('几何图形演示', 250, 30, [
            'textAlign' => 'center',
            'fontColor' => '#2D3748'
        ]);

        // 三角形
        $triangle = [100, 80, 50, 150, 150, 150];
        $draw->drawPolygon($triangle, [
            'strokeColor' => '#E53E3E',
            'strokeWidth' => 3
        ]);
        $draw->drawText('三角形', 100, 170, [
            'textAlign' => 'center',
            'fontColor' => '#E53E3E'
        ]);

        // 矩形
        $rectangle = [200, 80, 300, 80, 300, 150, 200, 150];
        $draw->drawPolygon($rectangle, [
            'strokeColor' => '#3182CE',
            'strokeWidth' => 3
        ]);
        $draw->drawText('矩形', 250, 170, [
            'textAlign' => 'center',
            'fontColor' => '#3182CE'
        ]);

        // 六边形
        $hexagon = [400, 80, 430, 95, 430, 125, 400, 140, 370, 125, 370, 95];
        $draw->drawPolygon($hexagon, [
            'strokeColor' => '#38A169',
            'strokeWidth' => 3
        ]);
        $draw->drawText('六边形', 400, 170, [
            'textAlign' => 'center',
            'fontColor' => '#38A169'
        ]);

        // 菱形
        $diamond = [100, 220, 130, 250, 100, 280, 70, 250];
        $draw->drawPolygon($diamond, [
            'strokeColor' => '#D69E2E',
            'strokeWidth' => 3
        ]);
        $draw->drawText('菱形', 100, 300, [
            'textAlign' => 'center',
            'fontColor' => '#D69E2E'
        ]);

        // 八边形
        $octagon = [
            250, 220, 270, 220, 285, 235, 285, 255,
            270, 270, 250, 270, 235, 255, 235, 235
        ];
        $draw->drawPolygon($octagon, [
            'strokeColor' => '#805AD5',
            'strokeWidth' => 3
        ]);
        $draw->drawText('八边形', 260, 300, [
            'textAlign' => 'center',
            'fontColor' => '#805AD5'
        ]);

        // 复杂星形
        $star = [
            400, 220, 410, 240, 430, 240, 415, 255,
            420, 275, 400, 265, 380, 275, 385, 255,
            370, 240, 390, 240
        ];
        $draw->drawPolygon($star, [
            'strokeColor' => '#E53E3E',
            'strokeWidth' => 2
        ]);
        $draw->drawText('星形', 400, 300, [
            'textAlign' => 'center',
            'fontColor' => '#E53E3E'
        ]);

        $draw->render();
    } catch (Exception $e) {
        header('Content-Type: text/plain');
        echo "错误: " . $e->getMessage();
    }
}

/**
 * 生成变量管理演示
 */
function generateVariablesDemo() {
    try {
        $draw = new StandaloneDraw(500, 350);

        // 设置大量自定义变量
        $draw->setOptions([
            'projectName' => '变量管理系统',
            'version' => '3.1.4',
            'author' => 'Jyotish Team',
            'buildDate' => '2024-01-15',
            'environment' => 'production',
            'theme' => 'modern',
            'showDebug' => true,
            'maxUsers' => 1000,
            'features' => ['charts', 'export', 'api'],
            'isActive' => true
        ]);

        $renderer = $draw->getRenderer();

        // 获取并使用变量
        $projectName = $renderer->getStoredOption('projectName');
        $version = $renderer->getStoredOption('version');
        $author = $renderer->getStoredOption('author');
        $theme = $renderer->getStoredOption('theme');
        $showDebug = $renderer->getStoredOption('showDebug');

        // 绘制项目信息
        $draw->drawText($projectName, 250, 30, [
            'textAlign' => 'center',
            'fontColor' => '#2D3748'
        ]);

        $draw->drawText("版本: {$version}", 250, 60, [
            'textAlign' => 'center',
            'fontColor' => '#4A5568'
        ]);

        $draw->drawText("作者: {$author}", 250, 85, [
            'textAlign' => 'center',
            'fontColor' => '#4A5568'
        ]);

        // 根据主题设置颜色
        $themeColors = [
            'modern' => '#667eea',
            'classic' => '#2D3748',
            'vibrant' => '#E53E3E'
        ];
        $themeColor = $themeColors[$theme] ?? '#000000';

        // 绘制主题展示区域
        $themeBox = [100, 120, 400, 120, 400, 180, 100, 180];
        $draw->drawPolygon($themeBox, [
            'strokeColor' => $themeColor,
            'strokeWidth' => 3
        ]);

        $draw->drawText("当前主题: {$theme}", 250, 150, [
            'textAlign' => 'center',
            'fontColor' => $themeColor
        ]);

        // 显示存储的变量统计
        $allOptions = $renderer->getStoredOptions();
        $optionCount = count($allOptions);

        $draw->drawText("存储变量总数: {$optionCount}", 250, 210, [
            'textAlign' => 'center',
            'fontColor' => '#2D3748'
        ]);

        // 条件显示调试信息
        if ($showDebug) {
            $draw->drawText('调试模式: 开启', 50, 250, [
                'fontColor' => '#D69E2E'
            ]);

            $draw->drawText('环境: ' . $renderer->getStoredOption('environment'), 50, 275, [
                'fontColor' => '#4A5568'
            ]);

            $draw->drawText('最大用户数: ' . $renderer->getStoredOption('maxUsers'), 50, 300, [
                'fontColor' => '#4A5568'
            ]);
        }

        // 显示构建信息
        $buildDate = $renderer->getStoredOption('buildDate');
        $draw->drawText("构建日期: {$buildDate}", 450, 320, [
            'textAlign' => 'right',
            'fontColor' => '#A0AEC0'
        ]);

        $draw->render();
    } catch (Exception $e) {
        header('Content-Type: text/plain');
        echo "错误: " . $e->getMessage();
    }
}
?>
