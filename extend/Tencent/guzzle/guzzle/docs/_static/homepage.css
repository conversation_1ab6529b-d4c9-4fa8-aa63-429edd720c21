/* Hero unit on homepage */

.hero-unit h1 {
  font-size: 49px;
  margin-bottom: 12px;
}

.hero-unit {
  padding: 40px;
}

.hero-unit p {
  font-size: 17px;
}

.masthead img {
  float: left;
  margin-right: 17px;
}

.hero-unit ul li {
  margin-left: 220px;
}

.hero-unit .buttons {
  text-align: center;
}

.jumbotron {
  position: relative;
  padding: 40px 0;
  color: #fff;
  text-shadow: 0 1px 3px rgba(0,0,0,.4), 0 0 30px rgba(0,0,0,.075);
  background: #00312F;
  background: -moz-linear-gradient(45deg, #002F31 0%, #335A6D 100%);
  background: -webkit-gradient(linear, left bottom, right top, color-stop(0%,#00312D), color-stop(100%,#33566D));
  background: -webkit-linear-gradient(45deg, #020031 0%,#334F6D 100%);
  background: -o-linear-gradient(45deg, #002D31 0%,#334D6D 100%);
  background: -ms-linear-gradient(45deg, #002F31 0%,#33516D 100%);
  background: linear-gradient(45deg, #020031 0%,#33516D 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#020031', endColorstr='#6d3353',GradientType=1 );
  -webkit-box-shadow: inset 0 3px 7px rgba(0, 0, 0, .2), inset 0 -3px 7px rgba(0, 0, 0, .2);
  -moz-box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
  box-shadow: inset 0 3px 7px rgba(0, 0, 0, .2), inset 0 -3px 7px rgba(0, 0, 0, .2);
}

.jumbotron h1 {
  font-size: 80px;
  font-weight: bold;
  letter-spacing: -1px;
  line-height: 1;
}

.jumbotron p {
  font-size: 24px;
  font-weight: 300;
  line-height: 1.25;
  margin-bottom: 30px;
}

.masthead {
  padding: 40px 0 30px;
  margin-bottom: 0;
  color: #fff;
  margin-top: -19px;
}

.masthead h1 {
  display: none;
}

.masthead p {
  font-size: 40px;
  font-weight: 200;
  line-height: 1.25;
  margin: 12px 0 0 0;
}

.masthead .btn {
  padding: 19px 24px;
  font-size: 24px;
  font-weight: 200;
  border: 0;
}

/* Social bar on homepage */

.social {
  padding: 2px 0;
  text-align: center;
  background-color: #f5f5f5;
  border-top: 1px solid #fff;
  border-bottom: 1px solid #ddd;
  margin: 0 0 20px 0;
}

.social ul {
  margin-top: 0;
}

.social-buttons {
  margin-left: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}

.social-buttons li {
  display: inline-block;
  padding: 5px 8px;
  line-height: 1;
  *display: inline;
  *zoom: 1;
}

.center-announcement {
  padding: 10px;
  background-color: rgb(238, 243, 255);
  border-radius: 8px;
  text-align: center;
  margin: 24px 0;
}
