<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeDeviceMonitorInfo返回参数结构体
 *
 * @method DeviceCpuInfo getCpu() 获取实例CPU监控数据
 * @method void setCpu(DeviceCpuInfo $Cpu) 设置实例CPU监控数据
 * @method DeviceMemInfo getMem() 获取实例内存监控数据
 * @method void setMem(DeviceMemInfo $Mem) 设置实例内存监控数据
 * @method DeviceNetInfo getNet() 获取实例网络监控数据
 * @method void setNet(DeviceNetInfo $Net) 设置实例网络监控数据
 * @method DeviceDiskInfo getDisk() 获取实例磁盘监控数据
 * @method void setDisk(DeviceDiskInfo $Disk) 设置实例磁盘监控数据
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeDeviceMonitorInfoResponse extends AbstractModel
{
    /**
     * @var DeviceCpuInfo 实例CPU监控数据
     */
    public $Cpu;

    /**
     * @var DeviceMemInfo 实例内存监控数据
     */
    public $Mem;

    /**
     * @var DeviceNetInfo 实例网络监控数据
     */
    public $Net;

    /**
     * @var DeviceDiskInfo 实例磁盘监控数据
     */
    public $Disk;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param DeviceCpuInfo $Cpu 实例CPU监控数据
     * @param DeviceMemInfo $Mem 实例内存监控数据
     * @param DeviceNetInfo $Net 实例网络监控数据
     * @param DeviceDiskInfo $Disk 实例磁盘监控数据
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Cpu",$param) and $param["Cpu"] !== null) {
            $this->Cpu = new DeviceCpuInfo();
            $this->Cpu->deserialize($param["Cpu"]);
        }

        if (array_key_exists("Mem",$param) and $param["Mem"] !== null) {
            $this->Mem = new DeviceMemInfo();
            $this->Mem->deserialize($param["Mem"]);
        }

        if (array_key_exists("Net",$param) and $param["Net"] !== null) {
            $this->Net = new DeviceNetInfo();
            $this->Net->deserialize($param["Net"]);
        }

        if (array_key_exists("Disk",$param) and $param["Disk"] !== null) {
            $this->Disk = new DeviceDiskInfo();
            $this->Disk->deserialize($param["Disk"]);
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
