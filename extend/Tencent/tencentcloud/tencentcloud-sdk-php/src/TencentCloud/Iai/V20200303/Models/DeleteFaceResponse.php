<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iai\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteFace返回参数结构体
 *
 * @method integer getSucDeletedNum() 获取删除成功的人脸数量
 * @method void setSucDeletedNum(integer $SucDeletedNum) 设置删除成功的人脸数量
 * @method array getSucFaceIds() 获取删除成功的人脸ID列表
 * @method void setSucFaceIds(array $SucFaceIds) 设置删除成功的人脸ID列表
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DeleteFaceResponse extends AbstractModel
{
    /**
     * @var integer 删除成功的人脸数量
     */
    public $SucDeletedNum;

    /**
     * @var array 删除成功的人脸ID列表
     */
    public $SucFaceIds;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param integer $SucDeletedNum 删除成功的人脸数量
     * @param array $SucFaceIds 删除成功的人脸ID列表
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SucDeletedNum",$param) and $param["SucDeletedNum"] !== null) {
            $this->SucDeletedNum = $param["SucDeletedNum"];
        }

        if (array_key_exists("SucFaceIds",$param) and $param["SucFaceIds"] !== null) {
            $this->SucFaceIds = $param["SucFaceIds"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
