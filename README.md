# Jyotish Draw 演示项目

这个项目演示了如何使用 Jyotish\Draw\Draw 类进行图形绘制。

## 🚀 快速开始

### 1. 文件结构

确保您的项目包含以下文件：

```
jyotish/
├── src/
│   ├── Base/
│   │   ├── Traits/
│   │   │   ├── OptionTrait.php
│   │   │   └── GetTrait.php
│   │   └── Utility.php
│   ├── Renderer/
│   │   ├── AbstractRenderer.php
│   │   ├── Image.php
│   │   └── Svg.php
│   └── Draw.php
├── fixed_demo.php          # 完整功能演示
├── image_test.php          # 图像输出测试
├── svg_test.php           # SVG 输出测试
└── README.md              # 本文件
```

### 2. 系统要求

- PHP 7.0 或更高版本
- GD 扩展（用于图像渲染）
- DOM 扩展（用于 SVG 渲染）

### 3. 运行演示

#### 方法一：命令行运行
```bash
php fixed_demo.php
```

#### 方法二：Web 浏览器访问
1. 将文件放在 Web 服务器目录下
2. 访问 `http://your-domain/fixed_demo.php`
3. 查看图像输出：`http://your-domain/image_test.php`
4. 查看 SVG 输出：`http://your-domain/svg_test.php`

## 📖 使用说明

### 基本用法

```php
<?php
// 包含必要文件
require_once 'jyotish/src/Base/Traits/OptionTrait.php';
require_once 'jyotish/src/Base/Traits/GetTrait.php';
require_once 'jyotish/src/Base/Utility.php';
require_once 'jyotish/src/Renderer/AbstractRenderer.php';
require_once 'jyotish/src/Renderer/Image.php';
require_once 'jyotish/src/Draw.php';

use Jyotish\Draw\Draw;

// 创建图像渲染器
$draw = new Draw(400, 300, Draw::RENDERER_IMAGE);

// 设置选项
$draw->setOptions([
    'fontColor' => '#000000',
    'strokeColor' => '#FF0000',
    'strokeWidth' => 2
]);

// 绘制文本
$draw->drawText('Hello World!', 200, 50, [
    'textAlign' => 'center',
    'fontSize' => 16
]);

// 绘制三角形
$trianglePoints = [200, 100, 150, 200, 250, 200];
$draw->drawPolygon($trianglePoints, ['strokeColor' => '#00FF00']);

// 输出图像
$draw->render();
?>
```

### 创建 SVG

```php
<?php
// 创建 SVG 渲染器
$svgDraw = new Draw(300, 200, Draw::RENDERER_SVG);

// 绘制内容
$svgDraw->drawText('SVG 示例', 150, 50, ['textAlign' => 'center']);

// 绘制矩形
$rectPoints = [50, 80, 250, 80, 250, 150, 50, 150];
$svgDraw->drawPolygon($rectPoints, [
    'strokeColor' => '#0000FF',
    'fillColor' => '#E0E0FF'
]);

// 输出 SVG
$svgDraw->render();
?>
```

## 🎨 功能特性

### 支持的渲染器
- **Image 渲染器**: 生成 PNG 图像
- **SVG 渲染器**: 生成矢量 SVG 图形

### 绘图功能
- **文本绘制**: 支持字体、颜色、对齐方式设置
- **多边形绘制**: 可绘制任意多边形（三角形、矩形、复杂形状）
- **选项配置**: 灵活的参数设置系统

### 可配置选项
- `fontColor`: 字体颜色（十六进制，如 '#FF0000'）
- `fontSize`: 字体大小
- `textAlign`: 文本对齐（'left', 'center', 'right'）
- `textValign`: 垂直对齐（'top', 'middle', 'bottom'）
- `strokeColor`: 描边颜色
- `strokeWidth`: 描边宽度
- `fillColor`: 填充颜色（仅 SVG）

## 🔧 故障排除

### 常见问题

1. **Fatal error: Trait not found**
   - 确保所有 trait 文件都已正确创建
   - 检查文件路径是否正确

2. **GD 扩展未找到**
   - 安装 PHP GD 扩展
   - 在 php.ini 中启用 `extension=gd`

3. **图像无法显示**
   - 确保在调用 `render()` 之前没有其他输出
   - 检查 HTTP 头是否正确设置

4. **颜色显示异常**
   - 使用正确的十六进制颜色格式（如 '#FF0000'）
   - 确保 Utility 类正确加载

### 调试技巧

1. 启用错误报告：
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

2. 检查文件是否存在：
```php
if (!file_exists('jyotish/src/Draw.php')) {
    echo "Draw.php 文件不存在";
}
```

3. 捕获异常：
```php
try {
    $draw = new Draw(400, 300);
    // ... 绘图代码
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
```

## 📝 更多示例

查看项目中的演示文件：
- `fixed_demo.php` - 完整功能演示
- `image_test.php` - 图像输出示例
- `svg_test.php` - SVG 输出示例

## 📄 许可证

GNU General Public License version 2 or later

## 🤝 贡献

欢迎提交问题和改进建议！
