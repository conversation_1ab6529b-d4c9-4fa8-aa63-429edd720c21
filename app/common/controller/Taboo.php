<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\controller;

/**
 * 禁忌控制器
 */
class Taboo extends AppBase
{

    /**
     * 禁忌列表
     */
    public function tabooList()
    {

        $where = $this->logicTaboo->getWhere($this->param_data);

        $data=$this->logicTaboo->getTabooList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 禁忌无分页列表
     */
    public function tabooColumn()
    {

        $data=$this->logicTaboo->getTabooColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 禁忌添加
     */
    public function tabooAdd()
    {
	  
	   $regit=$this->logicTaboo->tabooEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 禁忌删除
     */
    public function tabooDel()
    {

       $regit=$this->logicTaboo->tabooDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
