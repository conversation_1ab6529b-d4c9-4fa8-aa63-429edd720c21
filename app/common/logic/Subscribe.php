<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 预约列表逻辑
 */
class Subscribe extends LogicBase
{

      /**
       * 获取预约列表搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获预约列表单条信息
      */
     public function getSubscribeInfo($where = [], $field = '*')
     {

        return $this->modelSubscribe->getInfo($where, $field);
     }

    /**
     * 获取预约列表列表
     */

    public function getSubscribeList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelSubscribe->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取预约列表无分页列表
     */
    public function getSubscribeColumn($where = [], $field = '', $key = '')
    {
        return $this->modelSubscribe->getColumn($where, $field , $key);
    }

    /**
     * 预约列表单条编辑
     */
    public function subscribeEdit($data = [], $where = [])
    {
		
        $result = $this->modelSubscribe->setInfo($data,$where);
        
        return $result ? $result : $this->modelSubscribe->getError();
    }

    /**
     * 预约列表删除
     */
    public function subscribeDel($where = [], $is_true = false)
    {

        $result = $this->modelSubscribe->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelSubscribe->getError();
    }

    /**
     *会员更新
     */
    public function setSubscribeIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelSubscribe->setIncDecInfo($where, $field, $number, $setType);
    }
	//Admin模块操作
	/**
     * 获取预约列表搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];
        if(isset($data['status']) and $data['status']!='all'){
            $where['status'] = $data['status'];
        }else{
            $where['status']=['egt',-2];
        }
        $start_time=0;
        $end_time=9999999999;

        !empty($data['start_time']) && $start_time = strtotime($data['start_time']);
        !empty($data['end_time']) && $end_time = strtotime($data['end_time']);
        $where['create_time']=['between',[$start_time,$end_time]];

        return $where;
    }
	  
	/**
     * 预约列表单条编辑
     */
    public function subscribeAdminEdit($data = [])
    {


        $url = url('subscribeList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelSubscribe->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '预约列表' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '预约列表操作成功', $url] : [RESULT_ERROR, $this->modelSubscribe->getError()];
    }

    /**
     * 预约列表删除
     */
    public function subscribeAdminDel($where = [])
    {

        $result = $this->modelSubscribe->deleteInfo($where);
        
        $result && action_log('删除', '预约列表删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '预约列表删除成功'] : [RESULT_ERROR, $this->modelSubscribe->getError()];
    }
}
