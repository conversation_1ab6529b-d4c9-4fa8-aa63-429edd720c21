<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 收藏记录逻辑
 */
class LoveLog extends LogicBase
{

      /**
       * 获取收藏记录搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获收藏记录单条信息
      */
     public function getLoveLogInfo($where = [], $field = '*')
     {

        return $this->modelLoveLog->getInfo($where, $field);
     }

    /**
     * 获取收藏记录列表
     */

    public function getLoveLogList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelLoveLog->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取收藏记录无分页列表
     */
    public function getLoveLogColumn($where = [], $field = '', $key = '')
    {
        return $this->modelLoveLog->getColumn($where, $field , $key);
    }

    /**
     * 收藏记录单条编辑
     */
    public function loveLogEdit($data = [])
    {
		
        $result = $this->modelLoveLog->setInfo($data);
        
        return $result ? $result : $this->modelLoveLog->getError();
    }

    /**
     * 收藏记录删除
     */
    public function loveLogDel($where = [], $is_true = false)
    {

        $result = $this->modelLoveLog->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelLoveLog->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取收藏记录搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 收藏记录单条编辑
     */
    public function loveLogAdminEdit($data = [])
    {


        $url = url('loveLogList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelLoveLog->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '收藏记录' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '收藏记录操作成功', $url] : [RESULT_ERROR, $this->modelLoveLog->getError()];
    }

    /**
     * 收藏记录删除
     */
    public function loveLogAdminDel($where = [])
    {

        $result = $this->modelLoveLog->deleteInfo($where);
        
        $result && action_log('删除', '收藏记录删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '收藏记录删除成功'] : [RESULT_ERROR, $this->modelLoveLog->getError()];
    }
}
