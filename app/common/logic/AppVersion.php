<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * app版本逻辑
 */
class AppVersion extends LogicBase
{

      /**
       * 获取app版本搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获app版本单条信息
      */
     public function getAppVersionInfo($where = [], $field = '*')
     {

        return $this->modelAppVersion->getInfo($where, $field);
     }

    /**
     * 获取app版本列表
     */

    public function getAppVersionList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelAppVersion->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取app版本无分页列表
     */
    public function getAppVersionColumn($where = [], $field = '', $key = '')
    {
        return $this->modelAppVersion->getColumn($where, $field , $key);
    }

    /**
     * app版本单条编辑
     */
    public function appVersionEdit($data = [])
    {
		
        $result = $this->modelAppVersion->setInfo($data);
        
        return $result ? $result : $this->modelAppVersion->getError();
    }

    /**
     * app版本删除
     */
    public function appVersionDel($where = [], $is_true = false)
    {

        $result = $this->modelAppVersion->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelAppVersion->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取app版本搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * app版本单条编辑
     */
    public function appVersionAdminEdit($data = [])
    {


        $url = url('appVersionList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelAppVersion->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, 'app版本' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, 'app版本操作成功', $url] : [RESULT_ERROR, $this->modelAppVersion->getError()];
    }

    /**
     * app版本删除
     */
    public function appVersionAdminDel($where = [])
    {

        $result = $this->modelAppVersion->deleteInfo($where);
        
        $result && action_log('删除', 'app版本删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, 'app版本删除成功'] : [RESULT_ERROR, $this->modelAppVersion->getError()];
    }
}
