<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 *团队逻辑
 */
class WeiUserTeam extends LogicBase
{

    /**
     * 团队列表单条信息
     */
    public function getWeiUserTeamSingleInfo($param_data = [], $field = '*')
    {

        return $this->modelWeiUserTeam->getInfo($param_data, $field);
    }


    /**
     * 团队总数
     */
    public function getWeiUserTeamCount($where = [], $stat_type = 'count', $field = 'id')
    {

        return $this->modelWeiUserTeam->stat($where, $stat_type , $field );
    }


    /**
     * 团队列表单条编辑
     */
    public function weiUserTeamEdit($data = [], $where = [])
    {

        $result = $this->modelWeiUserTeam->setInfo($data, $where);

        return $result ? $result : $this->modelWeiUserTeam->getError();
    }

    /**
     * 团队列表批量设置
     */
    public function setListUserTeam($data = [], $replace = false)
    {
        $this->modelWeiUserTeam->setList($data, $replace);
    }
    /**
     * 获取团队列表无分页列表
     */
    public function getWeiUserTeamColumn($where = [], $field = '', $key = '')
    {
        return $this->modelWeiUserTeam->getColumn($where, $field , $key);
    }

    /**
     * 获取预约列表
     */

    public function getWeiUserTeamList($where = [], $field = '', $order = '', $paginate = 0,$group=null)
    {
        !empty($group) && $this->modelWeiUserTeam->group=$group;

        return $this->modelWeiUserTeam->getList($where, $field, $order, $paginate);
    }

}
