<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 微信appid配置
 */
class WeiAppid extends LogicBase
{
    /**
     * 获取微信appid配置列表搜索条件
     */
    public function getWhere($data = [])
    {
        $where = [];

        !empty($data['search_data']) && $where['name|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
    /**
     * 获取微信appid配置列表
     */
    public function getWeiAppidList($where = [], $field = true, $order = '', $paginate = '')
    {

        return $this->modelWeiAppid->getList($where, $field, $order, $paginate);
    }

    /**
     * 微信appid配置编辑
     */
    public function weiAppidEdit($data = [])
    {

        $validate_result = $this->validateWeiAppid->scene('edit')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateWeiAppid->getError()]; endif;

        $url = url('weiAppidList');

        $data['token'] = data_md5_key($data['appid'].time(),'token');

        if(!empty($data['is_encoding'])){
            $data['encoding_aes_key']=data_md5_key($data['appid'].time(), 'EncodingAESKey');
        }

        unset($data['is_encoding']);

        $result = $this->modelWeiAppid->setInfo($data);

        $handle_text = empty($data['id']) ? '新增' : '编辑';

        $result && action_log($handle_text, '微信appid配置' . $handle_text . '，data：' .http_build_query($data));


        return $result ? [RESULT_SUCCESS, '微信appid配置编辑成功', $url] : [RESULT_ERROR, $this->modelWeiAppid->getError()];
    }

    /**
     * 微信appid配置删除
     */
    public function weiAppidDel($where = [])
    {

        $result = $this->modelWeiAppid->deleteInfo($where);

        $result && action_log('删除', '删除微信appid配置，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '微信appid配置删除成功'] : [RESULT_ERROR, $this->modelWeiAppid->getError()];
    }

    /**
     * 获取微信appid配置信息
     */
    public function getGroupInfo($where = [], $field = true)
    {

        return $this->modelWeiAppid->getInfo($where, $field);
    }
}
