<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 消息发送记录逻辑
 */
class WeiMessageLog extends LogicBase
{

      /**
       * 获取消息发送记录搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获消息发送记录单条信息
      */
     public function getWeiMessageLogInfo($where = [], $field = '*')
     {

        return $this->modelWeiMessageLog->getInfo($where, $field);
     }

    /**
     * 获取消息发送记录列表
     */

    public function getWeiMessageLogList($where = [], $field = '', $order = '')
    {
        return $this->modelWeiMessageLog->getList($where, $field, $order);
    }

    /**
     * 获取消息发送记录无分页列表
     */
    public function getWeiMessageLogColumn($where = [], $field = '', $key = '')
    {
        return $this->modelWeiMessageLog->getColumn($where, $field , $key);
    }

    /**
     * 消息发送记录单条编辑
     */
    public function weiMessageLogEdit($data = [])
    {


        $url = url('weiMessageLogList');
        
        $data['member_id'] = MEMBER_ID;

        $result = $this->modelWeiMessageLog->setInfo($data);
        
        $handle_text = empty($data['id']) ? '消息发送记录' : '编辑';
        
        $result && action_log($handle_text, '消息发送记录' . $handle_text . '，id：' . $data['id']);
        
        return $result ? [RESULT_SUCCESS, '消息发送记录修改成功', $url] : [RESULT_ERROR, $this->modelWeiMessageLog->getError()];
    }
    /**
     * 消息发送记录新增
     */
    public function weiMessageLogAdd($data = [])
    {
        $url = url('weiMessageLogList');

        $data['member_id'] = MEMBER_ID;

        $where['id']=$data['id'];
        $openid=$data['openid'];

        unset($data['id']);
        unset($data['openid']);
        $logicWechatMessageSend=get_sington_object('logicWechatMessageSend', "app\\common\\logic\\WechatMessageSend",getAccessToken()['access_token']);
        foreach ($openid as $value){
            $reig=$logicWechatMessageSend->templateBaseSend($where,$value,$data);
        }

        $reig['errcode']==0 && action_log('新增', '消息发送记录新增' . '，title：' . $reig['msgid']);

        return $reig['errcode']==0 ? [RESULT_SUCCESS, '消息发送记录新增成功', $url] : [RESULT_ERROR, $this->modelWeiMessageLog->getError()];
    }

    /**
     * 消息发送记录删除
     */
    public function weiMessageLogDel($where = [])
    {

        $result = $this->modelWeiMessageLog->deleteInfo($where);
        
        $result && action_log('删除', '消息发送记录删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '消息发送记录删除成功'] : [RESULT_ERROR, $this->modelWeiMessageLog->getError()];
    }
}
