<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Document</title>
	<script src="__COMMON__/jquery/jquery-2.2.3.min.js"></script>
	<link href="__COMMON__/astrology/css/chart.css" rel="stylesheet" type="text/css">
	<link href="__COMMON__/astrology/css/font-xp-gryph.css" rel="stylesheet" type="text/css">
	
	<style>
 
		@media screen and (min-width:680px ) {
			.box {
				width: 600px;
				height: 600px;
				margin: 0 auto;
			}
		}
		@media screen and (max-width:680px ) {
			.box {
				width: 100%;
				height: 100%;
				margin: 0 auto;
			}
		}
		
		text {
			text-anchor: middle;
			dominant-baseline: middle;
			cursor: pointer;
		}
		
		.text_font {
			font-family: 'web_ixingpan_cn';
		}
		
		.must_symbo_font {
			font-family: 'web_ixingpan';
		}
		
		/* ------------------------------ chartbody ------------------------------------ */
		
		#chartbody #zodiac {
			fill: #f8f8c1;
			stroke: #b58c00;
			stroke-width: 2;
		}
		
		#chartbody #zodiac_min {
			fill: #EFF;
			stroke: #b58c00;
			stroke-width: 2;
		}
		
		#chartbody #hcircle {
			fill: white;
			stroke: #2279ab;
			stroke-width: 1;
		}
		
		#chartbody #hcircle_min {
			fill: white;
			stroke: #2279ab;
			stroke-width: 1;
		}
		
		.origin {
			stroke: #505050;
			stroke-width: 0.8;
		}
		
		#chartbody .zodiac_grid {
			stroke: #b58d00;
			stroke-width: 2;
		}
		
		.origin:hover {
			stroke: #CCF;
		}
		
		
		/* ------------------------------ chartbody ------------------------------------ */
		/* ------------------------------ house ------------------------------------ */
		#chartbody .house_grid {
			stroke: #6699CC;
			fill: none;
			stroke-width: 0.5;
			stroke-dasharray: 2, 1;
		}
		
		#chartbody .house_dark_grid {
			stroke: #2279ab;
			stroke-width: 1;
		}
		
		#chartbody .house_id {
			font-size: 16px;
			stroke-width: 1;
		}
		
		#chartbody .house_id:hover {
			line-height: 1;
			font-size: 20px;
		}
		
		#chartbody .house_1,
		#chartbody .house_5,
		#chartbody .house_9 {
			fill: red;
		}
		
		#chartbody .house_2,
		#chartbody .house_6,
		#chartbody .house_10 {
			fill: #CC9933;
		}
		
		#chartbody .house_3,
		#chartbody .house_7,
		#chartbody .house_11 {
			fill: #006633;
		}
		
		#chartbody .house_4,
		#chartbody .house_8,
		#chartbody .house_12 {
			fill: #0A0AFF;
		}
		
		/* ------------------------------ house ------------------------------------ */
		/* ------------------------------ signs ------------------------------------ */
		
		.signs_font {
			
			font-size: 24px;
			
		}
		
		.signs_font:hover {
			font-size: 28px;
		}
		
		.Aries,
		.Leo,
		.Sagittarius {
			stroke: none;
			fill: red;
			color: red;
		}
		
		.Taurus,
		.Virgo,
		.Capricorn {
			stroke: none;
			fill: #CC9933;
			color: #CC9933;
		}
		
		.Gemini,
		.Libra,
		.Aquarius {
			stroke: none;
			fill: #006633;
			color: #006633;
		}
		
		.Cancer,
		.Scorpio,
		.Pisces {
			stroke: none;
			fill: #0A0AFF;
			color: #0A0AFF;
		}
		
		/* ------------------------------ signs ------------------------------------ */
		
		/* ------------------------------ planets ------------------------------------ */
		
		.guardian_font {
			font-size: 16px;
		}
		
		.guardian_font:hover {
			font-size: 20px;
		}
		
		.planet_font {
			
			font-size: 14px;
			line-height: 1;
			stroke: none;
		}
		
		.planet_font:hover {
			font-size: 16px;
		}
		
		.planets_Sun,
		.planets_Asc,
		.planets_Jupiter,
		.planets_Mars {
			fill: red;
			color: red;
			stroke: none;
		}
		
		.planets_Moon,
		.planets_IC,
		.planets_Neptune,
		.planets_Pluto {
			fill: #0A0AFF;
			color: #0A0AFF;
			stroke: none;
		}
		
		.planets_Saturn,
		.planets_Venus,
		.planets_MC {
			fill: #CC9933;
			color: #CC9933;
			stroke: none;
		}
		
		.planets_Mercury,
		.planets_Des,
		.planets_Uranus {
			stroke: none;
			fill: #006633;
			color: #006633;
		}
		
		.planets_Chiron,
		.planets_Pholus,
		.planets_Ceres,
		.planets_Pallas,
		.planets_Juno,
		.planets_Vesta,
		.planets_Psyche,
		.planets_Eros {
			fill: #FF33FF;
			color: #FF33FF;
			stroke: none;
		}
		
		.planets_Cupido,
		.planets_Hades,
		.planets_Zeus,
		.planets_Kronos,
		.planets_Apollon,
		.planets_Admetos,
		.planets_Vulkanus,
		.planets_Poseidon {
			fill: #AC00AC;
			color: #AC00AC;
			stroke: none;
		}
		
		.planets_OscuApogee,
		.planets_MeanApogee,
		.planets_TrueNode,
		.planets_MeanNode,
		.planets_TrueSouthNode,
		.planets_MeanSouthNode,
		.planets_EastPoint,
		.planets_Sun-Moon,
		.planets_PartOfFortun,
		.planets_Vertex {
			fill: #00B8B8;
			color: #00B8B8;
			stroke: none;
		}
		
		.planet_signs_line_0 line {
			stroke: #7f7f00;
			fill: #7f7f00;
		}
		
		.planet_signs_line_60 line {
			stroke: #00B8B8;
			fill: #00B8B8;
		}
		
		.planet_signs_line_90 line {
			stroke: red;
			fill: red;
		}
		
		.planet_signs_line_120 line {
			stroke: #006633;;
			fill: #006633;;
		}
		
		.planet_signs_line_180 line {
			stroke: blue;
			fill: blue;
		}
		
		.planets_circle:hover {
			r: 2;
		}
		
		.planet_signs > line:hover {
			stroke-width: 2 !important;
			stroke-dasharray: none !important;
			
		}
		
		/* --------------------------- planets ----------------------------------- */
	
		#tip_sign_add{
			position: absolute;
			z-index: 9999;
			padding: 10px;
			border: 1px solid #F1D031;
			background-color: #FFFFA3;
			color: #555;
			font-size: 14px;
			line-height: 2;
		}
	</style>
</head>
<body>
<div class='box'>
	{$sample_info.svg}
</div>
<div id="tip_sign_add">

</div>

</body>
</html>