<!-- saved from url=(0154)https://cloud.tanmarket.cn/?appId=wx3c477e0c6dece167&userName=gh_b106140e4ee5&schemeUrl=BVfmIb&corpid=wwd5b6ecbb882ca9e7&host=https://account.tanmarket.cn -->
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>打开小程序</title>

    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1">
    <!-- weui 样式 -->
    <link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/2.4.1/weui.min.css">
    </link>
    <!-- 调试用的移动端 console -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/eruda"></script> -->
    <!-- <script>eruda.init();</script> -->
    <!-- 公众号 JSSDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <!-- 云开发 Web SDK -->
    <script src="https://res.wx.qq.com/open/js/cloudbase/1.1.0/cloud.js"></script>
    <script>
        function getValue(href, myKey) {
            if (!href) {
                href = window.location.href
            }
            var regExp = new RegExp(`(?:&|\\?)${myKey}=([^&]*)(&|$)`);
            var result = null;
            var matchs = href.match(regExp);
            if (matchs) {
                result = matchs[1]
            }
            return result
        }

        // 获取APPID
        var CURRENT_HOST = location.hostname;
        var APP_ID = getValue(location.href, 'appId');
        var HOST = getValue(location.href, 'host');
        var SCHEME_URL = getValue(location.href, 'schemeUrl');
        var CORP_ID = getValue(location.href, 'corpid');
        var USER_NAME = getValue(location.href, 'userName');
        var QUERY = `schemeUrl=${SCHEME_URL}&corpid=${CORP_ID}&host=${HOST}`;
        var PATH = 'pages/drainageByLink/drainageByLink';

        // https://cloud-dev.tanmarket.cn/?appId=wx5ae2c190b7bc658a&userName=gh_2dc54e6da4c5&schemeUrl=7RVjEv&corpid=ww9c5530210a0d5116&host=https://dev.tanmarket.cn
        function docReady(fn) {
            if (document.readyState === 'complete' || document.readyState === 'interactive') {
                fn()
            } else {
                document.addEventListener('DOMContentLoaded', fn);
            }
        }

        docReady(async function () {
            var ua = navigator.userAgent.toLowerCase();
            var isWXWork = ua.match(/wxwork/i) == 'wxwork';
            var isWeixin = !isWXWork && ua.match(/micromessenger/i) == 'micromessenger';
            var isMobile = false;
            var isDesktop = false;
            if (navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|IEMobile)/i)) {
                isMobile = true
            } else {
                isDesktop = true
            }
            if (!(APP_ID && HOST && SCHEME_URL && CORP_ID)) {
                var errorEl = document.getElementById('error-param-container');
                errorEl.classList.remove('hidden')
            } else if (isDesktop) {
                // 在 pc 上则给提示引导到手机端打开
                var containerEl = document.getElementById('desktop-web-container');
                containerEl.classList.remove('hidden')
            } else if (isWeixin) {
                if (!USER_NAME) {
                    var errorEl = document.getElementById('error-param-container');
                    errorEl.classList.remove('hidden');
                    return
                }
                var containerEl = document.getElementById('public-web-container');
                containerEl.classList.remove('hidden');
                var launchBtn = document.getElementById('weixin-jump-button');
                launchBtn.classList.remove('hidden');

                launchBtn.innerHTML = `<wx-open-launch-weapp username="${USER_NAME}" path="${PATH}?${QUERY}">
          <template>
            <button style="width: 240px; height: 44px; text-align: center; font-size: 14px; display: block; margin: 0 auto; border: none; border-radius: 4px; background-color: #07c160; color:#fff; display: flex; align-items: center; justify-content: center;">点击立即前往微信</button>
          </template>
        </wx-open-launch-weapp>`;
                wx.config({
                    debug: false, // 调试时可开启
                    appId: APP_ID, // <!-- replace -->
                    timestamp: 0, // 必填，填任意数字即可
                    nonceStr: 'nonceStr', // 必填，填任意非空字符串即可
                    signature: 'signature', // 必填，填任意非空字符串即可
                    jsApiList: ['chooseImage'], // 必填，随意一个接口即可
                    openTagList: ['wx-open-launch-weapp'], // 填入打开小程序的开放标签名
                })
            } else {
                var containerEl = document.getElementById('public-web-container');
                containerEl.classList.remove('hidden');
                var buttonEl = document.getElementById('public-web-jump-button');
                buttonEl.classList.remove('hidden');

                var option = {
                    // 必填，表示是未登录模式
                    identityless: true,
                    // 资源方 AppID
                    resourceAppid: 'wx4e213c753eda2713',
                    // 资源方环境 ID
                    resourceEnv: 'blueenv-6gjiqd8o69537906',
                };
                if (CURRENT_HOST === 'cloud.tanmarket.cn') {
                    option = {
                        // 必填，表示是未登录模式
                        identityless: true,
                        // 资源方 AppID
                        resourceAppid: 'wx3d4cd66a53376f12',
                        // 资源方环境 ID
                        resourceEnv: 'prod-7g54nl3u4b7d170f',
                    }
                }

                var c = new cloud.Cloud(option);

                await c.init();
                window.c = c;

                var buttonLoadingEl = document.getElementById('public-web-jump-button-loading');
                try {
                    await openWeapp(() => {
                        buttonEl.classList.remove('weui-btn_loading');
                        buttonLoadingEl.classList.add('hidden')
                    })
                } catch (e) {
                    buttonEl.classList.remove('weui-btn_loading');
                    buttonLoadingEl.classList.add('hidden');
                    throw e
                }
            }
        });

        async function openWeapp(onBeforeJump) {
            var c = window.c;
            var res = await c.callFunction({
                name: 'public',
                data: {
                    action: 'getUrlScheme',
                    appId: APP_ID,
                    query: `schemeUrl=${SCHEME_URL}&corpid=${CORP_ID}&host=${HOST}`
                },
            });
            if (onBeforeJump) {
                onBeforeJump()
            }
            location.href = res.result.openlink
        }
    </script>
    <style>
        body {
            background: #F5F5F5;
        }

        .hidden {
            display: none !important;
        }

        .page {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .tips {
            width: 100%;
            font-size: 12px;
            font-weight: 400;
            color: #666666;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 20px;
            line-height: 20px;
            position: absolute;
            top: 12px;
        }

        .tips i {
            display: inline-block;
            width: 11px;
            height: 13px;
            background: url("./icon-protect.png") no-repeat;
            background-size: 11px 13px;
            margin-right: 5px;
            position: relative;
            top: -1px;
        }

        .tips span {
            color: #000;
            height: 20px;
            line-height: 20px;
        }

        #public-web-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            top: -8%;
        }

        #public-web-container .icon {
            width: 70px;
            height: 56px;
            background: url("./icon-wx.png") no-repeat;
            background-size: 70px 56px;
        }

        #public-web-container p {
            font-size: 14px;
            font-weight: 400;
            line-height: 28px;
            color: #666666;
            text-align: center;
            margin: 20px 0 34px;
        }

        #public-web-jump-button {
            width: 240px;
            height: 44px;
            font-size: 14px;
            font-weight: 500;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #public-web-jump-button-loading {
            margin-right: 5px;
        }

        #desktop-web-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #error-param-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    </style>
</head>

<body>
<div class="page">
    <div class="tips">
        <i></i>本链接经过<span>SSL安全加密</span>，请放心点击！
    </div>
    <div id="public-web-container" class="">
        <div class="icon"></div>
        <p>
            正在跳转中...
            <br>
            如未自动打开微信请点击下方按钮
        </p>
        <a id="public-web-jump-button" href="javascript:" class="weui-btn weui-btn_primary" onclick="openWeapp()">
            <span id="public-web-jump-button-loading"
                  class="weui-primary-loading weui-primary-loading_transparent hidden"><i
                    class="weui-primary-loading__dot"></i></span>
            点击立即前往微信
        </a>
        <div class="hidden" id="weixin-jump-button"></div>
    </div>
    <div id="desktop-web-container" class="hidden">
        <p class="">请在手机打开网页链接</p>
    </div>
    <div id="error-param-container" class="hidden">
        <p class="">参数错误</p>
    </div>
</div>


</body>
</html>
