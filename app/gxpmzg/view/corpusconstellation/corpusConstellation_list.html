<div class="box">
	<div class="box-header">
		<div class="row search-form">
			<div class="col-sm-2">
				<ob_link><a class="btn btn-primary" href="{:url('corpusConstellationAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
			</div>
			<div class="col-sm-2">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-primary" type="button">星盘</button>
						</span>
					<select class="form-control" name="chartType">
						<option value="all">所有</option>
						{notempty name='chartType'}
						{volist name='chartType' id='voc'}
						<option value="{$key}">{$voc}</option>
						{/volist}
						{/notempty}
					</select>
				</div>
			</div>

			<div class="col-sm-3">
				<div class="box-tools search-form pull-right">
					<div class="input-group input-group-sm">
						<input type="text" name="search_data" style="width: 200px;" class="form-control pull-right"
						       value="{:input('search_data')}" placeholder="支持行星|星座|关键字|内容">
						
						<div class="input-group-btn">
							<button type="button" id="search" url="{:url('corpusConstellationList')}"
							        class="btn btn-info btn-flat"><i class="fa fa-search"></i></button>
						</div>
					
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<th>id</th>
			<th>星盘</th>
			<th>类型</th>
			<th>对象1</th>
			<th>对象2</th>
			<th>条件</th>
			<th>关键字</th>
			<th style="width: 260px">内容</th>
			<th>相关</th>
			<th>操作</th>
			</thead>
			
			{notempty name='list'}
			<tbody>

			{volist name='list' id='vo'}
			<tr>
				<td>{$vo.id}</td>
				<td>{$chartType[$vo.chartType]}</td>
				<td>{$type[$vo.type]}</td>
				<td>{$vo.oneself}</td>
				<td>{$vo.other}</td>
				<td>{$vo.degree}</td>
				<td>{$vo.keywords}</td>
				<td><textarea style="width: 260px;height: 60px;" readonly>{$vo.content}</textarea></td>
				<td>{$vo.associated}</td>
				<td class="col-md-2 text-center">
					<ob_link><a href="{:url('corpusConstellationEdit', array('id' => $vo['id']))}" class="btn btn-xs btn-warning"><i
							class="fa fa-edit"></i> 编辑</a></ob_link>
					<ob_link><a class="btn btn-xs btn-danger confirm ajax-get"
					            href="{:url('corpusConstellationDel', array('id' => $vo['id']))}"><i
							class="fa fa-trash-o"></i>
						删除</a></ob_link>
				</td>
			</tr>
			{/volist}
			
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="7" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
		</table>
	</div>
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>
</div>
<script type="text/javascript">
	ob.setValue("chartType", "{$Think.get.chartType |default = 'all'}");
</script>