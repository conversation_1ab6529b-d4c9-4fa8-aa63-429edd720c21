<div class="box">
    <div class="box-header">
        <div class="row">
            <div class="col-sm-8">
                <div class="box-tools search-form pull-right">
                    <div class="input-group input-group-sm">

                        <input type="text" name="search_data" style="width: 200px;" class="form-control pull-right"
                               value="{:input('search_data')}" placeholder="支持标题">

                        <div class="input-group-btn">
                            <button type="button" id="search" url="{:url('userLabelList')}"
                                    class="btn btn-info btn-flat"><i class="fa fa-search"></i></button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="box-body table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
            <th>id</th>
            <th>标题</th>
            <th>用户id</th>
            <th>档案id</th>

            <th>创建时间</th>
            <th>操作</th>
            </thead>

            {notempty name='list'}
            <tbody>

            {volist name='list' id='vo'}
            <tr>
                <td>{$vo.id}</td>
                <td>{$vo.title}</td>
				<td><a href="{:url('user/userList', array('id' => $vo['user_id']))}" class="logic_anyes User" logicName="User" logicIndex="id" logicFile="nickname" logicId="{$vo.user_id}">{$vo.user_id}</a> </td>
				<td><a href="{:url('userarchives/userArchivesList', array('id' => $vo['archives_id']))}" class="logic_anyes userArchives" logicName="userArchives" logicIndex="id" logicFile="nickname" logicId="{$vo.archives_id}">{$vo.archives_id}</a> </td>

                <td>{$vo.create_time}</td>
                <td class="col-md-2 text-center">
                    <ob_link><a href="{:url('userLabelEdit', array('id' => $vo['id']))}" class="btn btn-xs btn-warning"><i
                            class="fa fa-edit"></i> 编辑</a></ob_link>
                    <ob_link><a class="btn btn-xs btn-danger confirm ajax-get"
                                href="{:url('userLabelAdminDel', array('id' => $vo['id']))}"><i class="fa fa-trash-o"></i>
                        删除</a></ob_link>
                </td>
            </tr>
            {/volist}

            </tbody>
            {else/}
            <tbody>
            <tr class="odd">
                <td colspan="7" class="text-center" valign="top">{:config('empty_list_describe')}</td>
            </tr>
            </tbody>
            {/notempty}
        </table>
    </div>
    <div class="box-footer clearfix text-center">
        {$list->render()}
    </div>
</div>
{include file="layout/special"/}