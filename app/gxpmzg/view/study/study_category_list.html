<div class="box">
  <div class="box-header search-form">

      <div class="col-sm-1">
          <ob_link><a href="{:url('studyCategoryAdd')}" class="btn btn-primary"><i class="fa fa-plus"></i> 新 增</a></ob_link>
      </div>
      <div class="col-xs-2">
          <div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-primary" type="button">分类</button>
						</span>
              <select class="form-control pid" name="pid">
                  <option value="0">不限</option>
                  {notempty name='study_category_list'}
                  {volist name='study_category_list' id='vo'}
                  <option value="{$vo.id}">{$vo.name}</option>
                  {/volist}
                  {else/}
                  {/notempty}
              </select>
          </div>
      </div>
      <div class="col-sm-6">
          <div class="box-tools pull-right">
              <div class="input-group input-group-sm">

                  <input type="text" name="search_data" style="width: 200px;" class="form-control pull-right"
                         value="{:input('search_data')}" placeholder="支持标题|描述">

                  <div class="input-group-btn">
                      <button type="button" id="search" url="{:url('studyCategoryList')}" class="btn btn-info btn-flat">
                          <i class="fa fa-search"></i></button>
                  </div>

              </div>
          </div>
      </div>
  </div>
  <div class="box-body table-responsive">
    <table  class="table table-bordered table-hover table-striped">
      <thead>
      <tr>
          <th>名称</th>
          <th class="sort-th">排序</th>
          <th>图标</th>
          <th>操作</th>
      </tr>
      </thead>
      
      {notempty name='list'}
        <tbody>
            {volist name='list' id='vo'}
                <tr>
                  <td>{$vo.name}</td>
                    <td><input type="text" class="sort-th sort-text" href="{:url('setCategorySort')}" id="{$vo.id}" value="{$vo.sort}" /></td>
                    <td><i class="fa {$vo.icon}"></i></td>
                  <td class="col-md-2 text-center">
                      <ob_link><a href="{:url('studyCategoryEdit', array('id' => $vo['id']))}" class="btn btn-xs btn-warning"><i class="fa fa-edit"></i> 编辑</a></ob_link>
                      <ob_link><a class="btn btn-xs btn-danger confirm ajax-get"  href="{:url('studyCategoryDel', array('id' => $vo['id']))}"><i class="fa fa-trash-o"></i> 删除</a></ob_link>
                  </td>
                </tr>
            {/volist}
        </tbody>
        {else/}
        <tbody><tr class="odd"><td colspan="4" class="text-center" valign="top">{:config('empty_list_describe')}</td></tr></tbody>
      {/notempty}
    </table>
  </div>
  <div class="box-footer clearfix text-center">
      {$list->render()}
  </div>
</div>
<script type="text/javascript">
    ob.setValue("pid", "{$Think.get.pid }");
</script>