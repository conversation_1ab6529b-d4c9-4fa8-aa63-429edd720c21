<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\gxpmzg\controller;

/**
 * 占星师控制器
 */
class Teacher extends AdminBase
{

    /**
     * 占星师列表
     */
    public function teacherList()
    {

        $where = $this->logicTeacher->getAdminWhere($this->param);
        if(isset($this->param['level']) and $this->param['level']!='all'){
            $where['level'] = $this->param['level'];
        }
        $this->assign('list', $dsffs=$this->logicTeacher->getTeacherList($where, '', 'sort desc'));

        $this->assign('teacher_level', config('ext_config')['teacher_level']);
        $this->assign('is_quickly',['未开启','开启']);

        return $this->fetch('teacher_list');
    }
    /**
     * 占星师列表
     */
    public function answerQuickly()
    {

        $where = $this->logicTeacher->getAdminWhere($this->param);
        $where['is_quickly']=1;
        $dsffs=$this->logicTeacher->getTeacherList($where, '', 'sort desc');

        foreach ($dsffs as $kesdfy=>&$dsffsdf){
            $dsffsdf['total_count']=$this->logicAnswerQuicklyLog->getAnswerQuicklyLogStat(['t_id'=>$dsffsdf['id']]);
            $dsffsdf['total_time']=$this->logicAnswerQuicklyLog->getAnswerQuicklyLogStat(['t_id'=>$dsffsdf['id']],'sum','chat_time');
        }

        $this->assign('list', $dsffs);


        $this->assign('teacher_level', config('ext_config')['teacher_level']);
        $this->assign('is_quickly',['未开启','开启']);

        return $this->fetch('teacher_answer_list');
    }

    /**
     * 占星师添加
     */
    public function teacherAdd()
    {
        IS_POST && $this->jump($this->logicTeacher->teacherAdminEdit($this->param));

        $this->assign('teacher_level', config('ext_config')['teacher_level']);

//        $labelAccessList=array();
//
//        if(!empty($data['id'])){
//
//            $labelAccessColumn=$this->labelAccessColumn($data['id']);
//
//            if(!empty($labelAccessColumn[$data['id']])){
//                $labelAccessList=$labelAccessColumn[$data['id']];
//            }
//        }
//        $this->assign('labelAccessColumn', $labelAccessList);


        return $this->fetch('teacher_edit');
    }

    /**
     * 标签关联属性
     */
    public function labelAccessColumn($t_id)
    {
        $labelAccessColumn = $this->logicArticleLabelAccess->getLabelAccessLabelList(['ala.article_id' =>['in', $t_id]]);

        $labelColumn=array();
        foreach ($labelAccessColumn as $key=>$value){
            $labelColumn[$value['article_id']][]=$value->toArray();
        }
        return $labelColumn;
    }


    /**
     * 占星师提现
     */
    public function answerWithdraw()
    {

        $id=$this->param['ids'];

        $sore=$this->param['score'];
        $this->logicTeacher->setTeacherIncDec(['id' => $id], 'answer_withdraw', $sore);
        $this->logicTeacher->setTeacherIncDec(['id' => $id], 'answer_amount', $sore,'setDec');

        $this->jump(['success', '提现成功', url('answerQuickly')]);
    }
    /**
     * 占星师编辑
     */
    public function teacherEdit()
    {
        IS_POST && $this->jump($this->logicTeacher->teacherAdminEdit($this->param));

        $info = $this->logicTeacher->getTeacherInfo(['id' => $this->param['id']], '*');

        $scheduleColumn = $this->logicSchedule->getScheduleColumn(['t_id' => $this->param['id'],'status'=>['gt',-2]], '*');

        $scheduleList=array();
        foreach ($scheduleColumn as $key=>$value){
            $scheduleList[$value['rows']][$value['week']]=$value;
        }
        $this->assign('info', $info);

        $this->assign('scheduleColumn', $scheduleList);

        $this->assign('teacher_level', config('ext_config')['teacher_level']);

        return $this->fetch('teacher_edit');
    }



    /**
     * 占星师删除
     */
    public function teacherDel($id = 0)
    {

        $this->jump($this->logicTeacher->teacherDel(['id' => $id]));
    }


    /**
     * 排序
     */
    public function setSort()
    {

        $this->jump($this->logicAdminBase->setSort('teacher', $this->param));
    }
    /**
     * 数据状态设置
     */
    public function setStatus()
    {

        $this->jump($this->logicAdminBase->setStatus('teacher', $this->param));
    }
    /**
     * 数据状态设置
     */
    public function setAllStatus()
    {

        $this->jump($this->logicAdminBase->setAllStatus('teacher', $this->param));
    }
}
