<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\gxpmzg\controller;

/**
 * 综合语料配置控制器
 */
class Corpusfield extends AdminBase
{

    /**
     * 综合语料配置列表
     */
    public function corpusFieldList()
    {

        $where = $this->logicCorpusField->getAdminWhere($this->param);

        $this->assign('list', $this->logicCorpusField->getCorpusFieldList($where, '', ''));

        return $this->fetch('corpusField_list');
    }

    /**
     * 综合语料配置添加
     */
    public function corpusFieldAdd()
    {
        IS_POST && $this->jump($this->logicCorpusField->corpusFieldAdminEdit($this->param));
        return $this->fetch('corpusField_edit');
    }

    /**
     * 综合语料配置编辑
     */
    public function corpusFieldEdit()
    {
        IS_POST && $this->jump($this->logicCorpusField->corpusFieldAdminEdit($this->param));

        $info = $this->logicCorpusField->getCorpusFieldInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('corpusField_edit');
    }


    /**
     * 综合语料配置删除
     */
    public function corpusFieldDel($id = 0)
    {

        $this->jump($this->logicCorpusField->corpusFieldDel(['id' => $id]));
    }

    /**
     * 数据状态设置
     */
     public function setStatus()
     {

         $this->jump($this->logicAdminBase->setStatus('CorpusField', $this->param));

     }

     /**
      * 排序
     */
     public function setSort()
     {

         $this->jump($this->logicAdminBase->setSort('CorpusField', $this->param));
     }
}
