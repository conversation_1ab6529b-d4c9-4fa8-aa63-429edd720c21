<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\gxpmzg\controller;

/**
 * app启动页控制器
 */
class Appstart extends AdminBase
{

    /**
     * app启动页列表
     */
    public function appStartList()
    {

        $where = $this->logicAppStart->getAdminWhere($this->param);

        $this->assign('list', $this->logicAppStart->getAppStartList($where, '', ''));

        return $this->fetch('appStart_list');
    }

    /**
     * app启动页添加
     */
    public function appStartAdd()
    {
        IS_POST && $this->jump($this->logicAppStart->appStartEdit($this->param));
        return $this->fetch('appStart_edit');
    }

    /**
     * app启动页编辑
     */
    public function appStartEdit()
    {
        IS_POST && $this->jump($this->logicAppStart->appStartEdit($this->param));

        $info = $this->logicAppStart->getAppStartInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('appStart_edit');
    }


    /**
     * app启动页删除
     */
    public function appStartDel($id = 0)
    {

        $this->jump($this->logicAppStart->appStartDel(['id' => $id]));
    }
}
