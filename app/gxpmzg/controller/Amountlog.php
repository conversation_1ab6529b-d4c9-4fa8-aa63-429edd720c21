<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\gxpmzg\controller;

/**
 * 金额记录控制器
 */
class Amountlog extends AdminBase
{

    /**
     * 金额记录列表
     */
    public function amountLogList()
    {

        $where = $this->logicAmountLog->getAdminWhere($this->param);


        if(!empty($this->param['start_time']) or !empty($this->param['end_time'])){
            $start_time = 0;
            $end_time = 9999999999;

            !empty($this->param['start_time']) && $start_time = strtotime($this->param['start_time']);
            !empty($this->param['end_time']) && $end_time = strtotime($this->param['end_time']);
            $where['create_time'] = ['between', [$start_time, $end_time]];
        }

        $this->assign('list',$fff= $this->logicAmountLog->getAmountLogList($where, '', 'id desc'));

        $this->assign('status',[-2=>'退款',0=>'充值中',1=>'已完成',2=>'已受理',-1=>'驳回']);
        $this->assign('pay_type',[0=>'未知',1=>'微信',2=>'支付宝',3=>'苹果']);

        $this->assign('trade_type',['0'=>'未知','APP'=>'APP','WEIWAP'=>'微信H5','WEICHAT'=>'微信小程序','H5'=>'H5']);

        $this->assign('paylogtype',config('ext_config.paylogtype'));

        return $this->fetch('amountLog_list');
    }
    /**
     * 积分记录删除
     */
    public function topUp()
    {
        $user_id=$this->param['user_id'];
        $score=$this->param['score'];

        $weiUserInfo = $this->logicUser->getUserInfo(['id' => $user_id], 'id,amount');

        $amountLog['order_number'] = 'houtaichongzhong';
        $amountLog['type'] = 6;
        $amountLog['user_id'] = $user_id;
        $amountLog['money'] = $score;
        $amountLog['real_amount'] = $score/10;
        $amountLog['new_amount'] =$score + $weiUserInfo['amount'];
        $amountLog['transaction_id'] = '00';
        $amountLog['remark'] = config('ext_config.paylogtype')[6];
        $amountLog['transaction_id'] = '00';
        $this->logicAmountLog->amountLogEdit($amountLog);

        $this->logicUser->setUserIncDec(['id' => $user_id], 'amount', $score, 'setInc');

       // $this->logicWechatMessageSend->templateTopUpSend(['openid'=>$weiUserInfo['openid'],'title'=>'余额','quota'=>$score]);

        $this->jump([RESULT_SUCCESS, '操作完成']);
    }
    /**
     * 微信退款
     */
    public function wxRefund($id = 0)
    {

        $amount_log=$this->logicAmountLog->getAmountLogInfo(['id'=>$id]);

        $userConfig = getAppidToken('gh_716df361132c');

        $exwechat = get_sington_object('payEx', "exwechat\\api\\pay\\wxPay", $userConfig);

        $refundData['out_refund_no'] = 'TU' . time() . 'W' . mt_rand(10, 99) . 'R' . mt_rand(100, 999) . 'D' . $id;
        $refundData['transaction_id'] = $amount_log['transaction_id'];
        $refundData['refund_fee'] =  $amount_log['money']* 100;
        $refundData['total_fee'] = $amount_log['money']* 100;

        $regit = $exwechat->wxRefund($refundData);

        if(($regit['return_code']=='SUCCESS') && ($regit['result_code']=='SUCCESS')){
            $this->logicAmountLog->amountLogEdit(['id'=>$amount_log['id'],'status'=>-2]);
            $this->logicUser->setUserIncDec(['id'=>$amount_log['user_id']], 'amount', $amount_log['money'],'setDec');

            $this->jump([RESULT_SUCCESS, '退款成功',  url('amountLogList')]);

        }else if(($regit['return_code']=='FAIL') || ($regit['result_code']=='FAIL')){
            //退款失败
            //原因
            $reason = (empty($regit['err_code_des'])?$regit['return_msg']:$regit['err_code_des']);
            $this->jump([RESULT_ERROR, $regit['err_code_des']]);
        }else{

        }
        $this->jump([RESULT_ERROR, '退款异常，联系开发']);

    }
    /**
     * 金额提现记录列表
     */
    public function amountLogWithdrawalList()
    {

         if(isset($this->param['status']) and $this->param['status']!='all'){
             $where['status'] = $this->param['status'];
         }else{
             $where['status']=['egt',-2];
         }
        !empty($this->param['search_data']) && $where['user_id']=$this->param['search_data'];

        $where['type']=3;

        $fsfsfs= $this->logicAmountLog->getAmountLogList($where, '', '');

        foreach ($fsfsfs as $key=>&$vl){
            $vl['PaymentCard']=$this->logicPaymentCard->getPaymentCardColumn(['user_id'=>$vl['user_id']]);
        }

        $this->assign('balance_withdrawal_fee',config('balance_withdrawal_fee'));

        $this->assign('list',$fsfsfs);

        $this->assign('status',[0=>'申请中',1=>'已完成',2=>'已受理',-1=>'驳回']);

        return $this->fetch('withdrawalLog_list');
    }

    /**
     * 金额提现审核
     */
    public function setStatus()
    {

        $this->jump($this->logicAmountLog->amountLogWithdrawaConfirm($this->param));
    }


    /**
     * 金额记录删除
     */
    public function amountLogDel($id = 0)
    {

        $this->jump($this->logicAmountLog->amountLogAdminDel(['id' => $id]));
    }
}
