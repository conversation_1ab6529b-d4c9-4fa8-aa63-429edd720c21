<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\app\controller;

/**
 * 预约列表控制器
 */
class Subscribe extends UserBase
{

    /**
     * 预约列表列表
     */
    public function subscribeList()
    {
        $this->logicSchedule->subscribeOverdue();  //清理过期事件

        $where = $this->logicSubscribe->getWhere($this->param_data);

        $data = $this->logicSubscribe->getSubscribeList($where, '', '');

        return $this->apiReturn($data);
    }

    /**
     * 预约列表无分页列表
     */
    public function subscribeColumn()
    {

        $data = $this->logicSubscribe->getSubscribeColumn($this->param_data);

        return $this->apiReturn($data);
    }

    /**
     * 预约列表添加
     */
    public function add()
    {
        $where["c_id"] = $this->param_data["c_id"];
        $where["t_id"] = $this->param_data["t_id"];
        $where["s_id"] = $this->param_data["s_id"];
        $where["status"] = 0;


        $where["type"] = 0;

        $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $where["t_id"], 'status' => 1], 'id,level,name,mobile');
        if (empty($teacherInfo)) {
            return $this->apiReturn(['code' => 3050003, 'msg' => '当前咨询师不存在']);
        }

        //查询项目
        $consultWhere['level'] = $teacherInfo["level"];
        if (!empty($this->param_data["type"])) {
            $consultWhere['type'] = $this->param_data["type"];
        } else {
            $consultWhere['id'] = $this->param_data["c_id"];
        }
        $consultInfo = $this->logicConsult->getConsultInfo($consultWhere, 'id,title,cover_id,type,price,working,unit');
        if (empty($consultInfo)) {
            return $this->apiReturn(['code' => 3050004, 'msg' => '当前咨询项目不存在']);
        }

        $where["c_id"] = $consultInfo["id"];

        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where, 'id');
        if (!empty($subscribeInfo)) {
            return $this->apiReturn(['code' => 3050002, 'msg' => '您已经预约此时间段']);
        }
        $where["a_id"] = $this->param_data["a_id"];
        if (!empty($this->param_data["mobile"])) {
            $where["mobile"] = $this->param_data["mobile"];
        }
        $where["user_id"] = $this->param_data["user_id"];
        if (!empty($this->param_data["h_id"])) {
            $where["help_id"] = $this->param_data["h_id"];
        }

        $userInfo = $this->logicUser->getUserInfo(['id' => $where["user_id"]], 'id,amount,t_id');

        if ($userInfo['t_id'] == $where["t_id"]) {
            return $this->apiReturn(['code' => 3050001, 'msg' => '暂不支持预约自己进行咨询哦！']);
        }

        if ($userInfo['amount'] < $consultInfo['price']) {
            return $this->apiReturn(['code' => 1030105, 'msg' => '您的星币不足，请去充值']);
        }

        //查询课节
        $scheduleInfo = $this->logicSchedule->getScheduleInfo(['id' => $where['s_id'], 'status' => 1, 't_id' => $where['t_id']]);
        if (empty($scheduleInfo)) {
            return $this->apiReturn(['code' => 3050006, 'msg' => '当前咨询课时不存在']);
        }
        if ($scheduleInfo['status'] == 2) {
            return $this->apiReturn(['code' => 3050007, 'msg' => '当前时间段已被预约']);
        }
        if (empty($scheduleInfo['time'])) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间段休息']);
        }

        $week_time = $this->getWeekDate();

        $new_date = strtotime($week_time[$scheduleInfo['week']] . ' ' . $scheduleInfo['time']);

        if ($new_date <= time()) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间已过，请预约下一场']);
        }


        $a_id = array();

        if (!empty($where["help_id"])) {
            $archivesColumn = $this->logicUserArchives->getUserArchivesColumn(['id' => ['in', $where["a_id"]]], 'cover_id,cover_url,sex,nickname,birthday,birth_place,birth_points,now_place,now_points,category_id,time_zone,is_summer');

            foreach ($archivesColumn as $keya => $valuea) {
                $valuea['user_id'] = $where["user_id"];
                $valuea['category_id'] = 99;

                $a_id[] = $this->logicUserArchives->edit($valuea);
            }
            $where["a_id"] = implode(',', $a_id);
            db('subscribe')->where('id', $where["help_id"])->update(['status' => 1]);

        } else {
            $a_id = explode(",", $this->param_data["a_id"]);
            foreach ($a_id as $keya => $valuea) {
                $archivesInfo = $this->logicUserArchives->getUserArchivesInfo(['user_id' => $where["user_id"], 'id' => $valuea]);
                if (empty($archivesInfo)) {
                    return $this->apiReturn(['code' => 3050009, 'msg' => '第' . ($keya + 1) . '个无此档案']);
                }
            }
        }

        $where["ask"] = $this->param_data["ask"];

        $where['start_time'] = $new_date;
        $where['working'] = unit_time($consultInfo['working'], $consultInfo['unit']);
        $regit = $this->logicSubscribe->subscribeEdit($where);

        $this->logicSchedule->scheduleEdit(['id' => $where['s_id'], 'user_id' => $where['user_id'], 'c_id' => $where['c_id'], 'status' => 2, 'start_time' => $new_date]);

        $amountLog['order_number'] = 'Y' . time() . 'U' . $this->param_data['user_id'] . 'R' . mt_rand(100000, 999999);
        $amountLog['type'] = 3;
        $amountLog['pay_type'] = 0;
        $amountLog['trade_type'] = 'APP';
        $amountLog['user_id'] = $this->param_data['user_id'];
        $amountLog['money'] = 0 - $consultInfo['price'];
        $amountLog['status'] = 1;
        $amountLog['new_amount'] = $userInfo['amount'] - $consultInfo['price'];
        $amountLog['remark'] = config('ext_config.paylogtype')[3];
        $this->logicAmountLog->amountLogEdit($amountLog);
        $this->logicUser->setUserIncDec(['id' => $this->param_data['user_id']], 'amount', $consultInfo['price'], 'setDec');

       $this->logicCommonBase->getSmsCode(['PhoneNumberSet' =>array(strval($teacherInfo['mobile'])) ,'TemplateId' => '1452746','type'=>'none','TemplateParamSet' => array( date("m-d H:i",$new_date))]);


        $kf_account_level = $this->logicSite->getSiteInfo(['name'=>'order_remind']);

        $moble=explode(",",$kf_account_level['content']);

        foreach ($moble as $key=>$value){
            if(is_numeric($value)){
                $duanxin_array[]=$value;
            }else{
                send_email($value, '爱神星来新的订单了', '爱神星来新的订单了'.$new_date,'爱神星来新的订单了');
            }
        }

        if(!empty($duanxin_array)){
            $this->logicCommonBase->getSmsCode(['PhoneNumberSet' =>$duanxin_array ,'TemplateId' => '1653975','type'=>'none','TemplateParamSet' => array( date("m-d H:i",$new_date))]);
        }



        return $this->apiReturn(['id' => $regit]);
    }

    /**
     * 获取当前星期几时间戳
     */
    public function getWeekDate()
    {
        $cor_week = date("w");
        $week_time = array();

        $k = 0;
        for ($i = 0; $i < 14; $i++) {
            $new_week = 0;
            if ($i > 6) {
                $new_week = 7;
            }

            if ($i >= $cor_week and $i < ($cor_week + 7)) {
                $week_time[$i - $new_week] = date("Y-m-d", mktime(0, 0, 0, date('m'), date('d') + $k, date('Y')));
                $k++;
            }
        }
        return $week_time;
    }

    /**
     * 我的预约列表
     */
    public function mylist()
    {
        $this->logicSchedule->subscribeOverdue();  //清理过期事件

        $where["user_id"] = $this->param_data["user_id"];
        //$where["type"] = 0;
        $where["status"] = ['gt',-1];
        $order='start_time desc';
        if (isset($this->param_data["status"])) {
            $where["status"] = $this->param_data["status"];
            ($this->param_data["status"]==0 or $this->param_data["status"]==1 or $this->param_data["status"]==2) && $order='start_time asc';
        }
        $subscribeList = $this->logicSubscribe->getSubscribeList($where, '*', $order);

        foreach ($subscribeList as $key => &$value) {
            $consultInfo = $this->logicConsult->getConsultInfo(['id' => $value["c_id"]], 'id,title,cover_id');
            $value['title'] = $consultInfo['title'];
            $value['cover_id'] = $consultInfo['cover_id'];
            $value['cover_url'] = $this->logicFile->getPictureUrl($consultInfo['cover_id']);

            $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $value["t_id"], 'status' => ['gt', '-2']]);
            $value['name'] = $teacherInfo['name'];
            $value['level'] = $teacherInfo['level'];


            $value['is_evaluation'] = 0;
            $evaluation = $this->logicEvaluation->getEvaluationInfo(['user_id' => $where["user_id"], 'pid' => $value["c_id"], 'order_id' => $value["id"], 'type' => 2]);

            if (!empty($evaluation)) {
                $value['is_evaluation'] = 1;
                $value['evaluation_star'] = $evaluation['star'];
            }
        }

        return $this->apiReturn($subscribeList);
    }

    /**
     * 我的预约详细
     */
    public function myinfo()
    {
        $this->logicSchedule->subscribeOverdue();  //清理过期事件

        $where["user_id"] = $this->param_data["user_id"];
        $where["id"] = $this->param_data["id"];

        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where, '*');

        $consultInfo = $this->logicConsult->getConsultInfo(['id' => $subscribeInfo["c_id"]], 'id,title,cover_id,price,working,unit');
        $subscribeInfo['title'] = $consultInfo['title'];
        $subscribeInfo['cover_id'] = $consultInfo['cover_id'];
        $subscribeInfo['cover_url'] = $this->logicFile->getPictureUrl($consultInfo['cover_id']);


        $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $subscribeInfo["t_id"], 'status' => ['gt', '-2']]);
        $subscribeInfo['name'] = $teacherInfo['name'];
        $subscribeInfo['level'] = $teacherInfo['level'];
        $subscribeInfo['introduce'] = html_entity_decode($teacherInfo['introduce']);

        $subscribeInfo['t_user_id'] = 0;
        $subscribeInfo['t_nickname'] = '无';
        $t_infoData = $this->logicUser->getUserInfo(['t_id' => $subscribeInfo["t_id"]], 'id,nickname,name');
        if (!empty($t_infoData)) {
            $subscribeInfo['t_user_id'] = $t_infoData['id'];
            $subscribeInfo['t_nickname'] = $t_infoData['nickname'];
        }

        $subscribeInfo['cover_url'] = $this->logicFile->getPictureUrl($teacherInfo['cover_id']);

        $archivesInfo = $this->logicUserArchives->getUserArchivesColumn(['user_id' => $where["user_id"], 'id' => ['in', $subscribeInfo["a_id"]]], 'id,category_id,nickname');
        $subscribeInfo['archives_name'] = implode("&", array_column($archivesInfo,'nickname'));

        $scheduleInfo = $this->logicSchedule->getScheduleInfo([ 'id' => $subscribeInfo['s_id']]);
        if (empty($scheduleInfo)) {
            return $this->apiReturn(['code' => 3050006, 'msg' => '当前咨询课时不存在']);
        }

        $subscribeInfo['week'] = $scheduleInfo['week'];
        $subscribeInfo['time'] = date("Y-m-d H:i", $subscribeInfo['start_time']);

        $consult_time = unit_time($consultInfo['working'], $consultInfo['unit']);
        $subscribeInfo['seconds_price'] = $consultInfo['price']/$consult_time;

        $time_cha = $subscribeInfo['start_time'] - time();
        $subscribeInfo['time_c'] = $time_cha;
        $subscribeInfo['deduction'] = 0;

        if ($time_cha < 86400 and $time_cha > 43200) {
            $subscribeInfo['deduction'] = intval($consultInfo['price'] * 0.05);
        } elseif ($time_cha < 43200 and $time_cha > 0) {
            $subscribeInfo['deduction'] = intval($consultInfo['price'] * 0.2);
        }

        $subscribeInfo['is_evaluation'] = 0;
        $evaluation = $this->logicEvaluation->getEvaluationInfo(['user_id' => $where["user_id"], 'pid' => $subscribeInfo["c_id"], 'type' => 2, 'order_id' => $subscribeInfo["id"]]);

        if (!empty($evaluation)) {
            $subscribeInfo['is_evaluation'] = 1;
            $subscribeInfo['evaluation'] = $evaluation;
        }

        return $this->apiReturn($subscribeInfo);
    }

    /**
     * 预约列表修改
     */
    public function edit()
    {
        $where["user_id"] = $this->param_data["user_id"];
        $where["id"] = $this->param_data["id"];
        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where, '*');
        $consultInfo = $this->logicConsult->getConsultInfo(['id' => $subscribeInfo["c_id"]], 'id,title,cover_id,price,working,unit');
        $subscribeInfo['title'] = $consultInfo['title'];
        $subscribeInfo['cover_id'] = $consultInfo['cover_id'];

        $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $subscribeInfo["t_id"], 'status' => ['gt', -2]]);
        $subscribeInfo['name'] = $teacherInfo['name'];
        $subscribeInfo['t_id'] = $teacherInfo['id'];
        $subscribeInfo['level'] = $teacherInfo['level'];

        $scheduleInfo = $this->logicSchedule->getScheduleInfo(["user_id" => $where["user_id"], 'status' => 2, 'id' => $subscribeInfo['s_id']]);

        if (empty($scheduleInfo)) {
            return $this->apiReturn(['code' => 3050006, 'msg' => '当前咨询课时不存在']);
        }
        if ($scheduleInfo['status'] == 1) {
            return $this->apiReturn(['code' => 3050007, 'msg' => '当前时间段还未有预约']);
        }
        if (empty($scheduleInfo['time'])) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间段休息']);
        }
        $subscribeInfo['week'] = $scheduleInfo['week'];
        $subscribeInfo['time'] = $scheduleInfo['time'];
        $subscribeInfo['start_time'] = $scheduleInfo['start_time'];

        $time_cha = $scheduleInfo['start_time'] - time();
        if ($time_cha < 0) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '您的预约已结束，不可以修改']);
        }

        //查询课节
        $scheduleNewInfo = $this->logicSchedule->getScheduleInfo(['id' => $this->param_data['s_id'], 'status' => 1, 't_id' => $teacherInfo['id']]);
        if (empty($scheduleNewInfo)) {
            return $this->apiReturn(['code' => 3050006, 'msg' => '当前咨询课时不存在']);
        }
        if ($scheduleNewInfo['status'] == 2) {
            return $this->apiReturn(['code' => 3050007, 'msg' => '当前时间段已被预约']);
        }
        if (empty($scheduleNewInfo['time'])) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间段休息']);
        }

        $deduction = 0;

        if ($time_cha < 86400 and $time_cha > 43200) {
            $deduction = $consultInfo['price'] * 0.05;
        } elseif ($time_cha < 43200 and $time_cha > 0) {
            $deduction = $consultInfo['price'] * 0.2;
        }

        $deduction=intval($deduction);

        $week_time = $this->getWeekDate();

        $new_date = strtotime($week_time[$scheduleNewInfo['week']] . ' ' . $scheduleNewInfo['time']);

        if ($new_date <= time()) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间已过，请预约下一场']);
        }


        if ($deduction > 0) {
            $userInfo = $this->logicUser->getUserInfo(['id' => $where["user_id"]], 'id,amount');
            if ($userInfo['amount'] < $deduction) {
                return $this->apiReturn(['code' => 3050005, 'msg' => '您的星币不足，请去充值']);
            }

            $amountLog['order_number'] = 'YX' . time() . 'U' . $this->param_data['user_id'] . 'R' . mt_rand(100000, 999999);
            $amountLog['type'] = 4;
            $amountLog['pay_type'] = 0;
            $amountLog['trade_type'] = 'APP';
            $amountLog['user_id'] = $this->param_data['user_id'];
            $amountLog['money'] = 0 - $deduction;
            $amountLog['status'] = 1;
            $amountLog['new_amount'] = $userInfo['amount'] - $deduction;
            $amountLog['remark'] = config('ext_config.paylogtype')[4];
            $this->logicAmountLog->amountLogEdit($amountLog);
            $this->logicUser->setUserIncDec(['id' => $this->param_data['user_id']], 'amount', $deduction, 'setDec');
        }

        $this->logicSubscribe->subscribeDel(['id' => $where['id']]);
        $whereNew["c_id"] = $subscribeInfo["c_id"];
        $whereNew["t_id"] = $subscribeInfo["t_id"];
        $whereNew["s_id"] = $this->param_data["s_id"];
        $whereNew["a_id"] = $subscribeInfo["a_id"];
        $whereNew["user_id"] = $subscribeInfo["user_id"];
        $whereNew["ask"] = $subscribeInfo["ask"];
        $whereNew['start_time'] = $new_date;
        $whereNew['working'] = unit_time($consultInfo['working'], $consultInfo['unit']);

        $subscribeInfo["id"] = $this->logicSubscribe->subscribeEdit($whereNew);

        $this->logicSchedule->scheduleEdit(['id' => $scheduleInfo['id'], 'user_id' => 0, 'c_id' => 0, 'status' => 1, 'start_time' => 0]);

        //$this->logicSchedule->scheduleEdit(['id' => $where['s_id'], 'user_id' => $where["user_id"], 'c_id' => '', 'status' => 1, 'start_time' => '']);

        $this->logicSchedule->scheduleEdit(['id' => $whereNew['s_id'], 'user_id' => $where['user_id'], 'c_id' => $whereNew['c_id'], 'status' => 2, 'start_time' => $new_date]);

        return $this->apiReturn($subscribeInfo);
    }


    /**
     * 预约列表删除
     */
    public function subscribeDel()
    {

        $regit = $this->logicSubscribe->subscribeDel(['id' => $this->param_data["id"]]);

        return $this->apiReturn(['id' => $regit]);

    }

    //帮忙约功能

    /**
     * 预约列表添加
     */
    public function addHelp()
    {
        if (!empty($this->param_data["id"])) {
            $where["id"] = $this->param_data["id"];
        }

        $where["c_id"] = $this->param_data["c_id"];
        $where["t_id"] = $this->param_data["t_id"];
        $where["s_id"] = $this->param_data["s_id"];
        $where["a_id"] = $this->param_data["a_id"];
        if (!empty($this->param_data["mobile"])) {

            $mobile = $this->param_data['mobile'];

            $check = '/^(1(([35789][0-9])|(47)))\d{8}$/';
            if (!preg_match($check, $mobile)) {
                return $this->apiReturn(['code' => 1890007, 'msg' => '手机号格式输入不正确']);
            }

            $where["mobile"] = $mobile;
        }
        $where["user_id"] = $this->param_data["user_id"];
        $where["type"] = 1;
        $where["status"] = 0;
        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where, 'id');
        if (!empty($subscribeInfo)) {
            return $this->apiReturn(['code' => 3050002, 'msg' => '您已经预约此时间段']);
        }

        $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $where["t_id"], 'status' => 1]);
        if (empty($teacherInfo)) {
            return $this->apiReturn(['code' => 3050003, 'msg' => '当前咨询师不存在']);
        }

        //查询项目
        $consultInfo = $this->logicConsult->getConsultInfo(['id' => $where["c_id"], 'level' => $teacherInfo['level']]);
        if (empty($consultInfo)) {
            return $this->apiReturn(['code' => 3050004, 'msg' => '当前咨询项目不存在']);
        }

        //查询课节
        $scheduleInfo = $this->logicSchedule->getScheduleInfo(['id' => $where['s_id'], 'status' => 1, 't_id' => $where['t_id']]);
        if (empty($scheduleInfo)) {
            return $this->apiReturn(['code' => 3050006, 'msg' => '当前咨询课时不存在']);
        }
        if ($scheduleInfo['status'] == 2) {
            return $this->apiReturn(['code' => 3050007, 'msg' => '当前时间段已被预约']);
        }
        if (empty($scheduleInfo['time'])) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间段休息']);
        }

        $week_time = $this->getWeekDate();

        $new_date = strtotime($week_time[$scheduleInfo['week']] . ' ' . $scheduleInfo['time']);

        if ($new_date <= time()) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间已过，请预约下一场']);
        }

        $a_id = explode(",", $this->param_data["a_id"]);


        foreach ($a_id as $keya => $valuea) {
            $archivesInfo = $this->logicUserArchives->getUserArchivesInfo(['user_id' => $where["user_id"], 'id' => $valuea]);
            if (empty($archivesInfo)) {
                return $this->apiReturn(['code' => 3050009, 'msg' => '第' . ($keya + 1) . '个无此档案']);
            }
        }

        $where["type"] = 1;
        $where["ask"] = $this->param_data["ask"];
        $where['start_time'] = $new_date;
        $where['working'] = unit_time($consultInfo['working'], $consultInfo['unit']);

        $regit = $this->logicSubscribe->subscribeEdit($where);



        $kf_account_level = $this->logicSite->getSiteInfo(['name'=>'order_remind']);

        $moble=explode(",",$kf_account_level['content']);
        if(!empty($moble)){
            $this->logicCommonBase->getSmsCode(['PhoneNumberSet' =>$moble ,'TemplateId' => '1653975','type'=>'none','TemplateParamSet' => array( date("m-d H:i",$new_date))]);
        }

        return $this->apiReturn(['id' => $regit, 'msg' => '生成成功']);
    }

    /**
     * 预约列表添加
     */
    public function listHelp()
    {

        $where["type"] = 1;
        $where["user_id"] = $this->param_data["user_id"];

        if (isset($this->param_data["status"])) {
            $where["status"] = $this->param_data["status"];
        }

        $subscribeList = $this->logicSubscribe->getSubscribeList($where, '*', 'id desc');

        foreach ($subscribeList as $key => &$value) {
            $consultInfo = $this->logicConsult->getConsultInfo(['id' => $value["c_id"]], 'id,title,cover_id');
            $value['title'] = $consultInfo['title'];
            $value['cover_id'] = $consultInfo['cover_id'];
            $value['cover_url'] = $this->logicFile->getPictureUrl($value['cover_id']);

            $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $value["t_id"], 'status' => ['gt', '-1']]);
            $value['name'] = $teacherInfo['name'];
            $value['level'] = $teacherInfo['level'];

        }

        return $this->apiReturn($subscribeList);
    }

    /**
     * 预约列表修改
     */
    public function helpinfo()
    {
        $where["id"] = $this->param_data["id"];
        $where["type"] = 1;

        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where, '*');

        if (empty($subscribeInfo)) {
            return $this->apiReturn(['code' => 3050005, 'msg' => '当前数据不存在']);
        }

        $consultInfo = $this->logicConsult->getConsultInfo(['id' => $subscribeInfo["c_id"]], 'id,title,cover_id,price');
        $subscribeInfo['title'] = $consultInfo['title'];
        $subscribeInfo['cover_id'] = $consultInfo['cover_id'];
        $subscribeInfo['price'] = $consultInfo['price'];
        $subscribeInfo['cover_url'] = $this->logicFile->getPictureUrl($consultInfo['cover_id']);

        $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $subscribeInfo["t_id"], 'status' => ['gt', '-1']]);
        $subscribeInfo['name'] = $teacherInfo['name'];
        $subscribeInfo['level'] = $teacherInfo['level'];
        $subscribeInfo['introduce'] = html_entity_decode($teacherInfo['introduce']);

        $subscribeInfo['cover_url'] = $this->logicFile->getPictureUrl($teacherInfo['cover_id']);

        $archivesInfo = $this->logicUserArchives->getUserArchivesColumn(['id' => ['in', $subscribeInfo["a_id"]]], 'nickname');
        $subscribeInfo['archives_name'] = implode("&", $archivesInfo);

        $scheduleInfo = $this->logicSchedule->getScheduleInfo(['status' => ['gt', -2], 'id' => $subscribeInfo['s_id']]);

        if (empty($scheduleInfo)) {
            return $this->apiReturn(['code' => 3050006, 'msg' => '当前咨询课时不存在']);
        }
        if (empty($scheduleInfo['time'])) {
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前时间段休息']);
        }
        $subscribeInfo['week'] = $scheduleInfo['week'];
        $subscribeInfo['time'] = date("Y-m-d H:i", $subscribeInfo['start_time']);


        $time_cha = $subscribeInfo['start_time'] - time();
        $subscribeInfo['time_c'] = $time_cha;

        $subscribeInfo['price'] = $consultInfo['price'];

        return $this->apiReturn($subscribeInfo);
    }

    /**
     * 预约加时间
     */
    public function payAddTime()
    {

        $where["id"] = $this->param_data["id"];

        $time = $this->param_data["time"];
        if(!empty($this->param_data["t_id"])){
            $where["t_id"] = $this->param_data["t_id"];
        }else{
            $where["user_id"] = $this->param_data["user_id"];
        }
        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where);

        if(empty($subscribeInfo)){
            return $this->apiReturn(['code' => 3050005, 'msg' => '当前数据不存在']);
        }

        if ($subscribeInfo['status'] > 0) {

            $consultInfo = $this->logicConsult->getConsultInfo(['id' => $subscribeInfo["c_id"]], 'id,title,cover_id,price,working,unit');

            $consult_time = unit_time($consultInfo['working'], $consultInfo['unit']);

            $money = intval($time / $consult_time * $consultInfo['price']);

            $where["user_id"] = $this->param_data["user_id"];
            $userInfo = $this->logicUser->getUserInfo(['id' => $where["user_id"]], 'id,amount,t_id');

            if($userInfo['t_id']==0){
                if ($userInfo['amount'] < $money) {
                    return $this->apiReturn(['code' => 1030105, 'msg' => '您的星币不足，请去充值']);
                }
                $amountLog['order_number'] = 'XS' . time() . 'U' . $subscribeInfo['user_id'] . 'R' . mt_rand(100000, 999999);
                $amountLog['type'] = 12;
                $amountLog['pay_type'] = 0;
                $amountLog['trade_type'] = 'APP';
                $amountLog['user_id'] = $subscribeInfo['user_id'];
                $amountLog['money'] = 0 - $money;
                $amountLog['status'] = 1;
                $amountLog['new_amount'] = $userInfo['amount'] - $money;
                $amountLog['remark'] = config('ext_config.paylogtype')[12];
                $this->logicAmountLog->amountLogEdit($amountLog);
                $this->logicUser->setUserIncDec(['id' => $this->param_data['user_id']], 'amount', $money, 'setDec');
            }
            $this->logicSubscribe->setSubscribeIncDec(['id' => $where["id"]], 'working', $time);


            return $this->apiReturn(['code' => 0, 'msg' => '增加时成功']);
        } else {
            if($subscribeInfo['status']==0){
                return $this->apiReturn(['code' => 3050007, 'msg' => '请等待咨询开始十分钟后操作']);
            }
            return $this->apiReturn(['code' => 3050008, 'msg' => '当前咨询已经结束']);
        }
    }


    /**
     * 预约扣减时间
     */
    public function payDelTime()
    {
        $where["id"] = $this->param_data["id"];
        $where["user_id"] = $this->param_data["user_id"];
        $time = $this->param_data["time"];

        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where);

        if(empty($subscribeInfo)){
            return $this->apiReturn(['code' => 3050005, 'msg' => '当前数据不存在']);
        }

        if($time>$subscribeInfo['working']){
            $time=$subscribeInfo['working'];
        }

        $this->logicSubscribe->setSubscribeIncDec(['id' => $where["id"]], 'working', $time,'setDec');
        return $this->apiReturn(['code' => 0, 'msg' => '操作成功']);

    }

    /**
     * 老师接受通知修改
     */
    public function receivedEdit()
    {
        $where["id"] = $this->param_data["id"];
        $user_id = $this->param_data["user_id"];
        $userInfo = $this->logicUser->getUserInfo(['id' => $user_id], 'id,mobile,t_id');
        if(empty($userInfo)){
            return $this->apiReturn(['code' => 3050005, 'msg' => '您还是不是老师身份']);
        }
        $where["t_id"] = $userInfo["t_id"];
        $where['status']=['in',[1,0]];;

        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where);

        if(empty($subscribeInfo)){
            return $this->apiReturn(['code' => 3050006, 'msg' => '当前数据不存在']);
        }

        if($subscribeInfo['is_received']>0){
            return $this->apiReturn(['code' => 3050007, 'msg' => '您已经确认']);
        }
        $where['is_received']=1;
        $this->logicSubscribe->subscribeEdit($where);

        return $this->apiReturn(['code' => 0, 'msg' => '我收到了']);

    }


    /**
     * 预约通话开始状态修改
     */
    public function callStart()
    {
        $where["id"] = $this->param_data["id"];
        $where["status"] = 0;

        $subscribeInfo = $this->logicSubscribe->getSubscribeInfo($where);

        if(empty($subscribeInfo)){
            return $this->apiReturn(['code' => 3050005, 'msg' => '当前数据不存在']);
        }
        $where['status']=1;
        $this->logicSubscribe->subscribeEdit($where);
        return $this->apiReturn(['code' => 0, 'msg' => '操作成功']);

    }

    /**
     * 预约通话开始状态修改
     */
    public function subscribeMedia()
    {
        $where["user_id"] = $this->param_data["user_id"];
        if(!empty($this->param_data["t_id"])){
            unset( $where["user_id"]);
            $where["second_id"] = $this->param_data["user_id"];
        }
        $subscribeMediaList=$this->logicSubscribeMedia->getSubscribeMediaList($where, '', 'id desc');

        foreach ($subscribeMediaList as $key => &$value) {
            $subscribeInfo = $this->logicSubscribe->getSubscribeInfo(['id'=>$value['s_id']]);

            if(empty($subscribeInfo)){
                unset($subscribeMediaList[$key]);
                continue;
                return $this->apiReturn(['code' => 3050005, 'msg' => '当前数据不存在']);
            }

            $consultInfo = $this->logicConsult->getConsultInfo(['id' => $subscribeInfo["c_id"]], 'id,title,cover_id');
            $value['title'] = $consultInfo['title'];
            $value['cover_id'] = $consultInfo['cover_id'];
            $value['cover_url'] = $this->logicFile->getPictureUrl($consultInfo['cover_id']);
            $teacherInfo = $this->logicTeacher->getTeacherInfo(['id' => $subscribeInfo["t_id"], 'status' => ['gt', '-1']]);
            $value['teacher_name'] = $teacherInfo['name'];
            $value['teacher_level'] = $teacherInfo['level'];
            $UserData = $this->logicUser->getUserInfo(['id' => $value['user_id']], 'id,t_id,name');
            $value['user_name'] = $UserData['name'];
        }
        return $this->apiReturn($subscribeMediaList);

    }
}
