<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\app\controller;
/**
 * 语料控制器
 */
class Corpustwo extends UserBase
{

    public function getlist()
    {
        $param = json_decode(html_entity_decode($this->param['fallInto']), true);

        $chartType = $this->param['chartType'];

        $planetNameObject = get_sington_object('planetName', "astrology\\planetName");

        $planetName = $planetNameObject::$planetChinese;
        $signChinese = $planetNameObject::$signChinese;
        $houseChinese = $planetNameObject::$houseChinese;

        $chartEnglishType = config('ext_astrology')['chartEnglishType'];
        $allow_degree_cn = config('ext_astrology')['allow_degree_cn'];

        $corpusTwoWhere['chartType'] = (string)array_search($chartType, $chartEnglishType);

        if ($corpusTwoWhere['chartType'] < 0) {
            return $this->apiReturn(['code' => 1050002, 'msg' => '无此盘类型']);
        }

        $type_where=array();
        $oneself_where=array();
        $other_where=array();
        $degree_where[]=-1;
        foreach ($param as $keyy => $valuew) {
            $corpusTwoWhere = ['chartType' => $corpusTwoWhere['chartType']];

            if (empty($valuew['type'])) {
                continue;
            }

            $type_where[] = $valuew['type'];

            if ($valuew['type'] == 1) {
                if(!isset($valuew['planet_id']) or empty($planetName[$valuew['planet_id']])){
                    continue;
                }
                $oneself_where[] = $planetName[$valuew['planet_id']];
            } elseif ($valuew['type'] == 2) {
                $oneself_where[] = $houseChinese[($valuew['house_id'])];
            } elseif ($valuew['type'] == 3) {
                $oneself_where[] = $signChinese[$valuew['sign_id']];
            } elseif ($valuew['type'] == 4) {

                if(empty($planetName[$valuew['planet_id']])){
                    continue;
                }
                if(empty($valuew['house_id'])){
                    continue;
                }
                $oneself_where[] = $planetName[$valuew['planet_id']];
                $other_where[] = $houseChinese[($valuew['house_id']-1)];
            } elseif ($valuew['type'] == 5) {
                if(!isset($valuew['planet_id']) or empty($planetName[$valuew['planet_id']])){
                    continue;
                }
                $oneself_where[] = $planetName[$valuew['planet_id']];

                $other_where[] = $signChinese[$valuew['sign_id']];
            } elseif ($valuew['type'] == 6) {
                if(!isset($valuew['planet_id1']) or !isset($valuew['planet_id2']) or empty($planetName[$valuew['planet_id1']]) or empty($planetName[$valuew['planet_id2']])){
                    continue;
                }
                $oneself_where[] = $planetName[$valuew['planet_id1']];
                $other_where[] = $planetName[$valuew['planet_id2']];
                $degree_where[] = $valuew['degree'];

            }elseif ($valuew['type'] == 7) {
                if(empty($valuew['house_id1']) or empty($valuew['house_id2'])){
                    continue;
                }
                $oneself_where[] = $houseChinese[($valuew['house_id1']-1)];
                $other_where[] = $houseChinese[($valuew['house_id2'] - 1)];


            } elseif ($valuew['type'] == 8) {

                $oneself_where[] = $houseChinese[($valuew['house_id']-1)];
                $other_where[] = $signChinese[$valuew['sign_id']];
            }

        }

        !empty($oneself_where) && $corpusTwoWhere['oneself']=['in',array_unique($oneself_where)];
        !empty($other_where) && $corpusTwoWhere['other']=['in',array_unique($other_where)];
        !empty($degree_where) && $corpusTwoWhere['degree']=['in',array_unique($degree_where)];

        $corpusTwoWhere['type']=['in',array_unique($type_where)];

        $corpusTwoListData = array();
        $corpusTwoList = $this->logicCorpusTwo->getCorpusTwoList($corpusTwoWhere, 'oneself,other,degree,keywords,content,content1', '', false);


        $corpusConstellationList = array();
        if ($corpusTwoWhere['chartType'] == 1) {
            $CorpusConstellationAll['oneself'] = ['in', ['太阳', '月亮', '水星', '金星', '火星', '木星', '土星', '天王星', '海王星', '冥王星', '上升']];
            $CorpusConstellationAll['chartType'] = 1;
            $CorpusConstellationAll['type'] = ['in', [1, 5]];

            $corpusConstellationList = $this->logicCorpusConstellation->getCorpusConstellationAllList($CorpusConstellationAll, 'oneself,other,keywords,content,type', '', false);

        }

        foreach ($param as $keyy => $valuew) {
            $corpusTwoWhere = ['chartType' => $corpusTwoWhere['chartType']];

            if (empty($valuew['type'])) {
                continue;
            }
            $and_tr = '';
            $yuliao_zhen = false;
            $corpusTwoModify = array();
            foreach ($corpusTwoList as $key => $value) {
                if ($valuew['type'] == 1) {
                    if (empty($planetName[$valuew['planet_id']])) {
                        continue 2;
                    }
                    if ($value['oneself'] == $planetName[$valuew['planet_id']]) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }
                } elseif ($valuew['type'] == 2) {
                    if ($value['oneself'] == $houseChinese[($valuew['house_id'])]) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }
                } elseif ($valuew['type'] == 3) {
                    if ($value['oneself'] == $signChinese[($valuew['sign_id'])]) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }
                } elseif ($valuew['type'] == 4) {
                    if (empty($planetName[$valuew['planet_id']])) {
                        continue 2;
                    }
                    $and_tr = '落入';
                    if ($value['oneself'] == $planetName[$valuew['planet_id']] and $value['other'] == $houseChinese[($valuew['house_id'] - 1)]) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }
                } elseif ($valuew['type'] == 5) {
                    if (empty($planetName[$valuew['planet_id']])) {
                        continue 2;
                    }
                    $and_tr = '落入';
                    if ($value['oneself'] == $planetName[$valuew['planet_id']] and $value['other'] == $signChinese[($valuew['sign_id'])]) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }
                } elseif ($valuew['type'] == 6) {
                    if (empty($planetName[$valuew['planet_id1']]) or empty($planetName[$valuew['planet_id2']])) {
                        continue 2;
                    }
                    $and_tr = $allow_degree_cn[$valuew['degree']];

                    if ($value['oneself'] == $planetName[$valuew['planet_id1']] and $value['other'] == $planetName[$valuew['planet_id2']] and $value['degree'] == $valuew['degree']) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }

                } elseif ($valuew['type'] == 7) {
                    $and_tr = '宫主星飞入';
                    if(empty($valuew['house_id1']) or empty($valuew['house_id2'])){
                        continue;
                    }
                    if ($value['oneself'] == $houseChinese[($valuew['house_id1'] - 1)] and $value['other'] == $houseChinese[($valuew['house_id2'] - 1)]) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }
                } elseif ($valuew['type'] == 8) {
                    if ($value['oneself'] == $houseChinese[($valuew['house_id'] - 1)] and $value['other'] == $signChinese[$valuew['sign_id']]) {
                        $yuliao_zhen = true;
                        $corpusTwoModify = $value;
                        unset($corpusTwoList[$key]);
                    }
                } else {
                    continue 2;
                }

            }
            if ($yuliao_zhen and !empty($corpusTwoModify)) {

                $valuew['oneself'] = $corpusTwoModify['oneself'];
                $valuew['other'] = $corpusTwoModify['other'];

                $valuew['title'] = $corpusTwoModify['oneself'] . $and_tr . $corpusTwoModify['other'];

                $valuew['content'] = html_entity_decode($corpusTwoModify['content']);
                $valuew['content1'] = html_entity_decode($corpusTwoModify['content1']);

                if (strstr($valuew['content'], '[1]') !== false) {
                    $valuew['content'] = str_replace('[1]', '你', $valuew['content']);
                }
                if (strstr($valuew['content'], '[2]') !== false) {
                    $valuew['content'] = str_replace('[2]', '对方', $valuew['content']);
                }

                $corpusTwoListData[] = $valuew;
            }
        }


        $yang=array();
        $sheng=array();
        $qita=array();
        foreach ($corpusTwoListData as $key => $value) {

            foreach ($corpusConstellationList as $keys => $values) {
                if ($values['oneself'] == $value['oneself'] and $values['other'] == $value['other'] and $values['type'] == 5) {
                    $value['content3'] = $values['content'];
                    unset($corpusConstellationList[$keys]);
                }
                if ($values['oneself'] == $value['oneself'] and $values['type'] == 1) {
                    $value['describe'] = $values['content'];
                    // unset($corpusConstellationList[$keys]);
                }
            }
            if(in_array($value['oneself'],['太阳', '月亮'])){
                $yang[]=$value;
            }else if($value['oneself']=='上升'){
                $sheng[]=$value;
            }else{
                $qita[]=$value;
            }

        }
        return $this->apiReturn(array_merge($yang,$sheng,$qita));
    }

}
