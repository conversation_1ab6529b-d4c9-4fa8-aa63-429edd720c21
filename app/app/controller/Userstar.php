<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\app\controller;

/**
 * 明星档案控制器
 */
class Userstar extends UserBase
{

    /**
     * 明星档案列表
     */
    public function list()
    {

        $where = $this->logicUserStar->getWhere($this->param_data);

        if(empty($where['name'])){
            $where['status']=1;

        }

        $order='capital asc';
        if(!empty($this->param_data['order'])){
            $order=str_replace("****",",",$this->param['order']);
            $order=str_replace("**"," ",$order);
        }

        $data=$this->logicUserStar->getUserStarList($where, 'id,name,cover_url,capital', $order,false);

//        foreach ($data as $kye => &$vole) {
//            $vole['cover_url'] = $this->logicFile->getPictureUrl($vole['cover_id']);
//            unset($vole['cover_id']);
//        }

		return $this->apiReturn($data);
    }
    /**
     * 明星档案列表
     */
    public function listSearch()
    {
        $where = $this->logicUserStar->getWhere($this->param_data);
        if(empty($where['name'])){
            $where['status']=1;
        }

        $order='capital asc';
        if(!empty($this->param_data['order'])){
            $order=str_replace("****",",",$this->param['order']);
            $order=str_replace("**"," ",$order);
        }

        !empty($this->param_data['sex']) && $where['sex'] = $this->param_data['sex'];
        !empty($this->param_data['capital']) && $where['capital'] = $this->param_data['capital'];

        $data=$this->logicUserStar->getUserStarList($where, 'id,name,cover_url,capital,name as nickname,birthday,birth_place,birth_points,birth_place as now_place,birth_points as now_points,time_zone,is_summer', $order);

//        foreach ($data as $kye => &$vole) {
//            $vole['cover_url'] = $this->logicFile->getPictureUrl($vole['cover_id']);
//            unset($vole['cover_id']);
//        }

        return $this->apiReturn($data);
    }
    /**
     * 明星档案无分页列表
     */
    public function userStarColumn()
    {

        $data=$this->logicUserStar->getUserStarColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 明星档案添加
     */
    public function userStarAdd()
    {
	  
	   $regit=$this->logicUserStar->userStarEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
    }
    /**
     * 明星档案高级搜索
     */
    public function userStarSenior()
    {

        $data=$this->logicUserStar->getUserStarColumn($this->param_data);

        return $this->apiReturn($data);
    }


    /**
     * 明星档案删除
     */
    public function userStarDel()
    {

       $regit=$this->logicUserStar->userStarDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
