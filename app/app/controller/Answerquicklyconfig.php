<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\app\controller;

/**
 * 抢答配置控制器
 */
class Answerquicklyconfig extends UserBase
{

    /**
     * 抢答配置列表
     */
    public function answerQuicklyConfigList()
    {

        $where = $this->logicAnswerQuicklyConfig->getWhere($this->param_data);

        $data=$this->logicAnswerQuicklyConfig->getAnswerQuicklyConfigList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 抢答配置无分页列表
     */
    public function answerQuicklyConfigColumn()
    {

        $data=$this->logicAnswerQuicklyConfig->getAnswerQuicklyConfigColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 抢答配置添加
     */
    public function answerQuicklyConfigAdd()
    {
	  
	   $regit=$this->logicAnswerQuicklyConfig->answerQuicklyConfigEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 抢答配置删除
     */
    public function answerQuicklyConfigDel()
    {

       $regit=$this->logicAnswerQuicklyConfig->answerQuicklyConfigDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
