<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\weiapi\controller;
use app\common\controller\ControllerBase;

/**
 * 首页控制器
 */
class Apiinterface extends ControllerBase
{
    
    /**
     * 首页方法
     */
    public function index()
    {

        $list = $this->logicApiInterface->getApiList(['module'=>MODULE_NAME,'status'=>1], true, 'sort');

        $code_list = $this->logicApiInterface->apiErrorCodeData();

        $this->assign('code_list', $code_list);

        $content = $this->fetch('content_default');

        $this->assign('content', $content);

        $this->assign('list', $list);

        return $this->fetch();
    }

    /**
     * API详情
     */
    public function details($id = 0)
    {

       // $list = $this->logicApiInterface->getApiList(['module'=>MODULE_NAME], true, 'sort');

        $info = $this->logicApiInterface->getApiInfo(['id' => $id]);

        $this->assign('info', $info);

        $content = $this->fetch('content_template');

        if (IS_AJAX) {

            return throw_response_exception(['content' => $content]);
        }
//
//        $this->assign('content', $content);
//
//        $this->assign('list', $list);
//
//        return $this->fetch('index');
    }
}
