<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | authorization   | http://www.apache.org/licenses/LICENSE-2.0 )           |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiwap\controller;
use exwechat\api\OAuth\OAuth;

class Oauthbase extends Common
{

    /**
     * 构造方法
     */
    public function __construct()
    {
        // 执行父类构造方法
        parent::__construct();

        if(!cookie('openid') and empty($this->param['code'])){
            $this->snsapi_base($this->param['wechat'],URL_TRUE);
        }
    }
    //应用授权作用域，snsapi_base （不弹出授权页面，直接跳转，只能获取用户openid）
    public function snsapi_base($wechat,$url)
    {
        dump($url);
        $tokens=getAppidToken($wechat,true);
        cookie('appid_conf', $tokens,600);
        $redirect_uri = url('callback_base','', false, true);
        $scope = 'snsapi_base';
        $state = urlencode($url);

        $redirect_uri = urlencode($redirect_uri);
        $OAuth = get_sington_object('OAuthEx', "exwechat\\api\\OAuth\\OAuth",$tokens);
        $url = $OAuth->getCodeUrl($redirect_uri, $scope, $state);
        $this->redirect($url);
    }
    public function callback_base()
    {
        $OAuth = get_sington_object('OAuthEx', "exwechat\\api\\OAuth\\OAuth",cookie('appid_conf'));
        $ret = $OAuth->getToken($_GET['code']);
        cookie('openid', $ret['openid'],600);
        $url=$this->param['state'].'/openid/'.$ret['openid'];
        $this->redirect($url);
    }
}
