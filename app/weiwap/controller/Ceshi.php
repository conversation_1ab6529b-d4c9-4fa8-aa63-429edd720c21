<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiwap\controller;
use exwechat\api\OAuth\OAuth;
use exwechat\api\account\QRCode as QRCodeEx;

/**
 * 场景二维码控制器
 */
class Ceshi extends Common
{

    public function index()
    {


    }
    public function snsapi_userinfo($wechat)
    {
        if(empty($this->param['wechat'])){
            $this->jump(RESULT_ERROR, '非法载入',url('index'));
            exit();
        }
        $tokens=getAppidToken($wechat,true);

        session('appid_conf', $tokens);
        $redirect_uri = url('callback_userinfo','', false, true);
        $scope = 'snsapi_userinfo';
        $state = '123';
        $redirect_uri = urlencode($redirect_uri);

        $OAuth = get_sington_object('OAuthEx', OAuth::class,$tokens);
        $url = $OAuth->getCodeUrl($redirect_uri, $scope, $state);
        $this->redirect($url);
    }
    public function callback_userinfo()
    {

        $OAuth = get_sington_object('OAuthEx', OAuth::class,session('appid_conf'));

        $ret = $OAuth->getToken($_GET['code']);

        if(isset($ret['errcode'])){
            $this->jump(RESULT_ERROR, '错误码:'.$ret['errcode'].'错误信息:'.$ret['errmsg'],url('index'));
            exit();
        }

        $info = $OAuth->getUserInfo($ret['access_token'], $ret['openid']);
        $info['nickname']=filterEmoji($info['nickname']);

        $this->redirect("http://test.feixiaoguai.com:8787/?nickname=".$info['nickname']);
    }
}
