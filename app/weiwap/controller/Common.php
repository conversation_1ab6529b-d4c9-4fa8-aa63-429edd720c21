<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiwap\controller;


class Common extends IndexBase
{

    public function page($current_page = 0, $last_page = 0, $offset = 3, $page_number = 7)
    {

        $content = get_page_html($current_page, $last_page, $offset, $page_number);

        return throw_response_exception(['content' => $content]);
    }

    public function getShare($wechat = 'gh_ea6f357fa6cf')
    {

        !empty($this->param['wechat']) && $wechat = $this->param['wechat'];

        $tokens = getAppidToken($wechat, true);

        $JSSDK = get_sington_object('JSSDKEx', "exwechat\\api\\JSSDK\\JSSDK", $tokens['access_token']);
        $share = $tokens;
        $share['timestamp'] = time();
        //$shortUrl = get_sington_object('shortUrl', "exwechat\\api\\account\\shortUrl",session('appid_conf')['access_token']);
        $share['url'] = URL_TRUE;

        $share['signature'] = $JSSDK->signature($tokens['ticket'], 'abcdefghijklmnopqrstu', time(), URL_TRUE);

        $this->view->engine->layout(false);
        $this->assign('share', $share);
    }


    /**
     * 余额充值记录添加
     */
    public function getPayData()
    {

        $param = $this->param;


        $trade_view = 'wechat_pay';

        if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
            $param['trade_type'] = 'JSAPI';   //微信内部支付

            if(empty(cookie('openid'))){
                $this->snsapi_base('gh_ea6f357fa6cf',URL_TRUE);
            }else{
                $param['openid']=cookie('openid');
            }


        } else {
            if (isset ($_SERVER['HTTP_USER_AGENT'])) {
                $clientkeywords = array('nokia', 'sony', 'ericsson', 'mot',
                    'samsung', 'htc', 'sgh', 'lg', 'sharp',
                    'sie-', 'philips', 'panasonic', 'alcatel',
                    'lenovo', 'iphone', 'ipod', 'blackberry',
                    'meizu', 'android', 'netfront', 'symbian',
                    'ucweb', 'windowsce', 'palm', 'operamini',
                    'operamobi', 'openwave', 'nexusone', 'cldc',
                    'midp', 'wap', 'mobile'
                );
                // 从HTTP_USER_AGENT中查找手机浏览器的关键字
                if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
                    $param['trade_type'] = 'MWEB';   //H5手机端支付
                    $trade_view = 'wechat_pay_h';

                } else {
                    $param['trade_type'] = 'NATIVE';   //PC端支付
                    $trade_view = 'wechat_pay_pc';
                }
            } else {
                $param['trade_type'] = 'NATIVE';   //PC端支付
                $trade_view = 'wechat_pay_pc';
            }
        }
        $this->getShare();

        $param['urlPay'] = url('addPayData');

        $param['detectionPay'] = url('payDetectionData');
        $param['wechat']='gh_ea6f357fa6cf';

        $this->assign('param', $param);
        return $this->fetch($trade_view);
    }


    /**
     * VIP支付H5添加
     */
    public function addPayData()
    {
        $order_number = $this->param['order_number'];

        $trade_type = 'MWEB';

        !empty($this->param['trade_type']) && $trade_type = $this->param['trade_type'];

        !empty($this->param['openid']) && $payData['openid'] = $this->param['openid'];


        $amountLog = $this->logicAmountLog->getAmountLogInfo(['order_number' => $order_number, 'status' => 0]);

        if (empty($amountLog)) {

            return json(['code' => 124, 'msg' => '该笔订单不存在']);
        }

        $url_encode_redirect_url='';
        if ($amountLog['model'] == 'activity_sortition_log') {
            $url_encode_redirect_url = 'https://xg.robustcn.com/hdx/#/pages/bindmobile/bindmobile?order_number=' . $order_number;
        } elseif ($amountLog['model'] == 'activity_sortition_let') {
            $url_encode_redirect_url = 'https://xg.robustcn.com/hdx';
        } elseif ($amountLog['model'] == 'astrolabe_decode_order') {
            $order_type  = $this->logicAstrolabeDecodeOrder->getAstrolabeDecodeOrderInfo(['order_number'=>$order_number],'id,type,order_number,a_id,version');
            $url_encode_redirect_url = 'https://xg.robustcn.com/xlym/index.html#/pages/detail_'.($order_type['type']-1).'/detail_'.($order_type['type']-1).'?id='.$order_type['a_id'];

            if($order_type['version']==22){
                $url_encode_redirect_url = 'https://2.zxwhp.top/Nxlym/index.html#/pages/detail_'.($order_type['type']-1).'/detail_'.($order_type['type']-1).'?id='.$order_type['a_id'];
            }
        } elseif ($amountLog['model'] == 'activity_transport_user') {
            $order_type  = $this->logicActivityTransportUser->getActivityTransportUserInfo(['order_number'=>$order_number],'id,order_number');
            $url_encode_redirect_url = 'https://xg.robustcn.com/year/#/pages/report/report?id='.$order_type['id'];
        }elseif ($amountLog['model'] == 'activity_transport_two') {
            $order_type  = $this->logicActivityTransportTwo->getActivityTransportTwoInfo(['order_number'=>$order_number],'id,order_number');
            $url_encode_redirect_url = 'https://2.zxwhp.top/year22/#/pages/report/report?id='.$order_type['id'];
        }
        $userConfig = getAppidToken($this->param['wechat']);

        $exwechat = get_sington_object('payEx', "exwechat\\api\\pay\\wxPay", $userConfig);

        $payData['out_trade_no'] = $order_number;
        $payData['body'] = $amountLog['remark'];
        //$payData['total_fee'] = $getVipPayLogSingleInfo['amount'] * 100;
        $payData['total_fee'] = $amountLog['money'] * 100;
        $payData['notify_url'] = DOMAIN . '/wexc.php/paynotify/paynotify';
        $payData['trade_type'] = $trade_type;

        $regit = $exwechat->unified($payData);

        if($userConfig==false){
            return json(['code' => 12, 'msg' => '传输公众号不存在']);
        }

        if ($regit['return_code'] == 'SUCCESS') {//如果这两个都为此状态则返回mweb_url，详情看‘统一下单’接口文档

            if ($regit['result_code'] == 'SUCCESS') {//如果这两个都为此状态则返回mweb_url，详情看‘统一下单’接口文档

                if ($trade_type == 'MWEB') {  //拼接支付链接 H5

                    $url = $regit['mweb_url'] . '&redirect_url=' . urlencode($url_encode_redirect_url);

                    return json(['code' => 0, 'mweb_url' => $url, 'order_number' => $order_number, 'total_fee' => $amountLog['money'], 'remark' => $amountLog['remark'], 'redirect_url' => $url_encode_redirect_url]);

                } else if ($trade_type == 'NATIVE') {   //微信扫码支付

                    return json(['code' => 0, 'mweb_url' => $regit['code_url'], 'order_number' => $order_number, 'total_fee' => $amountLog['money'], 'remark' => $amountLog['remark'], 'redirect_url' => $url_encode_redirect_url]);

                } else if ($trade_type == 'JSAPI') {   //微信内部支付
                    $regit['url_href'] = $url_encode_redirect_url;
                    return json($regit);

                }
            }
            if ($regit['result_code'] == 'FAIL') {
                return json(['code' => 1245, 'msg' => $regit['err_code_des']]);
            }
        }else{
            return json(['code' => 12456, 'msg' => '支付异常，请联系开发人员']);
        }


    }

    /**
     * App VIP支付H5添加
     */
    public function addPayH5VipData()
    {
        $order_number = $this->param['order_number'];
        $wechat = $this->param['wechat'];

        $getVipPayLogSingleInfo = $this->logicVipPayLog->getVipPayLogSingleInfo(['order_number' => $order_number, 'order_status' => 0]);

        if (empty($getVipPayLogSingleInfo)) {

            $this->evokeApp('该笔订单不存在');
        }

        $userConfig = getAppidToken($wechat);

        $weiUserInfoData = $this->logicWeiUserInfo->getWeiUserInfoInfo(['id' => $getVipPayLogSingleInfo['user_id']], 'unionid');

        if (empty($weiUserInfoData['unionid'])) {

            $this->evokeApp('您还授权绑定微信');
        }

        $infoData = $this->logicWeiUser->getWeiUserInfo(['unionid' => $weiUserInfoData['unionid'], 'choose_appid' => $userConfig['id']], 'id,openid');
        if (empty($infoData['openid'])) {

            $this->evokeApp('您还授权绑定微信');
        }

        $exwechat = get_sington_object('payEx', "exwechat\\api\\pay\\wxPay", $userConfig);

        $payData['out_trade_no'] = $order_number;
        $payData['body'] = '购买VIP会员';
        $payData['total_fee'] = $getVipPayLogSingleInfo['amount'] * 100;
        //$payData['total_fee'] = 1;
        $payData['openid'] = $infoData['openid'];
        $payData['notify_url'] = DOMAIN . '/wexc.php/Paynotify/payNotify';
        $payData['trade_type'] = 'MWEB';

        $regit = $exwechat->unified($payData);

        if ($regit['return_code'] == 'SUCCESS') {//如果这两个都为此状态则返回mweb_url，详情看‘统一下单’接口文档

            if ($regit['result_code'] == 'SUCCESS') {//如果这两个都为此状态则返回mweb_url，详情看‘统一下单’接口文档
                $url_encode_redirect_url = urlencode(DOMAIN . '/weiwap/common/evokeApp/msg/支付成功');//支付成功回调路径，在Home/config.php文件中可配置
                $url = $regit['mweb_url'] . '&redirect_url=' . $url_encode_redirect_url;//拼接支付链接
                echo "<script language='javascript' type='text/javascript'>window.location.href='$url'</script>";
                exit();
            }
            if ($regit['result_code'] == 'FAIL') {
                $this->evokeApp($regit['err_code_des']);
            }
        }
    }

    /**
     * 唤起app
     */
    public function evokeApp($msg = '支付成功')
    {

        echo "<script language='javascript' type='text/javascript'>alert('" . $msg . "');window.location.href='robustcn://'</script>";
        exit();
    }


    //应用授权作用域，snsapi_base （不弹出授权页面，直接跳转，只能获取用户openid）
    public function snsapi_base($wechat,$url)
    {
        $tokens=getAppidToken($wechat,true);
        cookie('appid_conf', $tokens,1000);
        $redirect_uri = url('callback_base','', false, true);
        $scope = 'snsapi_base';
        $state = urlencode($url);

        $redirect_uri = urlencode($redirect_uri);
        $OAuth = get_sington_object('OAuthEx', "exwechat\\api\\OAuth\\OAuth",$tokens);
        $url = $OAuth->getCodeUrl($redirect_uri, $scope, $state);

        header("Location:$url");
        exit;
    }
    public function callback_base()
    {
        $OAuth = get_sington_object('OAuthEx', "exwechat\\api\\OAuth\\OAuth",cookie('appid_conf'));
        $ret = $OAuth->getToken($_GET['code']);

        if(!empty($ret['openid'])){

           // $url=$this->param['state'].'/openid/'.$ret['openid'];
            $url=$this->param['state'];
            cookie('openid', $ret['openid'],1000);
            header("Location:$url");
            exit;
        }else{
            echo '支付启动异常，请联系客服';
        }

    }
}
