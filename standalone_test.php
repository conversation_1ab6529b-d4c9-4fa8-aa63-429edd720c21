<?php
/**
 * 独立渲染器测试 - 不依赖外部类
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <title>独立渲染器测试</title>\n";
echo "    <style>\n";
echo "        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n";
echo "        .success { color: #008000; font-weight: bold; }\n";
echo "        .error { color: #ff0000; font-weight: bold; }\n";
echo "        .info { color: #0066cc; }\n";
echo "        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo "        pre { background: #f5f5f5; padding: 10px; margin: 10px 0; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>🚀 独立渲染器测试</h1>\n";

// 包含独立渲染器
echo "<div class='section'>\n";
echo "<h2>1. 包含独立渲染器</h2>\n";

if (file_exists('standalone_image_renderer.php')) {
    require_once 'standalone_image_renderer.php';
    echo "<span class='success'>✓</span> 独立渲染器加载成功<br>\n";
} else {
    echo "<span class='error'>✗</span> 独立渲染器文件不存在<br>\n";
    exit;
}

echo "</div>\n";

// 测试功能
echo "<div class='section'>\n";
echo "<h2>2. 功能测试</h2>\n";

try {
    use Jyotish\Draw\Renderer\StandaloneDraw;
    
    // 创建独立 Draw 实例
    $draw = new StandaloneDraw(400, 300, StandaloneDraw::RENDERER_IMAGE);
    echo "<span class='success'>✓</span> 创建独立 Draw 实例成功<br>\n";
    
    // 测试 setOptions - 现在应该不会报错了
    echo "<h3>2.1 setOptions 测试</h3>\n";
    $options = [
        'fontColor' => '#FF0000',
        'fontSize' => 14,
        'strokeColor' => '#0000FF',
        'strokeWidth' => 2,
        'customVar1' => 'test_value',
        'projectName' => 'Standalone Test'
    ];
    
    $draw->setOptions($options);
    echo "<span class='success'>✓</span> setOptions 调用成功！<br>\n";
    
    // 测试获取存储的选项
    echo "<h3>2.2 获取存储选项测试</h3>\n";
    $renderer = $draw->getRenderer();
    $storedOptions = $renderer->getStoredOptions();
    echo "<span class='info'>存储的选项数量:</span> " . count($storedOptions) . "<br>\n";
    
    $projectName = $renderer->getStoredOption('projectName', 'Unknown');
    $customVar = $renderer->getStoredOption('customVar1', 'default');
    echo "<span class='info'>项目名称:</span> $projectName<br>\n";
    echo "<span class='info'>自定义变量:</span> $customVar<br>\n";
    
    // 测试绘制功能
    echo "<h3>2.3 绘制功能测试</h3>\n";
    $draw->drawText("独立渲染器测试成功!", 200, 50, ['textAlign' => 'center']);
    echo "<span class='success'>✓</span> 绘制文本成功<br>\n";
    
    $trianglePoints = [200, 100, 160, 160, 240, 160];
    $draw->drawPolygon($trianglePoints, ['strokeColor' => '#00AA00']);
    echo "<span class='success'>✓</span> 绘制多边形成功<br>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 测试失败: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "</div>\n";

// 优势说明
echo "<div class='section'>\n";
echo "<h2>3. ✅ 独立渲染器的优势</h2>\n";

echo "<p><strong>解决的问题：</strong></p>\n";
echo "<ul>\n";
echo "<li>🔧 不依赖外部 <code>Jyotish\\Base\\Utility</code> 类</li>\n";
echo "<li>🔧 内置了颜色转换功能</li>\n";
echo "<li>🔧 包含完整的选项存储功能</li>\n";
echo "<li>🔧 单文件解决方案，易于部署</li>\n";
echo "</ul>\n";

echo "<p><strong>功能特性：</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ 完整的 <code>setOptions()</code> 功能</li>\n";
echo "<li>✅ 选项存储和检索</li>\n";
echo "<li>✅ 文本和多边形绘制</li>\n";
echo "<li>✅ 颜色处理（十六进制转 RGB）</li>\n";
echo "<li>✅ 文本对齐和样式设置</li>\n";
echo "</ul>\n";

echo "</div>\n";

// 使用示例
echo "<div class='section'>\n";
echo "<h2>4. 使用示例</h2>\n";

echo "<pre>";
echo htmlspecialchars('<?php
// 包含独立渲染器
require_once "standalone_image_renderer.php";

use Jyotish\Draw\Renderer\StandaloneDraw;

// 创建绘图实例
$draw = new StandaloneDraw(400, 300);

// 设置选项 - 完全正常工作！
$draw->setOptions([
    "fontColor" => "#FF0000",
    "strokeColor" => "#0000FF",
    "strokeWidth" => 2,
    "customVar" => "my_value",
    "projectName" => "My Project"
]);

// 获取存储的选项
$renderer = $draw->getRenderer();
$projectName = $renderer->getStoredOption("projectName");

// 绘制内容
$draw->drawText($projectName, 200, 50, ["textAlign" => "center"]);
$draw->drawPolygon([200, 100, 150, 200, 250, 200]);

// 输出图像
$draw->render();
?>');
echo "</pre>\n";

echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>5. 测试输出</h2>\n";
echo "<p>查看实际的图像输出：</p>\n";
echo "<p><a href='standalone_image_output.php' target='_blank'>🖼️ 查看独立渲染器图像输出</a></p>\n";
echo "</div>\n";

echo "<p><em>测试完成时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
echo "</body>\n";
echo "</html>\n";
?>
