<?php
/**
 * 图像输出测试
 */

// 包含所有必要文件
require_once 'jyotish/src/Base/Traits/OptionTrait.php';
require_once 'jyotish/src/Base/Traits/GetTrait.php';
require_once 'jyotish/src/Base/Utility.php';
require_once 'jyotish/src/Renderer/AbstractRenderer.php';
require_once 'jyotish/src/Renderer/Image.php';
require_once 'jyotish/src/Draw.php';

use Jyotish\Draw\Draw;

try {
    // 创建 400x300 像素的图像
    $draw = new Draw(400, 300, Draw::RENDERER_IMAGE);
    
    // 绘制标题
    $draw->drawText("Jyotish Draw 测试", 200, 30, [
        'fontColor' => '#000080',
        'textAlign' => 'center'
    ]);
    
    // 绘制说明文字
    $draw->drawText("图像渲染演示", 50, 60, [
        'fontColor' => '#333333'
    ]);
    
    // 绘制三角形
    $trianglePoints = [
        200, 80,   // 顶点
        150, 150,  // 左下
        250, 150   // 右下
    ];
    $draw->drawPolygon($trianglePoints, [
        'strokeColor' => '#FF0000',
        'strokeWidth' => 2
    ]);
    
    // 在三角形旁边添加标签
    $draw->drawText("三角形", 270, 120, [
        'fontColor' => '#FF0000'
    ]);
    
    // 绘制矩形
    $rectanglePoints = [
        50, 180,   // 左上
        350, 180,  // 右上
        350, 250,  // 右下
        50, 250    // 左下
    ];
    $draw->drawPolygon($rectanglePoints, [
        'strokeColor' => '#00AA00',
        'strokeWidth' => 3
    ]);
    
    // 在矩形内部添加文字
    $draw->drawText("矩形区域", 200, 215, [
        'fontColor' => '#00AA00',
        'textAlign' => 'center'
    ]);
    
    // 绘制一个复杂的多边形（五角星的外轮廓）
    $starPoints = [
        200, 270,  // 顶点
        210, 290,  // 右上
        230, 290,  // 右
        215, 305,  // 右下
        220, 325,  // 右底
        200, 315,  // 底
        180, 325,  // 左底
        185, 305,  // 左下
        170, 290,  // 左
        190, 290   // 左上
    ];
    $draw->drawPolygon($starPoints, [
        'strokeColor' => '#8000FF',
        'strokeWidth' => 2
    ]);
    
    // 添加版权信息
    $draw->drawText("© 2024 Jyotish Demo", 350, 290, [
        'fontColor' => '#666666',
        'textAlign' => 'right'
    ]);
    
    // 渲染并输出图像
    $draw->render();
    
} catch (Exception $e) {
    // 如果出错，输出错误信息
    header('Content-Type: text/plain');
    echo "生成图像时发生错误: " . $e->getMessage() . "\n";
    echo "错误详情:\n" . $e->getTraceAsString();
}
?>
