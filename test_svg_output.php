<?php
/**
 * 简单 SVG 输出测试
 */

// 包含必要文件
require_once 'jyotish/src/Base/Traits/OptionTrait.php';
require_once 'jyotish/src/Base/Traits/GetTrait.php';
require_once 'jyotish/src/Base/Utility.php';
require_once 'jyotish/src/Renderer/AbstractRenderer.php';
require_once 'jyotish/src/Renderer/Svg.php';
require_once 'jyotish/src/Draw.php';

use Jyotish\Draw\Draw;

try {
    // 创建 300x200 像素的 SVG
    $draw = new Draw(300, 200, Draw::RENDERER_SVG);
    
    // 设置选项
    $draw->setOptions([
        'fontColor' => '#000080',
        'strokeColor' => '#FF0000',
        'strokeWidth' => 2,
        'fillColor' => '#FFE0E0'
    ]);
    
    // 绘制标题
    $draw->drawText("SVG 测试成功!", 150, 30, [
        'textAlign' => 'center'
    ]);
    
    // 绘制三角形
    $trianglePoints = [
        150, 60,   // 顶点
        120, 120,  // 左下
        180, 120   // 右下
    ];
    $draw->drawPolygon($trianglePoints, [
        'strokeColor' => '#00AA00',
        'strokeWidth' => 3,
        'fillColor' => '#E0FFE0'
    ]);
    
    // 绘制矩形
    $rectanglePoints = [
        50, 140,   // 左上
        250, 140,  // 右上
        250, 180,  // 右下
        50, 180    // 左下
    ];
    $draw->drawPolygon($rectanglePoints, [
        'strokeColor' => '#0000FF',
        'strokeWidth' => 2,
        'fillColor' => '#E0E0FF'
    ]);
    
    // 在矩形中添加文字
    $draw->drawText("SVG 修复成功!", 150, 160, [
        'fontColor' => '#0000FF',
        'textAlign' => 'center'
    ]);
    
    // 渲染并输出 SVG
    $draw->render();
    
} catch (Exception $e) {
    // 如果出错，输出错误信息
    header('Content-Type: text/plain');
    echo "生成 SVG 时发生错误: " . $e->getMessage() . "\n";
    echo "错误详情:\n" . $e->getTraceAsString();
}
?>
