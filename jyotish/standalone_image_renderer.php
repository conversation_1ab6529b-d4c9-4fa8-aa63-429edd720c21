<?php
/**
 * 独立的图像渲染器 - 不依赖外部 Utility 类
 */

namespace Jyotish\Draw\Renderer;

/**
 * 独立的图像渲染器类
 */
class StandaloneImage
{
    protected $Resource = null;
    protected $rendererName = 'image';
    
    // 选项属性
    protected $optionTopOffset = 0;
    protected $optionLeftOffset = 0;
    protected $optionFontSize = 10;
    protected $optionFontName = '';
    protected $optionFontColor = '000';
    protected $optionTextAlign = 'left';
    protected $optionTextValign = 'bottom';
    protected $optionTextOrientation = 0;
    protected $optionStrokeWidth = 1;
    protected $optionStrokeColor = '000';
    protected $optionFillColor = 'fff';
    
    // 存储选项的数组
    protected $storedOptions = array();
    
    /**
     * 构造函数
     */
    public function __construct($width, $height)
    {
        $this->Resource = imagecreatetruecolor($width, $height);
        $color = $this->allocateColor($this->Resource, 255, 255, 255);
        imagefill($this->Resource, 0, 0, $color);
    }
    
    /**
     * 设置选项
     */
    public function setOptions(array $options = null)
    {
        if (is_null($options)) {
            return $this;
        }
        
        // 存储选项
        $this->storedOptions = array_merge($this->storedOptions, $options);
        
        foreach ($options as $optionName => $optionValue) {
            $methodName = 'setOption' . ucfirst($optionName);
            
            if (method_exists($this, $methodName)) {
                $this->$methodName($optionValue);
            } else {
                $propertyName = 'option' . ucfirst($optionName);
                if (property_exists($this, $propertyName)) {
                    $this->$propertyName = $optionValue;
                }
            }
        }
        
        return $this;
    }
    
    /**
     * 获取存储的选项
     */
    public function getStoredOptions()
    {
        return $this->storedOptions;
    }
    
    /**
     * 获取特定选项
     */
    public function getStoredOption($optionName, $default = null)
    {
        return isset($this->storedOptions[$optionName]) ? $this->storedOptions[$optionName] : $default;
    }
    
    /**
     * 绘制多边形
     */
    public function drawPolygon(array $points, array $options = null)
    {
        $this->setOptions($options);
        
        $colorRgb = $this->htmlToRgb($this->optionStrokeColor);
        $color = $this->allocateColor($this->Resource, $colorRgb['r'], $colorRgb['g'], $colorRgb['b']);
        
        imagesetthickness($this->Resource, $this->optionStrokeWidth);
        
        $numPoints = count($points) / 2;
        imagepolygon($this->Resource, $points, $numPoints, $color);
    }
    
    /**
     * 绘制文本
     */
    public function drawText($text, $x = 0, $y = 0, array $options = null)
    {
        $this->setOptions($options);
        
        $colorRgb = $this->htmlToRgb($this->optionFontColor);
        $color = $this->allocateColor($this->Resource, $colorRgb['r'], $colorRgb['g'], $colorRgb['b']);
        
        if ($this->optionFontName == null) {
            $this->optionFontName = 3;
        }
        
        if (is_numeric($this->optionFontName)) {
            $fontWidth = imagefontwidth($this->optionFontName);
            $fontHeight = imagefontheight($this->optionFontName);
            
            switch ($this->optionTextAlign) {
                case 'left':
                    $positionX = $x;
                    break;
                case 'center':
                    $positionX = $x - ceil(($fontWidth * strlen($text)) / 2);
                    break;
                case 'right':
                    $positionX = $x - ($fontWidth * strlen($text));
                    break;
            }
            
            switch ($this->optionTextValign) {
                case 'top':
                    $positionY = $y;
                    break;
                case 'middle':
                    $positionY = $y - $fontHeight / 2;
                    break;
                case 'bottom':
                    $positionY = $y - $fontHeight + 1;
                    break;
            }
            
            imagestring($this->Resource, $this->optionFontName, $positionX, $positionY, $text, $color);
        }
    }
    
    /**
     * 渲染图像
     */
    public function render()
    {
        header('Content-type: image/png');
        imagepng($this->Resource);
        imagedestroy($this->Resource);
    }
    
    /**
     * 内联的 HTML 颜色转 RGB 方法
     */
    private function htmlToRgb($htmlColor)
    {
        $htmlColor = ltrim($htmlColor, '#');
        
        if (strlen($htmlColor) === 3) {
            $htmlColor = $htmlColor[0] . $htmlColor[0] . 
                        $htmlColor[1] . $htmlColor[1] . 
                        $htmlColor[2] . $htmlColor[2];
        }
        
        $r = hexdec(substr($htmlColor, 0, 2));
        $g = hexdec(substr($htmlColor, 2, 2));
        $b = hexdec(substr($htmlColor, 4, 2));
        
        return ['r' => $r, 'g' => $g, 'b' => $b];
    }
    
    /**
     * 分配颜色
     */
    private function allocateColor($image, $r, $g, $b, $alpha = 100)
    {
        $alphaValue = (127 / 100) * (100 - $alpha);
        return imagecolorallocatealpha($image, $r, $g, $b, $alphaValue);
    }
    
    // Setter 方法
    public function setOptionFontColor($value)
    {
        $this->optionFontColor = $value;
        return $this;
    }
    
    public function setOptionStrokeColor($value)
    {
        $this->optionStrokeColor = $value;
        return $this;
    }
    
    public function setOptionStrokeWidth($value)
    {
        $this->optionStrokeWidth = $value;
        return $this;
    }
    
    public function setOptionTextAlign($value)
    {
        $this->optionTextAlign = $value;
        return $this;
    }
    
    public function setOptionFontSize($value)
    {
        $this->optionFontSize = $value;
        return $this;
    }
}

/**
 * 独立的 Draw 类
 */
class StandaloneDraw
{
    const RENDERER_IMAGE = 'image';
    
    protected $Renderer = null;
    
    public function __construct($width, $height, $renderer = self::RENDERER_IMAGE)
    {
        $this->Renderer = new StandaloneImage($width, $height);
    }
    
    public function setOptions($options)
    {
        $this->Renderer->setOptions($options);
        return $this;
    }
    
    public function drawText($text, $x, $y, array $options = null)
    {
        $this->Renderer->drawText($text, $x, $y, $options);
    }
    
    public function drawPolygon(array $points, array $options = null)
    {
        $this->Renderer->drawPolygon($points, $options);
    }
    
    public function render()
    {
        $this->Renderer->render();
    }
    
    public function getRenderer()
    {
        return $this->Renderer;
    }
}
?>
