<?php
/**
 * 测试修复后的演示
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <title>修复后的演示测试</title>\n";
echo "    <style>\n";
echo "        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n";
echo "        .success { color: #008000; font-weight: bold; }\n";
echo "        .error { color: #ff0000; font-weight: bold; }\n";
echo "        .info { color: #0066cc; }\n";
echo "        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>🎉 修复后的演示测试</h1>\n";

echo "<div class='section'>\n";
echo "<h2>1. 测试修复后的 demo_draw.php</h2>\n";

try {
    // 包含修复后的演示文件
    ob_start();
    include 'demo_draw.php';
    $output = ob_get_clean();
    
    echo "<span class='success'>✓</span> demo_draw.php 运行成功！<br>\n";
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #f9f9f9;'>\n";
    echo $output;
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>✗</span> demo_draw.php 运行失败: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>2. ✅ 修复说明</h2>\n";

echo "<p><strong>修复的问题：</strong></p>\n";
echo "<ul>\n";
echo "<li>🔧 替换了原来的 <code>Draw</code> 类为 <code>StandaloneDraw</code> 类</li>\n";
echo "<li>🔧 使用独立渲染器，不依赖 <code>Jyotish\\Base\\Utility</code> 类</li>\n";
echo "<li>🔧 内置了所有必要的功能，避免外部依赖</li>\n";
echo "</ul>\n";

echo "<p><strong>修改的文件：</strong></p>\n";
echo "<ul>\n";
echo "<li>📝 <code>jyotish/demo_draw.php</code> - 更新了类引用</li>\n";
echo "<li>📝 <code>jyotish/standalone_image_renderer.php</code> - 添加了独立渲染器</li>\n";
echo "</ul>\n";

echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>3. 测试图像输出</h2>\n";
echo "<p>查看实际的图像输出：</p>\n";
echo "<p><a href='demo_image_output.php' target='_blank'>🖼️ 查看演示图像输出</a></p>\n";
echo "</div>\n";

echo "<p><em>测试完成时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
echo "</body>\n";
echo "</html>\n";
?>
