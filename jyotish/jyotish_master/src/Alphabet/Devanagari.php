<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Alphabet;

/**
 * Devanagari class
 *
 * <AUTHOR> <<EMAIL>>
 */
class Devanagari extends \Jyotish\Alphabet\Language
{	
    /**
     * consonants unicode
     */
    const ka    = '0915';
    const kha   = '0916';
    const ga    = '0917';
    const gha   = '0918';
    const nga   = '0919';
    const ca    = '091A';
    const cha   = '091B';
    const ja    = '091C';
    const jha   = '091D';
    const nya   = '091E';
    const tta   = '091F';
    const ttha  = '0920';
    const dda   = '0921';
    const ddha  = '0922';
    const nna   = '0923';
    const ta    = '0924';
    const tha   = '0925';
    const da    = '0926';
    const dha   = '0927';
    const na    = '0928';
    const pa    = '092A';
    const pha   = '092B';
    const ba    = '092C';
    const bha   = '092D';
    const ma    = '092E';
    const ya    = '092F';
    const ra    = '0930';
    const la    = '0932';
    const va    = '0935';
    const sha   = '0936';
    const ssa   = '0937';
    const sa    = '0938';
    const ha    = '0939';

    /**
     * Additional consonants unicode
     */
    const qa    = '0958';
    const khha  = '0959';
    const ghha  = '095A';
    const za    = '095B';
    const rra   = '095C';
    const rrha  = '095D';
    const fa    = '095E';
    const yya   = '095F';

    /**
     * Vowels unicode
     */
    const _a    = '0905';
    const _aa   = '0906';
    const aa    = '093E';
    const _i    = '0907';
    const i     = '093F';
    const _ii   = '0908';
    const ii    = '0940';
    const _u    = '0909';
    const u     = '0941';
    const _uu   = '090A';
    const uu    = '0942';
    const _r    = '090B';
    const r     = '0943';
    const _rr   = '0960';
    const rr    = '0944';
    const _l    = '090C';
    const l     = '0962';
    const _ll   = '0961';
    const ll    = '0963';
    const _e    = '090F';
    const e     = '0947';
    const _ai   = '0910';
    const ai    = '0948';
    const _o    = '0913';
    const o     = '094B';
    const _au   = '0914';
    const au    = '094C';

    /**
     * Digits unicode
     */
    const  d0   = '0966';
    const  d1   = '0967';
    const  d2   = '0968';
    const  d3   = '0969';
    const  d4   = '096A';
    const  d5   = '096B';
    const  d6   = '096C';
    const  d7   = '096D';
    const  d8   = '096E';
    const  d9   = '096F';

    /**
     * Other symbols unicode
     */
    const anunasika = '0901';
    const anusvara  = '0902';
    const visarga   = '0903';
    const virama    = '094D';
}