<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Panchanga\Yoga;

/**
 * Data Yoga class.
 *
 * <AUTHOR> <<EMAIL>>
 */
class Yoga
{
    public static $yoga = [
        1 => 'Vishkambha',
        2 => 'Preeti',
        3 => 'Ayushman',
        4 => 'Soubhagya',
        5 => 'Shobha<PERSON>',
        6 => 'Athiganda',
        7 => '<PERSON><PERSON><PERSON>',
        8 => '<PERSON><PERSON>iti',
        9 => 'Shoola',
        10 => '<PERSON>anda',
        11 => 'Vrid<PERSON>',
        12 => 'Dhruva',
        13 => 'Vyagh<PERSON>',
        14 => '<PERSON><PERSON><PERSON>',
        15 => 'V<PERSON>ra',
        16 => 'Sid<PERSON>',
        17 => 'Vyateepat',
        18 => 'Vare<PERSON>ana',
        19 => 'Parigha',
        20 => 'Shiva',
        21 => '<PERSON><PERSON>',
        22 => '<PERSON><PERSON>',
        23 => '<PERSON><PERSON>',
        24 => '<PERSON><PERSON>',
        25 => '<PERSON>rahma',
        26 => 'Indra',
        27 => 'Vaid<PERSON>iti',
    ];

    /**
     * Returns the requested instance of yoga class.
     * 
     * @param int $key The key of yoga
     * @param null|array $options Options to set (optional)
     * @return the requested instance of yoga class
     * @throws Exception\InvalidArgumentException
     */
    public static function getInstance($key, array $options = null)
    {
        if (!array_key_exists($key, self::$yoga)) {
            throw new \Jyotish\Panchanga\Exception\InvalidArgumentException("Yoga with the key '$key' does not exist.");
        }
        
        $yogaClass = 'Jyotish\Panchanga\Yoga\Object\Y' . $key;
        $yogaObject = new $yogaClass($options);

        return $yogaObject;
    }
}