<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyo<PERSON><PERSON>\Panchanga\Tithi\Object;

use <PERSON><PERSON><PERSON><PERSON>\Panchanga\Karana\Karana;

/**
 * Class of tithi 22.
 *
 * <AUTHOR> <<EMAIL>>
 */
class T22 extends TithiObject
{
    /**
     * Tithi key
     * 
     * @var int
     */
    protected $tithiKey = 22;

    /**
     * Devanagari number 7 in transliteration.
     * 
     * @var array
     * @see Jyotish\Alphabet\Devanagari
     */
    protected $tithiTranslit = ['d7'];

    /**
     * Karana of tithi.
     * 
     * @var string
     */
    protected $tithiKarana = [
        1 => Karana::NAME_VISHTI,
        2 => Karana::NAME_BAVA
    ];
}