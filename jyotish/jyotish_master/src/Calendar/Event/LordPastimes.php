<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Calendar\Event;

/**
 * Events in the Pastimes of the Lord and His Associates.
 *
 * <AUTHOR> <<EMAIL>>
 */
class LordPastimes extends EventBase
{
    public static $eventsTitle = 'Events in the Pastimes of the Lord and His Associates';

    public static $eventsList = [
        [
            self::COL_NAME => 'Festival of Jagannatha Mishra',
            self::COL_MASA => 1,
            self::COL_TITHI => 1,
        ],
        [
            self::COL_NAME => 'Damanakaropana Dvadashi',
            self::COL_MASA => 1,
            self::COL_TITHI => 12,
        ],
        [
            self::COL_NAME => 'Shri Krishna Vasanta Rasa',
            self::COL_MASA => 1,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Shri Balarama Rasayatra',
            self::COL_MASA => 1,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => '<PERSON><PERSON><PERSON> starts',
            self::COL_MASA => 2,
            self::COL_TITHI => 3,
        ],
        [
            self::COL_NAME => 'Shrimati Sita Devi (Appearance)',
            self::COL_MASA => 2,
            self::COL_TITHI => 9,
        ],
        [
            self::COL_NAME => 'Rukmini Dvadashi',
            self::COL_MASA => 2,
            self::COL_TITHI => 12,
        ],
        [
            self::COL_NAME => 'Krishna Phula Dola',
            self::COL_MASA => 2,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Srhi Shri Radha Ramana Devaji (Appearance)',
            self::COL_MASA => 2,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Panihati Ciba Dahi Utsava',
            self::COL_MASA => 3,
            self::COL_TITHI => 13,
        ],
        [
            self::COL_NAME => 'Snana Yatra',
            self::COL_MASA => 3,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Gundicha Marjana',
            self::COL_MASA => 4,
            self::COL_TITHI => 16,
        ],
        [
            self::COL_NAME => 'Ratha Yatra',
            self::COL_MASA => 4,
            self::COL_TITHI => 17,
        ],
        [
            self::COL_NAME => 'Hera Panchami',
            self::COL_MASA => 4,
            self::COL_TITHI => 20,
        ],
        [
            self::COL_NAME => 'Return Ratha',
            self::COL_MASA => 4,
            self::COL_TITHI => 24,
        ],
        [
            self::COL_NAME => 'Guru Purnima',
            self::COL_MASA => 4,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Radha Govinda Jhulana Yatra begins',
            self::COL_MASA => 5,
            self::COL_TITHI => 11,
        ],
        [
            self::COL_NAME => 'Jhulana Yatra ends',
            self::COL_MASA => 5,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Lalita Shashti',
            self::COL_MASA => 6,
            self::COL_TITHI => 6,
        ],
        [
            self::COL_NAME => 'Nandotsava',
            self::COL_MASA => 6,
            self::COL_TITHI => 9,
        ],
        [
            self::COL_NAME => 'Ananta Chaturdashi Vrata',
            self::COL_MASA => 6,
            self::COL_TITHI => 14,
        ],
        [
            self::COL_NAME => 'Shri Vishvarupa Mahotsava',
            self::COL_MASA => 6,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Ramachandra Vijayotsava',
            self::COL_MASA => 7,
            self::COL_TITHI => 10,
        ],
        [
            self::COL_NAME => 'Shri Krishna Saradiya Rasayatra',
            self::COL_MASA => 7,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Go Puja',
            self::COL_NAME => 8,
            self::COL_TITHI => 16,
        ],
        [
            self::COL_NAME => 'Appearance of Radha Kunda',
            self::COL_MASA => 8,
            self::COL_TITHI => 23,
        ],
        [
            self::COL_NAME => 'Bahulashtami',
            self::COL_MASA => 8,
            self::COL_TITHI => 23,
        ],
        [
            self::COL_NAME => 'Dipavali',
            self::COL_MASA => 8,
            self::COL_TITHI => 30,
        ],
        [
            self::COL_NAME => 'Bali Daityaraja Puja',
            self::COL_MASA => 8,
            self::COL_TITHI => 1,
        ],
        [
            self::COL_NAME => 'Gopashtami',
            self::COL_MASA => 8,
            self::COL_TITHI => 8,
        ],
        [
            self::COL_NAME => 'Shri Krishna Rasayatra',
            self::COL_MASA => 8,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Tulasi-Shilagrama Vivaha',
            self::COL_MASA => 8,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Katyayani vrata begins',
            self::COL_MASA => 9,
            self::COL_TITHI => 16
        ],
        [
            self::COL_NAME => 'Odana Shashti',
            self::COL_MASA => 9,
            self::COL_TITHI => 6,
        ],
        [
            self::COL_NAME => 'Advent of Shrimad Bhagavad-gita',
            self::COL_MASA => 9,
            self::COL_TITHI => 11,
        ],
        [
            self::COL_NAME => 'Katyayani vrata ends',
            self::COL_MASA => 9,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Shri Krishna Pushya Abhisheka',
            self::COL_MASA => 10,
            self::COL_TITHI => 15,
        ],
        [
            self::COL_NAME => 'Vasanta Panchami',
            self::COL_MASA => 11,
            self::COL_TITHI => 5,
        ],
        [
            self::COL_NAME => 'Bhishmashtami',
            self::COL_MASA => 11,
            self::COL_TITHI => 8,
        ],
        [
            self::COL_NAME => 'Shri Krishna Madhura Utsava',
            self::COL_MASA => 11,
            self::COL_TITHI => 15,
        ]
    ];
}