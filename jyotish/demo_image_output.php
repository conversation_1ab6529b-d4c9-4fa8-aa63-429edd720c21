<?php
/**
 * Jyotish Draw 图像输出演示
 * 
 * 这个演示展示如何使用 Draw 类生成实际的图像文件
 */

// 包含必要的文件
require_once 'jyotish/src/Draw.php';
require_once 'jyotish/src/Renderer/AbstractRenderer.php';
require_once 'jyotish/src/Renderer/Image.php';

use Jyotish\Draw\Draw;

// 检查是否通过 URL 参数请求图像输出
if (isset($_GET['output']) && $_GET['output'] === 'image') {
    // 生成并输出图像
    generateImage();
    exit;
}

// 显示 HTML 页面
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jyotish Draw 图像演示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .demo-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .code-block { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        img { border: 1px solid #ccc; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Jyotish Draw 图像生成演示</h1>
    
    <div class="demo-section">
        <h2>功能说明</h2>
        <p>这个演示展示了如何使用 Jyotish\Draw\Draw 类生成图像。主要功能包括：</p>
        <ul>
            <li>创建指定尺寸的画布</li>
            <li>绘制文本（支持不同字体、颜色、对齐方式）</li>
            <li>绘制多边形（三角形、矩形、复杂形状）</li>
            <li>设置各种绘图选项</li>
        </ul>
    </div>
    
    <div class="demo-section">
        <h2>生成的图像示例</h2>
        <p>点击下面的链接查看生成的图像：</p>
        <p><a href="?output=image" target="_blank">查看生成的演示图像</a></p>
        <p>或者直接在下方查看：</p>
        <img src="?output=image" alt="Jyotish Draw 演示图像" />
    </div>
    
    <div class="demo-section">
        <h2>代码示例</h2>
        <p>以下是生成上述图像的代码：</p>
        <div class="code-block">
<pre><?php echo htmlspecialchars('
// 创建 400x300 像素的图像渲染器
$draw = new Draw(400, 300, Draw::RENDERER_IMAGE);

// 绘制标题文本
$draw->drawText("Jyotish Draw 演示", 200, 30, [
    "fontColor" => "#000080",
    "fontSize" => 16,
    "textAlign" => "center"
]);

// 绘制三角形
$trianglePoints = [200, 80, 150, 150, 250, 150];
$draw->drawPolygon($trianglePoints, [
    "strokeColor" => "#FF0000",
    "strokeWidth" => 2
]);

// 绘制矩形
$rectanglePoints = [50, 180, 350, 180, 350, 250, 50, 250];
$draw->drawPolygon($rectanglePoints, [
    "strokeColor" => "#00AA00",
    "strokeWidth" => 3
]);

// 输出图像
$draw->render();
'); ?></pre>
        </div>
    </div>
    
    <div class="demo-section">
        <h2>使用说明</h2>
        <ol>
            <li>确保服务器已安装 GD 扩展</li>
            <li>确保所有必要的 Jyotish 类文件都已正确包含</li>
            <li>调用 <code>render()</code> 方法会直接输出图像到浏览器</li>
            <li>如需保存图像文件，需要使用输出缓冲或修改渲染器</li>
        </ol>
    </div>
</body>
</html>

<?php

function generateImage() {
    try {
        // 创建 400x300 像素的图像
        $draw = new Draw(400, 300, Draw::RENDERER_IMAGE);
        
        // 绘制标题
        $draw->drawText("Jyotish Draw 演示", 200, 30, [
            'fontColor' => '#000080',
            'textAlign' => 'center'
        ]);
        
        // 绘制说明文字
        $draw->drawText("文本绘制示例", 50, 60, [
            'fontColor' => '#333333'
        ]);
        
        // 绘制三角形
        $trianglePoints = [
            200, 80,   // 顶点
            150, 150,  // 左下
            250, 150   // 右下
        ];
        $draw->drawPolygon($trianglePoints, [
            'strokeColor' => '#FF0000',
            'strokeWidth' => 2
        ]);
        
        // 在三角形旁边添加标签
        $draw->drawText("三角形", 270, 120, [
            'fontColor' => '#FF0000'
        ]);
        
        // 绘制矩形
        $rectanglePoints = [
            50, 180,   // 左上
            350, 180,  // 右上
            350, 250,  // 右下
            50, 250    // 左下
        ];
        $draw->drawPolygon($rectanglePoints, [
            'strokeColor' => '#00AA00',
            'strokeWidth' => 3
        ]);
        
        // 在矩形内部添加文字
        $draw->drawText("矩形区域", 200, 215, [
            'fontColor' => '#00AA00',
            'textAlign' => 'center'
        ]);
        
        // 绘制一个复杂的多边形（五角星的外轮廓）
        $starPoints = [
            200, 270,  // 顶点
            210, 290,  // 右上
            230, 290,  // 右
            215, 305,  // 右下
            220, 325,  // 右底
            200, 315,  // 底
            180, 325,  // 左底
            185, 305,  // 左下
            170, 290,  // 左
            190, 290   // 左上
        ];
        $draw->drawPolygon($starPoints, [
            'strokeColor' => '#8000FF',
            'strokeWidth' => 2
        ]);
        
        // 添加版权信息
        $draw->drawText("© Jyotish Draw Demo", 350, 290, [
            'fontColor' => '#666666',
            'textAlign' => 'right'
        ]);
        
        // 渲染并输出图像
        $draw->render();
        
    } catch (Exception $e) {
        // 如果出错，输出错误信息图像
        header('Content-Type: text/plain');
        echo "生成图像时发生错误: " . $e->getMessage();
    }
}
?>
