<?php
/**
 * J<PERSON><PERSON>h Draw 功能测试脚本
 * 
 * 这个脚本测试 Draw 类的各种功能，并提供详细的反馈
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Jyotish Draw 功能测试</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 15px 0; padding: 10px; border: 1px solid #ddd; }
    .success { color: #008000; font-weight: bold; }
    .error { color: #ff0000; font-weight: bold; }
    .warning { color: #ff8800; font-weight: bold; }
    .info { color: #0066cc; }
    pre { background: #f5f5f5; padding: 10px; margin: 5px 0; }
</style>\n";

// 测试 1: 检查文件是否存在
echo "<div class='test-section'>\n";
echo "<h2>测试 1: 检查必要文件</h2>\n";

$requiredFiles = [
    'jyotish/src/Draw.php',
    'jyotish/src/Renderer/AbstractRenderer.php',
    'jyotish/src/Renderer/Image.php',
    'jyotish/src/Renderer/Svg.php'
];

$allFilesExist = true;
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✓</span> 文件存在: $file<br>\n";
    } else {
        echo "<span class='error'>✗</span> 文件缺失: $file<br>\n";
        $allFilesExist = false;
    }
}

if (!$allFilesExist) {
    echo "<span class='warning'>警告: 某些必要文件缺失，测试可能无法完全进行。</span><br>\n";
}
echo "</div>\n";

// 测试 2: 检查 PHP 扩展
echo "<div class='test-section'>\n";
echo "<h2>测试 2: 检查 PHP 扩展</h2>\n";

if (extension_loaded('gd')) {
    echo "<span class='success'>✓</span> GD 扩展已加载<br>\n";
    $gdInfo = gd_info();
    echo "<span class='info'>GD 版本: " . $gdInfo['GD Version'] . "</span><br>\n";
    echo "<span class='info'>支持 PNG: " . ($gdInfo['PNG Support'] ? '是' : '否') . "</span><br>\n";
    echo "<span class='info'>支持 JPEG: " . ($gdInfo['JPEG Support'] ? '是' : '否') . "</span><br>\n";
} else {
    echo "<span class='error'>✗</span> GD 扩展未加载，图像渲染功能将不可用<br>\n";
}
echo "</div>\n";

// 测试 3: 尝试包含文件
echo "<div class='test-section'>\n";
echo "<h2>测试 3: 包含必要文件</h2>\n";

try {
    if (file_exists('jyotish/src/Draw.php')) {
        // 这里我们只是检查语法，不实际包含，因为可能有依赖问题
        $content = file_get_contents('jyotish/src/Draw.php');
        if (strpos($content, 'class Draw') !== false) {
            echo "<span class='success'>✓</span> Draw.php 文件格式正确<br>\n";
        } else {
            echo "<span class='error'>✗</span> Draw.php 文件格式异常<br>\n";
        }
    }
    
    // 检查命名空间
    if (strpos($content, 'namespace Jyotish\\Draw;') !== false) {
        echo "<span class='success'>✓</span> 命名空间正确<br>\n";
    } else {
        echo "<span class='warning'>⚠</span> 命名空间可能不正确<br>\n";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 包含文件时出错: " . $e->getMessage() . "<br>\n";
}
echo "</div>\n";

// 测试 4: 分析类结构
echo "<div class='test-section'>\n";
echo "<h2>测试 4: 分析 Draw 类结构</h2>\n";

if (isset($content)) {
    // 检查常量
    if (strpos($content, 'RENDERER_IMAGE') !== false) {
        echo "<span class='success'>✓</span> 找到 RENDERER_IMAGE 常量<br>\n";
    }
    if (strpos($content, 'RENDERER_SVG') !== false) {
        echo "<span class='success'>✓</span> 找到 RENDERER_SVG 常量<br>\n";
    }
    
    // 检查主要方法
    $methods = ['__construct', 'setOptions', 'drawText', 'drawPolygon', 'drawChakra', 'render'];
    foreach ($methods as $method) {
        if (strpos($content, "function $method") !== false || strpos($content, "public function $method") !== false) {
            echo "<span class='success'>✓</span> 找到方法: $method<br>\n";
        } else {
            echo "<span class='warning'>⚠</span> 未找到方法: $method<br>\n";
        }
    }
}
echo "</div>\n";

// 测试 5: 创建示例代码
echo "<div class='test-section'>\n";
echo "<h2>测试 5: 使用示例</h2>\n";

echo "<h3>基本使用示例：</h3>\n";
echo "<pre>";
echo htmlspecialchars('<?php
// 1. 包含必要文件
require_once "jyotish/src/Draw.php";
require_once "jyotish/src/Renderer/AbstractRenderer.php";
require_once "jyotish/src/Renderer/Image.php";

// 2. 创建 Draw 实例
$draw = new \Jyotish\Draw\Draw(400, 300, \Jyotish\Draw\Draw::RENDERER_IMAGE);

// 3. 设置选项
$draw->setOptions([
    "fontColor" => "#000000",
    "strokeColor" => "#FF0000",
    "strokeWidth" => 2
]);

// 4. 绘制文本
$draw->drawText("Hello Jyotish!", 200, 50, [
    "textAlign" => "center",
    "fontSize" => 16
]);

// 5. 绘制三角形
$trianglePoints = [200, 100, 150, 200, 250, 200];
$draw->drawPolygon($trianglePoints, ["strokeColor" => "#00FF00"]);

// 6. 渲染输出（这会输出 PNG 图像）
$draw->render();
?>');
echo "</pre>\n";

echo "<h3>SVG 渲染示例：</h3>\n";
echo "<pre>";
echo htmlspecialchars('<?php
// 创建 SVG 渲染器
$svgDraw = new \Jyotish\Draw\Draw(300, 200, \Jyotish\Draw\Draw::RENDERER_SVG);

// 绘制内容
$svgDraw->drawText("SVG 示例", 150, 50, ["textAlign" => "center"]);

// 绘制矩形
$rectPoints = [50, 80, 250, 80, 250, 150, 50, 150];
$svgDraw->drawPolygon($rectPoints, ["strokeColor" => "#0000FF"]);

// 渲染 SVG
$svgDraw->render();
?>');
echo "</pre>\n";
echo "</div>\n";

// 测试 6: 错误处理示例
echo "<div class='test-section'>\n";
echo "<h2>测试 6: 错误处理</h2>\n";

echo "<h3>常见错误及处理：</h3>\n";
echo "<pre>";
echo htmlspecialchars('<?php
try {
    // 错误 1: 无效的渲染器
    $draw = new \Jyotish\Draw\Draw(100, 100, "invalid_renderer");
} catch (\Jyotish\Draw\Exception\UnexpectedValueException $e) {
    echo "渲染器错误: " . $e->getMessage();
}

try {
    // 错误 2: 无效的选项
    $draw = new \Jyotish\Draw\Draw(100, 100);
    $draw->setOptions([
        "strokeWidth" => -1  // 负数宽度
    ]);
} catch (Exception $e) {
    echo "选项错误: " . $e->getMessage();
}
?>');
echo "</pre>\n";
echo "</div>\n";

// 测试 7: 性能建议
echo "<div class='test-section'>\n";
echo "<h2>测试 7: 性能和使用建议</h2>\n";

echo "<ul>\n";
echo "<li><strong>内存使用：</strong>大图像会消耗更多内存，建议合理设置图像尺寸</li>\n";
echo "<li><strong>渲染选择：</strong>SVG 适合矢量图形，Image 适合位图</li>\n";
echo "<li><strong>字体处理：</strong>使用 TTF 字体需要 FreeType 支持</li>\n";
echo "<li><strong>颜色格式：</strong>推荐使用十六进制颜色代码（如 #FF0000）</li>\n";
echo "<li><strong>坐标系统：</strong>原点 (0,0) 在左上角</li>\n";
echo "</ul>\n";
echo "</div>\n";

// 测试总结
echo "<div class='test-section'>\n";
echo "<h2>测试总结</h2>\n";

if ($allFilesExist && extension_loaded('gd')) {
    echo "<span class='success'>✓ 系统准备就绪，可以使用 Jyotish Draw 功能</span><br>\n";
    echo "<span class='info'>建议：运行上述示例代码来测试实际功能</span><br>\n";
} else {
    echo "<span class='warning'>⚠ 系统未完全准备好</span><br>\n";
    if (!$allFilesExist) {
        echo "<span class='error'>• 缺少必要的类文件</span><br>\n";
    }
    if (!extension_loaded('gd')) {
        echo "<span class='error'>• 缺少 GD 扩展</span><br>\n";
    }
}

echo "<p><strong>下一步：</strong></p>\n";
echo "<ol>\n";
echo "<li>确保所有依赖文件都存在</li>\n";
echo "<li>检查 PHP 环境和扩展</li>\n";
echo "<li>运行简单的绘图测试</li>\n";
echo "<li>根据需要调整配置和选项</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<p><em>测试完成时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
