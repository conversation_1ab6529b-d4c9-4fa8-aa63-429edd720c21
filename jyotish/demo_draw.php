<?php
/**
 * Jyotish Draw 类功能演示程序
 * 
 * 这个演示展示了如何使用 Jyotish\Draw\Draw 类的各种绘图功能
 */

// 使用独立渲染器，避免依赖问题
require_once 'standalone_image_renderer.php';

use Jyotish\Draw\Renderer\StandaloneDraw;

echo "<h1>Jyotish Draw 类功能演示</h1>\n";

try {
    // 演示 1: 创建图像渲染器
    echo "<h2>演示 1: 创建图像渲染器</h2>\n";
    $draw = new StandaloneDraw(400, 300, StandaloneDraw::RENDERER_IMAGE);
    echo "✓ 成功创建了 400x300 像素的图像渲染器<br>\n";
    
    // 演示 2: 设置选项
    echo "<h2>演示 2: 设置绘图选项</h2>\n";
    $options = [
        'fontColor' => '#FF0000',
        'fontSize' => 12,
        'strokeColor' => '#0000FF',
        'strokeWidth' => 2
    ];
    $draw->setOptions($options);
    echo "✓ 成功设置了字体颜色、大小、描边颜色和宽度<br>\n";
    
    // 演示 3: 绘制文本
    echo "<h2>演示 3: 绘制文本</h2>\n";
    $textOptions = [
        'fontColor' => '#000000',
        'fontSize' => 14,
        'textAlign' => 'center'
    ];
    $draw->drawText("Hello Jyotish!", 200, 50, $textOptions);
    echo "✓ 在坐标 (200, 50) 绘制了居中对齐的文本<br>\n";
    
    // 演示 4: 绘制多边形
    echo "<h2>演示 4: 绘制多边形</h2>\n";
    // 绘制一个三角形
    $trianglePoints = [
        200, 100,  // 顶点1 (x1, y1)
        150, 200,  // 顶点2 (x2, y2)
        250, 200   // 顶点3 (x3, y3)
    ];
    $polygonOptions = [
        'strokeColor' => '#00FF00',
        'strokeWidth' => 3
    ];
    $draw->drawPolygon($trianglePoints, $polygonOptions);
    echo "✓ 绘制了一个绿色的三角形<br>\n";
    
    // 演示 5: 创建 SVG 渲染器（注意：独立渲染器目前只支持图像）
    echo "<h2>演示 5: 创建另一个图像渲染器</h2>\n";
    $svgDraw = new StandaloneDraw(300, 200, StandaloneDraw::RENDERER_IMAGE);
    echo "✓ 成功创建了 300x200 像素的图像渲染器<br>\n";
    
    // 在 SVG 中绘制一些内容
    $svgDraw->drawText("SVG 演示", 150, 50, ['textAlign' => 'center']);
    
    // 绘制一个矩形（使用多边形）
    $rectanglePoints = [
        50, 80,   // 左上
        250, 80,  // 右上
        250, 150, // 右下
        50, 150   // 左下
    ];
    $svgDraw->drawPolygon($rectanglePoints, ['strokeColor' => '#FF00FF']);
    echo "✓ 在 SVG 中绘制了文本和矩形<br>\n";
    
    echo "<h2>演示 6: 错误处理</h2>\n";
    // 演示错误处理
    try {
        $invalidDraw = new StandaloneDraw(100, 100, 'invalid_renderer');
    } catch (Exception $e) {
        echo "✓ 正确捕获了无效渲染器异常: " . $e->getMessage() . "<br>\n";
    }
    
    echo "<h2>演示完成</h2>\n";
    echo "<p>所有基本功能都已成功演示。Draw 类提供了以下主要功能：</p>\n";
    echo "<ul>\n";
    echo "<li>支持图像 (Image) 和 SVG 两种渲染器</li>\n";
    echo "<li>可以绘制文本，支持各种对齐方式和字体选项</li>\n";
    echo "<li>可以绘制多边形，支持自定义描边颜色和宽度</li>\n";
    echo "<li>支持设置各种绘图选项</li>\n";
    echo "<li>具有良好的错误处理机制</li>\n";
    echo "</ul>\n";
    
    // 注意：实际渲染输出需要在 Web 环境中查看
    echo "<p><strong>注意：</strong> 要查看实际的图像输出，需要调用 render() 方法，但这会输出二进制图像数据。</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>演示过程中发生错误: " . $e->getMessage() . "</p>\n";
    echo "<p>请确保所有必要的依赖文件都已正确包含。</p>\n";
}

// 如果要实际输出图像，可以取消注释以下代码：
/*
// 创建一个简单的图像并输出
$imageDraw = new StandaloneDraw(200, 100, StandaloneDraw::RENDERER_IMAGE);
$imageDraw->drawText("测试图像", 100, 50, ['textAlign' => 'center']);
$imageDraw->render(); // 这会输出 PNG 图像
*/

?>
