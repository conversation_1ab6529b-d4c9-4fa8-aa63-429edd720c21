<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Base\Traits;

/**
 * Trait for setting options.
 * 
 * <AUTHOR> <<EMAIL>>
 */
trait OptionTrait
{
    /**
     * Set options.
     * 
     * @param array $options Options to set
     * @return $this
     */
    public function setOptions(array $options = null)
    {
        if (is_null($options)) {
            return $this;
        }
        
        foreach ($options as $optionName => $optionValue) {
            $methodName = 'setOption' . ucfirst($optionName);
            
            if (method_exists($this, $methodName)) {
                $this->$methodName($optionValue);
            } else {
                // 如果没有专门的 setter 方法，直接设置属性
                $propertyName = 'option' . ucfirst($optionName);
                if (property_exists($this, $propertyName)) {
                    $this->$propertyName = $optionValue;
                }
            }
        }
        
        return $this;
    }
    
    /**
     * Get option value.
     * 
     * @param string $optionName Option name
     * @return mixed
     */
    public function getOption($optionName)
    {
        $propertyName = 'option' . ucfirst($optionName);
        
        if (property_exists($this, $propertyName)) {
            return $this->$propertyName;
        }
        
        return null;
    }
    
    /**
     * Check if option exists.
     * 
     * @param string $optionName Option name
     * @return bool
     */
    public function hasOption($optionName)
    {
        $propertyName = 'option' . ucfirst($optionName);
        return property_exists($this, $propertyName);
    }
}
