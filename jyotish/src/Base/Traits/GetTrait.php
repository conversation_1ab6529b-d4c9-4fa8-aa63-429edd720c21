<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Base\Traits;

/**
 * Trait for getting properties.
 * 
 * <AUTHOR> <<EMAIL>>
 */
trait GetTrait
{
    /**
     * Get property value.
     * 
     * @param string $propertyName Property name
     * @return mixed
     * @throws \InvalidArgumentException
     */
    public function __get($propertyName)
    {
        if (property_exists($this, $propertyName)) {
            return $this->$propertyName;
        }
        
        throw new \InvalidArgumentException("Property '$propertyName' does not exist.");
    }
    
    /**
     * Check if property exists.
     * 
     * @param string $propertyName Property name
     * @return bool
     */
    public function __isset($propertyName)
    {
        return property_exists($this, $propertyName);
    }
    
    /**
     * Get all properties as array.
     * 
     * @return array
     */
    public function toArray()
    {
        $reflection = new \ReflectionClass($this);
        $properties = $reflection->getProperties();
        $result = [];
        
        foreach ($properties as $property) {
            $property->setAccessible(true);
            $result[$property->getName()] = $property->getValue($this);
        }
        
        return $result;
    }
}
