<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Base;

/**
 * Utility class with helper methods.
 * 
 * <AUTHOR> <<EMAIL>>
 */
class Utility
{
    /**
     * Convert HTML color to RGB array.
     * 
     * @param string $htmlColor HTML color code (e.g., '#FF0000' or 'FF0000')
     * @return array RGB array with keys 'r', 'g', 'b'
     * @throws \InvalidArgumentException
     */
    public static function htmlToRgb($htmlColor)
    {
        // Remove # if present
        $htmlColor = ltrim($htmlColor, '#');
        
        // Validate hex color
        if (!preg_match('/^[0-9A-Fa-f]{3}$|^[0-9A-Fa-f]{6}$/', $htmlColor)) {
            throw new \InvalidArgumentException("Invalid HTML color format: $htmlColor");
        }
        
        // Convert 3-digit hex to 6-digit
        if (strlen($htmlColor) === 3) {
            $htmlColor = $htmlColor[0] . $htmlColor[0] . 
                        $htmlColor[1] . $htmlColor[1] . 
                        $htmlColor[2] . $htmlColor[2];
        }
        
        // Convert to RGB
        $r = hexdec(substr($htmlColor, 0, 2));
        $g = hexdec(substr($htmlColor, 2, 2));
        $b = hexdec(substr($htmlColor, 4, 2));
        
        return [
            'r' => $r,
            'g' => $g,
            'b' => $b
        ];
    }
    
    /**
     * Convert RGB to HTML color.
     * 
     * @param int $r Red component (0-255)
     * @param int $g Green component (0-255)
     * @param int $b Blue component (0-255)
     * @return string HTML color code
     */
    public static function rgbToHtml($r, $g, $b)
    {
        $r = max(0, min(255, intval($r)));
        $g = max(0, min(255, intval($g)));
        $b = max(0, min(255, intval($b)));
        
        return sprintf('#%02X%02X%02X', $r, $g, $b);
    }
    
    /**
     * Convert RGB array to HTML color.
     * 
     * @param array $rgb RGB array with keys 'r', 'g', 'b'
     * @return string HTML color code
     */
    public static function rgbArrayToHtml(array $rgb)
    {
        return self::rgbToHtml($rgb['r'], $rgb['g'], $rgb['b']);
    }
    
    /**
     * Validate color format.
     * 
     * @param string $color Color to validate
     * @return bool
     */
    public static function isValidColor($color)
    {
        try {
            self::htmlToRgb($color);
            return true;
        } catch (\InvalidArgumentException $e) {
            return false;
        }
    }
    
    /**
     * Get color brightness (0-255).
     * 
     * @param string $htmlColor HTML color code
     * @return float Brightness value
     */
    public static function getColorBrightness($htmlColor)
    {
        $rgb = self::htmlToRgb($htmlColor);
        
        // Calculate brightness using standard formula
        return ($rgb['r'] * 0.299 + $rgb['g'] * 0.587 + $rgb['b'] * 0.114);
    }
    
    /**
     * Check if color is dark.
     * 
     * @param string $htmlColor HTML color code
     * @param float $threshold Brightness threshold (default: 128)
     * @return bool
     */
    public static function isColorDark($htmlColor, $threshold = 128)
    {
        return self::getColorBrightness($htmlColor) < $threshold;
    }
    
    /**
     * Get contrasting color (black or white).
     * 
     * @param string $htmlColor HTML color code
     * @return string Contrasting color ('#000000' or '#FFFFFF')
     */
    public static function getContrastingColor($htmlColor)
    {
        return self::isColorDark($htmlColor) ? '#FFFFFF' : '#000000';
    }
    
    /**
     * Lighten a color by a percentage.
     * 
     * @param string $htmlColor HTML color code
     * @param float $percent Percentage to lighten (0-100)
     * @return string Lightened color
     */
    public static function lightenColor($htmlColor, $percent)
    {
        $rgb = self::htmlToRgb($htmlColor);
        $percent = max(0, min(100, $percent)) / 100;
        
        $r = min(255, $rgb['r'] + (255 - $rgb['r']) * $percent);
        $g = min(255, $rgb['g'] + (255 - $rgb['g']) * $percent);
        $b = min(255, $rgb['b'] + (255 - $rgb['b']) * $percent);
        
        return self::rgbToHtml($r, $g, $b);
    }
    
    /**
     * Darken a color by a percentage.
     * 
     * @param string $htmlColor HTML color code
     * @param float $percent Percentage to darken (0-100)
     * @return string Darkened color
     */
    public static function darkenColor($htmlColor, $percent)
    {
        $rgb = self::htmlToRgb($htmlColor);
        $percent = max(0, min(100, $percent)) / 100;
        
        $r = max(0, $rgb['r'] * (1 - $percent));
        $g = max(0, $rgb['g'] * (1 - $percent));
        $b = max(0, $rgb['b'] * (1 - $percent));
        
        return self::rgbToHtml($r, $g, $b);
    }
}
