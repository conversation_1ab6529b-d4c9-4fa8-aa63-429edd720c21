<?php
/**
 * J<PERSON>tish Draw 简化演示
 * 
 * 这个演示展示了 Draw 类的基本用法，不依赖复杂的依赖关系
 */

echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <title>Jyotish Draw 功能演示</title>\n";
echo "    <style>\n";
echo "        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n";
echo "        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo "        .code { background: #f4f4f4; padding: 10px; margin: 10px 0; border-radius: 3px; }\n";
echo "        .success { color: #008000; }\n";
echo "        .error { color: #ff0000; }\n";
echo "        .info { color: #0066cc; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>Jyotish Draw 类功能演示</h1>\n";

echo "<div class='section'>\n";
echo "<h2>1. Draw 类概述</h2>\n";
echo "<p>Jyotish\\Draw\\Draw 类是一个用于绘图的 PHP 类，主要功能包括：</p>\n";
echo "<ul>\n";
echo "<li><strong>多渲染器支持：</strong>支持 Image (GD) 和 SVG 两种渲染方式</li>\n";
echo "<li><strong>文本绘制：</strong>可以绘制文本，支持字体、颜色、对齐等选项</li>\n";
echo "<li><strong>图形绘制：</strong>可以绘制多边形（三角形、矩形、复杂形状等）</li>\n";
echo "<li><strong>选项配置：</strong>支持各种绘图选项的设置</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>2. 类的基本结构</h2>\n";
echo "<div class='code'>\n";
echo "<strong>主要常量：</strong><br>\n";
echo "• RENDERER_IMAGE = 'image' - 图像渲染器<br>\n";
echo "• RENDERER_SVG = 'svg' - SVG 渲染器<br><br>\n";
echo "<strong>主要方法：</strong><br>\n";
echo "• __construct(\$width, \$height, \$renderer) - 构造函数<br>\n";
echo "• setOptions(\$options) - 设置选项<br>\n";
echo "• drawText(\$text, \$x, \$y, \$options) - 绘制文本<br>\n";
echo "• drawPolygon(\$points, \$options) - 绘制多边形<br>\n";
echo "• drawChakra(\$Data, \$x, \$y, \$options) - 绘制占星图<br>\n";
echo "• render() - 渲染输出<br>\n";
echo "</div>\n";
echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>3. 使用示例代码</h2>\n";

echo "<h3>3.1 创建 Draw 实例</h3>\n";
echo "<div class='code'>\n";
echo htmlspecialchars("<?php\n");
echo htmlspecialchars("// 创建 400x300 像素的图像渲染器\n");
echo htmlspecialchars("\$draw = new \\Jyotish\\Draw\\Draw(400, 300, \\Jyotish\\Draw\\Draw::RENDERER_IMAGE);\n\n");
echo htmlspecialchars("// 或者创建 SVG 渲染器\n");
echo htmlspecialchars("\$svgDraw = new \\Jyotish\\Draw\\Draw(300, 200, \\Jyotish\\Draw\\Draw::RENDERER_SVG);\n");
echo htmlspecialchars("?>");
echo "</div>\n";

echo "<h3>3.2 设置绘图选项</h3>\n";
echo "<div class='code'>\n";
echo htmlspecialchars("<?php\n");
echo htmlspecialchars("// 设置全局选项\n");
echo htmlspecialchars("\$options = [\n");
echo htmlspecialchars("    'fontColor' => '#FF0000',    // 字体颜色\n");
echo htmlspecialchars("    'fontSize' => 12,           // 字体大小\n");
echo htmlspecialchars("    'strokeColor' => '#0000FF', // 描边颜色\n");
echo htmlspecialchars("    'strokeWidth' => 2          // 描边宽度\n");
echo htmlspecialchars("];\n");
echo htmlspecialchars("\$draw->setOptions(\$options);\n");
echo htmlspecialchars("?>");
echo "</div>\n";

echo "<h3>3.3 绘制文本</h3>\n";
echo "<div class='code'>\n";
echo htmlspecialchars("<?php\n");
echo htmlspecialchars("// 绘制简单文本\n");
echo htmlspecialchars("\$draw->drawText('Hello World!', 100, 50);\n\n");
echo htmlspecialchars("// 绘制带选项的文本\n");
echo htmlspecialchars("\$textOptions = [\n");
echo htmlspecialchars("    'fontColor' => '#000000',\n");
echo htmlspecialchars("    'fontSize' => 14,\n");
echo htmlspecialchars("    'textAlign' => 'center',\n");
echo htmlspecialchars("    'textValign' => 'middle'\n");
echo htmlspecialchars("];\n");
echo htmlspecialchars("\$draw->drawText('居中文本', 200, 100, \$textOptions);\n");
echo htmlspecialchars("?>");
echo "</div>\n";

echo "<h3>3.4 绘制多边形</h3>\n";
echo "<div class='code'>\n";
echo htmlspecialchars("<?php\n");
echo htmlspecialchars("// 绘制三角形\n");
echo htmlspecialchars("\$trianglePoints = [\n");
echo htmlspecialchars("    200, 100,  // 顶点 (x1, y1)\n");
echo htmlspecialchars("    150, 200,  // 左下 (x2, y2)\n");
echo htmlspecialchars("    250, 200   // 右下 (x3, y3)\n");
echo htmlspecialchars("];\n");
echo htmlspecialchars("\$draw->drawPolygon(\$trianglePoints, ['strokeColor' => '#00FF00']);\n\n");
echo htmlspecialchars("// 绘制矩形\n");
echo htmlspecialchars("\$rectanglePoints = [\n");
echo htmlspecialchars("    50, 50,    // 左上\n");
echo htmlspecialchars("    150, 50,   // 右上\n");
echo htmlspecialchars("    150, 100,  // 右下\n");
echo htmlspecialchars("    50, 100    // 左下\n");
echo htmlspecialchars("];\n");
echo htmlspecialchars("\$draw->drawPolygon(\$rectanglePoints, ['strokeColor' => '#FF00FF']);\n");
echo htmlspecialchars("?>");
echo "</div>\n";

echo "<h3>3.5 渲染输出</h3>\n";
echo "<div class='code'>\n";
echo htmlspecialchars("<?php\n");
echo htmlspecialchars("// 对于图像渲染器，这会输出 PNG 图像\n");
echo htmlspecialchars("\$draw->render();\n\n");
echo htmlspecialchars("// 注意：render() 会设置适当的 HTTP 头并输出二进制数据\n");
echo htmlspecialchars("// 对于图像：Content-type: image/png\n");
echo htmlspecialchars("// 对于 SVG：Content-type: image/svg+xml\n");
echo htmlspecialchars("?>");
echo "</div>\n";
echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>4. 错误处理</h2>\n";
echo "<div class='code'>\n";
echo htmlspecialchars("<?php\n");
echo htmlspecialchars("try {\n");
echo htmlspecialchars("    // 无效的渲染器类型会抛出异常\n");
echo htmlspecialchars("    \$draw = new \\Jyotish\\Draw\\Draw(100, 100, 'invalid');\n");
echo htmlspecialchars("} catch (\\Jyotish\\Draw\\Exception\\UnexpectedValueException \$e) {\n");
echo htmlspecialchars("    echo '错误: ' . \$e->getMessage();\n");
echo htmlspecialchars("}\n");
echo htmlspecialchars("?>");
echo "</div>\n";
echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>5. 实际应用场景</h2>\n";
echo "<ul>\n";
echo "<li><strong>占星图绘制：</strong>使用 drawChakra() 方法绘制占星图表</li>\n";
echo "<li><strong>图表生成：</strong>创建各种统计图表和图形</li>\n";
echo "<li><strong>标签制作：</strong>生成带文字和图形的标签</li>\n";
echo "<li><strong>报告生成：</strong>为报告添加图形元素</li>\n";
echo "<li><strong>Web 图形：</strong>动态生成网页图形内容</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>6. 依赖要求</h2>\n";
echo "<ul>\n";
echo "<li><strong>PHP GD 扩展：</strong>图像渲染器需要 GD 库支持</li>\n";
echo "<li><strong>相关类文件：</strong>需要包含 AbstractRenderer、Image、Svg 等渲染器类</li>\n";
echo "<li><strong>工具类：</strong>可能需要 Utility 类用于颜色转换等功能</li>\n";
echo "<li><strong>Trait 文件：</strong>需要 OptionTrait 和 GetTrait</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>7. 注意事项</h2>\n";
echo "<div class='info'>\n";
echo "<strong>重要提示：</strong><br>\n";
echo "• render() 方法会直接输出到浏览器，确保之前没有其他输出<br>\n";
echo "• 图像渲染需要足够的内存，大图像可能导致内存不足<br>\n";
echo "• 坐标系统：(0,0) 在左上角，x 向右增加，y 向下增加<br>\n";
echo "• 多边形点数组格式：[x1, y1, x2, y2, x3, y3, ...]<br>\n";
echo "• 颜色格式通常使用十六进制，如 '#FF0000' 表示红色\n";
echo "</div>\n";
echo "</div>\n";

echo "</body>\n";
echo "</html>\n";
?>
