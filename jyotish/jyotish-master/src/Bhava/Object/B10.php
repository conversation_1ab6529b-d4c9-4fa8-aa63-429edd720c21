<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Bhava\Object;

use <PERSON>yo<PERSON>h\Tattva\Jiva\Nara\Manusha;

/**
 * Class of bhava 10.
 *
 * <AUTHOR> <<EMAIL>>
 */
class B10 extends BhavaObject
{
    /**
     * Bhava key
     * 
     * @var int
     */
    protected $objectKey = 10;
    
    /**
     * Names of the bhava.
     * 
     * @var array
     * @see Varahamihira. Brihat Jataka. Chapter 1, Verse 15-16.
     */
    protected $objectNames = [
        'Aspada',
        'Mana',
    ];

    /**
     * Indications of bhava.
     * 
     * @var array
     * @see Maharishi Parashara. Brihat Parashara Hora Shastra. Chapter 11, Verse 11.
     */
    protected $bhavaKarakatva = [
        'royalty',
        'place',
        'profession',
        'honour',
        'father',
        'living in foreign lands',
        'debts',
    ];

    /**
     * Purushartha of bhava.
     * 
     * @var string
     */
    protected $bhavaPurushartha = Manusha::PURUSHARTHA_ARTHA;
}