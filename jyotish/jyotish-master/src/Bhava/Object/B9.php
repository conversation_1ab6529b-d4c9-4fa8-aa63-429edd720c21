<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Bhava\Object;

use <PERSON>yo<PERSON><PERSON>\Tattva\Jiva\Nara\Manusha;

/**
 * Class of bhava 9.
 *
 * <AUTHOR> <<EMAIL>>
 */
class B9 extends BhavaObject
{
    /**
     * Bhava key
     * 
     * @var int
     */
    protected $objectKey = 9;
    
    /**
     * Names of the bhava.
     * 
     * @var array
     * @see Varahamihira. Brihat Jataka. Chapter 1, Verse 15-16.
     */
    protected $objectNames = [
        'Shubha',
        'Guru',
    ];

    /**
     * Indications of bhava.
     * 
     * @var array
     * @see Maharishi Parashara. Brihat Parashara Hora Shastra. Chapter 11, Verse 10.
     */
    protected $bhavaKarakatva = [
        'fortunes',
        'wifes brother',
        'religion',
        'brothers wife',
        'visits to shrines',
    ];

    /**
     * Purushartha of bhava.
     * 
     * @var string
     */
    protected $bhavaPurushartha = Manusha::PURUSHARTHA_DHARMA;
}