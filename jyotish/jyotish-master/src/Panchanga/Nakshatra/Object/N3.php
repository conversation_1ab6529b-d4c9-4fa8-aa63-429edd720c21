<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyo<PERSON>h\Panchanga\Nakshatra\Object;

use <PERSON><PERSON><PERSON><PERSON>\Panchanga\Nakshatra\Nakshatra;
use <PERSON><PERSON><PERSON><PERSON>\Graha\Graha;
use <PERSON><PERSON><PERSON><PERSON>\Tattva\Jiva\Nara\Deva;
use <PERSON>yo<PERSON><PERSON>\Tattva\Jiva\Nara\Manusha;
use <PERSON><PERSON><PERSON>h\Tattva\Jiva\Pasu;
use Jyo<PERSON>h\Tattva\Maha;
use Jyo<PERSON>h\Tattva\Ayurveda;

/**
 * Class of nakshatra 3.
 *
 * <AUTHOR> <<EMAIL>>
 */
class N3 extends NakshatraObject
{
    /**
     * Nakshatra key
     * 
     * @var int
     */
    protected $nakshatraKey = 3;

    /**
     * Devanagari title 'krittika' in transliteration.
     * 
     * @var array
     * @see Jyotish\Alphabet\Devanagari
     */
    protected $nakshatraTranslit = ['ka','r','ta','virama','ta','i','ka','aa'];
    
    /**
     * The number of taras (stars) of the nakshatra.
     * 
     * @var int
     * @see Varahamihira. Brihat Samhita. Chapter 98, Verse 1-3.
     */
    protected $nakshatraTara = 6;

    /**
     * Deva of nakshatra.
     * 
     * @var string
     * @see Varahamihira. Brihat Samhita. Chapter 98, Verse 4-5.
     */
    protected $nakshatraDeva = Deva::DEVA_AGNI;

    /**
     * Type of nakshatra.
     * 
     * @var string
     * @see Varahamihira. Brihat Samhita. Chapter 98, Verse 11.
     */
    protected $nakshatraType = Nakshatra::TYPE_SADHARANA;

    /**
     * Graha of nakshatra.
     * 
     * @var string
     * @see Satyacharya. Satya Jatakam. Chapter 1, Verse 9.
     */
    protected $nakshatraRuler = Graha::KEY_SY;

    protected $nakshatraEnergy = Nakshatra::ENERGY_LAYA;
    protected $nakshatraGana = Manusha::GANA_RAKSHASA;
    protected $nakshatraGender = Manusha::GENDER_FEMALE;
    protected $nakshatraGuna = Maha::GUNA_RAJA;
    protected $nakshatraPurushartha = Manusha::PURUSHARTHA_KAMA;
    protected $nakshatraVarna = Manusha::VARNA_BRAHMANA;
    protected $nakshatraPrakriti = Ayurveda::PRAKRITI_KAPHA;
    protected $nakshatraYoni = [
        'animal' => Pasu::ANIMAL_SHEEP,
        'gender' => Manusha::GENDER_FEMALE,
    ];
    protected $nakshatraRajju = [
        'lift' => Nakshatra::LIFT_AROHA,
        'limb' => Nakshatra::LIMB_NABHI,
    ];
}