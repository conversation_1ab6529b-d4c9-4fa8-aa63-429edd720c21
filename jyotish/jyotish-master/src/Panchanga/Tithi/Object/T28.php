<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyo<PERSON><PERSON>\Panchanga\Tithi\Object;

use <PERSON><PERSON><PERSON><PERSON>\Panchanga\Karana\Karana;

/**
 * Class of tithi 28.
 *
 * <AUTHOR> <<EMAIL>>
 */
class T28 extends TithiObject
{
    /**
     * Tithi key
     * 
     * @var int
     */
    protected $tithiKey = 28;

    /**
     * Devanagari number 13 in transliteration.
     * 
     * @var array
     * @see Jyotish\Alphabet\Devanagari
     */
    protected $tithiTranslit = ['d1', 'd3'];

    /**
     * Karana of tithi.
     * 
     * @var string
     */
    protected $tithiKarana = [
        1 => Karana::NAME_GARA,
        2 => Karana::NAME_VANIJA
    ];
}