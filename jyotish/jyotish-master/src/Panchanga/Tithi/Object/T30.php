<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyo<PERSON><PERSON>\Panchanga\Tithi\Object;

use <PERSON><PERSON><PERSON><PERSON>\Panchanga\Karana\Karana;

/**
 * Class of tithi 30.
 *
 * <AUTHOR> <<EMAIL>>
 */
class T30 extends TithiObject
{
    /**
     * Tithi key
     * 
     * @var int
     */
    protected $tithiKey = 30;

    /**
     * Devanagari number 15 in transliteration.
     * 
     * @var array
     * @see Jyotish\Alphabet\Devanagari
     */
    protected $tithiTranslit = ['d1', 'd5'];

    /**
     * Karana of tithi.
     * 
     * @var string
     */
    protected $tithiKarana = [
        1 => Karana::NAME_CHATUSHPADA,
        2 => Karana::NAME_NAGA
    ];
}