<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyo<PERSON>h\Panchanga\Yoga\Object;

/**
 * Parent class for yoga objects.
 *
 * <AUTHOR> das <<EMAIL>>
 */
class YogaObject extends \Jyotish\Panchanga\AngaObject
{
    use \Jyotish\Base\Traits\GetTrait;

    /**
     * Anga type.
     * 
     * @var string
     */
    protected $angaType = 'yoga';
    
    /**
     * Yoga key.
     * 
     * @var int
     */
    protected $yogaKey;
    
    /**
     * Yoga name.
     * 
     * @var string
     */
    protected $yogaName;

    /**
     * Deva of yoga.
     * 
     * @var string
     */
    protected $yogaDeva;
}
