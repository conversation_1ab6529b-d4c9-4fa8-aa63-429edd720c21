<?php
/**
 * @link      http://github.com/kunjara/jyotish for the canonical source repository
 * @license   GNU General Public License version 2 or later
 */

namespace Jyotish\Alphabet;

/**
 * Greek class
 *
 * <AUTHOR> <<EMAIL>>
 */
class Greek extends \Jyotish\Alphabet\Language
{
    /**
     * Uppercase letters unicode
     */
    const Alpha     = '0391';
    const Beta      = '0392';
    const Gamma     = '0393';
    const Delta     = '0394';
    const Epsilon   = '0395';
    const Zeta      = '0396';
    const Eta       = '0397';
    const Theta     = '0398';
    const Iota      = '0399';
    const Kappa     = '039A';
    const Lambda    = '039B';
    const Mu        = '039C';
    const Nu        = '039D';
    const Xi        = '039E';
    const Omicron   = '039F';
    const Pi        = '03A0';
    const Rho       = '03A1';
    const Sigma     = '03A3';
    const Tau       = '03A4';
    const Upsilon   = '03A5';
    const Phi       = '03A6';
    const Chi       = '03A7';
    const Psi       = '03A8';
    const Omega     = '03A9';

    /**
     * Lowercase letters unicode
     */
    const alpha     = '03B1';
    const beta      = '03B2';
    const gamma     = '03B3';
    const delta     = '03B4';
    const epsilon   = '03B5';
    const zeta      = '03B6';
    const eta       = '03B7';
    const theta     = '03B8';
    const iota      = '03B9';
    const kappa     = '03BA';
    const lambda    = '03BB';
    const mu        = '03BC';
    const nu        = '03BD';
    const xi        = '03BE';
    const omicron   = '03BF';
    const pi        = '03C0';
    const rho       = '03C1';
    const sigma     = '03C3';
    const tau       = '03C4';
    const upsilon   = '03C5';
    const phi       = '03C6';
    const chi       = '03C7';
    const psi       = '03C8';
    const omega     = '03C9';
}