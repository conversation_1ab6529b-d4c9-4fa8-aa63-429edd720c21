<?php
/**
 * 测试变量属性功能
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <title>变量属性测试</title>\n";
echo "    <style>\n";
echo "        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n";
echo "        .success { color: #008000; font-weight: bold; }\n";
echo "        .error { color: #ff0000; font-weight: bold; }\n";
echo "        .info { color: #0066cc; }\n";
echo "        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo "        pre { background: #f5f5f5; padding: 10px; margin: 10px 0; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>🔧 Jyotish Draw 变量属性测试</h1>\n";

// 包含必要文件
echo "<div class='section'>\n";
echo "<h2>1. 包含文件</h2>\n";

$files = [
    'jyotish/src/Base/Traits/OptionTrait.php',
    'jyotish/src/Base/Traits/GetTrait.php',
    'jyotish/src/Base/Utility.php',
    'jyotish/src/Renderer/AbstractRenderer.php',
    'jyotish/src/Renderer/Image.php',
    'jyotish/src/Draw.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        require_once $file;
        echo "<span class='success'>✓</span> $file<br>\n";
    } else {
        echo "<span class='error'>✗</span> $file<br>\n";
        exit("文件缺失");
    }
}

echo "</div>\n";

// 测试变量属性功能
echo "<div class='section'>\n";
echo "<h2>2. 变量属性功能测试</h2>\n";

try {
    use Jyotish\Draw\Draw;
    
    // 创建 Draw 实例
    $draw = new Draw(400, 300, Draw::RENDERER_IMAGE);
    echo "<span class='success'>✓</span> 创建 Draw 实例成功<br>\n";
    
    // 测试设置选项并存储到 setOptions 数组
    echo "<h3>2.1 设置选项测试</h3>\n";
    $options1 = [
        'fontColor' => '#FF0000',
        'fontSize' => 14,
        'strokeColor' => '#0000FF',
        'strokeWidth' => 2,
        'customProperty1' => 'value1',
        'customProperty2' => 123
    ];
    
    $draw->setOptions($options1);
    echo "<span class='success'>✓</span> 第一批选项设置成功<br>\n";
    
    // 设置更多选项
    $options2 = [
        'textAlign' => 'center',
        'fillColor' => '#FFE0E0',
        'customProperty3' => 'value3',
        'userDefinedVar' => 'Hello World'
    ];
    
    $draw->setOptions($options2);
    echo "<span class='success'>✓</span> 第二批选项设置成功<br>\n";
    
    // 获取存储的选项
    echo "<h3>2.2 获取存储的选项</h3>\n";
    $storedOptions = $draw->Renderer->getStoredOptions();
    echo "<span class='info'>存储的所有选项:</span><br>\n";
    echo "<pre>" . print_r($storedOptions, true) . "</pre>\n";
    
    // 获取特定选项
    echo "<h3>2.3 获取特定选项</h3>\n";
    $fontColor = $draw->Renderer->getStoredOption('fontColor');
    $customProp = $draw->Renderer->getStoredOption('customProperty1');
    $nonExistent = $draw->Renderer->getStoredOption('nonExistent', 'default_value');
    
    echo "<span class='info'>fontColor:</span> $fontColor<br>\n";
    echo "<span class='info'>customProperty1:</span> $customProp<br>\n";
    echo "<span class='info'>nonExistent (默认值):</span> $nonExistent<br>\n";
    
    // 测试绘制功能
    echo "<h3>2.4 绘制功能测试</h3>\n";
    $draw->drawText("变量属性测试", 200, 50, ['textAlign' => 'center']);
    echo "<span class='success'>✓</span> 绘制文本成功<br>\n";
    
    $trianglePoints = [200, 100, 160, 160, 240, 160];
    $draw->drawPolygon($trianglePoints, ['strokeColor' => '#00AA00']);
    echo "<span class='success'>✓</span> 绘制多边形成功<br>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 测试失败: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "</div>\n";

// 使用示例
echo "<div class='section'>\n";
echo "<h2>3. 使用示例</h2>\n";

echo "<h3>3.1 基本用法</h3>\n";
echo "<pre>";
echo htmlspecialchars('<?php
use Jyotish\Draw\Draw;

// 创建绘图实例
$draw = new Draw(400, 300, Draw::RENDERER_IMAGE);

// 设置选项 - 会自动存储到 $setOptions 数组中
$draw->setOptions([
    "fontColor" => "#FF0000",
    "strokeColor" => "#0000FF", 
    "strokeWidth" => 2,
    "customVar1" => "my_value",
    "userSetting" => 123
]);

// 获取存储的所有选项
$allOptions = $draw->Renderer->getStoredOptions();
print_r($allOptions);

// 获取特定选项
$fontColor = $draw->Renderer->getStoredOption("fontColor");
$customVar = $draw->Renderer->getStoredOption("customVar1", "default");

echo "字体颜色: " . $fontColor;
echo "自定义变量: " . $customVar;
?>');
echo "</pre>\n";

echo "<h3>3.2 高级用法</h3>\n";
echo "<pre>";
echo htmlspecialchars('<?php
// 分批设置选项
$draw->setOptions(["group1" => "value1", "group2" => "value2"]);
$draw->setOptions(["group3" => "value3", "group4" => "value4"]);

// 所有选项都会累积存储
$allOptions = $draw->Renderer->getStoredOptions();
// 结果: ["group1" => "value1", "group2" => "value2", "group3" => "value3", "group4" => "value4"]

// 检查选项是否存在
if (isset($allOptions["group1"])) {
    echo "group1 存在，值为: " . $allOptions["group1"];
}

// 使用默认值获取选项
$safeValue = $draw->Renderer->getStoredOption("nonexistent", "safe_default");
?>');
echo "</pre>\n";

echo "</div>\n";

// 功能说明
echo "<div class='section'>\n";
echo "<h2>4. ✅ 新增功能说明</h2>\n";

echo "<p><strong>现在您可以：</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ 在 Image 和 Svg 类中都添加了 <code>protected \$setOptions = array();</code> 属性</li>\n";
echo "<li>✅ 所有通过 <code>setOptions()</code> 设置的选项都会自动存储到这个数组中</li>\n";
echo "<li>✅ 可以使用 <code>getStoredOptions()</code> 获取所有存储的选项</li>\n";
echo "<li>✅ 可以使用 <code>getStoredOption(\$name, \$default)</code> 获取特定选项</li>\n";
echo "<li>✅ 支持自定义变量和标准选项的混合使用</li>\n";
echo "<li>✅ 多次调用 <code>setOptions()</code> 会累积存储所有选项</li>\n";
echo "</ul>\n";

echo "<p><strong>解决的问题：</strong></p>\n";
echo "<ul>\n";
echo "<li>🔧 添加了 <code>\$setOptions</code> 数组属性来存储选项</li>\n";
echo "<li>🔧 <code>\$this->Renderer->setOptions(\$options)</code> 现在完全正常工作</li>\n";
echo "<li>🔧 支持存储和检索任意自定义变量</li>\n";
echo "<li>🔧 提供了完整的选项管理API</li>\n";
echo "</ul>\n";

echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>5. 测试输出</h2>\n";
echo "<p>查看实际的图像输出：</p>\n";
echo "<p><a href='test_variable_image.php' target='_blank'>🖼️ 查看变量属性图像测试</a></p>\n";
echo "</div>\n";

echo "<p><em>测试完成时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
echo "</body>\n";
echo "</html>\n";
?>
