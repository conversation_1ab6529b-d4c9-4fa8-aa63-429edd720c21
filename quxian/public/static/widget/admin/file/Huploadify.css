@charset "utf-8";
/* CSS Document */
.uploadify-button{
	display:inline-block;
	margin:12px;
	border:1px solid #808080;
	background-color: #707070;
	line-height:24px;
	border-radius:12px;
	padding:0 18px;
    font-size:12px;
    font-weight: 600;
    font-family: '微软雅黑';
	color:#FFF;
	cursor:pointer;
	text-decoration:none;
	max-width: 100px;
}
.uploadify-button:hover{
	background-color: #888;
}
.uploadfile{
	width:0;
	}
.uploadify-queue .uploadify-queue-item{
	list-style-type:none;
	margin-top:10px;
	}
.uploadbtn,.delfilebtn{
	display:inline-block;
	border:1px solid #999;
	line-height:24px;
	border-radius:4px;
	padding:0 18px;
    font-size:12px;
	color:#666;
	text-decoration:none;
	}
.uploadbtn{
	display: none;/*默认不显示上传按钮，非自动上传时用js控制显示*/
}
.disabledbtn{

}
.up_filename,.progressnum,.delfilebtn,.uploadbtn,.up_percent{
	font-size:12px;
	color:#666;
	margin-left:10px;
	}
.uploadify-progress{
	 display:inline-block; 
	 width:600px; 
	 height:10px; 
	 background-color:white;
	 border-radius:20px;
	 border:2px groove #666;
	 vertical-align:middle;
	 padding:0;
	 }
.uploadify-progress-bar{
	width:0;
	height:100%;
	border-radius:20px;
	background-color: #0099FF;
	}
