<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\common\validate;

/**
 * 积分记录验证器
 */
class Phase extends ValidateBase
{
    // 验证规则
    protected $rule = [
        'title'  => 'require',
        'planet_one'  => 'require',
        'planet_two'  => 'require',
        'longitude'     => 'require',
        'allow'   => 'require',
        'score'   => 'require',
        'color'   => 'require',
    ];

    // 验证提示
    protected $message = [
        'title.require'    => '命名不能为空',
        'planet_two.require'    => '行星2不能为空',
        'longitude.require'    => '角度不能为空',
        'allow.require'    => '允许度不能为空',
        'score.require'    => '分值不能为空',
        'color.require'    => '颜色不能为空'

    ];

    // 应用场景
    protected $scene = [
        'add'  =>  ['title','planet_one','planet_two','longitude','allow','score','color'],
    ];

}
