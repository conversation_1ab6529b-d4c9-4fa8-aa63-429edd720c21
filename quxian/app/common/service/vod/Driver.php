<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

namespace app\common\service\vod;

use app\common\service\BaseInterface;

/**
 * 云存储服务驱动
 */
interface Driver extends BaseInterface
{

    /**
     * 获取驱动参数
     */
    public function getDriverParam();

    /**
     * 获取基本信息
     */
    public function driverInfo();

    /**
     * 配置信息
     */
    public function config();

    /**
     * 初始化客户端
     */
    public function initVodClient();

    /**
     * 获取指定的分类信息，及其子分类（即下一级分类）的列表
     */
    public function getCategories($cateId, $pageNo, $pageSize);

    /**
     * 视频分类修改
     */
    public function updateCategory($cateId, $cateName);

    /**
     * 视频分类新增
     */
    public function addCategory($cateName, $parentId);

    /**
     * 删除视频分类，同时会删除其下级分类（包括二级分类和三级分类），请慎重操作
     */
    public function deleteCategory($cateId);

    /**
     * 获取视频上传地址和凭证
     */
    public function createUploadVideo($title, $file_name, $description, $cover_url, $tags,$cateId);


    /**
     * 获取视频信息
     */
    public function getMezzanineInfo($videoId);
    /**
     * 获取视频信息
     */
    public function getVideoInfo($videoId);

    /**
     * 修改视频信息
     */
    public function updateVideoInfo($videoId, $title, $description, $cover_url, $tags, $cateId);

    /**
     * 删除视频
     */
    public function deleteVideos($videoIds);

    /**
     * 获取视频列表
     */
    public function getVideoList($page, $start, $end);

    /**
     * 搜索视频文件
     */
    public function searchMedia($page, $start, $end);
}
