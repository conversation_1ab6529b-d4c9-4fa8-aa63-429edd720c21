<script src="__STATIC__/module/admin/js/citydata.js"></script>
<script src="__STATIC__/module/admin/js/picker.min.js"></script>
<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<div class="box-body">
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label>姓名</label>
						<span>（姓名）</span>
						<input class="form-control" name="name" placeholder="请输入姓名" value="{$info['name']|default=''}"
						       type="text">
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="form-group">
						<label>出生日期</label>
						<span>（出生日期阳历）</span>
						<input name="birthday" size="16" type="text"
						       value="{$info['birthday']|default='1992-10-10 12:12'}"
						       readonly
						       class="form_datetime form-control">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>地点</label>
						<span>（地点）</span>
						<a href="#" class="btn btn-info active" role="button" id="sel_city">{$info['fullname']|default='北京市
							北京市 北京市'}</a>
						<input type="hidden" name="birth_district" class="birth_district"
						       value="{$info['birth_district']|default='1015'}">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>性别</label>
						<span>（性别）</span>
						<div class="icheck-primary ">
							<input type="radio" id="someRadioId1" value="1" name="sex" checked="checked"/>
							<label for="someRadioId1">男</label>
							<input type="radio" id="someRadioId2" value="2" name="sex"/>
							<label for="someRadioId2">女</label>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="box-footer">
			<input type="hidden" name="id" value="{$info['id']|default='0'}"/>
			{include file="layout/edit_btn_group"/}
		</div>
	</div>
</form>

<script type="text/javascript">
    ob.setValue("sex", {$info.sex |default = 1});
    ob.setValue("sex", {$info.sex |default = 1});
    $(document).ready(function () {

        $(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});

        console.log(city);

        var nameEl = document.getElementById('sel_city');

        var first = []; /* 省，直辖市 */
        var second = []; /* 市 */
        var third = []; /* 镇 */

        var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */

        var checked = [0, 0, 0]; /* 已选选项 */

        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = {};
                temp.text = item.text;
                temp.value = item.value;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('children')) {
            creatList(city[selectedIndex[0]].children, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].children[selectedIndex[1]].hasOwnProperty('children')) {
            creatList(city[selectedIndex[0]].children[selectedIndex[1]].children, third);
        } else {
            third = [{text: '', value: 0}];
        }

        var picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });

        picker.on('picker.select', function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';

            nameEl.innerText = text1 + ' ' + text2 + ' ' + text3;
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity.hasOwnProperty('children')) {
                    creatList(firstCity.children, second);

                    var secondCity = city[selectedIndex].children[0];
                    if (secondCity.hasOwnProperty('children')) {
                        creatList(secondCity.children, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0);
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].children[selectedIndex].hasOwnProperty('children')) {
                    var secondCity = city[first_index].children[selectedIndex];
                    creatList(secondCity.children, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
            console.log(selectedVal);
            console.log(checked);
            $(".birth_district").val(selectedVal[2]);
        });
        nameEl.addEventListener('click', function () {
            picker.show();
        });
    })
</script>