<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="__STATIC__/module/admin/css/getuserinfo.css"/>
	<link rel="stylesheet" href="__STATIC__/module/common/mdatetimer/css/rui_date.css">
	
	
	
	<script type="text/javascript" src="__STATIC__/module/common/jquery/jquery-2.2.3.min.js"></script>
	
	<script src="__STATIC__/module/admin/js/citydata.js"></script>
	<script src="__STATIC__/module/admin/js/picker.min.js"></script>
	
	
	<script src="__STATIC__/module/common/mdatetimer/js/chooseDate.js"></script>
	<script src="__STATIC__/module/common/mdatetimer/js/rui_date.js"></script>
	
	<script>
        (function(f, h) {
                if (!f.fn.ionCheckRadio) {
                    String.prototype.trim || function() {
                        var a = /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;
                        String.prototype.trim = function() {
                            return this.replace(a, "")
                        }
                    }();
                    var k = {}
                        , l = {}
                        , n = function(a) {
                        this.group = a.content;
                        this.type = a.type;
                        this.$group = f(this.group);
                        this.observer = this.old = null;
                        this.init()
                    };
                    n.prototype = {
                        init: function() {
                            this.$group.eq(0).hasClass("icr-input") ? this.prepare() : this.createHTML()
                        },
                        prepare: function() {
                            var a = this, e, b;
                            for (b = 0; b < this.group.length; b++)
                                e = f(this.group[b]),
                                    e = e.parent().parent(),
                                    f.data(this.group[b], "icr-parent", e),
                                    this.presetChecked(this.group[b]),
                                    this.presetDisabled(this.group[b]);
                            this.$group.on("change", function() {
                                a.change(this)
                            });
                            this.$group.on("focus", function() {
                                a.focus(this)
                            });
                            this.$group.on("blur", function() {
                                a.blur(this)
                            });
                            h.MutationObserver && this.setUpObserver()
                        },
                        setUpObserver: function() {
                            var a = this, e, b;
                            this.observer = new MutationObserver(function(b) {
                                    b.forEach(function(b) {
                                        e = b.target;
                                        "disabled" === b.attributeName && a.toggle(a.getParent(e), e.disabled, "disabled")
                                    })
                                }
                            );
                            for (b = 0; b < this.group.length; b++)
                                this.observer.observe(this.group[b], {
                                    attributes: !0
                                })
                        },
                        destroy: function() {
                            this.$group.off();
                            this.observer && (this.observer.disconnect(),
                                this.observer = null)
                        },
                        presetChecked: function(a) {
                            a.checked && (this.toggle(this.getParent(a), !0, "checked"),
                            "radio" === this.type && (this.old = a))
                        },
                        presetDisabled: function(a) {
                            a.disabled && this.toggle(this.getParent(a), !0, "disabled")
                        },
                        change: function(a) {
                            this.toggle(this.getParent(a), a.checked, "checked");
                            "radio" === this.type && this.old && this.old !== a && this.toggle(this.getParent(this.old), this.old.checked, "checked");
                            this.old = a
                        },
                        focus: function(a) {
                            this.toggle(this.getParent(a), !0, "focused")
                        },
                        blur: function(a) {
                            this.toggle(this.getParent(a), !1, "focused")
                        },
                        toggle: function(a, e, b) {
                            e ? a.addClass(b) : a.removeClass(b)
                        },
                        getParent: function(a) {
                            return f.data(a, "icr-parent")
                        },
                        createHTML: function() {
                            var a = []
                                , e = []
                                , b = []
                                , q = []
                                , h = []
                                , l = []
                                , n = []
                                , r = []
                                , p = this
                                , u = function(a) {
                                var b = [];
                                a = a[0].childNodes;
                                var d, c;
                                for (c = 0; c < a.length; c++)
                                    b.push(a[c]);
                                for (; b.length; ) {
                                    d = b[0];
                                    if (3 === d.nodeType) {
                                        if (a = d.nodeValue.trim())
                                            break
                                    } else if (1 === d.nodeType)
                                        for (a = d.childNodes,
                                                 c = 0; c < a.length; c++)
                                            b.push(a[c]);
                                    Array.prototype.splice.call(b, 0, 1)
                                }
                                b = d.parentNode.innerHTML;
                                0 <= b.indexOf("<input") && (d = b.indexOf("<input"),
                                    b = b.slice(d),
                                    d = b.indexOf(">"),
                                    b = b.slice(d + 1).trim());
                                return b
                            };
                            this.$group.each(function(g) {
                                var m, d = f(this);
                                m = d.prop("className");
                                var c = d.prop("type")
                                    , k = d.prop("name")
                                    , p = d.prop("value")
                                    , v = d.prop("checked")
                                    , w = d.prop("disabled")
                                    , t = d.prop("id");
                                a.push(m);
                                e.push(c);
                                b.push(k);
                                q.push(p);
                                l.push(v);
                                n.push(w);
                                m = t ? f("label[for='" + t + "']") : d.closest("label");
                                h.push(u(m));
                                c = '<label class="icr-label">   <span class="icr-item type_{type}"></span>   <span class="icr-hidden"><input class="icr-input {class_list}" type="{type}" name="{name}" value="{value}" {disabled} {checked} /></span>   <span class="icr-text">{text}</span></label>'.replace(/\{class_list\}/, a[g]);
                                c = c.replace(/\{type\}/g, e[g]);
                                c = c.replace(/\{name\}/, b[g]);
                                c = c.replace(/\{value\}/, q[g]);
                                c = c.replace(/\{text\}/, h[g]);
                                c = n[g] ? c.replace(/\{disabled\}/, "disabled") : c.replace(/\{disabled\}/, "");
                                c = l[g] ? c.replace(/\{checked\}/, "checked") : c.replace(/\{checked\}/, "");
                                m.after(c);
                                g = m.next();
                                r.push(g[0]);
                                d.remove();
                                m.remove()
                            });
                            this.$group = f(r).find("input");
                            this.$group.each(function(a) {
                                p.group[a] = this;
                                k[b[0]][a] = this
                            });
                            this.prepare()
                        }
                    };
                    f.fn.ionCheckRadio = function() {
                        var a, e = [], b, f;
                        for (a = 0; a < this.length; a++)
                            b = this[a],
                                f = b.name,
                                "radio" !== b.type && "checkbox" !== b.type || !f ? h.console && h.console.warn && h.console.warn("Ion.CheckRadio: Some inputs have wrong type or absent name attribute!") : (k[f] = {
                                    type: b.type,
                                    content: []
                                },
                                    e.push(b));
                        for (a = 0; a < e.length; a++)
                            b = e[a],
                                f = b.name,
                                k[f].content.push(b);
                        for (a in k)
                            l[a] && (l[a].destroy(),
                                l[a] = null),
                                l[a] = new n(k[a])
                    }
                }
            }
        )(jQuery, window);
	</script>
	<title>桃花曲线图录入信息</title>
	
</head>
<body>
<div class="container0">
	<div class="earth">
		<div class="countries"></div>
		<div class="countries"></div>
		<div class="countries"></div>
		<div class="countries"></div>
		<div class="countries"></div>
		<div class="countries"></div>
		<div class="countries"></div>
		<div class="eyes">
			<div class="eyes opened"></div>
			<div class="eyes closed"></div>
		</div>
		<div class="tear"></div>
	</div>
	<div class="excited">!</div>
	<div class="shadow"></div>
	<div class="moon">
		<div class="craters"></div>
		<div class="craters"></div>
		<div class="craters"></div>
		<div class="craters"></div>
		<div class="craters"></div>
		<div class="eyes"></div>
	</div>
	<div class="sun">
		<div class="left-eye">
			<div class="left-eyelash"></div>
		</div>
		<div class="right-eye">
			<div class="right-eyelash"></div>
		</div>
		<div class="rays">
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
			<div class="beam"></div>
		</div>
	</div>
	<div class="stars1"></div>
	<div class="stars2"></div>
	<div class="stars3"></div>
</div>
<div class="container">
	
	<form action="{:url()}" method="post" class="form_single">
		<div class="login-form">
			<div class="text">信息录入</div>
			<div class="form-item">
				<input name="name" type="text" placeholder="昵称">
				<input name="birthday" type="text" id="demo2" data-date="2015-08-01" placeholder="点击选择出生日期" readonly  data-toid-datetime="birthday1" data-toid-datetime="date1" data-toid-hour="hour1" data-toid-minute="minute1">
				<input type="hidden" id="date1" name="date">
				<input type="hidden" id="hour1" name="hour">
				<input type="hidden" id="minute1" name="minute">
				<input type="hidden" id="birthday1" name="birthday">
				
				<input id="sel_city" type="text" placeholder="点击选择出生地址" readonly>
				<input type="hidden" name="birth_district" class="birth_district" value="">
				<input name="member_id" type="hidden" value="{$member_id|default=2}">
			</div>
			<div class="zzsc">
			 
				<div class="js-demo-1">
					性别：
					<label class="icr-label checked">   <span class="icr-item type_radio"></span>   <span class="icr-hidden"><input class="icr-input " type="radio" name="sex" value="1" checked=""></span>   <span class="icr-text">男</span></label>
					<label class="icr-label">   <span class="icr-item type_radio"></span>   <span class="icr-hidden"><input class="icr-input " type="radio" name="sex" value="2"></span>   <span class="icr-text">女</span></label>
					<label class="icr-label">   <span class="icr-item type_radio"></span>   <span class="icr-hidden"><input class="icr-input " type="radio" name="sex" value="0"></span>   <span class="icr-text">保密</span></label>
				</div>
			</div>
			<button  type="submit" class="btn ladda-button ajax-post" data-style="slide-up" target-form="form_single" style="font-size: 20px;">
				 确 定
			</button>
		</div>
	</form>
</div>
</body>
</html>
<script language="javascript">
    $(".js-demo-1").find("input").ionCheckRadio();
    $(".js-demo-2").find("input").ionCheckRadio();
    /* 公农历选择切换插件 */
    var pcx = pcx || {};
    pcx.dc = new Lunar(); // 天干地支年份计算
    var date2 = new ruiDatepicker().init('#demo2');

    // 可通过下面的说明自行添加多几个年份，BaseKanChih 这个没找到很好的计算方式，我都不随便写，没用到所以不影响
    function tagLunarCal(d, i, w, k, m1, m2, m3, m4, m5, m6, m7, m8, m9, m10, m11, m12, m13) {
        this.BaseDays = d;         /* 1 月 1 日到正月初一的累计日 */
        this.Intercalation = i;    /* 闰月月份. 0==此年沒有闰月 */
        this.BaseWeekday = w;      /* 此年 1 月 1 日为星期减 1 */
        this.BaseKanChih = k;      /* 此年 1 月 1 日之干支序号减 1 */
        this.MonthDays = [m1, m2, m3, m4, m5, m6, m7, m8, m9, m10, m11, m12, m13]; /* 此农历年每月之大小, 0==小月(29日), 1==大月(30日) */
    }
    /* new tagLunarCal( 24,  4, 2, 38, 0, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1 ),
	new tagLunarCal( 42,  0, 4, 44, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1 ),
	new tagLunarCal( 31,  0, 5, 49, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0 ) */
    
    //时间选择结束
    
    var nameEl = document.getElementById('sel_city');

    var first = []; /* 省，直辖市 */
    var second = []; /* 市 */
    var third = []; /* 镇 */

    var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */

    var checked = [0, 0, 0]; /* 已选选项 */

    function creatList(obj, list) {
        obj.forEach(function (item, index, arr) {
            var temp = {};
            temp.text = item.text;
            temp.value = item.value;
            list.push(temp);
        })
    }

    creatList(city, first);

    if (city[selectedIndex[0]].hasOwnProperty('children')) {
        creatList(city[selectedIndex[0]].children, second);
    } else {
        second = [{text: '', value: 0}];
    }

    if (city[selectedIndex[0]].children[selectedIndex[1]].hasOwnProperty('children')) {
        creatList(city[selectedIndex[0]].children[selectedIndex[1]].children, third);
    } else {
        third = [{text: '', value: 0}];
    }

    var picker = new Picker({
        data: [first, second, third],
        selectedIndex: selectedIndex,
        title: '地址选择'
    });

    picker.on('picker.select', function (selectedVal, selectedIndex) {
        var text1 = first[selectedIndex[0]].text;
        var text2 = second[selectedIndex[1]].text;
        var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';

        nameEl.value = text1 + ' ' + text2 + ' ' + text3;
    });

    picker.on('picker.change', function (index, selectedIndex) {
        if (index === 0) {
            firstChange();
        } else if (index === 1) {
            secondChange();
        }

        function firstChange() {
            second = [];
            third = [];
            checked[0] = selectedIndex;
            var firstCity = city[selectedIndex];
            if (firstCity.hasOwnProperty('children')) {
                creatList(firstCity.children, second);

                var secondCity = city[selectedIndex].children[0];
                if (secondCity.hasOwnProperty('children')) {
                    creatList(secondCity.children, third);
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                }
            } else {
                second = [{text: '', value: 0}];
                third = [{text: '', value: 0}];
                checked[1] = 0;
                checked[2] = 0;
            }

            picker.refillColumn(1, second);
            picker.refillColumn(2, third);
            picker.scrollColumn(1, 0);
            picker.scrollColumn(2, 0)
        }

        function secondChange() {
            third = [];
            checked[1] = selectedIndex;
            var first_index = checked[0];
            if (city[first_index].children[selectedIndex].hasOwnProperty('children')) {
                var secondCity = city[first_index].children[selectedIndex];
                creatList(secondCity.children, third);
                picker.refillColumn(2, third);
                picker.scrollColumn(2, 0)
            } else {
                third = [{text: '', value: 0}];
                checked[2] = 0;
                picker.refillColumn(2, third);
                picker.scrollColumn(2, 0)
            }
        }

    });

    picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        console.log(selectedVal);
        console.log(checked);
        $(".birth_district").val(selectedVal[2]);
    });
    nameEl.addEventListener('click', function () {
        picker.show();
    });

</script>
