<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 会员控制器
 */
class Member extends AdminBase
{

    /**
     * 会员授权
     */
    public function memberAuth()
    {
        
        IS_POST && $this->jump($this->logicMember->addToGroup($this->param));
        
        // 所有的权限组
        $group_list = $this->logicAuthGroup->getAuthGroupList(['member_id' => MEMBER_ID]);
        
        // 会员当前权限组
        $member_group_list = $this->logicAuthGroupAccess->getMemberGroupInfo($this->param['id']);

        // 选择权限组
        $list = $this->logicAuthGroup->selectAuthGroupList($group_list, $member_group_list);

        $this->assign('list', $list);
        
        $this->assign('id', $this->param['id']);
        
        return $this->fetch('member_auth');
    }

    /**
     * 会员信息
     */
    public function memberInfo()
    {

        IS_POST && $this->jump($this->logicMember->memberEdit($this->param));

        $this->assign('info', $ggg=$this->logicMember->getMemberInfo(['id' => MEMBER_ID]));


        return $this->fetch('member_info');
    }
    
    /**
     * 会员列表
     */
    public function memberList()
    {
        
        $where = $this->logicMember->getWhere($this->param);

        $this->assign('list', $this->logicMember->getMemberColumn($where));
        
        return $this->fetch('member_list');
    }

    /**
     * 会员导出
     */
    public function exportMemberList()
    {
        
        $where = $this->logicMember->getWhere($this->param);
        
        $this->logicMember->exportMemberList($where);
    }
    
    /**
     * 会员添加
     */
    public function memberAdd()
    {
        
        IS_POST && $this->jump($this->logicMember->memberAdd($this->param));

        $memberList = $this->logicMember->getMemberColumn(['id' =>['gt',1],'status'=>1]);

        $this->assign('memberList', $memberList);

        return $this->fetch('member_add');
    }
    
    /**
     * 会员编辑
     */
    public function memberEdit()
    {
        
        IS_POST && $this->jump($this->logicMember->memberEdit($this->param));
        
        $info = $this->logicMember->getMemberInfo(['id' => $this->param['id']]);

        $memberList = $this->logicMember->getMemberColumn(['id' =>['gt',1],'status'=>1]);

        unset($memberList[$this->param['id']]);

        $this->assign('memberList', $memberList);

        $this->assign('info', $info);

        return $this->fetch('member_edit');
    }
    
    /**
     * 会员删除
     */
    public function memberDel($id = 0)
    {
        
        return $this->jump($this->logicMember->memberDel(['id' => $id]));
    }

    /**
     * 更新access_token
     */
    public function updateAccessToken()
    {

        return $this->jump($this->logicMember->updateAccessToken($this->param));
    }
    
    /**
     * 修改密码
     */
    public function editPassword()
    {
        
        IS_POST && $this->jump($this->logicMember->editPassword($this->param));
        
        $info = $this->logicMember->getMemberInfo(['id' => MEMBER_ID]);
        
        $this->assign('info', $info);
        
        return $this->fetch('edit_password');
    }
    /**
     * 会员修改状况
     */
    public function memberWeiApp()
    {

        return $this->jump($this->logicMember->memberWeiApp($this->param));
    }
    /**
     * 会员积分扣除
     */
    public function memberReduce()
    {

        return $this->jump($this->logicMember->memberReduce($this->param));
    }
}
