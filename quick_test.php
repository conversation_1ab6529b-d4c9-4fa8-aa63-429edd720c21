<?php
/**
 * 快速测试修复后的 Draw 功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Jyotish Draw 快速测试</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;}</style>\n";

// 包含所有必要文件
$files = [
    'jyotish/src/Base/Traits/OptionTrait.php',
    'jyotish/src/Base/Traits/GetTrait.php',
    'jyotish/src/Base/Utility.php',
    'jyotish/src/Renderer/AbstractRenderer.php',
    'jyotish/src/Renderer/Image.php',
    'jyotish/src/Draw.php'
];

echo "<h2>1. 包含文件测试</h2>\n";
foreach ($files as $file) {
    if (file_exists($file)) {
        require_once $file;
        echo "<span class='success'>✓</span> 成功包含: $file<br>\n";
    } else {
        echo "<span class='error'>✗</span> 文件缺失: $file<br>\n";
        exit("无法继续测试");
    }
}

echo "<h2>2. 类实例化测试</h2>\n";
try {
    use Jyotish\Draw\Draw;
    
    // 测试创建 Draw 实例
    $draw = new Draw(200, 150, Draw::RENDERER_IMAGE);
    echo "<span class='success'>✓</span> 成功创建 Draw 实例<br>\n";
    
    // 测试 setOptions 方法
    $options = [
        'fontColor' => '#FF0000',
        'fontSize' => 12,
        'strokeColor' => '#0000FF',
        'strokeWidth' => 2
    ];
    $draw->setOptions($options);
    echo "<span class='success'>✓</span> 成功调用 setOptions 方法<br>\n";
    
    // 测试绘制文本
    $draw->drawText("测试文本", 100, 50, ['textAlign' => 'center']);
    echo "<span class='success'>✓</span> 成功绘制文本<br>\n";
    
    // 测试绘制多边形
    $trianglePoints = [100, 70, 80, 120, 120, 120];
    $draw->drawPolygon($trianglePoints, ['strokeColor' => '#00FF00']);
    echo "<span class='success'>✓</span> 成功绘制多边形<br>\n";
    
    echo "<h2>3. 功能测试完成</h2>\n";
    echo "<p><span class='success'>✓ 所有基本功能测试通过！</span></p>\n";
    echo "<p>现在您可以：</p>\n";
    echo "<ul>\n";
    echo "<li><a href='test_image_output.php' target='_blank'>查看图像输出测试</a></li>\n";
    echo "<li><a href='test_svg_output.php' target='_blank'>查看 SVG 输出测试</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 测试失败: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>4. 使用示例</h2>\n";
echo "<p>基本使用代码：</p>\n";
echo "<pre style='background:#f5f5f5;padding:10px;'>";
echo htmlspecialchars('<?php
// 包含文件
require_once "jyotish/src/Base/Traits/OptionTrait.php";
require_once "jyotish/src/Base/Traits/GetTrait.php";
require_once "jyotish/src/Base/Utility.php";
require_once "jyotish/src/Renderer/AbstractRenderer.php";
require_once "jyotish/src/Renderer/Image.php";
require_once "jyotish/src/Draw.php";

use Jyotish\Draw\Draw;

// 创建绘图实例
$draw = new Draw(400, 300, Draw::RENDERER_IMAGE);

// 设置选项
$draw->setOptions([
    "fontColor" => "#000000",
    "strokeColor" => "#FF0000",
    "strokeWidth" => 2
]);

// 绘制文本
$draw->drawText("Hello World!", 200, 50, ["textAlign" => "center"]);

// 绘制三角形
$draw->drawPolygon([200, 100, 150, 200, 250, 200], ["strokeColor" => "#00FF00"]);

// 输出图像
$draw->render();
?>');
echo "</pre>\n";
?>
