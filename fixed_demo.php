<?php
/**
 * Jyotish Draw 修复版演示
 * 
 * 这个演示包含了所有必要的依赖文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <title>Jyotish Draw 修复版演示</title>\n";
echo "    <style>\n";
echo "        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n";
echo "        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo "        .success { color: #008000; font-weight: bold; }\n";
echo "        .error { color: #ff0000; font-weight: bold; }\n";
echo "        .warning { color: #ff8800; font-weight: bold; }\n";
echo "        .info { color: #0066cc; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>Jyotish Draw 修复版演示</h1>\n";

// 检查并包含所有必要文件
echo "<div class='section'>\n";
echo "<h2>1. 包含必要文件</h2>\n";

$requiredFiles = [
    'jyotish/src/Base/Traits/OptionTrait.php',
    'jyotish/src/Base/Traits/GetTrait.php', 
    'jyotish/src/Base/Utility.php',
    'jyotish/src/Renderer/AbstractRenderer.php',
    'jyotish/src/Renderer/Image.php',
    'jyotish/src/Renderer/Svg.php',
    'jyotish/src/Draw.php'
];

$allFilesExist = true;
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✓</span> 包含文件: $file<br>\n";
        require_once $file;
    } else {
        echo "<span class='error'>✗</span> 文件缺失: $file<br>\n";
        $allFilesExist = false;
    }
}

if (!$allFilesExist) {
    echo "<span class='error'>错误: 某些必要文件缺失，无法继续演示。</span><br>\n";
    echo "</div></body></html>\n";
    exit;
}

echo "</div>\n";

// 检查 GD 扩展
echo "<div class='section'>\n";
echo "<h2>2. 检查 GD 扩展</h2>\n";

if (extension_loaded('gd')) {
    echo "<span class='success'>✓</span> GD 扩展已加载<br>\n";
} else {
    echo "<span class='warning'>⚠</span> GD 扩展未加载，图像功能可能不可用<br>\n";
}

echo "</div>\n";

// 开始功能演示
echo "<div class='section'>\n";
echo "<h2>3. 功能演示</h2>\n";

try {
    use Jyotish\Draw\Draw;
    
    // 演示 1: 创建图像渲染器
    echo "<h3>3.1 创建图像渲染器</h3>\n";
    $draw = new Draw(400, 300, Draw::RENDERER_IMAGE);
    echo "<span class='success'>✓</span> 成功创建了 400x300 像素的图像渲染器<br>\n";
    
    // 演示 2: 设置选项
    echo "<h3>3.2 设置绘图选项</h3>\n";
    $options = [
        'fontColor' => '#FF0000',
        'fontSize' => 12,
        'strokeColor' => '#0000FF',
        'strokeWidth' => 2
    ];
    $draw->setOptions($options);
    echo "<span class='success'>✓</span> 成功设置了字体颜色、大小、描边颜色和宽度<br>\n";
    
    // 演示 3: 绘制文本
    echo "<h3>3.3 绘制文本</h3>\n";
    $textOptions = [
        'fontColor' => '#000000',
        'fontSize' => 14,
        'textAlign' => 'center'
    ];
    $draw->drawText("Hello Jyotish!", 200, 50, $textOptions);
    echo "<span class='success'>✓</span> 在坐标 (200, 50) 绘制了居中对齐的文本<br>\n";
    
    // 演示 4: 绘制多边形
    echo "<h3>3.4 绘制多边形</h3>\n";
    // 绘制一个三角形
    $trianglePoints = [
        200, 100,  // 顶点1 (x1, y1)
        150, 200,  // 顶点2 (x2, y2)
        250, 200   // 顶点3 (x3, y3)
    ];
    $polygonOptions = [
        'strokeColor' => '#00FF00',
        'strokeWidth' => 3
    ];
    $draw->drawPolygon($trianglePoints, $polygonOptions);
    echo "<span class='success'>✓</span> 绘制了一个绿色的三角形<br>\n";
    
    // 演示 5: 创建 SVG 渲染器
    echo "<h3>3.5 创建 SVG 渲染器</h3>\n";
    $svgDraw = new Draw(300, 200, Draw::RENDERER_SVG);
    echo "<span class='success'>✓</span> 成功创建了 300x200 像素的 SVG 渲染器<br>\n";
    
    // 在 SVG 中绘制一些内容
    $svgDraw->drawText("SVG 演示", 150, 50, ['textAlign' => 'center']);
    
    // 绘制一个矩形（使用多边形）
    $rectanglePoints = [
        50, 80,   // 左上
        250, 80,  // 右上
        250, 150, // 右下
        50, 150   // 左下
    ];
    $svgDraw->drawPolygon($rectanglePoints, ['strokeColor' => '#FF00FF']);
    echo "<span class='success'>✓</span> 在 SVG 中绘制了文本和矩形<br>\n";
    
    echo "<h3>3.6 错误处理测试</h3>\n";
    // 演示错误处理
    try {
        $invalidDraw = new Draw(100, 100, 'invalid_renderer');
    } catch (Exception $e) {
        echo "<span class='success'>✓</span> 正确捕获了无效渲染器异常: " . $e->getMessage() . "<br>\n";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 演示过程中发生错误: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "</div>\n";

// 提供图像输出链接
echo "<div class='section'>\n";
echo "<h2>4. 图像输出测试</h2>\n";
echo "<p>点击下面的链接查看实际生成的图像：</p>\n";
echo "<p><a href='image_test.php' target='_blank'>查看图像输出测试</a></p>\n";
echo "<p><a href='svg_test.php' target='_blank'>查看 SVG 输出测试</a></p>\n";
echo "</div>\n";

// 使用说明
echo "<div class='section'>\n";
echo "<h2>5. 使用说明</h2>\n";
echo "<p>现在所有必要的文件都已创建，您可以：</p>\n";
echo "<ul>\n";
echo "<li>使用 Draw 类创建图像或 SVG 渲染器</li>\n";
echo "<li>设置各种绘图选项（字体、颜色、描边等）</li>\n";
echo "<li>绘制文本和多边形</li>\n";
echo "<li>调用 render() 方法输出结果</li>\n";
echo "</ul>\n";

echo "<h3>基本用法示例：</h3>\n";
echo "<pre>";
echo htmlspecialchars('<?php
// 包含所有必要文件
require_once "jyotish/src/Base/Traits/OptionTrait.php";
require_once "jyotish/src/Base/Traits/GetTrait.php";
require_once "jyotish/src/Base/Utility.php";
require_once "jyotish/src/Renderer/AbstractRenderer.php";
require_once "jyotish/src/Renderer/Image.php";
require_once "jyotish/src/Draw.php";

use Jyotish\Draw\Draw;

// 创建绘图实例
$draw = new Draw(400, 300, Draw::RENDERER_IMAGE);

// 绘制内容
$draw->drawText("测试文本", 200, 50, ["textAlign" => "center"]);
$draw->drawPolygon([100, 100, 300, 100, 200, 200], ["strokeColor" => "#FF0000"]);

// 输出图像
$draw->render();
?>');
echo "</pre>\n";
echo "</div>\n";

echo "<p><em>演示完成时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
echo "</body>\n";
echo "</html>\n";
?>
