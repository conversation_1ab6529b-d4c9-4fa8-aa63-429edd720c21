<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

// 扩展配置文件，系统研发过程中需要的配置建议放在此处，与框架相关配置分离

return [
    // +----------------------------------------------------------------------
    // | 公众号类型
    // +1.未认证订阅号2.微信认证订阅3.未认证服务号4.微信认证服务号
    'appid_type' => [
        '1' => '未认证订阅号',
        '2' => '微信认证订阅',
        '3' => '未认证服务号',
        '4' => '微信认证服务号',
        '5' => '小程序',
    ],
    // +----------------------------------------------------------------------
    // | 微信菜单自定义菜类型
    // +----------------------------------------------------------------------
    'menu_type' =>[
        'view' => '跳转URL',
        'click' => '点击推事件',
        // 跳转URL用户点击view类型按钮后，微信客户端将会打开开发者在按钮中填写的网页URL，可与网页授权获取用户基本信息接口结合，获得用户基本信息。
        'scancode_push' => '扫码推事件',
        'scancode_waitmsg' => '扫码推事件且弹出“消息接收中”提示框',
        'pic_sysphoto' => '弹出系统拍照发图',
        'pic_photo_or_album' => '弹出拍照或者相册发图',
        'pic_weixin' => '弹出微信相册发图器',
        'location_select' => '弹出地理位置选择器',
    ],
    // +----------------------------------------------------------------------
    // | 微信素材类型 1.图片2.语音3.视频4.文本5.音乐6.图文7.多图文
    // +
    'material_typeid' => [
        '1' => ['type'=>'image','name'=>'图片'],
        '2' => ['type'=>'voice','name'=>'语音'],
        '3' => ['type'=>'video','name'=>'视频'],
        '4' => ['type'=>'text','name'=>'文本'],
        '5' => ['type'=>'music','name'=>'音乐'],
        '6' => ['type'=>'news','name'=>'单图文'],
        '7' => ['type'=>'mult','name'=>'多图文'],
        '8' => ['type'=>'event','name'=>'活动事件'],
    ],
    // +----------------------------------------------------------------------
    // | 微信素材类型 1.图片2.语音3.视频4.文本5.音乐6.图文7.多图文
    // +
    'message_typeid' => [
        '1' => ['type'=>'template','name'=>'模版消息'],
        '2' => ['type'=>'material','name'=>'素材消息'],
    ],
    'sex' => [
        '0' => '无',
        '1' => '男',
        '2' => '女',
    ],
    'wx_level' => [
        '1' => '普通',
        '69' => '审核员',
        '99' => '客服',
    ],
    'AtoN' => [
        'A' => 1,'B' => 2,'C' => 3,'D' => 4,'E' => 5,'F' => 6,'G' => 7,
    ],
];
