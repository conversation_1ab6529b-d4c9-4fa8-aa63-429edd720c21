<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;

/**
 * 广告控制器
 */
class Advertising extends UserBase
{

    /**
     * 广告列表
     */
    public function advertisingList()
    {

        $where = $this->logicAdvertising->getWhere($this->param_data);

        $data=$this->logicAdvertising->getAdvertisingList($where, '', 'sort desc');

        $data=$data->toArray();

        $data['imgs']=$this->logicFile->getPictureListUrl($data['data'],'cover_id');

		return $this->apiReturn($data);
    }
    /**
     * 广告无分页列表
     */
    public function advertisingColumn()
    {

        $where = $this->logicAdvertising->getWhere($this->param_data);

        $data['list']=$this->logicAdvertising->getAdvertisingColumn($where);

        $data['imgs']=$this->logicFile->getPictureListUrl($data['list'],'cover_id');

		return $this->apiReturn($data);
    }
    /**
     * 广告添加
     */
    public function advertisingAdd()
    {
	  
	   $regit=$this->logicAdvertising->advertisingEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 广告删除
     */
    public function advertisingDel()
    {

       $regit=$this->logicAdvertising->advertisingDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
