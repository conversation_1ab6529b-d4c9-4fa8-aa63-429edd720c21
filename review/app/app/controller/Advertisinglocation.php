<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;

/**
 * 广告分类控制器
 */
class Advertisinglocation extends UserBase
{

    /**
     * 广告分类列表
     */
    public function advertisingLocationList()
    {

        $where = $this->logicAdvertisingLocation->getWhere($this->param_data);

        $data=$this->logicAdvertisingLocation->getAdvertisingLocationList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 广告分类无分页列表
     */
    public function advertisingLocationColumn()
    {

        $data=$this->logicAdvertisingLocation->getAdvertisingLocationColumn(['status'=>1]);

		return $this->apiReturn($data);
    }
}
