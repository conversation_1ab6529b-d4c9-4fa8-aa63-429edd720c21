<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;

use app\common\controller\ControllerBase;
//use exwechat\exLog;
use exwechat\XMLParse;

/**
 * 余额充值记录控制器
 */
class Paynotify extends ControllerBase
{
    /**
     * 余额充值记录删除
     */
    public $post = array();////接受参数
    public $redata = array();////解析数据
    public $amountLogInfo = array();////解析数据

    public function _initialize()
    {
        $this->post = file_get_contents("php://input"); //接收微信参数
        if (empty($this->post)) {
            $this->redata = [];
        } else {
            $this->redata = XMLParse::xmlToArray($this->post);
        }

        $whereAmountLog['order_number'] = $this->redata['out_trade_no'];
        //$whereAmountLog['transaction_id'] = $this->redata['transaction_id'];
        $this->amountLogInfo = $this->logicAmountLog->getAmountLogInfo($whereAmountLog, $field = 'id');
    }

    //支付验证
    public function payNotify()
    {
        if (empty($this->amountLogInfo) and !empty($this->redata['sign'])) {


            if (strpos($this->redata['out_trade_no'], "A") !== false) {
                $this->activityOrderPay();  //活动购买接受
            } elseif (strrpos($this->redata['out_trade_no'], "D") !== false) {
                $this->payShopOrderNotify();
            } elseif (strrpos($this->redata['out_trade_no'], "VIP") !== false) {
                $this->vipOrderPay();  //VIP购买接受
            } elseif (strrpos($this->redata['out_trade_no'], "CT") !== false){

                $this->colorOrderPay();  //颜色测评请求
            }
        }

    }

    //商城购物支付
    public function payShopOrderNotify()
    {
        if (empty($this->amountLogInfo) and !empty($this->redata['sign'])) {

        }
    }

    /**
     * @return mixed活动报名支付
     */
    public function activityOrderPay()
    {
        $amount = $this->redata['total_fee'] / 100;
        $order_number = $this->redata["out_trade_no"];
        $user_id = explode("R", explode("U", $order_number)[1])[0];

        $activityData['order_number'] = $order_number;
        $activityData['user_id'] = $user_id;

        $regitActivity = $this->logicActivityJoin->getActivityJoinInfo($activityData);

        $activityJoinDdata['id'] = $this->logicActivityJoin->activityJoinEdit(['order_status' => 1], $activityData);   //修改订单状况

        $amountLog['order_number'] = $regitActivity['order_number'];
        $amountLog['order_id'] = $regitActivity['id'];
        $amountLog['type'] = 5;
        $amountLog['user_id'] = $user_id;
        $amountLog['money'] = $amount;
        $amountLog['transaction_id'] = $this->redata['transaction_id'];

        $amountLog['remark'] = config('ext_config.paylogtype')[5];
        $amountLog['transaction_id'] = $this->redata['transaction_id'];
        $amount_log_list[] = $amountLog;
        $this->logicAmountLog->setAmountLogList($amount_log_list);

        $this->return_success();
    }
    /**
     * @return mixed颜色活动支付
     */
    public function colorOrderPay()
    {
        $amount = $this->redata['total_fee'] / 100;
        $order_number = $this->redata["out_trade_no"];
        $user_id = explode("R", explode("U", $order_number)[1])[0];

        $activityData['order_number'] = $order_number;
        $activityData['user_id'] = $user_id;

        $colorLogData = $this->logicColorOrder->getColorOrderInfo($activityData,'id,amount,order_number,user_id');

        $this->logicColorOrder->colorOrderUpdate($activityData,['status'=>1]);   //修改订单状况

        $amountLog['order_number'] = $colorLogData['order_number'];
        $amountLog['order_id'] = $colorLogData['id'];
        $amountLog['type'] = 7;
        $amountLog['user_id'] = $user_id;
        $amountLog['money'] = $amount;
        $amountLog['transaction_id'] = $this->redata['transaction_id'];

        $amountLog['remark'] = config('ext_config.paylogtype')[7];

        $this->logicAmountLog->amountLogEdit($amountLog);

        $this->return_success();
    }
    /**
     * @return mixedVIP支付
     */
    public function vipOrderPay()
    {

        $amount = $this->redata['total_fee'] / 100;

        $order_number = $this->redata["out_trade_no"];
        $user_id = explode("R", explode("U", $order_number)[1])[0];


        $vipPayLogWhere['order_number'] = $order_number;
        $vipPayLogWhere['user_id'] = $user_id;


        $vipPayLog = $this->logicVipPayLog->getVipPayLogSingleInfo($vipPayLogWhere);


        if ($vipPayLog['amount'] == $amount) {  //计算是否与自己库数据相等
            return false;
        }

        $this->logicVipPayLog->vipPayLogEdit(['order_status' => 1], $vipPayLogWhere);

        if (!empty($vipPayLog['pid'])) {
            $this->logicVipPayLog->vipPayLogEdit(['order_status' => -1, 'status' => -1], ['id' => $vipPayLog['pid']]);
        }


        $this->logicVip->vipSetInc(['id' => $vipPayLog['v_id']], $field = 'number_orders');

        $this->logicWeiUser->weiUserWebEdit(['id'=>$user_id,'vip' => $vipPayLog['v_id']]);

        $amountLog['order_number'] = $vipPayLog['order_number'];
        $amountLog['order_id'] = $vipPayLog['id'];
        $amountLog['type'] = 2;
        $amountLog['user_id'] = $user_id;
        $amountLog['money'] = $amount;
        $amountLog['transaction_id'] = $this->redata['transaction_id'];
        $amountLog['remark'] = config('ext_config.paylogtype')[2];
        $amountLog['transaction_id'] = $this->redata['transaction_id'];
        $this->logicAmountLog->amountLogEdit($amountLog);

        $this->return_success();
    }

    /**
     * @return mixed课程支付
     */
    public function courseOrderPay()
    {

        $amount = $this->redata['total_fee'] / 100;

        $order_number = $this->redata["out_trade_no"];
        $user_id = explode("R", explode("U", $order_number)[1])[0];


        $coursePayLogWhere['order_number'] = $order_number;
        $coursePayLogWhere['user_id'] = $user_id;


        $coursePayLog = $this->logicCoursePayLog->getCoursePayLogSingleInfo($coursePayLogWhere);

//        if (floatval($coursePayLog['amount']) != $amount) {  //计算是否与自己库数据相等
//            return false;
//        }

        $coursePayLog = $this->logicCoursePayLog->getCoursePayLogSingleInfo($coursePayLogWhere);

        $this->logicCoursePayLog->coursePayLogEdit(['order_status' => 1], $coursePayLogWhere);

        $this->logicCoursePayLog->courseSetInc(['id' => $coursePayLog['course_id']], $field = 'number_orders');

        $amountLog['order_number'] = $coursePayLog['order_number'];
        $amountLog['order_id'] = $coursePayLog['id'];
        $amountLog['type'] = 1;
        $amountLog['user_id'] = $user_id;
        $amountLog['money'] = $amount;
        $amountLog['transaction_id'] = $this->redata['transaction_id'];
        $amountLog['remark'] = config('ext_config.paylogtype')[2];
        $amountLog['transaction_id'] = $this->redata['transaction_id'];

        $this->logicAmountLog->amountLogEdit($amountLog);

        $this->return_success();
    }

    //支付验证正确后执行返回事件
    private function return_success()
    {
        $return['return_code'] = 'SUCCESS';
        $return['return_msg'] = 'OK';
        $xml_post = '<xml>
                    <return_code>' . $return['return_code'] . '</return_code>
                    <return_msg>' . $return['return_msg'] . '</return_msg>
                    </xml>';
        echo $xml_post;
        exit;
    }
}
