<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;


/**
 * 砍价加入控制器
 */
class Upload extends UserBase
{
    /**
     * 图片上传
     */
    public function pictureUpload()
    {
        $result = $this->logicFile->pictureUpload();


        if (empty($result['url'])) {
            $result['url']='https://'.$_SERVER['SERVER_NAME'] . '/upload/picture/'  . $result['path'];
        }
        return $this->apiReturn($result);
    }
}
