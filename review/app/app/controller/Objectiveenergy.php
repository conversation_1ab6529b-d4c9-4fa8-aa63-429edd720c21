<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\app\controller;

/**
 * 客观能量控制器
 */
class Objectiveenergy extends UserBase
{

    /**
     * 客观能量列表
     */
    public function objectiveEnergyList()
    {

        $where = $this->logicObjectiveEnergy->getWhere($this->param_data);

        $data = $this->logicObjectiveEnergy->getObjectiveEnergyList($where, '', '');

        return $this->apiReturn($data);
    }

    /**
     * 客观能量自己的数据
     */
    public function objectiveEnergyMyIntroduce()
    {
        $where['user_id'] = $this->param_data['user_id'];

        $infoData = $this->logicWeiUserInfo->getWeiUserInfoInfo(['id' => $where['user_id']], 'birthday');

        if (empty($infoData['birthday']) || ($infoData['birthday'] == 0)) {
            return $this->apiReturn([API_CODE_NAME => 1090008, API_MSG_NAME => '请填写个人信息']);
        }

        $where['create_time'] = $infoData['birthday'];

        $dataObjectiveEnergy = $this->logicObjectiveEnergy->getObjectiveEnergyInfo($where, 'sun,moon,mercury,venus,mars,jupiter,saturn')->toarray();

        $plant = array('sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn');


        arsort($dataObjectiveEnergy);

        $energy_array_keys = array_keys($dataObjectiveEnergy);

        $talent_key_weakest = $energy_array_keys[6];

        $talent_select_weakest = ['name' => $talent_key_weakest, 'ltype' => 'B'];

        foreach ($dataObjectiveEnergy as $key => $vole) {
            if ($vole >= 8) {
                $single_select[$key] = ['name' => $key . '_' . $talent_key_weakest, 'ltype' => 'C', 'stype' => 'MAX'];
            } else if ($vole >= 0 && $vole < 8) {
                $single_select[$key] = ['name' => $key, 'ltype' => 'C', 'stype' => 'MID'];
            } else if ($vole < 0) {
                $single_select[$key] = ['name' => $key, 'ltype' => 'C', 'stype' => 'MC'];
            }
        }


        if ($dataObjectiveEnergy[$energy_array_keys[0]] >= 8) {
            if ($dataObjectiveEnergy[$energy_array_keys[1]] >= 8) {
                $talent_select_max = ['name' => $energy_array_keys[0] . '_' . $energy_array_keys[1], 'ltype' => 'A', 'stype' => 'M2'];
            } else {
                $talent_select_max = ['name' => $energy_array_keys[0], 'ltype' => 'A', 'stype' => 'M1'];
            }
        } else {
            $talent_select_max = ['name' => $energy_array_keys[0] . '_' . $energy_array_keys[1], 'ltype' => 'A', 'stype' => 'M3'];
        }

        $pnames_cn = array("太阳", "月亮", "水星", "金星", "火星", "木星", "土星");

        $talent_info_max = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($talent_select_max)->toArray();

        $talent_info_max_name = explode('_', $talent_select_max['name']);

        if (count($talent_info_max_name) > 1) {
            $talent_info_max['title'] = '最强天赋：' . $pnames_cn[array_search($talent_info_max_name[0], $plant)] . "能量&" . $pnames_cn[array_search($talent_info_max_name[1], $plant)] . '能量';
        } else {
            $talent_info_max['title'] = '最强天赋：' . $pnames_cn[array_search($talent_info_max_name[0], $plant)] . "能量";
        }

        $talent_info_list[] = $talent_info_max;

        $talent_info_weakest = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($talent_select_weakest)->toArray();

        $talent_info_weakest_name = explode('_', $talent_select_weakest['name']);

        if (count($talent_info_weakest_name) > 1) {
            $talent_info_weakest['title'] = '最弱天赋：' . $pnames_cn[array_search($talent_info_weakest_name[0], $plant)] . "能量&" . $pnames_cn[array_search($talent_info_weakest_name[1], $plant)] . '能量';
        } else {
            $talent_info_weakest['title'] = '最弱天赋：' . $pnames_cn[array_search($talent_info_weakest_name[0], $plant)] . "能量";
        }
        $talent_info_weakest['content'] .= $talent_info_weakest['content2'];
        $talent_info_list[] = $talent_info_weakest;





        foreach ($plant as $key => $value) {

            $valde= $single_select[$value];

            $valde_name = explode('_', $valde['name']);

            $single_select_valde = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($valde)->toArray();

            $single_select_valde['sore'] = $dataObjectiveEnergy[$valde_name[0]];
            $single_select_valde['name_c'] = $valde_name[0];
            $single_select_valde['title'] = $pnames_cn[array_search($valde_name[0], $plant)] . "(" . ucwords($valde_name[0]) . ")";

            $single_select_valde['title_vice'] = '您的 ' . $pnames_cn[array_search($valde_name[0], $plant)] . ' 天赋能量指数：' . floatval($dataObjectiveEnergy[$valde_name[0]]) . '分';

            if ($single_select_valde['stype'] == 'MAX' && $single_select_valde['ltype'] == 'C') {
                $single_select_valde['content'] = str_replace("REPLACE", $single_select_valde['content2'], $single_select_valde['content']);
            }

            $talent_info_list[] = $single_select_valde;

        }
        return $this->apiReturn($talent_info_list);
    }

    /**
     * 客观能量自己的数据
     */
    public function objectiveEnergyMyInfo()
    {
        $where['user_id'] = $this->param_data['user_id'];

        $infoData = $this->logicWeiUserInfo->getWeiUserInfoInfo(['id' => $where['user_id']], 'birthday');

        if (empty($infoData['birthday']) || ($infoData['birthday'] == 0)) {
            return $this->apiReturn([API_CODE_NAME => 1090008, API_MSG_NAME => '请填写个人信息']);
        }

        $where['create_time'] = $infoData['birthday'];

        $dataObjectiveEnergy = $this->logicObjectiveEnergy->getObjectiveEnergyInfo($where, 'sun,moon,mercury,venus,mars,jupiter,saturn');

        return $this->apiReturn($dataObjectiveEnergy);
    }

    /**
     * 客观能量当天数据
     */
    public function objectiveEnergyInfo()
    {

        $where = $this->logicObjectiveEnergy->getWhere($this->param_data);

        $start_time = strtotime(date('Y-m-d', time()));


        if (!empty($this->param_data['start_time'])) {
            $start_time = $this->param_data['start_time'];

        }

        $where['create_time'] = $start_time;


        $data = $this->logicObjectiveEnergy->getObjectiveEnergyInfo($where);

        if (empty($data)) {

            $infoData = $this->logicWeiUserInfo->getWeiUserInfoInfo(['id' => $where['user_id']], 'planet_json,birthday');

            if (empty($infoData['planet_json']) || ($infoData['planet_json'] == ' ') || empty($infoData['birthday'])) {
                return $this->apiReturn([API_CODE_NAME => 1090008, API_MSG_NAME => '请填写个人信息']);
            }

            $plant[0] = 'sun';
            $plant[1] = 'moon';
            $plant[2] = 'mercury';
            $plant[3] = 'venus';
            $plant[4] = 'mars';
            $plant[5] = 'jupiter';
            $plant[6] = 'saturn';

            $today_p_p = ($start_time + 43200);

            $where['create_time'] = $infoData['birthday'];

            $data = $this->logicObjectiveEnergy->getObjectiveEnergyInfo($where, 'sun,moon,mercury,venus,mars,jupiter,saturn');

            if(empty($data)){
                return $this->apiReturn([API_CODE_NAME => 1090008, API_MSG_NAME => '请填写个人信息']);
            }


            $tods_data = $this->logicObjectiveEnergy->thisScore($infoData['planet_json'], $today_p_p);

            $data=$data->toArray();

            foreach ($data as $key => &$datum) {


                $roers=$datum + $tods_data[$key];

                $datum = round($roers, 2);
            }


            $data['create_time'] = $start_time;
            $data['user_id'] = $where['user_id'];

          //  $this->logicObjectiveEnergy->objectiveEnergyEdit($data);
        }
        return $this->apiReturn($data);
    }

    /**
     * 客观能量无数据图
     */
    public function objectiveEnergyGraph()
    {

        $where['user_id'] = $this->param_data['user_id'];

        $infoData = $this->logicWeiUserInfo->getWeiUserInfoInfo(['id' => $where['user_id']], 'planet_json');

        if (empty($infoData['planet_json']) || ($infoData['planet_json'] == ' ')) {
            return $this->apiReturn([API_CODE_NAME => 1090008, API_MSG_NAME => '请填写个人信息']);
        }


        $day = date('d');

        $start_time = mktime(0, 0, 0, date('m'), 1);

        $end_time = mktime(0, 0, 0, date('m'), $day + 1);

        $where['create_time'] = ['between', [$start_time, $end_time]];

        $dataObjectiveEnergy = $this->logicObjectiveEnergy->getObjectiveEnergyColumn($where, '*', 'create_time');;

        $data = array();

        $plant[0] = 'sun';
        $plant[1] = 'moon';
        $plant[2] = 'mercury';
        $plant[3] = 'venus';
        $plant[4] = 'mars';
        $plant[5] = 'jupiter';
        $plant[6] = 'saturn';

        for ($i = 1; $i <= $day; $i++) {

            $data['time'][] = $i;
            $data['sun'][] = 0;
            $data['moon'][] = 0;
            $data['mercury'][] = 0;
            $data['venus'][] = 0;
            $data['mars'][] = 0;
            $data['jupiter'][] = 0;
            $data['saturn'][] = 0;
            $data['all'][] = 0;
            $between_time[] = [$start_time + ($i - 1) * 86400, $start_time + $i * 86400];
        }


        foreach ($dataObjectiveEnergy as $key => $value) {
            $i_d = (int)date("d", $value['create_time']);

            $data['sun'][$i_d - 1] = $value['sun'];
            $data['moon'][$i_d - 1] = $value['moon'];
            $data['mercury'][$i_d - 1] = $value['mercury'];
            $data['venus'][$i_d - 1] = $value['venus'];
            $data['mars'][$i_d - 1] = $value['mars'];
            $data['jupiter'][$i_d - 1] = $value['jupiter'];
            $data['saturn'][$i_d - 1] = $value['saturn'];

            $data['all'][$i_d - 1] = round($value['sun'] + $value['sun'] + $value['moon'] + $value['mercury'] + $value['venus'] + $value['mars'] + $value['jupiter'] + $value['saturn'], 2);
        }

        return $this->apiReturn($data);
    }

    /**
     * 客观能量添加
     */
    public function objectiveEnergyAdd()
    {

        $regit = $this->logicObjectiveEnergy->objectiveEnergyEdit($this->param_data);

        return $this->apiReturn(['id' => $regit]);

    }


    /**
     * 客观能量删除
     */
    public function objectiveEnergyDel()
    {

        $regit = $this->logicObjectiveEnergy->objectiveEnergyDel(['id' => $id]);

        return $this->apiReturn(['id' => $regit]);

    }
}
