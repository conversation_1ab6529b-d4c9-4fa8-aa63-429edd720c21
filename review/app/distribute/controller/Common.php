<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\distribute\controller;
use think\Cache;
class Common extends IndexBase
{

    public function page($current_page = 0, $last_page = 0, $offset = 3, $page_number = 7)
    {

        $content = get_page_html($current_page, $last_page, $offset, $page_number);

        return throw_response_exception(['content' => $content]);
    }
    public function getShare()
    {
        $tokens = getAppidToken('gh_3f4f0b09d232', true);
        $JSSDK = get_sington_object('JSSDKEx', "exwechat\\api\\JSSDK\\JSSDK", $tokens['access_token']);
        $share = $tokens;
        $share['timestamp'] = time();
        //$shortUrl = get_sington_object('shortUrl', "exwechat\\api\\account\\shortUrl",session('appid_conf')['access_token']);
        $share['url'] = URL_TRUE;
        $share['signature'] = $JSSDK->signature($tokens['ticket'], 'abcdefghijklmnopqrstu', time(), URL_TRUE);

        $this->view->engine->layout(false);
        $this->assign('share', $share);
    }

    /**
     * @return mixed
     * 我的邀请码
     */
    public function userMyPromote()
    {
        if(empty($this->param['invite'])){
            $this->jump([RESULT_ERROR, '非法载入参数', url('user/index')]);
        }
        $this->getShare();
        $addressInfo=$this->logicWeiUser->getWeiUserInfo(['invite'=>$this->param['invite']],'id,nickname,invite');
        $this->assign('userInfo', $addressInfo);
        return $this->fetch('my_promote');
    }
  /**
     * @return mixed
     * 我的邀请码
     */
    public function abnormal()
    {

        $this->view->engine->layout(false);
        return $this->fetch('abnormal');
    }

    // 获取验证码

    /**
     * @return
     * 获取验证码
     */
    public function getVerifyCode()
    {

        $data=$this->logicIndexBase->getSmsCode(['phone_number'=>$this->param['mobile']]);

        $this->view->engine->layout(false);

        return json($data);
    }

    // 获取自己验证码
    public function getYan()
    {
        $mobile = $this->param['mobile'];
        dump(getRedisHash('verifyCode', $mobile));
    }

}
