<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | authorization   | http://www.apache.org/licenses/LICENSE-2.0 )           |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\distribute\controller;

class User extends Oauthuserinfo
{

    // 我的个人中心
    public function index()
    {


        $infoData = $this->logicChannelList->getChannelListInfo(['id' => session('userinfo')['id']],'*');

        if (empty($infoData)) {
            $this->clearSession();
            header("Location:" . url('User/index'));
        }

        if($infoData['status']<0){
            $this->clearSession();
            $url=url('Common/abnormal');
            header("Location:$url");
            exit;
        }
        $this->assign('userInfo', $infoData);

        return $this->fetch('index');
    }

    public function clearSession()
    {
        session('userinfo', null);
        $this->index();
    }

    /**
     * @return mixed
     * 修改用户信息
     */
    public function userEdit()
    {
        IS_POST && $this->jump($this->logicChannelList->channelListEdit($this->param));
        $userInfo = $this->logicChannelList->getChannelListInfo(['id' => session('userinfo')['id']],'*');

        $this->assign('userInfo', $userInfo);
        $this->view->meta_title = '修改个人信息';
        return $this->fetch('user_edit');
    }
    /**
     * @return mixed
     * 用户安全
     */
    public function security()
    {

        $this->view->meta_title = '修改个人信息';
        return $this->fetch('security');
    }

    /**
     * @return mixed
     * 修改用户密码
     */
    public function passwordEdit()
    {
        IS_POST && $this->jump($this->logicWeiUser->weiUserPasswordEdit($this->param));
        $this->view->meta_title = '修改密码';
        $userInfo=$this->logicWeiUser->getChannelListInfo(['id'=>session('userinfo')['id']]);
        $this->assign('userInfo', $userInfo);
        return $this->fetch('password_edit');
    }

    /**
     * @return mixed
     * 修改交易密码
     */
    public function tradePasswordEdit()
    {

        IS_POST && $this->jump($this->logicWeiUser->weiUserTradePasswordEdit($this->param));
        $this->view->meta_title = '设置交易密码';
        $userInfo=$this->logicWeiUser->getChannelListInfo(['id'=>session('userinfo')['id']],'id');
        $this->assign('userInfo', $userInfo);
        return $this->fetch('trade_password_edit');
    }

    /**
     * @return mixed
     * 余额管理
     */
    public function topUp()
    {
        IS_POST && $this->jump($this->logicWeiUser->weiUserTopUp($this->param));
        $addressInfo = $this->logicWeiUser->getChannelListInfo(['id' => session('userinfo')['id']], 'id,mobile,amount,total_amount,uuid');
        $postUrl='https://zltcgd.com/water/mobile/water';
        $curlPost['mac']=$addressInfo['uuid'];
        $regic=post_data($postUrl,$curlPost);
        $addressInfo['amount']=0;
        if($regic['code']==200){
            $addressInfo['amount']=$regic['data']['water'];
        }
        $this->assign('userInfo', $addressInfo);
        return $this->fetch('topup');
    }

    /**
     * @return mixed
     * 余额管理
     */
    public function withdrawal()
    {
        IS_POST && $this->jump($this->logicWeiUser->weiUserWithdrawal($this->param));

        $addressInfo = $this->logicWeiUser->getChannelListInfo(['id' => session('userinfo')['id']], 'id,amount,total_amount');

        $this->assign('userInfo', $addressInfo);
        return $this->fetch('');

    }

    /**
     * 用户充值
     */
    public function userAmountTopup()
    {
        $url = url('userAmountWallet');

        if (empty($this->param['amount'])) {
            return json(['code' => 123, 'msg' => '支付金额不能为空']);
        }
        $userConfig = getAppidToken('gh_3f4f0b09d232');
        $payData['out_trade_no'] = 'A' . time() . 'U' . session('userinfo')['id'] . 'R' . mt_rand(1000, 9999);
        $payData['body'] = '余额充值';
        $payData['total_fee'] = $this->param['amount'];

        $payData['appid'] = $userConfig['appid'];
        $payData['openid'] = session('userinfo')['openid'];
        $payData['mch_id'] = $userConfig['mch_id'];
        $payData['key'] = $userConfig['mch_key'];

        $payData['notify_url'] = DOMAIN . '/wexc/Paynotify/payNotify';
        $exwechat = get_sington_object('payEx', "exwechat\\api\\pay\\wxXcPay", $payData);


        $regit = $exwechat->wxxcPay();

        $data['appId'] = $userConfig['appid'];
        $data['timeStamp'] = $regit['timeStamp'];
        $data['nonceStr'] = $regit['nonceStr'];
        $data['package'] = $regit['package'];
        $data['signType'] = $regit['signType'];
        $data['paySign'] = $regit['paySign'];

        return json($data);
    }


    /**
     * @return mixed
     * 金额记录
     */
    public function userAmountLog()
    {
        $where = $this->logicAmountLog->getApiWhere($this->param);

        $where['user_id'] = session('userinfo')['id'];

        $list = $this->logicAmountLog->getAmountLogList($where, '', 'id desc');

        $this->assign('list', $list);
        return $this->fetch('amount_log');
    }


    /*
     * 我的设置中心
     */
    public function mySet()
    {

        if (IS_POST) {
            $data['id'] = session('userinfo')['id'];
            $data['nickname'] = $this->param['nickname'];
            $data['sex'] = $this->param['sex'];
            $data['birthday'] = strtotime($this->param['birthday']);
            $this->jump($this->logicWeiUser->weiUserIdCardCertification($data));
        }
        $info = $this->logicWeiUser->getChannelListInfo(['id' => session('userinfo')['id']]);
        $this->assign('info', $info);
        return $this->fetch('my_set');
    }
}
