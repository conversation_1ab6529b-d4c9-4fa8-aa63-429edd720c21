<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;

/**
 * 前端首页控制器
 */
class Publics extends IndexBase
{


    //青少年pdf数据
    public function teenagerppdf()
    {
        $param['order_number']=$this->param['order_number'];


        $infoReport = $this->logicAstrolabeDecodeReport->getAstrolabeDecodeReportInfo($param,'id,sex,birthday,name,birth_district,planet_json');

        $info = $this->logicAstrolabeDecodeTeenager->getAstrolabeDecodeTeenagerInfo($param);

        if (!empty($infoReport['birth_district'])) {

            $birth_district = $this->logicArea->getAreaInfo(['id' => $infoReport['birth_district']], 'id,name,fullname');
            $infoReport['birth_district_array']=$birth_district['fullname'];
        }


        $info['infoReport']=$infoReport;

        $img_ids=[$info['talent_url'],$info['chart_url'],$info['energy_ur']];

        $img_ids_array=$this->logicFile->getPictureColumnUrl($img_ids);

        $info['img_array']=$img_ids_array;

        return json_encode($info);
    }

}
