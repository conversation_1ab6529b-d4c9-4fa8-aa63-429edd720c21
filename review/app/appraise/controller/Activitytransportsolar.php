<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\appraise\controller;

use astrology\planetName as planetName;

/**
 * 年运-太阳返照控制器
 */
class Activitytransportsolar extends UserBase
{

    /**
     * 年运-太阳返照列表
     */
    public function activityTransportSolarList()
    {

        $where = $this->logicActivityTransportSolar->getWhere($this->param_data);

        $data = $this->logicActivityTransportSolar->getActivityTransportSolarList($where, '', '');

        return $this->apiReturn($data);
    }

    /**
     * 年运-太阳返照无分页列表
     */
    public function activitytransportsolarcolum()
    {
        !empty($this->param['openid']) && $whereTransport['openid'] = $this->param['openid'];

        !empty($this->param['order_number']) && $whereTransport['order_number'] = $this->param['order_number'];

        if (empty($whereTransport)) {
            return $this->apiReturn(['code' => 99666, 'msg' => '订单查询用户id或订单号不能为空']);
        }

        $data = $this->logicActivityTransportSolar->getActivityTransportSolarList($whereTransport, 'id,name,order_number,transit_year,create_time,sex,birth_district,birthday,status', 'create_time desc', false);

        return $this->apiReturn($data);
    }

    /**
     * 年运-太阳返照详细
     */
    public function activityTransportSolarInfo()
    {
        $whereTransportSolar['id'] = $this->param['id'];
        if (!empty($this->param['openid'])) {
            $whereTransportSolar['openid'] = $this->param['openid'];
        } else {
            $whereTransportSolar['order_number'] = $this->param['order_number'];
        }
        $signs_guardian_english = ["Mars", "Venus", "Mercury", "Moon", "Sun", "Mercury", "Venus", "Mars", "Jupiter", "Saturn", "Saturn", "Jupiter"];

        $activityTransportSolarInfo = $this->logicActivityTransportSolar->getActivityTransportSolarInfo($whereTransportSolar);
        if (empty($activityTransportSolarInfo)) {
            return $this->apiReturn([RESULT_ERROR, '没查到您的计算数据哦']);
        }
        $data['create_time'] = $activityTransportSolarInfo['create_time'];
        $data['channel'] = $activityTransportSolarInfo['channel'];
        $data['transit_year'] = $activityTransportSolarInfo['transit_year'];
        $data['year'] = $activityTransportSolarInfo['year'];
        $data['status'] = $activityTransportSolarInfo['status'];
        $data['order_number'] = $activityTransportSolarInfo['order_number'];
        $data['openid'] = $activityTransportSolarInfo['openid'];
        $data['name'] = $activityTransportSolarInfo['name'];
        $data['sex'] = $activityTransportSolarInfo['sex'];
        $data['email'] = $activityTransportSolarInfo['email'];
        $data['birthday'] = $activityTransportSolarInfo['birthday'];

        $data['birth_district'] = $activityTransportSolarInfo['birth_district'];
        $data['living_district'] = $activityTransportSolarInfo['living_district'];
        $data['solar_birthday'] = $activityTransportSolarInfo['solar_birthday'];


            $birthday_years = date('Y', $data['birthday']);

            $birthday_month = date('m', $data['birthday']);

            $birthday_day = date('d', $data['birthday']);

            $birthday_hours = date('H', $data['birthday']);

            $birthday_mins = date('i', $data['birthday']);

            $birthday_secs = date('s', $data['birthday']);

            $planet_life_solar = json_decode($activityTransportSolarInfo['planet_json'], true);
            $planet_life = $planet_life_solar['life'];
            $planet_solar = $planet_life_solar['solar'];
            $solar_lin_score = json_decode($activityTransportSolarInfo['solar_lin_score'], true); //返照得分
            $life_lin_score = json_decode($activityTransportSolarInfo['life_lin_score'], true); //本命得分

            $data['solar_svg'] = $this->sample($planet_solar);


            $corpusRetrogradePalaceWhere['attribution'] = "beholding_luck";
            $corpusRetrogradePalaceWhere['status'] = 1;

            $corpusRetrogradePalace = $this->logicCorpus->getCorpusColumn($corpusRetrogradePalaceWhere, 'id,attribution,attribution_str,content1,content2,content3', 'title');


            $pressure_score_array = $this->houseGetIs($planet_solar['planet'], 6, $solar_lin_score);
            //压力指数
            if ($pressure_score_array['count'] == 0) {
                $pressure_score = $this->jisuansroe(($solar_lin_score['Saturn'] + $solar_lin_score['Moon']) / 2);
            } else {
                $pressure_score = $this->jisuansroe($pressure_score_array['sum'] / $pressure_score_array['count']);
            }
            $data['pressure_score'] = $pressure_score;

        //支付后
        if ($activityTransportSolarInfo['status'] == 1) {

            $houseChinese = array("第一宫", "第二宫", "第三宫", "第四宫", "第五宫", "第六宫", "第七宫", "第八宫", "第九宫", "第十宫", "第十一宫", "第十二宫");
            $signsChinese = array("白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "摩羯座", "水瓶座", "双鱼座");

            $planet_solarsolarPhase = $this->solarPhase($planet_solar, $planet_life);
            $planet_solar['planet'] = $planet_solarsolarPhase['solar_planet'];

            $true_love_night = ['Moon', 'Saturn', 'Jupiter', 'Mars', 'Sun', 'Venus', 'Mercury', 'meanNode', 'meanSouthNode'];
            $true_love_day = ['Sun', 'Venus', 'Mercury', 'Moon', 'Saturn', 'Jupiter', 'Mars', 'meanNode', 'meanSouthNode'];


            $night_day_love['Sun'] = ['num' => 7, 'var' => 10];
            $night_day_love['Venus'] = ['num' => 7, 'var' => 8];
            $night_day_love['Mercury'] = ['num' => 7, 'var' => 13];
            $night_day_love['Moon'] = ['num' => 7, 'var' => 9];
            $night_day_love['Saturn'] = ['num' => 7, 'var' => 11];
            $night_day_love['Jupiter'] = ['num' => 7, 'var' => 12];
            $night_day_love['Mars'] = ['num' => 7, 'var' => 7];
            $night_day_love['meanNode'] = ['num' => 1, 'var' => 3];
            $night_day_love['meanSouthNode'] = ['num' => 1, 'var' => 2];

            $birthday = ($data['birthday']);

            //用第一宫位和第七宫位判断白天和晚上
            if ($planet_life['house'][0]['longitude'] > $planet_life['house'][6]['longitude']) {
                if ($planet_life['planet'][0]['longitude'] <= $planet_life['house'][0]['longitude'] and $planet_life['planet'][0]['longitude'] > $planet_life['house'][6]['longitude']) {
                    $day_chart = true;
                } else {
                    $day_chart = false;
                }
            } else {
                if ($planet_life['planet'][0]['longitude'] > $planet_life['house'][0]['longitude'] and $planet_life['planet'][0]['longitude'] <= $planet_life['house'][6]['longitude']) {
                    $day_chart = false;
                } else {
                    $day_chart = true;
                }
            }
            $englishToChinese = array(
                'Sun' => '太阳',
                'Moon' => '月亮',
                'Mercury' => '水星',
                'Venus' => '金星',
                'Mars' => '火星',
                'Jupiter' => '木星',
                'Saturn' => '土星',
                'Uranus' => '天王星',
                'Neptune' => '海王星',
                'Pluto' => '冥王星');
            //返照行星能量
            foreach ($planet_solar['planet'] as $key => $value) {
                $hous_plant[$value['house_id']][] = $value;
            }

            //舒适指数

            $comfortable_score_array = $this->houseGetIs($planet_solar['planet'], 5, $solar_lin_score);
            if ($comfortable_score_array['count'] == 0) {
                $comfortable_score = $this->jisuansroe(($solar_lin_score['Venus'] + $solar_lin_score['Moon']) / 2);
            } else {
                $comfortable_score = $this->jisuansroe($comfortable_score_array['sum'] / $comfortable_score_array['count']);
            }
            $data['comfortable_score'] = $comfortable_score;


            //一。。。。。***今年的生活重点//

            //1、今年你希望突出的领域    1、返照太阳落入返照星盘的宫位  //2、返照太阳的相位，2个

            $area_key_vole = $corpusRetrogradePalace['今年你希望突出的领域-太阳' . $houseChinese[$planet_solar['planet'][0]['house_id'] - 1]];

            if (strstr($area_key_vole['content2'], ']') !== false) {
                $pattern = '/\[.*?\]/';
                $area_key_vole['content2'] = preg_replace($pattern, '', $area_key_vole['content2']);

            }
            $highlight_areas_list[] = ['key' => 'area', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];

            foreach ($planet_solar['planet'][0]['planet_allow_degree'] as $key => $value) {
                if (count($highlight_areas_list) < 3) {
                    if (!empty($corpusRetrogradePalace['今年你希望突出的领域-太阳' . $value["allow"] . $value['chinese_name']])) {
                        $area_key_vole = $corpusRetrogradePalace['今年你希望突出的领域-太阳' . $value["allow"] . $value['chinese_name']];

                        if (strstr($area_key_vole['content2'], ']') !== false) {
                            $pattern = '/\[.*?\]/';
                            dump($area_key_vole['content2']);
                            $area_key_vole['content2'] = preg_replace($pattern, '', $area_key_vole['content2']);

                        }

                        $highlight_areas_list[] = ['key' => 'area', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }

            $life_focus[] = ['title' => '今年你希望突出的领域', 'key' => 'highlight_areas', 'list' => $highlight_areas_list];

            //2、今年生活的重点   1.返照行星落入返照星盘四轴附近（4度容许度），顺序，中天，上升，天底，下降    2.返照上升落入本命星盘的宫位

            $focus_key_vole = $corpusRetrogradePalace['今年生活中重要事件-上升' . $houseChinese[$planet_solar['planet'][10]['house_id'] - 1]];
            $focus_life[] = ['key' => 'focus', 'title' => $focus_key_vole['content1'], 'str' => $focus_key_vole['content2']];
            $life_focus[] = ['title' => '今年生活中重要事件', 'key' => 'focus_life', 'list' => $focus_life];

            $data['life_focus'] = ['title' => '今年的生活重点', 'key' => 'life_focus', 'list' => $life_focus];


            //***今年的压力和轻松


            //读取土星、木星宫位，好，坏各一段，共24段，然后选择一段，那个压力高，高于一半得分，就选坏土星坏的，舒适高，高于平均分的一半，就选好的

            //1、外在环境      这一年你所处的外在环境  返照上升落入星座

//            $environment_key_vole = $corpusRetrogradePalace['外在环境-上升' . $signsChinese[$planet_solar['planet_ascendant']['sign_id']]];
//            $external_environment[] = ['key' => 'environment', 'title' => $environment_key_vole['content1'], 'str' => $environment_key_vole['content2']];
//
//            $environment_key_vole = $corpusRetrogradePalace['外在环境-月亮' . $signsChinese[$planet_solar['planet']['1']['sign_id']]];
//            $external_environment[] = ['key' => 'environment', 'title' => $environment_key_vole['content1'], 'str' => $environment_key_vole['content2']];

            $pingjunfen_zhi = ($data['comfortable_score'] + $data['pressure_score']) / 2;


            //舒适
            if ($data['comfortable_score'] > $pingjunfen_zhi) {

                $environment_key_vole = $corpusRetrogradePalace['土星' . (floor($planet_solar['planet'][6]['house_number'])) . '好'];
                $external_environment[] = ['key' => 'environment', 'title' => '今年让你感觉有压力的生活领域', 'str' => $environment_key_vole['content2']];
                $environment_key_vole = $corpusRetrogradePalace['木星' . (floor($planet_solar['planet'][5]['house_number'])) . '坏'];
                $external_environment[] = ['key' => 'environment', 'title' => '今年让你感觉有舒适的生活领域', 'str' => $environment_key_vole['content2']];
            } else {
                $environment_key_vole = $corpusRetrogradePalace['土星' . (floor($planet_solar['planet'][6]['house_number'])) . '坏'];
                $external_environment[] = ['key' => 'environment', 'title' => '今年让你感觉有压力的生活领域', 'str' => $environment_key_vole['content2']];
                $environment_key_vole = $corpusRetrogradePalace['木星' . (floor($planet_solar['planet'][5]['house_number'])) . '好'];
                $external_environment[] = ['key' => 'environment', 'title' => '今年让你感觉有舒适的生活领域', 'str' => $environment_key_vole['content2']];
            }


            $pressure_easily[] = ['title' => '外在环境', 'key' => 'external_environment', 'list' => $external_environment];

            //2、生活起伏  返照月亮返照星盘的宫位      2.返照上升落入本命星盘的宫位

            //$focus_key_vole = $corpusRetrogradePalace['生活起伏-月亮' . $houseChinese[$planet_solar['planet'][1]['house_id'] - 1]];
            //$life_ups[] = ['key' => 'life_ups', 'title' => $focus_key_vole['content1'], 'str' => $focus_key_vole['content2']];
            //$pressure_easily[] = ['title' => '生活起伏', 'key' => 'life_ups', 'list' => $life_ups];

            $data['pressure_easily'] = ['title' => '今年的压力和轻松', 'key' => 'pressure_easily', 'list' => $pressure_easily];
            //***个人成长

            //1、今年个人成长情况
            //1、返照上升落入星座成长
            //2、读取行星落第一宫，第三宫，第九宫综合解释，优先第一宫，第九宫，第三宫，一共读取两段，然后再按照行星排序。
            //3、补充：读取返照太阳0，60，120相位。如果不成相位，扩大到整宫制寻找，多个相位，按照行星得分排列。

            $focus_key_vole = $corpusRetrogradePalace['综述-上升' . $signsChinese[$planet_solar['planet'][10]['sign_id']]];

            $situation_liao[] = ['key' => 'life_ups', 'title' => $focus_key_vole['content1'], 'str' => $focus_key_vole['content2']];

            $personal_growth[] = ['title' => '今年个人成长情况', 'key' => 'situation', 'list' => $situation_liao];

            //2、个人成长机会  1、木星落入宫位 2、（木星良好相位，通用），与上行不重复选择得分最高的两个行星产生的相位。没有相位扩大到整个星座宫位制，选择1个

            $corpusRetrograde = $corpusRetrogradePalace['成长机会-木星' . $houseChinese[$planet_solar['planet'][5]['house_id'] - 1]];
            $opportunities[] = ['key' => 'opportunities', 'title' => $corpusRetrograde['content1'], 'str' => $corpusRetrograde['content2']];
            foreach ($planet_solar['planet'][5]['planet_allow_degree'] as $key => $value) {
                if ($value["allow"] == 0 or $value["allow"] == 60 or $value["allow"] == 120) {
                    if (!empty($corpusRetrogradePalace['成长机会-木星' . $value["allow"] . $value['chinese_name']])) {
                        $area_key_vole = $corpusRetrogradePalace['成长机会-木星' . $value["allow"] . $value['chinese_name']];
                        $growingchallenges[] = ['key' => 'opportunities', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }
            $personal_growth[] = ['title' => '个人成长机会', 'key' => 'opportunities', 'list' => $opportunities];

            //3、成长挑战   1、火星落入宫位  2、（火星负面相位，通用）选择得分最高的两个行星产生的相位。没有相位扩大到整个星座宫位制，选择1个

            $corpusRetrograde = $corpusRetrogradePalace['挑战-火星' . $houseChinese[$planet_solar['planet'][4]['house_id'] - 1]];
            $growingchallenges[] = ['key' => 'growing_challenges', 'title' => $corpusRetrograde['content1'], 'str' => $corpusRetrograde['content2']];
            foreach ($planet_solar['planet'][4]['planet_allow_degree'] as $key => $value) {
                if ($value["allow"] == 90 or $value["allow"] == 180) {
                    if (!empty($corpusRetrogradePalace['挑战-火星' . $value["allow"] . $value['chinese_name']])) {
                        $area_key_vole = $corpusRetrogradePalace['挑战-火星' . $value["allow"] . $value['chinese_name']];
                        $growingchallenges[] = ['key' => 'growing_challenges', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }
            $personal_growth[] = ['title' => '成长挑战', 'key' => 'growing_challenges', 'list' => $growingchallenges];

            $data['personal_growth'] = ['title' => '个人成长', 'key' => 'personal_growth', 'list' => $personal_growth];


            $gongwei = ["自我成长", "财务领域", "沟通和学习", "家庭", "爱情，创造，冒险或者子女", "工作和健康", "合作", "财务", "精神", "职业", "人际", "内心"];
            //这一年最可能感受到压力的领域是，
            //这一年最可能感受到轻松的领域是，

            //日生盘选火星，夜生盘选土星落入宫位通用关键词  4 6
            //日生盘选木星，夜生盘选金星落入宫位宫位通用关键词  5  3
            if ($day_chart == true) {
                //    $pressure_areas[]=['title' => '', 'key' => 'growing_challenges', 'str' => '这一年最可能感受到压力的领域是，'.];
            }

            //$data['pressure_areas'] = ['title' => '', 'key' => 'pressure_areas', 'list' => ];


            //***爱情

            //1、桃花情况
            // 1、读取落入第五宫行星，确定1个最强，只读取1个（可能有）如果感情有突破不选择无突破语料。
            // 2、读取落入第七宫行星，确定1个最强，只读取1个（可能有）如果感情有突破不选择无突破语料。
            // 3、读取金星宫位，（确定1段）
            // 4、读取金星相位相位超过3个，选择得分最高的两个行星产生的相位。没有相位扩大到整个星座宫位制，尽可能保证总数4段。如果12都空，这里就取3个相位。相位如果有突破，选择好的，没有突破选在不好的。
            $corpusRetrograde = $corpusRetrogradePalace['爱情运势评估-单身-金星' . $houseChinese[$planet_solar['planet'][3]['house_id'] - 1]];
            $peach_blossom[] = ['key' => 'opportunities', 'title' => $corpusRetrograde['content1'], 'str' => $corpusRetrograde['content2']];

            foreach ($planet_solar['planet'] as $key => $value) {
                if ($value['house_id'] == 5) {
                    if (!empty($corpusRetrogradePalace['爱情运势评估-单身' . $value["chinese_name"] . "第五宫"])) {
                        $area_key_vole = $corpusRetrogradePalace['爱情运势评估-单身' . $value["chinese_name"] . "第五宫"];
                        $peach_blossom[] = ['key' => 'growing_challenges', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
                if ($value['house_id'] == 7) {
                    if (!empty($corpusRetrogradePalace['爱情运势评估-单身' . $value["chinese_name"] . "第七宫"])) {
                        $area_key_vole = $corpusRetrogradePalace['爱情运势评估-单身' . $value["chinese_name"] . "第七宫"];
                        $peach_blossom[] = ['key' => 'growing_challenges', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }
            foreach ($planet_solar['planet'][3]['planet_allow_degree'] as $key => $value) {
                if (count($highlight_areas_list) < 5) {
                    if (!empty($corpusRetrogradePalace['爱情的挑战和机遇-金星' . $value["allow"] . $value['chinese_name']])) {
                        $area_key_vole = $corpusRetrogradePalace['爱情的挑战和机遇-金星' . $value["allow"] . $value['chinese_name']];
                        $peach_blossom[] = ['key' => 'growing_challenges', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }

            $love[3] = ['title' => '桃花情况', 'key' => 'situation', 'list' => $peach_blossom];
            //2、桃花时机     法达主运-副运-副运再分一次，然后选择金星和第五宫宫主为副运在分一次的那个区间，大概1个行星一个月


            if ($day_chart == true) {
                $link_true_nikg = $true_love_day;
            } else {
                $link_true_nikg = $true_love_night;
            }

            $new_birthday_time = time();


            $year_new_time = mktime($birthday_hours, $birthday_mins, $birthday_secs, $birthday_month, $birthday_day, date('Y', $new_birthday_time));

            $year_new = date('Y', $new_birthday_time) - $birthday_years;

            if ($new_birthday_time <= $year_new_time) {
                $year_new--;
            }
            $weelk = intval($year_new / 75);


            $link_true_nikg_b = array();
            $link_true_nikg_s = array();
            $birthday_d = $birthday;
            $birthday_up = $birthday;
            $key_number_vole = 0;

            $key_number = 0;
            $dd_array = array();
            foreach ($link_true_nikg as $key => $value) {
                $years = date('Y', $birthday_d);
                $month = date('m', $birthday_d);
                $day = date('d', $birthday_d);
                $hours = date('H', $birthday_d);
                $mins = date('i', $birthday_d);
                $secs = date('s', $birthday_d);

                $year_new_time = mktime($hours, $mins, $secs, $month, $day, ($years + $night_day_love[$value]['var']));

                $this_big_time = ($year_new_time - $birthday_d) / $night_day_love[$value]['num'];
                $small_true_nikg = ['Sun', 'Venus', 'Mercury', 'Moon', 'Saturn', 'Jupiter', 'Mars'];
                $small_true_nikg_two = $small_true_nikg;
                if (in_array($value, $small_true_nikg)) {
                    foreach ($small_true_nikg as $kys => $values) {
                        if ($values == $value) {
                            //重新获得
                            foreach ($small_true_nikg as $kys => $values) {


                                $birthday_start = $birthday_d;
                                $birthday_d += $this_big_time;
                                $birthday_end = $birthday_d;

                                if ($new_birthday_time >= $birthday_start and $new_birthday_time < $birthday_end) {

                                    $first = array_slice($small_true_nikg_two, 0, array_search($values, $small_true_nikg_two));
                                    $second = array_slice($small_true_nikg_two, array_search($values, $small_true_nikg_two));

                                    $result_san = array_merge($second, $first);

                                    $san_time = ($birthday_end - $birthday_start) / 7;

                                    foreach ($result_san as $kys => $values) {
                                        $san_birthday_start = $kys * $san_time + $birthday_start;
                                        $san_birthday_end = ($kys + 1) * $san_time + $birthday_start;

                                        $dd_array[$values] = [$san_birthday_start, $san_birthday_end];
                                    }
                                    break 3;
                                }
                            }
                            break;
                        } else {
                            unset($small_true_nikg[$kys]);
                            $small_true_nikg[] = $values;
                        }
                    }
                } else {
                    $birthday_start = $birthday_d;
                    $birthday_d += $this_big_time;
                    $birthday_end = $birthday_d;

                    if ($new_birthday_time >= $birthday_start and $new_birthday_time < $birthday_end) {

                    }

                }
            }

            $planet_solar_array = $planet_solar['planet'];

            $yun_zhou = array_values(array_unique(array_column($link_true_nikg, 'big')));

            array_splice($yun_zhou, 7, 2);

            $yun_zhou = array_merge($yun_zhou, $yun_zhou);

            $other_half_date = '今年脱单时机:' . date('Y-m-d H:i', intval($dd_array['Venus'][0])) . '至' . date('Y-m-d H:i', intval($dd_array['Venus'][1]));
            $other_half_date .= '\n' . date('Y-m-d H:i', intval($dd_array[$signs_guardian_english[$planet_life_solar['solar']['house'][4]['sign_id']]][0])) . '至' . date('Y-m-d H:i', intval($dd_array[$signs_guardian_english[$planet_life_solar['solar']['house'][4]['sign_id']]][1]));

            $love[4] = ['title' => '桃花时机', 'key' => 'timing', 'str' => $other_half_date];


            //3、感情突破
            //法达7宫主小运OR太阳返照太阳落入第七宫OR金星木星落入轴点OR金星落入第五宫OR
            //读取有突破语料，否则读取无突破


            if ($planet_solar['planet'][0]['house_id'] == 5 or $planet_solar['planet'][3]['house_id'] == 5 or $planet_solar['planet'][3]['house_id'] == 5) {
                $breakthrough[] = ['key' => 'emotional_breakthrough', 'title' => '', 'str' => $corpusRetrogradePalace['感情突破-有突破']['content2']];
            } else {
                $breakthrough[] = ['key' => 'emotional_breakthrough', 'title' => '', 'str' => $corpusRetrogradePalace['感情突破-没有突破']['content2']];
            }

            $love[5] = ['title' => '感情突破', 'key' => 'breakthrough', 'list' => $breakthrough];

            //五宫主星落入的星座，得分高于70是好，50-70是中，低于50是差
            $sign_id_guardian_two = $signs_guardian_english[$planet_solar['house'][4]['sign_id']];


            if ($solar_lin_score[$sign_id_guardian_two] > 70) {
                $peach_st = '好';
            } elseif ($solar_lin_score[$sign_id_guardian_two] > 50 and $solar_lin_score[$sign_id_guardian_two] < 70) {
                $peach_st = '中';
            } else {
                $peach_st = '差';
            }

            $relationship_status = array();
            foreach ($planet_solar_array as $key => $value) {
                if ($value['english_name'] == $sign_id_guardian_two) {
                    if (!empty($corpusRetrogradePalace['今年的感情状况-五宫主' . $signsChinese[$value['sign_id']] . $peach_st])) {
                        $area_key_vole = $corpusRetrogradePalace['今年的感情状况-五宫主' . $signsChinese[$value['sign_id']] . $peach_st];
                        $relationship_status[] = ['key' => 'relationship_status', 'title' => '', 'str' => $area_key_vole['content1']];
                    }
                }
            }
            $love[0] = ['title' => '今年的感情状况', 'key' => 'relationship_status', 'list' => $relationship_status];


            //5.2、今年爱情的挑战和机遇
            //两段，机遇一段，挑战一段
            //1、机遇
            //0，60，120度相位，，好中差按照年度好中差读取
            //
            //2、挑战
            //90180相位，好中差按照年度好中差读取

            $Love_Challenge = array();

            $jinxing_array = $planet_solar['planet'][3];

            $xiangwei = [0 => 1, 60 => 1, 90 => 3, 120 => 1, 180 => 3];

            foreach ($jinxing_array['planet_allow_degree'] as $key => $value) {

                if (in_array($value['allow'], [0, 60, 120]) and $peach_st=='好') {

                    if (!empty($corpusRetrogradePalace['今年爱情的挑战和机遇' . $value['chinese_name'] . '好' . $xiangwei[$value['allow']]])) {
                        $area_key_vole = $corpusRetrogradePalace['今年爱情的挑战和机遇' . $value['chinese_name'] . '好' . $xiangwei[$value['allow']]];
                        $Love_Challenge[] = ['key' => 'relationship_status', 'title' => '', 'str' => $area_key_vole['content1']];
                    }
                    //$Love_Challenge[]=['key'=>'Love_Challenge','title'=>'','str'=>$corpusRetrogradePalace['爱情-机会']['content1']];
                }
                if (in_array($value['allow'], [90, 180]) and $peach_st=='差') {
                    if (!empty($corpusRetrogradePalace['今年爱情的挑战和机遇' . $value['chinese_name'] . '差' . $xiangwei[$value['allow']]])) {
                        $area_key_vole = $corpusRetrogradePalace['今年爱情的挑战和机遇' . $value['chinese_name'] . '差' . $xiangwei[$value['allow']]];
                        $Love_Challenge[] = ['key' => 'relationship_status', 'title' => '', 'str' => $area_key_vole['content1']];
                    }
                }
                if (in_array($value['allow'], [0, 60, 90, 120, 180]) and $peach_st=='中') {
                    if (!empty($corpusRetrogradePalace['今年爱情的挑战和机遇' . $value['chinese_name'] . '中' . $xiangwei[$value['allow']]])) {
                        $area_key_vole = $corpusRetrogradePalace['今年爱情的挑战和机遇' . $value['chinese_name'] . '中' . $xiangwei[$value['allow']]];
                        $Love_Challenge[] = ['key' => 'relationship_status', 'title' => '', 'str' => $area_key_vole['content1']];
                    }
                }

            }
            $love[1] = ['title' => '今年爱情的挑战和机遇', 'key' => 'Love_Challenge', 'list' => $Love_Challenge];


            //5.3、今年爱情运势逐月分析 1、曲线图，见财运指数 健康指数月份表，81行开始

            $areaInfoLiving = $this->logicArea->getAreaInfo(['id' => $data['living_district']]);

            $birthdayToTime = strtotime($data['solar_birthday']);
            $transit_birth = $birthdayToTime - 86400 * 15;
            $utdatenow = date('d.m.Y', $transit_birth);

            $utnow = date('H:i:s', $transit_birth);

            $house = $areaInfoLiving['longitude'] . ',' . $areaInfoLiving['latitude'] . ',k';

            $arr = [
                'b' => $utdatenow,
                'p' => '1',
                'ut' => $utnow,
                'n' => '16',
                's' => '1',
                'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'e' => 'swe',
                'head',
                'roundsec',

            ];


//            $defug = $planet_life["planet"][1]['longitude'];
//
//            $exSweTest = get_sington_object('SweTest', "astrology\\SweTest");
//
//            $new_yeam=date('Y', $transit_birth);
//            $nwe_month=date('m', $transit_birth);
//            $jjjss=array();
//            for ($fsj=0;$fsj<12;$fsj++){
//                $nwe_month++;
//
//                $transit_birth = mktime(0, 0, 0, $nwe_month, 1, $new_yeam);
//
//                $utdatenow = date('d.m.Y', $transit_birth);
//                $arr['b'] = $utdatenow;
//
//                $behold_planet = $exSweTest->transitsProgressed($arr, $defug, 'd');
//                if(empty($behold_planet)){
//                    $transit_birth =$transit_birth-86400*15;
//                    $utdatenow = date('d.m.Y', $transit_birth);
//                    $arr['b'] = $utdatenow;
//
//                    $behold_planet = $exSweTest->transitsProgressed($arr, $defug, 'd');
//                }
//
//                //dump($arr);
//                //dump($behold_planet);
//                if(!empty($behold_planet)){
//                   // dump(date('Y-m-d H:i:s', $behold_planet));
//
//                }
//               // dump(date('Y-m-d H:i:s', $behold_planet));
//                $jjjss[]=$behold_planet;
//            }
//
//            $starsCode = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
//            $yuefa_dan_arr = [
//                'b' => $utdatenow,
//                'p' => '0123456789',
//                'house' => $house,
//                'ut' => $utnow,
//                'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
//                'g' => ',',
//                'head',
//                'roundsec',
//            ];


            //情绪指数
            //月亮落入1 4 7 10 宫	50
            //月亮落落入2 5 8 11宫	30
            //月亮落落入续宫	20
            //月亮金星三合或者六合，或者合相	20
            //月亮木星三合或者六合，或者合相	20
            //月亮和火星合相，刑相，冲相	-10
            //月亮和土星合相，刑相，冲相	-20
            //月亮没有金星木星，火星，土星相位	5
            //月亮和其他行星三合六合或者合相（累计最高10分）	10
//            $p = get_sington_object('SweTest', "astrology\\SweTest");
//            $yuefan_aiqing_sore=array();
//            foreach ($jjjss as $fffgg=>$gghjjs){
//                $yuefa_dan_arr['b']=date('d.m.Y', $gghjjs);
//                $yuefa_dan_arr['ut']=date('H:i:s', $gghjjs);
//                $behold_planet = $p->calculate($yuefa_dan_arr, $starsCode);
//                $yuefan_aiqing_sore[$fffgg]=0;
//                if(in_array(floor($behold_planet["planet"][1]["house_number"]),[1,4,7,10])){
//                    $yuefan_aiqing_sore[$fffgg]+=50;
//                }
//                if(in_array(floor($behold_planet["planet"][1]["house_number"]),[2,5,8,11])){
//                    $yuefan_aiqing_sore[$fffgg]+=30;
//                }
//                if(in_array(floor($behold_planet["planet"][1]["house_number"]),[2,5,8,11])){
//                    $yuefan_aiqing_sore[$fffgg]+=30;
//                }
//            }

            // dump($jjjss);
            //  exit();


            $yuliao = json_decode($activityTransportSolarInfo['yuliao'], true);

            $love[2] = ['title' => '今年爱情运势逐月分析', 'key' => 'moon_return_emotions', 'list' => $yuliao['love']];

            $data['love'] = ['title' => '爱情', 'key' => 'love', 'list' => $love];

            //***财运

            //1、主财情况
            //1、有行星落入第二宫，读取得分最高行星，三王星不参加排序，如果只有三王星冥王>海王>天王落入第二宫语料，and行星落入第八宫，OR没有读取金星落入宫位财富语料。


            $situation_fiscal = array();

            if (!empty($hous_plant[2])) {
                foreach ($hous_plant[2] as $key => $value) {
                    if (!empty($corpusRetrogradePalace['主财情况-' . $value["chinese_name"] . "第二宫"])) {
                        $area_key_vole = $corpusRetrogradePalace['主财情况-' . $value["chinese_name"] . "第二宫"];
                        $situation_fiscal[] = ['key' => 'situation_fiscal', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }

            //2、第二宫宫主落入宫位与星座组合为一段，星座语料放在宫位语料前面。共用一个关键词。


            $sign_id_guardian_two = $signs_guardian_english[$planet_solar['house'][1]['sign_id']];
            foreach ($planet_solar_array as $key => $value) {
                if ($value['english_name'] == $sign_id_guardian_two) {

                    if (!empty($corpusRetrogradePalace['主财情况-二宫主' . $houseChinese[$value['house_id'] - 1]])) {
                        $area_key_vole = $corpusRetrogradePalace['主财情况-二宫主' . $houseChinese[$value['house_id'] - 1]];
                        $situation_fiscal[] = ['key' => 'situation_fiscal', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                    if (!empty($corpusRetrogradePalace['主财情况-二宫主' . $signsChinese[$value['sign_id']]])) {
                        $area_key_vole = $corpusRetrogradePalace['主财情况-二宫主' . $signsChinese[$value['sign_id']]];
                        $situation_fiscal[] = ['key' => 'situation_fiscal', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }
            //3、读取金星财运相位，按照成相位行星得分排序，满足总体4段，没有相位扩大到整个星座宫位制。相位不分好坏。
//            foreach ($planet_solar_array[3]['planet_allow_degree'] as $key=>$value){
//                if($value['english_name']==$sign_id_guardian_two){
//                    $area_key_vole=$corpusRetrogradePalace['返照金星'.$value["allow"].'返照'.$value['chinese_name']];
//                    $situation_fiscal[]=['key'=>'situation_fiscal','title'=>$area_key_vole['content1'],'str'=>$area_key_vole['content2']];
//                    break;
//                }
//            }
            $fortune[] = ['title' => '主财情况', 'key' => 'fortune', 'list' => $situation_fiscal];
            /**/
            //3、读取二宫主小限月份作为注意时间。今年财运契机较好的时候是。例句，今年财运最好的时间是，xx月xx日-xx月xx日，
            //$facashijian='今年没有合适的发财时间哦，期待明年';

            $facashijian = date('Y-m-d', intval($dd_array[$signs_guardian_english[$planet_life_solar['solar']['house'][1]['sign_id']]][0])) . '至' . date('Y-m-d', intval($dd_array[$signs_guardian_english[$planet_life_solar['solar']['house'][1]['sign_id']]][1]));

            $fortune[] = ['title' => '今年财运契机较好', 'key' => 'situation', 'str' => '今年主财较好的时间段:' . $facashijian];

            //2、意外之财   第八宫宫主落入宫位，读取八宫主小限月份作为注意时间。例句，今年意外之财运势最好的时间是，xx月xx日-xx月xx日，
            $windfall = date('Y-m-d', intval($dd_array[$signs_guardian_english[$planet_life_solar['solar']['house'][7]['sign_id']]][0])) . '至' . date('Y-m-d', intval($dd_array[$signs_guardian_english[$planet_life_solar['solar']['house'][7]['sign_id']]][1]));

            $fortune[] = ['title' => '意外之财', 'key' => 'windfall_str', 'str' => '今年意外之财运势最好的时间是:' . $windfall];

            //八公主落入星座
            $sign_id_guardian_two = $signs_guardian_english[$planet_solar['house'][7]['sign_id']];
            foreach ($planet_solar_array as $key => $value) {
                if ($value['english_name'] == $sign_id_guardian_two) {
                    if (!empty($corpusRetrogradePalace['意外之财-八宫主' . $houseChinese[$value['house_id'] - 1]])) {
                        $area_key_vole = $corpusRetrogradePalace['意外之财-八宫主' . $houseChinese[$value['house_id'] - 1]];
                        $fortune[] = ['key' => 'situation_fiscal', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                    break;
                }
            }
            $investment = array();
            //投资  第五宫宫主星，落入的星座，投资注意事项
            $sign_id_guardian_two = $signs_guardian_english[$planet_solar['house'][4]['sign_id']];
            foreach ($planet_solar_array as $key => $value) {
                if ($value['english_name'] == $sign_id_guardian_two) {
                    if (!empty($corpusRetrogradePalace['投资-今年适合采取的投资态度-五宫主' . $signsChinese[$value['sign_id']]])) {
                        $area_key_vole = $corpusRetrogradePalace['投资-今年适合采取的投资态度-五宫主' . $signsChinese[$value['sign_id']]];
                        $investment[] = ['key' => 'investment', 'title' => $area_key_vole['content1'], 'str' => '例如:' . $area_key_vole['content2']];
                    }
                    if (!empty($corpusRetrogradePalace['投资-今年适合投资的行业-五宫主' . $houseChinese[$value['house_id'] - 1]])) {
                        $area_key_vole = $corpusRetrogradePalace['投资-今年适合投资的行业-五宫主' . $houseChinese[$value['sign_id']]];
                        $investment[] = ['key' => 'investment', 'title' => $area_key_vole['content1'], 'str' => '例如:' . $area_key_vole['content2']];
                    }
                    break;
                }
            }

            $fortune[] = ['title' => '投资', 'key' => 'windfall_str', 'list' => $investment];

            $data['fortune'] = ['title' => '财运', 'key' => 'fortune', 'list' => $fortune];


            //***事业

            //1、
            $guide = array();
            //1、调入十宫主落入星座部分。

            $sign_id_guardian_shi = $signs_guardian_english[$planet_solar['house'][9]['sign_id']];

            foreach ($planet_solar_array as $key => $value) {
                if ($signs_guardian_english[$value["sign_id"]] == $sign_id_guardian_shi) {

                    $area_key_vole = $corpusRetrogradePalace['职场求生指南-职场环境-十宫主' . $signsChinese[$value['sign_id']]];
                    $guide[] = ['key' => 'situation_fiscal', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    break;
                }
            }

            //2、落入第六宫OR第十宫的行星（超过三个行星，按照行星先天+后天得分排序只显示三个）
            if (!empty($hous_plant[6])) {
                foreach ($hous_plant[6] as $key => $value) {
                    if (!empty($corpusRetrogradePalace['职场求生指南-职场环境' . $value["chinese_name"] . "第六宫"])) {
                        $area_key_vole = $corpusRetrogradePalace['职场求生指南-职场环境' . $value["chinese_name"] . "第六宫"];
                        $guide[] = ['key' => 'situation_fiscal', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }
            if (!empty($hous_plant[10])) {
                foreach ($hous_plant[10] as $key => $value) {
                    if (!empty($corpusRetrogradePalace['职场求生指南-职场环境' . $value["chinese_name"] . "第十宫"])) {
                        $area_key_vole = $corpusRetrogradePalace['职场求生指南-职场环境' . $value["chinese_name"] . "第十宫"];
                        $guide[] = ['key' => 'situation_fiscal', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                    }
                }
            }

            $career[] = ['title' => '职场求生指南-职场环境', 'key' => 'guide', 'list' => $guide];
            //3、读取落入第三宫，第九宫的行星事业部分，一个宫位取1个，如果没有不取。

            //4、木星落入的宫位，（新增求生）
            $survival_advice = array();
            foreach ($planet_solar_array as $key => $value) {
                if ($value["chinese_name"] == '木星') {
                    if (!empty($corpusRetrogradePalace['职场求生建议-木星' . $houseChinese[$value['house_id'] - 1]])) {
                        $area_key_vole = $corpusRetrogradePalace['职场求生建议-木星' . $houseChinese[$value['house_id'] - 1]];
                        $survival_advice[] = ['key' => 'survival_advice', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                        break;
                    }

                }
            }
            $career[] = ['title' => '职场求生建议', 'key' => 'survival_advice', 'list' => $survival_advice];
            //2、创业商机


            $Entrepreneurial_opportunities = array();

            //3、创业的领域   读取10宫主落入星座。
            $field = array();
            $sign_id_guardian_shi = $signs_guardian_english[$planet_solar['house'][9]['sign_id']];

            if (empty($planet_solarsolarPhase['life_house'][9]["planet_array"])) {

                $area_key_vole = $corpusRetrogradePalace['创业商机-不适合创业'];
                $Entrepreneurial_opportunities[] = ['key' => 'field_business', 'title' => '', 'str' => $area_key_vole['content2']];
            } else {
                foreach ($planet_solar_array as $key => $value) {
                    if ($signs_guardian_english[$value["sign_id"]] == $sign_id_guardian_shi) {
                        $area_key_vole = $corpusRetrogradePalace['职场求生指南-职场环境-十宫主' . $signsChinese[$value['sign_id']]];
                        $field[] = ['key' => 'field_business', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
                        break;
                    }
                }
                $area_key_vole = $corpusRetrogradePalace['创业商机-适合创业'];
                $Entrepreneurial_opportunities[] = ['key' => 'field_business', 'title' => $area_key_vole['content1'], 'str' => $area_key_vole['content2']];
            }
            $career[] = ['title' => '创业商机', 'key' => 'opportunities', 'list' => $Entrepreneurial_opportunities];
            $career[] = ['title' => '创业的领域', 'key' => 'field', 'list' => $field];


            $data['career'] = ['title' => '事业', 'key' => 'career', 'list' => $career];


            //***健康

            //1、身体健康
            //1、读取上升落星座健康
            //2、今年需要注意的疾病，读取返照星盘最低低分行星
            //3、今年需要的健康建议，读取返照星盘最低低分行星

            $min = min($solar_lin_score);
            $min_key = array_search($min, $solar_lin_score);


            $healthbody = array();
            $healthbody_key = $corpusRetrogradePalace['健康-上升' . $signsChinese[$planet_solar['planet'][10]['sign_id']]];
            $healthbody[] = ['key' => 'healthbody', 'title' => $healthbody_key['content1'], 'str' => $healthbody_key['content2']];
            //2、今年需要注意的疾病，读取返照星盘最低低分行星
            $healthbody_key = $corpusRetrogradePalace['今年需要注意的疾病-' . $englishToChinese[$min_key]];
            $healthbody[] = ['key' => 'healthbody', 'title' => '今年需要注意的疾病', 'str' => $healthbody_key['content2']];
            //3、今年需要的健康建议，读取返照星盘最低低分行星
            $healthbody_key = $corpusRetrogradePalace['今年需要的健康建议-' . $englishToChinese[$min_key]];
            $healthbody[] = ['key' => 'healthbody', 'title' => '今年需要的健康建议', 'str' => $healthbody_key['content2']];
            $health[] = ['title' => '身体健康', 'key' => 'body', 'list' => $healthbody];

            $data['health'] = ['title' => '健康', 'key' => 'health', 'list' => $health];


            //运势指数表
            $growth_score = 0; //成长
            $peach_score = 0;  //桃花运
            $fortune_score = 0;  //财运
            $health_score = 0;   //健康
            $career_score = 0;  //事业

            foreach ($planet_solar_array as $key => $value) {
                if ($planet_solar['house'][1]['longitude'] >= $value['longitude'] and $value['longitude'] < $planet_solar['house'][2]['longitude']) {
                    if (!empty($breakthrough_str[$value["english_name"]])) {
                        $situation_fiscal[] = ['key' => 'situation_fiscal', 'title' => $breakthrough_str[$value["english_name"]]['title'], 'str' => $breakthrough_str[$value["english_name"]]['str']];
                    }
                }
            }
            $data['opportunities_challenge'] = $planet_solarsolarPhase['opportunities_challenge'];
            $data['solar_moon_score'] = json_decode($activityTransportSolarInfo['solar_moon_score'], true);


        } else {
            $time = 60 * 60 * 2 + strtotime($activityTransportSolarInfo['create_time']);
            $data['time'] = $time;
        }

        return $this->apiReturn($data);

    }

    //计算返照法达

    public function houseGetIs($planet, $id_house, $score)
    {
        $score_count = 0;
        $score_sum = 0;
        foreach ($planet as $key => $value) {
            if (!empty($score[$value['english_name']]) and floor($value['house_number']) == $id_house) {
                $score_count++;
                $score_sum += $score[$value['english_name']];
            }
        }
        return ['count' => $score_count, 'sum' => $score_sum];
    }

    //计算返照法达

    public function jisuansroe($sroe = [])
    {
        if ($sroe <= -10) {
            $value = 0;
        } else if ($sroe > -10 and $sroe <= -8) {
            $value = 1;
        } else if ($sroe > -8 and $sroe <= -6) {
            $value = 2;
        } else if ($sroe > -6 and $sroe <= -4) {
            $value = 3;
        } else if ($sroe > -4 and $sroe <= -2) {
            $value = 4;
        } else if ($sroe > -2 and $sroe <= 0) {
            $value = 5;
        } else if ($sroe > 0 and $sroe <= 2) {
            $value = 6;
        } else if ($sroe > 2 and $sroe <= 4) {
            $value = 7;
        } else if ($sroe > 4 and $sroe <= 6) {
            $value = 8;
        } else if ($sroe > 6 and $sroe <= 8) {
            $value = 9;
        } else if ($sroe > 8) {
            $value = 10;
        }
        return $value;
    }

    //计算返照法达

    /**
     * 计算反照行星数据
     */
    public function solarPhase($planet_solar, $planet_life)
    {
        $house = $planet_life['house'];
        $life_planet = $planet_life['planet'];
        $solar_planet = $planet_solar['planet'];

        $allow_degree['0'] = 2.5;
        $allow_degree['30'] = 2.5;
        //$allow_degree['45'] = 2.5;
        $allow_degree['60'] = 2.5;
        $allow_degree['90'] = 2.5;
        $allow_degree['120'] = 2.5;
        $allow_degree['180'] = 2.5;

        $opportunities_challenge['growth'] = 0;    //成长
        $opportunities_challenge['peach'] = 0;     //桃花
        $opportunities_challenge['fortune'] = 0;   //财富
        $opportunities_challenge['health'] = 0;     //健康
        $opportunities_challenge['career'] = 0;     //事业

        $plant_score['Sun'] = 70;
        $plant_score['Moon'] = 70;
        $plant_score['Mercury'] = 70;
        $plant_score['Venus'] = 70;
        $plant_score['Mars'] = 70;
        $plant_score['Jupiter'] = 70;
        $plant_score['Saturn'] = 70;
        $plant_score['Uranus'] = 70;


        foreach ($solar_planet as $keyg => &$value) {

            foreach ($house as $keyh => &$valueh) {
                $house_cha = abs($valueh['longitude'] - $value['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }
                if ($keyh < 11) {
                    $last_house = $house[$keyh + 1];
                } else {
                    $last_house = $house[0];
                }
                if ($valueh['longitude'] > $last_house['longitude']) {
                    if ($value['longitude'] < $last_house['longitude']) {
                        $value['longitude'] += 360;
                    }
                    $last_house['longitude'] += 360;
                }

                if ($value['longitude'] >= $valueh['longitude'] and $value['longitude'] < $last_house['longitude']) {
                    $value['house_id'] = $keyh + 1;
                    $value['house_longitude'] = $house_cha;
                    empty($valueh['planet_array']) && $valueh['planet_array'] = array();
                    $valueh['planet_array'][] = $value['code_name'];
                }
            }


            if (in_array($value['house_id'], [1, 9])) {
                if (in_array($value['code_name'], [0, 1, 2])) {
                    $opportunities_challenge['growth'] += 70;    //成长
                } else if ($value['code_name'] == 3) {
                    $opportunities_challenge['growth'] += 90;    //成长
                } else if ($value['code_name'] == 4) {
                    $opportunities_challenge['growth'] += 50;    //成长
                } else if ($value['code_name'] == 5) {
                    $opportunities_challenge['growth'] += 90;    //成长
                } else if ($value['code_name'] == 6) {
                    $opportunities_challenge['growth'] += 50;    //成长
                } else {
                    $opportunities_challenge['growth'] += 60;    //成长
                }
            }

            if (in_array($value['house_id'], [5, 7])) {
                if (in_array($value['code_name'], [0, 1, 2])) {
                    $opportunities_challenge['peach'] += 70;     //桃花
                } else if ($value['code_name'] == 3) {
                    $opportunities_challenge['peach'] += 90;     //桃花
                } else if ($value['code_name'] == 4) {
                    $opportunities_challenge['peach'] += 50;     //桃花
                } else if ($value['code_name'] == 5) {
                    $opportunities_challenge['peach'] += 90;     //桃花
                } else if ($value['code_name'] == 6) {
                    $opportunities_challenge['peach'] += 50;     //桃花
                } else {
                    $opportunities_challenge['peach'] += 60;     //桃花
                }
            }
            if (in_array($value['house_id'], [2, 8])) {
                if (in_array($value['code_name'], [0, 1, 2])) {
                    $opportunities_challenge['fortune'] += 70;   //财富
                } else if ($value['code_name'] == 3) {
                    $opportunities_challenge['fortune'] += 90;     //财富
                } else if ($value['code_name'] == 4) {
                    $opportunities_challenge['fortune'] += 50;     //财富
                } else if ($value['code_name'] == 5) {
                    $opportunities_challenge['fortune'] += 90;     //财富
                } else if ($value['code_name'] == 6) {
                    $opportunities_challenge['fortune'] += 50;     //财富
                } else {
                    $opportunities_challenge['fortune'] += 60;     //财富
                }
            }
            if (in_array($value['house_id'], [1, 6])) {
                if (in_array($value['code_name'], [0, 1, 2])) {
                    $opportunities_challenge['health'] = 0;     //健康
                } else if ($value['code_name'] == 3) {
                    $opportunities_challenge['health'] += 90;     //健康
                } else if ($value['code_name'] == 4) {
                    $opportunities_challenge['health'] += 50;     //健康
                } else if ($value['code_name'] == 5) {
                    $opportunities_challenge['health'] += 90;     //健康
                } else if ($value['code_name'] == 6) {
                    $opportunities_challenge['health'] += 50;     //健康
                } else {
                    $opportunities_challenge['health'] += 60;     //健康
                }
            }
            if (in_array($value['house_id'], [6, 10])) {
                if (in_array($value['code_name'], [0, 1, 2])) {
                    $opportunities_challenge['career'] = 0;     //事业
                } else if ($value['code_name'] == 3) {
                    $opportunities_challenge['career'] += 90;     //事业
                } else if ($value['code_name'] == 4) {
                    $opportunities_challenge['career'] += 50;     //事业
                } else if ($value['code_name'] == 5) {
                    $opportunities_challenge['career'] += 90;     //事业
                } else if ($value['code_name'] == 6) {
                    $opportunities_challenge['career'] += 50;     //事业
                } else {
                    $opportunities_challenge['career'] += 60;     //事业
                }
            }

            if (empty($value['planet_allow_degree'])) {
                $value['planet_allow_degree'] = array();
            }
            foreach ($life_planet as $key => $valueg) {

                $chazhi = abs($value['longitude'] - $valueg['longitude']);

                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                $planet_degree_lgit = 0;

                !empty($planet_degree[$value['code_name']]) && $planet_degree_lgit += $planet_degree[$value['code_name']];

                !empty($planet_degree[$valueg['code_name']]) && $planet_degree_lgit += $planet_degree[$valueg['code_name']];

                foreach ($allow_degree as $keyAd => $valueAd) {

                    $valueAd += $planet_degree_lgit;

                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {

                        $in_out = '-1';

                        if ($value['speed'] > $valueg['speed']) {
                            if ((fmod($value['longitude'], 30) < fmod($valueg['longitude'], 30))) {
                                $in_out = '1';
                            }
                        } else {
                            if ((fmod($valueg['longitude'], 30) < fmod($value['longitude'], 30))) {
                                $in_out = '1';
                            }
                        }

                        $planet_allow_degree = [
                            'planet_number' => $keyg,
                            'code_name' => $valueg['code_name'],
                            'english_name' => $valueg['english_name'],
                            'chinese_name' => $valueg['chinese_name'],
                            'current_longitude' => $valueg['longitude'],
                            'allow' => $keyAd,
                            'in_out' => $in_out
                        ];

                        $value['planet_allow_degree'][] = $planet_allow_degree;

                    }
                }
            }
        }
        return ['solar_planet' => $solar_planet, 'life_house' => $house, 'opportunities_challenge' => $opportunities_challenge];
    }

    //计算分值

    public function smallSmallCalculate($small_planet, $yun_zhou)
    {

        $yun_small_small = array();
        for ($ii = 0; $ii < 14; $ii++) {
            if ($small_planet == $yun_zhou[$ii]) {

                for ($jj = $ii; $jj < ($ii + 7); $jj++) {
                    $yun_small_small[] = $yun_zhou[$jj];
                }
                break;
            }
        }
        if (count($yun_small_small) == 0) {
            $yun_small_small[] = $small_planet;
        }
        return $yun_small_small;
    }

    //查找行星落入宫位

    public function bigSmallCalculate($yun_zhou, $longitude)
    {

        if ($longitude > 360 / 51 * 49) {
            $small_ge = intval(($longitude - 360 / 51 * 49) / (360 / 51));
            $small_ge_vale = $yun_zhou[$small_ge + 7];
            $big_ge_vale = $small_ge_vale;
            $small_ge += 49;
        } else {

            $small_ge = intval($longitude / (360 / 51));
            $big_ge = intval($small_ge / 7);

            $small_ge_vale = $yun_zhou[$small_ge % 7];
            $big_ge_vale = $yun_zhou[$big_ge];

        }
        return ['small_ge_vale' => $small_ge_vale, 'small_ge' => $small_ge, 'big_ge_vale' => $big_ge_vale];

    }


    public function developedCalculate($planets_data, $param, $link_true_nikg = [], $yun_zhou = [], $longitude)
    {

        $birthday = $param['birthday'];
        $life_house = $planets_data['house'];
        $life_planet = $planets_data['planet'];

        $new_birthday_time = strtotime($param['solar_birthday']);

        $years = date('Y', $birthday);

        $month = date('m', $birthday);

        $day = date('d', $birthday);

        $hours = date('H', $birthday);

        $mins = date('i', $birthday);

        $secs = date('s', $birthday);

        $year_new_time = mktime($hours, $mins, $secs, $month, $day, date('Y', $new_birthday_time));

        $year_new = date('Y', $new_birthday_time) - $years;

        if ($new_birthday_time <= $year_new_time) {
            $year_new--;
        }
        $weelk = intval($year_new / 75);

        $key_number = -1;
        $link_true_nikg_s = array();
        $link_true_nikg_b = array();


        for ($wk = 0; $wk <= $weelk; $wk++) {
            foreach ($link_true_nikg as $key => $value) {
                $value['time_start'] = mktime($hours, $mins, $secs, $month, ($day + $value['day']), ($years + $value['age'] + $wk * 75));

                $value['age'] += $wk * 75;

                $value['age_start'] = $value['age'] . '岁' . $value['day'] . '天';
                $value['standard_start'] = date('Y-m-d H', $value['time_start']);

                if ($key == (count($link_true_nikg) - 1)) {
                    $value['age_end'] = ($value['age'] + 1) . '岁';
                    $value['time_end'] = $value['time_start'] + 86400;
                } else {
                    $value['age_end'] = $link_true_nikg[$key + 1]['age'] . '岁' . $link_true_nikg[$key + 1]['day'] . '天';
                    $value['time_end'] = mktime($hours, $mins, $secs, $month, ($day + $link_true_nikg[$key + 1]['day']), ($years + $link_true_nikg[$key + 1]['age'] + $wk * 75));
                }
                $danwei = 360 / 51;

                $value['start_longitude'] = $danwei * $key;
                $value['end_longitude'] = $danwei * ($key + 1);

                if ($wk < 49) {
                    if ($value['start_longitude'] < $longitude and $value['end_longitude'] > $longitude) {
                        $danwei_er = $danwei / 7;
                        $dan_qian_l = $wk * $danwei;
                        $time_danwei = ($value['time_end'] - $value['time_start']) / 7;
                        for ($ii = 0; $ii < 14; $ii++) {
                            if ($value["planet"] == $yun_zhou[$ii]) {
                                for ($jj = $ii; $jj < ($ii + 7); $jj++) {
                                    //$yun_small_small[]=$yun_zhou[$jj];
                                    $start_longitude_t = $value['start_longitude'] + $danwei / 7 * ($jj - $ii);
                                    $end_longitude_t = $value['start_longitude'] + $danwei / 7 * ($jj - $ii + 1);
                                    $is_longitude = false;
                                    $small_small = ['planet' => $yun_zhou[$jj], 'time_start' => $value['time_start'] + $time_danwei * ($jj - $ii),
                                        'is_longitude' => $is_longitude,
                                        'time_end' => $value['time_start'] + $time_danwei * ($jj - $ii + 1),
                                        'start_longitude' => $start_longitude_t,
                                        'end_longitude' => $end_longitude_t];
                                    if ($start_longitude_t < $longitude and $end_longitude_t > $longitude) {
                                        $small_small['is_longitude'] = true;
                                        $small_small_true = $small_small;
                                    }

                                    $value['small_small'][] = $small_small;
                                }
                                break;
                            }
                        }
                    }
                } else {
                    $small_small = $value;
                    if ($value['start_longitude'] < $longitude and $value['end_longitude'] > $longitude) {
                        $small_small['is_longitude'] = true;
                        $small_small_true = $small_small;
                    }
                    $value['small_small'][] = $small_small;

                }

                $link_true_nikg_s[] = $value;

                if (empty($link_true_nikg_b[$value['big'] . $wk])) {

                    $link_true_nikg_b[$value['big'] . $wk] = $value;
                } else {
                    if ($value['time_end'] > $link_true_nikg_b[$value['big'] . $wk]['time_end']) {
                        $link_true_nikg_b[$value['big'] . $wk]['time_end'] = $value['time_end'];
                        $link_true_nikg_b[$value['big'] . $wk]['age_end'] = $value['age_end'];
                    }
                }
            }
        }
        $key_number_vole = 0;
        foreach ($link_true_nikg_s as $key => &$value) {
            if ($key < (count($link_true_nikg_s) - 1) and $new_birthday_time >= $value['time_start'] and $new_birthday_time < $link_true_nikg_s[$key + 1]['time_start']) {
                $key_number = $key;   //计算到法达在哪里
                $key_number_vole = ($new_birthday_time - $value['time_start']) / ($link_true_nikg_s[$key + 1]['time_start'] - $value['time_start']);   //计算到法达在哪里
                $value['this_year'] = true;
            } else if ($key == (count($link_true_nikg_s) - 1) and $new_birthday_time > $value['time_start']) {
                $key_number = $key;   //计算到法达在哪里
                $value['this_year'] = true;
            }
        }
        return ['small' => $link_true_nikg_s, 'small_small' => $small_small_true, 'big' => array_values($link_true_nikg_b), 'this_year' => $key_number, 'this_year_vole' => $key_number_vole];
    }

    public function getHouseId($house, $longitude)
    {

        foreach ($house as $key => $value) {
            if ($key < 11) {
                $last_house = $house[$key + 1];
            } else {
                $last_house = $house[0];
            }
            if ($longitude >= $value['longitude'] and $longitude < $last_house['longitude']) {
                return $key + 1;
            }
        }
    }

    /**
     * 计算星座内获得相位
     */
    public function planetPhase($planet, $status, $corpusRetrogradePalace)
    {
        $planetFont = array(
            "Sun" => "M",
            "Moon" => "N",
            "Mercury" => "O",
            "Venus" => "P",
            "Mars" => "Q",
            "Jupiter" => "R",
            "Saturn" => "S",
            "Uranus" => "T",
            "Neptune" => "U",
            "Pluto" => "V",
            "Ascendant" => "f",
            "MC" => "g",
            'Des' => 'h',
            'IC' => 'i',
            'a_0' => 'á',
            'a_30' => 'æ',
            'a_45' => 'è',
            'a_60' => 'å',
            'a_90' => 'ã',
            'a_120' => 'ä',
            'a_135' => 'é',
            'a_150' => 'ç',
            'a_180' => 'â',
        );


        $planet_allow_degree = [];

        $planet_degree['0'] = 0;
        $planet_degree['1'] = 0;

        $allow_degree['0'] = 4;
        $allow_degree['30'] = 4;
        $allow_degree['45'] = 4;
        $allow_degree['60'] = 4;
        $allow_degree['90'] = 4;
        $allow_degree['120'] = 4;
        $allow_degree['135'] = 4;
        $allow_degree['150'] = 4;
        $allow_degree['180'] = 4;

        $planet_twe = $planet;


        $turning_point_str[0] = ['title' => '自我身份认同、力量、生命力、意志、活力、目的、生活的方向、创造潜能'];
        $turning_point_str[1] = ['title' => '家和家庭、女性、饮食变化、健康'];
        $turning_point_str[2] = ['title' => '智力、学习、技能、证书、沟通、短途旅行'];
        $turning_point_str[3] = ['title' => '寻求快乐、人际关系、金钱、财产、自尊、爱情'];
        $turning_point_str[4] = ['title' => '行动、愤怒、自信、冲动、工作、男人、战斗'];
        $turning_point_str[5] = ['title' => '机会、财富、海外旅行、高等教育、教学、陌生经历、法律事务'];
        $turning_point_str[6] = ['title' => '事业、名誉、抱负、进步、成熟、责任、纪律、权威'];
        $turning_point_str[7] = ['title' => '变化、意外、灵感、突然的吸引、科技、名气'];
        $turning_point_str[8] = ['title' => '信仰、幻想和妄想、寻求真理、音乐和艺术、禁闭、丑闻、谎言、混乱'];
        $turning_point_str[9] = ['title' => '权力、控制、极端、强烈的经历、嫉妒、极端的财富'];

        $important_events = array();
        $turning_point = array();

        foreach ($planet_twe as $key => $value) {

            if (count($planet_allow_degree) > 4 and $status < 1) {
                break;
            }
            if (empty($value['planet_allow_degree'])) {
                $value['planet_allow_degree'] = array();
            }

            foreach ($planet as $keyg => $valueg) {

                if ($key == $keyg) {
                    continue;
                }
                $chazhi = abs($value['longitude'] - $valueg['longitude']);

                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                $planet_degree_lgit = 0;

                !empty($planet_degree[$value['code_name']]) && $planet_degree_lgit += $planet_degree[$value['code_name']];

                !empty($planet_degree[$valueg['code_name']]) && $planet_degree_lgit += $planet_degree[$valueg['code_name']];

                foreach ($allow_degree as $keyAd => $valueAd) {

                    $valueAd += $planet_degree_lgit;

                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {

                        $vael_l_key = '返照' . $value['chinese_name'] . $keyAd . '返照' . $valueg['chinese_name'];

                        if (!empty($corpusRetrogradePalace[$vael_l_key])) {
                            $planet_allow_degree[] = [
                                'code_name_one' => $value['code_name'],
                                'english_name_one' => $value['english_name'],
                                'chinese_name_one' => $value['chinese_name'],
                                'font_name_one' => $planetFont[$value['english_name']],
                                'allow' => $keyAd,
                                'font_allow' => $planetFont['a_' . $keyAd],
                                'code_name_two' => $valueg['code_name'],
                                'english_name_two' => $valueg['english_name'],
                                'chinese_name_two' => $valueg['chinese_name'],
                                'font_name_two' => $planetFont[$valueg['english_name']],
                                'corpus_title_str' => $corpusRetrogradePalace[$vael_l_key]['content1'],
                                'corpus_str' => $corpusRetrogradePalace[$vael_l_key]['content2'],
                            ];

                            if ($value['code_name'] == 0 and $valueg['code_name'] == 1 and in_array($keyAd, [0, 90, 180])) {


                                $important_events[] = [
                                    'corpus_title_str' => $corpusRetrogradePalace[$vael_l_key]['content1'],
                                    'corpus_str' => $corpusRetrogradePalace[$vael_l_key]['content2']];
                            }

                            if ($value['code_name'] == 0 and in_array($valueg['code_name'], [10, 11, 18, 19]) and in_array($keyAd, [0, 180])) {

                                $important_events[] = [
                                    'corpus_title_str' => $corpusRetrogradePalace[$vael_l_key]['content1'],
                                    'corpus_str' => $corpusRetrogradePalace[$vael_l_key]['content2']];
                            }

                            if (in_array($value['code_name'], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]) and in_array($valueg['code_name'], [10, 11, 18, 19]) and in_array($keyAd, [0, 180])) {

                                $turning_point[] = [
                                    'corpus_title_str' => '今年你最有可能发生和',
                                    'corpus_str' => $turning_point_str[$value['code_name']]['title'] . '相关的事情。'];
                            }


                        }

                    }
                }
            }
        }
        return ['planet_allow_degree' => $planet_allow_degree, 'important_events' => $important_events, 'turning_point' => $turning_point];
    }

    /**
     * 年运测评发送
     */
    public function smssend()
    {
        $param = $this->param_data;
        $activityTransportSolarInfo = $this->logicActivityTransportSolar->getActivityTransportSolarInfo(['order_number' => $param['order_number'], 'status' => 1]);

        if (empty($activityTransportSolarInfo)) {
            return $this->apiReturn([RESULT_ERROR, '没查到您的计算数据哦']);
        }


        $channel_name = '罗博科技';
        if (!empty($activityTransportSolarInfo['channel'])) {
            $channel = $this->logicChannelList->getChannelListInfo(['code' => $activityTransportSolarInfo['channel']]);
            if (!empty($channel)) {
                $channel_name = $channel['name'];
            }
        }
        $year = $activityTransportSolarInfo['year'];

        $year_tr = '2022';
        if ($year == 3) {
            $year_tr = '2022-2024';
        }

        if ($param['type'] == 1) {
            $parameter['sign_name'] = '罗博国际';
            $parameter['template_code'] = 'SMS_205448674';
            $parameter['phone_number'] = $param['account'];
            $parameter['template_param'] = ['mtname' => $param['order_number'], 'submittime' => 'transpor'];
            $this->logicApiBase->getSmsCode($parameter);
        } else {
            send_email($param['account'], $year_tr . '年太阳返照年运报告', '感谢您对' . $channel_name . '的肯定，这是您的订单信息：<br/>
服务内容：' . $year_tr . '年太阳返照年运报告<br/>
订单号：' . $param['order_number'] . '<br/>
<a href="https://xg.robustcn.com/sun/#/pages/query/query">【查看测算结果的链接】</a>', $channel_name);
        }

        return $this->apiReturn(['id' => true]);

    }

    /**
     * 年运-太阳返照添加
     */
    public function activityTransportSolarAdd()
    {

        $data = $this->param_data;

        $data['order_number'] = 'TU' . time() . 'W' . mt_rand(10, 99) . 'R' . mt_rand(100, 999);

        $data['amount'] = 19.9;
        $transit_year = date('Y', time());

        if (!empty($data['year']) and $data['year'] == 3) {
            $data['amount'] = 19.9*3;
        } else {
            $data['year'] = 1;
        }

        $ids = 0;
        for ($i = 0; $i < ((int)$data['year']); $i++) {

            $data['transit_year'] = $transit_year + $i;

            $regit = $this->logicActivityTransportSolar->activityTransportSolarApiEdit($data);
            if ($ids == 0) {
                $ids = $regit;
            }
        }


        return $this->apiReturn(['id' => $ids]);

    }


    /**
     * 计算星座内获得行星
     */
    //星盘计算
    public function sample($chart_data)
    {

        //$chart_data = json_decode($planet_json, true);

        $signs_phase = planetName::$signs_phase;

        $planetFont = planetName::$planetFont;

        $signsFont = planetName::$signsFont;

        $signsEnglish = planetName::$signsEnglish;

        $diameter = 600;
        $radius = $diameter / 2;
        $radius_one = $radius - 5;
        $radius_two = $radius - 50;
        $radius_three = $radius - 190;
        $radius_four = $radius - 220;

        $one_zodiac = $chart_data['planet_ascendant'];

        $one_zodiac_radian = $one_zodiac['longitude'] - 90;

        $svgHtml = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:math="http://exslt.org/math" version="1.0" viewBox="0 0 ' . $diameter . ' ' . $diameter . '" id="chart">';
        $svgHtml .= '<g id="chartbody">
                    <circle id="zodiac" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_one . '"></circle>
                    <circle id="hcircle" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_two . '"></circle>
                    <circle id="zodiac_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_three . '"></circle>
                    <circle id="hcircle_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_four . '"></circle>';

        for ($i = 0; $i < 12; $i++) {

            $i_du = 30 * $i;

            $zodiac_radian = (pi() / 180) * ($one_zodiac_radian - $i_du);


        }

        //宫头计算
        $house_h_array = $chart_data['house'];

        foreach ($house_h_array as $key => &$value) {

            $house_longitude = ($one_zodiac_radian) + 360 - $value['longitude'];

            $house_dark_x_bottom = $radius + sin((pi() / 180) * ($house_longitude)) * ($radius_four);
            $house_dark_y_bottom = $radius - cos((pi() / 180) * ($house_longitude)) * ($radius_four);
            if (!($key % 3)) {
                $house_dark_x_top = $radius + sin((pi() / 180) * ($house_longitude)) * ($radius_one + 10);
                $house_dark_y_top = $radius - cos((pi() / 180) * ($house_longitude)) * ($radius_one + 10);
            } else {
                $house_dark_x_top = $radius + sin((pi() / 180) * ($house_longitude)) * ($radius_two);
                $house_dark_y_top = $radius - cos((pi() / 180) * ($house_longitude)) * ($radius_two);
            }

            $svgHtml .= '<line  class="house_dark_grid" x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';

            if ($key == 11) {
                $zitijiaodu = abs(($one_zodiac_radian) + 360 - $house_h_array[0]['longitude'] - $house_longitude);

            } else {
                $zitijiaodu = abs(($one_zodiac_radian) + 360 - $house_h_array[$key + 1]['longitude'] - $house_longitude);
            }

            if ($zitijiaodu > 180) {
                $zitijiaodu = 360 - $zitijiaodu;
            }

            $house_id_x = $radius + sin((pi() / 180) * ($house_longitude - $zitijiaodu / 2)) * ($radius_four + 15);
            $house_id_y = $radius - cos((pi() / 180) * ($house_longitude - $zitijiaodu / 2)) * ($radius_four + 15);

            $svgHtml .= ' <g><text class="text_font house_id house_' . ($key + 1) . '" x="' . $house_id_x . '" y="' . $house_id_y . '"  serial="' . $key . '">' . ($key + 1) . '</text> </g>';


            $hou_cha = -5;
            if ($zitijiaodu > 180 or $zitijiaodu < 270) {
                $hou_cha = 5;
            }


            $sign_var = $value['sign'];
            //星座隔开线
            $zodiac_x_top = $radius + sin($house_longitude) * ($radius_one);
            $zodiac_y_top = $radius - cos($house_longitude) * ($radius_one);

            $zodiac_x_bottom = $radius + sin($house_longitude) * ($radius_two);
            $zodiac_y_bottom = $radius - cos($house_longitude) * ($radius_two);

            // $svgHtml .= '<line  class="zodiac_grid" x1="' . $zodiac_x_top . '" y1="' . $zodiac_y_top . '" x2="' . $zodiac_x_bottom . '" y2="' . $zodiac_y_bottom . '"/>';

            //星座字

            $svgHtml .= '<g id="' . $signsEnglish[$sign_var['sign_id']] . '">';

            //星座度数

            $zodiac_signs_deg_x = $radius + sin((pi() / 180) * ($house_longitude - $hou_cha)) * ($radius_two + 22);
            $zodiac_signs_deg_y = $radius - cos((pi() / 180) * ($house_longitude - $hou_cha)) * ($radius_two + 22);

            $svgHtml .= '<text class="text_font longitude__font longitude_' . $i . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $sign_var['deg'] . '°</text>';

            //星座秒数数

            $zodiac_signs_deg_x = $radius + sin((pi() / 180) * ($house_longitude + $hou_cha)) * ($radius_two + 22);
            $zodiac_signs_deg_y = $radius - cos((pi() / 180) * ($house_longitude + $hou_cha)) * ($radius_two + 22);

            $svgHtml .= '<text class="text_font longitude__font longitude_' . $i . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $sign_var['min'] . '′</text>';
            //星座字
            $zodiac_signs_x = $radius + sin((pi() / 180) * ($house_longitude)) * ($radius_two + 22);
            $zodiac_signs_y = $radius - cos((pi() / 180) * ($house_longitude)) * ($radius_two + 22);
            $svgHtml .= '<text class="text_font signs_font signs_' . $signsEnglish[$sign_var['sign_id']] . '" x="' . $zodiac_signs_x . '" y="' . $zodiac_signs_y . '" serial="' . $i . '">' . $signsFont[$sign_var['sign_id']] . '</text>';
            $svgHtml .= '</g>';


        }


        //行星计算位置

        $planet_array = $chart_data['planet'];

        $planet_text_array = array();

        $planet_arrays = $planet_array;
        array_multisort(array_column($planet_array, 'longitude'), SORT_ASC, $planet_arrays);

        foreach ($planet_arrays as $keygf => &$volegf) {
            if ($keygf > 0) {
                if ($volegf['longitude'] < ($planet_arrays[$keygf - 1]['longitude'] + 7)) {

                    $volegf['longitude'] = $planet_arrays[$keygf - 1]['longitude'] + 7;
                }
            }
            $planet_array_code_name[$volegf['code_name']] = $volegf['longitude'];
        }

        foreach ($planet_array as $key => $value) {

            $planet_longitude = $one_zodiac_radian + 360 - $value['longitude'];

            //外圈点
            $planet_x_two_circle = $radius + sin((pi() / 180) * ($planet_longitude)) * ($radius_two);
            $planet_y_two_circle = $radius - cos((pi() / 180) * ($planet_longitude)) * ($radius_two);
            $svgHtml .= '<circle class="planets_circle planets_' . $planet_array[$key]['english_name'] . '" cx="' . $planet_x_two_circle . '" cy="' . $planet_y_two_circle . '" r="1.5"></circle>';

            //外圈点
            $planet_x_four_circle = $radius + sin((pi() / 180) * ($planet_longitude)) * ($radius_four);
            $planet_y_four_circle = $radius - cos((pi() / 180) * ($planet_longitude)) * ($radius_four);
            $svgHtml .= '<circle class="planets_circle planets_' . $planet_array[$key]['english_name'] . '" cx="' . $planet_x_four_circle . '" cy="' . $planet_y_four_circle . '" r="1.5"></circle>';

            $planet_text_array = $one_zodiac_radian + 360 - $planet_array_code_name[$value['code_name']];
            $planet_x_text = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 25);
            $planet_y_text = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 25);
            $svgHtml .= '<g id="' . $value['english_name'] . '">';
            //行星点连线
            $svgHtml .= '<line style="stroke: gray;stroke-linecap: round;stroke-dasharray: 1,2;" x1="' . $planet_x_two_circle . '" y1="' . $planet_y_two_circle . '" x2="' . $planet_x_text . '" y2="' . $planet_y_text . '"/>';
            //行星字
            !empty($planetFont[$value['english_name']]) ? $planerFont = $planetFont[$value['english_name']] : $planerFont = '无';
            $svgHtml .= '<text class="text_font planet_font planets_' . $value['english_name'] . '" x="' . $planet_x_text . '" y="' . $planet_y_text . '" serial="' . $value['code_name'] . '">' . $planerFont . '</text>';
            //行星度
            $planet_x_deg_text = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 55);
            $planet_y_deg_text = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 55);
            $svgHtml .= '<text class="text_font longitude__font longitude_' . $value['english_name'] . '" x="' . $planet_x_deg_text . '" y="' . $planet_y_deg_text . '">' . $value['deg'] . '°</text>';

            //行星星座
            $planet_x_sign = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 85);
            $planet_y_sign = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 85);
            $svgHtml .= '<text class="text_font signs_font signs_' . $signsEnglish[$value['sign_id']] . '" x="' . $planet_x_sign . '" y="' . $planet_y_sign . '" serial="' . $value['sign_id'] . '">' . $signsFont[$value['sign_id']] . '</text>';

            //行星分
            $planet_x_min_text = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 115);
            $planet_y_min_text = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 115);
            $svgHtml .= '<text class="text_font longitude__font longitude_' . $value['english_name'] . '" x="' . $planet_x_min_text . '" y="' . $planet_y_min_text . '">' . $value['min'] . '′</text>';
            $svgHtml .= '</g>';
        }

        $svgHtml .= '</g></svg>';

        return $svgHtml;
    }
}
