<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\appraise\controller;
use app\common\controller\ControllerBase;

/**
 * 首页控制器
 */
class Apiinterface extends ControllerBase
{

    /**
     * 首页方法
     */
    public function index()
    {

        $list = $this->logicApiInterface->getApiList(['module'=>MODULE_NAME], true, 'sort');

        $code_list = $this->logicApiInterface->apiErrorCodeData();

        $this->assign('code_list', $code_list);

        $content = $this->fetch('content_default');

        $this->assign('content', $content);

        $this->assign('list', $list);

        return $this->fetch();
    }

    /**
     * API详情
     */
    public function details($id = 0)
    {

        // $list = $this->logicApiInterface->getApiList(['module'=>MODULE_NAME], true, 'sort');

        $info = $this->logicApiInterface->getApiInfo(['id' => $id]);

        $this->assign('info', $info);

        // 测试期间使用token ， 请勿公开自己的token
        if(empty(session('member_info'))){
            $test_access_token='请先登录后台选择正确的公众号';
        }else{
            $choose_appid=session('member_info')['choose_appid'];

            $apiInfo=$this->logicWeiAppid->getGroupInfo(['id'=>$choose_appid]);

            $test_access_token=$choose_appid.'*#'.$apiInfo['appid'];
        }
        $this->assign('test_access_token', $test_access_token);

        $content = $this->fetch('content_template');

        if (IS_AJAX) {

            return throw_response_exception(['content' => $content]);
        }
//
//        $this->assign('content', $content);
//
//        $this->assign('list', $list);
//
//        return $this->fetch('index');
    }
}
