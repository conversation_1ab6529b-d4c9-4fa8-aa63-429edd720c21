<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\appraise\controller;

use astrology\planetName as planetName;

/**
 * 心灵译码控制器
 */
class Astrolabedecode extends UserBase
{

    /**
     * 心灵译码列表
     */
    public function astrolabeDecodeList()
    {

        $where = $this->logicAstrolabeDecode->getWhere($this->param_data);

        $data = $this->logicAstrolabeDecode->getAstrolabeDecodeList($where, '', '');

        return $this->apiReturn($data);
    }

    /**
     * 心灵译码无分页列表
     */
    public function astrolabeDecodeColumn()
    {

        $data = $this->logicAstrolabeDecode->getAstrolabeDecodeList(['openid' => $this->param_data['openid']], 'id,name,sex,birth_district,birthday','create_time desc',false);

        $arrDTSDates=planetName::$arrDTSDates;
        foreach ($data as $key => &$vole) {
            foreach ($arrDTSDates as $keyds => $volesd) {
                $strtotime_start=strtotime($volesd[0]);
                $strtotime_end=strtotime($volesd[1]);
                if($vole['birthday']>$strtotime_start and $vole['birthday']<$strtotime_end){
                    $vole['birthday']+=3600;
                    $vole['birthday_date']=date("Y-m-d H:i:s", $vole['birthday']);
                }
            }


            if (!empty($vole['birth_district'])) {
                $birth_district = $this->logicArea->getAreaInfo(['id' => $vole['birth_district']], 'id,name,fullname');
                $birth_district_name = explode(" ", $birth_district['fullname']);
                $vole['birth_district_array'] = $this->logicArea->getAreaColumn(['name' => ['in', $birth_district_name]], 'id,name,fullname', 'level');
            }

        }

        return $this->apiReturn($data);
    }

    /**
     * 心灵译码添加
     */
    public function astrolabeDecodeAdd()
    {

        $regit = $this->logicAstrolabeDecode->astrolabeDecodeEdit($this->param_data);

        return $this->apiReturn(['id' => $regit]);

    }

    /**
     * 心灵译码详细
     */
    public function astrolabeDecodeInfo()
    {
        $where['id'] = $this->param_data['id'];
        $where['openid'] = $this->param_data['openid'];

        $type = $this->param_data['type'];

        $regit = $this->logicAstrolabeDecode->getAstrolabeDecodeInfo($where);

        if (empty($regit)) {
            return $this->apiReturn([API_CODE_NAME => 1010007, API_MSG_NAME => '您没有该订单']);
        }

        if(empty($regit['planet_json'])){
            $regit['birthday'] = date('Y-m-d H:i:s', $regit['birthday']);
            $this->logicAstrolabeDecode->astrolabeDecodeEdit($regit);
            $regit = $this->logicAstrolabeDecode->getAstrolabeDecodeInfo($where);
        }



        if ($type == 1) {
            $regit['svg'] = $this->sample($regit['planet_json']);
        }
        if ($type == 2) {
            $stars = 'all';
            if (!empty($this->param_data['stars'])) {
                $stars = $this->param_data['stars'];
            }
            $regit['talent'] = $this->talent($regit['score_json'], $stars);
        }
        if ($type > 2) {
            $order = $this->logicAstrolabeDecodeOrder->getAstrolabeDecodeOrderInfo(['openid' => $where['openid'], 'a_id' => $where['id'], 'type' => $type ]);
            if (empty($order)) {
                    $order['openid'] = $where['openid'];
                    $order['a_id'] = $where['id'];
                    $order['amount'] = 9.9;
                    $order['type'] = $type;
                    $order['version'] = $regit['version'];



                    $order['status'] = 0;
                    if($where['openid']=='ol3dIvyjydpaSIwWfQ1OLQAGXkYE'){
                       $order['status'] = 1;
                    }


                    $order['order_number'] = 'ADO' . time() . 'W' . mt_rand(10, 99) . 'R' . mt_rand(100, 999);
                    $order['id'] = $this->logicAstrolabeDecodeOrder->astrolabeDecodeOrderEdit($order);
                    $amountLog['order_number'] = $order['order_number'];
                    $amountLog['order_id'] = $order['id'];
                    $amountLog['type'] = 5;
                    $amountLog['model'] = 'astrolabe_decode_order';
                    $amountLog['money'] =  9.9;
                    $amountLog['remark'] = '心灵译码';
                    $this->logicAmountLog->amountLogEdit($amountLog);
            }


            if(!empty($this->param['code'])){
                $code=$this->param['code'];
                $dataPromoCode = $this->logicPromoCode->getPromoCodeUse(['code' => $code,'user_id'=>$this->param_data['id']]);
                if($dataPromoCode){

                    if($order['status']==0 ){
                        $order_inadd['id'] = $order['id'];
                        $order_inadd['status'] = 1;
                        $order['status'] = 1;
                         $this->logicAstrolabeDecodeOrder->astrolabeDecodeOrderEdit($order_inadd);

                    }


                    $type_ling=4;
                    if($type==4){
                        $type_ling=3;
                    }
                    $order_ling=$this->logicAstrolabeDecodeOrder->getAstrolabeDecodeOrderInfo(['openid' => $where['openid'], 'a_id' => $where['id'], 'type' => $type_ling]);
                    if(!empty($order_ling)){
                        if($order_ling['status']==0){
                            db('astrolabe_decode_order')->where('id', $order_ling['id'])->update(['status'=>1]);
                        }

                    }else{
                        $order_inadd=array();
                        $order_inadd['openid'] = $where['openid'];
                        $order_inadd['a_id'] = $where['id'];
                        $order_inadd['amount'] = 9.9;
                        $order_inadd['type'] = $type_ling;
                        $order_inadd['status'] = 1;
                        $order_inadd['order_number'] = 'ADO' . time() . 'W' . mt_rand(10, 99) . 'R' . mt_rand(100, 999);

                        $this->logicAstrolabeDecodeOrder->astrolabeDecodeOrderEdit($order_inadd);
                    }

                }else{
                    return $this->apiReturn([API_CODE_NAME => 1030105, API_MSG_NAME => '当前兑换码已使用']);
                }
            }





        }

        if ($type == 3) {
            $regit = $this->career($regit['score_json'], $order['status']);
            $regit['order_number'] = $order['order_number'];
            $regit['order_id'] = $order['id'];
        }
        if ($type == 4) {
            $regit = $this->love($regit, $order['status']);
            $regit['order_number'] = $order['order_number'];
            $regit['order_id'] = $order['id'];

        }

        return $this->apiReturn($regit);
    }

    /**
     * 心灵译码删除
     */
    public function astrolabeDecodeDel()
    {

        $where['id'] = $this->param_data['id'];

        $regit = $this->logicAstrolabeDecode->astrolabeDecodeDel($where);

        $this->logicAstrolabeDecodeOrder->astrolabeDecodeOrderDel(['a_id'=>$where['id']]);

        return $this->apiReturn(['id' => $regit]);

    }

    //星盘计算
    public function sample($planet_json)
    {

        $chart_data = json_decode($planet_json, true);

        $signs_phase = planetName::$signs_phase;

        $planetFont = planetName::$planetFont;

        $signsFont = planetName::$signsFont;

        $signsEnglish = planetName::$signsEnglish;

        $diameter = 600;
        $radius = $diameter / 2;
        $radius_one = $radius - 5;
        $radius_two = $radius - 50;
        $radius_three = $radius - 190;
        $radius_four = $radius - 220;

        $one_zodiac = $chart_data['planet_ascendant'];

        $one_zodiac_radian = $one_zodiac['longitude'] - 90;

        $svgHtml = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:math="http://exslt.org/math" version="1.0" viewBox="0 0 ' . $diameter . ' ' . $diameter . '" id="chart">';
        $svgHtml .= '<g id="chartbody">
                    <circle id="zodiac" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_one . '"></circle>
                    <circle id="hcircle" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_two . '"></circle>
                    <circle id="zodiac_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_three . '"></circle>
                    <circle id="hcircle_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_four . '"></circle>';

        for ($i = 0; $i < 12; $i++) {

            $i_du = 30 * $i;

            $zodiac_radian = (pi() / 180) * ($one_zodiac_radian - $i_du);


            //星座隔开线
            $zodiac_x_top = $radius + sin($zodiac_radian) * ($radius_one);
            $zodiac_y_top = $radius - cos($zodiac_radian) * ($radius_one);

            $zodiac_x_bottom = $radius + sin($zodiac_radian) * ($radius_two);
            $zodiac_y_bottom = $radius - cos($zodiac_radian) * ($radius_two);

            $svgHtml .= '<line  class="zodiac_grid" x1="' . $zodiac_x_top . '" y1="' . $zodiac_y_top . '" x2="' . $zodiac_x_bottom . '" y2="' . $zodiac_y_bottom . '"/>';

            //星座字

            $svgHtml .= '<g id="' . $signsEnglish[$i] . '">';

            //星座度数

            $zodiac_signs_deg_x = $radius + sin((pi() / 180) * ($one_zodiac_radian - 20 - $i_du)) * ($radius_two + 22);
            $zodiac_signs_deg_y = $radius - cos((pi() / 180) * ($one_zodiac_radian - 20 - $i_du)) * ($radius_two + 22);

            $svgHtml .= '<text class="text_font longitude__font longitude_' . $i . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $i . '°</text>';

            //星座秒数数

            $zodiac_signs_deg_x = $radius + sin((pi() / 180) * ($one_zodiac_radian - 10 - $i_du)) * ($radius_two + 22);
            $zodiac_signs_deg_y = $radius - cos((pi() / 180) * ($one_zodiac_radian - 10 - $i_du)) * ($radius_two + 22);

            $svgHtml .= '<text class="text_font longitude__font longitude_' . $i . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $i . '′</text>';
            //星座字
            $zodiac_signs_x = $radius + sin((pi() / 180) * ($one_zodiac_radian - 15 - $i_du)) * ($radius_two + 22);
            $zodiac_signs_y = $radius - cos((pi() / 180) * ($one_zodiac_radian - 15 - $i_du)) * ($radius_two + 22);
            $svgHtml .= '<text class="text_font signs_font signs_' . $signsEnglish[$i] . '" x="' . $zodiac_signs_x . '" y="' . $zodiac_signs_y . '" serial="' . $i . '">' . $signsFont[$i] . '</text>';
            $svgHtml .= '</g>';
        }


        //宫头计算
        $house_h_array = $chart_data['house'];

        foreach ($house_h_array as $key => &$value) {

            $house_longitude = ($one_zodiac_radian) + 360 - $value['longitude'];

            $house_dark_x_bottom = $radius + sin((pi() / 180) * ($house_longitude)) * ($radius_four);
            $house_dark_y_bottom = $radius - cos((pi() / 180) * ($house_longitude)) * ($radius_four);
            if (!($key % 3)) {
                $house_dark_x_top = $radius + sin((pi() / 180) * ($house_longitude)) * ($radius_one + 10);
                $house_dark_y_top = $radius - cos((pi() / 180) * ($house_longitude)) * ($radius_one + 10);
            } else {
                $house_dark_x_top = $radius + sin((pi() / 180) * ($house_longitude)) * ($radius_two);
                $house_dark_y_top = $radius - cos((pi() / 180) * ($house_longitude)) * ($radius_two);
            }

            $svgHtml .= '<line  class="house_dark_grid" x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';

            if ($key == 11) {
                $zitijiaodu = abs(($one_zodiac_radian) + 360 - $house_h_array[0]['longitude'] - $house_longitude);

            } else {
                $zitijiaodu = abs(($one_zodiac_radian) + 360 - $house_h_array[$key + 1]['longitude'] - $house_longitude);
            }

            if ($zitijiaodu > 180) {
                $zitijiaodu = 360 - $zitijiaodu;
            }

            $house_id_x = $radius + sin((pi() / 180) * ($house_longitude - $zitijiaodu / 2)) * ($radius_four + 15);
            $house_id_y = $radius - cos((pi() / 180) * ($house_longitude - $zitijiaodu / 2)) * ($radius_four + 15);

            $svgHtml .= ' <g><text class="text_font house_id house_' . ($key + 1) . '" x="' . $house_id_x . '" y="' . $house_id_y . '"  serial="' . $key . '">' . ($key + 1) . '</text> </g>';

        }


        //行星计算位置

        $planet_array = $chart_data['planet'];

        $planet_text_array = array();

        $planet_arrays = $planet_array;
        array_multisort(array_column($planet_array, 'longitude'), SORT_ASC, $planet_arrays);

        foreach ($planet_arrays as $keygf => &$volegf) {
            if ($keygf > 0) {
                if ($volegf['longitude'] < ($planet_arrays[$keygf - 1]['longitude'] + 7)) {

                    $volegf['longitude'] = $planet_arrays[$keygf - 1]['longitude'] + 7;
                }
            }
            $planet_array_code_name[$volegf['code_name']] = $volegf['longitude'];
        }

        foreach ($planet_array as $key => $value) {

            $planet_longitude = $one_zodiac_radian + 360 - $value['longitude'];

            //外圈点
            $planet_x_two_circle = $radius + sin((pi() / 180) * ($planet_longitude)) * ($radius_two);
            $planet_y_two_circle = $radius - cos((pi() / 180) * ($planet_longitude)) * ($radius_two);
            $svgHtml .= '<circle class="planets_circle planets_' . $planet_array[$key]['english_name'] . '" cx="' . $planet_x_two_circle . '" cy="' . $planet_y_two_circle . '" r="1.5"></circle>';

            //外圈点
            $planet_x_four_circle = $radius + sin((pi() / 180) * ($planet_longitude)) * ($radius_four);
            $planet_y_four_circle = $radius - cos((pi() / 180) * ($planet_longitude)) * ($radius_four);
            $svgHtml .= '<circle class="planets_circle planets_' . $planet_array[$key]['english_name'] . '" cx="' . $planet_x_four_circle . '" cy="' . $planet_y_four_circle . '" r="1.5"></circle>';

            $planet_text_array = $one_zodiac_radian + 360 - $planet_array_code_name[$value['code_name']];
            $planet_x_text = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 25);
            $planet_y_text = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 25);
            $svgHtml .= '<g id="' . $value['english_name'] . '">';
            //行星点连线
            $svgHtml .= '<line style="stroke: gray;stroke-linecap: round;stroke-dasharray: 1,2;" x1="' . $planet_x_two_circle . '" y1="' . $planet_y_two_circle . '" x2="' . $planet_x_text . '" y2="' . $planet_y_text . '"/>';
            //行星字
            !empty($planetFont[$value['english_name']]) ? $planerFont = $planetFont[$value['english_name']] : $planerFont = '无';
            $svgHtml .= '<text class="text_font planet_font planets_' . $value['english_name'] . '" x="' . $planet_x_text . '" y="' . $planet_y_text . '" serial="' . $value['code_name'] . '">' . $planerFont . '</text>';
            //行星度
            $planet_x_deg_text = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 55);
            $planet_y_deg_text = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 55);
            $svgHtml .= '<text class="text_font longitude__font longitude_' . $value['english_name'] . '" x="' . $planet_x_deg_text . '" y="' . $planet_y_deg_text . '">' . $value['deg'] . '°</text>';

            //行星星座
            $planet_x_sign = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 85);
            $planet_y_sign = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 85);
            $svgHtml .= '<text class="text_font signs_font signs_' . $signsEnglish[$value['sign_id']] . '" x="' . $planet_x_sign . '" y="' . $planet_y_sign . '" serial="' . $i . '">' . $signsFont[$value['sign_id']] . '</text>';

            //行星分
            $planet_x_min_text = $radius + sin((pi() / 180) * ($planet_text_array)) * ($radius_two - 115);
            $planet_y_min_text = $radius - cos((pi() / 180) * ($planet_text_array)) * ($radius_two - 115);
            $svgHtml .= '<text class="text_font longitude__font longitude_' . $value['english_name'] . '" x="' . $planet_x_min_text . '" y="' . $planet_y_min_text . '">' . $value['min'] . '′</text>';
            $svgHtml .= '</g>';
        }

        $svgHtml .= '</g></svg>';

        return $svgHtml;
    }


    //天赋报告计算
    public function talent($score_json, $stars)
    {

        $plant = array('sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn');
        $pnames_cn = array("太阳", "月亮", "水星", "金星", "火星", "木星", "土星");

        $dataObjectiveEnergy = json_decode($score_json, true);

        arsort($dataObjectiveEnergy);

        $energy_array_keys = array_keys($dataObjectiveEnergy);

        $talent_key_weakest = $energy_array_keys[6];

        if ($stars == 'all') {

            $talent_select_weakest = ['name' => $talent_key_weakest, 'ltype' => 'B'];

            if ($dataObjectiveEnergy[$energy_array_keys[0]] >= 8) {
                if ($dataObjectiveEnergy[$energy_array_keys[1]] >= 8) {
                    $talent_select_max = ['name' => $energy_array_keys[0] . '_' . $energy_array_keys[1], 'ltype' => 'A', 'stype' => 'M2'];
                } else {
                    $talent_select_max = ['name' => $energy_array_keys[0], 'ltype' => 'A', 'stype' => 'M1'];
                }
            } else {
                $talent_select_max = ['name' => $energy_array_keys[0] . '_' . $energy_array_keys[1], 'ltype' => 'A', 'stype' => 'M3'];
            }

            $talent_info_max = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($talent_select_max)->toArray();

            $talent_info_max_name = explode('_', $talent_select_max['name']);

            if (count($talent_info_max_name) > 1) {
                $talent_info_max['title'] = '最强天赋：' . $pnames_cn[array_search($talent_info_max_name[0], $plant)] . "能量&" . $pnames_cn[array_search($talent_info_max_name[1], $plant)] . '能量';
            } else {
                $talent_info_max['title'] = '最强天赋：' . $pnames_cn[array_search($talent_info_max_name[0], $plant)] . "能量";
            }

            $talent_info_list[] = $talent_info_max;

            $talent_info_weakest = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($talent_select_weakest)->toArray();

            $talent_info_weakest_name = explode('_', $talent_select_weakest['name']);

            if (count($talent_info_weakest_name) > 1) {
                $talent_info_weakest['title'] = '最弱天赋：' . $pnames_cn[array_search($talent_info_weakest_name[0], $plant)] . "能量&" . $pnames_cn[array_search($talent_info_weakest_name[1], $plant)] . '能量';
            } else {
                $talent_info_weakest['title'] = '最弱天赋：' . $pnames_cn[array_search($talent_info_weakest_name[0], $plant)] . "能量";
            }
            $talent_info_weakest['content'] .= $talent_info_weakest['content2'];
            $talent_info_list[] = $talent_info_weakest;
            return $talent_info_list;
        } else {
            $vole = $dataObjectiveEnergy[$stars];
            if ($vole >= 8) {
                $valde = ['name' => $stars . '_' . $talent_key_weakest, 'ltype' => 'C', 'stype' => 'MAX'];
            } else if ($vole >= 0 && $vole < 8) {
                $valde = ['name' => $stars, 'ltype' => 'C', 'stype' => 'MID'];
            } else if ($vole < 0) {
                $valde = ['name' => $stars, 'ltype' => 'C', 'stype' => 'MC'];
            }

            $valde_name = explode('_', $valde['name']);

            $single_select_valde = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($valde)->toArray();

            $single_select_valde['sore'] = $dataObjectiveEnergy[$valde_name[0]];
            $single_select_valde['name_c'] = $valde_name[0];
            $single_select_valde['title'] = $pnames_cn[array_search($valde_name[0], $plant)] . "(" . ucwords($valde_name[0]) . ")";

            $single_select_valde['title_vice'] = '您的' . $pnames_cn[array_search($valde_name[0], $plant)] . '天赋能量指数 ： <span class="title_score">' . floatval($dataObjectiveEnergy[$valde_name[0]]) . '</span>';

            if ($single_select_valde['stype'] == 'MAX' && $single_select_valde['ltype'] == 'C') {
                $single_select_valde['content'] = str_replace("REPLACE", $single_select_valde['content2'], $single_select_valde['content']);
            }

            return $single_select_valde;
        }
    }

    //事业告计算
    public function career($score_json, $order_status)
    {

        $planet_score = json_decode($score_json, true);

        //艺术天赋  artistic
        //运动天赋  movement
        //美貌天赋  beautiful
        //商业天赋  business
        //心灵天赋  mind
        //艺术天赋  writing

        $tits['artistic']='艺术天赋';
        $tits['movement']='运动天赋';
        $tits['beautiful']='美貌天赋';
        $tits['business']='商业天赋';
        $tits['mind']='心灵天赋';
        $tits['writing']='写作天赋';

        $business_report_array = explode("\r\n**\r\n", file_get_contents('./corpus_txt/astrolabe_decode/business_report.txt'));  //注意事项


        $score['artistic'] = intval($planet_score["venus"]);
        $score['movement'] = intval(($planet_score["mars"] + $planet_score["mars"]) / 2);
        $score['beautiful'] = intval(($planet_score["venus"] + $planet_score["mars"]) / 2);
        $score['business'] = intval(($planet_score["mercury"] + $planet_score["saturn"] + $planet_score["mars"]) / 3);
        $score['mind'] = intval(($planet_score["moon"] + $planet_score["jupiter"]) / 2);
        $score['writing'] = intval(($planet_score["mercury"] + $planet_score["saturn"]) / 2);


        $data['score']=$score;

        $data['status'] = $order_status;

        if ($order_status == 0) {
            return $data;
        }


        arsort($score);
        $business_keys = array_keys($score);

        if ($business_keys[0] == $business_keys[1]) {
            foreach ($business_report_array as $keysf=>$argv){
                $business_report_array_t = explode("\r\n", $argv);  //注意事项
                if($argv==$tits[$business_keys[0]]){
                    unset($business_report_array_t[0]);
                    $data['business_report'][]=$business_report_array_t;
                }
                if($argv==$tits[$business_keys[1]]){
                    unset($business_report_array_t[1]);
                    $data['business_report'][]=$business_report_array_t;
                }
            }

        }else{
            foreach ($business_report_array as $keysf=>$argv){
                $business_report_array_t = explode("\r\n", $argv);  //注意事项
                if($business_report_array_t[0]==$tits[$business_keys[0]]){
                    unset($business_report_array_t[0]);
                    $data['business_report'][]=$business_report_array_t;
                    break;
                }
            }
        }


        return $data;
    }

    //情感计算
    public function love($infoge, $order_status)
    {

        $p = get_sington_object('SweTest', "astrology\\SweTest");
        $planetChinese = planetName::$planetChinese;
        $signsChinese = planetName::$signsChinese;
        $signsFont = planetName::$signsFont;
        $signs_guardian_index = planetName::$signs_guardian_index;

        $starsCode = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'H'];

        $birthday = $infoge['birthday'];
        $sex = $infoge['sex'];

        $years = date('Y', $birthday);

        $month = date('m', $birthday);

        $day = date('d', $birthday);

        $age = floor((time() - $birthday) / 365.25 / 86400);

        $allow_degree['0'] = 5;
        $allow_degree['30'] = 5;
        $allow_degree['45'] = 5;
        $allow_degree['60'] = 5;
        $allow_degree['90'] = 5;
        $allow_degree['180'] = 5;


        $life_planet = json_decode($infoge['planet_json'], true)['planet'];

        foreach ($life_planet as $keyhhg => &$valuehhg) {

            $value_behold_house = floor($valuehhg['house_number']);

            $life_house_planet[$value_behold_house][] = $valuehhg;
            $valuehhg['planet_allow_degree'] = array();
            foreach ($life_planet as $keyg => $valueg) {
                if ($keyhhg == $keyg) {
                    continue;
                }
                $chazhi = abs($valuehhg['longitude'] - $valueg['longitude']);

                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                foreach ($allow_degree as $keyAd => $valueAd) {

                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {

                        $in_out = '-1';

                        if ($valuehhg['speed'] > $valueg['speed']) {
                            if ((fmod($valuehhg['longitude'], 30) < fmod($valueg['longitude'], 30))) {
                                $in_out = '1';
                            }
                        } else {
                            if ((fmod($valueg['longitude'], 30) > fmod($valuehhg['longitude'], 30))) {
                                $in_out = '1';
                            }
                        }
                        $valuehhg['planet_allow_degree'][] = ['planet' => $valueg['code_name'], 'degree' => $keyAd, 'diff' => $p->Convert_Longitude(abs(round(($chazhi - $keyAd), 4))), 'in_out' => $in_out];

                    }

                }
            }
        }

        $life_house = json_decode($infoge['planet_json'], true)['house'];

        $areaInfo = $this->logicArea->getAreaInfo(['id' => $infoge['birth_district']]);


        //遇艳闪婚脱单三个值

        $limit_birthdayToTime = $birthday + $age * 86400;

        $limit_birthdayToTime = $limit_birthdayToTime - $areaInfo['tz'] * 3600;
        //次限盘计算

        $utdatenow = date('d.m.Y', $limit_birthdayToTime);

        $utnow = date('H:i:s', $limit_birthdayToTime);

        $house = $areaInfo['longitude'] . ',' . $areaInfo['latitude'] . ',k';
        $arr = [
            'b' => $utdatenow,
            'p' => '0123456789',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',
            'g' => ',',
            'head',
            'roundsec',

        ];

        $time_limit_planet = $p->calculate($arr, ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']);

        $limit_planet = $time_limit_planet['planet'];

        $moon_limit_house = floor($limit_planet[1]['house_number']);

        if (empty($data['in_yan'])) {
            if ($moon_limit_house == 5 or $moon_limit_house == 7) {
                $data['in_yan'] = 90;
            }
        }

        if (empty($data['in_yan'])) {
            $v_x_l = [0, 60, 90, 120, 180];

            $chazhi_behold = abs($limit_planet[1]['longitude'] - $limit_planet[3]['longitude']);

            if ($chazhi_behold > 180) {
                $chazhi_behold = 360 - $chazhi_behold;
            }

            foreach ($v_x_l as $key => $value) {
                if ($chazhi_behold < ($value + 5) and $chazhi_behold > ($value - 5)) {
                    $data['in_yan'] = 80;
                    continue;
                }

            }
        }

        if (empty($data['in_yan'])) {
            foreach ($limit_planet as $key => $value) {
                $chazhi_behold = abs($value['longitude'] - $limit_planet[3]['longitude']);

                if ($chazhi_behold > 180) {
                    $chazhi_behold = 360 - $chazhi_behold;
                }

                if ($chazhi_behold < 65 and $chazhi_behold > 55) {
                    $data['in_yan'] = 80;
                    continue;
                }

                if ($chazhi_behold < 125 and $chazhi_behold > 115) {
                    $data['in_yan'] = 80;
                    continue;
                }

            }
        }
        if (empty($data['in_yan'])) {
            $data['in_yan'] = 50;
        }

        //脱单指数计算
        //返照盘计算

        //计算是否大于六个月

        $behold_birthday = mktime(0, 0, 0, $month, $day, date('Y', time()));

        //计算半年时间


        if ((time() - $behold_birthday) > (86400 * 30 * 6)) {

            $behold_birthday = mktime(0, 0, 0, $month, $day, date('Y', time()) + 1);

        }

        $behold_birthdayTime = $behold_birthday - $areaInfo['tz'] * 3600;

        $utdatenow = date('d.m.Y', strtotime(date('Y-m-d', $behold_birthdayTime)));

        $utnow = '12:0:0';


        $arr = [
            'b' => $utdatenow,
            'ut' => $utnow,
            'p' => '0',
            'n' => '6',
            's' => '1',
            'f' => 'pTlsj',
            'g' => ',',
            'e' => 'swe',
            'head',
            'roundsec',

        ];

        $behold_birthdayToTime = $p->transitsProgressed($arr, $life_planet[0]['longitude'], 'd');

        $utdatenow = date('d.m.Y', $behold_birthdayToTime);

        $utnow = date('H:i:s', $behold_birthdayToTime);

        $house = $areaInfo['longitude'] . ',' . $areaInfo['latitude'] . ',k';
        $arr = [
            'b' => $utdatenow,
            'p' => '0123456789',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',
            'g' => ',',
            'head',
            'roundsec',

        ];

        $time_behold_planet = $p->calculate($arr, ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']);

        $behold_planet = $time_behold_planet['planet'];

        //金星
        $venus_behold_house = floor($behold_planet[3]['house_number']);
        //木星
        $jupiter_behold_house = floor($behold_planet[5]['house_number']);

        if (empty($data['take_single'])) {
            if ($venus_behold_house == 5 or $jupiter_behold_house == 5) {
                $data['take_single'] = 90;
            }
        }

        if (empty($data['take_single'])) {
            foreach ($behold_planet as $key => $value) {
                $value_behold_house = floor($value['house_number']);
                if ($value_behold_house == 5) {
                    $data['take_single'] = 70;
                    continue;
                }
            }
        }

        if (empty($data['take_single'])) {
            foreach ($behold_planet as $key => $value) {
                $chazhi_behold = abs($value['longitude'] - $behold_planet[3]['longitude']);

                if ($chazhi_behold > 180) {
                    $chazhi_behold = 360 - $chazhi_behold;
                }

                if ($chazhi_behold < 65 and $chazhi_behold > 55) {
                    $data['take_single'] = 60;
                    continue;
                }

                if ($chazhi_behold < 125 and $chazhi_behold > 115) {
                    $data['take_single'] = 60;
                    continue;
                }

            }
        }

        if (empty($data['take_single'])) {
            foreach ($behold_planet as $key => $value) {
                $chazhi_behold = abs($value['longitude'] - $behold_planet[3]['longitude']);
                if ($chazhi_behold > 180) {
                    $chazhi_behold = 360 - $chazhi_behold;
                }

                if ($chazhi_behold < 5) {
                    $data['take_single'] = 50;
                    continue;
                }
            }
        }

        if (empty($data['take_single'])) {
            $data['take_single'] = 10;
        }

        //闪婚指数Flash marriage
        if (empty($data['flash_marriage'])) {
            if (floor($behold_planet[7]['house_number']) == 7) {
                $data['flash_marriage'] = 70;
            }
        }
        if (empty($data['flash_marriage'])) {
            if (floor($behold_planet[6]['house_number']) == 7) {
                $data['flash_marriage'] = 10;
            }
        }
        if (empty($data['flash_marriage'])) {
            foreach ($behold_planet as $key => $value) {
                $value_behold_house = floor($value['house_number']);
                if ($value_behold_house == 7) {
                    $data['flash_marriage'] = 30;
                    continue;
                }
            }
        }

        if (empty($data['flash_marriage'])) {
            $data['flash_marriage'] = 0;
        }
        $data['status'] = $order_status;
        if ($order_status == 0) {
            return $data;
        }


        //月亮变化

        $birthdayToTime = $birthday - $areaInfo['tz'] * 3600;

        $beginThismonth = mktime(0, 0, 0, date('m', $birthdayToTime), 1, date('Y', $birthdayToTime));

        $utdatenow = date('d.m.Y', $beginThismonth);

        $utnow = '12:0:0';

        $arr = [
            'b' => $utdatenow,
            'ut' => $utnow,
            'p' => '1',
            'd' => '0',
            'n' => '32',
            's' => '1',
            'f' => 'pTlsj',
            'g' => ',',
            'e' => 'swe',
            'head',
            'roundsec',
        ];

        //计算男早婚
        $data['early_marriage'] = 0;
        $data['age_marriage'] = 0;
        $data['marriage_introduce'] = '无';

        $full_moon_time = $p->transitsProgressed($arr, 180, 'd');


        $new_moon_time = $p->transitsProgressed($arr, 0, 'd');

        $waxing = false; //月盈

        if ($birthdayToTime > $new_moon_time && $birthdayToTime <= $full_moon_time) {
            $waxing = true;
        }

        if ($sex == 1) {
            //性别男早婚
            if (in_array(floor($life_planet[1]['house_number']), [4, 5, 6, 10, 11, 12]) and $waxing) {
                $data['early_marriage'] += 1;
                $data['marriage_introduce'] .= '你的运势来的较早，在婚姻上你也容易更早获得圆满。';
            }

            //男早婚第二条
            $early_1_2 = 0;
            foreach ($life_house_planet as $key => $kdsf) {
                if ($key < 5) {
                    $early_1_2 += count($kdsf);
                }
                if ($key > 9) {
                    $early_1_2 += count($kdsf);
                }
            }
            if ($early_1_2 / 11 > 0.5) {
                $data['early_marriage'] += 1;
                $data['marriage_introduce'] .= '你是个有主见的人，当你想结婚的时候，是不太会去考虑其他人或者周边环境的影响，因此如果你想早结婚，你就很有机会实现自己的想法。';
            }
            //男早婚第三条
            $early_1_3 = 0;
            if (in_array($life_planet[1]['sign_id'], [1, 3, 7]) or in_array($life_planet[3]['sign_id'], [1, 3, 7])) {
                foreach ($life_planet[1]['planet_allow_degree'] as $key => $vale) {
                    if (($vale['degree'] == 60 or $vale['degree'] == 120) and floor($life_planet[array_search($vale['planet'], $starsCode)]['house_number']) == 1) {
                        $early_1_3++;
                        break;
                    }
                }
                if ($early_1_3 < 3) {
                    foreach ($life_planet[3]['planet_allow_degree'] as $key => $vale) {
                        if (($vale['degree'] == 60 or $vale['degree'] == 120) and floor($life_planet[array_search($vale['planet'], $starsCode)]['house_number']) == 1) {
                            $early_1_3++;
                            break;
                        }
                    }
                }
                if ($early_1_3 > 2) {
                    $data['early_marriage'] += 1;
                    $data['marriage_introduce'] .= '你通常很受欢迎，脾气很好，也有着讨人喜欢的性格，有很好的异性缘，你会有更多恋爱机会，比其他人更有机会结婚。';
                }
            }
            //男早婚第四条
            if (in_array(floor($life_planet[1]['house_number']), [5, 7]) or in_array(floor($life_planet[3]['house_number']), [5, 7])) {
                $data['early_marriage'] += 1;
                $data['marriage_introduce'] .= '你对于爱情和婚姻关系有着比较早的认知与需，你可能非常需要人陪伴，你会因为希望人陪伴而踏入婚姻。';
            }
            //男早婚第五条
            if (in_array($life_house[4]['sign_id'], [1, 3, 7, 11]) or in_array($life_house[6]['sign_id'], [1, 3, 7, 11])) {
                $data['early_marriage'] += 1;
                $data['marriage_introduce'] .= '你重情重义，对感情有始有终，如果时机合适，你就会考虑踏入婚姻殿堂。';
            }
            //男早婚第六条
            $early_1_6 = 0;
            if ((in_array($life_house[3]['sign_id'], [1, 6]) and in_array($life_house[4]['sign_id'], [0, 7])) or ($life_house[3]['sign_id'] == 11 and $life_house[4]['sign_id'] == 9)) {

                foreach ($life_planet[3]['planet_allow_degree'] as $key => $vale) {
                    if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                        $early_1_6++;
                        break;
                    }
                }
                if ($early_1_6 < 3) {
                    foreach ($life_planet[4]['planet_allow_degree'] as $key => $vale) {
                        if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                            $early_1_6++;
                            break;
                        }
                    }
                }
                if ($early_1_6 > 2) {
                    $data['early_marriage'] += 1;
                    $data['marriage_introduce'] .= '人际关系很好，喜欢在朋友中扎堆，对事情也积极，所以更容易早婚。';
                }
            }

            //男早婚第七条
            $early_1_7 = 0;
            if (floor($life_planet[3]['house_number']) == 7 or floor($life_planet[5]['house_number']) == 7) {

                foreach ($life_planet[3]['planet_allow_degree'] as $key => $vale) {
                    if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                        $early_1_7++;
                    }
                }
                if ($early_1_7 < 3) {
                    foreach ($life_planet[5]['planet_allow_degree'] as $key => $vale) {
                        if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                            $early_1_7++;
                        }
                    }
                }
                if ($early_1_7 > 2) {
                    $data['early_marriage'] += 1;
                    if ($early_1_3 < 3) {
                        $data['marriage_introduce'] .= '你通常很受欢迎，脾气很好，也有着讨人喜欢的性格，有很好的异性缘，你会有更多恋爱机会，比其他人更有机会结婚。';
                    }
                }

            }


            //晚婚男 第一，如果他是在月亏 ( 从满月到新月 ) 的时段内出生，而且月亮又正好是落在第一宫、第二宫、第三宫、第七宫、第八宫或第九宫内的话。
            if (in_array(floor($life_planet[1]['house_number']), [1, 2, 3, 7, 8, 9]) and !$waxing) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你感情的运势来的较晚，早年恋爱可能机会较少。';
            }
            //晚婚男 第二，如果月亮或金星被火星、土星、天王星、海王星或冥王星所刑克到，特别是如果这些行星又正好是落在第五宫或第七宫内的话。
            $evening_1_2 = 0;
            foreach ($life_planet[1]['planet_allow_degree'] as $key => $vale) {

                $house_number_1_2 = floor($life_planet[array_search($vale['planet'], $starsCode)]['house_number']);

                if (($vale['degree'] == 90 or $vale['degree'] == 180) and in_array($vale['planet'], [4, 5, 7, 8, 9]) and ($house_number_1_2 == 5 or $house_number_1_2 == 7)) {
                    $evening_1_2++;
                    $data['age_marriage'] += 1;
                    $data['marriage_introduce'] .= '你的感情发展可能会有一点不顺利，容易遇到波折而不稳定。';
                    break;
                }
            }
            //晚婚男 第三，如果火星、土星、天王星、海王星或冥王星是落入第五宫或第七宫内，并且具有凶相位的话。
            foreach ($life_planet as $keyp => $valuep) {
                if (in_array(floor($valuep['house_number']), [5, 7])) {
                    foreach ($valuep['planet_allow_degree'] as $keys => $vales) {
                        if ($vales['degree'] == 90 or $vales['degree'] == 180) {
                            $data['age_marriage'] += 1;
                            if ($evening_1_2 < 1) {
                                $data['marriage_introduce'] .= '你的感情发展可能会有一点不顺利，容易遇到波折而不稳定。';
                            }
                            break 2;
                        }
                    }
                }
            }
            //晚婚男 第四，如果月亮或金星，与木星形成主凶相的话，
            foreach ($life_planet[5]['planet_allow_degree'] as $keys => $vales) {
                if (($vales['degree'] == 90 or $vales['degree'] == 180) and ($vale['planet'] == "1" or $vale['planet'] == "3")) {
                    $data['age_marriage'] += 1;
                    $data['marriage_introduce'] .= '你的恋爱机会多，但是可能遇到很多无法把握的情况，导致无法很快有结果。';
                    break;
                }
            }
            //晚婚男 第五，如果第五宫或第七宫宫主星是落在白羊座、双子座、狮子座、处女座或摩羯座内，而且又具有凶相位的话。
            if (in_array($life_house[4]['sign_id'], [0, 2, 4, 5, 9])) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你的对于感情比较保守，恋爱过程可能会遇到一些阻碍和困难，所以恋爱也是采取稳步推进的方式，自然婚姻会比较晚。';
            }

            //晚婚男 第六，如果第五宫或第七宫的宫头位置，是落在白羊座、狮子座、处女座或摩羯座的话。
            if (in_array($life_house[4]['sign_id'], [0, 4, 5, 9])) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你恋爱机会比较少，或者你对感情比较挑剔，这样会影响你的结婚时间。';
            }

            //晚婚男 第七，如果土星是落入天蝎座，或者是月亮落入天蝎座，而与土星形成平行相位、合相或主凶相的话
            if ($life_planet[1]['sign_id'] == 7 or $life_planet[6]['sign_id'] == 7) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你对感情比较克制，喜欢平静，不算冲动，所以这个有时候会造成你结婚时间的推后，因为你并不是冲动的人。';
            }

            //晚婚男 第八，如果月亮或金星是落入摩羯座，而且被刑克到，特别是如果被土星所刑克到的话
            if ($life_planet[1]['sign_id'] == 9 or $life_planet[3]['sign_id'] == 9) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '无';
            }

            //晚婚男 第九，如果月亮与太阳形成主凶相，而且月亮又正好是落在第一宫、第二宫、第三宫、第七宫、第八宫或第九宫内的话，则时常会因为缺乏决断力，而导致于婚姻有所延迟。
            if (in_array(floor($life_planet[1]['house_number']), [1, 2, 3, 7, 8, 9])) {
                foreach ($life_planet[1]['planet_allow_degree'] as $keys => $vales) {
                    if (($vales['degree'] == 90 or $vales['degree'] == 180) and $vale['planet'] == "0") {
                        $data['age_marriage'] += 1;
                        $data['marriage_introduce'] .= '有时候你在结婚这个问题上，会有一定的犹豫不决，不能当机立断，所以可能造成错失机会。';
                        break;
                    }
                }

            }

            //晚婚男 第十，如果月亮正好是落在星座的最后度数上 (28 度～ 30 度 ) ，并且又缺乏相位关系的话，则时常会因为缺乏异性吸引力，而导致于婚姻有所延迟。
            if ($life_planet[1]['deg']>28 and $life_planet[1]['deg']<30 and count($life_planet[1]['planet_allow_degree'])<1) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你可能因为恋爱的机会不太多，而导致结婚时间偏晚。';
            }

        }

        if ($sex == 2) {
            //性别女早婚
            if (in_array(floor($life_planet[0]['house_number']), [4, 5, 6, 10, 11, 12]) and $waxing) {
                $data['early_marriage'] += 1;
                $data['marriage_introduce'] .= '你的运势来的较早，在婚姻上你也容易更早获得圆满。';
            }

            //性别女早婚第二条
            $d_b_q = 0;
            foreach ($life_house_planet as $key => $kdsf) {
                if ($key < 5) {
                    $d_b_q += count($kdsf);
                }
                if ($key > 9) {
                    $d_b_q += count($kdsf);
                }
            }
            if ($d_b_q / 11 > 0.5) {
                $data['early_marriage'] += 1;
                $data['marriage_introduce'] .= '你是个有主见的人，当你想结婚的时候，是不太会去考虑其他人或者周边环境的影响，因此如果你想早结婚，你就很有机会实现自己的想法。';
            }
            //性别女早婚第三条
            $moon_3 = 0;
            if (in_array($life_planet[0]['sign_id'], [1, 3, 7]) or in_array($life_planet[3]['sign_id'], [1, 3, 7])) {
                foreach ($life_planet[1]['planet_allow_degree'] as $key => $vale) {
                    if (($vale['degree'] == 60 or $vale['degree'] == 120)) {
                        $moon_3++;
                    }
                }
                if ($moon_3 < 3) {
                    foreach ($life_planet[3]['planet_allow_degree'] as $key => $vale) {
                        if (($vale['degree'] == 60 or $vale['degree'] == 120)) {
                            $moon_3++;
                        }
                    }
                }
                if ($moon_3 > 2) {
                    $data['early_marriage'] += 1;
                    $data['marriage_introduce'] .= '你对于爱情和婚姻关系有着比较早的认知与需，你可能非常需要人陪伴，你会因为希望人陪伴而踏入婚姻。';
                }
            }
            //性别女早婚第四条
            $moon_4 = 0;
            if (in_array(floor($life_planet[0]['house_number']), [5, 7]) or in_array(floor($life_planet[3]['house_number']), [5, 7]) or in_array(floor($life_planet[4]['house_number']), [5, 7])) {
                foreach ($life_planet[0]['planet_allow_degree'] as $key => $vale) {
                    if (($vale['degree'] == 60 or $vale['degree'] == 120)) {
                        $moon_4++;
                    }
                }
                if ($moon_4 < 3) {
                    foreach ($life_planet[3]['planet_allow_degree'] as $key => $vale) {
                        if (($vale['degree'] == 60 or $vale['degree'] == 120)) {
                            $moon_4++;
                        }
                    }
                }

                if ($moon_4 < 3) {
                    foreach ($life_planet[4]['planet_allow_degree'] as $key => $vale) {
                        if (($vale['degree'] == 60 or $vale['degree'] == 120)) {
                            $moon_4++;
                        }
                    }
                }
                if ($moon_4 > 2) {
                    $data['early_marriage'] += 1;
                    if ($moon_3 > 2) {
                        $data['marriage_introduce'] .= '你对于爱情和婚姻关系有着比较早的认知与需，你可能非常需要人陪伴，你会因为希望人陪伴而踏入婚姻。';
                    }
                }
            }
            //性别女早婚第五条
            if (in_array($life_house[4]['sign_id'], [1, 3, 7, 11]) or in_array($life_house[6]['sign_id'], [1, 3, 7, 11])) {
                $data['early_marriage'] += 1;
                if ($moon_4 > 2) {
                    $data['marriage_introduce'] .= '你重情重义，对感情有始有终，如果时机合适，你就会考虑踏入婚姻殿堂。';
                }
            }
            //性别女早婚第六条
            $Venus_six = 0;
            if (($life_house[0]['sign_id'] == 4 and in_array($life_house[3]['sign_id'], [1, 6]) and in_array($life_house[4]['sign_id'], [0, 7])) or ($life_house[0]['sign_id'] == 0 and $life_house[3]['sign_id'] == 11 and $life_house[4]['sign_id'] == 9)) {

                foreach ($life_planet[0]['planet_allow_degree'] as $key => $vale) {
                    if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                        $Venus_six++;
                    }
                }
                if ($Venus_six < 3) {
                    foreach ($life_planet[3]['planet_allow_degree'] as $key => $vale) {
                        if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                            $Venus_six++;
                        }
                    }
                }
                if ($Venus_six < 3) {
                    foreach ($life_planet[4]['planet_allow_degree'] as $key => $vale) {
                        if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                            $Venus_six++;
                        }
                    }
                }
                if ($Venus_six > 2) {
                    $data['early_marriage'] += 1;
                    $data['marriage_introduce'] .= '人际关系很好，喜欢在朋友中扎堆，对事情也积极，所以更容易早婚。';
                }
            }
            //性别女早婚第七条
            $Venus_7 = 0;
            if (floor($life_planet[3]['house_number']) == 7 or floor($life_planet[5]['house_number']) == 7) {

                foreach ($life_planet[3]['planet_allow_degree'] as $key => $vale) {
                    if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                        $Venus_7++;
                    }
                }
                if ($Venus_7 < 3) {
                    foreach ($life_planet[5]['planet_allow_degree'] as $key => $vale) {
                        if ($vale['degree'] == 60 or $vale['degree'] == 120) {
                            $Venus_7++;
                        }
                    }
                }
                if ($Venus_7 > 2) {
                    $data['early_marriage'] += 1;
                    if ($Venus_six < 3) {
                        $data['marriage_introduce'] .= '人际关系很好，喜欢在朋友中扎堆，对事情也积极，所以更容易早婚。';
                    }
                }
            }

            //女晚婚 第一，如果她是在月亏 ( 从满月到新月 ) 的时段内出生，而且太阳又正好是落在第一宫、第二宫、第三宫、第七宫、第八宫或第九宫内的话。
            if (in_array(floor($life_planet[0]['house_number']), [1, 2, 3, 7, 8, 9]) and !$waxing) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你感情的运势来的较晚，早年恋爱可能机会较少。';
            }

            //女晚婚 第二，如果月亮或金星被火星、土星、天王星、海王星或冥王星所刑克到，特别是如果这些行星又正好是落在第五宫或第七宫内的话。
            $evening_2_2 = 0;
            foreach ($life_planet[0]['planet_allow_degree'] as $key => $vale) {

                $house_number_1_2 = floor($life_planet[array_search($vale['planet'], $starsCode)]['house_number']);

                if (($vale['degree'] == 90 or $vale['degree'] == 180) and in_array($vale['planet'], [4, 5, 7, 8, 9]) and ($house_number_1_2 == 5 or $house_number_1_2 == 7)) {
                    $evening_2_2++;
                    $data['age_marriage'] += 1;
                    $data['marriage_introduce'] .= '你的感情发展可能会有一点不顺利，容易遇到波折而不稳定。';
                    break;
                }
            }
            //女晚婚 第三，如果火星、土星、天王星、海王星或冥王星是落入第五宫或第七宫内，并且具有凶相位的话。
            foreach ($life_planet as $keyp => $valuep) {
                if (in_array(floor($valuep['house_number']), [5, 7])) {
                    foreach ($valuep['planet_allow_degree'] as $keys => $vales) {
                        if ($vales['degree'] == 90 or $vales['degree'] == 180) {
                            $data['age_marriage'] += 1;
                            if ($evening_2_2 < 1) {
                                $data['marriage_introduce'] .= '你的感情发展可能会有一点不顺利，容易遇到波折而不稳定。';
                            }
                            break 2;
                        }
                    }
                }
            }

            //女晚婚 第四，如果月亮或金星，与木星形成主凶相的话，
            foreach ($life_planet[5]['planet_allow_degree'] as $keys => $vales) {
                if (($vales['degree'] == 90 or $vales['degree'] == 180) and ($vale['planet'] == "1" or $vale['planet'] == "3")) {
                    $data['age_marriage'] += 1;
                    $data['marriage_introduce'] .= '你的恋爱机会多，但是可能遇到很多无法把握的情况，导致无法很快有结果。';
                    break;
                }
            }

            //女晚婚 第五，如果月亮与金星或天王星，形成平行相位、合相或主凶相的话，则时常也会具有延迟婚姻的现象。
            foreach ($life_planet[1]['planet_allow_degree'] as $keys => $vales) {
                if (($vales['degree'] == 90 or $vales['degree'] == 180 or $vales['degree'] == 0) and ($vale['planet'] == "1" or $vale['planet'] == "3")) {
                    $data['age_marriage'] += 1;
                    $data['marriage_introduce'] .= '你的恋爱机会多，但是可能遇到很多无法把握的情况，导致无法很快有结果。';
                    break;
                }
            }

            //女晚婚 第六，如果第五宫或第七宫宫主星是落在白羊座、双子座、狮子座、处女座或摩羯座内，而且又具有凶相位的话。
            if (in_array($life_house[4]['sign_id'], [0, 2, 4, 5, 9])) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你的对于感情比较保守，恋爱过程可能会遇到一些阻碍和困难，所以恋爱也是采取稳步推进的方式，自然婚姻会比较晚。';
            }
            //女晚婚 第七，如果第五宫或第七宫的宫头位置，是落在白羊座、狮子座、处女座或摩羯座的话。
            if (in_array($life_house[4]['sign_id'], [0, 4, 5, 9])) {
                $data['age_marriage'] += 1;
                $data['marriage_introduce'] .= '你恋爱机会比较少，或者你对感情比较挑剔，这样会影响你的结婚时间。';
            }
            //女晚婚 第八，如果土星是落入天蝎座，或者是太阳落入天蝎座，而与土星形成平行相位、合相或主凶相的话，则时常会因为缺乏性激情 ( 或者说是异性相吸力 ) ，而造成婚姻延迟，甚至于是不结婚的现象。
            if ($life_house[6]['sign_id']==7 or $life_house[0]['sign_id']==7) {
                foreach ($life_planet[6]['planet_allow_degree'] as $keys => $vales) {
                    if (($vales['degree'] == 90 or $vales['degree'] == 180 or $vales['degree'] == 0) and ($vale['planet'] == "0")) {
                        $data['age_marriage'] += 1;
                        $data['marriage_introduce'] .= '你对感情比较克制，喜欢平静，不算冲动，所以这个有时候会造成你结婚时间的推后，因为你并不是冲动的人。';
                        break;
                    }
                }
            }
            //女晚婚 第九，如果太阳或金星是落入摩羯座，而且被刑克到，特别是如果被土星所刑克到的话，则时常也会具有延迟婚姻的现象。
            if ($life_house[0]['sign_id']==9 or $life_house[3]['sign_id']==9) {
                foreach ( $life_planet[3]['planet_allow_degree'] as $keys => $vales) {
                    if ($vales['degree'] == 90 or $vales['degree'] == 180) {
                        $data['age_marriage'] += 1;
                        if ($evening_2_2 < 1) {
                            $data['marriage_introduce'] .= '你对感情比较克制，喜欢平静，不算冲动，所以这个有时候会造成你结婚时间的推后，因为你并不是冲动的人。';
                        }
                        break;
                    }
                }
                foreach ( $life_planet[0]['planet_allow_degree'] as $keys => $vales) {
                    if ($vales['degree'] == 90 or $vales['degree'] == 180) {
                        $data['age_marriage'] += 1;
                        if ($evening_2_2 < 1) {
                            $data['marriage_introduce'] .= '你对感情比较克制，喜欢平静，不算冲动，所以这个有时候会造成你结婚时间的推后，因为你并不是冲动的人。';
                        }
                        break;
                    }
                }
            }

            //女晚婚 第十，如果太阳或金星是落入摩羯座，而且被刑克到，特别是如果被土星所刑克到的话，则时常也会具有延迟婚姻的现象。
            if (in_array(floor($life_planet[0]['house_number']), [1, 2, 3, 7, 8, 9])) {
                foreach ($life_planet[0]['planet_allow_degree'] as $keys => $vales) {
                    if (($vales['degree'] == 90 or $vales['degree'] == 180) and ($vale['planet'] == "1")) {
                        $data['age_marriage'] += 1;
                        $data['marriage_introduce'] .= '有时候你在结婚这个问题上，会有一定的犹豫不决，不能当机立断，所以可能造成错失机会。';
                        break;
                    }
                }
            }
        }

        $early_marriage= $data['early_marriage']/7;

        $age_marriage=$data['age_marriage']/10;

        $cha_early=(1-$early_marriage-$age_marriage)/2;

        $data['early_marriage']=round(($cha_early+$early_marriage),2)*100 ;
        $data['age_marriage']=round(($cha_early+$age_marriage),2)*100 ;

        $matters_attention_array = explode("\r\n", file_get_contents('./corpus_txt/astrolabe_decode/matters_attention.txt'));  //注意事项

        $temperament_array = explode("\r\n", file_get_contents('./corpus_txt/astrolabe_decode/temperament.txt'));     //另一半性格文档

        $matters_career_array = explode("\r\n", file_get_contents('./corpus_txt/astrolabe_decode/matters_career.txt'));   //可能事业文档



        $data['matters_attention'] = array();

        //循环本命行星
        foreach ($life_planet as $key => $vale) {

            $value_behold_house = floor($vale['house_number']);

            //第七宫冲撞的行星---需要注意事项
            if (in_array($vale['code_name'], [9, 8, 7, 6, 4]) and $value_behold_house == 7 and empty($matters_attention)) {

                $data['matters_attention'][] = $this->get_txt_data($matters_attention_array, $vale['chinese_name'] . '7宫');
                continue;
            }
        }

        //另一半的计算
        $data['temperament'] = $this->get_txt_data($temperament_array, '婚神' . $signsChinese[$life_planet[10]['sign_id']]);

        //可能事业
        $data['matters_career'][] = $this->get_txt_data($matters_career_array, $signsChinese[$life_house[3]['sign_id']]);

        //可能星座
        $data['may_sign'][] = $signsFont[$life_planet[1]['sign_id']] . ' ' . $signsChinese[$life_planet[1]['sign_id']];
        $data['may_sign'][] = $signsFont[$life_planet[3]['sign_id']] . ' ' . $signsChinese[$life_planet[3]['sign_id']];
        $data['may_sign'][] = $signsFont[$life_planet[10]['sign_id']] . ' ' . $signsChinese[$life_planet[10]['sign_id']];


        if ($life_house[0]['longitude'] > $life_house[6]['longitude']) {
            if ($life_planet[0]['longitude'] <= $life_house[0]['longitude'] And $life_planet[0]['longitude'] > $life_house[6]['longitude']) {
                $day_chart = true;
            } else {
                $day_chart = false;
            }
        } else {
            if ($life_planet[0]['longitude'] > $life_house[0]['longitude'] And $life_planet[0]['longitude'] <= $life_house[6]['longitude']) {
                $day_chart = false;
            } else {
                $day_chart = true;
            }
        }

        $true_love_night = array(['planet' => 'Moon', 'age' => 0, 'day' => 0], ['planet' => 'Saturn', 'age' => 1, 'day' => 104], ['planet' => 'Jupiter', 'age' => 2, 'day' => 209], ['planet' => 'Mars', 'age' => 3, 'day' => 313], ['planet' => 'Sun', 'age' => 5, 'day' => 52], ['planet' => 'Venus', 'age' => 6, 'day' => 156], ['planet' => 'Mercury', 'age' => 7, 'day' => 261], ['planet' => 'Saturn', 'age' => 9, 'day' => 0], ['planet' => 'Jupiter', 'age' => 10, 'day' => 209], ['planet' => 'Mars', 'age' => 12, 'day' => 52], ['planet' => 'Sun', 'age' => 13, 'day' => 261], ['planet' => 'Venus', 'age' => 15, 'day' => 104], ['planet' => 'Mercury', 'age' => 16, 'day' => 313], ['planet' => 'Moon', 'age' => 18, 'day' => 156], ['planet' => 'Jupiter', 'age' => 20, 'day' => 0], ['planet' => 'Mars', 'age' => 21, 'day' => 261], ['planet' => 'Sun', 'age' => 23, 'day' => 156], ['planet' => 'Venus', 'age' => 25, 'day' => 52], ['planet' => 'Mercury', 'age' => 26, 'day' => 313], ['planet' => 'Moon', 'age' => 28, 'day' => 209], ['planet' => 'Saturn', 'age' => 30, 'day' => 104], ['planet' => 'Mars', 'age' => 32, 'day' => 0], ['planet' => 'Sun', 'age' => 33, 'day' => 0], ['planet' => 'Venus', 'age' => 34, 'day' => 0], ['planet' => 'Mercury', 'age' => 35, 'day' => 0], ['planet' => 'Moon', 'age' => 36, 'day' => 0], ['planet' => 'Saturn', 'age' => 37, 'day' => 0], ['planet' => 'Jupiter', 'age' => 38, 'day' => 0], ['planet' => 'Sun', 'age' => 39, 'day' => 0], ['planet' => 'Venus', 'age' => 40, 'day' => 156], ['planet' => 'Mercury', 'age' => 41, 'day' => 313], ['planet' => 'Moon', 'age' => 43, 'day' => 104], ['planet' => 'Saturn', 'age' => 44, 'day' => 261], ['planet' => 'Jupiter', 'age' => 46, 'day' => 52], ['planet' => 'Mars', 'age' => 47, 'day' => 209], ['planet' => 'Venus', 'age' => 49, 'day' => 0], ['planet' => 'Mercury', 'age' => 50, 'day' => 52], ['planet' => 'Moon', 'age' => 51, 'day' => 104], ['planet' => 'Saturn', 'age' => 52, 'day' => 156], ['planet' => 'Jupiter', 'age' => 53, 'day' => 209], ['planet' => 'Mars', 'age' => 54, 'day' => 261], ['planet' => 'Sun', 'age' => 55, 'day' => 313], ['planet' => 'Mercury', 'age' => 57, 'day' => 0], ['planet' => 'Moon', 'age' => 58, 'day' => 313], ['planet' => 'Saturn', 'age' => 60, 'day' => 261], ['planet' => 'Jupiter', 'age' => 62, 'day' => 209], ['planet' => 'Mars', 'age' => 64, 'day' => 156], ['planet' => 'Sun', 'age' => 66, 'day' => 104], ['planet' => 'Venus', 'age' => 68, 'day' => 52], ['planet' => 'meanNode', 'age' => 70, 'day' => 0], ['planet' => 'MeanSouthNode', 'age' => 73, 'day' => 0]);

        $true_love_day = array(['planet' => 'Sun', 'age' => 0, 'day' => 0], ['planet' => 'Venus', 'age' => 1, 'day' => 156], ['planet' => 'Mercury', 'age' => 2, 'day' => 313], ['planet' => 'Moon', 'age' => 4, 'day' => 104], ['planet' => 'Saturn', 'age' => 5, 'day' => 261], ['planet' => 'Jupiter', 'age' => 7, 'day' => 52], ['planet' => 'Mars', 'age' => 8, 'day' => 209], ['planet' => 'Venus', 'age' => 10, 'day' => 0], ['planet' => 'Mercury', 'age' => 11, 'day' => 52], ['planet' => 'Moon', 'age' => 12, 'day' => 104], ['planet' => 'Saturn', 'age' => 13, 'day' => 156], ['planet' => 'Jupiter', 'age' => 14, 'day' => 209], ['planet' => 'Mars', 'age' => 15, 'day' => 261], ['planet' => 'Sun', 'age' => 16, 'day' => 313], ['planet' => 'Mercury', 'age' => 18, 'day' => 0], ['planet' => 'Moon', 'age' => 19, 'day' => 313], ['planet' => 'Saturn', 'age' => 21, 'day' => 261], ['planet' => 'Jupiter', 'age' => 23, 'day' => 209], ['planet' => 'Mars', 'age' => 25, 'day' => 156], ['planet' => 'Sun', 'age' => 27, 'day' => 104], ['planet' => 'Venus', 'age' => 29, 'day' => 52], ['planet' => 'Moon', 'age' => 31, 'day' => 0], ['planet' => 'Saturn', 'age' => 32, 'day' => 104], ['planet' => 'Jupiter', 'age' => 33, 'day' => 209], ['planet' => 'Mars', 'age' => 34, 'day' => 313], ['planet' => 'Sun', 'age' => 36, 'day' => 52], ['planet' => 'Venus', 'age' => 37, 'day' => 156], ['planet' => 'Mercury', 'age' => 38, 'day' => 261], ['planet' => 'Saturn', 'age' => 40, 'day' => 0], ['planet' => 'Jupiter', 'age' => 41, 'day' => 209], ['planet' => 'Mars', 'age' => 43, 'day' => 52], ['planet' => 'Sun', 'age' => 44, 'day' => 261], ['planet' => 'Venus', 'age' => 46, 'day' => 104], ['planet' => 'Mercury', 'age' => 47, 'day' => 313], ['planet' => 'Moon', 'age' => 49, 'day' => 156], ['planet' => 'Jupiter', 'age' => 51, 'day' => 0], ['planet' => 'Mars', 'age' => 52, 'day' => 261], ['planet' => 'Sun', 'age' => 54, 'day' => 156], ['planet' => 'Venus', 'age' => 56, 'day' => 52], ['planet' => 'Mercury', 'age' => 57, 'day' => 313], ['planet' => 'Moon', 'age' => 59, 'day' => 209], ['planet' => 'Saturn', 'age' => 61, 'day' => 104], ['planet' => 'Mars', 'age' => 63, 'day' => 0], ['planet' => 'Sun', 'age' => 64, 'day' => 0], ['planet' => 'Venus', 'age' => 65, 'day' => 0], ['planet' => 'Mercury', 'age' => 66, 'day' => 0], ['planet' => 'Moon', 'age' => 67, 'day' => 0], ['planet' => 'Saturn', 'age' => 68, 'day' => 0], ['planet' => 'Jupiter', 'age' => 69, 'day' => 0], ['planet' => 'meanNode', 'age' => 70, 'day' => 0], ['planet' => 'MeanSouthNode', 'age' => 73, 'day' => 0]);
        $signs_guardian_name = $signs_guardian_index[$life_house[4]['sign_id']];

        if (is_array($signs_guardian_name)) {
            $signs_guardian_str = $signs_guardian_name[0];
        } else {
            $signs_guardian_str = $signs_guardian_name;
        }


        if ($age > 16 and $age <= 73) {
            if ($day_chart == true) {
                foreach ($true_love_day as $key => $value) {
                    if ($value['age'] > 16 and $signs_guardian_str == $value['planet']) {
                        $data['true_love_time'][] = date('Y年m月d日', $birthday + (365.25 * $value['age'] + $value['day']) * 86400) . '~' . date('Y年m月d日', $birthday + (365.25 * $true_love_day[$key + 1]['age'] + $true_love_day[$key + 1]['day']) * 86400);
                    }
                }
            } else {
                foreach ($true_love_night as $key => $value) {
                    if ($value['age'] > 16 and $signs_guardian_str == $value['planet']) {
                        $data['true_love_time'][] = date('Y年m月d日', $birthday + (365.25 * $value['age'] + $value['day']) * 86400) . '~' . date('Y年m月d日', $birthday + (365.25 * $true_love_day[$key + 1]['age'] + $true_love_day[$key + 1]['day']) * 86400);
                    }
                }
            }
        } else {
            $data['true_love_time'][] = '年龄不符';
        }


        foreach ($life_planet[10]['planet_allow_degree'] as $key => $vale) {
            //婚神星的负面相位---需要注意事项
            if ($vale['degree'] == 90) {
                if (count($data['matters_attention']) < 2) {
                    $data['matters_attention'][] = $this->get_txt_data($matters_attention_array, $planetChinese[$vale['planet']] . '负相位');
                    continue;
                }
            }

            if ($vale['degree'] == 180) {
                if (count($data['matters_attention']) < 2) {
                    $data['matters_attention'][] = $this->get_txt_data($matters_attention_array, $planetChinese[$vale['planet']] . '负相位');
                    continue;
                }
            }

        }

        $data['matters_attention'][] = $this->get_txt_data($matters_attention_array, '婚神' . $signsChinese[$life_planet[10]['sign_id']]);

        return $data;

    }

    //兑换码
    public function conversionCode()
    {

    }

    function get_txt_data($matters_attention_array, $title)
    {
        foreach ($matters_attention_array as $keys => $vales) {
            if (trim($vales) == $title) {
                return $matters_attention_array[$keys + 1];
            }

        }
    }

}
