<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\appraise\controller;

use ob\Lunar;

/**
 * 年运测评控制器
 */
class Activitytransport25 extends UserBase
{

    /**
     * 年运详细
     */
    public function activityTransport25Info()
    {
        !empty($this->param['id']) && $whereTransport['id'] = $this->param['id'];

        !empty($this->param['order_number']) && $whereTransport['order_number'] = $this->param['order_number'];
        !empty($this->param['openid']) && $whereTransport['openid'] = $this->param['openid'];

        $activityTransport25Info = $this->logicActivityTransport25->getActivityTransport25Info($whereTransport);

        if (empty($activityTransport25Info)) {
            return $this->apiReturn(['code' => 10549856, 'msg' => '没查到您的计算数据哦']);
        }

//        if(empty($activityTransport25Info['planet_json'])){
//            $regit['birthday'] = date('Y-m-d H:i:s', $activityTransport25Info['birthday']);
//            $this->logicActivityTransport25->astrolabeDecodeEdit($regit);
//            $regit = $this->logicActivityTransport25->getActivityTransport25Info($where);
//        }

        $data['mobile'] = $activityTransport25Info['mobile'];
        $data['channel'] = $activityTransport25Info['channel'];
        $data['status'] = $activityTransport25Info['status'];
        $data['order_number'] = $activityTransport25Info['order_number'];

        if (!empty($activityTransport25Info['birth_district'])) {
            $birth_district = $this->logicArea->getAreaInfo(['id' => $activityTransport25Info['birth_district']], 'id,name,fullname');
            $birth_district_name = explode(" ", $birth_district['fullname']);
            $data['user']['birth_district_array'] = $this->logicArea->getAreaColumn(['name' => ['in', $birth_district_name]], 'id,name,fullname,longitude,latitude,tz', 'level');
        }

        if (!empty($activityTransport25Info['living_district'])) {
            $birth_district = $this->logicArea->getAreaInfo(['id' => $activityTransport25Info['living_district']], 'id,name,fullname');
            $birth_district_name = explode(" ", $birth_district['fullname']);
            $data['user']['living_district_array'] = $this->logicArea->getAreaColumn(['name' => ['in', $birth_district_name]], 'id,name,fullname,longitude,latitude,tz', 'level');
        }

        $data['user']['name'] = $activityTransport25Info['name'];
        $data['user']['sex'] = $activityTransport25Info['sex'];
        if(!empty($activityTransport25Info['birthday'])){
            $data['user']['birthday'] = date('Y-m-d H:i:i', $activityTransport25Info['birthday']);

        }

        $planet_json = json_decode($activityTransport25Info['planet_json'], true);
        $life_planet = $planet_json['planet'];
        $life_house = $planet_json['house'];

        $houseChinese = array("第一宫", "第二宫", "第三宫", "第四宫", "第五宫", "第六宫", "第七宫", "第八宫", "第九宫", "第十宫", "第十一宫", "第十二宫");
        $signsChinese = array("白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "摩羯座", "水瓶座", "双鱼座");
        $planetChinese = array('Sun' => '太阳', 'Moon' => '月亮', 'Mercury' => '水星', 'Venus' => '金星', 'Mars' => '火星', 'Jupiter' => '木星', 'Saturn' => '土星', 'Uranus' => '天王星', 'Neptune' => '海王星', 'Pluto' => '冥王星', 'meanNode' => '北交', 'MeanSouthNode' => '南交');
        $dataKey = array('love_good' => 'love', 'career_good' => 'career', 'fortune_good' => 'fortune', 'health_good' => 'health',
            'love_bad' => 'love', 'career_bad' => 'career', 'fortune_bad' => 'fortune', 'health_bad' => 'health',
            'love_the' => 'love', 'career_the' => 'career', 'fortune_the' => 'fortune', 'health_the' => 'health',
            'other' => 'other', 'beginning_good' => 'beginning', 'beginning_bad' => 'beginning', 'beginning_the' => 'beginning'
        );

        $good_array = array('颇佳，', '总体不错，', '非常不错，基本上都很顺利。', '总体是不错的，', '顺利，不容易出现大的问题。');
        $bad_array = array('可能有较多的状况出现，，', '不算特别地理想，，', '有点低迷，有很多需要注意的地方，', '不会有大问题，但是有各种各样的小问题来纠缠让你头疼不已。', '并不会特别地乐观，');
        $the_array = array('差强人意，', '起伏不定，', '还算顺利，', '基本顺利，', '整体顺利，', '基本顺利。');

        $signs_guardian_english = ["Mars", "Venus", "Mercury", "Moon", "Sun", "Mercury", "Venus", "Pluto", "Jupiter", "Saturn", "Saturn", "Jupiter"];

        $signs_guardian_index = [4, 3, 2, 1, 0, 2, 3, 9, 5, 6, 6, 5];

        foreach ($life_planet as $key => $value) {
            $data['planet_sign'][] = ['planet' => $value['chinese_name'], 'sign' => $signsChinese[$value['sign_id']]];
        }

        if ($activityTransport25Info['status'] == 1) {

            $life_lin_score = json_decode($activityTransport25Info['life_lin_score'], true);

            $birthday = $activityTransport25Info['birthday'];

            $new_years = 2025;

            $years = date('Y', $birthday);

            $month = date('m', $birthday);

            $day = date('d', $birthday);

            $hours = date('H', $birthday);

            $mins = date('i', $birthday);

            $secs = date('s', $birthday);

            $new_years_time = mktime(0, 0, 0, 1, 1, date('Y'));

            $next_years_time = mktime(0, 0, 0, 1, 1, date('Y') + 1);

            $new_birthday_time = mktime($hours, $mins, $secs, $month, $day, $new_years);


            if ($life_house[0]['longitude'] > $life_house[6]['longitude']) {
                if ($life_planet[0]['longitude'] <= $life_house[0]['longitude'] And $life_planet[0]['longitude'] > $life_house[6]['longitude']) {
                    $day_chart = true;
                } else {
                    $day_chart = false;
                }
            } else {
                if ($life_planet[0]['longitude'] > $life_house[0]['longitude'] And $life_planet[0]['longitude'] <= $life_house[6]['longitude']) {
                    $day_chart = false;
                } else {
                    $day_chart = true;
                }
            }

            $true_love_night = array(['big' => 'Moon', 'planet' => 'Moon', 'age' => 0, 'day' => 0], ['big' => 'Moon', 'planet' => 'Saturn', 'age' => 1, 'day' => 104],
                ['big' => 'Moon', 'planet' => 'Jupiter', 'age' => 2, 'day' => 209], ['big' => 'Moon', 'planet' => 'Mars', 'age' => 3, 'day' => 313],
                ['big' => 'Moon', 'planet' => 'Sun', 'age' => 5, 'day' => 52], ['big' => 'Moon', 'planet' => 'Venus', 'age' => 6, 'day' => 156],
                ['big' => 'Moon', 'planet' => 'Mercury', 'age' => 7, 'day' => 261], ['big' => 'Saturn', 'planet' => 'Saturn', 'age' => 9, 'day' => 0],
                ['big' => 'Saturn', 'planet' => 'Jupiter', 'age' => 10, 'day' => 209], ['big' => 'Saturn', 'planet' => 'Mars', 'age' => 12, 'day' => 52],
                ['big' => 'Saturn', 'planet' => 'Sun', 'age' => 13, 'day' => 261], ['big' => 'Saturn', 'planet' => 'Venus', 'age' => 15, 'day' => 104],
                ['big' => 'Saturn', 'planet' => 'Mercury', 'age' => 16, 'day' => 313], ['big' => 'Saturn', 'planet' => 'Moon', 'age' => 18, 'day' => 156],
                ['big' => 'Jupiter', 'planet' => 'Jupiter', 'age' => 20, 'day' => 0], ['big' => 'Jupiter', 'planet' => 'Mars', 'age' => 21, 'day' => 261],
                ['big' => 'Jupiter', 'planet' => 'Sun', 'age' => 23, 'day' => 156], ['big' => 'Jupiter', 'planet' => 'Venus', 'age' => 25, 'day' => 52],
                ['big' => 'Jupiter', 'planet' => 'Mercury', 'age' => 26, 'day' => 313], ['big' => 'Jupiter', 'planet' => 'Moon', 'age' => 28, 'day' => 209],
                ['big' => 'Jupiter', 'planet' => 'Saturn', 'age' => 30, 'day' => 104], ['big' => 'Mars', 'planet' => 'Mars', 'age' => 32, 'day' => 0],
                ['big' => 'Mars', 'planet' => 'Sun', 'age' => 33, 'day' => 0], ['big' => 'Mars', 'planet' => 'Venus', 'age' => 34, 'day' => 0],
                ['big' => 'Mars', 'planet' => 'Mercury', 'age' => 35, 'day' => 0], ['big' => 'Mars', 'planet' => 'Moon', 'age' => 36, 'day' => 0],
                ['big' => 'Mars', 'planet' => 'Saturn', 'age' => 37, 'day' => 0], ['big' => 'Mars', 'planet' => 'Jupiter', 'age' => 38, 'day' => 0],
                ['big' => 'Sun', 'planet' => 'Sun', 'age' => 39, 'day' => 0], ['big' => 'Sun', 'planet' => 'Venus', 'age' => 40, 'day' => 156],
                ['big' => 'Sun', 'planet' => 'Mercury', 'age' => 41, 'day' => 313], ['big' => 'Sun', 'planet' => 'Moon', 'age' => 43, 'day' => 104],
                ['big' => 'Sun', 'planet' => 'Saturn', 'age' => 44, 'day' => 261], ['big' => 'Sun', 'planet' => 'Jupiter', 'age' => 46, 'day' => 52],
                ['big' => 'Sun', 'planet' => 'Mars', 'age' => 47, 'day' => 209], ['big' => 'Venus', 'planet' => 'Venus', 'age' => 49, 'day' => 0],
                ['big' => 'Venus', 'planet' => 'Mercury', 'age' => 50, 'day' => 52], ['big' => 'Venus', 'planet' => 'Moon', 'age' => 51, 'day' => 104],
                ['big' => 'Venus', 'planet' => 'Saturn', 'age' => 52, 'day' => 156], ['big' => 'Venus', 'planet' => 'Jupiter', 'age' => 53, 'day' => 209],
                ['big' => 'Venus', 'planet' => 'Mars', 'age' => 54, 'day' => 261], ['big' => 'Venus', 'planet' => 'Sun', 'age' => 55, 'day' => 313],
                ['big' => 'Mercury', 'planet' => 'Mercury', 'age' => 57, 'day' => 0], ['big' => 'Mercury', 'planet' => 'Moon', 'age' => 58, 'day' => 313],
                ['big' => 'Mercury', 'planet' => 'Saturn', 'age' => 60, 'day' => 261], ['big' => 'Mercury', 'planet' => 'Jupiter', 'age' => 62, 'day' => 209],
                ['big' => 'Mercury', 'planet' => 'Mars', 'age' => 64, 'day' => 156], ['big' => 'Mercury', 'planet' => 'Sun', 'age' => 66, 'day' => 104],
                ['big' => 'Mercury', 'planet' => 'Venus', 'age' => 68, 'day' => 52], ['big' => 'meanNode', 'planet' => 'meanNode', 'age' => 70, 'day' => 0],
                ['big' => 'MeanSouthNode', 'planet' => 'MeanSouthNode', 'age' => 73, 'day' => 0]);

            $true_love_day = array(['big' => 'Sun', 'planet' => 'Sun', 'age' => 0, 'day' => 0], ['big' => 'Sun', 'planet' => 'Venus', 'age' => 1, 'day' => 156],
                ['big' => 'Sun', 'planet' => 'Mercury', 'age' => 2, 'day' => 313], ['big' => 'Sun', 'planet' => 'Moon', 'age' => 4, 'day' => 104],
                ['big' => 'Sun', 'planet' => 'Saturn', 'age' => 5, 'day' => 261], ['big' => 'Sun', 'planet' => 'Jupiter', 'age' => 7, 'day' => 52],
                ['big' => 'Sun', 'planet' => 'Mars', 'age' => 8, 'day' => 209], ['big' => 'Venus', 'planet' => 'Venus', 'age' => 10, 'day' => 0],
                ['big' => 'Venus', 'planet' => 'Mercury', 'age' => 11, 'day' => 52], ['big' => 'Venus', 'planet' => 'Moon', 'age' => 12, 'day' => 104],
                ['big' => 'Venus', 'planet' => 'Saturn', 'age' => 13, 'day' => 156], ['big' => 'Venus', 'planet' => 'Jupiter', 'age' => 14, 'day' => 209],
                ['big' => 'Venus', 'planet' => 'Mars', 'age' => 15, 'day' => 261], ['big' => 'Venus', 'planet' => 'Sun', 'age' => 16, 'day' => 313],
                ['big' => 'Mercury', 'planet' => 'Mercury', 'age' => 18, 'day' => 0], ['big' => 'Mercury', 'planet' => 'Moon', 'age' => 19, 'day' => 313],
                ['big' => 'Mercury', 'planet' => 'Saturn', 'age' => 21, 'day' => 261], ['big' => 'Mercury', 'planet' => 'Jupiter', 'age' => 23, 'day' => 209],
                ['big' => 'Mercury', 'planet' => 'Mars', 'age' => 25, 'day' => 156], ['big' => 'Mercury', 'planet' => 'Sun', 'age' => 27, 'day' => 104],
                ['big' => 'Mercury', 'planet' => 'Venus', 'age' => 29, 'day' => 52], ['big' => 'Moon', 'planet' => 'Moon', 'age' => 31, 'day' => 0],
                ['big' => 'Moon', 'planet' => 'Saturn', 'age' => 32, 'day' => 104], ['big' => 'Moon', 'planet' => 'Jupiter', 'age' => 33, 'day' => 209],
                ['big' => 'Moon', 'planet' => 'Mars', 'age' => 34, 'day' => 313], ['big' => 'Moon', 'planet' => 'Sun', 'age' => 36, 'day' => 52],
                ['big' => 'Moon', 'planet' => 'Venus', 'age' => 37, 'day' => 156], ['big' => 'Moon', 'planet' => 'Mercury', 'age' => 38, 'day' => 261],
                ['big' => 'Saturn', 'planet' => 'Saturn', 'age' => 40, 'day' => 0], ['big' => 'Saturn', 'planet' => 'Jupiter', 'age' => 41, 'day' => 209],
                ['big' => 'Saturn', 'planet' => 'Mars', 'age' => 43, 'day' => 52], ['big' => 'Saturn', 'planet' => 'Sun', 'age' => 44, 'day' => 261],
                ['big' => 'Saturn', 'planet' => 'Venus', 'age' => 46, 'day' => 104], ['big' => 'Saturn', 'planet' => 'Mercury', 'age' => 47, 'day' => 313],
                ['big' => 'Saturn', 'planet' => 'Moon', 'age' => 49, 'day' => 156], ['big' => 'Jupiter', 'planet' => 'Jupiter', 'age' => 51, 'day' => 0],
                ['big' => 'Jupiter', 'planet' => 'Mars', 'age' => 52, 'day' => 261], ['big' => 'Jupiter', 'planet' => 'Sun', 'age' => 54, 'day' => 156],
                ['big' => 'Jupiter', 'planet' => 'Venus', 'age' => 56, 'day' => 52], ['big' => 'Jupiter', 'planet' => 'Mercury', 'age' => 57, 'day' => 313],
                ['big' => 'Jupiter', 'planet' => 'Moon', 'age' => 59, 'day' => 209], ['big' => 'Jupiter', 'planet' => 'Saturn', 'age' => 61, 'day' => 104],
                ['big' => 'Mars', 'planet' => 'Mars', 'age' => 63, 'day' => 0], ['big' => 'Mars', 'planet' => 'Sun', 'age' => 64, 'day' => 0],
                ['big' => 'Mars', 'planet' => 'Venus', 'age' => 65, 'day' => 0], ['big' => 'Mars', 'planet' => 'Mercury', 'age' => 66, 'day' => 0],
                ['big' => 'Mars', 'planet' => 'Moon', 'age' => 67, 'day' => 0], ['big' => 'Mars', 'planet' => 'Saturn', 'age' => 68, 'day' => 0],
                ['big' => 'Mars', 'planet' => 'Jupiter', 'age' => 69, 'day' => 0], ['big' => 'meanNode', 'planet' => 'meanNode', 'age' => 70, 'day' => 0],
                ['big' => 'MeanSouthNode', 'planet' => 'MeanSouthNode', 'age' => 73, 'day' => 0]);

            $planet_one_true = array();
            $planet_two_true = array();
            $planet_three_true = array();
            $key_number = 0;

            if ($day_chart == true) {
                foreach ($true_love_day as $key => &$value) {
                    $value['standard'] = mktime($hours, $mins, $secs, $month, ($day + $value['day']), ($years + $value['age']));
                    if ($new_birthday_time > $value['standard']) {
                        $key_number = $key;
                    }
                }
                $planet_one_true = $true_love_day[$key_number];
                $planet_two_true = $true_love_day[$key_number + 1];
                $true_love_day[$key_number + 1]['standard'] = mktime($hours, $mins, $secs, $month, ($day + $true_love_night[$key_number + 1]['day']), ($years + $true_love_night[$key_number + 1]['age']));


                if (date('Y', $planet_two_true['standard']) == $new_years and $key_number > 0) {

                    $planet_three_true = $true_love_day[(($key_number + 2)%51)];
                }

            } else {
                foreach ($true_love_night as $key => &$value) {
                    $value['standard'] = mktime($hours, $mins, $secs, $month, ($day + $value['day']), ($years + $value['age']));
                    if ($new_birthday_time > $value['standard']) {
                        $key_number = $key;
                    }
                }
                $planet_one_true = $true_love_night[$key_number];
                $planet_two_true = $true_love_night[$key_number + 1];
                $true_love_night[$key_number + 1]['standard'] = mktime($hours, $mins, $secs, $month, ($day + $true_love_night[$key_number + 1]['day']), ($years + $true_love_night[$key_number + 1]['age']));
                if (date('Y', $planet_two_true['standard']) == $new_years and $key_number > 0) {
                    $planet_three_true = $true_love_night[(($key_number + 2)%51)];
                }
            }


            $method_two['limit_small'] = $planet_one_true['planet'];
            $method_two['limit_big'] = $planet_one_true['big'];

            if ($planet_one_true['standard'] < mktime(0, 0, 1, 1, 1, 2025)) {
                $planet_one_true['standard'] = mktime(0, 0, 1, 1, 1, 2025);
            }


            $method_two['limit_date'] = date('Y年m月d日', $planet_one_true['standard']) . '-' . date('Y年m月d日', $planet_two_true['standard']);

            if (!empty($life_lin_score[strtolower($method_two['limit_small'])])) {
                $limit_lin_score = round($life_lin_score[strtolower($method_two['limit_small'])], 2);
            } else {
                $limit_lin_score = 0;
            }

            if ($limit_lin_score >= 7) {
                $method_two['limit_score'] = 5;
            } elseif ($limit_lin_score >= 5 and $limit_lin_score < 7) {
                $method_two['limit_score'] = 4;
            } elseif ($limit_lin_score >= 3 and $limit_lin_score < 5) {
                $method_two['limit_score'] = 3;
            } elseif ($limit_lin_score >= 0 and $limit_lin_score < 3) {
                $method_two['limit_score'] = 2;
            } else {
                $method_two['limit_score'] = 1;
            }

            $corpusWhere['title'] = $planetChinese[$method_two['limit_small']];
            $corpusWhere['status'] = 1;
            $corpusWhere['attribution_str'] = '小限行星';

            $corpusInfo = $this->logicCorpus->getCorpusInfo($corpusWhere);

            $corpusRetrogradePalaceWhere['attribution_str'] = ['in', ['逆行本命宫关键词', '逆行宫头星座']];
            $corpusRetrogradePalaceWhere['status'] = 1;
            $corpusRetrogradePalace = $this->logicCorpus->getCorpusColumn($corpusRetrogradePalaceWhere, true, 'title');


            $corpusTakeKeywords = array();
            $corpus_TakeKeywords_file = array();
            foreach ($life_planet as $key => $value) {
                if ($value['english_name'] == $method_two['limit_small']) {
                    $corpusTakeKeywordsWhere['title'] = '本命' . $planetChinese[$method_two['limit_small']] . '落入' . $signsChinese[$value['sign_id']];
                    $corpusTakeKeywordsWhere['attribution_str'] = '小限行星落座关键词';
                    $corpusTakeKeywordsWhere['status'] = 1;
                    $corpusTakeKeywords = $this->logicCorpus->getCorpusInfo($corpusTakeKeywordsWhere);
                    $corpus_TakeKeywords_file = json_decode($corpusTakeKeywords['note'], true);
                    break;
                }
            }

            $corpus_limit_file = json_decode($corpusInfo['note'], true);

            if (empty($corpus_limit_file)) {
                $corpus_limit_file = array();
            }
            $corpus_limit_info = array();
            foreach ($corpus_limit_file as $keycr => $valuecr) {
                $corpus_limit_info_lin = '';
                if ($method_two['limit_score'] >= 4) {
                    if (strstr($valuecr['field'], 'good') !== false) {
                        $corpus_limit_info_lin = $corpusInfo[$keycr];

                    } else {
                        continue;
                    }
                } elseif ($method_two['limit_score'] == 2 or $method_two['limit_score'] == 3) {
                    if (strstr($valuecr['field'], 'the') !== false) {
                        $corpus_limit_info_lin = $corpusInfo[$keycr];
                    } else {
                        continue;
                    }
                } else {
                    if (strstr($valuecr['field'], 'bad') !== false) {
                        $corpus_limit_info_lin = $corpusInfo[$keycr];
                    } else {
                        continue;
                    }
                }

                //小限行星主宰宫位 $signs_guardian_english
                if (strstr($corpus_limit_info_lin, '[小限行星主宰宫位]') !== false) {
                    foreach ($signs_guardian_english as $keyguardian => $guardianenglish) {
                        if ($guardianenglish == $method_two['limit_small']) {
                            foreach ($life_house as $keyh => $valueh) {
                                if ($valueh['sign_id'] == $keyguardian) {
                                    $signs_guardian_title = $corpusRetrogradePalace['本命' . $houseChinese[$valueh['house_id']] . '关键词']['content2'];
                                    if (count($signs_guardian_english) < ($keyguardian + 1)) {
                                        $signs_guardian_title .= '[小限行星主宰宫位]和';
                                    }
                                    $corpus_limit_info_lin = str_replace('[小限行星主宰宫位]', $signs_guardian_title, $corpus_limit_info_lin);
                                    break;
                                }
                                if ($keyh == 0) {
                                    $on_house = 11;
                                } else {
                                    $on_house = $keyh - 1;
                                }
                                if ($life_house[$on_house]['sign_id'] < $keyguardian and $valueh['sign_id'] > $keyguardian) {
                                    $signs_guardian_title = $corpusRetrogradePalace['本命' . $houseChinese[$valueh['house_id']] . '关键词']['content2'];
                                    if (count($signs_guardian_english) < ($keyguardian + 1)) {
                                        $signs_guardian_title .= '[小限行星主宰宫位]和';
                                    }
                                    $corpus_limit_info_lin = str_replace('[小限行星主宰宫位]', $signs_guardian_title, $corpus_limit_info_lin);
                                    break;
                                }

                            }
                        }
                    }
                }


                foreach ($life_house as $keyh => $valueh) {
                    //宫头落入星座
                    $house_signs_str = $houseChinese[$keyh] . '宫头落入' . $signsChinese[$valueh['sign_id']];
                    $needle = '[本命' . $houseChinese[$keyh] . '宫头落入的星座]';
                    if (strstr($corpus_limit_info_lin, $needle) !== false) {
                        $corpus_limit_info_lin = str_replace('[本命' . $houseChinese[$keyh] . '宫头落入的星座]', $corpusRetrogradePalace[$house_signs_str]['content1'], $corpus_limit_info_lin);
                        break;
                    }
                }
                //小限行星落座关键词本命火星落入星座负面  本命木星落入射手座

                foreach ($corpus_TakeKeywords_file as $keyctw => $valuectw) {
                    $corpus_TakeKeywords_info_lin = '';

                    if (strstr($corpus_limit_info_lin, '正面]') !== false) {

                        if (strstr($valuectw['field'], 'good') !== false) {

                            $corpus_TakeKeywords_info_lin = $corpusTakeKeywords[$keyctw];
                        } else {
                            continue;
                        }
                        if (!empty($corpus_TakeKeywords_info_lin)) {
                            $corpus_limit_info_lin = str_replace('[本命' . $planetChinese[$method_two['limit_small']] . '落入星座正面]', $corpus_TakeKeywords_info_lin, $corpus_limit_info_lin);
                        }
                    }
                    if (strstr($corpus_limit_info_lin, '负面]') !== false) {
                        if (strstr($valuectw['field'], 'bad') !== false) {
                            $corpus_TakeKeywords_info_lin = $corpusTakeKeywords[$keyctw];
                        } else {
                            continue;
                        }
                        if (!empty($corpus_TakeKeywords_info_lin)) {
                            $corpus_limit_info_lin = str_replace('[本命' . $planetChinese[$method_two['limit_small']] . '落入星座负面]', $corpus_TakeKeywords_info_lin, $corpus_limit_info_lin);
                        }
                    }
                }
                $corpus_limit_info[$dataKey[$valuecr['field']]] = $corpus_limit_info_lin;

            }
            $method_two['limit_corpus'] = $corpus_limit_info;
            $data['method'][] = $method_two;


            if (count($planet_three_true) > 0) {

                $method_one['limit_small'] = $planet_two_true['planet'];
                $method_one['limit_big'] = $planet_two_true['big'];
                $method_one['limit_date'] = date('Y年m月d日', $planet_two_true['standard']) . '-' . date('Y年m月d日', $planet_three_true['standard']);

                if (!empty($life_lin_score[strtolower($method_one['limit_small'])])) {
                    $limit_lin_score = round($life_lin_score[strtolower($method_one['limit_small'])], 2);
                } else {
                    $limit_lin_score = 0;
                }

                if ($limit_lin_score >= 7) {
                    $method_one['limit_score'] = 5;
                } elseif ($limit_lin_score >= 5 and $limit_lin_score < 7) {
                    $method_one['limit_score'] = 4;
                } elseif ($limit_lin_score >= 3 and $limit_lin_score < 5) {
                    $method_one['limit_score'] = 3;
                } elseif ($limit_lin_score >= 0 and $limit_lin_score < 3) {
                    $method_one['limit_score'] = 2;
                } else {
                    $method_one['limit_score'] = 1;
                }


                $corpusWhere['title'] = $planetChinese[$method_one['limit_small']];
                $corpusWhere['status'] = 1;
                $corpusWhere['attribution_str'] = '小限行星';

                $corpusInfo = $this->logicCorpus->getCorpusInfo($corpusWhere);

                $corpusTakeKeywords = array();
                $corpus_TakeKeywords_file = array();
                foreach ($life_planet as $key => $value) {
                    if ($value['english_name'] == $method_one['limit_small']) {
                        $corpusTakeKeywordsWhere['title'] = '本命' . $planetChinese[$method_one['limit_small']] . '落入' . $signsChinese[$value['sign_id']];
                        $corpusTakeKeywordsWhere['attribution_str'] = '小限行星落座关键词';
                        $corpusTakeKeywordsWhere['status'] = 1;
                        $corpusTakeKeywords = $this->logicCorpus->getCorpusInfo($corpusTakeKeywordsWhere);
                        $corpus_TakeKeywords_file = json_decode($corpusTakeKeywords['note'], true);
                        break;
                    }
                }

                $corpus_limit_file = json_decode($corpusInfo['note'], true);
                if (empty($corpus_limit_file)) {
                    $corpus_limit_file = array();
                }
                foreach ($corpus_limit_file as $keycr => $valuecr) {
                    $corpus_limit_info_lin = '';
                    if ($method_one['limit_score'] >= 4) {
                        if (strstr($valuecr['field'], 'good') !== false) {
                            $corpus_limit_info_lin = $corpusInfo[$keycr];

                        } else {
                            continue;
                        }
                    } elseif ($method_one['limit_score'] == 2 or $method_one['limit_score'] == 3) {
                        if (strstr($valuecr['field'], 'the') !== false) {
                            $corpus_limit_info_lin = $corpusInfo[$keycr];
                        } else {
                            continue;
                        }
                    } else {
                        if (strstr($valuecr['field'], 'bad') !== false) {
                            $corpus_limit_info_lin = $corpusInfo[$keycr];
                        } else {
                            continue;
                        }
                    }

                    //小限行星主宰宫位 $signs_guardian_english
                    if (strstr($corpus_limit_info_lin, '[小限行星主宰宫位]') !== false) {
                        foreach ($signs_guardian_english as $keyguardian => $guardianenglish) {
                            if ($guardianenglish == $method_one['limit_small']) {
                                foreach ($life_house as $keyh => $valueh) {
                                    if ($valueh['sign_id'] == $keyguardian) {
                                        $signs_guardian_title = $corpusRetrogradePalace['本命' . $houseChinese[$valueh['house_id']] . '关键词']['content2'];
                                        if (count($signs_guardian_english) < ($keyguardian + 1)) {
                                            $signs_guardian_title .= '[小限行星主宰宫位]和';
                                        }
                                        $corpus_limit_info_lin = str_replace('[小限行星主宰宫位]', $signs_guardian_title, $corpus_limit_info_lin);
                                        break;
                                    }
                                    if ($keyh == 0) {
                                        $on_house = 11;
                                    } else {
                                        $on_house = $keyh - 1;
                                    }
                                    if ($life_house[$on_house]['sign_id'] < $keyguardian and $valueh['sign_id'] > $keyguardian) {
                                        $signs_guardian_title = $corpusRetrogradePalace['本命' . $houseChinese[$valueh['house_id']] . '关键词']['content2'];
                                        if (count($signs_guardian_english) < ($keyguardian + 1)) {
                                            $signs_guardian_title .= '[小限行星主宰宫位]和';
                                        }
                                        $corpus_limit_info_lin = str_replace('[小限行星主宰宫位]', $signs_guardian_title, $corpus_limit_info_lin);
                                        break;
                                    }

                                }
                            }
                        }
                    }

                    foreach ($life_house as $keyh => $valueh) {
                        //宫头落入星座
                        $house_signs_str = $houseChinese[$keyh] . '宫头落入' . $signsChinese[$valueh['sign_id']];
                        $needle = '[本命' . $houseChinese[$keyh] . '宫头落入的星座]';
                        if (strstr($corpus_limit_info_lin, $needle) !== false) {
                            $corpus_limit_info_lin = str_replace('[本命' . $houseChinese[$keyh] . '宫头落入的星座]', $corpusRetrogradePalace[$house_signs_str]['content1'], $corpus_limit_info_lin);
                            break;
                        }
                    }
                    //小限行星落座关键词本命火星落入星座负面  本命木星落入射手座

                    foreach ($corpus_TakeKeywords_file as $keyctw => $valuectw) {
                        $corpus_TakeKeywords_info_lin = '';
                        if (strstr($corpus_limit_info_lin, '正面]') !== false) {
                            if (strstr($valuectw['field'], 'good') !== false) {

                                $corpus_TakeKeywords_info_lin = $corpusTakeKeywords[$keyctw];
                            } else {
                                continue;
                            }
                            if (!empty($corpus_TakeKeywords_info_lin)) {
                                $corpus_limit_info_lin = str_replace('[本命' . $planetChinese[$method_one['limit_small']] . '落入星座正面]', $corpus_TakeKeywords_info_lin, $corpus_limit_info_lin);
                            }
                        }
                        if (strstr($corpus_limit_info_lin, '负面]') !== false) {
                            if (strstr($valuectw['field'], 'bad') !== false) {
                                $corpus_TakeKeywords_info_lin = $corpusTakeKeywords[$keyctw];
                            } else {
                                continue;
                            }
                            if (!empty($corpus_TakeKeywords_info_lin)) {
                                $corpus_limit_info_lin = str_replace('[本命' . $planetChinese[$method_one['limit_small']] . '落入星座负面]', $corpus_TakeKeywords_info_lin, $corpus_limit_info_lin);
                            }
                        }
                    }
                    $corpus_limit_info[$dataKey[$valuecr['field']]] = $corpus_limit_info_lin;

                }
                $method_one['limit_corpus'] = $corpus_limit_info;
                $data['method'][] = $method_one;
            }

            //逆行

            $retrogradePlanet[] = ['plant' => 'Jupiter', 'p' => '5',  "post_shadow_end"=> "2025-05-01",
                "pre_shadow_start"=> "2024-07-15",
                "retrograde_end"=> "2025-02-04",
                "retrograde_start"=> "2024-10-09"];
            $retrogradePlanet[] = ['plant' => 'Jupiter', 'p' => '5',  "post_shadow_end"=> "2026-06-06",
                "pre_shadow_start"=> "2025-08-17",
                "retrograde_end"=> "2026-03-11",
                "retrograde_start"=> "2025-11-12"];
            $retrogradePlanet[] = ['plant' => 'Mars', 'p' => '4',  "post_shadow_end"=> "2025-05-02",
                "pre_shadow_start"=> "2024-10-05",
                "retrograde_end"=> "2025-02-24",
                "retrograde_start"=> "2024-12-07"];
            $retrogradePlanet[] = ['plant' => 'Mercury', 'p' => '2',"post_shadow_end"=> "2025-04-26",
                "pre_shadow_start"=> "2025-03-01",
                "retrograde_end"=> "2025-04-07",
                "retrograde_start"=> "2025-03-15"];
            $retrogradePlanet[] = ['plant' => 'Mercury', 'p' => '2', "post_shadow_end"=> "2025-08-25",
                "pre_shadow_start"=> "2025-06-30",
                "retrograde_end"=> "2025-08-11",
                "retrograde_start"=> "2025-07-18"];
            $retrogradePlanet[] = ['plant' => 'Mercury', 'p' => '2',
                "post_shadow_end"=> "2025-12-17",
                "pre_shadow_start"=> "2025-10-21",
                "retrograde_end"=> "2025-11-30",
                "retrograde_start"=> "2025-11-10"];
            //$retrogradePlanet[] = ['plant' => 'Neptune', 'p' => '8', 'start_time' => '2025-07-05 12=>56', 'end_time' => '2025-12-10 12=>14'];
            //$retrogradePlanet[] = ['plant' => 'Pluto', 'p' => '9', 'start_time' => '2025-05-04 12=>56', 'end_time' => '2025-10-14 12=>14'];
            $retrogradePlanet[] = ['plant' => 'Saturn', 'p' => '6',"post_shadow_end"=> "2026-03-03",
                "pre_shadow_start"=> "2025-04-06",
                "retrograde_end"=> "2025-11-28",
                "retrograde_start"=> "2025-07-13"];
            //$retrogradePlanet[] = ['plant' => 'Uranus', 'p' => '7', 'start_time' => '2024-09-01 12=>56', 'end_time' => '2025-01-31 12=>14'];
            //$retrogradePlanet[] = ['plant' => 'Uranus', 'p' => '7', 'start_time' => '2025-09-06 12=>56', 'end_time' => '2026-02-04 12=>14'];
            $retrogradePlanet[] = ['plant' => 'Venus', 'p' => '3',  "post_shadow_end"=> "2025-05-16",
                "pre_shadow_start"=> "2025-01-28",
                "retrograde_end"=> "2025-04-13",
                "retrograde_start"=> "2025-03-02"];


            $p = get_sington_object('SweTest', "astrology\\SweTest");

            $living_district_array = $data['user']['living_district_array'];
            $living_district = $living_district_array[(count($living_district_array) - 1)];

            foreach ($retrogradePlanet as $key => $value) {

                $birthdayToTime = strtotime($value['retrograde_start'])+12* 3600 - $living_district['tz'] * 3600;
                $utdatenow = date('d.m.Y', $birthdayToTime);
                $utnow = date('H:i:s', $birthdayToTime);
                $house = $living_district['longitude'] . ',' . $living_district['latitude'] . ',k';
                $arr = [
                    'b' => $utdatenow,
                    'p' => $value['p'],
                    'house' => $house,
                    'ut' => $utnow,
                    'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
                    'g' => ',',
                    'head',
                    'roundsec'
                ];
                $start_planet_house = $p->calculate($arr, [$value['p']]);
                $start_planet = $start_planet_house['planet'];
                $planet_house = $this->housePlanet($start_planet[0], $life_house);

                $corpusWhere['title'] = '逆行' . $planetChinese[$value['plant']] . $houseChinese[$planet_house];
                $corpusWhere['status'] = 1;
                $corpusWhere['attribution_str'] = '逆行行星落入宫位';
                $corpus_retrograde = $this->logicCorpus->getCorpusInfo($corpusWhere);

                $corpus_retrograde_file = json_decode($corpus_retrograde['note'], true);

                if (!empty($corpus_retrograde_file)) {


                    foreach ($corpus_retrograde_file as $keycr => $valuecr) {
                        $corpus_retrograde_info_lin = $corpus_retrograde[$keycr];
                        foreach ($life_house as $keyh => $valueh) {
                            //宫头落入星座
                            $needle = '[' . $houseChinese[$keyh] . '宫头落入星座]';
                            if (strstr($corpus_retrograde_info_lin, $needle) !== false) {
                                $house_signs_str = $houseChinese[$keyh] . '宫头落入' . $signsChinese[$valueh['sign_id']];
                                $corpus_retrograde_info_lin = str_replace($needle, $corpusRetrogradePalace[$house_signs_str]['content1'], $corpus_retrograde_info_lin);
                            }
                            //本命宫头,本命第四宫关键词
                            $needle = '[本命' . $houseChinese[$keyh] . '关键词]';
                            if (strstr($corpus_retrograde_info_lin, $needle) !== false) {

                                $corpus_key_retrograde = $corpusRetrogradePalace['本命' . $houseChinese[$keyh] . '关键词'];

                                $corpus_retrograde_info_lin = str_replace($needle, $corpus_key_retrograde['content1'], $corpus_retrograde_info_lin);
                            }
                        }

                        //行运行星sign_id
                        $needle = '[行运' . $planetChinese[$value['plant']] . '落入星座]';
                        if (strstr($corpus_retrograde_info_lin, $needle) !== false) {
                            $corpus_key_Where['title'] = '行运' . $planetChinese[$value['plant']] . '落入' . $signsChinese[$start_planet[0]['sign_id']];
                            $corpus_key_Where['attribution_str'] = '逆行行运行星落星座';
                            $corpus_key_retrograde = $this->logicCorpus->getCorpusInfo($corpus_key_Where);
                            $corpus_retrograde_info_lin = str_replace($needle, $corpus_key_retrograde['content1'], $corpus_retrograde_info_lin);
                        }

                        $corpus_retrograde_info['values'] = $value;
                        $corpus_retrograde_info['plant'] = $value['plant'];
                        $corpus_retrograde_info[$valuecr['field']] = $corpus_retrograde_info_lin;
                    }
                    $data['retrograde'][] = $corpus_retrograde_info;
                }
//                $birthdayToTime = strtotime($value['end_time']) - $living_district['tz'] * 3600;
//                $utdatenow = date('d.m.Y', $birthdayToTime);
//                $utnow = date('H:i:s', $birthdayToTime);
//                $house = $living_district['longitude'] . ',' . $living_district['latitude'] . ',k';
//                $arr = [
//                    'b' => $utdatenow,
//                    'p' => '0123456789H',
//                    'house' => $house,
//                    'ut' => $utnow,
//                    'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
//                    'g' => ',',
//                    'head',
//                    'roundsec'
//                ];
//                $end_planet_house = $p->calculate($arr, [ '2', '3','5', '6']);
//                $end_planet=$end_planet_house['planet'];
            }

            //流年Time flies
            $corpusTimeFliesWhere['attribution_str'] = ['in', ['流年相位', '流年行星过宫位']];
            $corpusTimeFliesWhere['status'] = 1;
            $corpusTimeFliesPalace = $this->logicCorpus->getCorpusColumn($corpusTimeFliesWhere, true, 'title');

            for ($i = 0; $i < 13; $i++) {

                $moon = 1 + $i;

                $timeFlies_start = mktime(0, 0, 0, $moon, 1, date('Y'));

                $utdatenow = date('d.m.Y', $timeFlies_start);
                $utnow = date('H:i:s', $birthdayToTime);
                $house = $living_district['longitude'] . ',' . $living_district['latitude'] . ',k';
                $arr = [
                    'b' => $utdatenow,
                    'p' => '0123456789H',
                    'house' => $house,
                    'ut' => $utnow,
                    'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
                    'g' => ',',
                    'head',
                    'roundsec'
                ];
                $timeFlies_planet_house = $p->calculate($arr, ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'H']);
                $timeFlies_planet[] = $timeFlies_planet_house['planet'];
            }

            //本命的宫宫主星位置
            for ($i = 0; $i < 12; $i++) {
                $signs_guardian_planet[] = $life_planet[$signs_guardian_index[$life_house[$i]['sign_id']]];  //本命第四宫宫主星
            }
            $corpusTimeFliesPalace_info_keyctf_bei = array();

            foreach ($timeFlies_planet as $keyt => $valuet) {

                if ($keyt < (count($timeFlies_planet) - 1)) {

                    $Venus_planet_timeFlies = $valuet[3];
                    $last_Venus_planet_timeFlies = $timeFlies_planet[$keyt + 1][3];
                    $planet_Venus_house = $this->housePlanet($Venus_planet_timeFlies, $life_house);
                    $last_planet_Venus_house = $this->housePlanet($last_Venus_planet_timeFlies, $life_house);

                    $Mars_planet_timeFlies = $valuet[4];
                    $last_Mars_planet_timeFlies = $timeFlies_planet[$keyt + 1][4];
                    $planet_Mars_house = $this->housePlanet($Mars_planet_timeFlies, $life_house);
                    $last_planet_Mars_house = $this->housePlanet($last_Mars_planet_timeFlies, $life_house);


                    //判断爱情好坏
                    //1、流年金星进入本命第五宫的月份（最后五天接触宫头算下一个月）
                    //2、流年金星进入本命第七宫的月份
                    //3、流年金星合本命第五宫宫主星的月份
                    //4、流年金星合相本命金星的月份）
                    $love_true = false;
                    $love_score = 3;
                    if ($planet_Venus_house == 4 or $last_planet_Venus_house == 4) {
                        $love_true = true;
                        $love_score++;
                    }
                    if ($planet_Venus_house == 6 or $last_planet_Venus_house == 6) {
                        $love_true = true;
                        $love_score++;
                    }
                    if ($signs_guardian_planet[4]['longitude'] > $Venus_planet_timeFlies['longitude'] and $signs_guardian_planet[4]['longitude'] < $last_Venus_planet_timeFlies['longitude']) {
                        $love_true = true;
                    }
                    if ($life_planet[3]['longitude'] > $Venus_planet_timeFlies['longitude'] and $life_planet[3]['longitude'] < $last_Venus_planet_timeFlies['longitude']) {
                        $love_true = true;
                    }

                    //
                    $allert = [60, 120];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[4]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[4]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[4]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[4]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[6]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[6]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[6]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[6]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[3]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[3]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[3]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[3]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[1]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[1]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[1]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[1]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[0]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[0]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[0]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[0]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score++;
                            break;
                        }
                    }
                    //当流年火星落入金牛或者天秤，进入本命第五宫的月份-1
                    if (($Mars_planet_timeFlies['sign_id'] == 1 or $last_Mars_planet_timeFlies['sign_id'] == 1) and ($Mars_planet_timeFlies['sign_id'] == 6 or $last_Mars_planet_timeFlies['sign_id'] == 6)) {
                        if ($planet_Mars_house == 4 or $last_planet_Mars_house == 4) {
                            $love_score--;
                        }
                        if ($planet_Mars_house == 6 or $last_planet_Mars_house == 6) {
                            $love_score--;
                        }

                    }
                    $allert = [0, 90, 180];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[6]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[6]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[6]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[6]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $love_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[4]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[4]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[4]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[4]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $love_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[3]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $life_planet[3]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($life_planet[3]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $life_planet[3]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $love_score--;
                            break;
                        }
                    }
                    $allert = [90, 180];
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[6]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[6]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[6]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[6]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[9]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[9]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[9]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[9]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[8]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[8]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[8]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[8]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $love_score--;
                            break;
                        }
                    }
                    //判断事业好坏
                    //1、流年金星进入本命第十宫的月份
                    //2、流年金星进入本命第六宫的月份
                    //3、流年金星合相本命第十宫宫主月份
                    //4、流年金星合相本命第六宫宫主月份
                    $career_true = false;
                    $career_score = 3;
                    if ($planet_Venus_house == 5 or $last_planet_Venus_house == 5) {
                        $career_true = true;
                        $career_score++;
                    }
                    if ($planet_Venus_house == 9 or $last_planet_Venus_house == 9) {
                        $career_true = true;
                        $career_score++;
                    }
                    if ($signs_guardian_planet[5]['longitude'] > $Venus_planet_timeFlies['longitude'] and $signs_guardian_planet[5]['longitude'] < $last_Venus_planet_timeFlies['longitude']) {
                        $career_true = true;
                    }
                    if ($signs_guardian_planet[9]['longitude'] > $Venus_planet_timeFlies['longitude'] and $signs_guardian_planet[9]['longitude'] < $last_Venus_planet_timeFlies['longitude']) {
                        $career_true = true;
                    }
                    $allert = [60, 120];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $career_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[4]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[4]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[4]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[4]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $career_score++;
                            break;
                        }
                    }
                    //当流年火星落入金牛或者天秤，进入本命第五宫的月份-1
                    if (($Mars_planet_timeFlies['sign_id'] == 1 or $last_Mars_planet_timeFlies['sign_id'] == 1) and ($Mars_planet_timeFlies['sign_id'] == 6 or $last_Mars_planet_timeFlies['sign_id'] == 6)) {
                        if ($planet_Mars_house == 5 or $last_planet_Mars_house == 5) {
                            $career_score--;
                        }
                        if ($planet_Mars_house == 9 or $last_planet_Mars_house == 9) {
                            $career_score--;
                        }
                    }
                    $allert = [0, 90, 180];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[6]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[6]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[6]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[6]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $career_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[4]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[4]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[4]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[4]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $career_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[6]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $life_planet[6]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($life_planet[6]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $life_planet[6]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $career_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[5]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $life_planet[5]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($life_planet[5]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $life_planet[5]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $career_score--;
                            break;
                        }
                    }
                    //判断健康好坏
                    //1、流年火星进入本命第六宫的月份
                    //2、流年火星进入本命第一宫的月份
                    //3、流年火星合相本命第一宫宫主月份
                    //4、流年火星合相本命第六宫宫主月份
                    $health_true = false;
                    $health_score = 3;
                    if ($planet_Mars_house == 5 or $last_planet_Mars_house == 5) {
                        $health_true = true;
                        $health_score++;
                    }
                    if ($planet_Mars_house == 0 or $last_planet_Mars_house == 0) {
                        $health_true = true;
                        $health_score++;
                    }
                    if ($signs_guardian_planet[0]['longitude'] > $Mars_planet_timeFlies['longitude'] and $signs_guardian_planet[0]['longitude'] < $last_Mars_planet_timeFlies['longitude']) {
                        $health_true = true;
                    }
                    if ($signs_guardian_planet[5]['longitude'] > $Mars_planet_timeFlies['longitude'] and $signs_guardian_planet[5]['longitude'] < $last_Mars_planet_timeFlies['longitude']) {
                        $health_true = true;
                    }
                    $allert = [60, 120];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[0]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[0]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[0]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[0]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $health_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $health_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[0]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[0]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[0]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[0]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $health_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[1]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[1]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[1]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[1]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $health_score++;
                            break;
                        }
                    }
                    //当流年火星落入金牛或者天秤，进入本命第1宫的月份-1
                    if (($Mars_planet_timeFlies['sign_id'] == 1 or $last_Mars_planet_timeFlies['sign_id'] == 1) and ($Mars_planet_timeFlies['sign_id'] == 6 or $last_Mars_planet_timeFlies['sign_id'] == 6)) {
                        if ($planet_Mars_house == 0 or $last_planet_Mars_house == 0) {
                            $health_score--;
                        }
                        if ($planet_Mars_house == 5 or $last_planet_Mars_house == 5) {
                            $health_score--;
                        }
                    }
                    $allert = [0, 90, 180];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[0]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[0]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[0]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[0]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $health_score--;
                            break;
                        }
                    }
                    $allert = [90, 180];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[5]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[5]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[5]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[5]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $health_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[0]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $life_planet[0]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($life_planet[0]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $life_planet[0]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $health_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[1]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $life_planet[1]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($life_planet[1]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $life_planet[1]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $health_score--;
                            break;
                        }
                    }
                    //判断财运好坏
                    //1、流年金星进入本命第二宫的月份
                    //2、流年金星进入本命第五宫的月份
                    //3、流年金星合相本命第二宫宫主月份
                    //4、流年金星合相本命第五宫宫主月份
                    $fortune_true = false;
                    $fortune_score = 3;
                    if ($planet_Venus_house == 1 or $last_planet_Venus_house == 1) {
                        $fortune_true = true;
                        $fortune_score++;
                    }
                    if ($planet_Venus_house == 4 or $last_planet_Venus_house == 4) {
                        $fortune_true = true;
                        $fortune_score++;
                    }
                    if ($signs_guardian_planet[1]['longitude'] > $Venus_planet_timeFlies['longitude'] and $signs_guardian_planet[1]['longitude'] < $last_Venus_planet_timeFlies['longitude']) {
                        $fortune_true = true;
                    }
                    if ($signs_guardian_planet[4]['longitude'] > $Venus_planet_timeFlies['longitude'] and $signs_guardian_planet[4]['longitude'] < $last_Venus_planet_timeFlies['longitude']) {
                        $fortune_true = true;
                    }

                    if ($planet_Venus_house == 10 or $last_planet_Venus_house == 10) {
                        $fortune_score++;
                    }
                    $allert = [60, 120];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[1]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[1]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[1]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[1]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[4]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[4]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[4]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[4]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[10]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[10]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[10]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[10]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[6]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[6]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[6]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[6]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score++;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] - $values) and $life_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] - $values)) or ($life_planet[5]['longitude'] > ($Venus_planet_timeFlies['longitude'] + $values) and $life_planet[5]['longitude'] < ($last_Venus_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score++;
                            break;
                        }
                    }
                    //当流年火星落入金牛或者天秤，进入本命第2宫的月份-1
                    if (($Mars_planet_timeFlies['sign_id'] == 1 or $last_Mars_planet_timeFlies['sign_id'] == 1) and ($Mars_planet_timeFlies['sign_id'] == 6 or $last_Mars_planet_timeFlies['sign_id'] == 6)) {
                        if ($planet_Mars_house == 1 or $last_planet_Mars_house == 1) {
                            $fortune_score--;
                        }
                        if ($planet_Mars_house == 4 or $last_planet_Mars_house == 4) {
                            $fortune_score--;
                        }
                        if ($planet_Mars_house == 10 or $last_planet_Mars_house == 10) {
                            $fortune_score--;
                        }
                    }
                    $allert = [0, 90, 180];
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[1]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[1]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[1]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[1]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($signs_guardian_planet[4]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $signs_guardian_planet[4]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($signs_guardian_planet[4]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $signs_guardian_planet[4]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[3]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $life_planet[3]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($life_planet[3]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $life_planet[3]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score--;
                            break;
                        }
                    }
                    foreach ($allert as $keys => $values) {
                        if (($life_planet[5]['longitude'] > ($Mars_planet_timeFlies['longitude'] - $values) and $life_planet[5]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] - $values)) or ($life_planet[5]['longitude'] > ($Mars_planet_timeFlies['longitude'] + $values) and $life_planet[5]['longitude'] < ($last_Mars_planet_timeFlies['longitude'] + $values))) {
                            $fortune_score--;
                            break;
                        }
                    }

                    //流年太阳落第一宫$good_array
                    $corpus_TimeFlies_info = array();
                    $shang = false;


                    foreach ($valuet as $keyff => $valueff) {

                        if ($valueff['english_name'] == 'Venus' or $valueff['english_name'] == 'Mercury' or $valueff['english_name'] == 'Sun' or $valueff['english_name'] == 'Mars') {


                            foreach ($life_house as $keyh => $valueh) {
                                if ($keyh < 11) {
                                    $last_house = $life_house[$keyh + 1];
                                } else {
                                    $last_house = $life_house[0];
                                }
                                if ($valueh['longitude'] > $last_house['longitude']) {
                                    if ($valueff['longitude'] < $last_house['longitude']) {
                                        $valueff['longitude'] += 360;
                                    }
                                    $last_house['longitude'] += 360;
                                }
                                if ($valueff['longitude'] >= $valueh['longitude'] and $valueff['longitude'] < $last_house['longitude']) {
                                    $TimeFlies_house = $keyh;
//                                    $house_ban=$last_house['longitude']-$valueh['longitude'];
//
//                                    if(($valueff['longitude']-$valueh['longitude'])<($house_ban/2)){
//                                        $shang=true;
//                                    }
                                }
                            }

                            $fssff = false;
                            foreach ($corpusTimeFliesPalace_info_keyctf_bei as $keyctPalace => $valuetPalace) {
                                if ($valuetPalace['plant'] == $valueff['english_name'] and $valuetPalace['house'] == $TimeFlies_house) {
                                    $fssff = true;
                                    break;
                                }
                            }


//                            if($fssff){
//                                $corpus_TimeFlies_info['love']['shang']='如果你单身，最近桃花机会一般，也许多出去，再让朋友帮帮忙，才有机会。如果你有伴侣，此时你的恋爱关系会很融洽，对方对你关怀备至。';
//                                $corpus_TimeFlies_info['career']['shang']='有机会去参与和工作有机会的技能学习，或者各种实用的技能学习。乐观友善的对待身边的人和事，善于倾听外界建议对你更有好处。一切的成功都是为有准备的人而等待，不忘初心一直努力永远不会错。';
//                                $corpus_TimeFlies_info['fortune']['shang']='近期你工作表现不错，你会结交到年轻的朋友，这部分人会带给你好运气，关键时刻是可以帮助到你的。';
//                                $corpus_TimeFlies_info['health']['shang']='你身体状态一般，这并不是好状态的时候，切记不能有太多过度的行为，记住越自律越自由。';
//
//                                $corpus_TimeFlies_info['love']['xia']='如果你单身，你在与网络、社会大众群体方面会有好的机会。如果你有伴侣，你愿意去分享自己在生活中的点点滴滴，切记不要过度去美化和粉饰关系。';
//                                $corpus_TimeFlies_info['career']['xia']='你自己处理关系的能力此时能够得到最大限度的发挥，目前职场人际关系处理的好的话对你非常有利。';
//                                $corpus_TimeFlies_info['fortune']['xia']='你心态不够稳定，这并不是好状态的时候，健康运势有小幅的回升，身体状况逐渐向好。';
//                                $corpus_TimeFlies_info['health']['xia']='你自己处理关系的能力此时能够得到最大限度的发挥，目前职场人际关系处理的好的话对你非常有利。';
//                                continue;
//                            }

                            if ($shang) {
                                $shang = false;
                            } else {
                                $shang = true;
                            }

                            $corpusTimeFliesPalace_info_keyctf_bei[] = ['plant' => $valueff['english_name'], 'house' => $TimeFlies_house];

                            $corpusTimeFliesPalace_key = '流年' . $planetChinese[$valueff['english_name']] . '落' . $houseChinese[$TimeFlies_house];

                            $corpusTimeFliesPalace_info = $corpusTimeFliesPalace[$corpusTimeFliesPalace_key];
                            $corpus_TimeFlies_file = json_decode($corpusTimeFliesPalace_info['note'], true);

                            foreach ($corpus_TimeFlies_file as $keyctf => $valuetf) {
                                $corpus_TimeFlies_info_lin = '';
                                $corpusTimeFliesPalace_info_keyctf = $corpusTimeFliesPalace_info[$keyctf];


                                if ($love_score >= 4 and $valuetf['field'] == 'love_good') {
                                    $corpus_TimeFlies_info_lin = $good_array[mt_rand(0, 4)] . $corpusTimeFliesPalace_info_keyctf;
                                } else if ($love_score < 0 and $valuetf['field'] == 'love_bad') {
                                    $corpus_TimeFlies_info_lin = $bad_array[mt_rand(0, 4)] . $corpusTimeFliesPalace_info_keyctf;
                                } else if ($love_score < 4 and $love_score >= 0 and $valuetf['field'] == 'love_the') {
                                    $corpus_TimeFlies_info_lin = $the_array[mt_rand(0, 5)] . $corpusTimeFliesPalace_info_keyctf;
                                }

                                if ($career_score >= 4 and $valuetf['field'] == 'career_good') {
                                    $corpus_TimeFlies_info_lin = $good_array[mt_rand(0, 4)] . $corpusTimeFliesPalace_info_keyctf;
                                } else if ($career_score < 0 and $valuetf['field'] == 'career_bad') {
                                    $corpus_TimeFlies_info_lin = $bad_array[mt_rand(0, 4)] . $corpusTimeFliesPalace_info_keyctf;
                                } else if ($career_score < 4 and $career_score >= 0 and $valuetf['field'] == 'career_the') {
                                    $corpus_TimeFlies_info_lin = $the_array[mt_rand(0, 5)] . $corpusTimeFliesPalace_info_keyctf;
                                }

                                if ($health_score >= 4 and $valuetf['field'] == 'health_good') {
                                    $corpus_TimeFlies_info_lin = $good_array[mt_rand(0, 4)] . $corpusTimeFliesPalace_info_keyctf;
                                } else if ($health_score < 0 and $valuetf['field'] == 'health_bad') {
                                    $corpus_TimeFlies_info_lin = $corpusTimeFliesPalace_info_keyctf;
                                } else if ($health_score < 4 and $health_score >= 0 and $valuetf['field'] == 'health_the') {
                                    $corpus_TimeFlies_info_lin = $the_array[mt_rand(0, 5)] . $corpusTimeFliesPalace_info_keyctf;
                                }

                                if ($fortune_score >= 4 and $valuetf['field'] == 'fortune_good') {
                                    $corpus_TimeFlies_info_lin = $good_array[mt_rand(0, 4)] . $corpusTimeFliesPalace_info_keyctf;
                                } else if ($fortune_score < 0 and $valuetf['field'] == 'fortune_bad') {
                                    $corpus_TimeFlies_info_lin = $bad_array[mt_rand(0, 4)] . $corpusTimeFliesPalace_info_keyctf;
                                } else if ($fortune_score < 4 and $fortune_score >= 0 and $valuetf['field'] == 'fortune_the') {
                                    $corpus_TimeFlies_info_lin = $the_array[mt_rand(0, 5)] . $corpusTimeFliesPalace_info_keyctf;
                                }

                                if (!empty($corpus_TimeFlies_info_lin)) {

                                    if (empty($corpus_TimeFlies_info[$dataKey[$valuetf['field']]])) {
                                        $corpus_TimeFlies_info[$dataKey[$valuetf['field']]]['shang'] = '';
                                        $corpus_TimeFlies_info[$dataKey[$valuetf['field']]]['xia'] = '';
                                    }
                                    if ($shang) {
                                        $corpus_TimeFlies_info[$dataKey[$valuetf['field']]]['shang'] = $corpus_TimeFlies_info_lin . '<br/>';
                                    } else {
                                        $corpus_TimeFlies_info[$dataKey[$valuetf['field']]]['xia'] = $corpus_TimeFlies_info_lin . '<br/>';
                                    }
                                }
                            }


                            //流年太阳与本命土星

//                            foreach ($life_planet as $key => $valueli) {
//                                if (in_array($valueli['code_name'], ['0','1','2','3','4','5','6'])) {
//                                    $allert = [60,120];
//
//                                    foreach ($allert as $keys => $values) {
//                                        if (($valueli['longitude'] > ($valueff['longitude'] - $values) and $valueli['longitude'] < ($last_valueff_planet_timeFlies[$key]['longitude'] - $values)) or ($valueli['longitude'] > ($valueff['longitude'] + $values) and $valueli['longitude'] < ($last_valueff_planet_timeFlies[$key]['longitude'] + $values))) {
//                                            $corpusTimeFlies_planet_info=$corpusTimeFliesPalace['流年' . $planetChinese[$valueff['english_name']].'与本命'.$valueli['chinese_name']];
//                                            $corpus_TimeFlies_planet_file = json_decode($corpusTimeFlies_planet_info['note'], true);
//
//                                            foreach ($corpus_TimeFlies_planet_file as $keyctf => $valuetf) {
//                                                if(!empty( $corpus_TimeFlies_info[$dataKey[$valuetf['field']]])){
//                                                    $corpus_TimeFlies_info[$dataKey[$valuetf['field']]].=$corpusTimeFlies_planet_info[$keyctf];
//                                                }
//                                            }
//                                            break;
//                                        }
//                                    }
//                                }
//                            }

                            $corpus_TimeFlies_info['corpusTimeFliesPalace_key'] = $corpusTimeFliesPalace_key;
                        }

                    }

                    if ($love_score >= 6) {
                        $corpus_TimeFlies_info['love_score'] = 5;
                    } else if ($love_score < 6 and $love_score >= 4) {
                        $corpus_TimeFlies_info['love_score'] = 4;
                    } else if ($love_score < 4 and $love_score >= 2) {
                        $corpus_TimeFlies_info['love_score'] = 3;
                    } else if ($love_score < 2 and $love_score >= 0) {
                        $corpus_TimeFlies_info['love_score'] = 2;
                    } else {
                        $corpus_TimeFlies_info['love_score'] = 1;
                    }

                    if ($career_score >= 6) {
                        $corpus_TimeFlies_info['career_score'] = 5;
                    } else if ($career_score < 6 and $career_score >= 4) {
                        $corpus_TimeFlies_info['career_score'] = 4;
                    } else if ($career_score < 4 and $career_score >= 2) {
                        $corpus_TimeFlies_info['career_score'] = 3;
                    } else if ($career_score < 2 and $career_score >= 0) {
                        $corpus_TimeFlies_info['career_score'] = 2;
                    } else {
                        $corpus_TimeFlies_info['career_score'] = 1;
                    }

                    if ($health_score >= 6) {
                        $corpus_TimeFlies_info['health_score'] = 5;
                    } else if ($health_score < 6 and $health_score >= 4) {
                        $corpus_TimeFlies_info['health_score'] = 4;
                    } else if ($health_score < 4 and $health_score >= 2) {
                        $corpus_TimeFlies_info['health_score'] = 3;
                    } else if ($health_score < 2 and $health_score >= 0) {
                        $corpus_TimeFlies_info['health_score'] = 2;
                    } else {
                        $corpus_TimeFlies_info['health_score'] = 1;
                    }
                    if ($fortune_score >= 6) {
                        $corpus_TimeFlies_info['fortune_score'] = 5;
                    } else if ($fortune_score < 6 and $fortune_score >= 4) {
                        $corpus_TimeFlies_info['fortune_score'] = 4;
                    } else if ($fortune_score < 4 and $fortune_score >= 2) {
                        $corpus_TimeFlies_info['fortune_score'] = 3;
                    } else if ($fortune_score < 2 and $fortune_score >= 0) {
                        $corpus_TimeFlies_info['fortune_score'] = 2;
                    } else {
                        $corpus_TimeFlies_info['fortune_score'] = 1;
                    }
                    $data['have_month'][] = $corpus_TimeFlies_info;
                }
            }

            //土木交互点
            $tu_mu_date = '**********';
            $tu_mu_longitude['longitude'] = '90';
            $tu_mu_house = $this->housePlanet($tu_mu_longitude, $life_house) + 1;
            $corpusInfo = $this->logicCorpus->getCorpusInfo(['title' => $tu_mu_house, 'attribution_str' => '土木交点2025'], true, 'title');
            $corpus_tu_mu_info['content'] = $corpusInfo['content1'];
            $data['tu_mu_points'] = $corpus_tu_mu_info;

            //生肖

            $animals = array('鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪');


            $lunar = new Lunar();

            $solarToLunar_month = $lunar->convertSolarToLunar($years, $month, $day);


            $animals_name = $solarToLunar_month[6];

            $animals_key = array_search($animals_name, $animals);

            $corpusInfo = $this->logicCorpus->getCorpusInfo(['title' => $animals_name, 'attribution_str' => '生肖2025'], true, 'title');


            $corpus_animal_file = json_decode($corpusInfo['note'], true);
            $corpus_animal_info['animals_id'] = $animals_key;
            $corpus_animal_info['animals_name'] = $animals_name;

            foreach ($corpus_animal_file as $keycr => $valuecr) {
                $corpus_animal_info_lin = $corpusInfo[$keycr];

                if (strstr($corpus_animal_info_lin, '[天干]') !== false) {

                    $corpus_animal_info_lin = str_replace('[天干]', $solarToLunar_month[3], $corpus_animal_info_lin);
                }

                $corpus_animal_info[$valuecr['field']] = $corpus_animal_info_lin;

            }
            $data['animal'] = $corpus_animal_info;

        } else {

            $time = 60 * 60 * 2 + strtotime($activityTransport25Info['create_time']);
            $data['time'] = $time;
        }

        return $this->apiReturn($data);
    }


    /**
     * 计算宫内获得行星
     */
    public function housePlanet($planet = [], $house = [])
    {
        $valuep = $planet;
        foreach ($house as $keyh => $valueh) {
            if ($keyh < 11) {
                $last_house = $house[$keyh + 1];
            } else {
                $last_house = $house[0];
            }

            if ($valueh['longitude'] > $last_house['longitude']) {

                if ($valuep['longitude'] < $last_house['longitude']) {
                    $valuep['longitude'] += 360;
                }

                $last_house['longitude'] += 360;
            }

            if ($valuep['longitude'] >= $valueh['longitude'] and $valuep['longitude'] < $last_house['longitude']) {

                return $keyh;
            }
        }
    }

    /**
     * 年运测评列表
     */
    public function activityTransport25List()
    {

        $whereTransport['channel'] = 1;

        !empty($this->param['channel']) && $whereTransport['channel'] = $this->param['channel'];

        $data = $this->logicActivityTransport25->getActivityTransport25List($whereTransport, 'id,channel,order_number,openid,name,sex,amount,birthday,update_time,create_time,status');

        return $this->apiReturn($data);
    }


    /**
     * 年运测评简单详细
     */
    public function activityTransport25Simple()
    {

        $whereTransport = array();

        !empty($this->param['channel']) && $whereTransport['channel'] = $this->param['channel'];

        !empty($this->param['id']) && $whereTransport['id'] = $this->param['id'];

        !empty($this->param['openid']) && $whereTransport['openid'] = $this->param['openid'];

        !empty($this->param['order_number']) && $whereTransport['order_number'] = $this->param['order_number'];

        $data = $this->logicActivityTransport25->getActivityTransport25Info($whereTransport, 'id,channel,order_number,openid,name,sex,amount,birthday,update_time,create_time,status');

        return $this->apiReturn($data);
    }


    /**
     * 年运测评无分页列表
     */
    public function activityTransport25Column()
    {


        !empty($this->param['openid']) && $whereTransport['openid'] = $this->param['openid'];

        !empty($this->param['order_number']) && $whereTransport['order_number'] = $this->param['order_number'];

        if (empty($whereTransport)) {
            return $this->apiReturn(['code' => 99666, 'msg' => '订单查询用户id或订单号不能为空']);
        }

        $data = $this->logicActivityTransport25->getActivityTransport25List($whereTransport, 'id,name,order_number,create_time,sex,birth_district,birthday,status', 'create_time desc', false);

        return $this->apiReturn($data);
    }

    /**
     * 年运测评添加
     */
    public function activityTransport25Add()
    {

        if(empty($this->param_data['birthday'])){

            return $this->apiReturn([API_CODE_NAME => 1010008, API_MSG_NAME => '出生日期必须选择']);
        }

        $regit = $this->logicActivityTransport25->activityTransport25Add($this->param_data);

        return $this->apiReturn(['id' => $regit]);

    }
    /**
     * 年运测评添加
     */
    public function activityTransport25Edit()
    {

        $where['order_number'] = $this->param_data['order_number'];

        $regit = $this->logicActivityTransport25->getActivityTransport25Info($where);

        if (empty($regit)) {
            return $this->apiReturn([API_CODE_NAME => 1010007, API_MSG_NAME => '您没有该订单']);
        }
        $data['id']=$regit['id'];
        !empty($this->param_data['mobile']) && $data['mobile'] = $this->param_data['mobile'];


        $this->logicActivityTransport25->activityTransport25Edit($data);


        return $this->apiReturn($data);

    }


    /**
     * 年运测评发送
     */
    public function activityTransporSend()
    {
        $param = $this->param_data;
        $activityTransport25Info = $this->logicActivityTransport25->getActivityTransport25Info(['order_number' => $param['order_number'], 'status' => 1]);

        if (empty($activityTransport25Info)) {
            return $this->apiReturn([RESULT_ERROR, '没查到您的计算数据哦']);
        }


        $channel_name = '罗博科技';
        if (!empty($activityTransport25Info['channel'])) {
            $channel = $this->logicChannelList->getChannelListInfo(['code' => $activityTransport25Info['channel']]);
            if (!empty($channel)) {
                $channel_name = $channel['name'];
            }
        }


        if ($param['type'] == 1) {
            $parameter['sign_name'] = '罗博国际';
            $parameter['template_code'] = 'SMS_205448674';
            $parameter['phone_number'] = $param['account'];
            $parameter['template_param'] = ['mtname' => $param['order_number'], 'submittime' => 'transpor25'];
            $this->logicApiBase->getSmsCode($parameter);
        } else {

            if (!filter_var($param['account'], FILTER_VALIDATE_EMAIL)) {
                return $this->apiReturn([RESULT_ERROR, '邮件格式不正确']);
            }

            if (!preg_match("/^[a-zA-Z0-9@.]*$/", $param['account'])) {
                return $this->apiReturn([RESULT_ERROR, '邮件地址里字符不正确']);
            }



            $content_str = '感谢您对' . $channel_name . '的肯定，这是您的订单信息：<br/>
服务内容：2025年个人星盘年运报告<br/>
订单号：' . $param['order_number'] . '<br/>
<a href="https://xg.robustcn.com/year25/#/pages/query/query">【查看测算结果的链接】</a><br/><br/><br/><br/>';
            $content_str .= 'Thank you for ' . $channel_name . '  here is your order information:
Service content: 2025 annual personal astrolabe report <br/>
Number: ' . $param['order_number'] . '<br/>
<a href="https://xg.robustcn.com/year25/#/pages/query/query">[Link to view calculation results]</a>';

            send_email($param['account'], '2025年个人星盘年运报告(2025 Personal Astrology Report)', $content_str, $channel_name);
        }

        return $this->apiReturn(['id' => true]);

    }


    /**
     * 年运测评删除
     */
    public function activityTransport25Del($id)
    {

        $regit = $this->logicActivityTransport25->activityTransport25Del(['id' => $id]);

        return $this->apiReturn(['id' => $regit]);

    }
}
