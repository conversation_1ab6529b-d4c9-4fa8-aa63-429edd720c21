<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\appraise\controller;

/**
 * 抽签语料控制器
 */
class Activitysortitioncorpus extends UserBase
{

    /**
     * 抽签语料列表
     */
    public function activitySortitionCorpusList()
    {

        $where = $this->logicActivitySortitionCorpus->getWhere($this->param_data);

        $data=$this->logicActivitySortitionCorpus->getActivitySortitionCorpusList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 抽签语料无分页列表
     */
    public function activitySortitionCorpusColumn()
    {

        $data=$this->logicActivitySortitionCorpus->getActivitySortitionCorpusColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 抽签语料添加
     */
    public function activitySortitionCorpusAdd()
    {
	  
	   $regit=$this->logicActivitySortitionCorpus->activitySortitionCorpusEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 抽签语料删除
     */
    public function activitySortitionCorpusDel()
    {

       $regit=$this->logicActivitySortitionCorpus->activitySortitionCorpusDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
