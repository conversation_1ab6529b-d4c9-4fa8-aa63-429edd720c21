<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;

/**
 * 基础配置控制器
 */
class Programconfig extends UserBase
{

    /**
     * 基础配置列表
     */
    public function programConfigList()
    {

        $where = $this->logicProgramConfig->getWhere($this->param_data);

        $data=$this->logicProgramConfig->getProgramConfigList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 基础配置无分页列表
     */
    public function programConfigColumn()
    {
        unset($this->param_data['user_id']);
        unset($this->param_data['choose_appid']);
        $data=$this->logicProgramConfig->getProgramConfigColumn($this->param_data,'*','name');

		return $this->apiReturn($data);
    }
    /**
     * 基础配置添加
     */
    public function programConfigAdd()
    {
	  
	   $regit=$this->logicProgramConfig->programConfigEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 基础配置删除
     */
    public function programConfigDel()
    {

       $regit=$this->logicProgramConfig->programConfigDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
