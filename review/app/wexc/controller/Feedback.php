<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;

/**
 * 用户反馈控制器
 */
class Feedback extends WexcBase
{

    /**
     * 用户反馈列表
     */
    public function feedbackList()
    {

        $where = $this->logicFeedback->getWhere($this->param_data);

        $data=$this->logicFeedback->getFeedbackList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 用户反馈无分页列表
     */
    public function feedbackColumn()
    {

        $data=$this->logicFeedback->getFeedbackColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 用户反馈添加
     */
    public function feedbackAdd()
    {
	  
	   $regit=$this->logicFeedback->feedbackEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 用户反馈删除
     */
    public function feedbackDel()
    {

       $regit=$this->logicFeedback->feedbackDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
