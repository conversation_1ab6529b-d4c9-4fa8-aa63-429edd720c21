<div id="project">
    <div class="pull-left">
      <h2>{$info.name}</h2>
    </div>
    <div class="pull-right">
    <div class="btn-group">
      <button id="version" class="btn btn-large dropdown-toggle" data-toggle="dropdown">
            接口状态：{$info.api_status_text}
      </button>

    </div>
    </div>
  <div class="clearfix"></div>
</div>
{notempty name="info['describe']"}
<div id="header">
    <p>{$info.describe}</p>
</div>
{/notempty}
<article>
    <div class="pull-left">
      <h2>接口请求地址</h2>
    </div>
    <br/>
    <pre class="prettyprint language-html prettyprinted" data-type="{$info.request_type_text}" style="">{$info.api_url}</pre>

{notempty name="info['request_data']"}

    <h2>请求参数</h2>
    <table>
      <thead>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>必填</th>
          <th>描述</th>
        </tr>
      </thead>
      <tbody>

          {volist name="info['request_data']" id='vo'}
              <tr>
                  <td>{$vo['field_name']}</td>
                  <td>{$vo['data_type']}</td>
                  <td>{$vo['is_require']}</td>
                  <td><p>{$vo['field_describe']}</p></td>
              </tr>
        {/volist}
      </tbody>
    </table>

{/notempty}

{notempty name="info['response_data']"}
    <h2>响应字段</h2>
    <table>
      <thead>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
      </thead>
      <tbody>

            {volist name="info['response_data']" id='vo'}
              <tr>
                  <td>{$vo['field_name']}</td>
                  <td>{$vo['data_type']}</td>
                  <td><p>{$vo['field_describe']}</p></td>
              </tr>
            {/volist}
      </tbody>
    </table>

{/notempty}

{notempty name="info['response_examples']"}

    <ul class="nav nav-tabs nav-tabs-examples">
        <li class="active">
          <a>响应数据样例</a>
        </li>
    </ul>

    <div class="tab-content">
      <div class="tab-pane active" id="error-examples-User-GetUser-0_3_0-0">
        <pre class="prettyprint language-json prettyprinted" data-type="json" style=""><code>{$info['response_examples']|html_entity_decode}</code></pre>
      </div>
    </div>

{/notempty}



{notempty name="info['describe_text']"}
    <ul class="nav nav-tabs nav-tabs-examples">
        <li class="active">
          <a>详细文本描述</a>
        </li>
    </ul>
    <br/>
    <div>
        {$info.describe_text|html_entity_decode}
    </div>
    <hr/>
{/notempty}

<h2>测试接口</h2>

测试Access_Token：{$test_access_token}
<br/><br/>

<form class="form-horizontal" action="{:url($info['api_url'])}" method="{$info['request_type_text']}" enctype="multipart/form-data">


    <div class="User-GetUser-0_3_0-sample-request-param-fields">

        {volist name="info['request_data']" id='vo'}
            <div class="control-group">
                <label>{$vo.field_name}（{$vo.field_describe}） {eq name="vo['is_require']" value='是'}<span style="color: red;"> * </span>{/eq}</label>
                  <div class="input-append">
                        {switch name="vo['data_type']"}
                            {case value="字符"}
                                <input name="{$vo.field_name}" placeholder="请输入{$vo.field_name}" class="input-xxlarge sample-request-param" type="text">
                            {/case}
                            {case value="文本"}
                                <input name="{$vo.field_name}" placeholder="请输入{$vo.field_name}" class="input-xxlarge sample-request-param" type="text">
                            {/case}
                            {case value="数组"}
                                <input name="{$vo.field_name}[]" placeholder="请输入{$vo.field_name}" class="input-xxlarge sample-request-param" type="text">
                                <button class="btn btn-default" onclick="addField(this)" type="button">新增一项</button>
                            {/case}
                            {case value="文件"}
                            <input style="border: 1px solid #CCC;  height: 28px;" name="{$vo.field_name}" placeholder="请输入{$vo.field_name}" class="input-xxlarge sample-request-param" type="file">
                            {/case}
                            {case value="数字"}
                                   <input name="{$vo.field_name}" placeholder="请输入{$vo.field_name}" class="input-xxlarge sample-request-param" type="number">
                            {/case}
                        {/switch}

                        <span class="add-on">{$vo.data_type}</span>
                  </div>
            </div>
        {/volist}

    </div>
    <hr/>
    <button type="submit" class="btn btn-default sample-request-send">提交数据</button>

</form>

<div id="api_response" style="display: none;">
    <ul class="nav nav-tabs nav-tabs-examples">
        <li class="active">
          <a>测试接口响应数据</a>
        </li>
    </ul>



    <div class="tab-content">
      <div class="tab-pane active" id="error-examples-User-GetUser-0_3_0-0">
        <pre class="prettyprint language-json prettyprinted api_response_pre" data-type="json" style=""></pre>
      </div>
    </div>
</div>
</article>


<div id="generator">
      <div class="content">
        Copyright ©2018 <a href="http://wechat.feixiaoguai.com/">ThinkTree</a>
      </div>
</div>

<script type="text/javascript" src="__STATIC__/module/api/js/api.js"></script>