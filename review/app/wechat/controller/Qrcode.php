<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wechat\controller;


/**
 * 场景二维码控制器
 */
class Qrcode extends WechatBase
{

    /**
     * 场景二维码列表
     */
    public function weiQrcodeList()
    {
        $where['choose_appid']=session('member_info')['choose_appid'];

        $qrcodeListData=$this->logicWeiQrcode->getWeiQrcodeList($where, true, '');

        $this->assign('list',$qrcodeListData);

        return $this->fetch('weiqrcode_list');
    }

    /**
     * 菜单线上更新
     */
    public function weiQrcodeDelete()
    {
        $ret = self::delete();
        echo '<pre>';
        print_r($ret);
        exit('</pre>');
    }

    /**
     * 场景二维码删除
     */
    public function weiQrcodeDel($id = 0)
    {
        
        return $this->jump($this->logicWeiQrcode->weiQrcodeDel(['id' => $id]));
    }
    /**
     * 场景二维码新增
     */
    public function weiQrcodeAdd($data)
    {
        if(IS_POST){


            $data['choose_appid']=session('member_info')['choose_appid'];
            $this->logicWeiQrcode->weiQrcodeAdd($data);
        }

        return $this->fetch('weiqrcode_edit');
        //return $this->jump($this->logicWeiMenu->memberDel(['id' => $id]));
    }
}
