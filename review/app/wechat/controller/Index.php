<?php
namespace app\wechat\controller;
use exwechat\exRequest;
use exwechat\exWechat;


class Index extends WechatBase
{
    // 微信消息对象
    // 数组消息体 － 微信消息对象的局部信息
    private $_msg;

    /**
     * 微信消息入口
     *
     * <AUTHOR>
     */
    public function index()
    {
        $appid_token=$this->request->param();

        //exLog::log($_GET, 'get');
//        exLog::log(file_get_contents("php://input"), 'post');
//        exLog::log($appid_token, 'appid_token');
        // 微信验证控制器
        $exwechat = get_sington_object('exWechatEx', exWechat::class,$appid_token['token']);
        // 接口配置 和 签名验证

        $ret = $exwechat->authentication();

        if(is_bool($ret)){
            if(!$ret){
                exit('签名验证失败');
            }
        }else{ //接口配置  开发者模式接入
            exit($ret);
        }

        $this->exRequest = exRequest::instance();
        $ToUserName = $this->exRequest->getToUserName();

        $conf = getAppidToken($ToUserName);

        //session('appid_conf', $conf);
        // 根据ToUserName获取 appid, token等对应信息
        //$conf = new WechatConfig($ToUserName);
        $config['appid'] = $conf['appid'];
        $config['token'] = $conf['token'];
        $config['encodingAesKey'] = $conf['encoding_aes_key'];

        // $encryptType = $conf->encryptType;
        $encryptType = 2;
        // 提取微信消息

        $this->exRequest->extractMsg($encryptType, $config, false);

        if($this->exRequest->errorCode){
            exit($this->exRequest->errorMsg);
        }
        // 获取用户发来的消息 － 数组格式
        $this->_msg = $this->exRequest->getMsg();

        // 微信消息分类处理
        $this->_msgTypeHandle();
    }

    /**
     * 微信消息分类处理
     * 消息分类控制器接管后续操作
     * <AUTHOR>
     */
    public function _msgTypeHandle()
    {
        $app_root=explode(SYS_DS_CONS, get_class($this));
        //exLog::log($app_root, 'get');
        array_pop($app_root);

        switch ($this->_msg['MsgType']) {
            case 'event':
            case 'text':
            case 'image':
            case 'location':
            case 'link':
            case 'video':
            case 'voice':
                 $cls = get_sington_object('controller'.ucfirst($this->_msg['MsgType']), implode(SYS_DS_CONS,$app_root).SYS_DS_CONS.'Handle'.ucfirst($this->_msg['MsgType']));
                 $cls->handle($this->_msg);
                 break;
            default:
                $cls = get_sington_object('controllerHandleDefault',implode(SYS_DS_CONS,$app_root). SYS_DS_CONS.'HandleDefault');
                $cls->handle($this->_msg);
        }
    }
}
