<?php

namespace app\wechat\controller;
use exwechat\api\user\user;
/**
 * 微信事件控制器
 *
 */
class HandleEvent extends AbstractHandle
{
    public function handle($arrayMsg = '')
    {
        $this->msg = empty($arrayMsg) ? $this->exRequest->getMsg() : $arrayMsg;

        $app_root = explode(SYS_DS_CONS, get_class($this));
        array_pop($app_root);
        $app_root=implode(SYS_DS_CONS, $app_root). SYS_DS_CONS ;

        if ('LOCATION' == $this->msg['Event'] || 'location_select' == $this->msg['Event']) {
        } else {
            $this->_saveToDB($this->msg);
            // 用户场景捕获
            // = get_sington_object('sceneCatchLogic', LogicSceneCatch::class);  //微场景事件
            $scene = 'yes';
            //$scene = $SceneCatch->getScene($this->msg['FromUserName'], 'location');
        }
        $message=array();
        switch ($this->msg['Event']) {
            // 关注公众号
            case 'subscribe':
                $this->_saveFromUserToDB($this->msg);
                $cls = get_sington_object('controllerHandleWelcome',$app_root . 'HandleWelcome');
                $message= $cls->handle($this->msg);
                //exLog::log(json_encode($message), 'get');
                //优先级，自动回复先
                if ($message) {
                } else {
                    $message['text'] = '关注扫码行为: 二维码内容是： ' . json_encode($message);
                }
                break;
            // 取消关注公众号
            case 'unsubscribe':
                $message['text'] = '取消关注中: ';
                break;
            // 扫描带参数二维码事件
            case 'SCAN':
                //$message['text'] = '已经关注扫码行为: 二维码内容是： ' . $this->msg['EventKey'];

                $message=$this->_evenScan();
                break;
            // 自定义菜单事件
            case 'CLICK':

                $cls = get_sington_object('controllerHandleClick', $app_root. 'HandleClick');
                $message = $cls->handle($this->msg);

                break;
            // 模板消息发送成功通知
            case 'TEMPLATESENDJOBFINISH':
                break;
            // 菜单跳转链接
            case 'VIEW':

                break;
            // 扫码推事件的事件推送
            case 'scancode_push':
                $message['text'] = '扫码行为: 二维码内容是： ' . $this->msg['ScanCodeInfo']['ScanResult'];
                break;
            // 扫码推事件且弹出“消息接收中”提示框的事件推送
            case 'scancode_waitmsg':
                $message['text'] = '扫码行为: 二维码内容是： ' . $this->msg['ScanCodeInfo']['ScanResult'];
                break;
            // 弹出系统拍照发图的事件推送
            case 'pic_sysphoto':
                break;
            // 弹出拍照或者相册发图的事件推送
            case 'pic_photo_or_album':
                break;
            // 弹出微信相册发图器的事件推送
            case 'pic_weixin':
                break;
            // 上报地理位置事件
            case 'LOCATION':
                $cls = get_sington_object('controllerHandleLocation', $app_root. 'HandleLocation');
                $ret = $cls->saveToDB($this->msg);

                if ($scene == 'yes') {
                    $location = "微信上报个人位置LOCATION\n";
                    $location .= 'Latitude:' . $this->msg['Latitude'] . "\n";
                    $location .= 'Longitude:' . $this->msg['Longitude'] . "\n";
                    $location .= 'Precision:' . $this->msg['Precision'] . "\n";
                }
                $message['text'] = $location;
                break;
            // 菜单弹出地理位置选择器的事件推送
            case 'location_select':
                $cls = get_sington_object('controllerHandleLocation', $app_root. 'HandleLocation');
                $ret = $cls->saveToDB($this->msg);
                if ($scene == 'yes') {
                    $location = "上传个人位置\n";
                    $location .= 'Location_X:' . $this->msg['SendLocationInfo']['Location_X'] . "\n";
                    $location .= 'Location_Y:' . $this->msg['SendLocationInfo']['Location_Y'] . "\n";
                    $location .= 'Scale:' . $this->msg['SendLocationInfo']['Scale'] . "\n";
                    $location .= 'Label:' . $this->msg['SendLocationInfo']['Label'] . "\n";
                    $location .= 'Poiname:' . $this->msg['SendLocationInfo']['Poiname'] . "\n";
                }
                $message['text'] = $location;
                break;
            default:
                $message['text'] = '这个类型事件还没开发呢！-' . $this->msg['Event'];
        }

        foreach ($message as $key=>$value){
            $this->response($value, $key, $data = [], $this->msg);
        }
        exit(); //阻止DEBUG信息输出
    }

    // 对应数据库为 msg_text
    private function _saveToDB($msg = '')
    {
        foreach ($msg as $key => $value) {
            if (in_array($key, ['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Event', 'EventKey'])) {
                $data[$key] = $value;
                unset($msg[$key]);
            }
        }
        $data['other'] = empty($msg) ? '' : json_encode($msg);
        if (isset($data['Encrypt'])) unset($data['Encrypt']);

        $this->logicWeiEventLog->weiEventWebAdd($data);

        $ret = json_encode($data);
        //$ret = db('we_msg_event')->insert($data);
        return $ret;
    }
    /**
     * @param string $data 保存用户信息
     */
    private function _saveFromUserToDB($data = '')
    {
        $userdata['openid'] = $data['FromUserName'];
        $userdata['openid_p'] = $data['EventKey'];
        $userdata['choose_appid'] =getAppidToken($data['ToUserName'])['id'];

        $infoData = $this->logicWeiUser->getWeiUserInfo(['openid' => $userdata['openid']], 'id,count');
        if ($infoData) {
            $up_data['id'] = $infoData['id'];
            $up_data['count'] = $infoData['count'] + 1;
            $this->logicWeiUser->weiUserWebEdit($up_data);
        } else {
            $OAuth = get_sington_object('userEx', user::class,getAppidToken($data['ToUserName'],true)['access_token']);
            $ret = $OAuth->getUserInfo($userdata['openid']);
            $userdata['nickname']=filterEmoji($ret['nickname']);
            $userdata['sex']=$ret['sex'];
            $userdata['subscribe']=$ret['subscribe'];
            $userdata['city']=$ret['city'];
            $userdata['country']=$ret['country'];
            $userdata['province']=$ret['province'];
            $userdata['language']=$ret['language'];
            $userdata['headimgurl']=$ret['headimgurl'];
            if(!empty($ret['unionid'])){
                $userdata['unionid'] = $ret['unionid'];
            }
            $headimgurl='./upload/headimg/'.$userdata['openid'].'.png';

            $this->logicWeiUser->weiUserWebAdd($userdata);
            if(empty($infoData['headimgurl'])){
                downFile($userdata['headimgurl'], $headimgurl);
            }

            $this->msg['EventKey']=ltrim($this->msg['EventKey'],"qrscene_");
            //exLog::log(json_encode(['scene_str' => $this->msg['EventKey']]), 'get');


            //活动生成
//            $WeiActivity = $this->logicWeiActivity->getWeiActivityInfo(['event' =>'PromoteQrcode','status' =>'1','choose_appid' =>$userdata['choose_appid']], 'update_time,template_id,config_json');
//
//            if(!empty($WeiActivity)){
//                $WeiActivity['config_json']=json_decode($WeiActivity['config_json'],true);
//                $result=$this->logicWeiQrcode->getWeiQrcodeInfo(['scene_str' => $this->msg['EventKey'],'create_time'=>['GT',$WeiActivity['update_time']]],'gz_count');
//                $gz_count=0;
//                //exLog::log(json_encode($result), 'get');
//
//                if($result){
//                    $result=$result->toArray();
//                    $gz_count=$result['gz_count']+1;
//                    $this->logicWeiQrcode->updateWeiQrcode(['scene_str' => $this->msg['EventKey'],'id'=>['GT',4]], ['gz_count'=>$gz_count]);
//                }
//                if($gz_count>=$WeiActivity['config_json']['number']){
//                    $logicWechatMessageSend=get_sington_object('logicWechatMessageSend', "app\\common\\logic\\WechatMessageSend",getAppidToken($this->msg['ToUserName'],true)['access_token']);
//
//                    $logicWechatMessageSend->templateSend($this->msg['EventKey'],$gz_count,$WeiActivity['config_json']['template_id']);
//                }
//            }

//            if($gz_count>=3){
//                $logicWechatMessageSend->templateFivSend($this->msg['EventKey']);
                // $logicWechatMessageSend->templateSend($this->msg['EventKey'],$gz_count);
//            }else{
//                $logicWechatMessageSend->templateSend($this->msg['EventKey'],$gz_count);
//            }

        }
    }
    //扫码事件执行
    private function _evenScan()
    {

       $this->logicWeiQrcode->setInc(['scene_str' => $this->msg['EventKey'],'create_time'=>['gt',1539975872]], 'scan_count', 1, 'setInc');

        $app_root = explode(SYS_DS_CONS, get_class($this));
        array_pop($app_root);
        $app_root=implode(SYS_DS_CONS, $app_root). SYS_DS_CONS ;
        $cls = get_sington_object('controllerHandleWelcome',$app_root . 'HandleWelcome');
        $message= $cls->handle($this->msg);
        return $message;
    }
}
