<?php

namespace app\wechat\controller;
use exwechat\exLog;

class <PERSON>leClick extends WechatBase
{
    public function handle($arrayMsg='')
    {

        $this->msg = empty($arrayMsg) ? $this->exRequest->getMsg() : $arrayMsg;
        // 数据库关键词
        $ret= $this->_dbKeyword($this->msg);

        return $ret;
        exit(); //阻止DEBUG信息输出
    }
    // 数据库中的关键字
    private function _dbKeyword($data)
    {
        $where['key_wrod']=$data['EventKey'];
        $where['status']=1;
        $where['choose_appid']=getAppidToken($data['ToUserName'])['id'];
        $where['FromUserName']=$data['FromUserName'];
        $where['ToUserName']=$data['ToUserName'];
        return $this->logicWeiReply->wechatReplyMaterialGet($where);
    }

}