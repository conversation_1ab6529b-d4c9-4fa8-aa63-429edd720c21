<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wechat\logic;
use app\common\logic\LogicBase;
use exwechat\api\account\QRCode;
/**
 * 拉取二维码操作逻辑业务层
 */
class PromoteQrcode extends LogicBase
{
    /**
     * 获取微信appid配置列表搜索条件
     */
    public function getWhere($data = [])
    {

        return $data;
    }
    /**
     * 推广二维码生成
     */
    public function PromoteQrcodeEvent($data = [])
    {
        $data['scene_name']=$data['ToUserName'];
        $data['keyword']='我要推广';
        $data['action_name']='QR_STR_SCENE';
        $data['expire']='2592000';
        $data['scene_str']=$data['FromUserName'];

        if($result=$this->modelWeiQrcode->getInfo(['scene_name'=>$data['scene_name'],'scene_str'=>$data['scene_str'],'create_time'=>['gt',time()-$data['expire']]])){
            $result_data=$result->toArray();
            $result_data['ToUserName']=$result_data["scene_name"];
            $result_data['FromUserName']=$result_data["scene_str"];
        }else{

            //获取后台设置二维码参数数据
            $WeiActivity = $this->modelWeiActivity->getInfo(['event' =>'PromoteQrcode'], 'update_time,template_id,config_json')->toarray();
            $WeiActivity['config_json']=json_decode($WeiActivity['config_json'],true);

            //生成自己的二维码
            $QRCodeEx = get_sington_object('QRCodeEx', QRCode::class,getAppidToken($data['ToUserName'],true)['access_token']);
            $Qr_data=$QRCodeEx->permanentQR($data['action_name'],$data['scene_str'],$data['expire']);
            $data['ticket']=$Qr_data['ticket'];
            $data['url']=$Qr_data['url'];

            $qrcode_img=create_qrcode($data['url'], './upload/qrcode/' , $ecc = 'H', $size =4);

            $data['qrcode_url']=substr($qrcode_img['path'],1);

            $data['local_url']=$qrcode_img['path'];

            $final_url='./upload/promote/'.$data['FromUserName'].'.png'; //图像二维码
            //图像的水印
            $headimgurl='./upload/headimg/'.$data['FromUserName'].'.png';

            $image_bg = \think\Image::open('./upload/'.$WeiActivity['config_json']['under_src']);
            $loc_headimg=array($WeiActivity['config_json']['oneself_left'],$WeiActivity['config_json']['oneself_top']);
            $image_bg->water($headimgurl,$loc_headimg)->save($final_url);

            //二维码的水印
            $image = \think\Image::open($final_url);
            $loc=array($WeiActivity['config_json']['wx_left'],$WeiActivity['config_json']['wx_top']);
            $image->water($data['local_url'],$loc)->save($final_url);

            $data['local_url']=$final_url;
            $data['event']='PromoteQrcode';

            $result_data=$data;
            unset($data['ToUserName']);
            unset($data['FromUserName']);
            $this->modelWeiQrcode->setInfo($data);
        }
        $result_data['event']='PromoteQrcode';
        $wechatMediaInfo=get_sington_object('logicWeiMedia', "app\\common\\logic\\WeiMedia",getAppidToken($result_data['ToUserName'],true)['access_token'])->wechatMediaAdd($result_data);
        $wechatMedia[$wechatMediaInfo['type']]=$wechatMediaInfo['media_id'];
        return $wechatMedia;
    }

    /**
     * 推广二维码解析
     */
    public function weiQrcodeParsing($data = [])
    {


    }
}
