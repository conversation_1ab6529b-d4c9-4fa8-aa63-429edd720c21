<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | authorization   | http://www.apache.org/licenses/LICENSE-2.0 )           |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiwap\controller;
use exwechat\api\OAuth\OAuth as OAuths;

class Oauth  extends Common
{

    // 首页
    public function index()
    {
//        $this->assign('list',$WeiMovieList);
        // 关闭布局
        $this->view->engine->layout(false);
        session('ToUserName', 'wxd6cb460abb63532a');
        return $this->fetch('index');
    }

    // 首页
    public function login()
    {
//        $this->assign('list',$WeiMovieList);
        // 关闭布局
        $this->view->engine->layout(false);
        return $this->fetch('index');
    }

    //应用授权作用域，snsapi_base （不弹出授权页面，直接跳转，只能获取用户openid）
    public function snsapi_base()
    {

        $redirect_uri = url('callback_base','', false, true);
        $scope = 'snsapi_base';
        $state = '123';
        $redirect_uri = urlencode($redirect_uri);
        $OAuth = new OAuths($this->appid, $this->secret);
        $url = $OAuth->getCodeUrl($redirect_uri, $scope, $state);
        echo '<pre>';
        print_r( $url );
        header('Location: '.$url);
        exit();
    }
    //（弹出授权页面，可通过openid拿到昵称、性别、所在地。并且， 即使在未关注的情况下，只要用户授权，也能获取其信息 ）
    public function snsapi_userinfo()
    {
        if(empty($this->param['wechat'])){
            $this->jump(RESULT_ERROR, '非法载入',url('index'));
            exit();
        }
        $tokens=getAppidToken($this->param['wechat']);
        $this->tokens = $tokens;
        $redirect_uri = url('callback_userinfo','', false, true);
        $scope = 'snsapi_userinfo';
        $state = '123';
        $redirect_uri = urlencode($redirect_uri);
        $OAuth = new OAuths($tokens['appid'], $tokens['secret']);
        $url = $OAuth->getCodeUrl($redirect_uri, $scope, $state);
        $this->redirect($url);
    }



    public function callback_base()
    {
        $tokens=getAppidToken(session('ToUserName'));
        $OAuth = new OAuths($tokens['appid'], $tokens['secret']);
        $ret = $OAuth->getToken($_GET['code']);
        if(isset($ret['errcode'])){
            echo '<pre>';

            exit('</pre>');
        }
        $this->_saveAccess($ret);
        echo '<pre>';
        print_r( $_GET );
        echo '<br/>';
        print_r( $ret );
        exit('</pre>');
    }

    public function callback_userinfo()
    {

        $tokens=getAppidToken(session('ToUserName'));
        $OAuth = new OAuths($tokens['appid'], $tokens['secret']);
        $ret = $OAuth->getToken($_GET['code']);
        if(isset($ret['errcode'])){
            $this->jump(RESULT_ERROR, '错误码:'.$ret['errcode'].'错误信息:'.$ret['errmsg'],url('index'));
            exit();
        }
        $info = $OAuth->getUserInfo($ret['access_token'], $ret['openid']);
        if(isset($info['errcode'])){
            $this->jump(RESULT_ERROR, '错误码:'.$ret['errcode'].'错误信息:'.$ret['errmsg'],url('index'));
            exit();
        }
        //$check = $OAuth->checkToken($ret['access_token'], $ret['openid']);
        // $refresh = $OAuth->refreshToken($ret['refresh_token']);
        $this->_saveAccess($ret);
        $this->_saveUserInfo($info);
        $this->redirect('User/index');
    }
    /**
     * 存在更新，不存在则插入
     * @DateTime 2017-03-25T12:33:06+0800
     *
     * 这个地方建一个联合索引应该可以用replace
     */
    private function _saveAccess($data)
    {
//        $check = db('oauth_access')->where(['openid'=>$data['openid'], 'scope'=>$data['scope']])->find();
//        if($check){
//            $ret = db('oauth_access')->where(['openid'=>$data['openid'], 'scope'=>$data['scope']])->update($data);
//            return $ret;
//        }else{
//            $ret = db('oauth_access')->insert($data);
//            return $ret;
//        }
        return "sdfsdf";
    }

    /**
     * 因为这个有唯一主键openid所以， 可以用replace
     */
    // replace into think_oauth_userinfo set openid=123,nickname='xiaobai',sex=1,language='zh_CN',city='朝阳',province='北京',country='',headimgurl='',privilege=null,unionid='213213';
    private function _saveUserInfo($userinfo)
    {
        if($info=$this->logicWeiUser->getWeiUserInfo( ['openid'=>$userinfo['openid']], $field = true)){
            $auth = ['user_id' => $info['id'], TIME_UT_NAME => TIME_NOW];

            session('user_info', $info->toarray());
            session('user_auth', $auth);
            session('user_auth_sign', data_auth_sign($auth));
            return $info;
        }else{

            $data['openid'] = $userinfo['openid'];
            $data['nickname'] = $userinfo['nickname'];
            $data['sex'] = $userinfo['sex'];
            $data['language'] = $userinfo['language'];
            $data['city'] = $userinfo['city'];
            $data['province'] = $userinfo['province'];
            $data['country'] = $userinfo['country'];
            $data['wechat'] = getAppidToken(session('ToUserName'))['id'];
            $data['headimgurl'] = $userinfo['headimgurl'];
            if(!empty($data['unionid'])){
                $userdata['unionid'] = $data['unionid'];
            }
            $re=$this->logicWeiUser->weiUserOAuth($data);
            $auth = ['user_id' => $re, TIME_UT_NAME => TIME_NOW];

            session('user_info', $data);
            session('user_auth', $auth);
            session('user_auth_sign', data_auth_sign($auth));

            return true;
        }
    }
}
