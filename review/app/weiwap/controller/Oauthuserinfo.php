<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | authorization   | http://www.apache.org/licenses/LICENSE-2.0 )           |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiwap\controller;

use exwechat\api\OAuth\OAuth;

class Oauthuserinfo extends Common
{

    /**
     * 构造方法
     */
    public function __construct()
    {
        // 执行父类构造方法
        parent::__construct();

        if (empty($this->param['code'])) {

            if(empty($this->param['wechat'])){
                $this->errec();
            }else{
                $this->snsapi_userinfo($this->param['wechat'], $this->param['p']);
            }
        }
    }
    public function errec()
    {
        echo "<h1 style='text-align:center;margin-top: 50%;'>请同意右下角授权,之后再重新打开<h1/>";
        exit;
    }
    //（弹出授权页面，可通过openid拿到昵称、性别、所在地。并且， 即使在未关注的情况下，只要用户授权，也能获取其信息 ）
    public function snsapi_userinfo($wechat, $url)
    {
        if (empty($this->param['wechat'])) {
            $this->jump(RESULT_ERROR, '非法载入', url('index'));
            exit();
        }
        $tokens = getAppidToken($wechat, true);

        cookie('appid_conf', $tokens,600);

        $redirect_uri = url('callback_userinfo', '', false, true);
        $scope = 'snsapi_userinfo';

//        $shortUrl = get_sington_object('shortUrl', "exwechat\\api\\account\\shortUrl",$tokens['access_token']);
//
//        $url=$shortUrl->create($url)["short_url"] ;

        $state = urlencode($url);

        $redirect_uri = urlencode($redirect_uri);

        $OAuth = get_sington_object('OAuthEx', OAuth::class, $tokens);

        $url = $OAuth->getCodeUrl($redirect_uri, $scope, $state);

        header("Location:$url");
        exit;
    }

    public function callback_userinfo()
    {

        $OAuth = get_sington_object('OAuthEx', OAuth::class, cookie('appid_conf'));

        $ret = $OAuth->getToken($_GET['code']);

        if (isset($ret['errcode'])) {
            $ret['errcode']='参数异常';
            $this->assign('param', $ret);
            return $this->fetch('codeerror');
            exit();
        }
        $info = $OAuth->getUserInfo($ret['access_token'], $ret['openid']);
        if (isset($info['errcode'])) {
            $ret['errcode']='用户信息异常';
            $this->assign('param', $ret);
            return $this->fetch('codeerror');
            exit();
        }

        $userdata = $this->_saveFromUserToDB($info);

        $url = $this->param['state'].'?openid='.$userdata['openid'].'&id='.$userdata['id'];

        header("Location:$url");
        exit;
        //$this->redirect($url);
        //$check = $OAuth->checkToken($ret['access_token'], $ret['openid']);
        // $refresh = $OAuth->refreshToken($ret['refresh_token']);
    }

    /**
     * @param string $data 保存用户信息
     */
    private function _saveFromUserToDB($data = '')
    {
        $userdata['openid'] = $data['openid'];
        $userdata['nickname'] = filterEmoji($data['nickname']);
        $userdata['sex'] = $data['sex'];
        $userdata['choose_appid'] = cookie('appid_conf')['id'];
        $userdata['city'] = $data['city'];
        $userdata['country'] = $data['country'];
        $userdata['province'] = $data['province'];
        $userdata['language'] = $data['language'];
        $userdata['headimgurl'] = $data['headimgurl'];
        if(!empty($data['unionid'])){
            $userdata['unionid'] = $data['unionid'];
        }

        $headimgurl = './upload/headimg/' . $userdata['openid'] . '.png';

        if (empty($infoData['headimgurl'])) {
            downFile($userdata['headimgurl'], $headimgurl);
        }
        $infoData = $this->logicWeiUser->getWeiUserInfo(['openid' => $userdata['openid']], 'id,count');
        if (!empty($infoData)) {
            $userdata['id'] = $infoData['id'];
        }
        $id = $this->logicWeiUser->weiUserWebEdit($userdata);
        if (!empty($infoData)) {
            $userdata['id'] = $infoData['id'];
        }else{
            $userdata['id'] =$id;
        }
        return $userdata;
    }
}
