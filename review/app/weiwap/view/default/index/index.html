<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>话题版面</title>
	<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
	<meta content="yes" name="apple-mobile-web-app-capable"/>
	<meta content="black" name="apple-mobile-web-app-status-bar-style"/>
	<meta content="telephone=no" name="format-detection"/>
	<script type="text/javascript" src="__STATIC__/js/jquery.min.js"></script>
	<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
</head>
<body>

</body>
<script type="text/javascript">
    $(document).ready(function () {


		//分享管理
		var shareData = {
			title: "话题找同学", // 分享标题
			desc: "话题找同学", // 分享描述
			link: "{$share.url}", // 分享链接
			imgUrl: "https://wechat.feixiaoguai.com/static/module/admin/ext/adminlte/dist/img/user2-160x160.jpg", // 分享图标
			// type: '', // 分享类型,music、video或link，不填默认为link
			// dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
			success: function (msg) {
				$("#mcover").hide();
				// 用户确认分享后执行的回调函数
			},
			cancel: function (msg) {
				// alert('no')
				// 用户取消分享后执行的回调函数
			}
		};

        wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: "{$share.appid|default=0}", // 必填，公众号的唯一标识
            timestamp: "{$share.timestamp|default=0}", // 必填，生成签名的时间戳
            nonceStr: "abcdefghijklmnopqrstu", // 必填，生成签名的随机串
            signature: "{$share.signature|default=0}",// 必填，签名，见附录1
            jsApiList: ['onMenuShareAppMessage', 'onMenuShareTimeline', 'onMenuShareQQ'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
        });
		wx.ready(function () {
			wx.onMenuShareAppMessage(shareData);//微信朋友分享
			wx.onMenuShareTimeline(shareData);
			//qq分享
			wx.onMenuShareQQ(shareData);
		});
    });
    

</script>
</html>
