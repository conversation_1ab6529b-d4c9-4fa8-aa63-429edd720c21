<html>
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0" />
	<title>微信扫一扫</title>
	<script type="text/javascript" src="__STATIC__/topic/js/jquery.js"></script>
	<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>

</head>
<body>
<h3 style="text-align: center;margin-top: 200px;">正在调起摄像头<br>如果没有启动请手动打开扫一扫</h3>
</body>
<script>
	$(document).ready(function () {
		wx.config({
			debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
			appId: "{$share.appid|default=0}", // 必填，公众号的唯一标识
			timestamp: "{$share.timestamp|default=0}", // 必填，生成签名的时间戳
			nonceStr: "abcdefghijklmnopqrstu", // 必填，生成签名的随机串
			signature: "{$share.signature|default=0}",// 必填，签名，见附录1
			jsApiList: [ 'checkJsApi', 'scanQRCode'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
		});

		wx.error(function(res) {
			alert("出错了：" + res.errMsg);//这个地方的好处就是wx.config配置错误，会弹出窗口哪里错误，然后根据微信文档查询即可。
		});
		wx.ready(function() {
			wx.checkJsApi({
				jsApiList : ['scanQRCode'],
				success : function(res) {
					console.log(44);
				}
			});
			wx.scanQRCode({
				needResult : 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
				scanType : [ "qrCode"], // 可以指定扫二维码还是一维码，默认二者都有
				success : function(res) {
					var result = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
					window.location.href = result;//因为我这边是扫描后有个链接，然后跳转到该页面
				}
			});

		});
	});
</script>
</html>
