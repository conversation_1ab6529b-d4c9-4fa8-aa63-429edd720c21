<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<div class="box">
	<div class="box-header">
		<div class="row search-form">
			<div class="col-sm-2">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">开始时间</button>
						</span>
					<input name="start_time" size="16" type="text"
					       value="{$Think.get.start_time|default=''}" readonly
					       class="form_datetime form-control">
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">结束时间</button>
						</span>
					<input name="end_time" size="16" type="text"
					       value="{$Think.get.end_time|default=''}" readonly
					       class="form_datetime form-control">
				</div>
			</div>
			<div class="col-sm-8">
				<div class="row">
					<div class="col-xs-3">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">状态</button>
						</span>
							<select class="form-control" name="status">
								<option value="all">不限</option>
								{notempty name='status'}
								{volist name='status' id='vo'}
								<option value="{$key}">{$vo}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-3">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">渠道</button>
						</span>
							<select class="form-control" name="channel">
								<option value="all">不限</option>
								{notempty name='channel'}
								{volist name='channel' id='vo'}
								<option value="{$vo.code}">{$vo.name}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="input-group input-group-sm">
							<input type="text" name="search_data" style="" class="form-control pull-right"
							       value="{:input('search_data')}" placeholder="支持标题|描述">
							<div class="input-group-btn">
								<button type="button" id="search" url="{:url('activitySortitionLogList')}"
								        class="btn btn-info btn-flat"><i
										class="fa fa-search"></i></button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<th>id</th>
			<th>签id</th>
			<th>是否支付</th>
			<th>用户openid</th>
			<th>渠道</th>
			<th>发布时间</th>
			<th>操作</th>
			</thead>
			
			{notempty name='list'}
			<tbody>
			
			{volist name='list' id='vo'}
			<tr>
				<td>{$vo.id}</td>
				<td>{$vo.s_id}</td>
				<td>{$status[$vo.status]}</td>
				<td>{$vo.openid}</td>
				<td>{$vo.channel|default='无'}</td>
				<td>{$vo.create_time}</td>
				<td class="col-md-2 text-center">
					<ob_link><a href="{:url('activitySortitionLogEdit', array('id' => $vo['id']))}"
					            class="btn btn-xs"><i
							class="fa fa-edit"></i> 编辑</a></ob_link>
					<ob_link><a class="btn btn-xs confirm ajax-get"
					            href="{:url('activitySortitionLogDel', array('id' => $vo['id']))}"><i
							class="fa fa-trash-o"></i>
						删除</a></ob_link>
				</td>
			</tr>
			{/volist}
			
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="7" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
		</table>
	</div>
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>
</div>
<script type="text/javascript">
    ob.setValue("status","{present name='$_GET["status"]'}{$Think.get.status}{else}all{/present}");
    ob.setValue("channel", "{present name='$_GET["channel"]'}{$Think.get.channel}{else}all{/present}");
    $(document).ready(function () {

        $(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});
        //导出功能
        $(".export").click(function () {

            window.location.href = searchFormUrl($(".export"));
        });
    })

</script>