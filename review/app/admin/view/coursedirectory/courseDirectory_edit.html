<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<div class="box-body">
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label>标题</label>
						<span class="">（标题）</span><input class="form-control title_c" name="title" placeholder="请输入标题"
						                                 value="{$info['title']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>简介</label>
						<span class="">（简介）</span><input class="form-control describe_c" name="describe"
						                                 placeholder="请输入简介"
						                                 value="{$info['describe']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>排序</label>
						<span class="">（越大越前）</span><input class="form-control" name="sort" placeholder="越大越前"
						                                   value="{$info['sort']|default=''}" type="number">
					</div>
				</div>
				<input type="hidden" name="c_id" value="{$Request.param.c_id}">
				<div class="col-md-6">
					<div class="form-group">
						<label>课程包</label>
						<span class="">（课程包）</span>
						<select name="c_id" class="form-control">
							{notempty name='course_list'}
							{volist name='course_list' id='vo'}
							<option value="{$vo.id}">{$vo.title}</option>
							{/volist}
							{/notempty}
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>讲师</label>
						<span class="">（讲师）</span>
						<input class="form-control" name="teacher_name" placeholder="请输入标题"
						       value="{$info['teacher_name']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>最低时间</label>
						<span class="">（秒为单位 一分钟60秒）</span>
						<input class="form-control" name="min_time" placeholder="请输入多少秒"
						       value="{$info['min_time']|default=''}" type="text">
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="form-group">
						<label>上传格式</label>
						<span class="">（视频 音频）</span>
						<select name="type" class="form-control">
							<option value="1">视频</option>
							<option value="2">音频</option>
						</select>
					</div>
				</div>
			</div>
			<div class="col-md-6">
				<div class="form-group">
					<label>媒体地址</label>
					<a class="btn" data-toggle="modal" data-target="#myModal">选择资源</a>
					<input class="form-control media_url" name="media_url" value="{$info['media_url']|default=''}"
					       type="hidden">
					
					
					
					<div class="upload-img-boxcover_id">
						<input type="hidden" class="cover_id" name="cover_id" value="{$info['cover_id']|default='0'}">
						<div class="upload-pre-item">
							<a target="_blank" class="media_url_bo"
							   href="{$info['media_url']|default='javascript:;'}">
								<img class="cover_url" style="max-width: 150px;"
								     src="{$info['cover_id']|default='0'|get_picture_url}">
							</a>
							<span class="">时长</span><input class="form-control duration" name="duration" value="{$info['duration']|default='0'}" readonly>
						</div>
					</div>
				
				</div>
			</div>
			<div class="box-footer">
				<input type="hidden" name="id" value="{$info['id']|default='0'}"/>
				{include file="layout/edit_btn_group"/}
			</div>
		</div>
	</div>
</form>

<!-- 模态框（Modal） -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×
				</button>
				<h4 class="modal-title" id="myModalLabel">
					资源列表
				</h4>
			</div>
			<div class="modal-body">
				
				
				<div class="box-header">
					
					
					<!--					<ul class="nav nav-pills">-->
					<!--						<li class="active"><a href="#">Home</a></li>-->
					<!--						<li><a href="#">SVN</a></li>-->
					<!--						<li><a href="#">iOS</a></li>-->
					<!--						<li><a href="#">VB.Net</a></li>-->
					<!--						<li><a href="#">Java</a></li>-->
					<!--						<li><a href="#">PHP</a></li>-->
					<!--					</ul>-->
				
				
				</div>
				<div class="box-body">
					
					<div class="row list_html">
					
					
					</div>
				</div>
			</div>
			<div class="footer  text-center">
				<h4 class="loadingText"></h4>
				<p class="text-success">...</p>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<script type="text/javascript">
    ob.setValue("type", {$info['type'] |default = 1});
    ob.setValue("c_id", {$Request.param.c_id |default = 1});
    ob.setValue("is_free", {$info['is_free'] |default = 1});
    $(document).ready(function () {
        var current_page = 1;
        var last_page = 1;
        var lock_page = false;
        var _loadingText = document.querySelector('.loadingText');
        var list_html = '';

        $(document).on('click', '.current_video', function () {
            var VideoId = $(this).attr('videoid');
            var mediatype = $(this).attr('mediatype');
            var pic_url = $(this).attr('pic_url');
 
            $.ajax({
                url: "{:url('alivod/alivodFileUrl')}",
                type: 'post',
                data: {videoid: VideoId,picurl: pic_url},
                async: true, //默认为true 异步
                error: function (data) {
                    console.log('选择异常');
                },
                success: function (info_data) {
                    console.log(info_data);
                    $(".media_url_bo").attr('href',info_data['FileURL']);
                    $(".media_url").val(info_data['FileURL']);
                    $('#myModal').modal('hide');
                    $(".cover_url").attr('src',pic_url);
                    $(".cover_id").val(info_data['cover_id']);
                    $(".duration").val(info_data['Duration']);
                    if(mediatype=='Audio'){
                        ob.setValue("type", 2);
                    }
                    if(mediatype=='Video'){
                        ob.setValue("type", 1);
                    }
                }
            });
        });

        getList();



        // 获取文档完整的高度
        function getScrollHeight() {
            return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight);
        }



        $("#myModal").scroll(function() {
            var myHight=$('#myModal').prop("scrollHeight");
            var currentHight=$("#myModal").scrollTop();
            if (currentHight + getScrollHeight() == myHight) {
                _loadingText.innerText = '加载中...';
                if (last_page >= current_page && !lock_page) {
                    getList();
                } else {
                    _loadingText.innerText = '已经最后一页啦';
                }
            }
        });

        function getList() {
            lock_page=true;
            $.ajax({
                url: "{:url('alivod/alivodlistjson')}",
                type: 'post',
                data: {current_page: current_page},
                async: true, //默认为true 异步
                error: function (data) {
                    console.log('选择异常');
                },
                success: function (list_data) {
                    current_page++;
                    var list = list_data['data'];
                    last_page=list_data['last_page'];
                    for (let index in list) {
                        var pic_url = 'http://adminxg.robustcn.com/static/module/admin/img/onimg.png';
                        var MediaType=list[index]['MediaType'];
                        MediaType=MediaType.replace(MediaType[0],MediaType[0].toUpperCase());

                        if (list[index][MediaType]['CoverURL']) {

                            pic_url = list[index][MediaType]['CoverURL'];
                        }
                        //console.log(list[index][MediaType]);
                        list_html += '<div class="col-sm-4 col-md-2">\n' +
                            '\t\t\t\t\t\t\t<div class="thumbnail">\n' +
                            '\t\t\t\t\t\t\t\t<img style="height: 60px;" mediatype="'+MediaType+'"  pic_url="'+pic_url+'" class="current_video" videoid="' + list[index][MediaType][MediaType+'Id'] + '" src="' + pic_url + '"\n' +
                            '\t\t\t\t\t\t\t\t     title="' + list[index][MediaType]['Title'] + '">\n' +
                            '\t\t\t\t\t\t\t\t<div class="caption">\n' +
                            '\t\t\t\t\t\t\t\t\t<span>类型：' + MediaType + '</span>\n' +
                            '\t\t\t\t\t\t\t\t\t<p>' + list[index][MediaType]['Title'] + '</p>\n' +

                            '\t\t\t\t\t\t\t\t\t<div  class="clearfix">\n' +
                            '\t\t\t\t\t\t\t\t\t\t<a target="_blank" href="/admin.php/alivod/vodfileedit/VideoId/' + list[index][MediaType][MediaType+'Id'] + '" class="btn btn-xs pull-left">修改</a>\n' +
                            '\t\t\t\t\t\t\t\t\t\t<a target="_blank" href="/admin.php/alivod/vodfileurl/VideoId/' + list[index][MediaType][MediaType+'Id'] + '"  class="btn btn-xs pull-right">播放</a>\n' +
                            '\t\t\t\t\t\t\t\t\t</div>\n' +
                            '\t\t\t\t\t\t\t\t</div>\n' +
                            '\t\t\t\t\t\t\t</div>\n' +
                            '\t\t\t\t\t\t</div>';
                    }
                    ;
                    $('.list_html').append(list_html);
                    lock_page=false;
                }
            });
        }
    })

</script>

