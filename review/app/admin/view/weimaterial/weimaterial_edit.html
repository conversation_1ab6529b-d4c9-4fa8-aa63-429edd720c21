<form action="{:url()}" method="post" class="form_single">
    <div class="box">


        <div class="box-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>标题</label>
                        <span class="">（文章标题名称）</span>
                        <input class="form-control" name="title" placeholder="请输入文章标题名称"
                               value="{$info['title']|default=''}" type="text">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>类别</label>
                        <span class="">（素材类别）</span>
                        <select name="type_id" class="form-control material_typeid">
                            <option value="0">请选择</option>
                            {volist name='material_typeid' id='vo'}
                            <option value="{$key}">{$vo['name']}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="col-md-6 text show_hide">
                    <div class="form-group">
                        <label>内容</label>
                        <span class="">（可为地址或执行事件或文本内容）</span>
                        <textarea name="content" class="form-control">{$info['content']|default=''}</textarea>
                    </div>
                </div>
                <div class="col-md-6 picurl show_hide">
 
                    <div class="form-group">
                        <label>图片</label>
                        <span class="">（请上传单张封面图片）</span>
                        <br/>
             
                        
                        {assign name="pic" value="$info.content|default='0'" /}
                        {:widget('file/index', ['name' => 'pic', 'value' =>$pic, 'type' => 'img'])}

                    </div>
                </div>

                <div class="col-md-6 file show_hide">
                    <div class="form-group">
                        <label>附件</label>
                        <span class="">（文件上传）</span>
                        <br/>
                        {assign name="file" value="$info.content|default=''" /}
                        {:widget('file/index', ['name' => 'file', 'value' => $file, 'type' => 'file'])}

                    </div>
                </div>
            </div>

            <div class="box-footer">

                <input type="hidden" name="id" value="{$info['id']|default='0'}"/>

                <button type="submit" class="btn  ladda-button ajax-post" data-style="slide-up"
                        target-form="form_single">
                    <span class="ladda-label">确 定</span>
                </button>

                <a class="btn " onclick="javascript:history.back(-1);return false;"> 返 回</a>
            </div>

        </div>

    </div>
</form>
<script type="text/javascript">
    $(document).ready(function () {
        var type_id={$info.type_id|default=0};
        $(".show_hide").hide();
        if(type_id>=1){
            ob.setValue("type_id", type_id);
            type_event(type_id);
        }
        $(".material_typeid").change(function () {                     //选择配置类型

            $(".show_hide").hide();
            var index = $(this).val();
            type_event(index);
        });
        function  type_event(indx_id){
            if (indx_id == 1) {
                $(".picurl").show();
            } else if (indx_id == 2) {
                $(".file").show();
            } else if (indx_id == 3) {
                $(".file").show();
            } else if (indx_id == 4) {
                $(".text").show();
            } else if (indx_id == 5) {
                $(".file").show();
            } else if (indx_id == 8) {
                $(".text").show();
            }
        }
    });
</script>