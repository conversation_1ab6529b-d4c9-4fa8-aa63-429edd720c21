<div class="box">
    <div class="box-header">
        
        <div class="row">
            <div class="col-sm-4">
                <ob_link><a class="btn btn-primary" href="{:url('weiWelcomeAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
            </div>
            
            <div class="col-sm-8">
                <div class="box-tools search-form pull-right">
                    <div class="input-group input-group-sm">
                        <input type="text" name="search_data" style="width: 200px;" class="form-control pull-right" value="{:input('search_data')}" placeholder="支持昵称|用户名|邮箱|手机">
                        <div class="input-group-btn">
                            <button type="button" id="search"  url="{:url('weiWelcomeList')}" class="btn btn-info btn-flat"><i class="fa fa-search"></i></button>
                        </div>
                    
                    </div>
                </div>
            </div>
        </div>
    
    </div>
    <div class="box-body table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
            <tr>
                <th>排序</th>
                <th>标题</th>
                <th>回复类型</th>
                <th>回复关联</th>
                <th>状态</th>
                <th>创建实际时间</th>
                <th>操作</th>
            </tr>
            </thead>

            {notempty name='list'}
            <tbody>
            {volist name='list' id='vo'}
            <tr>
                <td>{$vo.id}</td>
                <td>{$vo.key_wrod}</td>
                <td>{$material_typeid[$vo['typeid']]["name"]|default='一级菜单'}</td>
                <td><a class="logic_anyes weiMaterial" logicIndex="m_id" logicName="weiMaterial" logicFile="title" logicId="{$vo.m_id}"  href="{:url('Weimaterial/weiMaterialList', array('m_id' => $vo['m_id']))}">{$vo.m_id}<a/></td>
                <td>{$vo.status_text}</td>
                <td>{$vo.create_time}</td>
                <td class="col-md-2 text-center">&nbsp;
                    <ob_link>
                        {if condition="$vo.status eq 0"}
                        <a class="btn confirm ajax-get btn-success btn-sm" href="{:url('weiWelcomeSet', array('id' => $vo['id']))}?status=1"><i class="fa fa-check"></i> 启动</a>
                        {else /}
                        <a class="btn confirm ajax-get btn-info btn-sm" href="{:url('weiWelcomeSet', array('id' => $vo['id']))}?status=0"><i class="fa fa-remove"></i> 禁用</a>
                        {/if}
                    </ob_link>
                    <ob_link><a class="btn btn-warning  btn-sm" href="{:url('weiWelcomeEdit', array('id' => $vo['id']))}"><i class="fa fa-user-plus"></i> 修 改</a></ob_link>
                    <ob_link><a class="btn confirm ajax-get btn-danger  btn-sm" href="{:url('weiWelcomeDel', array('id' => $vo['id']))}"><i class="fa fa-trash-o"></i> 删 除</a></ob_link>
                </td>
            </tr>
            {/volist}
            </tbody>
            {else/}
            <tbody>
            <tr class="odd">
                <td colspan="8" class="text-center" valign="top">{:config('empty_list_describe')}</td>
            </tr>
            </tbody>
            {/notempty}
        </table>
        {include file="layout/special"/}
    </div>


</div>


<script>
    //搜索功能
    $("#search").click(function () {
        var url = $(this).attr('url');
        var query = $('.search-form').find('input').serialize();
        query = query.replace(/(&|^)(\w*?\d*?\-*?_*?)*?=?((?=&)|(?=$))/g, '');
        query = query.replace(/^&/g, '');
        if (url.indexOf('?') > 0) {
            url += '&' + query;
        } else {
            url += '?' + query;
        }
        window.location.href = url;
    });

    //回车搜索
    $(".search-input").keyup(function (e) {
        if (e.keyCode === 13) {
            $("#search").click();
            return false;
        }
    });
</script>