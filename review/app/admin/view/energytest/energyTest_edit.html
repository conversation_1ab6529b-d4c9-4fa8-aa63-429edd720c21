<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-danger">
                        <div class="box-header with-border">
                            <h3 class="box-title">视频试题配置</h3>
                        </div>
                        <div class="box-body">
                            
                            <div class="box-body" style="display: block;">
								<span class="badge" style="background-color: red"><i class="fa fa-exclamation"
                                                                                     style="font-size:1.3em"></i></span>
                                多选时候，漏选获得分数为总分/个数 举例：总分为<span class="label label-info">8</span>，选项为ABCD 答案为ABC，。选择了AB
                                ，计算分数为<span class="label label-info">2</span>
                            </div>
                            
                            <div class="form-inline" style="margin-top: 5px;border-top:1px dashed lightseagreen">
                                
                                <div class="form-group" style="margin-top: 10px">
                                    <label for="exampleInputName2" class="text_id"> 题目：</label>
                                    <textarea name="title" class="form-control" >{$info['title']|default=''}</textarea>
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <label for="exampleInputName2"> 题目类型：</label>
                                    <select name="type" class="form-control">
                                        <option value="1">自我确定力</option>
                                        <option value="2">情绪控制力</option>
                                        <option value="3">心智觉知力</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <div class="panel panel-primary">
                                        <div class="panel-heading">
                                            <h3 class="panel-title">选项(正确答案前面打勾)</h3>
                                        </div>
                                        <div class="panel-body" style="padding: 5px;">
                                            <input class="test_option" name="test_option" type="hidden" value="">
                                            
                                            <ul class="list-group option_ul">
                                                <li class="list-group-item option_li">
                                                    <label class="option_choose_label">
                                                        <input type="checkbox" class="option_choose" value="1">
                                                        <span class="xuhao">A:</span>
                                                    </label>
                                                    <input type="text" class="form-control input-sm option_value"
                                                           placeholder="输入选项" value="">
                                                    
                                                    <span class="badge" style="margin-top: 5px"><i
                                                            class="fa fa-remove field_remove"
                                                            style="font-size:1.3em"></i></span>
                                                    <span class="badge" style="margin-top: 5px"><i class="fa fa-plus field_add"
                                                                                                   style="font-size:1.3em"></i></span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /.box-body -->
                    </div>
                </div>
            </div>
			
			<div class="box-footer">
				<input type="hidden" name="id" value="{$info['id']|default='0'}"/>
				{include file="layout/edit_btn_group"/}
			</div>
		</div>
	</div>
</form>

<script type="text/javascript">
    $(document).ready(function () {
        ob.setValue("type", {$info['type']|default=0});
        var lines = 1;
        
        var charA = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J'];
        $(document).on('click', '.field_add', function () {
            $(this).parent().parent().after($(this).parent().parent().prop("outerHTML"));
            if ($(this).parent().parent().hasClass('form-inline')) {
                lines++;
            } else if ($(this).parent().parent().hasClass('option_li')) {
                //xunhao($(this).parents(".option_ul"))
            }
            $(this).remove();
        });
        $(document).on('click', '.field_remove', function () {
            if ($(this).parent().next().children('i.field_add').length == 0) {
                if ($(this).parent().parent().hasClass('form-inline')) {
                    lines--;
                }
                var ul_abj=$(this).parents(".option_ul");
                $(this).parent().parent().remove();
                if ($(this).parent().parent().hasClass('option_li')) {
              
                }
            }
            
        });
        
        
        var teachingTest={$info|json_encode};
 
        var options_json=JSON.parse(teachingTest['options']);
    
        var o_index=0;
    
        for(var o_i in options_json){
            if(!$('.form-inline').eq(0).find(".option_li").eq(o_index).hasClass('option_li')){
                $('.form-inline').eq(0).find(".option_li").eq(o_index-1).after($('.form-inline').eq(0).find(".option_li").eq(o_index-1).prop("outerHTML"));
                $('.form-inline').eq(0).find(".option_li").eq(o_index-1).find(".field_add").remove();
            }
            $('.form-inline').eq(0).find(".xuhao").eq(o_index).text(o_i+':');
            $('.form-inline').eq(0).find(".option_value").eq(o_index).val(options_json[o_i]);
            o_index++;
        }
        $(document).on('change', '.form-inline', function () {
            xunhao($(this));
        });
        function xunhao(thisA) {
            if(thisA.find(".test_title").val()==''){
                toast.error('请输入题目');
                thisA.find(".test_title").focus();
                return false;
            }
            if(thisA.find(".test_score").val()==0){
                toast.error('请输入分值');
                thisA.find(".test_score").focus();
                return false;
            }
            var option_name = {};
            var option_choose = [];
            thisA.find(".option_ul").children('li').each(function () {
                var o_c_index=charA[$(this).index()];
                $(this).find(".xuhao").text(o_c_index+':');
                option_name[o_c_index] = $(this).find(".option_value").val();
                if ($(this).find(".option_choose").is(':checked')) {
                    option_choose.push(o_c_index);
                }
            });
            thisA.find(".test_option").val(JSON.stringify(option_name));
        }
    });

</script>