<div class="box">
    <div class="box-header">
        <div class="row">
            <div class="col-sm-4">
                <ob_link><a class="btn" href="{:url('weiAppidAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
                <ob_link><a class="btn ajax-get" href="{:url('getCheckAppid')}">检查默认号</a></ob_link>
            </div>
            <div class="col-sm-8">
                <div class="box-tools search-form pull-right">
                    <div class="input-group input-group-sm">
                        <input type="text" name="search_data" style="width: 200px;" class="form-control pull-right" value="{:input('search_data')}" placeholder="支持标题|描述">
                        <div class="input-group-btn">
                            <button type="button" id="search"  url="{:url('weiAppidList')}" class="btn btn-info btn-flat"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="box-body table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
            <tr>
                <th>默认</th>
                <th>appid</th>
                <th>类型</th>
                <th>URL</th>
                <th>TOKEN</th>
                <th>encodingAesKey</th>
                <th>创建实际时间</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
            </thead>

            {notempty name='list'}
            <tbody>
            {volist name='list' id='vo'}
            <tr>
                <td>
                    {eq name="vo.id" value="$Think.session.member_info.choose_appid"}
                    已经选择
                    {else/}
                    <a href="{:url('Member/memberWeiApp', array('choose_appid' => $vo['id']))}" class="confirm ajax-get">{$vo.name}</a>
                    {/eq}

                </td>
                <td>{$vo.appid}</td>
                <td>{:config('ext_wechat.appid_type')[$vo.type]}</td>
                <td><input class="form-control" readonly value="https://adminxg.robustcn.com//wechat/index/index/token/{$vo.token}"></td>
                <td><input class="form-control" readonly value="{$vo.token}"></td>
                <td><input class="form-control" readonly value="{$vo.encoding_aes_key}"></td>
                <td>{$vo.create_time}</td>
                <td>{$vo.status_text}</td>
                <td class="col-md-2 text-center">
                    <ob_link><a class="btn btn-xs" href="{:url('weiAppidEdit', array('id' => $vo['id']))}"><i
                            class="fa fa-user-plus"></i> 修 改</a></ob_link>
                    &nbsp;
                    <ob_link><a class="btn btn-xs confirm ajax-get" href="{:url('weiappidDel', array('id' => $vo['id']))}"><i
                            class="fa fa-trash-o"></i> 删 除</a></ob_link>
                </td>
            </tr>
            {/volist}
            </tbody>
            {else/}
            <tbody>
            <tr class="odd">
                <td colspan="8" class="text-center" valign="top">{:config('empty_list_describe')}</td>
            </tr>
            </tbody>
            {/notempty}
        </table>
    </div>

    <div class="box-footer clearfix text-center">
        {$list->render()}
    </div>
</div>