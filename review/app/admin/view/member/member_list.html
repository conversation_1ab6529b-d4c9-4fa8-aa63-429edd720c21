<div class="box">
    
  <div class="box-header">

    <div class="row">
        <div class="col-sm-5">
            <ob_link><a class="btn" href="{:url('memberAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
            &nbsp;
            <ob_link><a class="btn export" url="{:url('exportMemberList')}"><i class="fa fa-download"></i> 导 出</a></ob_link>
        </div>
        
        <div class="col-sm-7">
            <div class="box-tools search-form pull-right">
                <div class="input-group input-group-sm">
                    
                    <input type="text" name="search_data" style="width: 200px;" class="form-control pull-right" value="{:input('search_data')}" placeholder="支持昵称|用户名|邮箱|手机">

                    <div class="input-group-btn">
                      <button type="button" id="search"  url="{:url('memberlist')}" class="btn btn-info btn-flat"><i class="fa fa-search"></i></button>
                    </div>

                </div>
           </div>
        </div>
    </div>
    
  </div>
    
    
  <div class="box-body table-responsive">
    <table  class="table table-bordered table-hover table-striped">
      <thead>
      <tr>
          <th>头像</th>
          <th>昵称</th>
          <th>用户名</th>
          <th>邮箱</th>
          <th>手机</th>
          <th>注册时间</th>
          <th>上级</th>
          <th>操作</th>
      </tr>
      </thead>
      
      {notempty name='list'}
        <tbody>
            {volist name='list' id='vo'}
                <tr>
                  <td>
                      <img class="img-circle" style="width: 30px; height: 30px;" src="{$vo.head_img_id|get_head_picture_url}"/>
                  </td>
                  <td>{$vo.nickname}</td>
                  <td>{$vo.username}</td>
                  <td>{$vo.email|default='未绑定'}</td>
                  <td>{$vo.mobile|default='未绑定'}</td>
                  <td>{$vo.create_time}</td>
                  <td>{$vo.leader_nickname}</td>
                  <td class="text-center">
                      <ob_link><a href="{:url('memberEdit', array('id' => $vo['id']))}"><span class='badge bg-green'>编 辑</span></a></ob_link>
                      
                      <ob_link><a href="{:url('memberAuth', array('id' => $vo['id']))}"><span class='badge bg-green'>授 权</span></a></ob_link>
                      &nbsp;
                      <ob_link><a class="confirm ajax-get"  href="{:url('memberDel', array('id' => $vo['id']))}"><span class='badge bg-green'>删 除</span></a></ob_link>
                  </td>
                </tr>
            {/volist}
        </tbody>
        {else/}
        <tbody><tr class="odd"><td colspan="8" class="text-center" valign="top">{:config('empty_list_describe')}</td></tr></tbody>
      {/notempty}
    </table>
  </div>

  <div class="box-footer clearfix text-center">
      {$list->render()}
  </div>

</div>

<script>
    //导出功能
    $(".export").click(function(){
        
        window.location.href = searchFormUrl($(".export"));
    });
</script>