<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<div class="box">
	<div class="box-header">
		<div class="row search-form">
			<div class="col-sm-2">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">开始时间</button>
						</span>
					<input name="start_time" size="16" type="text"
						   value="{$Think.get.start_time|default=''}" readonly
						   class="form_datetime form-control">
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">结束时间</button>
						</span>
					<input name="end_time" size="16" type="text"
						   value="{$Think.get.end_time|default=''}" readonly
						   class="form_datetime form-control">
				</div>
			</div>
			<div class="col-sm-8">
				<div class="row">
					<div class="col-xs-3">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">状态</button>
						</span>
							<select class="form-control" name="status">
								<option value="all">不限</option>
								{notempty name='status'}
								{volist name='status' id='vo'}
								<option value="{$key}">{$vo}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-3">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">渠道</button>
						</span>
							<select class="form-control" name="channel">
								<option value="all">不限</option>
								{notempty name='channel'}
								{volist name='channel' id='vo'}
								<option value="{$vo.code}">{$vo.name}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="input-group input-group-sm">
							<input type="text" name="search_data" style="" class="form-control pull-right"
								   value="{:input('search_data')}" placeholder="支持标题|描述">
							<div class="input-group-btn">
								<button type="button" id="search" url="{:url('')}"
										class="btn btn-info btn-flat"><i
										class="fa fa-search"></i></button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<th>渠道code</th>
			<th>单数</th>
			<th>总金额</th>
			</thead>
			
			{notempty name='list'}
			<tbody>
			{assign name="channel_count_s" value="0"/}
			{assign name="amount_s" value="0"/}
			{volist name='list' id='vo'}

			<spen style="display: none;">
				{$channel_count_s+=$vo.channel_count}
				{$amount_s+=$vo.amount}</spen>
			<tr>
				<td>{$vo.channel}</td>
				<td>{$vo.channel_count}</td>
				<td>{$vo.amount}</td>
			</tr>
			{/volist}
			<tr style="color: red">
				<td>统计</td>
				<td>{$channel_count_s}</td>
				<td>{$amount_s}</td>
			</tr>
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="7" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
			{include file="layout/special"/}
		</table>
	</div>
	<div class="box-footer clearfix text-center">

	</div>
</div>
<script type="text/javascript">
	ob.setValue("status","{present name='$_GET["status"]'}{$Think.get.status}{else}all{/present}");
	ob.setValue("channel", "{present name='$_GET["channel"]'}{$Think.get.channel}{else}all{/present}");
	$(document).ready(function () {

		$(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});
		//导出功能
		$(".export").click(function () {

			window.location.href = searchFormUrl($(".export"));
		});
	})

</script>