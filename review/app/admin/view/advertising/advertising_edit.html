
<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<div class="box-body">
			<div class="row">
				
				<div class="col-md-6">
					<div class="form-group">
						<label>广告标题</label>
						<span class="">（广告标题）</span><input class="form-control" name="title" placeholder="请输入广告标题"
						                                   value="{$info['title']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>广告位</label>
						<span class="">（广告位）</span>
						<select name="location_id" class="form-control">
							{notempty name='location_list'}
							{volist name='location_list' id='vo'}
							<option value="{$vo.id}">{$vo.title}</option>
							{/volist}
							{/notempty}
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>封面图片</label>
						<span class="">（请上传单张封面图片：分辨率宽高比率  340:150  可以等比例放大）</span>
						<br/>
						{assign name="cover_id" value="$info.cover_id|default='0'" /}
						{:widget('file/index', ['name' => 'cover_id', 'value' => $cover_id, 'type' => 'img'])}
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>排序</label>
						<span class="">（排序）</span><input class="form-control" name="sort" placeholder="请输入排序"
						                                 value="{$info['sort']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>描述</label>
						<span class="">（描述）</span><input class="form-control" name="describe" placeholder="请输入描述"
						                                 value="{$info['describe']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>主题类型</label>
						<span>（主题类型）</span>
						<select class="form-control type_id" name="type" >
							<option value="1">课程</option>
							<option value="2">杂类</option>
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>地址</label>
						<span class="">（跳转地址）</span>
						<select class="form-control url_data" name="url">
							<option value="0">0</option>
						</select>
					</div>
				</div>
			
			</div>
			
			<div class="box-footer">
				<input type="hidden" name="id" value="{$info['id']|default='0'}"/>
				{include file="layout/edit_btn_group"/}
			</div>
		</div>
	</div>
</form>
<script src="__STATIC__/module/common/bootstrap-select/js/bootstrap-select.min.js"></script>
<script type="text/javascript">
    
    $(document).ready(function () {
        var type_id={$info['type']|default = 1};
        var course_column={$course_column|default='[]'};
        $('.type_id').change(function () {
            select_openid($(this).val())
        });
        select_openid(type_id)
        var url_id = {$info['url']|default = 0};
        if(url_id>0){
            console.log(url_id);
            $('.url_data').val(url_id);
        }
        
         function select_openid(type_id) {
             var data_opendi=[];
             var url_data='';
             if(type_id==1){
                 data_opendi= course_column
             }else if(type_id==2){
                 data_opendi= teachers_column
             }else if(type_id==3){
             }
             
             for(let i in data_opendi){
                 url_data+='<option value="'+data_opendi[i]['id']+'">'+data_opendi[i]['title']+'</option>';
             }
             $('.url_data').empty().append(url_data);
        }
        ob.setValue("type", "{$info['type']|default = 1}");
    });

</script>