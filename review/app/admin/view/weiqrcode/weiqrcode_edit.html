<form action="{:url()}" method="post" class="form_single">
    <div class="box">
      <div class="box-body">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label>场景名称</label>
              <span>（场景名称会作为默认的昵称）</span>
              <input class="form-control" name="scene_name" placeholder="请输入名称" type="text" value="{$info['scene_str']|default=''}">
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label>关键词</label>
              <span>（扫描此二维码的同时也会触发此关键词。）</span>
              <input class="form-control" name="keyword" placeholder="请输入关键词" type="text" value="{$info['scene_str']|default=''}">
            </div>
          </div>
 
          <div class="col-md-6">
            <div class="form-group qr_type_click">
              <label>类型</label>
              <span></span>
              <label><input type="radio" checked="checked" name="action_name" value="QR_LIMIT_STR_SCENE"> 永久</label>
              <label><input type="radio" name="action_name"  value="QR_STR_SCENE"> 临时</label>
            </div>
          </div>
          <div class="col-md-6 scene_str_vale vale">
            <div class="form-group">
              <label>场景值字符</label>
              <span>目前腾讯规定了永久二维码只能生成10万个，请悠着点使用永久类型二维码。</span>
              <input class="form-control" name="scene_str" placeholder="请输入场景值字符" type="text" value="{$info['scene_str']|default=''}">
            </div>
          </div>
          <div class="col-md-6 expire_vale vale">
            <div class="form-group">
              <label>有效时间/秒</label>
              <span>临时二维码过期时间, 最大为30天（2592000秒）</span>
              <input class="form-control expire_seconds" name="expire_seconds" placeholder="请输入时间" type="text" value="{$info['scene_str']|default='2592000'}">
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer">
        <input value="{$info['id']|default='0'}" name="id" type="hidden">
        <button  type="submit" class="btn ladda-button ajax-post" data-style="slide-up" target-form="form_single">
            <span class="ladda-label">确 定</span>
        </button>
         
        <a class="btn" onclick="javascript:history.back(-1);return false;"> 返 回</a>
      </div>
    </div>
</form>

<script type="text/javascript">
    ob.setValue("action_name", {$info.action_name|default='"QR_LIMIT_STR_SCENE"'});
    var a = $("input[name='action_name']:checked").val();
    radioVale(a);
    $(function () {
        $(".qr_type_click").click(function () {
            a = $("input[name='action_name']:checked").val();
            radioVale(a)
        });
    })
    function radioVale(a){
        $(".expire_vale").hide();
        if(a=='QR_LIMIT_STR_SCENE'){
            $(".expire_seconds").val(0);
            //$(".scene_str_vale").show();
        }else{
            $(".expire_vale").show();
        }
    }
</script>