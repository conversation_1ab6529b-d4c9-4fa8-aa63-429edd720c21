<div class="box">
  <div class="box-header">
      <ob_link><a class="btn" href="{:url('groupAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
      <br/>
  </div>
    
  <!-- /.box-header -->
  <div class="box-body table-responsive">
    <table  class="table table-bordered table-hover table-striped">
      <thead>
      <tr>
          <th>名称</th>
          <th>描述</th>
          <th>状态</th>
          <th>操作</th>
      </tr>
      </thead>
      
      {notempty name='list'}
        <tbody>
            {volist name='list' id='vo'}
                <tr>
                  <td>{$vo.name}</td>
                  <td>{$vo.describe}</td>
                  <td>{$vo.status_text}</td>
                  <td class="col-md-3 text-center">
                      <ob_link><a href="{:url('menuAuth', array('id' => $vo['id']))}" class="btn"><i class="fa fa-reorder"></i> 菜单授权</a></ob_link>
                      &nbsp;
                      <ob_link><a href="{:url('groupEdit', array('id' => $vo['id']))}" class="btn"><i class="fa fa-edit"></i> 编辑</a></ob_link>
                      &nbsp;
                      <ob_link><a class="btn confirm ajax-get"  href="{:url('groupDel', array('id' => $vo['id']))}"><i class="fa fa-trash-o"></i> 删 除</a></ob_link>
                  </td>
                </tr>
            {/volist}
        </tbody>
        {else/}
        <tbody><tr class="odd"><td colspan="4" class="text-center" valign="top">{:config('empty_list_describe')}</td></tr></tbody>
      {/notempty}
    </table>
  </div>
  <!-- /.box-body -->

  <div class="box-footer clearfix text-center">
      {$list->render()}
  </div>

</div>