<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 会员控制器
 */
class Weireply extends AdminBase
{


    /**
     * 自定义回复列表
     */
    public function weiReplyList()
    {

        $where = $this->logicWeiReply->getWhere($this->param);
        $where['choose_appid']=session('member_info')['choose_appid'];
        $this->assign('list', $this->logicWeiReply->getWeiReplyList($where));
        $this->assign('material_typeid',config('ext_wechat.material_typeid'));
        return $this->fetch('weireply_list');
    }
    
    /**
     * 自定义回复添加
     */
    public function weiReplyAdd()
    {
        IS_POST && $this->jump($this->logicWeiReply->weiReplyAdd($this->param));
        $list['material_typeid'] = config('ext_wechat.material_typeid');
        $WeiMaterialColumn = $this->logicWeiMaterial->getWeiMaterialColumn(['choose_appid' => session('member_info')['choose_appid']]);

        $list['material_list']=array();
        foreach ($WeiMaterialColumn as $key => $value) {
            $list['material_list'][$value['type_id']][] = $value;
        }
        $this->assign('data', $list);
        return $this->fetch('weireply_edit');
    }
    /**
     * 自定义回复修改
     */
    public function weiReplyEdit()
    {

        IS_POST && $this->jump($this->logicWeiReply->weiReplyEdit($this->param));

        $weiReplyInfo=$this->logicWeiReply->getWeiReplyInfo(['id'=>$this->param['id']])->toArray();
        $weiReplyInfo['k_id']=json_encode(explode(',',$weiReplyInfo['k_id']));
        $weiReplyInfo['k_type']=json_encode(explode(',',$weiReplyInfo['k_type']));
        $this->assign('info',$weiReplyInfo);

        $list['material_typeid'] = config('ext_wechat.material_typeid');
        $WeiMaterialColumn = $this->logicWeiMaterial->getWeiMaterialColumn(['choose_appid' => session('member_info')['choose_appid']]);

        foreach ($WeiMaterialColumn as $key => $value) {
            $list['material_list'][$value['type_id']][] = $value;
        }
        $this->assign('data', $list);

        return $this->fetch('weireply_edit');
    }
    /**
     * 自定义回复修改
     */
    public function weiReplySet()
    {
        return $this->jump($this->logicWeiReply->weiReplySet($this->param));
    }
    /**
     * 自定义回复删除
     */
    public function weiReplyDel($id = 0)
    {
        
        return $this->jump($this->logicWeiReply->weiReplyDel(['id' => $id]));
    }
    public function fff(){
        $result = $this->logicWeiReply->wechatReplyMaterialGet(['key_wrod' => 1234, 'status' => 1, 'choose_appid' => session('member_info')['choose_appid']]);
    }
}
