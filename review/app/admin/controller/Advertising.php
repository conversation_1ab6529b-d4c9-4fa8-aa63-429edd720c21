<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 广告控制器
 */
class Advertising extends AdminBase
{

    /**
     * 广告列表
     */
    public function advertisingList()
    {

        $where = $this->logicAdvertising->getAdminWhere($this->param);

        $this->assign('list', $this->logicAdvertising->getAdvertisingList($where, '', ''));

        $this->assign('type',[1=>'课程',2=>'讲师',3=>'活动']);

        return $this->fetch('advertising_list');
    }

    /**
     * 广告添加
     */
    public function advertisingAdd()
    {
        IS_POST && $this->jump($this->logicAdvertising->advertisingAdminEdit($this->param));

        $this->assign('location_list', $this->logicAdvertisingLocation->getAdvertisingLocationColumn([]));

        $this->assign('course_column', json_encode(array_values($this->logicCourse->getCourseColumn(['status'=>1],'id,member_id,title'))));

        return $this->fetch('advertising_edit');
    }

    /**
     * 广告编辑
     */
    public function advertisingEdit()
    {
        IS_POST && $this->jump($this->logicAdvertising->advertisingAdminEdit($this->param));

        $info = $this->logicAdvertising->getAdvertisingInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        $this->assign('location_list', $this->logicAdvertisingLocation->getAdvertisingLocationColumn([]));

        $this->assign('course_column', json_encode(array_values($this->logicCourse->getCourseColumn(['status'=>1],'id,member_id,title'))));

        return $this->fetch('advertising_edit');
    }


    /**
     * 广告删除
     */
    public function advertisingDel($id = 0)
    {

        $this->jump($this->logicAdvertising->advertisingAdminDel(['id' => $id]));
    }
}
