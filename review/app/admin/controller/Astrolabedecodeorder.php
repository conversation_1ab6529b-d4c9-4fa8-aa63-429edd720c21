<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 心灵译码订单控制器
 */
class Astrolabedecodeorder extends AdminBase
{

    /**
     * 心灵译码订单列表
     */
    public function astrolabeDecodeOrderList()
    {

        $where = $this->logicAstrolabeDecodeOrder->getAdminWhere($this->param);

        $this->assign('list', $this->logicAstrolabeDecodeOrder->getAstrolabeDecodeOrderList($where, '', ''));

        return $this->fetch('astrolabeDecodeOrder_list');
    }

    /**
     * 心灵译码订单添加
     */
    public function astrolabeDecodeOrderAdd()
    {
        IS_POST && $this->jump($this->logicAstrolabeDecodeOrder->astrolabeDecodeOrderAdminEdit($this->param));
        return $this->fetch('astrolabeDecodeOrder_edit');
    }

    /**
     * 心灵译码订单编辑
     */
    public function astrolabeDecodeOrderEdit()
    {
        IS_POST && $this->jump($this->logicAstrolabeDecodeOrder->astrolabeDecodeOrderAdminEdit($this->param));

        $info = $this->logicAstrolabeDecodeOrder->getAstrolabeDecodeOrderInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('astrolabeDecodeOrder_edit');
    }


    /**
     * 心灵译码订单删除
     */
    public function astrolabeDecodeOrderDel($id = 0)
    {

        $this->jump($this->logicAstrolabeDecodeOrder->astrolabeDecodeOrderDel(['id' => $id]));
    }

    /**
     * 数据状态设置
     */
     public function setStatus()
     {

         $this->jump($this->logicAdminBase->setStatus('AstrolabeDecodeOrder', $this->param));

     }

     /**
      * 排序
     */
     public function setSort()
     {

         $this->jump($this->logicAdminBase->setSort('AstrolabeDecodeOrder', $this->param));
     }
}
