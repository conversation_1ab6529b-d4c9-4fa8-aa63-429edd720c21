<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 性格测试管理控制器
 */
class Corpusmember extends AdminBase
{

    /**
     * 性格测试管理列表
     */
    public function corpusMemberList()
    {

        $where = $this->logicCorpusMember->getAdminWhere($this->param);

        $this->assign('list', $this->logicCorpusMember->getCorpusMemberList($where, '', ''));

        $this->assign('sex', [0=>'男',1=>'女']);

        return $this->fetch('corpusMember_list');
    }

    /**
     * 性格测试导出
     */
    public function exportCorpusMemberList()
    {

        $where = $this->logicMember->getWhere($this->param);

        $this->logicCorpusMember->exportCorpusMemberList($where);
    }
    /**
     * 性格测试管理添加
     */
    public function corpusMemberAdd()
    {
        IS_POST && $this->jump($this->logicCorpusMember->corpusMemberAdminEdit($this->param));
        return $this->fetch('corpusMember_edit');
    }

    /**
     * 性格测试管理编辑
     */
    public function corpusMemberEdit()
    {
        IS_POST && $this->jump($this->logicCorpusMember->corpusMemberAdminEdit($this->param));

        $info = $this->logicCorpusMember->getCorpusMemberInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('corpusMember_edit');
    }


    /**
     * 性格测试管理删除
     */
    public function corpusMemberDel($id = 0)
    {

        $this->jump($this->logicCorpusMember->corpusMemberAdminDel(['id' => $id]));
    }
}
