<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 前端模块基类控制器
 */
class WeiBase extends AdminBase
{
    /**
     * 基类初始化
     */
    public function __construct()
    {
        parent::__construct();

        if(!empty(getAccessToken()['errcode'])){
            $this->jump([RESULT_ERROR,'当前用户未设置公众号,或者api错误', url('weiappid/weiappidlist')]);
        }
    }
}
