<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 年运测评控制器
 */
class Activitytransportsolar2 extends AdminBase
{

    /**
     * 年运测评列表
     */
    public function activitytransportsolarList()
    {


        $this->assign('channel',$channel=$this->logicChannelList->getChannelListColumn(['status'=>1,'id'=>['in',session('member_info')['channel']]]));


        $where['status']=['gt',-3];

        if (isset($this->param['status']) and $this->param['status'] != 'all') {
            $where['status'] = $this->param['status'];
        }

        if (isset($this->param['channel']) and $this->param['channel'] != 'all') {
            $where['channel'] = $this->param['channel'];
        }else{
            if(IS_ROOT){

            }else{
                $where['channel'] = ['in',array_column($channel,'code')];

            }
        }
        !empty($this->param['search_data']) && $where['order_number|name'] = ['like', '%'.$this->param['search_data'].'%'];

        $start_time = 0;
        $end_time = 9999999999;

        !empty($this->param['start_time']) && $start_time = strtotime($this->param['start_time']);
        !empty($this->param['end_time']) && $end_time = strtotime($this->param['end_time']);
        $where['create_time'] = ['between', [$start_time, $end_time]];

        $jj=$this->logicActivityTransportSolar2->getActivitytransportsolarList($where, '', '');
        foreach ($jj as $k=>&$v){
            if(!empty($v['living_district']) and is_numeric($v['birthday'])){
                $v['birthday']=date('Y-m-d H:i:s',$v['birthday']);

            }
            if($v['living_district']>0){
                $living_district=$this->logicArea->getAreaInfo(['id' => $v['living_district']]);
                $v['living_district'] = $living_district['fullname'].'('.$living_district['longitude'].','.$living_district['latitude'].')';
            }
            if($v['birth_district']>0) {
                $birth_district=$this->logicArea->getAreaInfo(['id' => $v['birth_district']]);
                $v['birth_district'] = $birth_district['fullname'].'('.$birth_district['longitude'].','.$birth_district['latitude'].')';
            }
        }
        $this->assign('list', $jj);

        $this->assign('count', $this->logicActivityTransportSolar2->ActivitytransportsolarStat($where, 'count','id'));
        $this->assign('sum', $this->logicActivityTransportSolar2->ActivitytransportsolarStat($where, 'sum','amount'));


        $this->assign('status',[0=>'待支付',1=>'支付完成',-1=>'取消订单',-2=>'已退款']);

        return $this->fetch('activityTransportSolar_list');
    }

    /**
     * 年运测评添加
     */
    public function activitytransportsolarAdd()
    {
        IS_POST && $this->jump($this->logicActivityTransportSolar2->ActivitytransportsolarAdminEdit($this->param));
        return $this->fetch('activitytransportsolar_edit');
    }

    /**
     * 年运测评编辑
     */
    public function activitytransportsolarEdit()
    {
        IS_POST && $this->jump($this->logicActivityTransportSolar2->ActivitytransportsolarAdminEdit($this->param));

        $info = $this->logicActivityTransportSolar2->getActivitytransportsolarInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('activitytransportsolar_edit');
    }

    /**
     * 年运测评统计
     */
    public function activitytransportsolarTotal($id = 0)
    {
        $this->assign('channel',$channel=$this->logicChannelList->getChannelListColumn(['status'=>1,'id'=>['in',session('member_info')['channel']]]));

        $where['status']=['gt',-3];

        if (isset($this->param['status']) and $this->param['status'] != 'all') {
            $where['status'] = $this->param['status'];
        }

        if (isset($this->param['channel']) and $this->param['channel'] != 'all') {
            $where['channel'] = $this->param['channel'];
        }else{
            if(IS_ROOT){

            }else{
                $where['channel'] = ['in',array_column($channel,'code')];

            }
        }
        !empty($this->param['search_data']) && $where['order_number|name'] = ['like', '%'.$this->param['search_data'].'%'];

        $start_time = 0;
        $end_time = 9999999999;

        !empty($this->param['start_time']) && $start_time = strtotime($this->param['start_time']);
        !empty($this->param['end_time']) && $end_time = strtotime($this->param['end_time']);
        $where['create_time'] = ['between', [$start_time, $end_time]];
        $jj= $this->logicActivityTransportSolar2->getActivitytransportsolarList($where, 'channel,count(id) as channel_count,sum(amount) as amount', '',false,'channel');


        $this->assign('list',$jj);


        $this->assign('status',[0=>'待支付',1=>'支付完成',-1=>'取消订单',-2=>'已退款']);

        return $this->fetch('activityTransportsolar_total');
    }
    /**
     * 年运测评删除
     */
    public function activitytransportsolarDel($id = 0)
    {

        $this->jump($this->logicActivityTransportSolar2->ActivitytransportsolarDel(['id' => $id]));
    }

    /**
     * 年运测评删除
     */
    public function wxRefund($id = 0)
    {
        $Transport24Info=$this->logicActivityTransportSolar2->getActivitytransportsolarInfo(['id' => $id]);

        $amount_log=$this->logicAmountLog->getAmountLogInfo(['order_number'=>$Transport24Info['order_number'],'order_id'=>$id]);

        $userConfig = getAppidToken('gh_18c3ed5925ca');

        $exwechat = get_sington_object('payEx', "exwechat\\api\\pay\\wxPay", $userConfig);

        $refundData['out_refund_no'] = 'TU' . time() . 'W' . mt_rand(10, 99) . 'R' . mt_rand(100, 999) . 'D' . $id;
        $refundData['transaction_id'] = $amount_log['transaction_id'];
        $refundData['refund_fee'] =  $amount_log['money']* 100;
        $refundData['total_fee'] = $amount_log['money']* 100;

        $regit = $exwechat->wxRefund($refundData);

        if(($regit['return_code']=='SUCCESS') && ($regit['result_code']=='SUCCESS')){
            $this->logicAmountLog->amountLogEdit(['id'=>$amount_log['id'],'status'=>-2]);
            $this->logicActivityTransportSolar2->ActivitytransportsolarEdit(['id' => $id,'status'=>-2]);
            $this->jump([RESULT_SUCCESS, '退款成功',  url('activitytransportsolarList')]);

        }else if(($regit['return_code']=='FAIL') || ($regit['result_code']=='FAIL')){
            //退款失败
            //原因
            $reason = (empty($regit['err_code_des'])?$regit['return_msg']:$regit['err_code_des']);
        }else{

        }
        $this->jump([RESULT_ERROR, '退款异常，联系开发']);

    }

    /**
     * 数据状态设置
     */
    public function setStatus()
    {

        $this->jump($this->logicAdminBase->setStatus('activitytransportsolar', $this->param));

    }

    /**
     * 排序
     */
    public function setSort()
    {

        $this->jump($this->logicAdminBase->setSort('activitytransportsolar', $this->param));
    }
}
