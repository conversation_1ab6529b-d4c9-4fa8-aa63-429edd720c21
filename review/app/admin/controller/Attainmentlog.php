<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 获取勋章记录控制器
 */
class Attainmentlog extends AdminBase
{

    /**
     * 获取勋章记录列表
     */
    public function attainmentLogList()
    {

        $where = $this->logicAttainmentLog->getAdminWhere($this->param);

        $this->assign('list', $this->logicAttainmentLog->getAttainmentLogList($where, '', ''));

        return $this->fetch('attainmentLog_list');
    }

    /**
     * 获取勋章记录添加
     */
    public function attainmentLogAdd()
    {
        IS_POST && $this->jump($this->logicAttainmentLog->attainmentLogAdminEdit($this->param));
        return $this->fetch('attainmentLog_edit');
    }

    /**
     * 获取勋章记录编辑
     */
    public function attainmentLogEdit()
    {
        IS_POST && $this->jump($this->logicAttainmentLog->attainmentLogAdminEdit($this->param));

        $info = $this->logicAttainmentLog->getAttainmentLogInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('attainmentLog_edit');
    }


    /**
     * 获取勋章记录删除
     */
    public function attainmentLogDel($id = 0)
    {

        $this->jump($this->logicAttainmentLog->attainmentLogDel(['id' => $id]));
    }

    /**
     * 数据状态设置
     */
     public function setStatus()
     {

         $this->jump($this->logicAdminBase->setStatus('AttainmentLog', $this->param));

     }

     /**
      * 排序
     */
     public function setSort()
     {

         $this->jump($this->logicAdminBase->setSort('AttainmentLog', $this->param));
     }
}
