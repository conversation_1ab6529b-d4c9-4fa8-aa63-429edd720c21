<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;
use OSS\Core\OssException;
use OSS\OssClient;

/**
 * 心灵译码订单控制器
 */
class Astrolabedecodereport extends AdminBase
{

    /**
     * 心灵译码订单列表
     */
    public function astrolabeDecodeReportList()
    {

        $where = $this->logicAstrolabeDecodeReport->getAdminWhere($this->param);

        $portList=$this->logicAstrolabeDecodeReport->getAstrolabeDecodeReportList($where, '', '');


        foreach ($portList  as $key=>&$value){
            $value['birth_district_array'] ='无';
            if (!empty($value['birth_district'])) {
                $birth_district = $this->logicArea->getAreaInfo(['id' => $value['birth_district']], 'id,name,fullname');
                $value['birth_district_array']=$birth_district['fullname'];
            }
        }



        $this->assign('list', $portList);



        return $this->fetch('astrolabeDecodeReport_list');
    }

    /**
     * 心灵译码订单添加
     */
    public function astrolabeDecodeReportAdd()
    {
        IS_POST && $this->jump($this->logicAstrolabeDecodeReport->astrolabeDecodeReportAdminEdit($this->param));
        return $this->fetch('astrolabeDecodeReport_edit');
    }

    /**
     * 心灵译码订单编辑
     */
    public function astrolabeDecodeReportEdit()
    {
        IS_POST && $this->jump($this->logicAstrolabeDecodeReport->astrolabeDecodeReportAdminEdit($this->param));

        $info = $this->logicAstrolabeDecodeReport->getAstrolabeDecodeReportInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('astrolabeDecodeReport_edit');
    }

    /**
     * 心灵译码订单编辑
     */
    public function astrolabeDecodeTeenagerEdit()
    {
        IS_POST && $this->jump($this->logicAstrolabeDecodeTeenager->astrolabeDecodeTeenagerAdminEdit($this->param));

        $info = $this->logicAstrolabeDecodeTeenager->getAstrolabeDecodeTeenagerInfo(['r_id' => $this->param['id']], '*');

        foreach ($info as $key=>&$value){
            $value=html_entity_decode($value);
        }
        $this->assign('info', $info);

        return $this->fetch('astrolabeDecodeTeenager_edit');
    }


    /**
     * 心灵译码订单删除
     */
    public function astrolabeDecodeReportDel($id = 0)
    {

        $this->jump($this->logicAstrolabeDecodeReport->astrolabeDecodeReportDel(['id' => $id]));
    }

    /**
     * 数据状态设置
     */
     public function setStatus()
     {

         $this->jump($this->logicAdminBase->setStatus('AstrolabeDecodeReport', $this->param));

     }

     /**
      * 排序
     */
     public function setSort()
     {

         $this->jump($this->logicAdminBase->setSort('AstrolabeDecodeReport', $this->param));
     }

     /**
      * 排序
     */
     public function uploadFile()
     {
         echo '<h1>正在生成......</h1>';
         $AstrolabeDecodeReportInfo=$this->logicAstrolabeDecodeReport->getAstrolabeDecodeReportInfo(['id'=>$this->param['id']]);


         $files ='pdf'.SYS_DS_PROS.'file'.SYS_DS_PROS.$AstrolabeDecodeReportInfo['order_number'].'.pdf';

         if(is_file($files)){
             unlink($files);
         }
         $url ='http://adminxg.robustcn.com/pdf/gets2.php?order_number='.$AstrolabeDecodeReportInfo['order_number'];

         $ch = curl_init();

         //设置选项，包括URL
         curl_setopt($ch, CURLOPT_URL, $url);
         curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
         curl_setopt($ch, CURLOPT_HEADER, 0);
         curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//绕过ssl验证
         curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,10000);
         curl_setopt($ch,CURLOPT_TIMEOUT,30000);
         curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

         //执行并获取HTML文档内容
         $output = curl_exec($ch);

         //释放curl句柄
         curl_close($ch);

        if(!empty($output)){
            echo '<h1>生成成功</h1>';
            $config["ak_id"] = "LTAI4GJdhZgi9gZ1bdbTc5cw";
            $config["ak_secret"] ="******************************";
            $config["bucket_name"] = "xingpan2";
            $config["endpoint"] ="oss-cn-zhangjiakou-internal.aliyuncs.com";
            $config["domain"] = "";

            $oss = new OssClient($config['ak_id'], $config['ak_secret'], $config['endpoint']);

            $files ='pdf'.SYS_DS_PROS.'file'.SYS_DS_PROS.$AstrolabeDecodeReportInfo['order_number'].'.pdf';

            $file_path = PATH_PUBLIC . $files;

            try{
                $fdsfs=$oss->uploadFile($config['bucket_name'],$files,$file_path);

                $this->param['pdf_url']='https://xingpan2.oss-cn-zhangjiakou.aliyuncs.com/'.$files;
                $this->logicAstrolabeDecodeReport->astrolabeDecodeReportAdminEdit(['id'=>$AstrolabeDecodeReportInfo['id'],'pdf_url'=>'https://xingpan2.oss-cn-zhangjiakou.aliyuncs.com/'.$files]);
                echo '<h1>上传成功</h1>';
                echo '<h1>地址:https://xingpan2.oss-cn-zhangjiakou.aliyuncs.com/'.$files.'</h1>';

            }catch (OssException $e){
                printf(__FUNCTION__ . ": FAILED\n");
                printf($e->getMessage() . "\n");

                dump('手动操作1:'.$url);

                echo '<h1>上传异常，请稍后请求</h1>';
            }
        }
     }


}
