<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 成就宣章控制器
 */
class Attainment extends AdminBase
{

    /**
     * 成就宣章列表
     */
    public function attainmentList()
    {

        $where = $this->logicAttainment->getAdminWhere($this->param);

        $this->assign('list', $this->logicAttainment->getAttainmentList($where, '', 'sort asc'));

        $this->assign('type', ['1'=>'连续登录'
            ,'2'=>'连续打卡'
            ,'3'=>'能量任务'
            ,'4'=>'探索答案'
            ,'5'=>'树洞倾诉'
            ,'6'=>'能量倾听'
            ,'7'=>'心智觉知'
            ,'8'=>'情绪提升'
            ,'9'=>'时间管理'
        ]);

        return $this->fetch('attainment_list');
    }

    /**
     * 成就宣章添加
     */
    public function attainmentAdd()
    {
        IS_POST && $this->jump($this->logicAttainment->attainmentAdminEdit($this->param));
        return $this->fetch('attainment_edit');
    }

    /**
     * 成就宣章编辑
     */
    public function attainmentEdit()
    {
        IS_POST && $this->jump($this->logicAttainment->attainmentAdminEdit($this->param));

        $info = $this->logicAttainment->getAttainmentInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('attainment_edit');
    }


    /**
     * 成就宣章删除
     */
    public function attainmentDel($id = 0)
    {

        $this->jump($this->logicAttainment->attainmentAdminDel(['id' => $id]));
    }

    /**
     * 排序
     */
    public function setSort()
    {

        $this->jump($this->logicAdminBase->setSort('Attainment', $this->param));
    }
    /**
     * 数据状态设置
     */
    public function setStatus()
    {

        $this->jump($this->logicAdminBase->setStatus('Attainment', $this->param));
    }
}
