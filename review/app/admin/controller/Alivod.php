<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

use think\paginator\driver\Bootstrap;

/**
 * 阿里点播
 */
class Alivod extends AdminBase
{

    /**
     * 阿里点播列表
     */
    public function alivodList()
    {

     //   $Category_json = $this->logicAlivod->getAlivodCategoryList();

        $page = 1;
        if (!empty($this->param['page'])) {//条件查询
            $page = $this->param['page'];
        }

        $a_json = $this->logicAlivod->searchMedia($page,$start=1588262400,$end=9999999999);


        $list["total"] = $a_json['Total'];
        $list["per_page"] = 40;
        $list["current_page"] = $page;
        $list["last_page"] = ceil($a_json['Total'] / 40);
        $list["data"] = $a_json['MediaList'];

        $query = array();

        if (input("post.CateId")) {//条件查询
            $query = ['CateId' => input("post.CateId")];
        } elseif (input("get.CateId")) {//分页查询
            $query = ['CateId' => input("get.CateId")];
        } else {//初始或者无条件的按钮操作
            $query = ['CateId' => input("get.CateId")];
        }

        $data = $a_json['MediaList'];   //需要输出的数组

        $curpage = input('page') ? input('page') : 1;//当前第x页，有效值为：1,2,3,4,5...

        $listRow = 40;//每页10行记录

        $p = Bootstrap::make($data, $listRow, $curpage, $a_json['Total'], false, [
            'var_page' => 'page',
            'path' => url('alivodList'),//这里根据需要修改url
            'query' => $query,
            'fragment' => '',
        ]);

        $p->appends($_GET);

        $this->assign('plistpage', $p->render());


        $this->assign('list', $list);

        return $this->fetch('alivod_list');
    }
    /**
     * 阿里点播详细
     */
    public function vodFileUrl($VideoId){
        $a_json = $this->logicAlivod-> getMezzanineInfo($VideoId);

        header("Location:".$a_json['Mezzanine']['FileURL'] );
    }

    /**
     * 阿里点播删除
     */
    public function vodFileDel($VideoId)
    {

        $this->jump($this->logicAlivod->deleteVideos($VideoId));
    }

    /**
     * 阿里点播获取秘钥
     */
    public function dailyMediaUploadVideo()
    {

        $title=$this->param['Title'];
        $file_name=$this->param['FileName'];
        $description=$this->param['Description'];
        $CateId=$this->param['CateId'];
        $cover_url = "";
        $tags = "";
        if(!empty($this->param['CoverURL'])){
            $cover_url=$this->param['CoverURL'];
        }
        $obj = $this->logicAlivod->createVod($title,$file_name, $description, $cover_url, $tags,$CateId);
        return json_encode($obj);
    }
    /**
     * 每日任务添加
     */
    public function vodFileAdd()
    {
        IS_POST && $this->jump($this->logicAlivod->addVideoInfo($this->param));

        $this->assign('Category', $Category_json = $this->logicAlivod->getAlivodCategoryList()['SubCategories']['Category']);

        return $this->fetch('alivod_edit');
    }

    /**
     * 阿里点播修改
     */
    public function vodFileEdit($VideoId)
    {
        IS_POST && $this->jump($this->logicAlivod->updateVideoInfo($this->param));

        $a_json = $this->logicAlivod-> getVideoInfo($VideoId)['Video'];

        $a_Mezzanine = $this->logicAlivod-> getMezzanineInfo($VideoId)["Mezzanine"];

        $a_json['FileURL']=$a_Mezzanine['FileURL'];
        $a_json['FileName']=$a_Mezzanine['FileName'];

        $a_json['cover_id']=0;

        if(!empty($a_json['CoverURL'])){
            $PictureInfo = $this->logicFile->getPictureInfo(['url'=>$a_json['CoverURL']]);

            $a_json['cover_id']=$PictureInfo['id'];
        }

        $this->assign('info', $a_json);

        $this->assign('Category', $Category_json = $this->logicAlivod->getAlivodCategoryList()['SubCategories']['Category']);

        return $this->fetch('alivod_edit');
    }


    /**
     * 阿里点播列表
     */
    public function alivodListJson()
    {

        //   $Category_json = $this->logicAlivod->getAlivodCategoryList();


        $current_page = 1;
        if (!empty($this->param['current_page'])) {//条件查询
            $current_page = $this->param['current_page'];
        }

        $a_json = $this->logicAlivod->searchMedia($current_page,$start=1588262400,$end=9999999999);


        $list["total"] = $a_json['Total'];
        $list["per_page"] = 40;
        $list["current_page"] = $current_page;
        $list["last_page"] = ceil($a_json['Total'] / 40);
        $list["data"] = $a_json['MediaList'];

        return json($list);
    }

    /**
     * 阿里点播列表
     */
    public function alivodFileUrl()
    {
        $a_json = $this->logicAlivod-> getMezzanineInfo($this->param['videoid']);

        $pictureInfo=$this->logicFile->getPictureInfo(['url'=>$this->param['picurl']]);


        $a_json['Mezzanine']['cover_id']=$pictureInfo['id'];

        return json($a_json['Mezzanine']);
    }
}
