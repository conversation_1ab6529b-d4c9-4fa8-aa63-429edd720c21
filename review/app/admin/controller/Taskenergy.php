<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 任务能量控制器
 */
class Taskenergy extends AdminBase
{

    /**
     * 任务能量列表
     */
    public function taskEnergyList()
    {

        $where = $this->logicTaskEnergy->getAdminWhere($this->param);

        $this->assign('list', $this->logicTaskEnergy->getTaskEnergyList($where, '', ''));

        return $this->fetch('taskEnergy_list');
    }

    /**
     * 任务能量添加
     */
    public function taskEnergyAdd()
    {
        IS_POST && $this->jump($this->logicTaskEnergy->taskEnergyAdminEdit($this->param));
        return $this->fetch('taskEnergy_edit');
    }

    /**
     * 任务能量编辑
     */
    public function taskEnergyEdit()
    {
        IS_POST && $this->jump($this->logicTaskEnergy->taskEnergyAdminEdit($this->param));

        $info = $this->logicTaskEnergy->getTaskEnergyInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('taskEnergy_edit');
    }


    /**
     * 任务能量删除
     */
    public function taskEnergyDel($id = 0)
    {

        $this->jump($this->logicTaskEnergy->taskEnergyDel(['id' => $id]));
    }

    /**
     * 数据状态设置
     */
     public function setStatus()
     {

         $this->jump($this->logicAdminBase->setStatus('TaskEnergy', $this->param));

     }

     /**
      * 排序
     */
     public function setSort()
     {

         $this->jump($this->logicAdminBase->setSort('TaskEnergy', $this->param));
     }
}
