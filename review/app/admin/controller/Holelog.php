<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 树洞记录控制器
 */
class Holelog extends AdminBase
{

    /**
     * 树洞记录列表
     */
    public function holeLogList()
    {

        $where = $this->logicHoleLog->getAdminWhere($this->param);

        $this->assign('list', $this->logicHoleLog->getHoleLogList($where, '', ''));

        return $this->fetch('holeLog_list');
    }

    /**
     * 树洞记录添加
     */
    public function holeLogAdd()
    {
        IS_POST && $this->jump($this->logicHoleLog->holeLogEdit($this->param));
        return $this->fetch('holeLog_edit');
    }

    /**
     * 树洞记录编辑
     */
    public function holeLogEdit()
    {
        IS_POST && $this->jump($this->logicHoleLog->holeLogEdit($this->param));

        $info = $this->logicHoleLog->getHoleLogInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('holeLog_edit');
    }


    /**
     * 树洞记录删除
     */
    public function holeLogDel($id = 0)
    {

        $this->jump($this->logicHoleLog->holeLogDel(['id' => $id]));
    }
}
