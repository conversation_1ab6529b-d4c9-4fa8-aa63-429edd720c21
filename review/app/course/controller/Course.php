<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\course\controller;
use exwechat\api\pay\wxXcPay;
/**
 * 课程控制器
 */
class Course extends UserBase
{

    /**
     * 课程无分页列表
     */
    public function courseColumn()
    {

        $where=array();

        !empty($this->param_data['category_id']) && $where['category_id'] = ['in',$this->param_data['category_id']];

        $data['data']=$this->logicCourse->getCourseList($where, '*', 'id asc', false);

        $data['imgs']=$this->logicFile->getPictureListUrl($data['data'],'cover_id');

		return $this->apiReturn($data);
    }

    /**
     * 课程信息
     */
    public function courseInfo()
    {

        $user_id=$this->param_data['user_id'];

        $vipData = $this->logicVip->getUserVipData($user_id);

        $data['course']=$this->logicCourse->getCourseInfo(['id'=>$this->param_data['id']]);

        if(empty($data['course'])){
            return $this->apiReturn([API_CODE_NAME => 1000011, API_MSG_NAME => '数据不存在']);
        }

        $this->logicCourse->courseEdit(['id'=>$this->param_data['id'],'views'=>$data['course']['views']+1]);

        $data['course']['vip']=$vipData;

        $data['course']['isbuy']=0;

        $course_log_id=$this->logicCoursePayLog->getCoursePayLogSingleInfo(['user_id'=>$user_id,'course_id'=>$this->param_data['id'],'order_status'=>1],'id');

        if(!empty($course_log_id)){
            $data['course']['isbuy']=1;
        }

        $data['course']['cover_url']=$this->logicFile->getPictureUrl($data['course']['cover_id']);

        $data['directory']=$this->logicCourseDirectory->getCourseDirectoryList(['c_id'=>$this->param_data['id'],'status'=>1], 'id,pid,sort,title,type,is_free,c_id,create_time', 'sort DESC',false);

        return $this->apiReturn($data);

    }
    /**
     * 课程课程发起订单
     */
    public function coursePayAdd()
    {
        $course_id['id'] = $this->param_data['course_id'];

        $user_id = $this->param_data['user_id'];

        $vipData = $this->logicVip->getUserVipData($user_id);

        $courseInfo=$this->logicCourse->getCourseInfo($course_id);

        $amount=round($courseInfo['price']*$vipData['discount']/100, 2);

        if ($courseInfo['status'] == -1) {
            return $this->apiReturn([API_CODE_NAME => 1030101, API_MSG_NAME => '当前课程包已过期']);
        }else if ($courseInfo['status'] == 0) {
            return $this->apiReturn([API_CODE_NAME => 1030102, API_MSG_NAME => '当前课程包正在审核']);
        }else if ($courseInfo['status'] == -2) {
            return $this->apiReturn([API_CODE_NAME => 1030103, API_MSG_NAME => '当前课程包审核不通过']);
        }

        $payLogInfo=$this->logicCoursePayLog->getCoursePayLogSingleInfo(['user_id'=>$user_id,'course_id'=>$course_id['id'],'order_status'=>0]);

        if($payLogInfo){
            $courseDdata['id'] =$payLogInfo['id'];
            $courseDdata['order_id'] =$payLogInfo['id'];
        }

        $courseDdata['order_number'] = 'C' . time() . 'U' . $this->param_data['user_id'] . 'R' . mt_rand(100000, 999999);
        $courseDdata['course_id']= $this->param_data['course_id'];

        $courseDdata['course_id']= $course_id['id'];
        $courseDdata['amount']= $amount;
        $courseDdata['price']= $courseInfo['price'];
        $courseDdata['user_id']= $this->param_data['user_id'];
        $courseDdata['order_status']= 0;

        $payLogPayId = $this->logicCoursePayLog->coursePayLogEdit($courseDdata);

        if(empty($payLogInfo)){
            $courseDdata['order_id'] =$payLogPayId;
        }

        return $this->apiReturn($courseDdata);

    }
    /**
     * @return mixed课程发起支付
     */
    public function coursePay()
    {
        $activityWhere['id'] = $this->param_data['order_id'];
        $activityWhere['user_id'] = $this->param_data['user_id'];

        $payLogInfo=$this->logicCoursePayLog->getCoursePayLogSingleInfo($activityWhere);


        if(empty($payLogInfo)){
            return $this->apiReturn([API_CODE_NAME => 1030105, API_MSG_NAME => '当前订单异常请重新下单']);
        }
        if($payLogInfo['order_status']!=0){
            return $this->apiReturn([API_CODE_NAME => 1030106, API_MSG_NAME => '当前订单异常']);
        }
        $total_fee=$payLogInfo['amount'];
        if(!empty($this->param_data['additional']['user_discounts_id'])){

            $userDiscountsInfo=$this->logicUserDiscounts->getUserDiscountsInfo(['id'=>$this->param_data['additional']['user_discounts_id']]);
            $total_fee=$payLogInfo['amount']-$userDiscountsInfo['money'];
            $updataPayLog['user_discounts_id']=$this->param_data['additional']['user_discounts_id'];
            $updataPayLog['discount']=$userDiscountsInfo['money'];

            $this->logicUserDiscounts->userDiscountsEdit(['id'=>$updataPayLog['user_discounts_id'],'status'=>-1]);
        }
        if($total_fee<=0){
            $total_fee=0.01;
        }

        $userConfig=getAppidToken($this->param_data['toUserName'], true);
        $payData['appid']=$userConfig['appid'];
        $payData['openid']=$this->param_data['openid'];
        $payData['mch_id']=$userConfig['mch_id'];
        $payData['key']=$userConfig['mch_key'];
        $payData['out_trade_no']=$payLogInfo['order_number'];
        $payData['body']=empty($this->param_data['remark'])?'教学课程:'.$payLogInfo['title']:$this->param_data['remark'];
        $payData['total_fee']=$total_fee;
        $payData['total_fee']=0.01;
        $payData['notify_url']=DOMAIN.'/wexc.php/Paynotify/payNotify';

        $exwechat = get_sington_object('payEx', wxXcPay::class,$payData);
        $regit =$exwechat->wxxcPay();
        $regit['amount']=$payData['total_fee'];
        $regit['total_fee']=$payData['total_fee'];

        $updataPayLog['amount']=$total_fee;

        $this->logicCoursePayLog->coursePayLogEdit($updataPayLog,$activityWhere);


        return $this->apiReturn($regit);
    }
    /**
     * @return mixed课程发起支付
     */
    public function coursePayLogInfo()
    {
        $where['course_id']=$this->param_data['course_id'];

        $where['id']=$this->param_data['id'];

        $regit=$this->logicCoursePayLog->getCoursePayLogInfo($where);

        $regit['cover_url']=$this->logicFile->getPictureUrl($regit['cover_id']);

        $regit['discounts']=$this->logicUserDiscounts->deductionUserDiscounts($this->param_data['user_id'], [0,$where['course_id']]);


        return $this->apiReturn($regit);
    }
    /**
     * 活动报名列表删除
     */
    public function coursePayDel()
    {
        $data['order_status']=-1;

        $data['id']=$this->param_data['id'];

        $regit = $this->logicCoursePayLog->coursePayLogEdit($data);

        return $this->apiReturn(['id' => $regit]);

    }
    /**
     * 课程支付订单列表
     */
    public function coursePayList()
    {
        $where = $this->logicCoursePayLog->getweiWhere($this->param_data);

        $data=$this->logicCoursePayLog->getCoursePayLogList($where);
        $data=$data->toArray();
        $data['imgs']=$this->logicFile->getPictureListUrl($data['data'],'cover_id');

        return $this->apiReturn($data);
    }
}
