<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 树洞记录逻辑
 */
class HoleLog extends LogicBase
{

      /**
       * 获取树洞记录搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获树洞记录单条信息
      */
     public function getHoleLogInfo($where = [], $field = '*')
     {

        return $this->modelHoleLog->getInfo($where, $field);
     }

    /**
     * 获取树洞记录列表
     */

    public function getHoleLogList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelHoleLog->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取树洞记录无分页列表
     */
    public function getHoleLogColumn($where = [], $field = '', $key = '')
    {
        return $this->modelHoleLog->getColumn($where, $field , $key);
    }

    /**
     * 树洞记录单条编辑
     */
    public function holeLogEdit($data = [])
    {
		
        $result = $this->modelHoleLog->setInfo($data);
        
        return $result ? $result : $this->modelHoleLog->getError();
    }

    /**
     * 树洞记录删除
     */
    public function holeLogDel($where = [], $is_true = false)
    {

        $result = $this->modelHoleLog->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelHoleLog->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取树洞记录搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 树洞记录单条编辑
     */
    public function holeLogAdminEdit($data = [])
    {


        $url = url('holeLogList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelHoleLog->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '树洞记录' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '树洞记录操作成功', $url] : [RESULT_ERROR, $this->modelHoleLog->getError()];
    }

    /**
     * 树洞记录删除
     */
    public function holeLogAdminDel($where = [])
    {

        $result = $this->modelHoleLog->deleteInfo($where);
        
        $result && action_log('删除', '树洞记录删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '树洞记录删除成功'] : [RESULT_ERROR, $this->modelHoleLog->getError()];
    }
}
