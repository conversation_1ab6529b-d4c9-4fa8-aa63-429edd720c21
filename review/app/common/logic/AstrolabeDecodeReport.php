<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 心灵译码逻辑
 */
class AstrolabeDecodeReport extends LogicBase
{

    /**
     * 获取心灵译码搜索条件
     */
    public function getWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 获心灵译码单条信息
     */
    public function getAstrolabeDecodeReportInfo($where = [], $field = '*')
    {

        return $this->modelAstrolabeDecodeReport->getInfo($where, $field);
    }

    /**
     * 获取心灵译码列表
     */

    public function getAstrolabeDecodeReportList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelAstrolabeDecodeReport->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取心灵译码无分页列表
     */
    public function getAstrolabeDecodeReportColumn($where = [], $field = '', $key = '')
    {
        return $this->modelAstrolabeDecodeReport->getColumn($where, $field, $key);
    }
    /**
     * 心灵译码单条编辑
     */
    public function astrolabeDecodeReportWuEdit($data = [])
    {
        $result = $this->modelAstrolabeDecodeReport->setInfo($data);

        return $result ? $result : $this->modelAstrolabeDecodeReport->getError();
    }
    /**
     * 心灵译码单条编辑
     */
    public function astrolabeDecodeReportEdit($data = [])
    {

        $data['birthday']=strtotime($data['birthday']);


        $areaInfo = $this->modelArea->getInfo(['id' => $data['birth_district']]);

        $birthdayToTime = $data['birthday'] - $areaInfo['tz'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $house = $areaInfo['longitude'] . ',' . $areaInfo['latitude'] . ',k';
        $arr = [
            'b' => $utdatenow,
            'p' => '0123456789Hm',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec',

        ];

        $p = get_sington_object('SweTest', "astrology\\SweTest");


        $planet_json = $p->calculate($arr, ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'H', 'm','21','11']);

        $data['planet_json'] = json_encode($planet_json);

        $life_lin_score = array();

        $plant = array('sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn');


        $planet = $planet_json['planet'];

        foreach ($planet as $key => $value) {

            if ($key < 7) {
                $life_array[$key] = $value;   //本命先天数据
                $life_lin_score[$plant[$key]] = $this->logicObjectiveEnergy->calculate($planet[$key]['sign_id'], $key, (int)$planet[$key]['deg']);
            }
        }

        $constellation_plant = array(4, 3, 2, 1, 0, 2, 3, 4, 5, 6, 6, 5);

        arsort($life_lin_score);

        $planet_keys = array_keys($life_lin_score);

        if ($life_lin_score[$planet_keys[0]] == $life_lin_score[$planet_keys[1]]) {

            $life_lin_score[$planet_keys[0]] += $life_lin_score[$plant[$constellation_plant[$planet[array_search($planet_keys[0], $plant)]['sign_id']]]];

            $life_lin_score[$planet_keys[1]] += $life_lin_score[$plant[$constellation_plant[$planet[array_search($planet_keys[1], $plant)]['sign_id']]]];

        }

        if ($life_lin_score[$planet_keys[5]] == $life_lin_score[$planet_keys[6]]) {

            $life_lin_score[$planet_keys[5]] += $life_lin_score[$plant[$constellation_plant[$planet[array_search($planet_keys[5], $plant)]['sign_id']]]];

            $life_lin_score[$planet_keys[6]] += $life_lin_score[$plant[$constellation_plant[$planet[array_search($planet_keys[6], $plant)]['sign_id']]]];

        }
        arsort($life_lin_score);
        $planet_keys = array_keys($life_lin_score);
        if ($life_lin_score[$planet_keys[0]] == $life_lin_score[$planet_keys[1]]) {

            $life_lin_score[$planet_keys[0]] += 2;

            $life_lin_score[$planet_keys[1]] += 1;

        }
        if ($life_lin_score[$planet_keys[5]] == $life_lin_score[$planet_keys[6]]) {

            $life_lin_score[$planet_keys[6]] -= 1;

        }
        $data['score_json'] = json_encode($life_lin_score);
        $data['status'] = 0;
        $data['order_number'] = 'DR' . time() . 'W' . mt_rand(10, 99) . 'R' . mt_rand(100, 999);;
        unset($data['choose_appid']);
        unset($data['sms_code']);
        $result = $this->modelAstrolabeDecodeReport->setInfo($data);

//        $amountLog['order_number'] = $data['order_number'];
//        $amountLog['order_id'] = $result;
//        $amountLog['type'] = 5;
//        $amountLog['model'] = 'astrolabe_DecodeReport';
//        $amountLog['money'] = $data['amount'];
//        $amountLog['remark'] = '心灵译码';
//
//        $this->logicAmountLog->amountLogEdit($amountLog);

        return $result ? ['id'=>$result,'order_number'=>$data['order_number']] : $this->modelAstrolabeDecodeReport->getError();
    }

    /**
     * 心灵译码删除
     */
    public function astrolabeDecodeReportDel($where = [], $is_true = false)
    {

        $result = $this->modelAstrolabeDecodeReport->deleteInfo($where, $is_true);

        return $result ? $result : $this->modelAstrolabeDecodeReport->getError();
    }


    //Admin模块操作

    /**
     * 获取心灵译码搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['name|order_number|mobile|code'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 心灵译码单条编辑
     */
    public function astrolabeDecodeReportAdminEdit($data = [])
    {


        $url = url('astrolabeDecodeReportList');

        $data['member_id'] = MEMBER_ID;

        $result = $this->modelAstrolabeDecodeReport->setInfo($data);

        $handle_text = empty($data['id']) ? '新增' : '编辑';

        $result && action_log($handle_text, '心灵译码' . $handle_text . '，data：' . http_build_query($data));

        return $result ? [RESULT_SUCCESS, '心灵译码操作成功', $url] : [RESULT_ERROR, $this->modelAstrolabeDecodeReport->getError()];
    }

    /**
     * 心灵译码删除
     */
    public function astrolabeDecodeReportAdminDel($where = [])
    {

        $result = $this->modelAstrolabeDecodeReport->deleteInfo($where);

        $result && action_log('删除', '心灵译码删除，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '心灵译码删除成功'] : [RESULT_ERROR, $this->modelAstrolabeDecodeReport->getError()];
    }


    //Admin模块操作

    /**
     * 获取心灵译码搜索条件
     */
    public function uploadFile($order_number = '')
    {

        $file_path ='pdf'.SYS_DS_PROS.'file'.SYS_DS_PROS.$order_number.'.pdf';

        $storage_driver = config('storage_driver');


        if (empty($storage_driver)) {

            return false;
        }

        $driver = SYS_DRIVER_DIR_NAME . $storage_driver;

        $storage_result = $this->serviceStorage->$driver->uploadStaticFile($file_path);

    }
}
