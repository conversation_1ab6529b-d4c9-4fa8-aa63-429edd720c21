<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 分销用户逻辑
 */
class DistributeUser extends LogicBase
{

      /**
       * 获取分销用户搜索条件
      */
      public function getAdminWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获分销用户单条信息
      */
     public function getDistributeUserInfo($where = [], $field = '*')
     {

        return $this->modelDistributeUser->getInfo($where, $field);
     }

    /**
     * 获取分销用户列表
     */

    public function getDistributeUserList($where = [], $field = '', $order = '')
    {
        return $this->modelDistributeUser->getList($where, $field, $order);
    }

    /**
     * 获取分销用户无分页列表
     */
    public function getDistributeUserColumn($where = [], $field = '', $key = '')
    {
        return $this->modelDistributeUser->getColumn($where, $field , $key);
    }

    /**
     * 分销用户单条编辑
     */
    public function distributeUserEdit($data = [])
    {


        $url = url('distributeUserList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelDistributeUser->setInfo($data);
        
        $handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '分销用户' . $handle_text . '，data：' .http_build_query($data));
        
        return $result ? [RESULT_SUCCESS, '分销用户修改成功', $url] : [RESULT_ERROR, $this->modelDistributeUser->getError()];
    }

    /**
     * 分销用户删除
     */
    public function distributeUserDel($where = [])
    {

        $result = $this->modelDistributeUser->deleteInfo($where);
        
        $result && action_log('删除', '分销用户删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '分销用户删除成功'] : [RESULT_ERROR, $this->modelDistributeUser->getError()];
    }

}
