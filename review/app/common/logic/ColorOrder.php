<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 颜色测试逻辑
 */
class ColorOrder extends LogicBase
{

      /**
       * 获取颜色测试搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获颜色测试单条信息
      */
     public function getColorOrderInfo($where = [], $field = '*')
     {

        return $this->modelColorOrder->getInfo($where, $field);
     }

    /**
     * 获取颜色测试列表
     */

    public function getColorOrderList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelColorOrder->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取颜色测试无分页列表
     */
    public function getColorOrderColumn($where = [], $field = '', $key = '')
    {
        return $this->modelColorOrder->getColumn($where, $field , $key);
    }

    /**
     * 颜色测试单条编辑
     */
    public function colorOrderEdit($data = [])
    {
		
        $result = $this->modelColorOrder->setInfo($data);
        
        return $result ? $result : $this->modelColorOrder->getError();
    }


    /**
     * 颜色测试单条编辑
     */
    public function colorOrderUpdate($where = [], $data = [])
    {

        $result = $this->modelColorOrder->updateInfo($where, $data);

        return $result ? $result : $this->modelColorOrder->getError();
    }

    /**
     * 颜色测试删除
     */
    public function colorOrderDel($where = [], $is_true = false)
    {

        $result = $this->modelColorOrder->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelColorOrder->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取颜色测试搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 颜色测试单条编辑
     */
    public function colorOrderAdminEdit($data = [])
    {


        $url = url('colorOrderList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelColorOrder->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '颜色测试' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '颜色测试操作成功', $url] : [RESULT_ERROR, $this->modelColorOrder->getError()];
    }

    /**
     * 颜色测试删除
     */
    public function colorOrderAdminDel($where = [])
    {

        $result = $this->modelColorOrder->deleteInfo($where);
        
        $result && action_log('删除', '颜色测试删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '颜色测试删除成功'] : [RESULT_ERROR, $this->modelColorOrder->getError()];
    }
}
