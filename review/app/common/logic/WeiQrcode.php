<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
use exwechat\api\account\QRCode;
//use exwechat\api\account\shortUrl;
/**
 * 会员
 */
class WeiQrcode extends LogicBase
{


    public  function __call($method, $params)
    {
        // 自动化寻找类并且实例化执行操作
        return call_user_func_array([get_sington_object('QRCodeEx', QRCode::class,getAccessToken()['access_token']), $method], $params);
        //return call_user_func_array([get_sington_object('shortUrlEx', shortUrl::class,getAccessToken()['access_token']), $method], $params);
    }
    /**
     * 获取会员搜索条件
     */
    public function getWhere($data = [])
    {
        $where = [];

        !empty($data['search_data']) && $where['name|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
    /**
     * 获取会员列表
     */
    public function getWeiQrcodeList($where = [], $field = true, $order = '', $paginate = 10)
    {
        return $this->modelWeiQrcode->getList($where, $field, $order, $paginate);
    }


    /**
     * 获取会员列表
     */
    public function setAllListData($data)
    {

        return $this->modelWeiQrcode->setAllList($data);
    }

    /**
     * 二维码后台添加
     */
    public function weiQrcodeAdd($data = [])
    {

        $validate_result = $this->validateWeiQrcode->scene('add')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateWeiQrcode->getError()]; endif;

        $url = url('weiQrcodeList');

        $Qr_data=self::permanentQR($data['action_name'],$data['scene_str'],$data['expire_seconds']);

        if (!empty($Qr_data['errcode'])) : return [RESULT_ERROR,$Qr_data['errmsg']]; endif;

        $data['ticket']=$Qr_data['ticket'];
        $data['url']=$Qr_data['url'];

        $data['qrcode_url']=self::QRshow($Qr_data['ticket']);

        downFile($data['qrcode_url'], './upload/qrcode/'.$data['scene_str'].'_'.MEMBER_ID.'.jpg');
        $data['local_url']='./upload/qrcode/'.$data['scene_str'].'.jpg';

        $result = $this->modelWeiQrcode->setInfo($data);


        $result && action_log('新增', '新增二维码：' . $data['scene_name']);

        return $result ? [RESULT_SUCCESS, '二维码添加成功', $url] : [RESULT_ERROR, $this->modelWeiQrcode->getError()];
    }

    /**
     *会员编辑
     */
    public function weiQrcodeEdit($data = [])
    {

        $validate_result = $this->validateWeiQrcode->scene('edit')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateWeiQrcode->getError()]; endif;

        $url = url('weiQrcodeList');

        $result = $this->modelWeiQrcode->setInfo($data);

        $result && action_log('编辑', '编辑二维码，name：' . $data['wechat']);

        return $result ? [RESULT_SUCCESS, '微信二维码编辑成功', $url] : [RESULT_ERROR, $this->modelWeiQrcode->getError()];
    }

    /**
     * 会员删除
     */
    public function weiQrcodeDel($where = [])
    {

        $result = $this->modelWeiQrcode->deleteInfo($where);

        $result && action_log('删除', '删除二维码，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '微信二维码成功'] : [RESULT_ERROR, $this->modelWeiQrcode->getError()];
    }
    /**
     *会员编辑
     */
    public function weiQrcodeSet($data = [])
    {

        $result = $this->modelWeiQrcode->setInfo($data);

        $result && action_log('编辑', '二维码配置，name：' . $data['id']);

        return $result ? [RESULT_SUCCESS, '二维码配置成功'] : [RESULT_ERROR, $this->modelWeiQrcode->getError()];
    }
    /**
     * 获取会员信息
     */
    public function getWeiQrcodeInfo($where = [], $field = true)
    {

        return $this->modelWeiQrcode->getInfo($where, $field);
    }
    /**
     * 获取会员信息
     */
    public function getWeiQrcodestat($where = [], $field = 'id', $stat_type = 'count')
    {

        return $this->modelWeiQrcode->stat($where, $stat_type, $field);
    }
    /**
     * 获取会员信息
     */
    public function updateWeiQrcode($where = [], $data = [])
    {

        return $this->modelWeiQrcode->updateInfo($where , $data);
    }
    /**
     * 获取会员信息
     */
    public function addWeiQrcode($data = [])
    {

        return $this->modelWeiQrcode->setInfo($data);
    }
    /**
     * 积分次数增减
     */
    public function setInc($where = [],$field = 'score', $number = 1,$setType='setInc')
    {

        return  $this->modelWeiQrcode->setIncDecInfo($where, $field, $number,$setType);
    }
}
