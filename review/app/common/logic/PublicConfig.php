<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 配置逻辑
 */
class PublicConfig extends LogicBase
{
    
    /**
     * 获取配置列表
     */
    public function getPublicConfigList($where = [], $field = true, $order = '', $paginate = 0)
    {
        
        return $this->modelPublicConfig->getList($where, $field, $order, $paginate);
    }
    
    /**
     * 获取配置列表过滤
     */
    public function getPublicConfigListFilter($param = [])
    {
        
        $where = [];
        
        $group = empty($param['group']) ? DATA_DISABLE : (int)$param['group'];
        
        !empty($group) && $where['group'] = $group;
        
        !empty($param['search_data']) && $where['name|title'] = ['like', '%'.(string)$param['search_data'].'%'];
        
        $sort = 'sort asc, create_time desc';
        
        if (!empty($param['order_field'])) {
            
            
            $sort = empty($param['order_val']) ? $param['order_field'] . ' asc' : $param['order_field'] . ' desc';
        }
        
        $data['list'] = $this->getPublicConfigList($where, true, $sort);
        
        $data['group'] = $group;
        
        return $data;
    }
    
    /**
     * 获取配置信息
     */
    public function getPublicConfigInfo($where = [], $field = true)
    {
        
        return $this->modelPublicConfig->getInfo($where, $field);
    }
    
    /**
     * 系统设置
     */
    public function settingSave($data = [])
    {

        $where = array('name' => $data['name']);

        $this->modelPublicConfig->updateInfo($where, ['value' => $data['value']]);
        
        action_log('设置', '系统设置保存');
        
        return [RESULT_SUCCESS, '设置保存成功'];
    }
    
    /**
     * 配置添加
     */
    public function PublicConfigAdd($data = [])
    {
        

        
        $result = $this->modelPublicConfig->setInfo($data);

    }
    
    /**
     * 配置编辑
     */
    public function publicConfigEdit($data = [])
    {
        
        $validate_result = $this->validatePublicConfig->scene('edit')->check($data);
        
        if (!$validate_result) {
            
            return [RESULT_ERROR, $this->validatePublicConfig->getError()];
        }
        
        $url = url('publicConfigList', array('group' => $data['group'] ? $data['group'] : 0));
        
        $result = $this->modelPublicConfig->setInfo($data);
        
        $result && action_log('编辑', '编辑配置，name：' . $data['name']);
        
        return $result ? [RESULT_SUCCESS, '配置编辑成功', $url] : [RESULT_ERROR, $this->modelPublicConfig->getError()];
    }
    
    /**
     * 配置删除
     */
    public function publicConfigDel($where = [])
    {
        
        $result = $this->modelPublicConfig->deleteInfo($where);
        
        $result && action_log('删除', '删除配置，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '菜单删除成功'] : [RESULT_ERROR, $this->modelPublicConfig->getError()];
    }
}
