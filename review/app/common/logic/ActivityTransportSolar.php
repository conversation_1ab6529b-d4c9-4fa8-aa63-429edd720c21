<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 年运-太阳返照逻辑
 */
class ActivityTransportSolar extends LogicBase
{

    /**
     * 获取年运-太阳返照搜索条件
     */
    public function getWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 获年运-太阳返照单条信息
     */
    public function getActivityTransportSolarInfo($where = [], $field = '*')
    {

        return $this->modelActivityTransportSolar->getInfo($where, $field);
    }

    /**
     * 获取年运-太阳返照列表
     */

    public function getActivityTransportSolarList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelActivityTransportSolar->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取年运-太阳返照无分页列表
     */
    public function getActivityTransportSolarColumn($where = [], $field = '', $key = '')
    {
        return $this->modelActivityTransportSolar->getColumn($where, $field, $key);
    }
    public function activityTransportSolarEdit($data = [],$where=[])
    {
        $result = $this->modelActivityTransportSolar->setInfo($data, $where);

        return $result ? $result : $this->modelActivityTransportSolar->getError();
    }

    /**
     * 抽签记录单条编辑
     */
    public function activityTransportSolarStat($where = [], $stat_type = 'count', $field = 'id')
    {

        $result = $this->modelActivityTransportSolar->stat($where, $stat_type, $field);

        return $result ? $result : $this->modelActivityTransportSolar->getError();
    }
    /**
     * 年运-太阳返照单条编辑
     */
    public function activityTransportSolarApiEdit($data = [])
    {
        $arrDTSDates = config('ext_astrology.arrDTSDates');

        $dst = 0;
        foreach ($arrDTSDates as $dtkey => $dtvale) {
            if (strtotime($data['birthday']) >= strtotime($dtvale[0]) and strtotime($data['birthday']) <= strtotime($dtvale[1])) {
                $dst = 1;
            }
        }

        $data['birthday'] = strtotime($data['birthday']);

        $areaInfo = $this->modelArea->getInfo(['id' => $data['birth_district']]);

        $birthdayToTime = $data['birthday'] - ($areaInfo['tz'] - $dst) * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $house = $areaInfo['longitude'] . ',' . $areaInfo['latitude'] . ',k';

        $arr = [
            'b' => $utdatenow,
            'p' => '0123456789',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $p = get_sington_object('SweTest', "astrology\\SweTest");

        $starsCode = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '18', '19'];

        $life_calculate = $p->calculate($arr, $starsCode);

        //本命分值计算
        $life_lin_score = array();

        $life_planet = $life_calculate['planet'];

        foreach ($life_planet as $key => $value) {

            if ($key < 7) {
                $life_lin_score[$value['english_name']] = $this->logicObjectiveEnergy->calculateNew($life_planet[$key]['sign_id'], $key, (int)$life_planet[$key]['deg']);
            }
        }
        $data['order_number'] = $data['order_number'];


            $data['life_lin_score'] = json_encode($life_lin_score);
            //本命分值计算结束

            $planet_json['life'] = $life_calculate;

            $defug = $life_calculate['planet'][0]['longitude'];

            $birthday = $data['birthday'];

            $birth_month = date('m', $birthday);
            $birth_day = date('d', $birthday);
            $transit_year = $data['transit_year'];

            $transit_birth = mktime(0, 0, 0, $birth_month, $birth_day - 4, $transit_year);

            $utdatenow = date('d.m.Y', $transit_birth);

            $utnow = '12:0:0';

            $arr2 = [
                'b' => $utdatenow,
                'ut' => $utnow,
                'p' => '0',
                'n' => '6',
                's' => '1',
                'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'e' => 'swe',
                'head',
                'roundsec',

            ];

            $behold_planet = $p->transitsProgressed($arr2, $defug, 'd') + $areaInfo['tz'] * 3600;


            if (abs($behold_planet - time()) > 86400 * 183) {
                $transit_birth = mktime(0, 0, 0, $birth_month, $birth_day - 3, $transit_year - 1);

                $utdatenow = date('d.m.Y', $transit_birth);
                $arr2['b'] = $utdatenow;

                $behold_planet = $p->transitsProgressed($arr2, $defug, 'd') + $areaInfo['tz'] * 3600;
            }

            $data['solar_birthday'] = date('Y-m-d H:i:s', $behold_planet);

            $areaInfoLiving = $this->modelArea->getInfo(['id' => $data['living_district']]);

            $birthdayToTime = $behold_planet - ($areaInfo['tz'] - $dst) * 3600;

            $utdatenow = date('d.m.Y', $birthdayToTime);

            $utnow = date('H:i:s', $birthdayToTime);

            $house = $areaInfoLiving['longitude'] . ',' . $areaInfoLiving['latitude'] . ',k';

            $arr = [
                'b' => $utdatenow,
                'p' => '0123456789',
                'house' => $house,
                'ut' => $utnow,
                'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'head',
                'roundsec'
            ];

            $planet_json['solar'] = $p->calculate($arr, $starsCode);

            //本命分值计算
            $solar_lin_score = array();

            $solar_planet = $planet_json['solar']['planet'];

            foreach ($solar_planet as $key => $value) {

                if ($key < 7) {
                    $solar_lin_score[$value['english_name']] = $this->logicObjectiveEnergy->calculateNew($solar_planet[$key]['sign_id'], $key, (int)$solar_planet[$key]['deg']);
                }
            }
            $data['solar_lin_score'] = json_encode($solar_lin_score);
            //本命分值计算结束
            $data['planet_json'] = json_encode($planet_json);


            $transit_yue_birth = mktime(0, 0, 0, 1, 1, $transit_year);
            $utdatenow_yue = date('d.m.Y', $transit_yue_birth);
            $nnnn = (mktime(0, 0, 0, 1, 1, $transit_year + 1) - mktime(0, 0, 0, 1, 1, $transit_year)) / 86400;


            $conditions = [
                'b' => $utdatenow_yue,
                'ut' => '12:30:30',
                'p' => '0123456',
                'n' => $nnnn,
                'house' => $areaInfoLiving['longitude'] . ',' . $areaInfoLiving['latitude'] . ',D',
                's' => '1440m',
                'f' => 'PTls',  //名字 时间 度数经度 速度 宫位
                'g' => ',',
                'head',
                'roundsec',
            ];
            $signs_guardian_english = ["Mars", "Venus", "Mercury", "Moon", "Sun", "Mercury", "Venus", "Mars", "Jupiter", "Saturn", "Saturn", "Jupiter"];
            $planet_name = ['Sun', 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter', 'Saturn', 'Uranus', 'Neptune', 'Pluto'];
            $planetEnglish = array(
                '0' => 'Sun',
                '1' => 'Moon',
                '2' => 'Mercury',
                '3' => 'Venus',
                '4' => 'Mars',
                '5' => 'Jupiter',
                '6' => 'Saturn',
                '7' => 'Uranus',
                '8' => 'Neptune',
                '9' => 'Pluto');
            $planetChinese = array('0' => '太阳',
                '1' => '月亮',
                '2' => '水星',
                '3' => '金星',
                '4' => '火星',
                '5' => '木星',
                '6' => '土星',
                '7' => '天王星',
                '8' => '海王星',
                '9' => '冥王星');
            $houseChinese = array("第一宫", "第二宫", "第三宫", "第四宫", "第五宫", "第六宫", "第七宫", "第八宫", "第九宫", "第十宫", "第十一宫", "第十二宫");
            $allow_degree['0'] = 5;
            $allow_degree['30'] = 5;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['120'] = 5;
            $allow_degree['180'] = 5;

            $data_fff = array();
            $planet_data = array();
            $planet_degree = array();
            $planet_yue_min = array();
            $planet_yue_min_time = array();
            $yue_longitude = $life_calculate['planet'][1]['longitude'];


            $behold_planet = $p->SweTest($conditions);


            foreach ($behold_planet as $key => $value) {
                $planetinfo = explode(',', str_replace(' ', '', $value));

                if (in_array($planetinfo[0], ['Sun', 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter', 'Saturn', 'house1', 'house2', 'house3', 'house4', 'house5', 'house6', 'house7', 'house8', 'house9', 'house10', 'house11', 'house12'])) {
                    $planetinfo[4] = strtotime(str_replace("UT", "UTC", $planetinfo[1]));

                    if ($planetinfo[0] == 'Moon') {

                        if (empty($planet_yue_min[strtotime(date('Y-m', $planetinfo[4]))])) {
                            $planet_yue_min[strtotime(date('Y-m', $planetinfo[4]))] = 99999999;
                        }
                        $yue_longitude_min_cha = abs($planetinfo[2] - $yue_longitude);

                        if ($yue_longitude_min_cha < $planet_yue_min[strtotime(date('Y-m', $planetinfo[4]))]) {
                            $planet_yue_min[strtotime(date('Y-m', $planetinfo[4]))] = $yue_longitude_min_cha;
                            $planet_yue_min_time[strtotime(date('Y-m', $planetinfo[4]))] = $planetinfo[4];
                        }
                    }
                    if (empty($planet_list[$planetinfo[4]])) {
                        $planet_list[$planetinfo[4]] = array();
                    }

                    if (empty($house_list_data[$planetinfo[4]])) {
                        $house_list_data[$planetinfo[4]] = array();
                    }


                    if (strpos($planetinfo[0], 'house') !== false and strpos($planetinfo[0], 'house') == 0) {

                        $house_info = array();
                        $house_info['house_id'] = str_replace('house', '', $planetinfo[0]) - 1;
                        $house_info['sign'] = $p->Convert_Longitude($planetinfo[2]);
                        $house_info['signs_guardian'] = $signs_guardian_english[$house_info['sign']['sign_id']];
                        $house_info['time'] = $planetinfo[4];
                        $house_info['longitude'] = $planetinfo[2];
                        $house_list_data[$planetinfo[4]][str_replace('house', '', $planetinfo[0]) - 1] = $house_info;

                    } else {
                        $planet_info = array();
                        $planet_info['english_name'] = str_replace('.', '', $planetinfo[0]);
                        $planet_info['sign'] = $p->Convert_Longitude($planetinfo[2]);
                        $planet_info['code_name'] = (string)array_search($planet_info['english_name'], $planetEnglish);
                        $planet_info['sore'] = $this->logicObjectiveEnergy->calculateNew($planet_info['sign']['sign_id'], $planet_info['code_name'], (int)$planet_info['sign']['deg']);
                        $planet_info['longitude'] = $planetinfo[2];
                        $planet_info['time'] = $planetinfo[4];

                        $planet_list[$planetinfo[4]][] = $planet_info;
                    }


                }
            }

            $house_list_data_new = array();
            $planet_list_new = array();
            //每个月的计算哪一天数据
            foreach ($planet_yue_min_time as $key_yue => $value_time) {

                $house_yue_info = $house_list_data[$value_time];
                $planet_yue_info = $planet_list[$value_time];

                $planet_twe = $planet_yue_info;
                foreach ($planet_yue_info as $key => &$value) {
                    foreach ($house_yue_info as $keyh => &$valueh) {
                        $house_cha = abs($valueh['longitude'] - $value['longitude']);
                        if ($house_cha > 180) {
                            $house_cha = 360 - $house_cha;
                        }
                        if ($keyh < 11) {
                            $last_house = $house_yue_info[$keyh + 1];
                        } else {
                            $last_house = $house_yue_info[0];
                        }
                        $first_house = $value;

                        if ($valueh['longitude'] > $last_house['longitude']) {
                            if ($first_house['longitude'] < $last_house['longitude']) {
                                $first_house['longitude'] += 360;
                            }
                            $last_house['longitude'] += 360;
                        }
                        if (empty($valueh['planet_list'])) {
                            $valueh['planet_list'] = array();
                        }
                        if (round($first_house['longitude'], 7) >= $valueh['longitude'] and round($first_house['longitude'], 7) < $last_house['longitude']) {
                            $valueh['planet_list'][] = $key;

                            $value['house_id'] = $keyh;

                        }
                    }

                    foreach ($planet_twe as $keya => $valuea) {

                        if ($key == $keya) {
                            continue;
                        }
                        $chazhi = abs($value['longitude'] - $valuea['longitude']);

                        if ($chazhi > 180) {
                            $chazhi = 360 - $chazhi;
                        }
                        $planet_degree_lgit = 0;

                        !empty($planet_degree[$value['code_name']]) && $planet_degree_lgit += $planet_degree[$value['code_name']];

                        !empty($planet_degree[$valuea['code_name']]) && $planet_degree_lgit += $planet_degree[$valuea['code_name']];

                        if (empty($value['planet_allow_degree'])) {
                            $value['planet_allow_degree'] = array();
                        }
                        foreach ($allow_degree as $keyAd => $valueAd) {
                            $valueAd += $planet_degree_lgit;

                            if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {

                                if (empty($value['planet_allow_degree'][$keyAd])) {
                                    $value['planet_allow_degree'][$keyAd] = array();
                                }
                                $value['planet_allow_degree'][$keyAd][] = $keya;
                            }
                        }
                    }
                }

                $house_list_data_new[$value_time] = $house_yue_info;
                $planet_list_new[$value_time] = $planet_yue_info;
            }

            $sore_data_list = array();
            foreach ($house_list_data_new as $keyhso => $valuehso) {

                $sore_data['main_wealth'] = 0;    //主财
                $sore_data['partial_wealth'] = 0;  //偏财
                $sore_data['investment'] = 0;     ///投资
                $sore_data['healthy'] = 0;        //健康
                $sore_data['emotions'] = 0;      ///情绪

                //一宫主id
                $one_hous_zhu_id = array_search($signs_guardian_english[$valuehso[0]["sign"]["sign_id"]], $planet_name);
                //二宫主id   2
                $two_hous_zhu_id = array_search($signs_guardian_english[$valuehso[1]["sign"]["sign_id"]], $planet_name);
                if ($planet_list_new[$keyhso][$two_hous_zhu_id]['sore'] >= 0) {
                    $sore_data['main_wealth'] += 50;
                } else {
                    $sore_data['main_wealth'] += 30;
                }
                //五宫主id   4
                $five_hous_zhu_id = array_search($signs_guardian_english[$valuehso[4]["sign"]["sign_id"]], $planet_name);
                if ($planet_list_new[$keyhso][$five_hous_zhu_id]['sore'] >= 0) {
                    $sore_data['investment'] += 50;
                } else {
                    $sore_data['investment'] += 30;
                }
                //六宫主id
                $six_hous_zhu_id = array_search($signs_guardian_english[$valuehso[5]["sign"]["sign_id"]], $planet_name);
                //八宫主id   3
                $egit_hous_zhu_id = array_search($signs_guardian_english[$valuehso[7]["sign"]["sign_id"]], $planet_name);
                if ($planet_list_new[$keyhso][$egit_hous_zhu_id]['sore'] >= 0) {
                    $sore_data['partial_wealth'] += 50;
                } else {
                    $sore_data['partial_wealth'] += 30;
                }
                // 5
                if (in_array(3, $valuehso[1]["planet_list"]) or in_array(5, $valuehso[1]["planet_list"])) {
                    $sore_data['main_wealth'] += 10;
                }
                // 7
                if (in_array(3, $valuehso[4]["planet_list"]) or in_array(5, $valuehso[4]["planet_list"])) {
                    $sore_data['investment'] += 10;
                }
                // 6
                if (in_array(3, $valuehso[7]["planet_list"]) or in_array(5, $valuehso[7]["planet_list"])) {
                    $sore_data['partial_wealth'] += 10;
                }
                $er_plan_fen = 0;
                foreach ($valuehso[1]["planet_list"] as $kefgsd => $dgsc) {
                    $er_plan_fen += $planet_list_new[$keyhso][$dgsc]['sore'];
                }
                $wu_plan_fen = 0;
                foreach ($valuehso[4]["planet_list"] as $kefgsd => $dgsc) {
                    $wu_plan_fen += $planet_list_new[$keyhso][$dgsc]['sore'];
                }
                $ba_plan_fen = 0;
                foreach ($valuehso[7]["planet_list"] as $kefgsd => $dgsc) {
                    $ba_plan_fen += $planet_list_new[$keyhso][$dgsc]['sore'];
                }
                // 8
                if ($er_plan_fen >= 0) {
                    $sore_data['main_wealth'] += 10;
                }
                // 9
                if ($wu_plan_fen >= 0) {
                    $sore_data['investment'] += 10;
                }
                // 10
                if ($ba_plan_fen >= 0) {
                    $sore_data['partial_wealth'] += 10;
                }
                //11
                if (!in_array(4, $valuehso[11]["planet_list"]) and !in_array(6, $valuehso[11]["planet_list"])) {
                    $sore_data['main_wealth'] += 4;
                    $sore_data['investment'] += 4;
                    $sore_data['partial_wealth'] += 4;
                }
                //12
                $eleven_hous_zhu_id = array_search($signs_guardian_english[$valuehso[11]["sign"]["sign_id"]], $planet_name);
                if ($planet_list_new[$keyhso][$eleven_hous_zhu_id]['sore'] >= 0) {
                    $sore_data['main_wealth'] += 4;
                    $sore_data['investment'] += 4;
                    $sore_data['partial_wealth'] += 4;
                }
                //13  14  15
                foreach ($planet_list_new[$keyhso][3]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (in_array($kgjsdf, [0, 60, 120])) {

                        if (in_array($two_hous_zhu_id, $gsdc)) {
                            $sore_data['main_wealth'] += 6;
                        }
                        if (in_array($five_hous_zhu_id, $gsdc)) {
                            $sore_data['investment'] += 6;
                        }
                        if (in_array($egit_hous_zhu_id, $gsdc)) {
                            $sore_data['partial_wealth'] += 6;
                        }
                    }
                }
                //16  17  18
                foreach ($planet_list_new[$keyhso][5]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (in_array($kgjsdf, [0, 60, 120])) {

                        if (in_array($two_hous_zhu_id, $gsdc)) {
                            $sore_data['main_wealth'] += 4;
                        }
                        if (in_array($five_hous_zhu_id, $gsdc)) {
                            $sore_data['investment'] += 4;
                        }
                        if (in_array($egit_hous_zhu_id, $gsdc)) {
                            $sore_data['partial_wealth'] += 4;
                        }
                    }
                }
                //19  2宫主和火星没有合、刑、冲火星
                //20  5宫主和火星没有合、刑、冲火星
                //21  8宫主和火星没有合、刑、冲火星
                foreach ($planet_list_new[$keyhso][4]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (!in_array($kgjsdf, [0, 180, 90])) {
                        if (in_array($two_hous_zhu_id, $gsdc)) {
                            $sore_data['main_wealth'] += 6;
                        }
                        if (in_array($five_hous_zhu_id, $gsdc)) {
                            $sore_data['investment'] += 6;
                        }
                        if (in_array($egit_hous_zhu_id, $gsdc)) {
                            $sore_data['partial_wealth'] += 6;
                        }
                    }
                }
                //22  2宫主和土星没有合、刑、冲火星
                //23  5宫主和土星没有合、刑、冲火星
                //24  8宫主和土星没有合、刑、冲火星
                foreach ($planet_list_new[$keyhso][6]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (!in_array($kgjsdf, [0, 180, 90])) {
                        if (in_array($two_hous_zhu_id, $gsdc)) {
                            $sore_data['main_wealth'] += 6;
                        }
                        if (in_array($five_hous_zhu_id, $gsdc)) {
                            $sore_data['investment'] += 6;
                        }
                        if (in_array($egit_hous_zhu_id, $gsdc)) {
                            $sore_data['partial_wealth'] += 6;
                        }
                    }
                }

                //28 第六宫宫主星+第一宫宫主星得分为正分和0 ，基础分为50分，第六宫宫主星+第一宫宫主星得分为负分，基础分为30分
                if ($planet_list_new[$keyhso][$six_hous_zhu_id]['sore'] >= 0 and $planet_list_new[$keyhso][$one_hous_zhu_id]['sore'] >= 0) {
                    $sore_data['healthy'] += 50;
                } else {
                    $sore_data['healthy'] += 30;
                }
                // 29 金星，落入第六宫或者第一宫
                if (in_array(3, $valuehso[5]["planet_list"]) or in_array(3, $valuehso[0]["planet_list"])) {
                    $sore_data['healthy'] += 10;
                }
                // 30 木星，落入第六宫或者第一宫
                if (in_array(5, $valuehso[5]["planet_list"]) or in_array(5, $valuehso[0]["planet_list"])) {
                    $sore_data['healthy'] += 10;
                }
                // 31 火星没有落入，第六宫或者第一宫
                if (!in_array(4, $valuehso[5]["planet_list"]) or !in_array(4, $valuehso[0]["planet_list"])) {
                    $sore_data['healthy'] += 7;
                }
                // 32 土星没有落入，第六宫或者第一宫
                if (!in_array(6, $valuehso[5]["planet_list"]) or !in_array(6, $valuehso[0]["planet_list"])) {
                    $sore_data['healthy'] += 8;
                }
                // 33 一宫主没有合、刑、冲火星
                //35 六宫主没有合、刑、冲火星
                foreach ($planet_list_new[$keyhso][4]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (!in_array($kgjsdf, [0, 180, 90])) {
                        if (in_array($one_hous_zhu_id, $gsdc)) {
                            $sore_data['main_wealth'] += 2;
                        }
                        if (in_array($six_hous_zhu_id, $gsdc)) {
                            $sore_data['investment'] += 2;
                        }
                    }
                }
                // 34 一宫主没有合、刑、冲土星
                //36 六宫主没有合、刑、冲土星

                foreach ($planet_list_new[$keyhso][6]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (!in_array($kgjsdf, [0, 180, 90])) {
                        if (in_array($one_hous_zhu_id, $gsdc)) {
                            $sore_data['main_wealth'] += 3;
                        }
                        if (in_array($six_hous_zhu_id, $gsdc)) {
                            $sore_data['investment'] += 3;
                        }
                    }
                }
                //37  返照月亮没有落入第八宫
                if ($planet_list_new[$keyhso][1]['house_id'] == 7) {
                    $sore_data['investment'] += 5;
                }
                //39  月亮落入1 4 7 10 宫
                //40 月亮落落入2 5 8 11宫
                //41 月亮落落入续宫

                if (in_array($planet_list_new[$keyhso][1]['house_id'], [0, 3, 6, 9])) {
                    $sore_data['emotions'] += 50;
                } else if (in_array($planet_list_new[$keyhso][1]['house_id'], [1, 4, 7, 10])) {
                    $sore_data['emotions'] += 30;
                } else {
                    $sore_data['emotions'] += 20;
                }
                //42 月亮金星三合或者六合，或者合相
                //43 月亮木星三合或者六合，或者合相
                foreach ($planet_list_new[$keyhso][1]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (in_array($kgjsdf, [0, 60, 120])) {
                        if (in_array(3, $gsdc)) {
                            $sore_data['emotions'] += 20;
                        }
                        if (in_array(5, $gsdc)) {
                            $sore_data['emotions'] += 20;
                        }
                    }
                }
                //44 月亮和火星合相，刑相，冲相
                //45 月亮和土星合相，刑相，冲相
                foreach ($planet_list_new[$keyhso][1]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (in_array($kgjsdf, [0, 90, 180])) {
                        if (in_array(4, $gsdc)) {
                            $sore_data['emotions'] -= 10;
                        }
                        if (in_array(6, $gsdc)) {
                            $sore_data['emotions'] -= 20;
                        }
                    }
                }
                //46 月亮没有金星木星，火星，土星相位
                foreach ($planet_list_new[$keyhso][1]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (!in_array(4, $gsdc) and !in_array(5, $gsdc) and !in_array(6, $gsdc)) {
                        $sore_data['emotions'] += 5;
                    }
                }
                //47 月亮和其他行星三合六合或者合相（累计最高10分）
                foreach ($planet_list_new[$keyhso][1]["planet_allow_degree"] as $kgjsdf => $gsdc) {
                    if (in_array($kgjsdf, [0, 60, 120])) {
                        $sore_data['emotions'] += 2 * count($gsdc);
                    }
                }

                $sore_data_list[$keyhso] = $sore_data;
            }


            $corpusRetrogradePalaceWhere['attribution'] = "beholding_luck";
            $corpusRetrogradePalaceWhere['status'] = 1;
            $corpusRetrogradePalaceWhere['attribution_str'] = '今年爱情运势逐月分析';

            $corpusRetrogradePalace = $this->logicCorpus->getCorpusColumn($corpusRetrogradePalaceWhere, 'id,attribution,attribution_str,content1,content2,content3', 'title');


            //每个月的月返数据计算语料
            $yuefan_qingxu_yuliao = array();
            foreach ($sore_data_list as $yuefayuliao => $vjusf) {


                $gognwei = $house_list_data_new[$yuefayuliao][4];

                $emotions_str = '差';
                if ($vjusf['emotions'] > 70) {
                    $emotions_str = '好';
                }

                if (count($gognwei["planet_list"]) > 0) {
                    //2、逐月分析，写十二个月的，标题为1月，2月，3月这样，逻辑1、月返有行星落入5宫，读行星解释，得分高于70读好，低于70读差。
                    $jjjf = array();
                    foreach ($gognwei["planet_list"] as $kfhhf => $ghhuu) {
                        if (!empty($corpusRetrogradePalace['今年爱情运势逐月分析' . $planetChinese[$ghhuu] . '第五宫' . $emotions_str])) {
                            $area_key_vole = $corpusRetrogradePalace['今年爱情运势逐月分析' . $planetChinese[$ghhuu] . '第五宫' . $emotions_str];
                            $jjjf[] = $area_key_vole['content1'];

                        }
                    }
                    if (!empty($jjjf)) {
                        $yuefan_qingxu_yuliao[] = ['key' => 'moon_return_emotions', 'title' => date('m', $yuefayuliao) . '月', 'str' => implode('<br>', $jjjf)];
                    }
                } else {
                    //2、月返没有行星落入第五宫读金星宫位，得分高于70读好，低于70读差。
                    if (!empty($corpusRetrogradePalace['今年爱情运势逐月分析金星' . $houseChinese[$planet_list_new[$yuefayuliao][3]['house_id']] . $emotions_str])) {
                        $area_key_vole = $corpusRetrogradePalace['今年爱情运势逐月分析金星' . $houseChinese[$planet_list_new[$yuefayuliao][3]['house_id']] . $emotions_str];
                        $yuefan_qingxu_yuliao[] = ['key' => 'moon_return_emotions', 'title' => date('m', $yuefayuliao) . '月', 'str' => $area_key_vole['content1']];
                    }

                    //dump(date('y-m-d',$yuefayuliao));
                }
            }

            $data['yuliao'] = json_encode(['love' => $yuefan_qingxu_yuliao]);


            $data['solar_moon_score'] = json_encode($sore_data_list);


        $result = $this->modelActivityTransportSolar->setInfo($data);
        if (empty($data['id'])) {
            $amountLog['order_number'] = $data['order_number'];
            $amountLog['order_id'] = $result;
            $amountLog['type'] = 5;
            $amountLog['model'] = 'activity_transport_solar';
            $amountLog['money'] = 19.9;

            $amountLog['remark'] = '返照A';

            if (!empty($data["type"])) {
              //  $amountLog['model'] = 'activity_transport_solar2';
            }

            $this->logicAmountLog->amountLogEdit($amountLog);
        }
        return $result ? $result : $this->modelActivityTransportSolar->getError();
    }


    /**
     * 年运-太阳返照删除
     */
    public function activityTransportSolarDel($where = [], $is_true = false)
    {

        $result = $this->modelActivityTransportSolar->deleteInfo($where, $is_true);

        return $result ? $result : $this->modelActivityTransportSolar->getError();
    }



    //Admin模块操作

    /**
     * 获取年运-太阳返照搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 年运-太阳返照单条编辑
     */
    public function activityTransportSolarAdminEdit($data = [])
    {


        $url = url('activityTransportSolarList');

        $data['member_id'] = MEMBER_ID;

        $result = $this->modelActivityTransportSolar->setInfo($data);

        $handle_text = empty($data['id']) ? '新增' : '编辑';

        $result && action_log($handle_text, '年运-太阳返照' . $handle_text . '，data：' . http_build_query($data));

        return $result ? [RESULT_SUCCESS, '年运-太阳返照操作成功', $url] : [RESULT_ERROR, $this->modelActivityTransportSolar->getError()];
    }

    /**
     * 年运-太阳返照删除
     */
    public function activityTransportSolarAdminDel($where = [])
    {

        $result = $this->modelActivityTransportSolar->deleteInfo($where);

        $result && action_log('删除', '年运-太阳返照删除，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '年运-太阳返照删除成功'] : [RESULT_ERROR, $this->modelActivityTransportSolar->getError()];
    }
}
