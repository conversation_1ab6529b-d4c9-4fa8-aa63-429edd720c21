<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\common\logic;
use app\common\error\CodeBase;

/**
 * Api基础逻辑
 */
class ApiBase extends LogicBase
{

    /**
     * API返回数据
     */
    public function apiReturn($code_data = [], $return_data = [], $return_type = 'json')
    {
        if (is_array($code_data) && array_key_exists(API_CODE_NAME, $code_data)) {
            
            !empty($return_data) && $code_data['data'] = $return_data;

            $result = $code_data;
            
        } else {
            
            $result = CodeBase::$success;
            
            $result['data'] = $code_data;
        }
        
        $return_result = $this->checkDataSign($result);
        
        $return_result['exe_time'] = debug(MODULE_NAME.'_begin', MODULE_NAME.'_end');
        
        return $return_type == 'json' ? json($return_result) : $return_result;
    }

    /**
     * 检查是否需要响应数据签名
     */
    public function checkDataSign($data)
    {
        $info =getRedisHash('apiBase', URL);
        if (!$info) {
            $info = $this->modelApi->getInfo(['api_url' => URL]);
            setRedisHash('apiBase', URL,$info);
        }

        $info['is_response_sign'] && !empty($data['data']) && $data['data']['data_sign'] = create_sign_filter($data['data']);

        return $data;
    }
    
    /**
     * API错误终止程序
     */
    public function apiError($code_data = [])
    {
        
        return throw_response_exception($code_data);
    }

    /**
     * API提交附加参数检查
     */
    public function checkParam($param = [])
    {

        $info =getRedisHash('apiBase', URL);
        if (!$info) {
            $info = $this->modelApi->getInfo(['api_url' => URL]);
            setRedisHash('apiBase', URL,$info);
        }
        empty($info) && $this->apiError(CodeBase::$apiUrlError);

        if(!empty($param['blacklist_uid'])){
            $rules_array =getRedisHash('apiUrlBlackList', $param['blacklist_uid']);
            if(!$rules_array){
                $rules_array=array();
            }
            in_array( $info['id'], $rules_array)&& $this->apiError(CodeBase::$apiUrlBlacklist);
            unset($param['blacklist_uid']);
        }

        (empty($param['access_token']) || !get_access_token($param['access_token']) || $param['access_token'] != get_access_token($param['access_token'])) && $this->apiError(CodeBase::$accessTokenError);

        if ($info['is_user_token'] && empty($param['user_token'])) {

            $this->apiError(CodeBase::$userTokenNull);

        } elseif ($info['is_user_token']) {

            $decoded_user_token = decoded_user_token($param['user_token']);

            is_string($decoded_user_token) && $this->apiError(CodeBase::$userTokenError);

            $param['user_token'] = $decoded_user_token;
        }

        $info['is_request_sign']    && (empty($param['data_sign'])      || create_sign_filter($param) != $param['data_sign']) && $this->apiError(CodeBase::$dataSignError);

        if(!empty($info['is_request_data'])){
            foreach ($info['request_data'] as $key=>$val){
                if ($val['field_name']==1 and !in_array($val['field_name'], array_keys($param)) and empty($param[$val['field_name']])){

                    $this->apiError( [API_CODE_NAME => 1000005,   API_MSG_NAME => '请求字段<'.$val['field_name'].'>为空']);
                }
            }
        }

        return $param;
    }

    public function getSmsCode($parameter=[])
    {

        return $this->serviceSms->driverAlidy->sendSms($parameter);
    }

}
