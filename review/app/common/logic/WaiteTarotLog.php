<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 维特塔罗记录逻辑
 */
class WaiteTarotLog extends LogicBase
{

      /**
       * 获取维特塔罗记录搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获维特塔罗记录单条信息
      */
     public function getWaiteTarotLogInfo($where = [], $field = '*')
     {

        return $this->modelWaiteTarotLog->getInfo($where, $field);
     }

    /**
     * 获取维特塔罗记录列表
     */

    public function getWaiteTarotLogList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelWaiteTarotLog->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取维特塔罗记录无分页列表
     */
    public function getWaiteTarotLogColumn($where = [], $field = '', $key = '')
    {
        return $this->modelWaiteTarotLog->getColumn($where, $field , $key);
    }

    /**
     * 维特塔罗记录单条编辑
     */
    public function waiteTarotLogEdit($data = [])
    {
		
        $result = $this->modelWaiteTarotLog->setInfo($data);
        
        return $result ? $result : $this->modelWaiteTarotLog->getError();
    }

    /**
     * 维特塔罗记录删除
     */
    public function waiteTarotLogDel($where = [], $is_true = false)
    {

        $result = $this->modelWaiteTarotLog->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelWaiteTarotLog->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取维特塔罗记录搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 维特塔罗记录单条编辑
     */
    public function waiteTarotLogAdminEdit($data = [])
    {


        $url = url('waiteTarotLogList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelWaiteTarotLog->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '维特塔罗记录' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '维特塔罗记录操作成功', $url] : [RESULT_ERROR, $this->modelWaiteTarotLog->getError()];
    }

    /**
     * 维特塔罗记录删除
     */
    public function waiteTarotLogAdminDel($where = [])
    {

        $result = $this->modelWaiteTarotLog->deleteInfo($where);
        
        $result && action_log('删除', '维特塔罗记录删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '维特塔罗记录删除成功'] : [RESULT_ERROR, $this->modelWaiteTarotLog->getError()];
    }
}
