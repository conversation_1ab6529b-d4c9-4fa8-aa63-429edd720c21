<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 综合语料配置逻辑
 */
class CorpusConfig extends LogicBase
{

      /**
       * 获取综合语料配置搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获综合语料配置单条信息
      */
     public function getCorpusConfigInfo($where = [], $field = '*')
     {

        return $this->modelCorpusConfig->getInfo($where, $field);
     }

    /**
     * 获取综合语料配置列表
     */

    public function getCorpusConfigList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelCorpusConfig->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取综合语料配置无分页列表
     */
    public function getCorpusConfigColumn($where = [], $field = '', $key = '')
    {
        return $this->modelCorpusConfig->getColumn($where, $field , $key);
    }

    /**
     * 综合语料配置单条编辑
     */
    public function corpusConfigEdit($data = [])
    {
		
        $result = $this->modelCorpusConfig->setInfo($data);
        
        return $result ? $result : $this->modelCorpusConfig->getError();
    }

    /**
     * 综合语料配置删除
     */
    public function corpusConfigDel($where = [], $is_true = false)
    {

        $result = $this->modelCorpusConfig->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelCorpusConfig->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取综合语料配置搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 综合语料配置单条编辑
     */
    public function corpusConfigAdminEdit($data = [])
    {


        dump($data);



        $url = url('corpusConfigList');

        $data['member_id'] = MEMBER_ID;

        if(empty($data['field'])){
            return [RESULT_ERROR, '最少配置一个字段属性'];
        }

        $note=array();

        foreach ($data['field'] as $key=>$vol){

            $note['content'.($key+1)]=array('field'=>$vol,'chinese'=>$data['chinese'][$key]);
        }

        unset($data['field']);
        unset($data['chinese']);
        $data['note']=json_encode($note);

        dump($data);

//        $result = $this->modelCorpusConfig->setInfo($data);
//
//		$handle_text = empty($data['id']) ? '新增' : '编辑';
//
//        $result && action_log($handle_text, '综合语料配置' . $handle_text . '，data：' .http_build_query($data));
//
//        return $result ? [RESULT_SUCCESS, '综合语料配置操作成功', $url] : [RESULT_ERROR, $this->modelCorpusConfig->getError()];
    }

    /**
     * 综合语料配置删除
     */
    public function corpusConfigAdminDel($where = [])
    {

        $result = $this->modelCorpusConfig->deleteInfo($where);
        
        $result && action_log('删除', '综合语料配置删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '综合语料配置删除成功'] : [RESULT_ERROR, $this->modelCorpusConfig->getError()];
    }
}
