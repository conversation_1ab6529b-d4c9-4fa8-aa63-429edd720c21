<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 通知公告逻辑
 */
class WeiNotice extends LogicBase
{

      /**
       * 获取通知公告搜索条件
      */
      public function getWhere($data = [])
      {

         $where = $data;

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获通知公告单条信息
      */
     public function getWeiNoticeInfo($where = [], $field = '*')
     {

        return $this->modelWeiNotice->getInfo($where, $field);
     }

    /**
     * 获取通知公告列表
     */

    public function getWeiNoticeList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelWeiNotice->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取通知公告无分页列表
     */
    public function getWeiNoticeColumn($where = [], $field = '', $key = '')
    {
        return $this->modelWeiNotice->getColumn($where, $field , $key);
    }

    /**
     * 通知公告单条编辑
     */
    public function weiNoticeEdit($data = [])
    {

        $url = url('weiNoticeList');

        $data['member_id'] = MEMBER_ID;

        $result = $this->modelWeiNotice->setInfo($data);

        $handle_text = empty($data['id']) ? '新增' : '编辑';

        $result && action_log($handle_text, '通知公告' . $handle_text . '，id：' . $data['id']);

        return $result ? [RESULT_SUCCESS, '通知公告新增成功', $url] : [RESULT_ERROR, $this->modelWeiNotice->getError()];
    }
    /**
     * 通知公告新增
     */
    public function weiNoticeAdd($data = [])
    {

        $url = url('weiNoticeList');

        $data['choose_appid']=session('member_info')['choose_appid'];

        $result = $this->modelWeiNotice->setInfo($data);

        $result && action_log('新增', '新增通知公告：');

        return $result ? [RESULT_SUCCESS, '通知公告新增成功', $url] : [RESULT_ERROR, $this->modelWeiNotice->getError()];

    }
    /**
     * 通知公告浏览量新增
     */
    public function setWeiNotice($where = [],$field = 'score', $number = 1,$setType='setInc')
    {
        $this->modelWeiNotice->setIncDecInfo($where,$field , $number ,$setType);
    }
    /**
     * 通知公告删除
     */
    public function weiNoticeDel($where = [], $is_true = false)
    {

        $result = $this->modelWeiNotice->deleteInfo($where, $is_true);

        $result && action_log('删除', '删除通知公告，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '通知公告删除成功'] : [RESULT_ERROR, $this->modelWeiNotice->getError()];

    }
}
