<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
use exwechat\api\media\media;
/**
 * 微信素材
 */
class WeiMaterial extends LogicBase
{
    public  function __call($method, $params)
    {
        // 自动化寻找类并且实例化执行操作
        return call_user_func_array([get_sington_object('mediaEx', media::class,getAccessToken()['access_token']), $method], $params);
    }
    /**
     * 获取微信素材列表搜索条件
     */
    public function getWhere($data = [])
    {
        $where = [];

        !empty($data['search_data']) && $where['name|describe'] = ['like', '%'.$data['search_data'].'%'];

        !empty($data['m_id']) && $where['m_id'] = $data['m_id'];

        return $where;
    }
    /**
     * 获取微信素材列表
     */
    public function getWeiMaterialList($where = [], $field = true, $order = '', $paginate = '')
    {

        return $this->modelWeiMaterial->getList($where, $field, $order, $paginate);
    }
    /**
     * 获取微信素材列表
     */
    public function getWeiMaterialColumn($where = [], $field = true, $key= '')
    {

        return $this->modelWeiMaterial->getColumn($where, $field, $key);
    }
    /**
     * 微信菜单配置批量添加
     */
    public function weiMaterialAllAdd($data = [],$replace = false)
    {

        $url = url('weiMenuList');
        $result = $this->modelWeiMaterial->setList($data , $replace);
        $result && action_log('新增', '新增微信素材');

        return $result ? [RESULT_SUCCESS, '新增微信素材成功', $url] : [RESULT_ERROR, $this->modelWeiMaterial->getError()];
    }
    /**
     * 微信素材添加
     */
    public function weiMaterialAdd($data = [])
    {
        $validate_result = $this->validateWeiMaterial->scene('add')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateWeiMaterial->getError()]; endif;

        $url = url('weiMaterialList');
        $vlau_data['title'] = $data['title'];
        $vlau_data['member_id'] = MEMBER_ID;
        $vlau_data['type_id'] = $data['type_id'];
        if($data['type_id']==1){
            $material_url=$this->logicFile->downloadStorage($data['pic']);

            $material_id=self::add_material($material_url, 'image');
            $vlau_data['media_id'] = $material_id['media_id'];//self::add_material(q, 'image');

            unlink(str_replace('\\','/',ROOT_PATH . 'public' . DS. $material_url));

            $vlau_data['content'] = $data['pic'];
        }elseif ($data['type_id']==2){
            $vlau_data['media_id'] = MEMBER_ID;
        }elseif ($data['type_id']==3){
            $vlau_data['media_id'] = MEMBER_ID;
        }elseif ($data['type_id']==4){
            $vlau_data['media_id'] = MEMBER_ID;
            $vlau_data['content'] = $data['content'];
        }elseif ($data['type_id']==5){
            $vlau_data['content'] = $data['file'];
        }elseif ($data['type_id']==6){
            $vlau_data = $data;
            unset($vlau_data['file_id']);
        }elseif ($data['type_id']==7){

        }

        $vlau_data['id'] = $data['id'];
        if(empty($vlau_data['id'])){
            $info=$this->logicWeiMaterial->getMaterialInfo($where = [], 'MAX(`m_id`) as m_id_max');
            $vlau_data['m_id']=$info['m_id_max']+1;
        }
        $vlau_data["choose_appid"] =session('member_info')['choose_appid'];

        $result = $this->modelWeiMaterial->setInfo($vlau_data);

        $result && action_log('新增', '修改微信素材标题：' . $vlau_data['title']);

        return $result ? [RESULT_SUCCESS, '微信素材添加成功', $url] : [RESULT_ERROR, $this->modelWeiMaterial->getError()];
    }

    /**
     * 微信素材编辑
     */
    public function weiMaterialEdit($data = [])
    {

        $validate_result = $this->validateWeiMaterial->scene('edit')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateWeiMaterial->getError()]; endif;

        $url = url('weiMaterialList');

        $result = $this->modelWeiMaterial->setInfo($data);

        $result && action_log('编辑', '编辑微信appid配置，name：' . $data['wechat']);

        return $result ? [RESULT_SUCCESS, '微信appid配置编辑成功', $url] : [RESULT_ERROR, $this->modelWeiMaterial->getError()];
    }

    /**
     * 微信素材删除
     */
    public function weiMaterialDel($where = [])
    {

        $result = $this->modelWeiMaterial->deleteInfo($where);

        $result && action_log('删除', '删除微信素材，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '微信素材删除成功'] : [RESULT_ERROR, $this->modelWeiMaterial->getError()];
    }

    /**
     * 获取微信素材信息
     */
    public function getMaterialInfo($where = [], $field = true)
    {

        return $this->modelWeiMaterial->getInfo($where, $field);
    }
}
