<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 客观能量逻辑
 */
class ObjectiveEnergy extends LogicBase
{

      /**
       * 获取客观能量搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

          !empty($data['user_id']) && $where['user_id'] = $data['user_id'];
         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获客观能量单条信息
      */
     public function getObjectiveEnergyInfo($where = [], $field = '*')
     {

        return $this->modelObjectiveEnergy->getInfo($where, $field);
     }

    /**
     * 获取客观能量列表
     */

    public function getObjectiveEnergyList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelObjectiveEnergy->getList($where, $field, $order, $paginate);
    }
    public function setObjectiveEnergyList($data_list = [], $replace = false)
    {
        return $this->modelObjectiveEnergy->setList($data_list, $replace);
    }
    /**
     * 获取客观能量无分页列表
     */
    public function getObjectiveEnergyColumn($where = [], $field = '', $key = '')
    {
        return $this->modelObjectiveEnergy->getColumn($where, $field , $key);
    }

    /**
     * 客观能量单条编辑
     */
    public function objectiveEnergyEdit($data = [])
    {
		
        $result = $this->modelObjectiveEnergy->setInfo($data);
        
        return $result ? $result : $this->modelObjectiveEnergy->getError();
    }

    /**
     * 客观能量删除
     */
    public function objectiveEnergyDel($where = [], $is_true = false)
    {

        $result = $this->modelObjectiveEnergy->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelObjectiveEnergy->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取客观能量搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 客观能量单条编辑
     */
    public function objectiveEnergyAdminEdit($data = [])
    {
        $url = url('objectiveEnergyList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelObjectiveEnergy->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '客观能量' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '客观能量操作成功', $url] : [RESULT_ERROR, $this->modelObjectiveEnergy->getError()];
    }

    /**
     * 客观能量删除
     */
    public function objectiveEnergyAdminDel($where = [])
    {

        $result = $this->modelObjectiveEnergy->deleteInfo($where);
        
        $result && action_log('删除', '客观能量删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '客观能量删除成功'] : [RESULT_ERROR, $this->modelObjectiveEnergy->getError()];
    }
//计算自己的七天时间


    public function thisScore($planet_json,$date)
    {
        $life_array=json_decode($planet_json,true);


        $todayData = $this->logicTodayStars->getTodayStarsInfo(['date'=>$date]);

        if(empty($todayData)){

            $plant = array('sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn');

            $p=get_sington_object('planets', "astrology\\Planets",['starsCode' => '0123456','birthday'=>date('Y-m-d H:i',$date)]);

            $Convert = $p->Natal();

            $today_array = $Convert['planet'];

            $this->logicTodayStars->todayStarsEdit(['date'=>$date,'code'=>'0123456','json'=>json_encode($todayData)]);
        }else{

            $today_array=json_decode($todayData['json'],true);

        }

        $moon_sign=$today_array[1]['longitude_array']['sign_num'];

        $BM[0]=0;
        $BM[1]=2;
        $BM[2]=3;
        $BM[3]=-4;
        $BM[4]=5;
        $BM[5]=-3;
        $BM[6]=-5;

        $plant[0] = 'sun';
        $plant[1] = 'moon';
        $plant[2] = 'mercury';
        $plant[3] = 'venus';
        $plant[4] = 'mars';
        $plant[5] = 'jupiter';
        $plant[6] = 'saturn';
        foreach ($life_array as $key=>$value){

            if($key<7){

                $moon_number=abs($value['longitude_array']['sign_num']-$moon_sign);
                if($moon_number>6){
                    $moon_number=12-6;
                }

                $Life_score[$plant[$key]]=$BM[$moon_number];   //本命先天得分

            }
        }
        return $Life_score;
    }

    //计算自己的七天时间


    public function thisRewScore($planet_json,$date)
    {

        $life_array=json_decode($planet_json,true);

        foreach ($life_array as $key=>$value){
            if($key<7){
                $Life_score[$key]= $this->calculate($value['longitude_array']['sign_num'], $key, $value['longitude_array']['deg']);   //本命先天得分
            }
        }
        $todayData = $this->logicTodayStars->getTodayStarsInfo(['date'=>$date]);

        $today_array=json_decode($todayData['json'],true);

        foreach ($today_array as $key=>$value){
            if($key<7){
                $today_lin_score[$key]= $this->calculate($value['longitude_array']['sign_num'], $key, (int)$value['longitude_array']['deg']);
            }
        }
        $BM[0]=5;
        $BM[1]=1;
        $BM[2]=2;
        $BM[3]=-4;
        $BM[4]=4;
        $BM[5]=-1;
        $BM[6]=-5;
        foreach ($life_array as $key=>$value){
            $CC[$key]=$Life_score[$key]*0.3;  //BA得分
            foreach ($today_array as $keyt=>$valuet){
                $AA_score_info=0;
                $ax_number=0;
                if($keyt==1){
                    $sign_cha=abs($value['longitude_array']['sign_num']-$valuet['longitude_array']['sign_num']);

                    if($sign_cha>6){
                        $sign_cha=12-$sign_cha;
                    }

                    $CC[$key]+=($BM[$sign_cha]+$today_lin_score[$keyt])*0.5;   //BB得分


                }else{
                    $chazhifen=abs($value['longitude']-$valuet['longitude']);
                    if($chazhifen>180){
                        $chazhifen=360-$chazhifen;
                    }
                    $ax=0;
                    if( $chazhifen<2){
                        if($keyt==5 || $keyt==6){
                            $ax=-1;
                        }else{
                            $ax=1;
                        }
                    }
                    if(($chazhifen>58 && $chazhifen<62) || ($chazhifen>118 && $chazhifen<122)){
                        $ax=0.6;
                    }
                    if(($chazhifen>88 && $chazhifen<92) || $chazhifen>178){
                        $ax=-0.8;
                    }
                    if(!empty($ax)){
                        $ax_number++;
                    }
                    $AA_score_info+=$today_lin_score[$keyt]*$ax;
                }
            }
            if($ax_number>0){
                $CC[$key]+=$AA_score_info/$ax_number*0.2;  //AA得分
            }
        }

       return $CC;
    }
    //本命得分
    public function thisLifeScore(){

    }

    public function calculate($constellation_name, $plan_name, $degree)
    {

        $score = 0; //分值
        $constellation['1'] = '0';
        $constellation['2'] = '1';
        $constellation['3'] = '2';
        $constellation['4'] = '3';
        $constellation['5'] = '4';
        $constellation['6'] = '5';
        $constellation['7'] = '6';
        $constellation['8'] = '7';
        $constellation['9'] = '8';
        $constellation['10'] = '9';
        $constellation['11'] = '10';
        $constellation['12'] = '11';

        $planet['1'] = '0';
        $planet['2'] = '1';
        $planet['3'] = '2';
        $planet['4'] = '3';
        $planet['5'] = '4';
        $planet['6'] = '5';
        $planet['7'] = '6';


        //数组模式方便入库。


        $assoc[1] = [5 => 5, 4 => 1, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 12], 3 => [12, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [5, 1, 4]];

        $assoc[2] = [5 => 4, 4 => 2, 3 => [4, 2, 5], 2 => [4 => [0, 8], 3 => [8, 14], 6 => [14, 22], 7 => [22, 27], 5 => [27, 30]], 1 => [3, 2, 7]];

        $assoc[3] = [5 => 3, 4 => false, 3 => [7, 4, 6], 2 => [3 => [0, 6], 6 => [6, 12], 4 => [12, 17], 5 => [17, 24], 7 => [24, 30]], 1 => [6, 5, 1]];

        $assoc[4] = [5 => 2, 4 => 6, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [7, 13], 3 => [13, 19], 6 => [19, 26], 7 => [26, 30]], 1 => [4, 3, 2]];

        $assoc[5] = [5 => 1, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 11], 7 => [11, 18], 3 => [18, 24], 5 => [24, 30]], 1 => [7, 6, 5], 5 => 1];

        $assoc[6] = [5 => 3, 4 => 3, 3 => [4, 2, 5], 2 => [3 => [0, 7], 4 => [7, 17], 6 => [17, 21], 5 => [21, 28], 7 => [28, 30]], 1 => [1, 4, 3]];

        $assoc[7] = [5 => 4, 4 => 7, 3 => [7, 3, 6], 2 => [7 => [0, 6], 3 => [6, 14], 6 => [14, 21], 4 => [21, 24], 5 => [24, 30]], 1 => [2, 7, 6]];

        $assoc[8] = [5 => 5, 4 => false, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [7, 11], 3 => [11, 19], 6 => [19, 24], 7 => [24, 30]], 1 => [5, 1, 4]];

        $assoc[9] = [5 => 6, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 12], 4 => [12, 17], 3 => [17, 21], 7 => [21, 26], 5 => [26, 30]], 1 => [3, 2, 7]];

        $assoc[10] = [5 => 7, 4 => 5, 3 => [4, 2, 5], 2 => [3 => [0, 7], 6 => [7, 14], 4 => [14, 22], 7 => [22, 26], 5 => [26, 30]], 1 => [6, 5, 1]];

        $assoc[11] = [5 => 7, 4 => false, 3 => [7, 3, 6], 2 => [3 => [0, 7], 4 => [7, 13], 6 => [13, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [4, 3, 2]];

        $assoc[12] = [5 => 6, 4 => 4, 3 => [4, 5, 2], 2 => [4 => [0, 12], 6 => [12, 16], 3 => [16, 19], 5 => [19, 2], 7 => [28, 30]], 1 => [7, 6, 5]];

        //真正计算在线面，上面是方便入库数组模式

        //判断星座是否存在
        if (!$constellation_id = array_search($constellation_name, $constellation)) {
            return false;
        }
        //判断行星是否存在
        if (!$plan_id = array_search($plan_name, $planet)) {
            return false;
        }
        //计算5分
        if ($assoc[$constellation_id][5] == $plan_id) {
            $score += 5;
        }
        //计算4分
        if ($assoc[$constellation_id][4] == $plan_id && $assoc[$constellation_id][4]!=false) {
            $score += 4;
        }
        //计算3分
        if (array_search($plan_id, $assoc[$constellation_id][3]) > -1) {
            $score += 3;
        }
        //计算2分
        if (!empty($assoc[$constellation_id][2][$plan_id])) {

            $degree_array = $assoc[$constellation_id][2][$plan_id];
            if ($degree >= $degree_array[0] and $degree < $degree_array[1]) {
                $score += 2;
            }
        }
        //计算1分
        if (($index_key = array_search($plan_id, $assoc[$constellation_id][1])) > -1) {
            if ($degree >= $index_key * 10 and $degree < ($index_key + 1) * 10) {
                $score += 1;
            }
        }
        return $score;
    }
    public function calculateNew($constellation_name, $plan_name, $degree)
    {

        $score = 0; //分值
        $constellation['1'] = '0';
        $constellation['2'] = '1';
        $constellation['3'] = '2';
        $constellation['4'] = '3';
        $constellation['5'] = '4';
        $constellation['6'] = '5';
        $constellation['7'] = '6';
        $constellation['8'] = '7';
        $constellation['9'] = '8';
        $constellation['10'] = '9';
        $constellation['11'] = '10';
        $constellation['12'] = '11';

        $planet['1'] = '0';
        $planet['2'] = '1';
        $planet['3'] = '2';
        $planet['4'] = '3';
        $planet['5'] = '4';
        $planet['6'] = '5';
        $planet['7'] = '6';


        //数组模式方便入库。


        $assoc[1] = [5 => 5, 4 => 1, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 12], 3 => [12, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [5, 1, 4], -5 => 3, -4 => 6];

        $assoc[2] = [5 => 4, 4 => 2, 3 => [4, 2, 5], 2 => [4 => [0, 8], 3 => [8, 14], 6 => [14, 22], 7 => [22, 27], 5 => [27, 30]], 1 => [3, 2, 7], -5 => 4, -4 => false];

        $assoc[3] = [5 => 3, 4 => false, 3 => [7, 4, 6], 2 => [3 => [0, 6], 6 => [6, 12], 4 => [12, 17], 5 => [17, 24], 7 => [24, 30]], 1 => [6, 5, 1], -5 => 5, -4 => false];

        $assoc[4] = [5 => 2, 4 => 6, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [7, 13], 3 => [13, 19], 6 => [19, 26], 7 => [26, 30]], 1 => [4, 3, 2], -5 => 6, -4 => 4];

        $assoc[5] = [5 => 1, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 11], 7 => [11, 18], 3 => [18, 24], 5 => [24, 30]], 1 => [7, 6, 5], -5 => 6, -4 => false];

        $assoc[6] = [5 => 3, 4 => 3, 3 => [4, 2, 5], 2 => [3 => [0, 7], 4 => [7, 17], 6 => [17, 21], 5 => [21, 28], 7 => [28, 30]], 1 => [1, 4, 3], -5 => 5, -4 => 3];

        $assoc[7] = [5 => 4, 4 => 7, 3 => [7, 3, 6], 2 => [7 => [0, 6], 3 => [6, 14], 6 => [14, 21], 4 => [21, 24], 5 => [24, 30]], 1 => [2, 7, 6], -5 => 4, -4 => 0];

        $assoc[8] = [5 => 5, 4 => false, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [7, 11], 3 => [11, 19], 6 => [19, 24], 7 => [24, 30]], 1 => [5, 1, 4], -5 => 3, -4 => 1];

        $assoc[9] = [5 => 6, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 12], 4 => [12, 17], 3 => [17, 21], 7 => [21, 26], 5 => [26, 30]], 1 => [3, 2, 7], -5 => 2, -4 => false];

        $assoc[10] = [5 => 7, 4 => 5, 3 => [4, 2, 5], 2 => [3 => [0, 7], 6 => [7, 14], 4 => [14, 22], 7 => [22, 26], 5 => [26, 30]], 1 => [6, 5, 1], -5 => 1, -4 => 5];

        $assoc[11] = [5 => 7, 4 => false, 3 => [7, 3, 6], 2 => [3 => [0, 7], 4 => [7, 13], 6 => [13, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [4, 3, 2], -5 => 0, -4 => false];

        $assoc[12] = [5 => 6, 4 => 4, 3 => [4, 5, 2], 2 => [4 => [0, 12], 6 => [12, 16], 3 => [16, 19], 5 => [19, 2], 7 => [28, 30]], 1 => [7, 6, 5], -5 => 2, -4 => 2];

        //真正计算在线面，上面是方便入库数组模式

        //判断星座是否存在
        if (!$constellation_id = array_search($constellation_name, $constellation)) {
            return false;
        }
        //判断行星是否存在
        if (!$plan_id = array_search($plan_name, $planet)) {
            return false;
        }
        //计算5分
        if ($assoc[$constellation_id][5] == $plan_id) {
            $score += 5;
        }
        //计算4分
        if ($assoc[$constellation_id][4] == $plan_id && $assoc[$constellation_id][4]!=false) {
            $score += 4;
        }
        //计算3分
        if (array_search($plan_id, $assoc[$constellation_id][3]) > -1) {
            $score += 3;
        }
        //计算2分
        if (!empty($assoc[$constellation_id][2][$plan_id])) {

            $degree_array = $assoc[$constellation_id][2][$plan_id];
            if ($degree >= $degree_array[0] and $degree < $degree_array[1]) {
                $score += 2;
            }
        }
        //计算1分
        if (($index_key = array_search($plan_id, $assoc[$constellation_id][1])) > -1) {
            if ($degree >= $index_key * 10 and $degree < ($index_key + 1) * 10) {
                $score += 1;
            }
        }
        //计算-5分

        if ($assoc[$constellation_id][-5] == $plan_id) {
            $score -= 5;
        }
        //计算-4分
        if ($assoc[$constellation_id][-4] == $plan_id && $assoc[$constellation_id][-4]!=false) {
            $score -= 5;
        }

        return $score;
    }
}
