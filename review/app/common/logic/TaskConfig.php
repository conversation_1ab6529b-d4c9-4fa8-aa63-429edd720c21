<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 任务配置逻辑
 */
class TaskConfig extends LogicBase
{

      /**
       * 获取任务配置搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获任务配置单条信息
      */
     public function getTaskConfigInfo($where = [], $field = '*')
     {

        return $this->modelTaskConfig->getInfo($where, $field);
     }

    /**
     * 获取任务配置列表
     */

    public function getTaskConfigList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelTaskConfig->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取任务配置无分页列表
     */
    public function getTaskConfigColumn($where = [], $field = '', $key = '')
    {
        return $this->modelTaskConfig->getColumn($where, $field , $key);
    }

    /**
     * 任务配置单条编辑
     */
    public function taskConfigEdit($data = [])
    {
		
        $result = $this->modelTaskConfig->setInfo($data);
        
        return $result ? $result : $this->modelTaskConfig->getError();
    }

    /**
     * 任务配置删除
     */
    public function taskConfigDel($where = [], $is_true = false)
    {

        $result = $this->modelTaskConfig->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelTaskConfig->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取任务配置搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 任务配置单条编辑
     */
    public function taskConfigAdminEdit($data = [])
    {


        $url = url('taskConfigList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelTaskConfig->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '任务配置' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '任务配置操作成功', $url] : [RESULT_ERROR, $this->modelTaskConfig->getError()];
    }

    /**
     * 任务配置删除
     */
    public function taskConfigAdminDel($where = [])
    {

        $result = $this->modelTaskConfig->deleteInfo($where);
        
        $result && action_log('删除', '任务配置删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '任务配置删除成功'] : [RESULT_ERROR, $this->modelTaskConfig->getError()];
    }
}
