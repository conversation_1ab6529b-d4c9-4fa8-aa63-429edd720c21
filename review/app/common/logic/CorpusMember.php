<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 性格测试管理逻辑
 */
class CorpusMember extends LogicBase
{

      /**
       * 获取性格测试管理搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获性格测试管理单条信息
      */
     public function getCorpusMemberInfo($where = [], $field = '*')
     {

        return $this->modelCorpusMember->getInfo($where, $field);
     }

    /**
     * 获取性格测试管理列表
     */

    public function getCorpusMemberList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelCorpusMember->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取性格测试管理无分页列表
     */
    public function getCorpusMemberColumn($where = [], $field = '', $key = '')
    {
        return $this->modelCorpusMember->getColumn($where, $field , $key);
    }

    /**
     * 性格测试管理单条编辑
     */
    public function corpusMemberEdit($data = [])
    {
		
        $result = $this->modelCorpusMember->setInfo($data);
        
        return $result ? $result : $this->modelCorpusMember->getError();
    }

    /**
     * 性格测试管理删除
     */
    public function corpusMemberDel($where = [], $is_true = false)
    {

        $result = $this->modelCorpusMember->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelCorpusMember->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取性格测试管理搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 性格测试管理单条编辑
     */
    public function corpusMemberAdminEdit($data = [])
    {


        $url = url('corpusMemberList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelCorpusMember->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '性格测试管理' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '性格测试管理操作成功', $url] : [RESULT_ERROR, $this->modelCorpusMember->getError()];
    }

    /**
     * 性格测试管理删除
     */
    public function corpusMemberAdminDel($where = [])
    {

        $result = $this->modelCorpusMember->deleteInfo($where);
        
        $result && action_log('删除', '性格测试管理删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '性格测试管理删除成功'] : [RESULT_ERROR, $this->modelCorpusMember->getError()];
    }

    /**
     * 导出会员列表
     */
    public function exportCorpusMemberList($where = [])
    {

        $list = $this->getCorpusMemberColumn($where);

        $titles = "姓名,性别,出生日期,出生时间,出生地点,夏令时,提交时间";
        $keys   = "name,sex,date,time,dist,dst,create_time";

        action_log('导出', '导出exportCorpusMember列表');

        export_excel($titles, $keys, $list, '测试数据列表'.date('Y-m-d H:i:s'));
    }
}
