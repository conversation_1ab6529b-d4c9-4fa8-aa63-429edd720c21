<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 会员
 */
class WeiEventLog extends LogicBase
{
    /**
     * 获取会员搜索条件
     */
    public function getWhere($data = [])
    {
        $where = [];

        !empty($data['search_data']) && $where['a.name|a.describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
    /**
     * 获取会员列表
     */
    public function getWeiEventList($where = [], $field = true, $order = '', $paginate = 10)
    {

        return $this->modelWeiEventLog->getList($where, $field, $order, $paginate);
    }


    /**
     * 获取会员列表
     */
    public function setAllListData($data)
    {

        return $this->modelWeiEventLog->setAllList($data);
    }

    /**
     * 会员添加
     */
    public function weiEventAdd($data = [])
    {

        $validate_result = $this->validateWeiEvent->scene('add')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateWeiEvent->getError()]; endif;

        $url = url('weiEventList');

        $result = $this->modelWeiEventLog->setInfo($data);

        $result && action_log('新增', '新增自定义回复：' . $data['m_id']);

        return $result ? [RESULT_SUCCESS, '自定义回复添加成功', $url] : [RESULT_ERROR, $this->modelWeiEventLog->getError()];
    }

    /**
     *会员编辑
     */
    public function weiEventEdit($data = [])
    {

        $validate_result = $this->validateWeiEvent->scene('edit')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateWeiEvent->getError()]; endif;

        $url = url('weiEventList');

        $result = $this->modelWeiEventLog->setInfo($data);

        $result && action_log('编辑', '编辑微信appid配置，name：' . $data['wechat']);

        return $result ? [RESULT_SUCCESS, '微信appid配置编辑成功', $url] : [RESULT_ERROR, $this->modelWeiEventLog->getError()];
    }

    /**
     * 会员删除
     */
    public function weiEventDel($where = [])
    {

        $result = $this->modelWeiEventLog->deleteInfo($where);

        $result && action_log('删除', '删除微信素材，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '微信素材删除成功'] : [RESULT_ERROR, $this->modelWeiEventLog->getError()];
    }
    /**
     *会员编辑
     */
    public function weiEventSet($data = [])
    {

        $result = $this->modelWeiEventLog->setInfo($data);

        $result && action_log('编辑', '自定义回复配置，name：' . $data['id']);

        return $result ? [RESULT_SUCCESS, '自定义回复配置成功'] : [RESULT_ERROR, $this->modelWeiEventLog->getError()];
    }
    /**
     * 获取会员信息
     */
    public function getWeiEventInfo($where = [], $field = true)
    {

        return $this->modelWeiEventLog->getInfo($where, $field);
    }

    /**  web端增加用户数据
     * @param array $data
     * @return array
     */
    public function weiEventWebAdd($data = [])
    {
        $this->modelWeiEventLog->setInfo($data);
    }

    /**
     * @param array $data
     */
    public function weiEventWebWebEdit($data = [])
    {
        $this->modelWeiEventLog->setInfo($data);
    }
}
