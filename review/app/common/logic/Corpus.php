<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 综合语料逻辑
 */
class Corpus extends LogicBase
{

      /**
       * 获取综合语料搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获综合语料单条信息
      */
     public function getCorpusInfo($where = [], $field = '*')
     {

        return $this->modelCorpus->getInfo($where, $field);
     }

    /**
     * 获取综合语料列表
     */

    public function getCorpusList($where = [], $field = '', $order = '', $paginate = 0, $group = null)
    {

        !empty($group) &&  $this->modelCorpus->group($group);
        return $this->modelCorpus->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取综合语料无分页列表
     */
    public function getCorpusColumn($where = [], $field = '', $key = '')
    {
        return $this->modelCorpus->getColumn($where, $field , $key);
    }

    /**
     * 综合语料单条编辑
     */
    public function corpusEdit($data = [])
    {
		
        $result = $this->modelCorpus->setInfo($data);
        
        return $result ? $result : $this->modelCorpus->getError();
    }

    /**
     * 综合语料删除
     */
    public function corpusDel($where = [], $is_true = false)
    {

        $result = $this->modelCorpus->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelCorpus->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取综合语料搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['title'] = ['like', '%'.$data['search_data'].'%'];
        !empty($data['attribution_str']) && $where['attribution_str'] = $data['attribution_str'];

        return $where;
    }
	  
	/**
     * 综合语料单条编辑
     */
    public function corpusAdminEdit($data = [])
    {


        $url = url('corpusList');
        
        $data['member_id'] = MEMBER_ID;

        foreach ($data as $key=>&$value){
            $value=html_entity_decode($value);
        }
		
        $result = $this->modelCorpus->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '综合语料' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '综合语料操作成功', $url] : [RESULT_ERROR, $this->modelCorpus->getError()];
    }

    /**
     * 综合语料删除
     */
    public function corpusAdminDel($where = [])
    {

        $result = $this->modelCorpus->deleteInfo($where);
        
        $result && action_log('删除', '综合语料删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '综合语料删除成功'] : [RESULT_ERROR, $this->modelCorpus->getError()];
    }
}
