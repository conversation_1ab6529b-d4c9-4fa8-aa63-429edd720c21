<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\model;
/**
 * 课程模型
 */
class Course extends ModelBase
{
    public function getDetailedsAttr($valve,$data)
    {

        $valve = html_entity_decode($valve);

        return $valve;
    }
}
