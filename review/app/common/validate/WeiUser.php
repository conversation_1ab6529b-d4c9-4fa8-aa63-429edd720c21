<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\validate;

/**
 * 会员验证器
 */
class WeiUser extends ValidateBase
{
    
    // 验证规则
    protected $rule = [
        'username'  => 'require|max:10|unique:username',
        'name'  => 'require|max:25',
        'password'  => 'require|length:6,30',
        'email'     => 'require|email|unique:email',
        'mobile'          => 'require|/^1[34578]{1}[0-9]{9}$/',
        'openid'    => 'require',
        'kf_account'    => 'require|length:1,10|unique:email',
    ];

    // 验证提示
    protected $message = [
        'username.require'    => '用户名不能为空',
        'username.length'     => '用户名长度为6-30个字符之间',
        'username.unique'     => '用户名已存在',
        'name.require'     => '姓名不能为空',
        'name.max'     => '姓名最多不能超过25个字符',
        'password.require'    => '密码不能为空',
        'password.length'     => '密码长度为6-30个字符之间',
        'email.require'       => '邮箱不能为空',    
        'email.email'         => '邮箱格式不正确', 
        'email.unique'        => '邮箱已存在',
        'mobile'   => '请输入正确联系方式',
        'openid.require'       => 'openid不能为空',
        'kf_account.require'    => '名不能为空',
        'kf_account.max'     => '名长度为1-10个字符之间',
        'kf_account.unique'        => '名已存在',
    ];

    // 应用场景
    protected $scene = [
        'add'  =>  ['username','password','email'],
        'UserEdit'  =>  ['username','name','email','mobile'],
        'weiUserOAuth'  =>  ['openid'],
        'KfCustomAdd'  =>  ['kf_account'],
    ];
}
