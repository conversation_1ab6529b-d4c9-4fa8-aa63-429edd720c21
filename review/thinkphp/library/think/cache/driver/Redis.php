<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2017 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\cache\driver;

use think\cache\Driver;

/**
 * Redis缓存驱动，适合单机部署、有前端代理实现高可用的场景，性能最好
 * 有需要在业务层实现读写分离、或者使用RedisCluster的需求，请使用Redisd驱动
 *
 * 要求安装phpredis扩展：https://github.com/nicolasff/phpredis
 * <AUTHOR> <<EMAIL>>
 */
class Redis extends Driver
{
    protected $options = [
        'host'       => '127.0.0.1',
        'port'       => 6379,
        'password'   => '',
        'select'     => 0,
        'timeout'    => 0,
        'expire'     => 0,
        'persistent' => false,
        'prefix'     => '',
    ];

    /**
     * 构造函数
     * @param array $options 缓存参数
     * @access public
     */
    public function __construct($options = [])
    {
        if (!extension_loaded('redis')) {
            throw new \BadFunctionCallException('not support: redis');
        }
        if (!empty($options)) {
            $this->options = array_merge($this->options, $options);
        }
        $func          = $this->options['persistent'] ? 'pconnect' : 'connect';
        $this->handler = new \Redis;

        $this->handler->$func($this->options['host'], $this->options['port'], $this->options['timeout']);

        if ('' != $this->options['password']) {
            $this->handler->auth($this->options['password']);
        }

        if (0 != $this->options['select']) {
            $this->handler->select($this->options['select']);
        }
    }

    /**
     * 判断缓存
     * @access public
     * @param string $name 缓存变量名
     * @return bool
     */
    public function has($name)
    {
        return $this->handler->get($this->getCacheKey($name)) ? true : false;
    }
    /**
     * 读取缓存
     * @access public
     * @param string $name 缓存变量名
     * @param mixed  $default 默认值
     * @return mixed
     */
    public function getKey()
    {
        $value = $this->handler->keys('*');

        return $value;
    }
    /**
     * 读取缓存
     * @access public
     * @param string $name 缓存变量名
     * @param mixed  $default 默认值
     * @return mixed
     */
    public function get($name, $default = false)
    {
        $value = $this->handler->get($this->getCacheKey($name));
        if (is_null($value)) {
            return $default;
        }
        $jsonData = json_decode($value, true);
        // 检测是否为JSON数据 true 返回JSON解析数组, false返回源数据 byron sampson<<EMAIL>>
        return (null === $jsonData) ? $value : $jsonData;
    }

    /**
     * 写入缓存
     * @access public
     * @param string    $name 缓存变量名
     * @param mixed     $value  存储数据
     * @param integer   $expire  有效时间（秒）
     * @return boolean
     */
    public function set($name, $value, $expire = null)
    {

        if (is_null($expire)) {
            $expire = $this->options['expire'];
        }
        if ($this->tag && !$this->has($name)) {
            $first = true;
        }

        $key = $this->getCacheKey($name);

        //对数组/对象数据进行缓存处理，保证数据完整性  byron sampson<<EMAIL>>
        $value = (is_object($value) || is_array($value)) ? json_encode($value) : $value;
        if (!empty($expire)) {
            $result = $this->handler->set($key, $value,$expire);
        } else {
            $result = $this->handler->set($key, $value);
        }


        isset($first) && $this->setTagItem($key);
        return $result;
    }

    /**
     * 自增缓存（针对数值缓存）
     * @access public
     * @param string    $name 缓存变量名
     * @param int       $step 步长
     * @return false|int
     */
    public function inc($name, $step = 1)
    {
        $key = $this->getCacheKey($name);
        return $this->handler->incrby($key, $step);
    }

    /**
     * 自减缓存（针对数值缓存）
     * @access public
     * @param string    $name 缓存变量名
     * @param int       $step 步长
     * @return false|int
     */
    public function dec($name, $step = 1)
    {
        $key = $this->getCacheKey($name);
        return $this->handler->decrby($key, $step);
    }

    /**
     * 删除缓存
     * @access public
     * @param string $name 缓存变量名
     * @return boolean
     */
    public function rm($name)
    {
        return $this->handler->del($this->getCacheKey($name));
    }

    /**
     * 清除缓存
     * @access public
     * @param string $tag 标签名
     * @return boolean
     */
    public function clear($tag = null)
    {
        if ($tag) {
            // 指定标签清除
            $keys = $this->getTagItem($tag);
            foreach ($keys as $key) {
                $this->handler->del($key);
            }
            $this->rm('tag_' . md5($tag));
            return true;
        }
        return $this->handler->flushDB();
    }
    /**
     * @param $key 哈希关键字
     * @param $name 缓存标识
     * @param $value  缓存值
     * @param null $expire  缓存时间
     * @return bool
     */
    public function hset($key,$name, $value, $expire = null)
    {
        if (is_null($expire)) {
            $expire = $this->options['expire'];
        }
        //对数组/对象数据进行缓存处理，保证数据完整性  byron sampson<<EMAIL>>
        $value = (is_object($value) || is_array($value)) ? json_encode($value) : $value;
        $result = $this->handler->hset($key,$name, $value);
        return $result;
    }

    /**
     * @param $key
     * @param $value  批量设置多个key的值 格式$value = [1=>1, 2=>2, 3=>3, 4=>4, 5=>5];
     * @param null $expire
     * @return bool 批量设置多个key的值
     */
    public function hmset($key, $value, $expire = null)
    {
        if (is_null($expire)) {
            $expire = $this->options['expire'];
        }
        //对数组/对象数据进行缓存处理，保证数据完整性  byron sampson<<EMAIL>>
        $value = (is_object($value) || is_array($value)) ? $value : json_decode($value);
        $result = $this->handler->hmset($key, $value);
        return $result;
    }
    /**
     * @param $key 不多少
     * @param $name  不多说
     * @param bool $default
     * @return bool|mixed|string   获取单个组单个name下的数据
     */
    public function hget($key,$name, $default = false)
    {
        $value = $this->handler->hget($key,$name);
        if (is_null($value)) {
            return $default;
        }
        $jsonData = json_decode($value, true);
        // 检测是否为JSON数据 true 返回JSON解析数组, false返回源数据 byron sampson<<EMAIL>>
        return (null === $jsonData) ? $value : $jsonData;
    }

    /**
     * @param $key
     * @param $arr  $arr = [1, 2, 3, 5];
     * @param bool $default
     * @return array|bool|mixed 批量获得额多个key的值
     */
    public function hmget($key,$arr, $default = false)
    {
        $value = $this->handler->hmget($key,$arr);
        if (is_null($value)) {
            return $default;
        }
        if(!empty($value)){
            foreach ($value as $key=>$va){
                $value[$key] = json_decode($va, true);
            }
        }
        // 检测是否为JSON数据 true 返回JSON解析数组, false返回源数据 byron sampson<<EMAIL>>
        return (null === $value) ? $default : $value;
    }

    /**
     * @param $key
     * @param $name
     * @return bool 检测hash中某个key知否存在没有返回false
     */
    public function hexists($key,$name)
    {
        return $this->handler->hexists($key,$name);
    }
    /**
     * @param $key
     * @param bool $default
     * @return array|bool  返回Hash中所有的keys 顺序是随机的
     */
    public function hkeys($key, $default = false)
    {
        $value = $this->handler->hkeys($key);
        if (is_null($value)) {
            return $default;
        }
        return $value;
    }
    /**
     * @param $key
     * @param bool $default
     * @return array|bool  返回Hash所有的值和key 顺序是随机的
     */
    public function hgetall($key, $default = false)
    {
        $value = $this->handler->hgetall($key);
        if (is_null($value)) {
            return $default;
        }
        return $value;
    }
    /**
     * @param $key
     * @param bool $default
     * @return array|bool  获取hash中key的数量
     */
    public function hlen($key, $default = false)
    {
        $value = $this->handler->hlen($key);
        if (is_null($value)) {
            return $default;
        }
        return $value;
    }

    /**
     * @param $key
     * @param $name
     * @return int   删除hash租中其中一个值，全部删除不安全，暂且不写
     */
    public function hdel($key,$name)
    {
        return $this->handler->hdel($key,$name);
    }



    /**
     * @param $key   不多说这是什么
     * @param $value   更不多说这是什么
     * @param null $expire
     * @return int
     */
    public function LPush($key, $value, $expire = null)
    {
        if (is_null($expire)) {
            $expire = $this->options['expire'];
        }
        //对数组/对象数据进行缓存处理，保证数据完整性  byron sampson<<EMAIL>>
        $value = (is_object($value) || is_array($value)) ? json_encode($value) : $value;
        $result = $this->handler->lpush($key, $value);
        return $result;
    }

    /**
     * @param $key
     * @param int $start 默认开始0
     * @param int $end   默认结束-1    0到-1是全部
     * @param bool $default
     * @return array|bool|mixed
     */
    public function lRange($key,$start=0,$end=-1, $default = false)
    {
        $value = $this->handler->lrange($key,$start, $end);
        if (is_null($value)) {
            return $default;
        }
        $jsonData = json_decode($value, true);
        // 检测是否为JSON数据 true 返回JSON解析数组, false返回源数据 byron sampson<<EMAIL>>
        return (null === $jsonData) ? $value : $jsonData;
    }

    /**
     * @param $key 不多少说
     * @param $key2  这是需要说，关键字在这列表里的
     * @param int $start  正说是从左，负数是从右，数字是几个
     * @return int
     */
    public function lRem($key,$key2,$start=0)
    {
        return $this->handler->lrem($key,$key2,$start);
    }
}
