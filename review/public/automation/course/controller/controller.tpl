<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\[controller]\controller;

/**
 * [title]控制器
 */
class [ucwords_name] extends [ucwords_module]Base
{

    /**
     * [title]列表
     */
    public function [lower_name]List()
    {

        $where = $this->logic[upper_name]->getWhere($this->param_data);

        $data=$this->logic[upper_name]->get[upper_name]List($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * [title]无分页列表
     */
    public function [lower_name]Column()
    {

        $data=$this->logic[upper_name]->get[upper_name]Column($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * [title]添加
     */
    public function [lower_name]Add()
    {
	  
	   $regit=$this->logic[upper_name]->[lower_name]Edit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * [title]删除
     */
    public function [lower_name]Del()
    {

       $regit=$this->logic[upper_name]->[lower_name]Del(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
