 body { padding: 0; margin: 0; background:#efeff4;}
 ol,ul,li {padding: 0;  margin: 0; list-style: none}
 h2,h3,h4,h5,ul,li,p,hr {padding: 0;margin: 0;}
 input {outline: none;}
 html{ padding: 0; margin: 0;}
* {
   -webkit-box-sizing: border-box;
   -moz-box-sizing: border-box;
   box-sizing: border-box;
   margin:0;
}
 .am-slider-default .am-direction-nav .am-prev{ display: none;}
 .am-slider-default .am-direction-nav .am-next{ display: none;}
 .tm-head{ background: transparent; top: 0; position: absolute; z-index: 999;}
 .sortbar-fixed{ position: fixed; top: 0; background: #ff6000;}
.ad_img img { border: none; vertical-align: middle; max-width: 100%; font-size:0px;}

 .sq-head{ background: #ff5500; position: fixed; top: 0; z-index: 9999; left: 0;}
 .nav_text{ font-size: 14px; margin-top: -35px;}
 .am-navbar-default .am-navbar-nav{background: #00a6ac;}
.nav_text i.sel {display: inline-block;width: 9px;height: 6px;background-position: -48px 0;margin-left: 4px;margin-bottom: 2px; background:url(../images/common.png) -153px 0px no-repeat;  transition: all .3s linear;  -webkit-transition: all .3s linear;}
.nav_text:hover i.sel{ background-position: -153px -6px; transform: rotate(360deg); -webkit-transform: rotate(360deg);}
.am-header .am-header-nav img{ height: 25px; margin-top: -18px;}
.am-header .am-header-title1 { position: relative;margin: 0 14%;font-size: 1.8rem;font-weight: 400;
    text-align: center; display: block;word-wrap: normal; text-overflow: ellipsis; white-space: nowrap; color: #fff;
}
.am-slider-default .am-control-nav{ bottom: 10px; text-align:center; }
.am-slider-default .am-control-nav li a.am-active{  background: #cc6b17;}
.am-slider-default .am-control-nav li a{ background: #fff; border: 1px solid #cc6b17;}
.am-slider-default{ margin: 0;}


.i_m{ width:100%; padding:0 10px; margin-top:10px;}
.i_m li{ width:50%; float:left; margin-bottom:10px; text-align:center;}
.i_m li a{ display:block; width:100%; padding:10px 0; color:#FFF;}
.i_m li a img{ width:60%; max-width:160px; height:60px; vertical-align:top;}
.i_m li a h2{ width:100%; height:30px; line-height:30px; font-size:15px; font-weight:normal;}
.i_m li.i_m1 a{ background:#2d99ed;}
.i_m li.i_m2 a{ background:#6b6298;}
.i_m li.i_m3 a{ background:#38b48b;}
.i_m li.i_m4 a{ background:#ec6d51;}
.i_ma{ width:100%; padding:0 10px;}

.quanbu-ul1{ width:100%; padding:5px 0; background:#fff; position:fixed; left:0; bottom:0;}
.quanbu-ul1 li{ float:left; width:33.33333%; text-align:center;}
.quanbu-ul1 li .qu-tu1{ width:20px; height:20px; margin:0 auto;}
.qu-tu1 .atu1{}
.qu-tu1 .atu2{ display:none;}
.qu-tu1 img{ display:block; width:100%; height:100%;}
.quanbu-ul1 li .qu-ul1a{ font-size:12px; color:#353535;}
.quanbu-ul1 .current a{ color:#00a6ac;}
.quanbu-ul1 .current .atu1{ display:none;}
.quanbu-ul1 .current .atu2{ display:block;}

.icon{background-image:url(../images/icon_index.png);background-repeat:no-repeat;display:inline-block;background-size:auto 180px;}
ul.ab_list li{display:-webkit-box; background:#fff;padding:10px 10px 10px 18px; margin-bottom:10px;}
ul.ab_list li:last-child{border-bottom:none;}
ul.ab_list li .ab_l{width:56px;margin-right:23px;}
ul.ab_list li .ab_r{-webkit-box-flex:1;}
ul.ab_list li .ab_r h3{margin-bottom:3px;color:#4b4b4b;font-size:16px; padding-top:3px;}
ul.ab_list li .ab_l i{width:56px;height:56px;vertical-align:middle;}
ul.ab_list li .ab_l i.pj{background-position:0 -61px}
ul.ab_list li .ab_l i.za{background-position:-69px -62px}
ul.ab_list li .ab_l i.yc{background-position:-135px -62px}
.f12{ font-size:14px;} .gray3 { color: #989898;}

/***个人中心***/
header.glue2{width:100%;background:#008792;background-size:100%}
header.glue2 div.top{padding-top:10px;height:45px;position:relative}
header.glue2 div.top .big{float:left;margin-left:10px;line-height:45px;font-size:14px;color:#FFF;font-weight:400}
header.glue2 div.top .big span{display:inline-block;width:35px;height:35px;border-radius:200px}
header.glue2 div.top .big span img{width:35px;height:35px;border-radius:200px;background-size:35px auto}
header.glue2 div.top a.left_a{float:left;margin-left:15px;color:#FFF;font-size:15px;line-height:47px;padding-top:0}
header.glue2 div.top a.right_a{float:right;margin-right:15px;color:#FFF;font-size:15px;line-height:47px;padding-top:0}
header.glue2 div.cont{width:100%;height:145px}
header.glue2 div.cont div.cont_list{text-align:center;padding-top:22px;position:relative}
header.glue2 div.cont div.cont_list span{ display:block; text-align:center; color:#fff;}
header.glue2 div.cont div.cont_list span.ttprice{ font-size:40px;}
header.glue2 div.cont div.cont_list span.tttext{ font-size:16px; color:#ffc948;}
header.glue2 div.butt{width:100%;height:70px;background:rgba(255,255,255,.1)}
header.glue2 div.butt ul.butt_list{width:100%;position:relative}
header.glue2 div.butt ul.butt_list li{float:left;width:50%;text-align:center;position:relative;}
header.glue2 div.butt ul.butt_list li:nth-of-type(1):after{content:" ";border-right:1px solid #e0b49d;position:absolute;height:50px;top:10px;left:100%}
header.glue2 div.butt ul.butt_list li span{color:#fff;display:block;opacity:1; margin:0px; padding:0px;}
header.glue2 div.butt ul.butt_list li span.ttprice{ font-size:25px;}
header.glue2 div.butt ul.butt_list li span.tttext{ font-size:14px; color:#ffc948;}

 header.glue3{width:100%;background-size:100%}
 header.glue3 div.cont div.cont_list{text-align:center;padding-top:32px;position:relative}
 header.glue3 div.cont div.cont_list span{ display:block; text-align:center; color:#008792;}
 header.glue3 div.cont div.cont_list span.ttprice{ font-size:40px;}
 header.glue3 div.cont div.cont_list span.tttext{ font-size:16px; color:#2d99ed;}


.user_nav_list{border-top-width:1px;border-left-width:1px;border-top-style:solid;border-left-style:solid;border-top-color:#EBEBEB;border-left-color:#EBEBEB; background:#fff; margin:10px 0px;}
.user_nav_list li{border-right-width:1px;border-bottom-width:1px;border-right-style:solid;border-bottom-style:solid;border-right-color:#EBEBEB;border-bottom-color:#EBEBEB;height:50px}
.user_nav_list a{display:block;color:#666;text-decoration:none;width:100%;float:left}
.user_nav_list .u_nav_icon{float:left;margin-left:2%;height:30px;width:30px;background-size:90%;margin-top:10px}
.user_nav_list .znx{background-image:url(../images/iconfont-zhanneixin.png); background-repeat:no-repeat}
.user_nav_list .money{background-image:url(../images/iconfont-jiaofei.png);background-repeat:no-repeat}
.user_nav_list .jine{background-image:url(../images/iconfont-jifen.png);background-repeat:no-repeat}
.user_nav_list .huibi{background-image:url(../images/iconfont-hui.png);background-repeat:no-repeat}
.user_nav_list .dingdan{background-image:url(../images/iconfont-dingdan%20.png);background-repeat:no-repeat}
.user_nav_list .tixian{background-image:url(../images/iconfont-wiappquxianguanli.png);background-repeat:no-repeat}
.user_nav_list .anqun{background-image:url(../images/iconfont-anqun.png);background-repeat:no-repeat}
.user_nav_list .message{background-image:url(../images/iconfont-zhanneixin.png);background-repeat:no-repeat}
.user_nav_list .u_nav_name{float:left;margin-left:1.5%;line-height:50px;font-size:15px}
.user_nav_list .u_money{position:relative;font-size:13px;color:#666;line-height:51px;float:right}
.user_nav_list .u_money i{color:#ff6063;font-style:normal}
.user_nav_list .nt_icon{background-image:url(../images/iconfont-jiantou.png);background-repeat:no-repeat;background-position:center;background-size:15px;height:50px;width:32px;float:right;margin-right:1%}
.user_nav_list span.upload_num { width: 10px; height: 10px; line-height:10px; border-radius: 50%; background:#F00;
    color: #cb2527; display: block; float:right; margin-top:20px;}
 .add-input{ float: left; background: transparent; border: 0;  height: 35px; line-height: 35px; font-family: 'Microsoft YaHei',Arial, Helvetica, sans-serif; color:#999;}
 .product_sq{padding:0 5%;width:100%;overflow: hidden;background:#fff;}
 .product_sq span{ float: left; width: 25%; color: #666; text-align: left; font-size:14px; height: 35px; line-height: 35px;}
 .product_sq li{ padding: 0.5rem 0; overflow: hidden; border-bottom: 1px solid #ddd;}
 .product_sq a.fscode{ color:#00a6ac; position:absolute; right:30px; line-height:2.8em;}
 .product_sq a.fasong{ color:#b2b2b2;}
/**投诉**/
.say_more{ padding:1em 0; width:92%; margin-left:4%; line-height:22px; color:#212121; font-family: 'Microsoft YaHei',Arial, Helvetica, sans-serif; font-size:14px;}
.evaluate_form{  height:160px; width:92%; margin-left:4%; position:relative;}
textarea.evaluate{ width:100%; height:160px; line-height:22px; color:#191919; font-family: 'Microsoft YaHei',Arial, Helvetica, sans-serif; padding:5px; border:0px;}
textarea:focus{border:0; outline:none;}
.evaluate_form span{bottom: 10px;position: absolute;right:10px; color:#797878}
.confirm{ background-color:#00a6ac; color:#fff; border-radius:3px; width:92%; height:40px; line-height:40px; text-align:center; font-size:16px; margin-left:4%;margin-top:14px; border:none; font-family: 'Microsoft YaHei',Arial, Helvetica, sans-serif;display: block;}

/*交易记录*/
.detail_title{ width:21%; height:39px; line-height:39px; text-align:center; margin-left:auto; margin-right:auto; position:relative; color:#65646b; font-size:12px;}
.detail_title .border_top{ width:100px; top:19px;}
.detail{ height:75px; position:relative; background-color:#fff; padding-left:4%; padding-top:8px; box-sizing:border-box; border-bottom:1px solid #e0e0e0;}
.detail h1{ line-height:20px; font-size:14px; color:#191919; font-weight:normal;}
.detail h2{ line-height:20px; font-size:12px; color:#747474; font-weight:normal;}
.detail h3{ line-height:20px; font-size:12px; color:#747474; font-weight:normal;}
.detail p{ line-height:75px; position:absolute;right:4%; top:0; color:#414141; font-size:16px;}

/***设置交易密码***/
.pay-iphone{ margin: 1rem 5%; color: #4b4b4b;}
.payment{ width: 90%; margin: 0 5%; overflow: hidden; height: 50px; border: 1px solid #ddd; background: #fff; border-radius: 5px;}
.payment li{ width: 16.6%; height: 50px; border-right: 1px solid #ddd; float: left;}
.payment li:last-child{ border-right: 0;}
.payment input{ border: 0; width: 100%; line-height: 50px; text-align: center;}

/***提现***/
.card-list{ background:#22a5b4; margin:18px 4% 10px; padding:16px; border-radius:4px;}
.card-list .logo{ width:40px; height:40px; overflow:hidden; float:left;}
.card-list .logo img{ width:40px; height:40px; display:block;}
.card-list .text{ color:#fff; margin-left:48px;}
.card-list .text .name{ font-size:16px; line-height:40px;}
.card-list .text .info{ margin-top:4px; text-align:right; font-size:16px;}

.card-info{ background:#fff; margin:8px 0px; padding:16px; border-radius:4px;}
.card-info .logo{ width:40px; height:40px; overflow:hidden; float:left;}
.card-info .logo img{ width:40px; height:40px; display:block;}
.card-info .text{ color:#666; margin-left:48px;}
.card-info .text .name{ font-size:16px;}
.card-info .text .info{ margin-top:4px;}

/***提现说明***/
.about{ color: #333333; overflow: hidden; padding: 20px 4%; padding-bottom:30px;}
.about h1{ padding-left:10px; font-weight:bold; font-size:16px; border-left:2px solid #00a6ac; }
.about p{ font-size:14px; line-height:26px; text-indent:0em; padding:20px 0px;}

/***系统消息***/
.mess li{padding: 0 5%; margin-top: 10px; overflow: hidden; background: #fff; border-top: 1px solid #ddd; border-bottom: 1px s #ddd;}
.mess li a{ color: #000;}
.mess li h2{ font-weight: normal; line-height: 40px; font-size:18px; margin-top: 5px;}
.mess li .con{ color: #808080; font-size:14px; padding-bottom:5px;}
.mess li .time{ border-top: 1px solid #ddd; font-size:14px; line-height: 40px;}
.mess li .time i{  float: right;font-style: normal;}
.mess li .time span{ float: left; }

/***消息列表***/
.message{ background:#fff; overflow:hidden;}
.message li{ padding:.5rem 1rem; border-bottom: 1px solid #ddd; overflow: hidden;}
.message li img{ float: left;}
.message .am-icon-angle-rights{ float: right; margin-top: 0.7rem; width:10px; height:10px; border:1px solid #6b6b6b; border-top: none; border-right: none; transform:rotate(225deg); -o-transform: rotate(225deg); /* Opera浏览器 */
-webkit-transform: rotate(225deg); /* Webkit内核浏览器 */
-moz-transform: rotate(225deg); /* Firefox浏览器 */ float: right; margin-top:15px; margin-right:10px;}
.message li a{ color: #242424; overflow: hidden; display: block;}
.message li i{ float: left; font-style:normal; margin-left: 10px; font-size:16px;  margin-top: 0.5rem;}
.message li em{ float:right; font-style:normal; margin-right: 10px; font-size:16px;  margin-top: 0.5rem; color:#00a6ac}
.message li p{ color: #d3d3d3; text-indent: 50px; font-size: 1.4rem;}
.add-inputtext{width: 92%; padding-left:2%; margin-left:4%; background:#fff; border: 0;  height: 35px; line-height: 35px; font-family: 'Microsoft YaHei',Arial, Helvetica, sans-serif; margin-top:10px;}
.am-rights{ float: right; margin-top: 0.7rem; width:10px; height:10px; border:1px solid #6b6b6b; border-top: none; border-right: none; transform:rotate(225deg); -o-transform: rotate(225deg); /* Opera浏览器 */
-webkit-transform: rotate(225deg); /* Webkit内核浏览器 */
-moz-transform: rotate(225deg); /* Firefox浏览器 */ float: right; margin-top:15px; margin-right:10px;}
.am-icons{ float: right; margin-top: 0.7rem; width:14px; height:14px;margin-right:10px;}
.am-icon-img{ float:right; border-radius: 50%; width:35px; height:35px; margin-right: 10px;
    background-size: cover; background-color: #ffb385;}


 /*基础数据*/
 .four-text {
     background: #fff;
     padding: 0.5rem 0.4rem;
     margin-bottom: 4%;
 }

 .four-text ul {
     display: flex;
     justify-content: space-between;
     -webkit-justify-content: center;
 }

 .four-text ul li {
     width: 48%;
     border-radius: 4px;
     padding: 20px 12px;
 }

 .four-ul {
     margin-bottom: 12px;
 }

 .four-text ul li .box-text p {
     display: inline-block;
     color: #fff;
     font-size: 0.48rem;
 }

 .aidou-text {
     display: flex;
     display: -webkit-box;
     display: -webkit-flex;
     display: -ms-flexbox;
 }

 .four-text ul li .box-text_2 {
     padding-left: 0.4rem;
     margin-top: 0.3rem;
 }

 .four-text ul li .box-text_2 p {
     font-size: 0.4rem;
     color: #fff;
 }

 .four-text ul li i {
     display: inline-block;
     width: 34px;
     height: 34px;
     margin: 0 auto;
     background: #fff;
     border-radius: 50%;
     text-align: center;
     line-height: 34px;
     font-size: 20px;
 }

 .four-text ul li i.zise {
     color: #8285FF;
 }

 .four-text ul li i.hongse {
     color: #FE7FB2;
 }

 .four-text ul li i.fense {
     color: #FFC6B5;
 }

 .four-text ul li i.lanse {
     color: #489AFF;
 }
 /*分页*/
 .box-footer{
     text-align: center;
 }
 .pagination {
     display: inline-block;
     padding-left: 0;
     margin: 20px 0;
     border-radius: 4px;
 }
 .pagination > li {
     display: inline;
 }
 .pagination > li > a,
 .pagination > li > span {
     position: relative;
     float: left;
     padding: 6px 12px;
     line-height: 1.42857143;
     text-decoration: none;
     color: #337ab7;
     background-color: #fff;
     border: 1px solid #ddd;
     margin-left: -1px;
 }
 .pagination > li:first-child > a,
 .pagination > li:first-child > span {
     margin-left: 0;
     border-bottom-left-radius: 4px;
     border-top-left-radius: 4px;
 }
 .pagination > li:last-child > a,
 .pagination > li:last-child > span {
     border-bottom-right-radius: 4px;
     border-top-right-radius: 4px;
 }
 .pagination > li > a:hover,
 .pagination > li > span:hover,
 .pagination > li > a:focus,
 .pagination > li > span:focus {
     z-index: 2;
     color: #23527c;
     background-color: #eeeeee;
     border-color: #ddd;
 }
 .pagination > .active > a,
 .pagination > .active > span,
 .pagination > .active > a:hover,
 .pagination > .active > span:hover,
 .pagination > .active > a:focus,
 .pagination > .active > span:focus {
     z-index: 3;
     color: #fff;
     background-color: #337ab7;
     border-color: #337ab7;
     cursor: default;
 }
 .pagination > .disabled > span,
 .pagination > .disabled > span:hover,
 .pagination > .disabled > span:focus,
 .pagination > .disabled > a,
 .pagination > .disabled > a:hover,
 .pagination > .disabled > a:focus {
     color: #777777;
     background-color: #fff;
     border-color: #ddd;
     cursor: not-allowed;
 }
 .pagination-lg > li > a,
 .pagination-lg > li > span {
     padding: 10px 16px;
     font-size: 18px;
     line-height: 1.3333333;
 }
 .pagination-lg > li:first-child > a,
 .pagination-lg > li:first-child > span {
     border-bottom-left-radius: 6px;
     border-top-left-radius: 6px;
 }
 .pagination-lg > li:last-child > a,
 .pagination-lg > li:last-child > span {
     border-bottom-right-radius: 6px;
     border-top-right-radius: 6px;
 }
 .pagination-sm > li > a,
 .pagination-sm > li > span {
     padding: 5px 10px;
     font-size: 12px;
     line-height: 1.5;
 }
 .pagination-sm > li:first-child > a,
 .pagination-sm > li:first-child > span {
     border-bottom-left-radius: 3px;
     border-top-left-radius: 3px;
 }
 .pagination-sm > li:last-child > a,
 .pagination-sm > li:last-child > span {
     border-bottom-right-radius: 3px;
     border-top-right-radius: 3px;
 }
 .pager {
     padding-left: 0;
     margin: 20px 0;
     list-style: none;
     text-align: center;
 }
 .pager li {
     display: inline;
 }
 .pager li > a,
 .pager li > span {
     display: inline-block;
     padding: 5px 14px;
     background-color: #fff;
     border: 1px solid #ddd;
     border-radius: 15px;
 }
 .pager li > a:hover,
 .pager li > a:focus {
     text-decoration: none;
     background-color: #eeeeee;
 }
 .pager .next > a,
 .pager .next > span {
     float: right;
 }
 .pager .previous > a,
 .pager .previous > span {
     float: left;
 }
 .pager .disabled > a,
 .pager .disabled > a:hover,
 .pager .disabled > a:focus,
 .pager .disabled > span {
     color: #777777;
     background-color: #fff;
     cursor: not-allowed;
 }




