/*
* Tencent is pleased to support the open source community by making WeUI available.
*
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
*
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
*
*       http://opensource.org/licenses/MIT
*
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "./base/reset";

// theme
@import "./base/theme/index";

// icon font
@import "./icon/weui-icon";


// text
@import "./widget/weui-text/weui-link";

// button
@import "./widget/weui-button/weui-button";

// cell
@import "./widget/weui-cell/weui-cell_global";
@import "./widget/weui-cell/weui-cell_swiped";
@import "./widget/weui-cell/weui-access";
@import "./widget/weui-cell/weui-check";
@import "./widget/weui-cell/weui-form";
@import "./widget/weui-cell/weui-gallery";
@import "./widget/weui-cell/weui-switch";
@import "./widget/weui-cell/weui-uploader";

// msg
@import "./widget/weui-page/weui-msg";

// form
@import "./widget/weui-page/weui-form";

// article
@import "./widget/weui-page/weui-article";

// tab
@import "./widget/weui-tab/weui-tab";

// progress
@import "./widget/weui-progress/weui-progress";

// panel
@import "./widget/weui-panel/weui-panel";

// media box
@import "./widget/weui-media-box/weui-media-box";

// grid
@import "./widget/weui-grid/weui-grid";

// copyright
@import "./widget/weui-footer/weui-footer";


// flex
@import "./widget/weui-flex/weui-flex";

// tips
@import "./widget/weui-tips/weui-dialog";
@import "./widget/weui-tips/weui-half-screen-dialog";
@import "./widget/weui-tips/weui-toast";
@import "./widget/weui-tips/weui-mask";
@import "./widget/weui-tips/weui-actionsheet";
@import "./widget/weui-tips/weui-loadmore";
@import "./widget/weui-tips/weui-badge";
@import "./widget/weui-tips/weui-toptips";

//searchbar
@import "./widget/weui-searchbar/weui-searchbar";

// picker
@import "./widget/weui-picker/weui-picker";

// animate
@import "./widget/weui-animate/weui-animate";

// agree
@import "./widget/weui-agree/weui-agree";

// loading
@import "./widget/weui-loading/weui-loading";

// slider
@import "./widget/weui-slider/weui-slider";



// 小程序 patch 兼容文件，放在最后
@import "./base/patch";
