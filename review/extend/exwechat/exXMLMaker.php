<?php

namespace exwechat;
class exXMLMaker
{
    private $_FromUserName = '';
    private $_ToUserName = '';

    public function __construct($UserName)
    {
        if (empty($UserName)) {
            $msg = exRequest::instance()->getMsg();
            $this->_FromUserName = $msg['FromUserName'];
            $this->_ToUserName = $msg['ToUserName'];
        } else {
            $this->_FromUserName = $UserName['FromUserName'];
            $this->_ToUserName = $UserName['ToUserName'];
        }
    }

    /**
     * @param string $contentStr 内容
     * @param string $from openid
     * @param string $to 微信名
     * @return string    发送文本
     */
    public function response_text($contentStr = '', $from = '', $to = '')
    {
        $FromUserName = empty($from) ? $this->_FromUserName : $from;
        $ToUserName = empty($to) ? $this->_ToUserName : $to;
        empty($contentStr) ? $contentStr = "谢谢关注" : $contentStr;
        $textTpl = "<xml>
                    <ToUserName><![CDATA[" . $FromUserName . "]]></ToUserName>
                    <FromUserName><![CDATA[" . $ToUserName . "]]></FromUserName>
                    <CreateTime>" . time() . "</CreateTime>
                    <MsgType><![CDATA[text]]></MsgType>
                    <Content><![CDATA[" . $contentStr . "]]></Content>
                    </xml>";

        return $textTpl;
    }

    /**
     * @param string $media_id 图片media_id
     * @param string $from openid
     * @param string $to 微信名
     * @return string    发送图片
     */
    public function response_image($media_id = '', $from = '', $to = '')
    {

        $FromUserName = empty($from) ? $this->_FromUserName : $from;
        $ToUserName = empty($to) ? $this->_ToUserName : $to;
        $str = '<xml>';
        $str .= '<ToUserName><![CDATA[' . $FromUserName . ']]></ToUserName>';
        $str .= '<FromUserName><![CDATA[' . $ToUserName . ']]></FromUserName>';
        $str .= '<CreateTime>' . time() . '</CreateTime>';
        $str .= '<MsgType><![CDATA[image]]></MsgType>';
        $str .= '<Image>';
        $str .= '<MediaId><![CDATA[' . $media_id . ']]></MediaId>';
        $str .= '</Image>';
        $str .= '</xml>';

        return $str;
    }

    /**
     * @param string $media_id 语音media_id
     * @param string $from openid
     * @param string $to 微信名
     * @return string    发送语音
     */
    public function response_voice($media_id = '', $from = '', $to = '')
    {
        $FromUserName = empty($from) ? $this->_FromUserName : $from;
        $ToUserName = empty($to) ? $this->_ToUserName : $to;
        $str = '<xml>';
        $str .= '<ToUserName><![CDATA[' . $FromUserName . ']]></ToUserName>';
        $str .= '<FromUserName><![CDATA[' . $ToUserName . ']]></FromUserName>';
        $str .= '<CreateTime>' . time() . '</CreateTime>';
        $str .= '<MsgType><![CDATA[voice]]></MsgType>';
        $str .= '<Voice>';
        $str .= '<MediaId><![CDATA[' . $media_id . ']]></MediaId>';
        $str .= '</Voice>';
        $str .= '</xml>';
        return $str;
    }

    /**
     * @param string $data title标题   description描述
     * @param string $media_id 视频media_id
     * @param string $from openid
     * @param string $to 微信名
     * @return string    发送视频
     * @return string
     */
    public function response_video($media_id = '', $data = '', $from = '', $to = '')
    {
        $FromUserName = empty($from) ? $this->_FromUserName : $from;
        $ToUserName = empty($to) ? $this->_ToUserName : $to;
        $str = '<xml>';
        $str .= '<ToUserName><![CDATA[' . $FromUserName . ']]></ToUserName>';
        $str .= '<FromUserName><![CDATA[' . $ToUserName . ']]></FromUserName>';
        $str .= '<CreateTime>' . time() . '</CreateTime>';
        $str .= '<MsgType><![CDATA[Video]]></MsgType>';
        $str .= '<Video>';
        $str .= '<MediaId><![CDATA[' . $media_id . ']]></MediaId>';
        $str .= '<Title><![CDATA[' . $data['title'] . ']]></Title>';
        $str .= '<Description><![CDATA[' . $data['description'] . ']]></Description>';
        $str .= '</Video>';
        $str .= '</xml>';
        return $str;
    }

    /**
     * @param string $media_id  音乐media_id
     * @param string $data   title标题   description描述 music_url音乐地址 hqmusic_url高清音乐地址 thumb_media_id图片地址
     * @param string $from
     * @param string $to
     * @return string
     */
    public function response_music( $data = '', $from = '', $to = '')
    {
        $FromUserName = empty($from) ? $this->_FromUserName : $from;
        $ToUserName = empty($to) ? $this->_ToUserName : $to;
        $str = '<xml>';
        $str .= '<ToUserName><![CDATA[' . $FromUserName . ']]></ToUserName>';
        $str .= '<FromUserName><![CDATA[' . $ToUserName . ']]></FromUserName>';
        $str .= '<CreateTime>' . time() . '</CreateTime>';
        $str .= '<MsgType><![CDATA[music]]></MsgType>';
        $str .= '<Music>';
        $str .= '<Title><![CDATA[' . $data['title'] . ']]></Title>';
        $str .= '<Description><![CDATA[' . $data['description'] . ']]></Description>';
        $str .= '<MusicUrl><![CDATA[' . $data['music_url'] . ']]></MusicUrl>';
        $str .= '<HQMusicUrl><![CDATA[' . $data['hqmusic_url'] . ']]></HQMusicUrl>';
        $str .= '<ThumbMediaId><![CDATA[' . $data['thumb_media_id'] . ']]></ThumbMediaId>';
        $str .= '</Music>';
        $str .= '</xml>';
        return $str;
    }
    /**
     * @param array $newres 图文消息
     * @param string $from openid
     * @param string $to 微信名
     * @return string    发送图文
     */
    public function response_news($newres = [], $from = '', $to = '')
    {
        $FromUserName = empty($from) ? $this->_FromUserName : $from;
        $ToUserName = empty($to) ? $this->_ToUserName : $to;
        $str = "<xml>";
        $str .= "<ToUserName><![CDATA[" . $FromUserName . "]]></ToUserName>";
        $str .= "<FromUserName><![CDATA[" . $ToUserName . "]]></FromUserName>";
        $str .= "<CreateTime>" . time() . "</CreateTime>";
        $str .= "<MsgType><![CDATA[news]]></MsgType>";
        $str .= "<ArticleCount>" . count($newres) . "</ArticleCount>";
        $str .= "<Articles>";

        foreach ($newres as $value) {
            // url关键字替换成用户openid
            $url = str_replace('openidvalue', $FromUserName, $value['url']);

            $str .= "<item>";
            $str .= "<Title><![CDATA[" . $value['title'] . "]]></Title>";
            $str .= "<Description><![CDATA[" . $value['description'] . "]]></Description>";
            $str .= "<PicUrl><![CDATA[" . $value['picurl'] . "]]></PicUrl>";
            $str .= "<Url><![CDATA[" . $url . "]]></Url>";
            $str .= "</item>";
        }
        $str .= "</Articles>";
        $str .= "</xml>";
        return $str;
    }

    /**
     * @param array $data
     * @param array $cdata
     * @return string通用数组转xml数据结构
     */
    public static function arrToXml(array $data, $cdata=[])
    {
        $cdataKey = array_merge(['detail', 'scene_info'], $cdata);
        $xml = '<xml>';
        foreach ($data as $key => $value) {
            if(in_array($key, $cdataKey)){
                $xml .='<'.$key.'><![CDATA['.$value.']]></'.$key.'>';
            }else{
                $xml .='<'.$key.'>'.$value.'</'.$key.'>';
            }
        }
        $xml .='</xml>';
        return $xml;
    }
}
