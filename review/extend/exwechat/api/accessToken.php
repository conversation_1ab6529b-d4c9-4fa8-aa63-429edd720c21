<?php

namespace exwechat\api;

use think\Cache;
use exwechat\api\JSSDK\JSSDK;

/**
 * 获取accessToken
 * <AUTHOR> <<EMAIL>>
 */
class accessToken extends AbstractApi
{
    // private $str = '{"access_token":"8-Ul_wDeEqs8dptGPrb5Mjdw05g9W705ot4cHeyq1rfyASsCJ7ZyIwltWNjFd3fTPClDmsZDwGL4Rxbh8IWCcpbKekubbMPCAeIKBre1dRydhe47UxwIqvKr3fjBf6lxJQTfAEAELO","expires_in":7200}';
    private $url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s';

    private $appid;
    private $secret;

    public function __construct($appid = '', $secret = '', $token = '', $encodingAesKey = '')
    {
        $this->appid = $appid;
        $this->secret = $secret;
        $this->token = $token;
        $this->encodingAesKey = $encodingAesKey;
    }

    /**
     * 缓存token
     * @return [type] [description]
     * <AUTHOR>
     */
    public function getToken()
    {

    }

    /**
     * 从微信服务器获取accessToken
     * @return [type] [description]
     * <AUTHOR>
     */
    public function getAccessToken()
    {
        $appidAccessToken = Cache::store('redis')->hget('access_token_ticket', $this->appid . "_access");
        //Cache::store('redis')->rm($this->appid."_access");
        if ($appidAccessToken['expires_time'] < (time() - 7100)) {
            $url = sprintf($this->url, $this->appid, $this->secret);
            $ret = http::curl_get($url);

            if (!$ret[0]) { // 正确执行

                $regitData = json_decode($ret[1], true);

                if (empty($regitData['errcode'])) {
                    $this->data = $regitData;
                    $this->data['expires_time'] = time();
                    $ticket = new JSSDK($this->data['access_token']);
                    $this->data['ticket'] = $ticket->get_jsapi_ticket()['ticket'];
                    Cache::store('redis')->hset('access_token_ticket', $this->appid . "_access", $this->data);
                } else {
                    $this->data = ['errcode' => $regitData['errcode'], 'errmsg' => $regitData['errmsg'],'access_token'=>0];
                    $this->errorCode = $regitData['errcode'];
                    $this->errorMsg = $regitData['errmsg'];
                }

            } else { // 有错误码
                $this->errorCode = $ret[0];
                $this->errorMsg = $ret[1];
            }
        } else {
            $this->data = $appidAccessToken;
        }

        return $this->data;
    }

}
