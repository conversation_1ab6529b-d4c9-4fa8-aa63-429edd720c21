<?php
namespace exwechat\api\message;

use exwechat\api\AbstractApi;
use exwechat\api\http;
/**
 * 模板消息
 * <AUTHOR> <<EMAIL>>
 */
class newtmpl extends AbstractApi
{
    // 获取小程序账号的类目 GET
    private $url_get_industry = 'https://api.weixin.qq.com/wxaapi/newtmpl/getcategory?access_token=%s';
    // 获取模板列表 GET
    private $url_all_template = 'https://api.weixin.qq.com/wxaapi/newtmpl/gettemplate?access_token=%s';
    // 删除模板 POST
    private $url_del_template = 'https://api.weixin.qq.com/wxaapi/newtmpl/deltemplate?access_token==%s';
    // 获取模板标题下的关键词列表 GET
    private $url_key_words_template = 'https://api.weixin.qq.com/wxaapi/newtmpl/getpubtemplatekeywords?';
    // 发送模板消息 POST
    private $url_send = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s';
    // 获取帐号所属类目下的公共模板标题 GET
    private $get_pubtemplate = 'https://api.weixin.qq.com/wxaapi/newtmpl/getpubtemplatetitles?';

    private $token;
    public function __construct($token='')
    {
        $this->token = $token;
    }

    // 发送模板消息
    public function send($data)
    {
        $url = sprintf($this->url_send, $this->token);
        $json = is_array($data) ? json_encode($data) : $data;
        $ret = http::curl_post($url, $json);
        return $this->HandleRet($ret);
    }

    // 删除模板消息
    public function del($data)
    {
        $url = sprintf($this->url_del_template, $this->token);
        $json = is_array($data) ? json_encode($data) : $data;
        $ret = http::curl_post($url, $json);
        return $this->HandleRet($ret);
    }

    // 获取获取小程序账号的类目
    public function get_industry()
    {
        $url = sprintf($this->url_get_industry, $this->token);
        $ret = http::curl_get($url);
        return $this->HandleRet($ret);
    }

    // 获取模板列表
    public function get_all_template()
    {
        $url = sprintf($this->url_all_template, $this->token);
        $ret = http::curl_get($url);
        return $this->HandleRet($ret);
    }
    // 删除模板消息
    public function key_word($data)
    {
        $data['access_token']=$this->token;
        $ret = http::curl_get($this->url_key_words_template.http_build_query($data));
        return $this->HandleRet($ret);
    }
    // 获取帐号所属类目下的公共模板标题
    public function get_pubtemplate($data)
    {
        $data['access_token']=$this->token;
        $ret = http::curl_get($this->get_pubtemplate.http_build_query($data));
        return $this->HandleRet($ret);
    }
}
