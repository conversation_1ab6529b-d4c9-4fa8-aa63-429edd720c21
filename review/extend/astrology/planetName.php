<?php

namespace astrology;

class planetName
{
    public static $planetEnglish = array(
        '0' => 'Sun',
        '1' => 'Moon',
        '2' => 'Mercury',
        '3' => 'Venus',
        '4' => 'Mars',
        '5' => 'Jupiter',
        '6' => 'Saturn',
        '7' => 'Uranus',
        '8' => 'Neptune',
        '9' => 'Pluto',
        '10' => 'Ascendant',
        '11' => 'MC',
        '12' => 'ARMC',
        '13' => 'Vertex',
        '14' => 'equatAsc',
        '15' => 'co-AscWKoch',
        '16' => 'co-AscMunkasey',
        '17' => 'PolarAsc',
        '18' => 'Des',
        '19' => 'IC',
        '20' => 'Sun-Moon',
        '21' => 'MeanSouthNode',
        'SunMoon' => 'Sun-Moon',
        'Des' => 'Descending',
        'IC' => 'Descending',
        'c' => 'intpApogee',
        'e' => 'print a line of labels',
        'g' => 'intpPerigee',
        'm' => 'meanNode',
        'n' => 'Nutation',
        'o' => 'EclObl',
        'q' => 'DeltaT',
        'x' => 'SiderealTime',
        't' => 'trueNode',
        'y' => 'TimeEqu',
        'z' => 'xz',
        'w' => "Waldemath's dark Moon",

        'A' => 'meanApogee',
        'B' => 'oscApogee',
        'C' => '666',
        'D' => 'Chiron',
        'E' => 'Pholus',
        'F' => 'Ceres',
        'G' => 'Pallas',
        'H' => 'Juno',
        'I' => 'Vesta',
        's' => 'xs',
        'J' => 'Cupido',
        'K' => 'Hades',
        'L' => 'Zeus',
        'M' => 'Kronos',
        'N' => 'Apollon',
        'O' => 'Admetos',
        'P' => 'Vulcanus',
        'Q' => 'Poseidon',
        'R' => 'Isis-Transpluto',
        'S' => 'Nibiru',
        'T' => 'Harrington',
        'U' => "Leverrier(Neptune)",
        'V' => "Adams(Neptune)",
        'W' => "Lowell(Pluto)",
        'X' => "Pickering(Pluto)",
        'Y' => 'Vulcan',
        'Z' => 'Selena/WhiteMoon',

        'pFortune' => 'PartOfFortune',

        'xs136199' => 'Eris',
        'xs136472' => 'Makemake',
        'xs136108' => 'Haumea',

        'xs433' => 'Eros',
        'xs16' => 'Psyche',

    );


    public static $planetChinese = array('0' => '太阳',
        '1' => '月亮',
        '2' => '水星',
        '3' => '金星',
        '4' => '火星',
        '5' => '木星',
        '6' => '土星',
        '7' => '天王星',
        '8' => '海王星',
        '9' => '冥王星',

        '10' => '上升',
        '11' => '中天',
        '12' => 'ARMC',
        '13' => '宿命',
        '14' => '东升',
        '15' => 'co-Asc.W.Koch',
        '16' => 'co-AscMunkasey',
        '17' => '上升的',
        '18' => '下降',
        '19' => '天底',
        '20' => '日月中',
        '21' => '南交',

        'c' => '月亮远地点',
        'e' => '打印的标签',
        'g' => '月亮近地点',
        'm' => '北交',
        'n' => '章动',
        'o' => '倾斜的黄道',
        'q' => 'Delta T',
        'y' => '一次方程',
        's' => '小行星,MPC在x数量',
        't' => '真实月焦点',
        'x' => '恒星时',
        'w' => "暗月",
        'z' => '小星星,在xz数量',

        'A' => '莉莉丝',
        'B' => '密切月球远地点',
        'C' => '地球',
        'D' => '凯龙',
        'E' => '人龙',
        'F' => '谷神',
        'G' => '智神',
        'H' => '婚神',
        'I' => '灶神',
        'J' => '丘比特',
        'K' => '哈迪斯',
        'L' => '宙斯',
        'M' => '克洛诺斯',
        'N' => '阿波罗',
        'O' => '安德门图斯',
        'P' => '弗卡奴斯',
        'Q' => '波塞冬',
        'R' => '伊希斯(西维因)',
        'S' => '尼比奴(西琴)',
        'T' => '哈林顿',
        'U' => "勒维烈的海王星",
        'V' => "亚当斯的海王星",
        'W' => "洛厄尔的",
        'X' => "皮克林的冥王星",
        'Y' => "火神",
        'Z' => "白月",

        'pFortune' => '福点',

        'xs136199' => '阋神星',
        'xs136472' => '中的神祗',
        'xs136108' => 'Haumea',

        'xs433' => '爱神',
        'xs16' => '灵神星',


    );
    public static $planetFont = array(
        "Mars" => "Q",
        "Aries" => "A",
        "Venus" => "P",
        "Taurus" => "B",
        "Gemini" => "C",
        "Moon" => "N",
        "Cancer" => "D",
        "Sun" => "M",
        "Leo" => "E",
        "Mercury" => "O",
        "Virgo" => "F",
        "Libra" => "G",
        "Pluto" => "V",
        "Scorpio" => "H",
        "Jupiter" => "R",
        "Sagittarius" => "I",
        "Saturn" => "S",
        "Capricorn" => "J",
        "Uranus" => "T",
        "Aquarius" => "K",
        "Vertex" => "k",
        "Neptune" => "U",
        "Pisces" => "L",
        "MC" => "g",
        "PartOfFortune" => "j",
        "Ascendant" => "f",
        "Chiron" => '\\',
        "Juno" => "`",
        "Vulcanus" => "{",
        "Zeus" => "w",
        "meanApogee" => "Y",
        "Pallas" => "_",
        "Cupido" => "u",
        "Hades" => "v",
        "Kronos" => "x",
        "Apollon" => "y",
        "Admetos" => "z",
        "Poseidon" => "|",
        "Ceres" => "^",
        "MeanNode" => "W",
        "Pholus" => "]",
        "Vulcan" => "火",
        "ARMC" => "g1",
        'equatAsc' => 'l',
        'Eros' => 'Á',
        'trueNode' => 'e',
        'meanNode' => 'W',
        'oscApogee' => 'Z',
        'Sun-Moon' => 'm',
        'Des' => 'h',
        'IC' => 'i',
        'MeanSouthNode' => 'd',
        'Psyche' => 'Â',
        "Vesta" => "a"
    );
    public static $arrDTSDates = array(
        array('1935-05-01', '1935-09-30'),
        array('1936-05-01', '1936-09-30'),
        array('1937-05-01', '1937-09-30'),
        array('1938-05-01', '1938-09-30'),
        array('1939-05-01', '1939-09-30'),
        array('1940-05-01', '1940-09-30'),
        array('1941-05-01', '1941-09-30'),
        array('1942-05-01', '1942-09-30'),
        array('1943-05-01', '1943-09-30'),
        array('1944-05-01', '1944-09-30'),
        array('1945-05-01', '1945-09-30'),
        array('1946-05-01', '1946-09-30'),
        array('1947-05-01', '1947-09-30'),
        array('1948-05-01', '1948-09-30'),
        array('1949-05-01', '1949-09-30'),
        array('1950-05-01', '1950-09-30'),
        array('1951-05-01', '1951-09-30'),
        array('1952-03-01', '1952-10-31'),
        array('1953-04-01', '1953-10-31'),
        array('1954-04-01', '1954-10-31'),
        array('1955-05-01', '1955-09-30'),
        array('1956-05-01', '1956-09-30'),
        array('1957-04-01', '1957-09-30'),
        array('1958-04-01', '1958-09-30'),
        array('1959-04-01', '1959-09-30'),
        array('1960-06-01', '1960-09-30'),
        array('1961-06-01', '1961-09-30'),
        array('1974-04-01', '1974-10-31'),
        array('1975-04-01', '1975-10-31'),
        array('1979-07-01', '1979-09-30'),
        array('1986-04-13', '1986-09-14'),
        array('1987-04-12', '1987-09-13'),
        array('1988-04-10', '1988-09-11'),
        array('1989-04-16', '1989-09-17'),
        array('1990-04-15', '1990-09-16'),
        array('1991-04-14', '1991-09-15'),
    );
    public static $signsFont = array("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L");

    public static $signsEnglish = array('Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo', 'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces');

    public static $signs_guardian_index = ["Mars", "Venus", "Mercury", "Moon", "Sun", "Mercury", "Venus", "Pluto", "Jupiter", "Saturn", ["Saturn", "Uranus"], ["Jupiter", "Neptune"]];

    public static $house_life = ["命宫", "财帛宫", "兄弟宫", "田宅宫", "子女宫", "奴仆宫", "夫妻宫", "疾厄宫", "迁移宫", "官禄宫", "福德宫", "玄秘宫"];

    public static $signs_phase = ["火相、本位", "土相、固定", "风相、变动", "水相、本位", "火相、固定", "土相、变动", "风相、本位", "水相、固定", "火相、变动", "土相、本位", "风相、固定", "水相、变动"];

    public static $signsChinese = array("白羊", "金牛", "双子", "巨蟹", "狮子", "处女", "天秤", "天蝎", "射手", "摩羯", "水瓶", "双鱼");

    public static $houseChinese = array("一宫", "二宫", "三宫", "四宫", "五宫", "六宫", "七宫", "八宫", "九宫", "十宫", "十一宫", "十二宫");

}