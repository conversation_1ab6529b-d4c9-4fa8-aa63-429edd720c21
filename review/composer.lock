{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "7a4fa22414f938da4f9da13d19a85b47", "packages": [{"name": "aliyuncs/oss-sdk-php", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "e69f57916678458642ac9d2fd341ae78a56996c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/e69f57916678458642ac9d2fd341ae78a56996c8", "reference": "e69f57916678458642ac9d2fd341ae78a56996c8", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "~1.0"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "time": "2018-01-08T06:59:35+00:00"}, {"name": "firebase/php-jwt", "version": "v5.0.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "9984a4d3a32ae7673d6971ea00bae9d0a1abba0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/9984a4d3a32ae7673d6971ea00bae9d0a1abba0e", "reference": "9984a4d3a32ae7673d6971ea00bae9d0a1abba0e", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": " 4.8.35"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "time": "2017-06-27T22:17:23+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "shasum": ""}, "require": {"ext-xml": "*", "ext-xmlwriter": "*", "php": ">=5.2.0"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "http://phpexcel.codeplex.com", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "abandoned": "phpoffice/phpspreadsheet", "time": "2015-05-01T07:00:55+00:00"}, {"name": "qiniu/php-sdk", "version": "v7.1.3", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "b91653485e36b4797d7a302cc86c49695e47a642"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/b91653485e36b4797d7a302cc86c49695e47a642", "reference": "b91653485e36b4797d7a302cc86c49695e47a642", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "autoload": {"psr-4": {"Qiniu\\": "src/<PERSON>iu"}, "files": ["src/Qiniu/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "time": "2016-11-18T02:57:31+00:00"}, {"name": "topthink/framework", "version": "v5.0.7", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "bcf64f19f4fadbff6c8503891689ff7e6f2fe9a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/bcf64f19f4fadbff6c8503891689ff7e6f2fe9a3", "reference": "bcf64f19f4fadbff6c8503891689ff7e6f2fe9a3", "shasum": ""}, "require": {"php": ">=5.4.0", "topthink/think-installer": "~1.0"}, "require-dev": {"johnkary/phpunit-speedtrap": "^1.0", "mikey179/vfsstream": "~1.6", "phpdocumentor/reflection-docblock": "^2.0", "phploc/phploc": "2.*", "phpunit/phpunit": "4.8.*", "sebastian/phpcpd": "2.*"}, "type": "think-framework", "autoload": {"psr-4": {"think\\": "library/think"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the new thinkphp framework", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "time": "2017-02-25T02:51:57+00:00"}, {"name": "topthink/think-captcha", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "0c55455df26a1626a60d0dc35d2d89002b741d44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/0c55455df26a1626a60d0dc35d2d89002b741d44", "reference": "0c55455df26a1626a60d0dc35d2d89002b741d44", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"think\\captcha\\": "src/"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp5", "time": "2016-07-06T01:47:11+00:00"}, {"name": "topthink/think-image", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/top-think/think-image.git", "reference": "8586cf47f117481c6d415b20f7dedf62e79d5512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-image/zipball/8586cf47f117481c6d415b20f7dedf62e79d5512", "reference": "8586cf47f117481c6d415b20f7dedf62e79d5512", "shasum": ""}, "require": {"ext-gd": "*"}, "require-dev": {"phpunit/phpunit": "4.8.*", "topthink/framework": "^5.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Image Package", "time": "2016-09-29T06:05:43+00:00"}, {"name": "topthink/think-installer", "version": "v1.0.11", "source": {"type": "git", "url": "https://github.com/top-think/think-installer.git", "reference": "4c6e1ebecd1afce3f4ccc47e147d61bbe1bf641d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-installer/zipball/4c6e1ebecd1afce3f4ccc47e147d61bbe1bf641d", "reference": "4c6e1ebecd1afce3f4ccc47e147d61bbe1bf641d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0"}, "require-dev": {"composer/composer": "1.0.*@dev"}, "type": "composer-plugin", "extra": {"class": "think\\composer\\Plugin"}, "autoload": {"psr-4": {"think\\composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": "2016-12-01T09:08:45+00:00"}, {"name": "topthink/think-queue", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/top-think/think-queue.git", "reference": "5eaa66aa73741f544aa3e010cd787be55b20320c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-queue/zipball/5eaa66aa73741f544aa3e010cd787be55b20320c", "reference": "5eaa66aa73741f544aa3e010cd787be55b20320c", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"think\\queue\\": "src"}, "files": ["src/config.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Queue Package", "time": "2016-08-09T04:20:35+00:00"}, {"name": "topthink/think-worker", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/top-think/think-worker.git", "reference": "f48ca8e5d8324aace15cdbf0f3de751397ef803f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-worker/zipball/f48ca8e5d8324aace15cdbf0f3de751397ef803f", "reference": "f48ca8e5d8324aace15cdbf0f3de751397ef803f", "shasum": ""}, "require": {"workerman/workerman": "^3.3.0"}, "type": "library", "autoload": {"psr-4": {"think\\worker\\": "src"}, "files": []}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "workerman extend for thinkphp5", "time": "2016-10-04T04:54:34+00:00"}, {"name": "workerman/workerman", "version": "v3.5.22", "source": {"type": "git", "url": "https://github.com/walkor/Workerman.git", "reference": "488f108f9e446f31bac4d689bb9f9fe3705862cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/Workerman/zipball/488f108f9e446f31bac4d689bb9f9fe3705862cf", "reference": "488f108f9e446f31bac4d689bb9f9fe3705862cf", "shasum": ""}, "require": {"php": ">=5.3"}, "suggest": {"ext-event": "For better performance. "}, "type": "library", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "time": "2019-09-06T03:42:47+00:00"}, {"name": "workerman/workerman-for-win", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/walkor/workerman-for-win.git", "reference": "cbaae3193e4567fd9cfc8099931c63d9b12174ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/workerman-for-win/zipball/cbaae3193e4567fd9cfc8099931c63d9b12174ee", "reference": "cbaae3193e4567fd9cfc8099931c63d9b12174ee", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "project", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "time": "2017-08-28T10:05:00+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.4.0"}, "platform-dev": []}