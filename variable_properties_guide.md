# Jyotish Draw 变量属性功能指南

## 🎯 功能概述

现在 Jyotish Draw 支持变量属性功能！您可以通过 `setOptions()` 方法设置任意自定义变量，这些变量会自动存储在 `$setOptions` 数组中，方便后续使用。

## 🔧 新增功能

### 1. 变量存储
- ✅ 在 `Image` 和 `Svg` 类中添加了 `protected $setOptions = array();` 属性
- ✅ 所有通过 `setOptions()` 设置的选项都会自动存储
- ✅ 支持标准选项和自定义变量的混合使用

### 2. 新增方法
- `getStoredOptions()` - 获取所有存储的选项
- `getStoredOption($name, $default)` - 获取特定选项，支持默认值

## 📖 使用方法

### 基本用法

```php
<?php
use Jyotish\Draw\Draw;

// 创建绘图实例
$draw = new Draw(400, 300, Draw::RENDERER_IMAGE);

// 设置选项（包括自定义变量）
$draw->setOptions([
    // 标准选项
    'fontColor' => '#FF0000',
    'strokeColor' => '#0000FF',
    'strokeWidth' => 2,
    
    // 自定义变量
    'projectName' => 'My Project',
    'version' => '1.0',
    'author' => 'Your Name',
    'theme' => 'blue'
]);

// 获取存储的变量
$projectName = $draw->Renderer->getStoredOption('projectName');
$version = $draw->Renderer->getStoredOption('version', '0.0');

// 使用变量
$draw->drawText($projectName . ' v' . $version, 200, 50, ['textAlign' => 'center']);
?>
```

### 分批设置选项

```php
<?php
// 第一批设置
$draw->setOptions([
    'fontColor' => '#000000',
    'userType' => 'admin',
    'language' => 'zh-CN'
]);

// 第二批设置（会累积到第一批）
$draw->setOptions([
    'strokeColor' => '#FF0000',
    'debugMode' => true,
    'theme' => 'dark'
]);

// 获取所有存储的选项
$allOptions = $draw->Renderer->getStoredOptions();
print_r($allOptions);
// 输出包含所有6个选项的数组
?>
```

### 条件逻辑使用

```php
<?php
// 设置配置变量
$draw->setOptions([
    'theme' => 'blue',
    'showDebug' => true,
    'userLevel' => 'advanced'
]);

// 根据主题设置颜色
$theme = $draw->Renderer->getStoredOption('theme', 'default');
$color = '#000000'; // 默认黑色

switch ($theme) {
    case 'blue':
        $color = '#0066CC';
        break;
    case 'red':
        $color = '#CC0000';
        break;
    case 'green':
        $color = '#00CC00';
        break;
}

// 使用主题色绘制
$draw->drawText('主题演示', 200, 50, ['fontColor' => $color]);

// 条件显示调试信息
if ($draw->Renderer->getStoredOption('showDebug', false)) {
    $draw->drawText('调试模式开启', 50, 280, ['fontColor' => '#FF8800']);
}
?>
```

## 🎨 实际应用场景

### 1. 项目配置管理

```php
$draw->setOptions([
    'projectName' => 'Website Analytics',
    'version' => '2.1.0',
    'buildDate' => '2024-01-15',
    'environment' => 'production'
]);

$title = $draw->Renderer->getStoredOption('projectName') . 
         ' v' . $draw->Renderer->getStoredOption('version');
$draw->drawText($title, 200, 30, ['textAlign' => 'center']);
```

### 2. 用户个性化设置

```php
$draw->setOptions([
    'userName' => 'John Doe',
    'preferredColor' => '#3366CC',
    'fontSize' => 14,
    'showGrid' => true
]);

$userName = $draw->Renderer->getStoredOption('userName', 'Guest');
$userColor = $draw->Renderer->getStoredOption('preferredColor', '#000000');

$draw->drawText("欢迎, " . $userName, 200, 50, [
    'fontColor' => $userColor,
    'textAlign' => 'center'
]);
```

### 3. 动态内容生成

```php
$draw->setOptions([
    'dataSource' => 'database',
    'chartType' => 'bar',
    'showLegend' => true,
    'animationSpeed' => 'fast'
]);

$chartType = $draw->Renderer->getStoredOption('chartType', 'line');
$showLegend = $draw->Renderer->getStoredOption('showLegend', false);

// 根据配置绘制不同类型的图表
if ($chartType === 'bar') {
    // 绘制柱状图
} elseif ($chartType === 'pie') {
    // 绘制饼图
}
```

## 🔍 调试和检查

### 查看所有存储的选项

```php
$allOptions = $draw->Renderer->getStoredOptions();
echo "<pre>";
print_r($allOptions);
echo "</pre>";
```

### 安全获取选项（带默认值）

```php
// 如果选项不存在，返回默认值
$safeValue = $draw->Renderer->getStoredOption('nonexistent', 'default_value');
$fontSize = $draw->Renderer->getStoredOption('fontSize', 12);
$showBorder = $draw->Renderer->getStoredOption('showBorder', true);
```

## ✅ 优势

1. **灵活性** - 可以存储任意类型的变量
2. **持久性** - 变量在整个绘图过程中保持可用
3. **累积性** - 多次调用 `setOptions()` 会累积存储
4. **安全性** - 支持默认值，避免未定义变量错误
5. **兼容性** - 与现有的标准选项完全兼容

## 🚀 测试文件

- `test_variable_properties.php` - 功能测试和演示
- `test_variable_image.php` - 图像输出测试

现在您可以充分利用这个强大的变量属性系统来创建更加灵活和动态的绘图应用！
