<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\astrology\controller;

use app\common\controller\ControllerBase;

/**
 * 接口基类控制器
 */
class ApiBase extends ControllerBase
{

    /**
     * 基类初始化
     */
    public function __construct()
    {
        
        parent::__construct();


        $this->param = $this->logicApiBase->checkParam($this->param);

        debug(MODULE_NAME.'_begin');

    }
    
    /**
     * API返回数据
     */
    public function apiReturn($code_data = [], $return_data = [], $return_type = 'json')
    {
        
        $result = $this->logicApiBase->apiReturn($code_data, $return_data, $return_type);

        debug(MODULE_NAME.'_end');

        return $result;
    }

    /**
     * API返回数据
     */
    //单盘计算生成

    public function muchTimeP()
    {

        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude']=='' or $this->param['latitude']=='') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }

        if (empty($this->param['tz'])) {
            return ['code' => 1060002, 'msg' => '时区不能为空'];
        }
    }
}
