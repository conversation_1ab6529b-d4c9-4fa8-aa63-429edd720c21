<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\astrology\controller;

/**
 * 客观能量控制器
 */
class Objectiveenergy extends UserBase
{

    /**
     * 客观能量列表
     */
    public function objectiveEnergyList()
    {

        $where = $this->logicObjectiveEnergy->getWhere($this->param_data);

        $data=$this->logicObjectiveEnergy->getObjectiveEnergyList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 客观能量无分页列表
     */
    public function objectiveEnergyColumn()
    {

        $where = $this->logicObjectiveEnergy->getWhere($this->param_data);

        $start_time = strtotime(date('Y-m-d', time()));


        if(!empty($this->param_data['start_time'])){
            $start_time=$this->param_data['start_time'];

        }
        $days=1;
        if(!empty($this->param_data['days'])){

            $days=(int)$this->param_data['days'];

            $end_time=$start_time+86400*$days;

        }else{
            $end_time = $start_time+86400;
        }
        $where['create_time']=['between',[$start_time,$end_time-1]];


        $data=$this->logicObjectiveEnergy->getObjectiveEnergyColumn($where,'*','create_time');


        $infoData = $this->logicWeiUser->getWeiUserInfo(['id' => $where['user_id']],'planet_json');

        if(empty($infoData['planet_json']) || ($infoData['planet_json']==' ')){
            return $this->apiReturn([API_CODE_NAME => 1090008, API_MSG_NAME => '请先填写个人生辰']);
        }

        $plant[0]='sun';
        $plant[1]='moon';
        $plant[2]='mercury';
        $plant[3]='venus';
        $plant[4]='mars';
        $plant[5]='jupiter';
        $plant[6]='saturn';

        for($i=0;$i<$days;$i++){
            $start_time_p=$start_time+86400*$i;
            $today_p_p=($start_time_p+43200);

            if(empty($data[$start_time_p])){
                $fsd_data=array();
                $tods_data=$this->logicObjectiveEnergy->thisScore($infoData['planet_json'],$today_p_p);
                $fsd_data['create_time']=$start_time_p;
                $fsd_data['user_id']=$where['user_id'];
                foreach ($tods_data as $key=>$datum){
                    $fsd_data[$plant[$key]]=$datum;
                }
                $data[$start_time_p]=$fsd_data;
                $lishi[]=$fsd_data;
            }
        }
        if(!empty($lishi)){
            $this->logicObjectiveEnergy->setObjectiveEnergyList($lishi);
        }
		return $this->apiReturn($data);
    }
    /**
     * 客观能量添加
     */
    public function objectiveEnergyAdd()
    {
	  
	   $regit=$this->logicObjectiveEnergy->objectiveEnergyEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 客观能量删除
     */
    public function objectiveEnergyDel()
    {

       $regit=$this->logicObjectiveEnergy->objectiveEnergyDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
