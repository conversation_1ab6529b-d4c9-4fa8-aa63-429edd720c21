<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\astrology\controller;

use think\Cache;

/**
 * 界面点击统计控制器
 */
class Sign extends ApiBase
{

    private $SweTest;
    private $planetNameObject;


    //太阳推运盘计算
    public function sign()
    {
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys'] = 'D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = 8;
        if(!empty($param['tz'])){
            $time_zone = $param['tz'];
        }

        $birth_points = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '01234Hm',
            'house' => $birth_points . ',P',
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $exSweTest = get_sington_object('SweTest', "astrology\\SweTestEmpty");
        $life_data = $exSweTest->calculate($life_arr, ['0', '1', '2', '3', '4', 'H', 'm', 'virtual' => ['10']]);


        $head = array();
        $sign_cur = array();
        $house_cur = array();
        $paper = '';

        $sign_phase = $life_data['sign_phase'];
        $attribute_chinese = ['变动' => 'change', '固定' => 'fixed', '本位' => 'standard', '土相' => 'soil', '水相' => 'water', '火相' => 'fire', '风相' => 'wind'];

        $attribute_corpus['change'] = '多变，不愿意受到束缚，适应力超强';
        $attribute_corpus['fixed'] = '忠诚、有恒心、可靠。';
        $attribute_corpus['standard'] = '意志力强，不受外界影响，随时准备迎接挑战';
        $attribute_corpus['soil'] = '脚踏实地的现实主义者，可靠、勤奋和务实';
        $attribute_corpus['water'] = '多情，敏感，洞察力强，追求深刻意义';
        $attribute_corpus['fire'] = '精神高尚、热情和直接的诚实';
        $attribute_corpus['wind'] = '超然、客观公正，喜欢团队合作，终生学习者';

        $hsore['east'] = '天生领导，自我激励，冒险者，愿意迎接外界挑战';
        $hsore['west'] = '喜欢团队合作，机会主义者，能充分利用机会';
        $hsore['south'] = '雄心勃勃，事业心强，希望获得名望和认可';
        $hsore['north'] = '主观，重视个人成长，喜欢一个人搞定事情';
        $house_nameList = array("一宫", "二宫", "三宫", "四宫", "五宫", "六宫", "七宫", "八宫", "九宫", "十宫", "十一宫", "十二宫");

        //"太阳","月亮","水星","金星","火星","北交","上升","婚神"

        $corpusConstellationListWhere['type']=['in',[1,2,3,4,5,6]];
        $corpusConstellationListWhere['chartType']=1;
        $data_plant = $this->logicCorpusConstellation->getCorpusConstellationList($corpusConstellationListWhere, '*', '', false);


        $zuiliangxing='';
        foreach ($life_data['planet'] as $key => $valoe) {

            foreach ($life_data['house'] as $keyh => $valueh) {

                if ($keyh < 11) {
                    $last_house = $life_data['house'][$keyh + 1];
                } else {

                    $last_house = $life_data['house'][0];
                }

                if ($valueh['longitude'] > $last_house['longitude']) {

                    if ($valoe['longitude'] < $last_house['longitude']) {
                        $valoe['longitude'] += 360;
                    }

                    $last_house['longitude'] += 360;
                }

                if ($valoe['longitude'] >= $valueh['longitude'] and $valoe['longitude'] < $last_house['longitude']) {
                    $valoe['house_id'] = $keyh + 1;
                }
            }

            $attribute[$attribute_chinese[$sign_phase[$valoe['sign']['sign_id']][0]]][] = $valoe;
            $attribute[$attribute_chinese[$sign_phase[$valoe['sign']['sign_id']][1]]][] = $valoe;

            // $data = $this->logicCorpusConstellation->getCorpusConstellationInfo(['oneself' => $valoe['planet_chinese'], 'type' => 1, 'chartType' => 1, 'degree' => -1], 'oneself,other,keywords,content');

            //'other' => $valoe['sign']['sign_chinese'],

            if (empty($sign_cur[$valoe['code_name']])) {
                $sign_cur[$valoe['code_name']] = array();
            }
            if (empty($head[$valoe['code_name']])) {
                $head[$valoe['code_name']] = array();
            }
            if (empty($head[$valoe['code_name']])) {
                $head[$valoe['code_name']] = array();
            }
            if (empty($house_cur[$valoe['code_name']])) {
                $house_cur[$valoe['code_name']] = array();
            }

            foreach ($data_plant as $kysf => $vsd) {

                if ($vsd['oneself'] == $valoe['planet_chinese']) {

                    $title_key=$vsd['chartType'].$vsd['type'].$vsd['oneself'].$vsd['other'].$vsd['degree'];


                    if (!empty($vsd['keywords']) and $vsd['type'] == 5 and $valoe["sign"]["sign_chinese"] == $vsd['other']) {
                        $keywords = explode("，", $vsd['keywords']);
                        $keywords_key = rand(0, (count($keywords) - 1));
                        $vsd['keywords'] = $keywords[$keywords_key];

                        //$title_key=$valoe['chartType'].$valoe['type'].$valoe['oneself'].$valoe['other'].$valoe['degree'];

                        $head[$valoe['code_name']] = ['planet_id' => $valoe['code_name'], 'planet' => $valoe['planet_chinese'], 'sign_id' => $valoe["sign"]["sign_id"], 'sign' => $vsd['other'], 'keywords' => $vsd['keywords'], 'title_key' => $title_key];
                        $sign_cur[$valoe['code_name']]['planet_id'] = $valoe['code_name'];
                        $sign_cur[$valoe['code_name']]['planet'] = $valoe['planet_chinese'];
                        $sign_cur[$valoe['code_name']]['sign_id'] = $valoe["sign"]["sign_id"];
                        $sign_cur[$valoe['code_name']]['sign'] = $vsd['other'];
                        $sign_cur[$valoe['code_name']]['content'] = $vsd['content'];
                        $sign_cur[$valoe['code_name']]['title_key'] = $title_key;
                    }

                    if ($vsd['type'] == 1) {
                        $sign_cur[$valoe['code_name']]["describe"] = html_entity_decode($vsd['content']);
                        $sign_cur[$valoe['code_name']]["title_key_t"] = $title_key;
                    }
                    //行星落宫位
                    if ($vsd['type'] == 4 and $vsd['other'] == $valoe["house_id"]) {

                        $house_cur[$valoe['code_name']]['planet_id'] = $valoe['code_name'];
                        $house_cur[$valoe['code_name']]['planet'] = $valoe['planet_chinese'];
                        $house_cur[$valoe['code_name']]['house_id'] = $valoe["house_id"];
                        $house_cur[$valoe['code_name']]['house'] = $vsd['other'].'宫';
                        $house_cur[$valoe['code_name']]['content'] = $vsd['content'];
                        $house_cur[$valoe['code_name']]['title_key'] = $title_key;
                       // $house_cur[$valoe['code_name']]['describe'] = '第' . $vsd['other'].'宫';
                    }
                }

                if ($vsd['type'] == 3   and $vsd['oneself'] == $valoe["house_id"]) {

                    if(!empty($house_cur[$valoe['code_name']])){
                        $house_cur[$valoe['code_name']]["describe"] = '第' . $vsd['oneself'].'宫：'.html_entity_decode($vsd['content']);
                    }
                }
            }
            if(empty($zuiliangxing) and in_array($valoe['code_name'].$valoe['sign']['sign_chinese'],['1金牛','3金牛','2双子','1巨蟹','2处女','3天秤','4天蝎','4摩羯','3双鱼'])){

                $diji=ceil($valoe['sign']['deg']/ 6);
                if($diji==0 or $diji==6){
                    $diji=1;
                }
                $SignNamesSimple = config('ext_astrology')['PlanetNamesSimple'];
                $zuiliangxing=$SignNamesSimple[$valoe['code_name']].$valoe['sign']['sign_chinese'].$diji;
            }
        }
        $xingluogong['title']='日'.$life_data['planet'][0]['sign']['sign_chinese'].'月'.$life_data['planet'][1]['sign']['sign_chinese'];
        $xingluogong['attribution']="bujuan";
        $xingluogong['attribution_str']="星座一句话";
        $CorpusUserInfo_yiju = $this->logicCorpusUser->getCorpusUserInfo($xingluogong);

        $paper = $CorpusUserInfo_yiju['content1'];

        $taiyang_sign=$life_data['planet'][0]['sign']['sign_id'];
        $taiyang_sign+=6;
        $yuelia_sign=$life_data['planet'][1]['sign']['sign_id'];
        $yuelia_sign+=6;
        if($taiyang_sign>=12){$taiyang_sign=$taiyang_sign-12;}
        if($yuelia_sign>=12){$yuelia_sign=$yuelia_sign-12;}

        if($taiyang_sign==$yuelia_sign){
            $sign_code['compatibility_sign'] = $life_data["signChinese"][$taiyang_sign].'座';
        }else{
            $sign_code['compatibility_sign'] = $life_data["signChinese"][$taiyang_sign].'座、'.$life_data["signChinese"][$yuelia_sign].'座';

        }
        $yushu=date('H', $life_birth_time)% 3;
        $lastDigit = date('d', $life_birth_time) % 10; // 获取个位数

        $cor_user_where['title']=$life_data["signChinese"][$life_data['planet'][$yushu]['sign']['sign_id']].($lastDigit+1);
        $cor_user_where['attribution']='bujuan';
        $cor_user_where['attribution_str']='九宫格星座密码';

        $corpusUserInfo = $this->logicCorpusUser->getCorpusUserInfo($cor_user_where);

        $sign_code['surface'] = $corpusUserInfo['content1'];
        $sign_code['actual'] = $corpusUserInfo['content2'];
        $sign_code['hidden_skills'] = $corpusUserInfo['content3'];
        $sign_code['title_key'] = $corpusUserInfo['title'].$corpusUserInfo['attribution'].$corpusUserInfo['attribution_str'];

        $date_reg['paper'] = $paper;


        if(empty($zuiliangxing)){

            $diji=ceil($life_data['planet'][0]['sign']['deg']/ 6);
            if($diji==0 or $diji==6){
                $diji=1;
            }
            $zuiliangxing=$life_data['planet'][0]['sign']['sign_chinese'].$diji;
        }

        $cor_user_where['title']=$zuiliangxing;
        $cor_user_where['attribution']='bujuan';
        $cor_user_where['attribution_str']='星座关键词';
        $corpusUserInfoziwei = $this->logicCorpusUser->getCorpusUserInfo($cor_user_where);

        $date_reg['supreme_planet'] = $corpusUserInfoziwei['content1'];
        $date_reg['supreme_planet_describe'] = $corpusUserInfoziwei['content2'];

        $date_reg['love_describe'] = '越爱越舒服';
        $date_reg['career_describe'] = '得天独厚';
        $date_reg['wealth_describe'] = '努力就暴富';
        $date_reg['people_describe'] = '越爱越舒服';

        $date_reg['sign_code'] = $sign_code;


        $head = array_filter($head);
        $house_cur = array_filter($house_cur);
        $sign_cur = array_filter($sign_cur);


        $date_reg['head'] = array_values($head);
        $date_reg['sign'] = array_values($sign_cur);
        $date_reg['house'] = array_values($house_cur);

        return $this->apiReturn($date_reg);
    }

    //星历表
    public function ephemeris()
    {
        $param = $this->param;

        $planets = '0123456789';
        $longitude = '121.47,31';
        $h_sys = 'K';
        $ay = 'K';
        $tz = 8;
        $param['time'] = 12;

        !empty($param['ay']) && $ay = $param['ay'];

        !empty($param['planets']) && $planets = $param['planets'];

        !empty($param['longitude']) && $longitude = $param['longitude'];

        !empty($param['h_sys']) && $h_sys = $param['h_sys'];

        $start_time = $param['start_date'] . ' ' . $param['time'];

        $start_time = date('Y-m-d H:i:s', (strtotime($start_time) - $tz * 3600));

        if (!empty($param['end_date'])) {
            $lastdaytime = strtotime($param['end_date']);

            if ($lastdaytime < $start_time) {

            }

        } else {
            $lastdaytime = strtotime("$start_time +1 month");
        }

        $lastdaytime += 86400;
        $this->SweTest = get_sington_object('SweTest', "astrology\\SweTest");


        $step = (int)(($lastdaytime - strtotime($start_time)) / 86400);

        $arr = [
            'b' => date('d.m.Y', strtotime($start_time)),
            'ut' => date('H:i:s', strtotime($start_time)),
            'p' => $planets,
            'n' => $step,
            'house' => $longitude . ',' . $h_sys,
            's' => '1440m',
            'f' => 'PTls',  //名字 时间 度数经度 速度 宫位
            'g' => ',',
            'head',
            'roundsec',

        ];
        $planetNameObject = get_sington_object('planetName', "astrology\\planetName");

        $planetChinese = $planetNameObject::$planetChinese;
        $planetFont = $planetNameObject::$planetFont;
        $planetEnglish = $planetNameObject::$planetEnglish;

        $planet_json = $this->SweTest->SweTest($arr);

        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));

        $data_array = array();
        foreach ($planet_json as $key => $value) {
            $new_lineInfo = explode(',', $value);

            if (count($new_lineInfo) > 3) {

                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);

                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $date_new = date('d.m.Y', $newMoon_time);
                    if (empty($data_array[strtotime($date_new)])) {
                        $data_array[strtotime($date_new)] = array();
                    }
                    $new_lineInfo_post['planetEnglish'] = str_replace(' ', '', $new_lineInfo[0]);
                    $new_lineInfo_post['code_name'] = (string)array_search($new_lineInfo_post['planetEnglish'], $planetEnglish);
                    $new_lineInfo_post['planetChinese'] = $planetChinese[$new_lineInfo_post['code_name']];
                    $new_lineInfo_post['planetFont'] = $planetFont[$new_lineInfo_post['planetEnglish']];
                    $new_lineInfo_post['sign'] = $this->SweTest->Convert_sign_Longitude(trim($new_lineInfo[2], ' '));
                    $data_array[strtotime($date_new)][$new_lineInfo_post['planetEnglish']] = $new_lineInfo_post;

                }
            }
        }


        return $this->apiReturn($data_array);
    }

    //获取星座所有落座
    public function planerSign($start_time, $end_time, $longitude, $p)
    {

    }

    //月亮落座
    public function moonSign($start_time, $longitude)
    {
        $lastdaytime = strtotime("$start_time +1 month") + 86400;
        $step = (int)(($lastdaytime - strtotime($start_time)) / 86400);
        $arr = [
            'b' => date('d.m.Y', strtotime($start_time)),
            'ut' => "15:30",
            'p' => '1',
            'n' => $step,
            'house' => $longitude,
            's' => '1440m',
            'f' => 'pTls',  //名字 时间 度数经度 速度 宫位
            'g' => ',',
            'head',
            'roundsec',
        ];

        $SweTest = get_sington_object('SweTest', "astrology\\SweTest");
        $planetNameObject = get_sington_object('planetName', "astrology\\planetName");
        $planetChinese = $planetNameObject::$planetChinese;
        $planetFont = $planetNameObject::$planetFont;
        $planetEnglish = $planetNameObject::$planetEnglish;
        $planet_json = $SweTest->SweTest($arr);
        $step = $arr['n'] - 1;
        $planet_count = count(str_split($arr['p']));
        $data_array = array();
        foreach ($planet_json as $key => $value) {
            $new_lineInfo = explode(',', $value);
            if (count($new_lineInfo) > 3) {
                $new_defug = $SweTest->crunch(trim($new_lineInfo[2], ' '));
                $newMoon = str_replace(' UT', '', $new_lineInfo[1]);
                if (!empty($newMoon)) {
                    $newMoon_time = strtotime($newMoon);
                    $date_new = date('d.m.Y', $newMoon_time);
                    if (empty($data_array[strtotime($date_new)])) {
                        $data_array[strtotime($date_new)] = array();
                    }
                    $new_lineInfo_post['code_name'] = $new_lineInfo[0];
                    $new_lineInfo_post['planet_english'] = $new_lineInfo[0];
                    $new_lineInfo_post['planetChinese'] = $planetChinese[$new_lineInfo_post['code_name']];
                    $new_lineInfo_post['planetEnglish'] = $planetEnglish[$new_lineInfo_post['code_name']];
                    $new_lineInfo_post['planetFont'] = $planetFont[$new_lineInfo_post['planetEnglish']];
                    $new_lineInfo_post['sign'] = $SweTest->Convert_sign_Longitude(trim($new_lineInfo[2], ' '));
                    $data_array[strtotime($date_new)][$new_lineInfo[0]] = $new_lineInfo_post;

                }
            }
        }
        return $this->apiReturn($data_array);
    }

    //一月内相位数据
    public function astroCalendar()
    {



        $year = $this->param['year'];
        $month = $this->param['month'];
        $tz=8;
        if(!empty($this->param['tz'])){
            $tz = $this->param['tz'];
        }
        if ($month > 12 or $month < 1) {
            $month = 1;
        }
        if ($year > 3999 or $year < -100) {
            $year = 1;
        }
        return $this->apiReturn($this->logicAstrocalendar->astroCalendar($year,$month,$tz));

    }

//回归黄道
    public
    function isay($ay_conditions)
    {
        if (isset($ay) && $ay > -1) {
            $ay_conditions['ay'] = $ay;
            $ay_conditions['p'] = '0';
            $ay_conditions['f'] = 'Tl';
//            [
//                'b' => '10.10.2020',
//                'p' => '01',
//                'house' => '121.47,31.23,K',
//                'ut' => '04:14:00',
//                'f' => 'Tl',  //名字 经度 度数经度 速度 宫位 名字id
//                'g' => ',',
//                'ay' => '0',
//                'head',
//                'roundsec'
//            ];
            $ay_planetOut = $this->SweTest($ay_conditions);

            $ay_planetinfo = explode(',', str_replace(' ', '', $ay_planetOut[0]));

            $this->ay_longitude = $this->changeToDu($ay_planetinfo[1]);
        }
    }
}
