<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\astrology\controller;
/**
 * 语料控制器
 */
class Corpusconstellation extends ApiBase
{

    public function getlist()
    {

        if(is_array($this->param['fallInto'])){
            return $this->apiReturn(['code'=>1070001,'msg'=>'fallInto字段-请求格式不对，请看文档']);
        }
        $param = json_decode(html_entity_decode($this->param['fallInto']), true);

        $chartType = $this->param['chartType'];

        $planetNameObject = get_sington_object('planetName', "astrology\\planetName");

        $planetName = $planetNameObject::$planetChinese;
        $fixedChinese = $planetNameObject::$fixedChinese;
        $planetName=array_merge($planetName,$fixedChinese);

        $signChinese = $planetNameObject::$signChinese;
        $houseChinese = $planetNameObject::$houseChinese;

        $chartEnglishType= config('ext_astrology')['chartEnglishType'];
        $allow_degree_cn= config('ext_astrology')['allow_degree_cn'];
        $corpusConstellationList = array();
        $corpusConstellationWhere['chartType'] = (string)array_search($chartType, $chartEnglishType);

        if($corpusConstellationWhere['chartType']<0){
            return $this->apiReturn(['code'=>1050002,'msg'=>'无此盘类型']);
        }
        $corpusConstellationList = array();

        foreach ($param as $key => $value) {
            $fallInto = $value;
            if (empty($fallInto['type'])) {
                continue;
            }
            $and_tr='';

            $corpusConstellationWhere['type'] = $fallInto['type'];
            if ($fallInto['type'] == 1) {
                $corpusConstellationWhere['oneself'] = $planetName[$fallInto['planet_id']];
            } elseif ($fallInto['type'] == 2) {
                $corpusConstellationWhere['oneself'] = $houseChinese[($fallInto['house_id'])];
            } elseif ($fallInto['type'] == 3) {
                $corpusConstellationWhere['oneself'] = $signChinese[$fallInto['sign_id']];
            } elseif ($fallInto['type'] == 4) {
                $corpusConstellationWhere['oneself'] = $planetName[$fallInto['planet_id']];
                $and_tr='落入';
                $corpusConstellationWhere['other'] = $houseChinese[($fallInto['house_id']-1)];
                $planet_desc=$fallInto['planet_id'];
            } elseif ($fallInto['type'] == 5) {
                $corpusConstellationWhere['oneself'] = $planetName[$fallInto['planet_id']];
                $and_tr='落入';
                $corpusConstellationWhere['other'] = $signChinese[$fallInto['sign_id']];
                $planet_desc=$fallInto['planet_id'];
            } elseif ($fallInto['type'] == 6) {
                $corpusConstellationWhere['oneself'] = $planetName[$fallInto['planet_id1']];
                $and_tr=$allow_degree_cn[$fallInto['degree']];
                $corpusConstellationWhere['other'] = $planetName[$fallInto['planet_id2']];
                $corpusConstellationWhere['degree'] = $fallInto['degree'];
                $planet_desc=$fallInto['planet_id1'];

            }elseif ($fallInto['type'] == 7) {
                $corpusConstellationWhere['oneself'] = $houseChinese[($fallInto['house_id']-1)];
                $corpusConstellationWhere['other'] = $signChinese[$fallInto['sign_id']];

            }

           // dump($corpusConstellationWhere);
            $corpusConstellationInfo = $this->logicCorpusConstellation->getCorpusConstellationInfo($corpusConstellationWhere, 'oneself,other,keywords,content');

            if (!empty($corpusConstellationInfo) and !empty($corpusConstellationInfo['content'])) {

                $corpusConstellationModify = $fallInto;

                $corpusConstellationModify['title'] = $corpusConstellationInfo['oneself'].$and_tr . $corpusConstellationInfo['other'];

                $corpusConstellationModify['content'] = html_entity_decode($corpusConstellationInfo['content']);

                if(!empty($planet_desc)){
                    $corpusConstellationDescribeInfo = $this->logicCorpusConstellation->getCorpusConstellationInfo(['chartType' => $corpusConstellationWhere['chartType'], 'type' => 1, 'oneself' => $planetName[$planet_desc]], 'oneself,other,keywords,content');
                    if (!empty($corpusConstellationDescribeInfo) and !empty($corpusConstellationDescribeInfo['content'])) {
                    $corpusConstellationModify['describe'] = html_entity_decode($corpusConstellationDescribeInfo['content']);
                    }
                }

                $corpusConstellationList[] = $corpusConstellationModify;
            }


        }

        return $this->apiReturn($corpusConstellationList);
    }

}
