<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 轮播图逻辑
 */
class Swiper extends LogicBase
{
    
    
    /**
     * 获取轮播图列表
     */
    public function getSwiperList($where = [], $field = '', $order = '' , $paginate = 0)
    {
        return $this->modelSwiper->getList($where, $field, $order, $paginate);
    }
    /**
     * 获取轮播图列表
     */
    public function getSwiperColumn($where = [], $field = '', $key = '')
    {
        return $this->modelSwiper->getColumn($where, $field , $key);
    }
    /**
     * 获取轮播图列表搜索条件
     */
    public function getWhere($data = [])
    {
        
        $where = [];
        
        !empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];
        
        return $where;
    }
    
    /**
     * 轮播图信息编辑
     */
    public function swiperEdit($data = [])
    {
        
        
        $url = url('swiperList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelSwiper->setInfo($data);
        
        $handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '轮播' . $handle_text . '，title：' . $data['title']);
        
        return $result ? [RESULT_SUCCESS, '轮播操作成功', $url] : [RESULT_ERROR, $this->modelArticle->getError()];
    }

    /**
     * 获取轮播图信息
     */
    public function getSwiperInfo($where = [], $field = '*')
    {
        
        return $this->modelSwiper->getInfo($where, $field);
    }
    /**
     * 轮播图删除
     */
    public function swiperDel($where = [])
    {
        
        $result = $this->modelSwiper->deleteInfo($where);
        
        $result && action_log('删除', '轮播删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '轮播删除成功'] : [RESULT_ERROR, $this->modelArticle->getError()];
    }
}
