<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 综合语料逻辑
 */
class CorpusUser extends LogicBase
{

      /**
       * 获取综合语料搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获综合语料单条信息
      */
     public function getCorpusUserInfo($where = [], $field = '*')
     {

        return $this->modelCorpusUser->getInfo($where, $field);
     }

    /**
     * 获取综合语料列表
     */

    public function getCorpusUserList($where = [], $field = '', $order = '', $paginate = 0, $group = null)
    {

        !empty($group) &&  $this->modelCorpusUser->group($group);
        return $this->modelCorpusUser->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取综合语料无分页列表
     */
    public function getCorpusUserColumn($where = [], $field = '', $key = '')
    {
        return $this->modelCorpusUser->getColumn($where, $field , $key);
    }

    /**
     * 综合语料单条编辑
     */
    public function corpusUserEdit($data = [])
    {
		
        $result = $this->modelCorpusUser->setInfo($data);
        
        return $result ? $result : $this->modelCorpusUser->getError();
    }

    /**
     * 综合语料删除
     */
    public function corpusUserDel($where = [], $is_true = false)
    {

        $result = $this->modelCorpusUser->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelCorpusUser->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取综合语料搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['title'] = ['like', '%'.$data['search_data'].'%'];
        !empty($data['attribution_str']) && $where['attribution_str'] = $data['attribution_str'];

        return $where;
    }
	  
	/**
     * 综合语料单条编辑
     */
    public function corpusUserAdminEdit($data = [])
    {


        $url = url('CorpusUserList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelCorpusUser->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '综合语料' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '综合语料操作成功', $url] : [RESULT_ERROR, $this->modelCorpusUser->getError()];
    }

    /**
     * 综合语料删除
     */
    public function corpusUserAdminDel($where = [])
    {

        $result = $this->modelCorpusUser->deleteInfo($where);
        
        $result && action_log('删除', '综合语料删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '综合语料删除成功'] : [RESULT_ERROR, $this->modelCorpusUser->getError()];
    }
}
