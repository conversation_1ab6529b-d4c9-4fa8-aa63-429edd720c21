<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 绑定标签逻辑
 */
class ArticleCategoryAccess extends LogicBase
{

    /**
     * 获取绑定标签搜索条件
     */
    public function getWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 获绑定标签单条信息
     */
    public function getArticleCategoryAccessInfo($where = [], $field = '*')
    {

        return $this->modelArticleCategoryAccess->getInfo($where, $field);
    }

    /**
     * 获取绑定标签列表
     */

    public function getArticleCategoryAccessList($where = [], $field = '', $order = '', $paginate = false)
    {

        $field = 'a.id,a.name,a.category_id,a.describe,a.author,ala.category_id';

        $this->modelArticleCategoryAccess->alias('ala');

        $join = [
            [SYS_DB_PREFIX . 'article a', 'a.id = ala.article_id'],
        ];

        $this->modelArticleCategoryAccess->join = $join;

        return $this->modelArticleCategoryAccess->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取绑定标签列表
     */

    public function getCategoryAccessCategoryList($where = [], $field = '', $order = '', $paginate = false)
    {

        $field = 'ac.*,aca.article_id';

        $this->modelArticleCategoryAccess->alias('aca');

        $join = [
            [SYS_DB_PREFIX . 'article_category ac', 'ac.id = aca.category_id'],
        ];

        $this->modelArticleCategoryAccess->join = $join;

        return $this->modelArticleCategoryAccess->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取绑定标签无分页列表
     */
    public function getArticleCategoryAccessColumn($where = [], $field = '', $key = '')
    {
        return $this->modelArticleCategoryAccess->getColumn($where, $field, $key);
    }

    /**
     * 绑定标签单条编辑
     */
    public function articleCategoryAccessEdit($data = [], $where = [])
    {

        $result = $this->modelArticleCategoryAccess->setInfo($data, $where);

        return $result ? $result : $this->modelArticleCategoryAccess->getError();
    }

    /**
     * 绑定标签单条编辑
     */
    public function articleCategoryAccessSetInc($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {

        $result = $this->modelArticleCategoryAccess->setIncDecInfo($where, $field, $number, $setType);

        return $result ? $result : $this->modelArticleCategoryAccess->getError();
    }

    /**
     * 绑定标签删除
     */
    public function articleCategoryAccessDel($where = [], $is_true = false)
    {

        $result = $this->modelArticleCategoryAccess->deleteInfo($where, $is_true);

        return $result ? $result : $this->modelArticleCategoryAccess->getError();
    }


    //Admin模块操作

    /**
     * 获取绑定标签搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 绑定标签单条编辑
     */
    public function articleCategoryAccessAdminEdit($data = [])
    {


        $url = url('articleCategoryAccessList');

        $data['member_id'] = MEMBER_ID;

        $result = $this->modelArticleCategoryAccess->setInfo($data);

        $handle_text = empty($data['id']) ? '新增' : '编辑';

        $result && action_log($handle_text, '绑定标签' . $handle_text . '，title：' . $data['title']);

        return $result ? [RESULT_SUCCESS, '绑定标签操作成功', $url] : [RESULT_ERROR, $this->modelArticleCategoryAccess->getError()];
    }

    /**
     * 绑定标签删除
     */
    public function articleCategoryAccessAdminDel($where = [])
    {

        $result = $this->modelArticleCategoryAccess->deleteInfo($where);

        $result && action_log('删除', '绑定标签删除，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '绑定标签删除成功'] : [RESULT_ERROR, $this->modelVip->getError()];
    }
}
