<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

use astrology\SweTestEmpty as SweTestEmpty;

use astrology\SweTest as SweTest;

/**
 * 太阳弧盘数据生成
 */
class SolarArc extends LogicBase
{

    public function plateData($param)
    {
        if (!empty($param['phase'])) {
            foreach ($param['phase'] as $key => $value) {
                $allow_degree[$key] = $value;
            }
        }

        if (empty($allow_degree)) {
            $allow_degree['0'] = 5;
            $allow_degree['30'] = 5;
            $allow_degree['45'] = 5;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['180'] = 5;
        }

        $starsCode = $param['planets'];

        $planets = implode('', $starsCode);

        if (!empty($param['virtual'])) {
            $starsCode['virtual'] = $param['virtual'];
        }

        if (!empty($param['planet_xs']) and !empty($param['planet_xs'][0])) {
            $starsCode['planet_xs'] = $param['planet_xs'];
        }
        if (!empty($param['planet_xf']) and !empty($param['planet_xf'][0])) {
            $starsCode['planet_xf'] = $param['planet_xf'];
        }
        $ay=false;
        if(isset($param['ay'])){
            $ay=$param['ay'];
        }
        $birthdayToTime = strtotime($param['birthday']) ;

        $utdatenow = date('d.m.Y', $birthdayToTime- $param['tz'] * 3600);

        $utnow = date('H:i:s', $birthdayToTime- $param['tz'] * 3600);

        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        $arr = [
            'b' => $utdatenow,
            'p' => $planets,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];
        $exSweTestEmpty = get_sington_object('exSweTestEmpty', SweTestEmpty::class);


        $life_sweTest = $exSweTestEmpty->SweTest($arr,$starsCode);


        $transitdateToTime = strtotime($param['transitday']);

        $fff= $transitdateToTime-$birthdayToTime;

        $prog_timedd = $fff / (365.2422*86400);
        $prog_timedd=$birthdayToTime+$prog_timedd*86400;
        $utdatenow = date('d.m.Y', $prog_timedd- $param['tz'] * 3600);

        $utnow = date('H:i:s', $prog_timedd- $param['tz'] * 3600);

        $arr2 = [
            'b' => $utdatenow,
            'p' => '0',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];
        $data1 = $exSweTestEmpty->SweTest($arr2,  [0]);

        //计算太阳弧度
        $life_sum = explode(',', str_replace(' ', '', $life_sweTest[0]));
        $data1_sum = explode(',', str_replace(' ', '', $data1[0]));
        $solar_arc = $exSweTestEmpty->crunch($data1_sum[1] - $life_sum[1]);




        foreach ($life_sweTest as $key=>$value){
            $value_array = explode(',', str_replace(' ', '', $value));
            $value_array[1]=$exSweTestEmpty->crunch($value_array[1] + $solar_arc);
            $data1[$key]=implode(",",$value_array);
        }



        $one_data  = $exSweTestEmpty->calculate($arr, $starsCode, $life_sweTest);

        $solar_planet  = $exSweTestEmpty->calculate($arr, $starsCode, $data1);

        $exSweTest = get_sington_object('exSweTest', SweTest::class);

        $second_data = $solar_planet;

        $planets_data['user'] = $param;

        $planets_data['house'] = $this->logicComparision->housePlanet($exSweTest, $one_data);

        $sign_attribute = $this->logicComparision->signPlanet($one_data);
        $planets_data['sign'] = $sign_attribute['sign'];
        $planet = $this->logicComparision->planetSecondPhase($exSweTest, $one_data, $second_data, $allow_degree);

        $planets_data['planet'] = $planet['planet'];
        $planets_data['planet_second'] = $planet['planet_second'];

        if(!isset($param['format'])){
            $param['format']=1;
        }

        if(!empty($param['svg_type']) and $param['svg_type']==1){
            $planets_data['svg'] = $this->logicComparision->simpleSvg($planets_data,$one_data,$param['format']);
        }else if(!empty($param['svg_type']) and $param['svg_type']==-1){

        }else{
            $planets_data['svg'] = $this->logicComparision->seniorSvg($planets_data,$one_data,$second_data);
        }

        $planets_data['attribute'] = $sign_attribute['attribute'];

        return $planets_data;

    }

}
