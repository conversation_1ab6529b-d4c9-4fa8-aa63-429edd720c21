@font-face {
  font-family: "Flaticon";
  src: url("../fonts/Flaticon.eot");
  src: url("../fonts/Flaticon.eot?#iefix") format("embedded-opentype"),
       url("../fonts/Flaticon.woff2") format("woff2"),
       url("../fonts/Flaticon.woff") format("woff"),
       url("../fonts/Flaticon.ttf") format("truetype"),
       url("../fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../fonts/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
	font-family: Flaticon;
	font-style: normal;
	line-height: 1;
}

.flaticon-strong-body:before { content: "\f100"; }
.flaticon-flexibility:before { content: "\f101"; }
.flaticon-healthy-lifestyle:before { content: "\f102"; }
.flaticon-blood-flow:before { content: "\f103"; }
.flaticon-drops-blood:before { content: "\f104"; }
.flaticon-adrenal-gland:before { content: "\f105"; }
.flaticon-lotus:before { content: "\f106"; }
.flaticon-trainers:before { content: "\f107"; }
.flaticon-trophy:before { content: "\f108"; }
.flaticon-users:before { content: "\f109"; }
.flaticon-classes:before { content: "\f10a"; }