<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\xjzn\controller;

/**
 * 日月年运控制器
 */
class Luck extends ApiBase
{
    //日运
    public function day()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        !isset($this->param['tz']) && $this->param['tz'] = 8;
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys'] = 'D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = $param['tz'];
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789m',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }
        //将时间转换为当前日的中午12点的时间戳
        $date_ime = strtotime(date('Y-m-d', $date_ime) . ' 12:00:00');

        $new_birth_time = $date_ime - $time_zone * 3600 - $is_summer * 3600;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => '12:00:00',
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $data = $this->logicDayLuck->basicData($life_arr, $new_conditions);

        $year = date('Y', $date_ime);
        $month = date('m', $date_ime);
        $day = date('d', $date_ime);
        $luckyDuckDay = $this->yiji($date_ime, $life_birth_time);

        $data['lucky_number'] = $luckyDuckDay['lucky_number'];
        $data['lucky_flower'] = $luckyDuckDay['lucky_flower'];
        $data['lucky_color'] = $luckyDuckDay['lucky_color'];
        $data['lucky_stone'] = $luckyDuckDay['lucky_stone'];
        $data['lucky_foods'] = $luckyDuckDay['lucky_foods'];

        return $this->apiReturn($data);
    }

    //周运
    public function weeks()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys'] = 'D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = 8;
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789m',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }
// 获取当前日期
        $currentDate = date('Y-m-d', $date_ime);
// 计算本周三的日期
        $wednesdayDate = date('Y-m-d', strtotime("$currentDate +3 weekdays -" . date('w', $date_ime) . " days"));
// 拼接成本周三12点的日期时间字符串
        $wednesday12pmDateTime = "$wednesdayDate 12:00:00";
// 转换为时间戳
        $date_ime = strtotime($wednesday12pmDateTime);


        $new_birth_time = $date_ime - $time_zone * 3600 - $is_summer * 3600;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points. ',' . $param['h_sys'] ,
            'ut' => '12:00:00',
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $data = $this->logicWeekLuck->basicData($life_arr, $new_conditions);

        $year = date('Y', $date_ime);
        $month = date('m', $date_ime);
        $day = date('d', $date_ime);
        $luckyDuckDay = $this->yiji($date_ime, $life_birth_time);

        $data['lucky_number'] = $luckyDuckDay['lucky_number'];
        $data['lucky_flower'] = $luckyDuckDay['lucky_flower'];
        $data['lucky_color'] = $luckyDuckDay['lucky_color'];
        $data['lucky_stone'] = $luckyDuckDay['lucky_stone'];
        $data['lucky_foods'] = $luckyDuckDay['lucky_foods'];


        return $this->apiReturn($data);
    }

    //月运
    public function moon()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys'] = 'D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = 8;
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789m',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }

        $date_ime = strtotime(date('Y-m-d', $date_ime) . ' 12:00:00');

        $chazhit = $date_ime - $time_zone * 3600 - $is_summer * 3600-$life_birth_time;
        $prog_timedd = $chazhit / (27.321582 * 86400);
        $new_birth_time=$life_birth_time+$prog_timedd*86400;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => date('H:i:s', $new_birth_time),
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $data = $this->logicMoonLuck->basicData($life_arr, $new_conditions);

//        $data['corpus'][] = $this->logicLunarreTurn->explainData(['birthday'=>$birthday,
//            'birth_points'=>$birth_points,
//            'is_summer'=>$is_summer,
//            'time_zone'=>$time_zone,
//            'current_date'=> date('Y-m-d H:i:s', $date_ime),
//            'h_sys'=>'P']);


        $year = date('Y', $date_ime);
        $month = date('m', $date_ime);
        $day = date('d', $date_ime);
        $luckyDuckDay = $this->yiji($date_ime, $life_birth_time,);

        $data['lucky_number'] = $luckyDuckDay['lucky_number'];
        $data['lucky_flower'] = $luckyDuckDay['lucky_flower'];
        $data['lucky_color'] = $luckyDuckDay['lucky_color'];
        $data['lucky_stone'] = $luckyDuckDay['lucky_stone'];
        $data['lucky_foods'] = $luckyDuckDay['lucky_foods'];
        // $data['describe'] = '今日很多事在忙，你的社会关系能够让你看到自己的情绪是如何影响他人的，这会让你陷入感官冲突。这也会造成你无法正确表达自己的情感。';

        return $this->apiReturn($data);
    }

    //年运
    public function year()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys'] = 'D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = 8;
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789m',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }
        $chazhit = $date_ime - $time_zone * 3600 - $is_summer * 3600-$life_birth_time;
        $prog_timedd = $chazhit / (365.2422 * 86400);
        $new_birth_time=$life_birth_time+$prog_timedd*86400;



        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => date('H:i:s', $new_birth_time),
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $data=$this->logicYearLuck->basicData($life_arr,$new_conditions,$new_birth_time);
        $data['corpus'][] = $this->logicSolarreTurn->explainData(['birthday'=>$birthday,
            'birth_points'=>$birth_points,
            'is_summer'=>$is_summer,
            'time_zone'=>$time_zone,
            'current_date'=>date('Y-m-d H:i:s', $date_ime),
            'h_sys'=>'P']);


        $year = date('Y', $date_ime);
        $month = date('m', $date_ime);
        $day = date('d', $date_ime);
        $luckyDuckDay = $this->logicCorpusConstellation->yiji($date_ime, $life_birth_time);

        $data['lucky_number'] = $luckyDuckDay['lucky_number'];
        $data['lucky_flower'] = $luckyDuckDay['lucky_flower'];
        $data['lucky_color'] = $luckyDuckDay['lucky_color'];
        $data['lucky_stone'] = $luckyDuckDay['lucky_stone'];
        //$data['describe'] = '今日很多事在忙，你的社会关系能够让你看到自己的情绪是如何影响他人的，这会让你陷入感官冲突。这也会造成你无法正确表达自己的情感。';

        return $this->apiReturn($data);
    }

    //查找行星落入宫位
    public function getHouseId($house, $longitude)
    {

        foreach ($house as $key => $value) {
            if ($key < 11) {
                $last_house = $house[$key + 1];
            } else {
                $last_house = $house[0];
            }
            if ($longitude >= $value['longitude'] and $longitude < $last_house['longitude']) {
                return $key + 1;
            }
        }
    }


    public function yiji($date_ime,$life_birth_time,$house_id=0)
    {

        $data['lucky_number'] = 6;

        $lucky_day=date('dmY', $date_ime).date('dmY', $life_birth_time).$house_id;

        $suijinri=[0,3,1,4,1,5,9,2,6,5,3,5,9,7,1,8,3,0,2,7,5,1,9,3,7,2,8,6,9,1,3,8];
        $lucky_day+=$suijinri[(int)date('d',$date_ime)];

        chong:
        $lucky_day_array=str_split($lucky_day);
        $lucky_day=0;
        foreach ($lucky_day_array as $keyl=>$valuel){
            $lucky_day+=$valuel;
        }

        if($lucky_day>9){
            goto chong;
        }
        $data['lucky_number'] = $lucky_day;
        $data['lucky_color'] = '黄色';
        $data['lucky_bearing'] = '东北';

        $color[] = ["红色", "黄色", "绿色", "青色", "蓝色", "紫色", "黑色"];
        $color[] = ["朱红", "桔黄", "豆绿", "豆青", "天蓝", "紫罗兰色", "土黑"];
        $color[] = ["粉红", "深桔黄", "浅豆绿", "花青", "蔚蓝", "紫藤色", "棕色"];
        $color[] = ["梅红", "浅桔黄", "橄榄绿", "茶青", "月光蓝", "紫水晶色", "红棕"];
        $color[] = ["玫瑰红", "柠檬黄", "茶绿", "葱青", "海洋蓝", "葡萄紫", "金棕"];
        $color[] = ["桃红", "玉米黄", "葱绿", "天青", "海蓝", "茄皮紫", "铁锈棕"];
        $color[] = ["樱桃红", "橄榄黄", "苹果绿", "霁青", "湖蓝", "玫瑰紫", "桔棕"];
        $color[] = ["桔红", "樱草黄", "原野绿", "石青", "深湖蓝", "丁香紫", "橄榄棕"];
        $color[] = ["石榴红", "稻草黄", "森林绿", "铁青", "中湖蓝", "钴紫", "褐色"];
        $color[] = ["枣红", "芥末黄", "洋蓟绿", "蟹青", "浅湖蓝", "绛紫", "深褐"];
        $color[] = ["莲红", "杏黄", "苔藓绿", "鳝鱼青", "赤褐", "赤褐", "灰褐"];
        $color[] = ["灰色", "莲灰", "铅灰", "豆灰", "梅红", "蓝色", "绿色"];
        $color[] = ["银灰", "茶褐", "碳灰", "藕灰", "玫瑰红", "天蓝", "豆绿"];
        $color[] = ["铁灰", "紫褐", "驼灰", "天青", "桃红", "蔚蓝", "浅豆绿"];
        $flowers = array(
            array("牡丹", "月季", "兰花", "桂花", "菊花", "荷花", "水仙"),
            array("玫瑰", "百合", "茉莉", "杜鹃", "郁金香", "紫罗兰", "风信子"),
            array("夜来香", "丁香", "芍药", "君子兰", "蒲公英", "栀子花", "冬青"),
            array("桃花", "梅花", "素馨", "金盏花", "矢车菊", "薰衣草", "牛王花"),
            array("罂粟", "鸢尾花", "紫菀", "落新妇", "杜英", "玉兰", "塔菊"),
            array("穗穗金", "凌霄", "勿忘我", "满天星", "雏菊", "迎春花", "常春藤"),
            array("蓝莓花", "牵牛花", "金鸡菊", "木蓝", "桔梗", "翠菊", "薹草"),
            array("七里香", "油桐花", "洋水仙", "石斛兰", "龙胆", "虞美人", "凤仙花"),
            array("蝴蝶花", "火鹤花", "曼陀罗", "金盏黄", "黄槐花", "柳兰", "百日菊"),
            array("唐菖蒲", "海桐花", "葵花", "安哥拉", "萱草", "风车草", "白香花"),
            array("芝樱", "飞燕草", "金花", "鸡冠花", "锦葵", "仙客来", "五色梅"),
            array("雏菊", "铁线莲", "夜来香", "寿菊", "秋桜", "云雾菊", "百子莲"),
            array("阳光菊", "银盏花", "丹参花", "金鱼草", "睡莲", "马利筋", "鹿蹄草")
        );
        $stones = array(
            array("长石", "黑曜石", "石英", "黄玉", "绿松石", "孔雀石", "青金石"),
            array("红玛瑙", "蓝玛瑙", "黄玛瑙", "紫晶", "白玉", "软玉", "葡萄石"),
            array("青玉", "橄榄石", "黄金矿", "碧玺", "锡矿", "绿耀石", "磷灰石"),
            array("猫眼石", "蛋白石", "蓝宝石", "红宝石", "翡翠", "舍丹石", "孔雀石"),
            array("镉矾", "粉晶", "象牙石", "氧化铝", "石榴石", "草绿宝石", "珍珠"),
            array("琥珀", "黑玄武岩", "刚玉", "斜长石", "和田玉", "金星石", "云母石"),
            array("玻璃石", "彩虹石", "钻石", "海蓝宝石", "蓝铜矿", "烟熏石", "石榴石"),
            array("橄榄石", "黄铜矿", "方解石", "硅石", "海蓝宝石", "镉黄矿", "坦桑石"),
            array("紫水晶", "玻璃石", "隕石", "辉石", "影青石", "石碱", "杂石矿"),
            array("石膏", "蜜蜡石", "月石", "硼石", "樟柏石", "雪花石", "朱砂"),
            array("花岗岩", "石墨", "滑石", "煤", "间砂岩", "石灰岩", "粉砂岩"),
            array("赭石", "黄铁矿", "云母", "霞石", "蛇纹岩", "石英箨", "紫砂"),
            array("金刚砂", "孔雀石", "水滴石", "铁矿石", "白珪石", "光母石", "橄榄岩")
        );
        $foods = array(
            array("红烧肉", "清蒸鱼", "排骨汤", "烤鸭肉", "宫保鸡", "麻婆豆", "鱼香肉"),
            array("炖牛肉", "羊肉串", "大闸蟹", "佛跳墙", "四季豆", "红烧茄", "西兰花"),
            array("涮火锅", "麻辣烫", "烤肉串", "香辣鱼", "香辣蟹", "酸菜鱼", "牛肉面"),
            array("糖醋鱼", "葱爆羊", "狮子头", "梅菜肉", "清蒸参", "炸响铃", "蚝油菜"),
            array("鸡翅中", "香辣虾", "酱牛肉", "鲍鱼片", "炒腰花", "红烧骨", "扇贝蒸"),
            array("麻辣锅", "鱼豆腐", "红烧鳗", "香辣煲", "糖醋里", "清蒸鱼", "生蚝烤"),
            array("糖醋藕", "红烧瓜", "清蒸薯", "香辣茄", "红烧皮", "清蒸丝", "空心菜"),
            array("红烧兔", "香辣蛙", "清蒸鱼", "煲仔饭", "干煸肠", "红烧鸡", "蘑菇蚝"),
            array("糖醋带", "葱油鸡", "红烧鸭", "清蒸斑", "香辣圈", "红烧蹄", "海带结"),
            array("红烧狗", "香辣虾", "清蒸鳕", "海鲜煲", "干煸丝", "鸡翅根", "生菜卷"),
            array("糖醋丸", "小龙虾", "红烧鹅", "清蒸鱼", "炒鳝片", "炖土豆", "秋葵蒜"),
            array("红烧驴", "香辣螺", "清蒸鱼", "海鲜锅", "四季炒", "红烧头", "扇贝蒸"),
            array("脆皮鱼", "香辣腩", "红烧鸽", "黄花鱼", "炒猪肚", "蹄筋烧", "蒸扇贝")
        );
        $foods = array(
            array("红烧肉", "清蒸鱼", "排骨汤", "烤鸭肉", "宫保鸡丁", "麻婆豆腐", "鱼香肉丝"),
            array("炖牛肉", "羊肉串", "大闸蟹", "佛跳墙", "四季豆炒肉", "红烧茄子", "西兰花炒肉"),
            array("涮火锅", "麻辣烫", "烤肉串", "香辣鱼块", "香辣蟹", "酸菜鱼", "牛肉面"),
            array("糖醋鱼块", "葱爆羊肉", "狮子头", "梅菜扣肉", "清蒸海参", "炸响铃", "蚝油菜心"),
            array("鸡翅中", "香辣虾", "酱牛肉", "鲍鱼片", "炒腰花", "红烧排骨", "扇贝蒸蛋"),
            array("麻辣锅", "鱼豆腐", "红烧鳗鱼", "香辣煲仔", "糖醋里脊", "清蒸鱼", "生蚝烤肉"),
            array("糖醋藕片", "红烧冬瓜", "清蒸红薯", "香辣茄子", "红烧猪皮", "清蒸萝卜丝", "空心菜"),
            array("红烧兔肉", "香辣牛蛙", "清蒸鱼", "煲仔饭", "干煸肥肠", "红烧鸡肉", "蘑菇蚝煎"),
            array("糖醋带鱼", "葱油鸡", "红烧鸭肉", "清蒸石斑鱼", "香辣鱿鱼圈", "红烧猪蹄", "海带结"),
            array("红烧狗肉", "香辣虾球", "清蒸鳕鱼", "海鲜煲仔", "干煸土豆丝", "鸡翅根", "生菜卷"),
            array("糖醋丸子", "小龙虾", "红烧鹅肉", "清蒸鱼", "炒鳝鱼片", "炖土豆", "秋葵蒜香"),
            array("红烧驴肉", "香辣田螺", "清蒸鱼", "海鲜锅", "四季豆炒肉", "红烧鱼头", "扇贝蒸粉丝"),
            array("脆皮鱼", "香辣猪腩", "红烧鸽子", "清蒸黄花鱼", "炒猪肚", "蹄筋烧牛肉", "蒸扇贝")
        );
        $foods = array(
            array("红烧肉", "清蒸鱼", "排骨汤", "烤鸭肉", "宫保鸡丁", "麻婆豆腐", "鱼香肉丝"),
            array("炖牛肉", "羊肉串", "大闸蟹", "佛跳墙", "四季豆炒肉", "红烧茄子", "西兰花炒肉"),
            array("涮火锅", "麻辣烫", "烤肉串", "香辣鱼块", "香辣蟹", "酸菜鱼", "牛肉面"),
            array("糖醋鱼块", "葱爆羊肉", "狮子头", "梅菜扣肉", "清蒸海参", "炸响铃", "蚝油菜心"),
            array("鸡翅中", "香辣虾", "酱牛肉", "鲍鱼片", "炒腰花", "红烧排骨", "扇贝蒸蛋"),
            array("麻辣锅", "鱼豆腐", "红烧鳗鱼", "香辣煲仔", "糖醋里脊", "清蒸鱼", "生蚝烤肉"),
            array("糖醋藕片", "红烧冬瓜", "清蒸红薯", "香辣茄子", "红烧猪皮", "清蒸萝卜丝", "空心菜"),
            array("红烧兔肉", "香辣牛蛙", "清蒸鱼", "煲仔饭", "干煸肥肠", "红烧鸡肉", "蘑菇蚝煎"),
            array("糖醋带鱼", "葱油鸡", "红烧鸭肉", "清蒸石斑鱼", "香辣鱿鱼圈", "红烧猪蹄", "海带结"),
            array("红烧排骨", "香辣虾球", "清蒸鳕鱼", "海鲜煲仔", "干煸土豆丝", "鸡翅根", "生菜卷"),  // 修改"红烧狗肉"
            array("糖醋丸子", "小龙虾", "红烧鹅肉", "清蒸鱼", "炒鳝鱼片", "炖土豆", "秋葵蒜香"),
            array("红烧驴肉", "香辣田螺", "清蒸鱼", "海鲜锅", "四季豆炒肉", "红烧鱼头", "扇贝蒸粉丝"),
            array("脆皮鱼", "香辣猪腩", "红烧鸽子", "清蒸黄花鱼", "炒猪肚", "蹄筋烧牛肉", "蒸扇贝")
        );

        $data['lucky_color'] = $color[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_flower'] = $flowers[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_stone'] = $stones[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_foods'] = $foods[$lucky_day][date('w', $life_birth_time)];
        return $data;
    }
}
