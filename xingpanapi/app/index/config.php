<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

// 前端配置文件
// 前端配置文件

$wechat_themes = 'default';
$module         = request()->module();

$view_path = ROOT_PATH . SYS_APP_NAMESPACE . SYS_DS_PROS . $module. SYS_DS_PROS . LAYER_VIEW_NAME . SYS_DS_PROS . $wechat_themes . SYS_DS_PROS;

if(isset($_SERVER['HTTP_X_CLIENT_SCHEME'])){
    $scheme = $_SERVER['HTTP_X_CLIENT_SCHEME'];
}elseif(isset($_SERVER['REQUEST_SCHEME'])){
    $scheme = $_SERVER['REQUEST_SCHEME'];
}else{
    $scheme = 'http';
}

$static['__STATIC__'] =  $scheme.'://media.xingpan.vip/public'.SYS_DS_PROS . SYS_STATIC_DIR_NAME .SYS_DS_PROS .'module'. SYS_DS_PROS . $module .  SYS_DS_PROS . $wechat_themes;
$static['__COMMON__'] =  $scheme.'://media.xingpan.vip/public'.SYS_DS_PROS . SYS_STATIC_DIR_NAME .SYS_DS_PROS .'module'. SYS_DS_PROS . SYS_COMMON_DIR_NAME;
return [

    'template' => ['view_path' => $view_path,  'layout_on' =>  true, 'layout_name' => 'layout'],

    /* 模板常量替换配置 */
    'view_replace_str' => $static,
    /* 存储驱动,若无需使用云存储则为空 */
    'storage_driver' => 'Aliyun',

    // 应用调试模式
    'app_debug'              => true,
    'lang_switch_on' => true,
    // 默认语言
    'default_lang'           => 'en-us',
];