<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\index\controller;

use app\common\controller\ControllerBase;

use think\Cache;

class Timing extends ControllerBase
{


    public function apistatistical()
    {

        $create_time = mktime(date('H'), 0, 0, date('m'), date('d'), date('Y'));
        $create_time -= 3600;
        $member_list = $this->logicMember->getMemberColumn(['status' => ['gt', -2]]);
        $api_list = $this->logicApi->getApiList(['status' => ['gt', -2]], true, '', false);
        foreach ($api_list as $keyapi => $valueapi) {

            foreach ($member_list as $key => $value) {

                $data_post['count'] = Cache::store('redis')->get($value['id'] . '_' . $valueapi['id'] . '_' . $create_time);
                $data_post['create_time'] = $create_time;
                $data_post['member_id'] = $value['id'];
                $data_post['a_id'] = $valueapi['id'];
                //$data[]=$data_post;
                if (!$data_post['count']) {
                    $data_post['count'] = 0;
                    continue;
                }
                $data_where = db('api_statistical')->where(['create_time' => $create_time, 'a_id' => $valueapi['id'], 'member_id' => $value['id']])->find();

                if (!empty($data_where)) {
                    db('api_statistical')->where('id', $data_where["id"])->update($data_post);
                } else {
                    db('api_statistical')->insert($data_post);
                }
                dump($data_post);
                delRedis($value['id'] . '_' . $valueapi['id'] . '_' . $create_time);
                //Cache::store('redis')->rm($value['id'].'_count_'.$create_time);
            }

        }

        //$this->logicApiStatistical->setApiStatisticalColumn($data);
    }
    //采集这个网址：https://www.georgianicols.com/daily/
    //需要页面的内容是十二个星座标题和把内容
    public function apistatistical2()
    {

        $create_time = $this->param['time'];

        for ($ii = 0; $ii <= 5; $ii++) {
            $create_time -= 3600 * $ii;
            $member_list = $this->logicMember->getMemberColumn(['status' => ['gt', -2]]);
            $api_list = $this->logicApi->getApiList(['status' => ['gt', -2]], true, '', false);
            foreach ($api_list as $keyapi => $valueapi) {

                foreach ($member_list as $key => $value) {

                    $data_post['count'] = Cache::store('redis')->get($value['id'] . '_' . $valueapi['id'] . '_' . $create_time);
                    $data_post['create_time'] = $create_time;
                    $data_post['member_id'] = $value['id'];
                    $data_post['a_id'] = $valueapi['id'];
                    //$data[]=$data_post;
                    if (!$data_post['count']) {
                        $data_post['count'] = 0;
                        continue;
                    }
                    $data_where = db('api_statistical')->where(['create_time' => $create_time, 'a_id' => $valueapi['id'], 'member_id' => $value['id']])->find();
                    dump($data_post);
                    if (!empty($data_where)) {
                        dump($data_where);
                        db('api_statistical')->where('id', $data_where["id"])->update($data_post);
                    } else {
                        db('api_statistical')->insert($data_post);
                    }
                    delRedis($value['id'] . '_' . $valueapi['id'] . '_' . $create_time);
                    //Cache::store('redis')->rm($value['id'].'_count_'.$create_time);
                }

            }
        }
        //自动跳转当前地址time修改为当前时间戳
        $url = 'http://demo.xingpan.vip/index/Timing/apistatistical2?time=' . $create_time;
        echo $url;
        header("Refresh: 1; url=$url");

        //$this->logicApiStatistical->setApiStatisticalColumn($data);
    }

    public function apistatistical3()
    {

        $redis = new \Redis();

// 连接到Redis服务器
// 参数说明：host, port, timeout
        $redis->connect('127.0.0.1', 6379, 2.5);

// 如果你想连接到需要密码的Redis服务器
// $redis->auth('your_redis_password');

// 获取所有键
        $member_list = $redis->keys('*');
        $iii=0;
// 输出所有键
        foreach ($member_list as $key => $value) {

            if($iii>300){
                break;
            }
            $data_post['count'] = Cache::store('redis')->get($value);
            //拆分996_18_1745071200
            $value_arauyf = explode('_', $value);
            dump($value);
            dump($value);
            dump($value_arauyf);
            if (count($value_arauyf) == 3 and $value_arauyf[1]>0 and $value_arauyf[2]>0) {
                dump(count($value_arauyf));
                $data_post['create_time'] = $value_arauyf[2];
                $data_post['member_id'] = $value_arauyf[0];
                $data_post['a_id'] = $value_arauyf[1];
                //$data[]=$data_post;
                if (!$data_post['count']) {
                    $data_post['count'] = 0;
                    continue;
                }
                $data_where = db('api_statistical')->where(['create_time' => $data_post['create_time'], 'a_id' => $data_post['a_id'], 'member_id' => $data_post['member_id']])->find();
                dump($data_post);
                if (!empty($data_where)) {
                    db('api_statistical')->where('id', $data_where["id"])->update($data_post);
                } else {
                    db('api_statistical')->insert($data_post);
                }
                delRedis($value);
            }
            $iii++;
            //Cache::store('redis')->rm($value['id'].'_count_'.$create_time);
        }
        $url = 'http://demo.xingpan.vip/index/Timing/apistatistical3';
        echo $url;
        header("Refresh: 1; url=$url");
    }

    public function astrologyday()
    {
        // 文件路径
        $filePath = 'day_' . date("Ymd") . '.json';
        // 检查文件是否存在
        if (file_exists($filePath)) {
            echo "文件已存在，无需重新获取数据。";
            return;
        }

        // 目标网址
        $url = 'https://www.georgianicols.com/daily/yesterday';

        // 初始化 cURL 会话
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）

        // 禁用 SSL 验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        // 设置 User-Agent
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');

        // 执行请求
        $response = curl_exec($ch);

        // 检查请求是否成功
        if (curl_errno($ch)) {
            error_log('cURL Error: ' . curl_error($ch));
            var_dump(curl_errno($ch));
            var_dump(curl_error($ch));
            return;
        }

        // 关闭 cURL 会话
        curl_close($ch);

        // 创建 DOMDocument 对象
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true); // 禁用错误报告
        $dom->loadHTML($response);
        libxml_clear_errors(); // 清除错误报告

        // 创建 DOMXPath 对象
        $xpath = new \DOMXPath($dom);

        // 定位星座标题和内容
        $astrologyData = [];
        $signDivs = $xpath->query('//div[contains(@class, "scope-sign")]');

        foreach ($signDivs as $signDiv) {
            // 获取 h2 标签的内容
            $h2Element = $signDiv->getElementsByTagName('h2')->item(0);
            $title = $h2Element ? $h2Element->textContent : '';

            // 如果 h2 标签不存在，尝试获取 h4 标签的内容
            if (empty($title)) {
                $h4Element = $signDiv->getElementsByTagName('h4')->item(0);
                $title = $h4Element ? $h4Element->textContent : '';
            }

            // 获取内容并过滤特殊字符 ***
            $content = $signDiv->getElementsByTagName('p')->item(0)->textContent;
            $content = str_replace('***', '', $content);

            // 获取类名并替换 -
            $className = $signDiv->getAttribute('class');
            $classes = explode(' ', $className);
            $key = str_replace('-', '_', $classes[1]);

            $astrologyData[$key] = [
                'title' => trim($title),
                'content' => trim($content)
            ];
        }

        var_dump($astrologyData);

        file_put_contents($filePath, json_encode($astrologyData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

        echo "数据已保存到文件：{$filePath}";
    }


    //月见拉去数据
    public function getastrologyday()
    {
        // 文件路径
        $this_time = time();
        $today = date("Ymd", ($this_time + 8 * 3600));
        $filePath = 'sign/day_' . $today . '.json';

        // 检查文件是否存在
        if (file_exists($filePath)) {
            echo "文件已存在，无需重新获取数据。";
            return;
        }

        // 定义星座数组
        $signs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];

        $base_url = 'https://dailyhoianicols-ohcqthkvvh.us-west-1.fcapp.run/horoscope/';
        $results = [];

        foreach ($signs as $sign) {
            $url = $base_url . $sign;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            // 增加超时时间
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
            curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）
            // 禁用 SSL 验证
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            // 设置 User-Agent
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
            // 执行请求
            $response = curl_exec($ch);
            // 检查请求是否成功
            if (curl_errno($ch)) {
                var_dump(curl_error($ch));
                return;
            }
            if ($response === false) {
                // 处理错误
                echo "请求失败";
            } else {
                // 将 JSON 字符串转换为数组
                $data = json_decode($response, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    // 处理 JSON 解析错误
                    echo "JSON 解析错误: " . json_last_error_msg();
                } else {
                    // 处理转换后的数组

                    $dateString = $data['date'];
                    $timestamp = strtotime($dateString);
                    $formattedDate = date('Ymd', $timestamp);

                    if ($formattedDate != $today) {

                        dump($today);
                        dump($formattedDate);
                        break;
                    }

                    $results[$sign] = $data;
                }
            }
            sleep(2);
        }

        if (!empty($results)) {

            file_put_contents($filePath, json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            echo "数据已保存到文件：{$filePath}";
        }
    }

    //月见拉去数据
    public function getastrologyweek()
    {
        // 文件路径

        $this_time = time();
        $today = date("YW", ($this_time + 8 * 3600));
        $filePath = 'sign/week_' . $today . '.json';

        // 检查文件是否存在
        if (file_exists($filePath)) {
            echo "文件已存在，无需重新获取数据。";
            return;
        }

        // 定义星座数组
        $signs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];

        $base_url = 'https://dailyhoianicols-ohcqthkvvh.us-west-1.fcapp.run/horoscope/weekly/';
        $results = [];

        foreach ($signs as $sign) {
            $url = $base_url . $sign;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            // 增加超时时间
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
            curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）
            // 禁用 SSL 验证
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            // 设置 User-Agent
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
            // 执行请求
            $response = curl_exec($ch);
            // 检查请求是否成功
            if (curl_errno($ch)) {
                var_dump(curl_error($ch));
                return;
            }
            if ($response === false) {
                // 处理错误
                echo "请求失败";
            } else {
                // 将 JSON 字符串转换为数组
                $data = json_decode($response, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    // 处理 JSON 解析错误
                    echo "JSON 解析错误: " . json_last_error_msg();
                } else {
                    // 处理转换后的数组

                    $dateString = $data['date'];
                    $timestamp = strtotime($dateString);
                    $formattedDate = date("YW", $timestamp);

                    if ($formattedDate != $today) {

                        dump($today);
                        dump($formattedDate);
                        dump($dateString);
                        // break;
                    }

                    $results[$sign] = $data;
                }
            }
            sleep(2);
        }

        if (!empty($results)) {

            file_put_contents($filePath, json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            echo "数据已保存到文件：{$filePath}";
        }
    }

    //月见拉去数据
    public function getastrologymonth()
    {
        // 文件路径

        $this_time = time();
        $today = date("Ym", ($this_time - 16 * 3600));
        $filePath = 'sign/month_' . $today . '.json';

        // 检查文件是否存在
        if (file_exists($filePath)) {
            echo "文件已存在，无需重新获取数据。";
            return;
        }

        // 定义星座数组
        $signs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];

        $base_url = 'https://dailyhoianicols-ohcqthkvvh.us-west-1.fcapp.run/horoscope/monthly/';
        $results = [];

        // 定义星座数组
        $signs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];

        $base_url = 'https://answerdeepseek-xdtlofpqls.cn-hangzhou.fcapp.run/monthly/monthly_horoscope_' . $today . '.json';
        $results = [];
        dump($base_url);
        $url = $base_url;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）
        // 禁用 SSL 验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // 设置 User-Agent
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
        // 执行请求
        $response = curl_exec($ch);
        // 检查请求是否成功
        if (curl_errno($ch)) {
            var_dump(curl_error($ch));
            return;
        }
        if ($response === false) {
            // 处理错误
            echo "请求失败";
        } else {
            // 将 JSON 字符串转换为数组+
            dump($response);
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                // 处理 JSON 解析错误
                echo "JSON 解析错误: " . json_last_error_msg();
            } else {
                // 处理转换后的数组

                $dateString = $data;
                foreach ($data as $sign) {
                    dump($sign);

                    $results[$sign['sign']] = $sign;
                }
            }
        }
        dump($results);

        if (!empty($results)) {

            file_put_contents($filePath, json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            echo "数据已保存到文件：{$filePath}";
        }
    }

    //心智拉十二星座数据
//    public function getday_xinjianzhineng()
//    {  // 开启错误报告
//        error_reporting(E_ALL);
//        ini_set('display_errors', 1);
//
//        ini_set('max_execution_time', 1200); // 设置最大执行时间为600秒
//        ini_set('memory_limit', '156M');    // 设置内存限制为256MB
//        // 文件路径
//        $this_time=time();
//        $today=date("Ymd", ($this_time+9*3600+10));
//        $filePath = 'xinjianzhineng/day_' . $today . '.json';
//
//        // 检查文件是否存在
//        if (file_exists($filePath)) {
//            echo "文件已存在，无需重新获取数据。";
//            return;
//        }
//
//        // 定义星座数组
//        $signs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
//            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];
//
//        $base_url = 'https://xinjianengriyun-aqvkfeacbx.cn-hangzhou.fcapp.run?sign=';
//        $results = [];
//
//        for( $i=1;$i<=12;$i++){
//            $url = $base_url . $i;
//
//            var_dump($url);
//            $ch = curl_init();
//            curl_setopt($ch, CURLOPT_URL, $url);
//            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//            // 增加超时时间
//            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
//            curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）
//            // 禁用 SSL 验证
//            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
//            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
//            // 设置 User-Agent
//            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
//            // 执行请求
//            $response = curl_exec($ch);
//            // 检查请求是否成功
//            if (curl_errno($ch)) {
//                var_dump(curl_error($ch));
//                return;
//            }
//            var_dump(111);
//            if ($response === false) {
//                // 处理错误
//                echo "请求失败";
//            } else {
//                // 将 JSON 字符串转换为数组
//                $data = json_decode($response, true);
//
//                var_dump(222);
//                if (json_last_error() !== JSON_ERROR_NONE) {
//                    // 处理 JSON 解析错误
//                    echo "JSON 解析错误: " . json_last_error_msg();
//                } else {
//                    // 处理转换后的数组
//
//                    var_dump(333);
//                        dump($data['data']);
//
//                        if(empty($data['data'])){
//                            exit();
//                        }else{
//                            $results[$i] = $data['data'];
//                        }
//                }
//            }
//            sleep(1);
//        }
//
//        dumP($results);
//        if(!empty($results)){
//
//            file_put_contents($filePath, json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
//            echo "数据已保存到文件：{$filePath}";
//        }
//    }

    public function getday_xinjianzhineng()
    {  // 开启错误报告
        error_reporting(E_ALL);
        ini_set('display_errors', 1);

        ini_set('max_execution_time', 1200); // 设置最大执行时间为600秒
        ini_set('memory_limit', '156M');    // 设置内存限制为256MB
        // 文件路径

        $this_time = time();
        if (!empty($_GET['time'])) {
            $this_time = $_GET['time'];
        }

        $today = date("Ymd", ($this_time + 9 * 3600 + 10));
        $filePath = 'xinjianzhineng/day_' . $today . '.json';

        dump($filePath);
        // 检查文件是否存在
        if (file_exists($filePath)) {
            //读取文件
            $data = file_get_contents($filePath);

            echo "文件已存在，无需重新获取数据。";
            return;
        }

        // 定义星座数组
        $signs = ["", 'aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];
        $signChinese = array("", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座",
            "射手座", "摩羯座", "水瓶座", "双鱼座");

        $base_url = 'https://xinjianengriyun-aqvkfeacbx.cn-hangzhou.fcapp.run/get_horoscope_by_filename?filename=horoscope_' . date("Ymd", $this_time) . ".json";
        $results = [];


        $url = $base_url;

        var_dump($url);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）
        // 禁用 SSL 验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // 设置 User-Agent
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
        // 执行请求
        $response = curl_exec($ch);

        // 检查请求是否成功
        if (curl_errno($ch)) {
            var_dump(curl_error($ch));
            return;
        }
        var_dump(111);
        if ($response === false) {
            // 处理错误
            echo "请求失败";
        } else {
            // 将 JSON 字符串转换为数组
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                // 处理 JSON 解析错误
                echo "JSON 解析错误: " . json_last_error_msg();
            } else {
                // 处理转换后的数组

                if (empty($data)) {
                    exit();
                } else {
                    $results = $data["horoscopes"];
                }
            }
        }
        dump($results);
        $dsf = array();
        $results_sign = array();
        foreach ($results as $keysf => $valuesf) {
            if (empty($dsf[array_search($keysf, $signChinese)])) {
                $dsf[array_search(strtolower($valuesf['sign']), $signs)] = array();
            }
            dump(array_search(strtolower($valuesf['sign']), $signs));
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['overall'];
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['love'];
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['career'];
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['health'];


            $results_sign[strtolower($valuesf['sign'])] = $valuesf;
        }
        dump($dsf);
        dump($results_sign);
        if (!empty($dsf)) {

            file_put_contents('sign_corpus/day_' . $today . '.json', json_encode($results_sign, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            file_put_contents($filePath, json_encode($dsf, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            echo "数据已保存到文件：{$filePath}";
        }

        if (date("w", $this_time) == 1) {
            $this->getweek_xinjianzhineng();
        }
        //判断当前日期是不是当月最后一天
        if (date("t", $this_time) == date("j", $this_time)) {
            $this->getmoon_xinjianzhineng();
        }
    }


    // 每周运势
    public function getweek_xinjianzhineng()
    {

        // 文件路径
        $this_time = time();
        //判断今天是不是周日
        if (!empty($_GET['time'])) {
            $this_time = $_GET['time'];
        }


        dump(date("w", $this_time));

        $today = date("YW", ($this_time));
        $filePath = 'xinjianzhineng/week_' . $today . '.json';

        dump($today);
        // 检查文件是否存在
        if (file_exists($filePath)) {
            echo "文件已存在，无需重新获取数据。";
            return;
        }

        // 定义星座数组
        $signs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];
        $signChinese = array("", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座",
            "射手座", "摩羯座", "水瓶座", "双鱼座");

        $url = 'https://xinjianouyunglm-ycrsemijot.cn-hangzhou.fcapp.run/all';
        $url = 'https://answerdeepseek-xdtlofpqls.cn-hangzhou.fcapp.run/weekly/weekly_horoscopecn_' . date("Ymd", ($this_time + 9 * 3600 + 10)) . '.json';
        $results = [];
        var_dump($url);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）
        // 禁用 SSL 验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // 设置 User-Agent
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
        // 执行请求
        $response = curl_exec($ch);
        // 检查请求是否成功
        if (curl_errno($ch)) {
            return;
        }
        var_dump($response);
        if ($response === false) {
            // 处理错误
            echo "请求失败";
        } else {
            // 将 JSON 字符串转换为数组
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                // 处理 JSON 解析错误
                echo "JSON 解析错误: " . json_last_error_msg();
            } else {
                // 处理转换后的数组

                if (empty($data)) {
                    exit();
                } else {
                    $results = $data;
                }
            }
        }
        dump($results);
        $dsf = array();
        $results_sign = array();
        //计算当前日期
        $dateRange = $results[0]['week'];
        list($startDateStr, $endDateStr) = explode(" - ", $dateRange);

        $timestamp_new = strtotime($endDateStr);

        $today = date("YW", ($timestamp_new));

        $filePath = 'xinjianzhineng/week_' . $today . '.json';

        foreach ($results as $keysf => $valuesf) {
            if (empty($dsf[array_search($keysf, $signChinese)])) {
                $dsf[array_search(strtolower($valuesf['sign']), $signs)] = array();
            }
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['overall'];
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['love'];
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['career'];
            $dsf[array_search(strtolower($valuesf['sign']), $signs)][] = $valuesf['wealth'];

            $results_sign[strtolower($valuesf['sign'])] = $valuesf;
        }

        if (!empty($dsf)) {
            file_put_contents('sign_corpus/week_' . $today . '.json', json_encode($results_sign, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            file_put_contents($filePath, json_encode($dsf, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            echo "数据1已保存到文件：{$filePath}";
        }
    }

    // 每月运势
    public function getmoon_xinjianzhineng()
    {
        // 文件路径
        $this_time = time();
        //判断今天是不是周日
        if (!empty($_GET['time'])) {
            $this_time = $_GET['time'];
        }
        $today = date("Ym", ($this_time + 9 * 3600 + 10));
        $filePath = 'xinjianzhineng/moon_' . $today . '.json';

        // 检查文件是否存在
        if (file_exists($filePath)) {
            echo "文件已存在，无需重新获取数据。";
            return;
        }

        // 定义星座数组
        $signs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra',
            'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];
        $signChinese = array("", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座",
            "射手座", "摩羯座", "水瓶座", "双鱼座");
        $base_url = 'https://answerdeepseek-xdtlofpqls.cn-hangzhou.fcapp.run/monthly/monthly_horoscope_' . $today . '.json';
        $base_url = 'https://answerdeepseek-xdtlofpqls.cn-hangzhou.fcapp.run/monthly/monthly_horoscope_' . $today . '.json';

        $results = [];
        dump($base_url);
        $url = $base_url;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 总超时时间（秒）
        // 禁用 SSL 验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // 设置 User-Agent
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');
        // 执行请求
        $response = curl_exec($ch);
        // 检查请求是否成功
        if (curl_errno($ch)) {
            var_dump(curl_error($ch));
            return;
        }
        if ($response === false) {
            // 处理错误
            echo "请求失败";
        } else {
            // 将 JSON 字符串转换为数组+
            dump($response);
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                // 处理 JSON 解析错误
                echo "JSON 解析错误: " . json_last_error_msg();
            } else {
                // 处理转换后的数组

                $dateString = $data;
                foreach ($data as $sign) {

                    $results[$sign['sign']] = $sign;
                }
            }
        }
        dump($results);


        if (!empty($results)) {

            file_put_contents('sign_corpus/moon_' . $today . '.json', json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            file_put_contents($filePath, json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            echo "数据已保存到文件：{$filePath}";
        }
    }

    public function ceshhh()
    {
        $dateRange = "Feb 24, 2025 - Mar 2, 2025";
        list($startDateStr, $endDateStr) = explode(" - ", $dateRange);
        $timestamp = strtotime($startDateStr);
        $formattedDate = date('Ymd', $timestamp);
        $timestamp_new = strtotime($endDateStr);

        $today = date("YW", ($timestamp_new));
        echo $today;
        echo '<br>';
        $this_time = time();
        //判断今天是不是周日

        $today = date("YW", ($this_time + 9 * 3600 + 10));
        $filePath = 'xinjianzhineng/week_' . $today . '.json';

        echo $today;
    }
}
