<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

// 解析字符串中的{$变量}
function parse_string_val($string, $vars)
{
    $taglib_begin = '{';
    $taglib_end = '}';

    $pattern = '/(' . $taglib_begin . ').*?(' . $taglib_end . ')/is';

    $results = [];

    preg_match_all($pattern, $string, $results);

    foreach ($results[0] as $v) {

        $del_start = substr($v, 2);

        $del_end = substr($del_start, 0, strlen($del_start) - 1);

        $string = isset($vars[$del_end]) ? str_replace($v, $vars[$del_end], $string) : sr($string, $v);
    }

    return $string;
}

/**
 * 检测用户是否登录
 * @return integer 0-未登录，大于0-当前登录用户ID
 */
function is_wei_login()
{

    $member = session('user_auth');

    if (empty($member)) {

        return DATA_DISABLE;
    } else {

        return session('user_auth_sign') == data_auth_sign($member) ? $member['user_id'] : DATA_DISABLE;
    }
}

/*
 * 模版选择
 */
function wechat_themes()
{
    return 'default';
}

/*
 * 模版选择
 */
function getShare($wechat)
{
    $tokens = getAppidToken($wechat, true);
    $JSSDK = get_sington_object('JSSDKEx', exwechat\api\JSSDK\JSSDK::class, $tokens['access_token']);
    $share = $tokens;
    $share['timestamp'] = time();
    $share['url'] = URL_TRUE;
    $share['signature'] = $JSSDK->signature($tokens['ticket'], 'abcdefghijklmnopqrstu', time(), URL_TRUE);
    $share['jsApiList'] = $JSSDK->jsApiList();
    return $share;
}

function msubstr($str , $start=0 , $length=0 , $charset="utf-8",$suffix=true)
{
    empty($length) && $length=mb_strlen($str, 'utf-8')-$start;
    if (function_exists("mb_substr")) {
        if ($suffix) {
            return mb_substr($str, $start, $length, $charset);
        } else {
            return mb_substr($str, $start, $length, $charset);
        }
    } elseif (function_exists('iconv_substr')) {
        if ($suffix) {
            return iconv_substr($str, $start, $length, $charset);
        } else {
            return iconv_substr($str, $start, $length, $charset);
        }
    }
}