<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\evaluation\controller;

class Common extends ApiBase
{

    public function getShare()
    {
        $tokens = getAppidToken('gh_82932ce7f4a3', true);
        $JSSDK = get_sington_object('JSSDKEx', "exwechat\\api\\JSSDK\\JSSDK", $tokens['access_token']);
        $share = $tokens;
        $share['timestamp'] = time();
        //$shortUrl = get_sington_object('shortUrl', "exwechat\\api\\account\\shortUrl",session('appid_conf')['access_token']);
        $share['url'] = URL_TRUE;

        $share['signature'] = $JSSDK->signature($tokens['ticket'], 'abcdefghijklmnopqrstu', time(), URL_TRUE);

        $this->view->engine->layout(false);
        $this->assign('share', $share);
    }

    public function getVip()
    {
        $vip_list = config("ext_evaluation")['vipList'];

        return $this->apiReturn($vip_list);
    }

    /**
     * @return
     * 获取验证码
     */
    public function getVerifyCode()
    {
        $phone = $this->param['mobile'];
        if (empty($this->param['code'])) {
            // 用户没有输入验证码
            return $this->apiReturn(['code' => 1890005, 'msg' => '图形验证不能为空']);
            exit;
        }
        $code = $this->param['code'];


        if(!captcha_check($code,$phone)){
            return $this->apiReturn(['code' => 1890006, 'msg' => '图形验证错误']);
            exit;
        }

        $check = '/^(1(([3-9][0-9])|(47)))\d{8}$/';
        if (!preg_match($check, $phone)) {
            return $this->apiReturn(['code' => 1890007, 'msg' => '手机号格式输入不正确']);
        }
        $code = (string)mt_rand(10000, 99999);

        $parameter['sign_name'] = '罗博科技';
        $parameter['template_code'] = 'SMS_316275238';
        $parameter['phone_number'] = $phone;
        $parameter['template_param'] = ['code' => $code];
        $fdsfs=$this->logicApiBase->getSmsCode($parameter);


        //$fdsfs_array = json_decode($fdsfs, true);

        return $this->apiReturn($code);
    }

}
