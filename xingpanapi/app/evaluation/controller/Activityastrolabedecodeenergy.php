<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\evaluation\controller;

use astrology\planetName as planetName;

/**
 * 能量报告pdf控制器
 */

class Activityastrolabedecodeenergy extends ApiBase
{


    /**
     * 能量报告pdf添加
     */
    public function astrolabeDecodeEnergyAdd()
    {
//        $mobile=$this->param['channel'];
//        $sms_code=$this->param['sms_code'];
//
//        if (empty(cache('send_sms_code_' . $mobile)) || empty($sms_code) || $sms_code != cache('send_sms_code_' . $mobile)) {
//            return $this->apiReturn([API_CODE_NAME => 1010007, API_MSG_NAME => '短信验证码错误']);
//        }

//        $code=$this->param['code'];
//
//         $PromoCodeInfo = $this->logicPromoCode->getPromoCodeUse(['code' => $code,'user_id'=>$mobile]);

        $PromoCodeInfo['status']=1;
        $PromoCodeInfo['enterprise_code']=$this->param['enterprise_code'];

        $getPublicConfigInfo=$this->logicPublicConfig->getPublicConfigInfo(['name'=>'enterprise_code']);

        $values_value_array=array();
        if($getPublicConfigInfo){
            $values_value=$getPublicConfigInfo['value'];
            $values_value_array=json_decode($values_value,true);

        }
        $code_q=array_column($values_value_array,'code');

        $code_q[]=123456;
        if(in_array($PromoCodeInfo['enterprise_code'],$code_q)){
            $regit = $this->logicAstrolabeDecodeEnergy->astrolabeDecodeEnergyEdit($this->param_data);

        }else{
            $regit=[API_CODE_NAME => 1030105, API_MSG_NAME => '当前企业码不存在'];
        }
        return $this->apiReturn($regit);

    }

    /**
     * 能量报告pdf详细
     */
    public function info()
    {

        empty($this->param['order_number']) &&   $this->apiReturn(['code' => 10549856, 'msg' => '订单号不能为空']);
        $whereTransport['order_number'] = $this->param['order_number'];
        $order_number=$this->param['order_number'];
        $activityAstrolabeDecodeEnergyInfo = $this->logicActivityAstrolabeDecodeEnergy->getActivityAstrolabeDecodeEnergyInfo($whereTransport);

        if (empty($activityAstrolabeDecodeEnergyInfo)) {
            //return $this->apiReturn(['code' => 10549856, 'msg' => '没查到您的计算数据哦']);
            $report_report = $this->logicEvaluationReportOrder->verifyOrderInfo(['order_number' => $order_number]);
            if($report_report['code']>0){
                return $this->apiReturn($report_report);
            }
            $archive_info=array_values($report_report['archive_info']);
            $order_info=$report_report['order_info'];
            $report_info=$report_report['report_info'];


            $activityAstrolabeDecodeEnergyInfo = [
                'amount' => $order_info['amount'],
                'order_number' => $order_number,
                'name' => $archive_info[0]['nickname'],
                'sex' => $archive_info[0]['sex'],
                'birthday' => strtotime($archive_info[0]['birthday']),
                'birth_district' => $archive_info[0]['birth_place'],
                'living_district' => $archive_info[0]['now_place'],
                'birth_points' => $archive_info[0]['birth_points'],
                'now_points' => $archive_info[0]['now_points'],
                'is_summer' => $archive_info[0]['is_summer'],
                'status' => 1,
                'time_zone' => $archive_info[0]['time_zone']
            ];
            $activityAstrolabeDecodeEnergyInfo = $this->logicActivityAstrolabeDecodeEnergy->activityAstrolabeDecodeEnergyEdit($activityAstrolabeDecodeEnergyInfo);

        }





        $data['name']=$activityAstrolabeDecodeEnergyInfo['name'];
        $data['sex']=$activityAstrolabeDecodeEnergyInfo['sex']==1 ? '男':'女';
        $data['age']=$this->getAgeByBirth($activityAstrolabeDecodeEnergyInfo['birthday']);




       //火元素：白羊座、狮子座、射手座；风元素：双子座、天秤座、水瓶座；水元素：双鱼座、天蝎座、巨蟹座；土元素：金牛座、处女座、摩羯座。
        $chart_data = json_decode($activityAstrolabeDecodeEnergyInfo["planet_json"], true);


        $pdf_data=array();

        $attribute_chinese = ['变动', '固定' , '开创' , '土象' , '水象' , '火象', '风象'];
        $attribute_english = ['change',  'fixed',  'standard', 'soil',  'water', 'fire','wind'];
        $sign_phase = [["fire", "standard"], ["soil", "fixed"], ["wind", "change"], ["water", "standard"], ["fire", "fixed"], ["soil", "change"], ["wind", "standard"], ["water", "fixed"], ["fire", "change"], ["soil", "standard"], ["wind", "fixed"], ["water", "change"]];
        $sign_score=array(0,0,0,0,0,0,0,0,0,0,0,0);

        $allow_degree['0'] = 5;
        $allow_degree['30'] = 2;
        $allow_degree['60'] = 5;
        $allow_degree['90'] = 5;
        $allow_degree['120'] = 5;
        $allow_degree['150'] = 2;
        $allow_degree['180'] = 5;

        $attribute_score=array('change'=>0,  'fixed'=>0,  'standard'=>0, 'soil'=>0,  'water'=>0, 'fire'=>0,'wind'=>0);
        //日、月、水、金、火、木、土、南交8个点
        foreach ($chart_data as $keys=>&$voles){
            //
            if(in_array($voles['code_name'],['0','1','2','3','4','5','6','21'])){
                $sign_score[$voles['sign']['sign_id']]++;
            }
            if(in_array($voles['code_name'],['0','1','2','3','4','5','6'])){

                $attribute_score[$sign_phase[$voles['sign']['sign_id']][0]]++;
                $attribute_score[$sign_phase[$voles['sign']['sign_id']][1]]++;
            }

            foreach ($chart_data as $keyg => $valueg) {
                if ($keys == $keyg) {
                    continue;
                }
                $chazhi = abs($voles['longitude'] - $valueg['longitude']);
                $chazhi_xian = $chazhi;
                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                $planet_degree_lgit = 0;
                $voles['planet_allow_degree']=array();
                foreach ($allow_degree as $keyAd => $valueAd) {

                    $valueAd += $planet_degree_lgit;

                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {

                        $planet_allow_degree = [
                            'planet_number' => $keyg,
                            'code_name' => $valueg['code_name'],
                            'planet_english' => $valueg['planet_english'],
                            'planet_chinese' => $valueg['planet_chinese'],
                            'current_longitude' => $valueg['longitude'],
                            'sign_id' => $valueg['sign']['sign_id'],
                            'allow' => $keyAd,
                        ];

                        $voles['planet_allow_degree'][] = $planet_allow_degree;

                    }
                }
            }
        }
        $data['sign_score']=$sign_score;
        $data['attribute_score']=$attribute_score;

        $signChinese = array("白羊", "金牛", "双子", "巨蟹", "狮子", "处女", "天秤", "天蝎", "射手", "摩羯", "水瓶", "双鱼");


        $gao_xing=false;
        if($sign_score[0]<2 and $sign_score[4]<2 and $sign_score[7]<2 and $sign_score[9]<2){
            $gao_xing=true;
        }

        $gao_yue_tian_wang=false;
        foreach ($chart_data[1]["planet_allow_degree"] as $keigs=>$jgsh){
            if($jgsh['code_name']==7){
                $gao_yue_tian_wang=true;
            }
        }

        $gao_yue=false;
        if(in_array($chart_data[1]['sign']['sign_id'], [1,6,10]) or (in_array($chart_data[1]['sign']['sign_id'], [2,3,5,7,9,11]) and $gao_yue_tian_wang)){
            $gao_yue=true;
        }
        if($gao_xing and $gao_yue and in_array($chart_data[3]['sign']['sign_id'], [6,10])){
            $data['peace_score']='过高';
        }else if(($sign_score[0]>2 or $sign_score[4]>2 or $sign_score[7]>2 or $sign_score[9]>2) and in_array($chart_data[1]['sign']['sign_id'], [0,4,7,9])){
            $data['peace_score']='偏低';
        }else{
            $data['peace_score']='正常';
        }



        //Unstable
        $mu_tai_xiang=false;
        $mu_tai_duo_xiang=false;
        foreach ($chart_data[5]["planet_allow_degree"] as $keigs=>$jgsh){
            if($jgsh['code_name']==0 and in_array($jgsh['allow'],[0,60,120])){
                $mu_tai_xiang=true;
            }

            if($jgsh['code_name']==0 and in_array($jgsh['allow'],[0,30,90,150,180])){
                $mu_tai_duo_xiang=true;
            }
        }

        $tu_xiang=true;
        foreach ($chart_data[6]["planet_allow_degree"] as $keigs=>$jgsh){
            if(in_array($jgsh['code_name'],[0,1,2,3,4,21]) and in_array($jgsh['allow'],[0,30,90,150,180])){
                $tu_xiang=false;
            }
        }

        if(($mu_tai_xiang ) and $tu_xiang){
            $data['unstable_score']='过高';
        }else if($mu_tai_duo_xiang){
            $data['unstable_score']='偏低';
        }else{
            $data['unstable_score']='正常';
        }


        //control
        $yue_suo_xian=true;
        $yue_ju_xian=false;
        foreach ($chart_data[1]["planet_allow_degree"] as $keigs=>$jgsh){
            if(in_array($jgsh['sign_id'],[0,3])){
                $yue_suo_xian=false;
            }
            if(in_array($jgsh['sign_id'],[3,11])){
                $yue_ju_xian=true;
            }
        }
        foreach ($chart_data[4]["planet_allow_degree"] as $keigs=>$jgsh){
            if(in_array($jgsh['sign_id'],[0,3])){
                $yue_suo_xian=false;
            }
        }

        if(in_array($chart_data[1]['sign']['sign_id'], [1,9,5,2,6,10]) and !in_array($chart_data[4]['sign']['sign_id'], [0,4,8]) and $yue_suo_xian){
            $data['control_score']='过高';
        }else if((($sign_score[0]>2 or $sign_score[5]>2 or $sign_score[8]>2) and in_array($chart_data[1]['sign']['sign_id'], [3,11])) or ($yue_ju_xian and in_array($chart_data[1]['sign']['sign_id'], [0,4,8]))){
            $data['control_score']='偏低';
        }else{
            $data['control_score']='正常';
        }


        //tolerance
        if(!in_array($chart_data[1]['sign']['sign_id'], [3,11]) and $sign_score[9]>2 ){
            $data['tolerance_score']='过高';
        }else if(($sign_score[3]>2 or $sign_score[11]>2) and $sign_score[9]<2 and $chart_data[1]['sign']['sign_id']!=9){
            $data['tolerance_score']='偏低';
        }else{
            $data['tolerance_score']='正常';
        }

        $corpusRetrogradePalaceWhere['status'] = 1;
        $corpusRetrogradePalaceWhere['attribution_str'] = '能量报告pdf';



        $corpusRetrogradePalace = $this->logicCorpus->getCorpusColumn($corpusRetrogradePalaceWhere, 'id,title,attribution,attribution_str,content1,content2');




        arsort($sign_score);

        $attribute_gu_english = ['change',  'fixed',  'standard'];

        $attribute_si_english = [ 'soil',  'water', 'fire','wind'];



        foreach ($attribute_score as $keysf=>$ksg){
            if(in_array($keysf,$attribute_si_english)){
                $attribute_si_array[$keysf]=$ksg;
            }else{
                $attribute_gu_array[$keysf]=$ksg;
            }
        }
        arsort($attribute_si_array);
        arsort($attribute_gu_array);
        arsort($attribute_gu_array);

        $attribute_si_max_str=$attribute_chinese[array_search(array_keys($attribute_si_array)[0],$attribute_english)];

        $attribute_si_min_str=$attribute_chinese[array_search(array_keys($attribute_si_array)[3],$attribute_english)];

        $attribute_gu_max_str=$attribute_chinese[array_search(array_keys($attribute_gu_array)[0],$attribute_english)];

        $attribute_gu_min_str=$attribute_chinese[array_search(array_keys($attribute_gu_array)[2],$attribute_english)];


        $sign_max_str=$attribute_chinese[array_search(array_keys($sign_score)[0],$attribute_english)];
        $sign_min_str=$attribute_chinese[array_search(array_keys($sign_score)[11],$attribute_english)];

        $data['character_str']='';
        $data['drive_str']='';
        $data['impact_str']='';
        $data['stability_str']=array();
        $data['warning_str']=array();
        $data['improvement_str']='';
        $data['repair_str']='';

        //"peace_score": "正常",  4、平和度
        //"unstable_score": "偏低",  5、自信度
        //"control_score": "正常",  6、情绪控制度
        //"tolerance_score": "正常",  7、抗压度

        $zhengchang_yue["peace_score"]["wind"]="在平和度方面需要及时察觉的问题是，您过于注重维持关系的融洽，喜欢以柔和平稳的形象充当陪伴者角色，不会对他人有太多的情绪索取，即便在闹得不愉快的情况下，也依旧想保持与对方关系的平衡。但往往这样的平和度，也造就了您在内心疏离、习惯性审视的一面。您会倾向于考虑自我的投入产出比，并过分计较双方的付出，如果遇到挫折，或是较为严峻的考验，您很容易不够坚定，甚至因犹豫不决而产生放弃想法。这样的情况也将导致您在他人心目中的形象不够真诚，降低信任感。";
        $zhengchang_yue["peace_score"]["fire"]="在平和度方面需要及时察觉的问题是，当您遇到不顺遂的事情，您往往伴随着强烈的情绪反应，如愤怒和恨意，这些负面情绪不仅可能影响人际关系，更可能通过精神及身体的联系，表现为躯体疼痛、心脏不适等生理症状。同样，强烈的情绪反应也可能影响个体的睡眠质量，导致睡眠障碍。";
        $zhengchang_yue["peace_score"]["water"]="在平和度方面需要及时察觉的问题是，由于跟他人沟通状况的多变因素，您容易发展为同时包含平和度过高和过低的问题。具体表现在短时间内经历压抑和冲突的切换，这不仅对心理健康构成压力，也可能引发各种身心疾病。因此，保持适度的平和度，同时学习有效的应对压力和冲突的方式，对于身心健康至关重要。";
        $zhengchang_yue["peace_score"]["soil"]="在平和度方面需要及时察觉的问题是，由于在面临冲突情景时，您更倾向于隐忍或退让，过分压抑自我，长期下来可能导致身心疲惫，出现诸如胸闷气短、偏头痛等疼痛症状。同时，因为压抑过多的情绪，个体的睡眠质量可能会受到影响，甚至可能出现噩梦等睡眠障碍，如果您的梦境中充满戾气和暴力，往往反映出其在现实中无法释放的压力和冲突。此外，压力和情绪的压抑还可能影响内分泌，导致激素失调，产生各种心理和生理问题。";
        $zhengchang_yue["unstable_score"]["wind"]="在自信度方面需要及时察觉的问题是，随着不同的人生阶段起伏，您可能会在自信过度和自卑之间反复摇摆，在取得极大成功时，您会容易有膨胀的快感并乐在其中。但生活路径并非持续上升的斜线，更是高低交杂的曲线进程，在遇到低谷时，您的自信度则很容易跌落至现有水平之下，这种状态下，您可能经常感到情绪波动大，容易产生焦虑、抑郁等情绪问题。同时，这种自信度的不稳定可能也会影响您的睡眠质量，导致疼痛、内分泌问题的发生。";
        $zhengchang_yue["unstable_score"]["fire"]="在自信度方面需要及时察觉的问题是，在事情未达到圆满落地前，会容易对周遭事物有过分乐观的预判。在这种情况下，您将无法敏锐捕捉身边人的感受和想法。甚至会有独断专行的倾向，只愿意听信跟自己思维相近的意见，但很多情况下真相往往是忠言逆耳的，所以您会容易误信口蜜腹剑的言论，也容易遭小人算计，被捧杀却不自知，在事业发展的进程中看不到潜在的风险。";
        $zhengchang_yue["unstable_score"]["water"]="在自信度方面需要及时察觉的问题是，您的基本内在需求总是渴望有一种归属感，来让自己信任和安心。因为您的自信度往往对周遭环境的人有很强的依赖感。所以非常需要他人的鼓励。一旦遇到对方直言不讳的批评与指点，那么您的自信度数值很容易产生大幅下降，会过分考虑他人的感受。毕竟自信度更考验一个人逻辑自洽的复原能力，如果很容易接收到外界带来的负面影响。尤其这种影响也会渗透在自己的人际关系网络中。那么进一步您会导致自己情绪低落，不开朗，产生抑郁或情感双相障碍的隐患。";
        $zhengchang_yue["unstable_score"]["soil"]="在自信度方面需要及时察觉的问题是，太容易倚仗于成绩而影响自身情绪。当您事业不可避免地进入到瓶颈阶段，或是取得较大成就后，难以再创辉煌。在这个时期，您往往会对自己容易产生过度的自我批评和负面判断。这种负面的自我评价和持续的压力可能会导致各种心理问题，如抑郁、焦虑等。同时，您的睡眠质量可能因此受到影响，这可能进一步加重身心的疼痛，造成内分泌失衡，甚至影响心脏健康。您的生命活力和积极性可能也因此大打折扣。";
        $zhengchang_yue["control_score"]["wind"]="在情绪控制度方面需要及时察觉的问题是，当您的情绪控制度不稳定时，那么您会容易在两种极端情况的拉扯下反复横跳：有的时候，您会因为过分理性，间接影响了您与他人的深度链接，导致对方会认为您较缺乏共情能力，因此妨碍了情感的流动。有的时候，您又会情绪波动频繁，导致免疫力低下，从而影响内分泌系统的正常运作，以及较容易出现严重的睡眠障碍。";
        $zhengchang_yue["control_score"]["fire"]="的情绪控制度需要得到进一步的管控、提升。虽然在普遍情况下，您能够很好地有效控制自我，但遇到突发状况时，您很容易情绪波动频繁，这种急躁可能会对内分泌系统产生负面影响，包括但不限于激素水平的不稳定，这可能会引起各种健康问题，如体重波动、疲劳、抑郁等。同时，频繁的情绪波动可能使免疫系统承受压力，导致免疫力下降，使您更容易受到疾病的侵袭。此外，您还可能引发睡眠问题，因为过度的焦虑和紧张会使入睡和保持睡眠变得困难，长期下来可能会导致睡眠质量下降，进一步影响身心健康。";
        $zhengchang_yue["control_score"]["water"]="在情绪控制度方面需要及时察觉的问题是，，容易引发出对外界事物过度敏感的反应模式，虽然您能够了解到别人情绪的波动，也能及时察觉别人的需求，但面对不同的情景，您的情绪控制度容易出现不稳定的状态，比如在面对负面事件时，倾向于多愁善感，过于沉浸在忧郁和消极预设的内耗中。对于事情总是抱有悲观的想法，尤其是对于两性关系，很难抱有超然的态度。长此以往，很容易滋生出心脏、肝、脾胃的病症。";
        $zhengchang_yue["control_score"]["soil"]="非常擅长管理自己的情绪控制度。在得体、周全的表象下，也意味着您习惯于隐忍和妥协。长此以往，在过度抑制自我情绪的过程中，您可能会缩减自己的情感表达。这种状态可能导致生理上的疼痛问题，这种疼痛是指由于长期的压力和紧张等造成的身体反应。此外，您的情绪控制度可能导致您失去与他人建立亲密关系的能力，人与人之间的情感连接程度降低。这样的情况可能导致孤独感和社交障碍，进一步可能引发心理健康问题，如焦虑和抑郁等。";
        $zhengchang_yue["tolerance_score"]["wind"]="在抗压度方面需要及时察觉的问题是，您通常对压力过于敏感。本身您的身体觉察和感受能力较强。一旦面临压力，您可能很快就会出现身心反应，如睡眠质量下降、内分泌紊乱等。持续的压力感受可能导致您的心理疲劳，心理疲劳又可能进一步影响身体健康，从而形成一个恶性循环。长期处于这种状态，可能导致各种身心疾病的发生，比如疼痛、消化系统疾病、心血管疾病等。而您的情绪也可能变得较为不稳定，容易出现焦虑、抑郁等情绪问题。";
        $zhengchang_yue["tolerance_score"]["fire"]="在抗压度方面需要及时察觉的问题是，虽然在面对难关时您可以全身心投入在解决方案的规划中，也不容易被外界干预，内心认定的事很难被撼动，甚至面对突发的压力与困难也可以做到习以为常，更善于突破与反击。但内在的损耗，会让您容易急躁、胡思乱想，缺乏耐心和长线思维的培养，在这种情况下，您会容易我行我素不顾及他人感受，同时会引发肝火过剩，头晕胀痛，甚至有脑出血等严重生理病症的极高概率。";
        $zhengchang_yue["tolerance_score"]["water"]="在抗压度方面需要及时察觉的问题是，您很容易在两种不同的极端情况下反复拉扯。有的时候您会有很强的抗压性。喜欢激烈的竞争，追求升迁的快感，甚至驱从一些不容易实现的目标，喜欢挑战。但总是缺乏耐心，做事情不愿意拖拖拉拉，更不喜欢拖延时间，在放松时总有一种负罪感。这样的情况反而会让您对身体觉知度不敏感，容易在后背、肩颈产生较大的疼痛问题。除此之外，您也容易过分求胜，走向另一种情况：变得对压力极为敏感，易疲劳、睡眠质量差，以及感觉自己不被理解，容易出现情绪化反应，影响工作和人际关系。";
        $zhengchang_yue["tolerance_score"]["soil"]="在抗压度方面需要及时察觉的问题是，您往往对身体的觉知度不敏感，可能忽视了许多身体的警示信号。您常常在背部、肩部和颈部疼痛达到较严重的程度时，才会意识到自己的身体状况出了问题。在长期的高压状态下，您可能会出现各种各样的身心问题。不仅仅是疼痛问题，可能还会出现内分泌紊乱、消化系统问题，以及心血管疾病等。而在精神层面，您可能会经历持续的精神压力，长期处于“应激反应”状态，容易导致焦虑、抑郁等情绪问题。此外，过高的抗压度也可能影响到您的睡眠质量，不良的睡眠再进一步加剧身心问题。";



        foreach ($corpusRetrogradePalace as $keyc => $volec) {
            $volec['content1']=str_replace("\n", '<br/><br/>', $volec['content1']);

           if($volec['title']==$attribute_si_max_str.'月亮'.$signChinese[$chart_data[1]['sign']['sign_id']].'情绪性格'){
               $data['character_str']=$volec['content1'];
           }
           if($volec['title']==$attribute_gu_max_str.'月亮'.$signChinese[$chart_data[1]['sign']['sign_id']].'情绪自驱力'){
               $data['drive_str']=$volec['content1'];
           }
           if($volec['title']==$attribute_si_max_str.'月亮'.$signChinese[$chart_data[1]['sign']['sign_id']].'沟通的影响'){
               $data['impact_str']=$volec['content1'];
           }
            if($volec['title']=='平和度'.$data['peace_score'].'情绪稳定性'){
                $data['stability_str'][0]=$volec['content1'];
            }
            if($volec['title']=='自信度'.$data['unstable_score'].'情绪稳定性'){
                $data['stability_str'][1]=$volec['content1'];
            }
            if($volec['title']=='情绪控制度'.$data['control_score'].'情绪稳定性'){
                $data['stability_str'][2]=$volec['content1'];
            }
            if($volec['title']=='抗压度'.$data['tolerance_score'].'情绪稳定性'){
                $data['stability_str'][3]=$volec['content1'];
            }


            if($volec['title']=='平和度'.$data['peace_score'].'身心问题预警'){
                //$zhengchang_yue["peace_score"][]
                //$data['warning_str'][0]=$volec['content1'];
                if($data['peace_score']=='正常'){
                    $data['warning_str'][0]=$zhengchang_yue["peace_score"][$sign_phase[$chart_data[1]['sign']['sign_id']][0]];
                }else{
                    $data['warning_str'][0]=$volec['content1'];
                }

            }
            if($volec['title']=='自信度'.$data['unstable_score'].'身心问题预警'){
                //$data['warning_str'][1]=$volec['content1'];
                if($data['unstable_score']=='正常'){
                    $data['warning_str'][1]=$zhengchang_yue["unstable_score"][$sign_phase[$chart_data[1]['sign']['sign_id']][0]];
                }else{
                    $data['warning_str'][1]=$volec['content1'];
                }


            }
            if($volec['title']=='情绪控制度'.$data['control_score'].'身心问题预警'){
                //$data['warning_str'][2]=$volec['content1'];
                if($data['control_score']=='正常'){
                    $data['warning_str'][2]=$zhengchang_yue["control_score"][$sign_phase[$chart_data[1]['sign']['sign_id']][0]];
                }else{
                    $data['warning_str'][2]=$volec['content1'];
                }



            }
            if($volec['title']=='抗压度'.$data['tolerance_score'].'身心问题预警'){
                //$data['warning_str'][3]=$volec['content1'];
                if($data['tolerance_score']=='正常'){
                    $data['warning_str'][3]=$zhengchang_yue["tolerance_score"][$sign_phase[$chart_data[1]['sign']['sign_id']][0]];
                }else{
                    $data['warning_str'][3]=$volec['content1'];
                }


            }

            if($volec['title']==$attribute_si_max_str.$data['control_score'].'能力改善'){
                $data['improvement_str']=$volec['content1'];
            }

            if($volec['title']==$attribute_gu_max_str.'月亮'.$signChinese[$chart_data[1]['sign']['sign_id']].'能力修复'){
                $data['repair_str']=$volec['content1'];
            }
        }
        if(!empty($activityAstrolabeDecodeEnergyInfo['energy_url'])){
            $energy_url=explode(",",$activityAstrolabeDecodeEnergyInfo['energy_url']);
            $imgs_url_array=$this->logicFile->getPictureColumnUrl($energy_url);
            $data['imgs_url']=array();
            foreach ($energy_url as $keysdf=>$sdsf){
                if(!empty($imgs_url_array[$sdsf])){
                    $data['imgs_url'][]= $imgs_url_array[$sdsf];
                }

            }
           // $data['imgs_url']= array_values($data['imgs_url']);
        }

        //"peace_score": "正常",  4、平和度
        //"unstable_score": "偏低",  5、自信度
        //"control_score": "正常",  6、情绪控制度
        //"tolerance_score": "正常",  7、抗压度

       // $this->logicAstrolabeDecodeTeenager->astrolabeDecodeTeenagerEdit($pdf_data);

        $data['improvement_array']=explode("<br/><br/>",$data['improvement_str']);
        return $this->apiReturn($data);
    }

    public function  getAgeByBirth($date,$type = 1){
        $nowYear = date("Y",time());
        $nowMonth = date("m",time());
        $nowDay = date("d",time());
        $birthYear = date("Y",$date);
        $birthMonth = date("m",$date);
        $birthDay = date("d",$date);
        if($type == 1){
            $age = $nowYear - ($birthYear - 1);
        }
        if($type==2){
            if($nowMonth<$birthMonth){
                $age = $nowYear - $birthYear - 1;
            }elseif($nowMonth==$birthMonth){
                if($nowDay<$birthDay){
                    $age = $nowYear - $birthYear - 1;
                }else{
                    $age = $nowYear - $birthYear;
                }
            }else{
                $age = $nowYear - $birthYear;
            }
        }
        return $age;
    }

    public function planetPhase($exSweTest, &$starsDtat = [], $allow_degree = [], $planet_degree = [])
    {
        $allow_degree_cn = [
            '0' => '合',
            '30' => '十二分',
            '36' => '十分',
            '45' => '八分',
            '60' => '六合',
            '72' => '五分',
            '90' => '刑',
            '120' => '拱',
            '135' => '补八分',
            '144' => '补五分',
            '150' => '梅花',
            '180' => '冲'
        ];
        if (empty($planet_degree)) {
            $planet_degree['0'] = 1;
            $planet_degree['1'] = 1;
        }

        $corpus_where = array();
        $house = $starsDtat['house'];
        $planet = $starsDtat['planet'];
        $planet_twe = $planet;
        $planetFont = planetName::$planetFont;
        foreach ($planet_twe as $key => &$value) {

            $value['sign'] = $exSweTest->Convert_sign_Longitude($value["longitude"]);
            $value['planet_font'] = $planetFont[$value['planet_english']];
            foreach ($house as $keyh => $valueh) {

                $house_cha = abs($valueh['longitude'] - $value['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }

                if ($keyh < 11) {

                    $last_house = $house[$keyh + 1];
                } else {

                    $last_house = $house[0];
                }
                $first_house = $value;

                if ($valueh['longitude'] > $last_house['longitude']) {

                    if ($first_house['longitude'] < $last_house['longitude']) {
                        $first_house['longitude'] += 360;
                    }

                    $last_house['longitude'] += 360;
                }

                if ($first_house['longitude'] >= $valueh['longitude'] and $first_house['longitude'] < $last_house['longitude']) {
                    $house_array_angle = $exSweTest->Convert_deg_min($house_cha);
                    $value['house_id'] = $keyh + 1;
                    $value['house_longitude'] = $house_cha;
                    $value['house_deg'] = $house_array_angle['deg'];
                    $value['house_min'] = $house_array_angle['min'];
                    $value['house_sec'] = $house_array_angle['sec'];
                }
            }

            if (empty($value['planet_allow_degree'])) {
                $value['planet_allow_degree'] = array();
            }

            foreach ($planet as $keyg => $valueg) {

                if ($key == $keyg) {
                    continue;
                }
                $chazhi = abs($value['longitude'] - $valueg['longitude']);
                $chazhi_xian = $chazhi;
                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                $planet_degree_lgit = 0;

                !empty($planet_degree[$value['code_name']]) && $planet_degree_lgit += $planet_degree[$value['code_name']];

                !empty($planet_degree[$valueg['code_name']]) && $planet_degree_lgit += $planet_degree[$valueg['code_name']];

                $in_out_xu_long = abs($value['longitude'] - $valueg['longitude']);

                //如果第二星大于第一星值，相减绝对值小于180 那么就是第一追第二
                if ($value['longitude'] < $valueg['longitude']) {
                    if ($in_out_xu_long < 180) {
                        $a_out_info = $value;
                        $b_out_info = $valueg;
                    } else {
                        $a_out_info = $valueg;
                        $b_out_info = $value;
                    }
                } else {
                    if ($in_out_xu_long < 180) {
                        $a_out_info = $valueg;
                        $b_out_info = $value;
                    } else {
                        $a_out_info = $value;
                        $b_out_info = $valueg;
                    }
                }
                $value['planet_allow_degree']=array();
                foreach ($allow_degree as $keyAd => $valueAd) {
                    $valueAd += $planet_degree_lgit;
                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {
                        $in_out = '-1';
                        $in_out_start = $a_out_info['longitude'] + $keyAd - $valueAd;

                        if ($in_out_start > 360) {
                            $in_out_start = $in_out_start - 360;
                        } elseif ($in_out_start < 0) {
                            $in_out_start = $in_out_start + 360;
                        }
                        $in_out_middle = $in_out_start + $valueAd;
                        if ($in_out_middle > 360) {
                            $in_out_middle = $in_out_middle - 360;
                        } elseif ($in_out_middle < 0) {
                            $in_out_middle = $in_out_middle + 360;
                        }
                        $in_out_end = $in_out_middle + $valueAd;
                        if ($in_out_end > 360) {
                            $in_out_end = $in_out_end - 360;
                        } elseif ($in_out_end < 0) {
                            $in_out_end = $in_out_end + 360;
                        }

                        if ($b_out_info['longitude'] < $in_out_middle) {
                            $location_zuo_you = 'nei';
                            if ($in_out_end < $in_out_middle and $b_out_info['longitude'] < $in_out_end) {
                                $location_zuo_you = 'wai';
                            }
                        } else {
                            $location_zuo_you = 'wai';
                            if ($in_out_start > $in_out_middle and $b_out_info['longitude'] > $in_out_end) {
                                $location_zuo_you = 'nei';
                            }
                        }

                        if ($b_out_info['speed'] > $a_out_info['speed']) {
                            if ($location_zuo_you == 'nei') {
                                $in_out = '1';
                            }
                        } else {
                            if ($location_zuo_you == 'wai') {
                                $in_out = '1';
                            }
                        }

                        $planet_allow_degree = [
                            'planet_number' => $keyg,
                            'code_name' => $valueg['code_name'],
                            'planet_chinese' => $valueg['planet_chinese'],
                            'current_longitude' => $valueg['longitude'],
                            'allow' => $keyAd,
                            'allow_cn' => $allow_degree_cn[$keyAd],
                            'in_out' => $in_out
                        ];

                        $planet_allow_degree = array_merge($planet_allow_degree, $exSweTest->Convert_Longitude(abs(round(($chazhi - $keyAd), 4))));
                        $value['planet_allow_degree'][] = $planet_allow_degree;

                    }
                }
            }
            $value['longitude'] = $exSweTest->crunch($value['longitude']);
        }
        $starsDtat['planet'] = $planet_twe;
    }

    public function career($planet_score)
    {

        //艺术天赋  artistic
        //运动天赋  movement
        //美貌天赋  beautiful
        //商业天赋  business
        //心灵天赋  mind
        //艺术天赋  writing

        $tits['artistic'] = '艺术';
        $tits['movement'] = '运动竞争';
        $tits['beautiful'] = '吸引力';
        $tits['business'] = '商业财富';
        $tits['mind'] = '心灵疗愈';
        $tits['writing'] = '心灵手巧';

        // $business_energy_array = explode("\r\n**\r\n", file_get_contents('./corpus_txt/astrolabe_decode/business_energy.txt'));  //注意事项


        $score['artistic'] = intval($planet_score["venus"]);
        $score['movement'] = intval(($planet_score["mars"] + $planet_score["mars"]) / 2);
        $score['beautiful'] = intval(($planet_score["venus"] + $planet_score["mars"]) / 2);
        $score['business'] = intval(($planet_score["mercury"] + $planet_score["saturn"] + $planet_score["mars"]) / 3);
        $score['mind'] = intval(($planet_score["moon"] + $planet_score["jupiter"]) / 2);
        $score['writing'] = intval(($planet_score["mercury"] + $planet_score["saturn"]) / 2);


        arsort($score);

        $score[key($score)]++;
        return $score;
    }

    public function talent($dataObjectiveEnergy, $stars)
    {

        $plant = array('sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn');
        $pnames_cn = array("太阳", "月亮", "水星", "金星", "火星", "木星", "土星");


        arsort($dataObjectiveEnergy);

        $energy_array_keys = array_keys($dataObjectiveEnergy);

        $talent_key_weakest = $energy_array_keys[6];

        if ($stars == 'all') {

            $talent_select_weakest = ['name' => $talent_key_weakest, 'ltype' => 'B'];

            if ($dataObjectiveEnergy[$energy_array_keys[0]] >= 8) {
                if ($dataObjectiveEnergy[$energy_array_keys[1]] >= 8) {
                    $talent_select_max = ['name' => $energy_array_keys[0] . '_' . $energy_array_keys[1], 'ltype' => 'A', 'stype' => 'M2'];
                } else {
                    $talent_select_max = ['name' => $energy_array_keys[0], 'ltype' => 'A', 'stype' => 'M1'];
                }
            } else {
                $talent_select_max = ['name' => $energy_array_keys[0] . '_' . $energy_array_keys[1], 'ltype' => 'A', 'stype' => 'M3'];
            }

            $talent_info_max = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($talent_select_max)->toArray();

            $talent_info_max_name = explode('_', $talent_select_max['name']);

            if (count($talent_info_max_name) > 1) {
                $talent_info_max['title'] = '最强天赋：' . $pnames_cn[array_search($talent_info_max_name[0], $plant)] . "能量&" . $pnames_cn[array_search($talent_info_max_name[1], $plant)] . '能量';
            } else {
                $talent_info_max['title'] = '最强天赋：' . $pnames_cn[array_search($talent_info_max_name[0], $plant)] . "能量";
            }

            $talent_info_list[] = $talent_info_max;

            $talent_info_weakest = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($talent_select_weakest)->toArray();

            $talent_info_weakest_name = explode('_', $talent_select_weakest['name']);

            if (count($talent_info_weakest_name) > 1) {
                $talent_info_weakest['title'] = '最弱天赋：' . $pnames_cn[array_search($talent_info_weakest_name[0], $plant)] . "能量&" . $pnames_cn[array_search($talent_info_weakest_name[1], $plant)] . '能量';
            } else {
                $talent_info_weakest['title'] = '最弱天赋：' . $pnames_cn[array_search($talent_info_weakest_name[0], $plant)] . "能量";
            }
            $talent_info_weakest['content'] .= $talent_info_weakest['content2'];
            $talent_info_list[] = $talent_info_weakest;
            return $talent_info_list;
        } else {
            $vole = $dataObjectiveEnergy[$stars];
            if ($vole >= 8) {
                $valde = ['name' => $stars . '_' . $talent_key_weakest, 'ltype' => 'C', 'stype' => 'MAX'];
            } else if ($vole >= 0 && $vole < 8) {
                $valde = ['name' => $stars, 'ltype' => 'C', 'stype' => 'MID'];
            } else if ($vole < 0) {
                $valde = ['name' => $stars, 'ltype' => 'C', 'stype' => 'MC'];
            }

            $valde_name = explode('_', $valde['name']);

            $single_select_valde = $this->logicPlanetaryInterpretation->getPlanetaryInterpretationInfo($valde)->toArray();

            $single_select_valde['sore'] = $dataObjectiveEnergy[$valde_name[0]];
            $single_select_valde['name_c'] = $valde_name[0];
            $single_select_valde['title'] = $pnames_cn[array_search($valde_name[0], $plant)] . "(" . ucwords($valde_name[0]) . ")";

            $single_select_valde['title_vice'] = '您的' . $pnames_cn[array_search($valde_name[0], $plant)] . '天赋能量指数 ： <span class="title_score">' . floatval($dataObjectiveEnergy[$valde_name[0]]) . '</span>';

            if ($single_select_valde['stype'] == 'MAX' && $single_select_valde['ltype'] == 'C') {
                $single_select_valde['content'] = str_replace("REPLACE", $single_select_valde['content2'], $single_select_valde['content']);
            }

            return $single_select_valde;
        }
    }

    /**
     * 获取语料数据
     */
    public function getcorpusRetrogradePalace($corpusRetrogradePalace, $where = [], $duo = true)
    {
        $data_array = '';
        foreach ($corpusRetrogradePalace as $keyc => $volec) {

            if ($where['title'] == $volec['title'] and $where['content1'] == $volec['content1']) {

                $data_array .= $volec['content2'] . chr(10);

                if ($duo) {
                    break;
                }
            }
        }
        return $data_array;
    }



    //天赋报告计算

    /**
     * 能量报告pdf修改
     */
    public function astrolabeDecodeEnergyPdfEdit()
    {

        $where['order_number'] = $this->param_data['order_number'];

        $regit = $this->logicAstrolabeDecodeEnergy->getAstrolabeDecodeEnergyInfo($where);

        if (empty($regit)) {
            return $this->apiReturn([API_CODE_NAME => 1010007, API_MSG_NAME => '您没有该订单']);
        }
        $data['id']=$regit['id'];
        !empty($this->param_data['energy_ur']) && $data['energy_url'] = $this->param_data['energy_ur'];

        if(!empty($this->param_data['mail'])){
            $data['mail'] = $this->param_data['mail'];
            $data['status'] = 1;
        }
        $this->logicAstrolabeDecodeEnergy->astrolabeDecodeEnergyWuEdit($data);

        if(!empty($this->param_data['mail'])){

             // 添加到 crontab
           // $result = exec('crontab -l');
          //  $cronjob = "*/1 * * * * /usr/bin/curl http://adminxg.robustcn.com/index/Timing/subscribeSend";
          //  $result .= "$cronjob\n";
           // file_put_contents('/tmp/crontab.txt', $result);
         //   exec('crontab /tmp/crontab.txt');

        }

        return $this->apiReturn(['id' => 1]);

    }

    //事业告计算

    /**
     * 能量报告pdf删除
     */
    public function astrolabeDecodeEnergyDel()
    {

        $where['id'] = $this->param_data['id'];

        $regit = $this->logicAstrolabeDecodeEnergy->astrolabeDecodeEnergyDel($where);

        $this->logicAstrolabeDecodeEnergyOrder->astrolabeDecodeEnergyOrderDel(['a_id' => $where['id']]);

        return $this->apiReturn(['id' => $regit]);

    }


    //兑换码

    function get_txt_data($matters_attention_array, $title)
    {
        foreach ($matters_attention_array as $keys => $vales) {
            if (trim($vales) == $title) {
                return $matters_attention_array[$keys + 1];
            }

        }
    }

    public function conversionCode()
    {

    }

}
