<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\xykj\logic;
use app\common\logic\LogicBase;
/**
 * 运势分值逻辑
 */
class DayLuck extends LogicBase
{


    public function basicData($life_conditions = [], $new_conditions = [])
    {

        $exSweTest = get_sington_object('SweTest', "astrology\\SweTestEmpty");
        $life_data = $exSweTest->calculate($life_conditions, ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'm', 'virtual' => ['10', '11']]);

        $new_data = $exSweTest->calculate($new_conditions, ['0', '1', '2', '3', '4', '5', '6']);

        $new_time=strtotime($new_conditions['b']);

        $new_conditions_s = $new_conditions;
        $new_conditions_s['b'] = date('d.m.Y', strtotime($new_conditions_s['b'] . "  -20 day"));
        $new_conditions_s['n'] = 40;
        $new_conditions_s['s'] = '1440m';
        $new_conditions_s['f'] = 'pPTlsj';
        unset($new_conditions_s['house']);


        $in_out_time_list = $exSweTest->universalPhaseProgressed($new_conditions_s,86400*20);

        if (empty($allow_degree)) {
            $allow_degree['0'] = 6;
            $allow_degree['45'] = 7;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['120'] = 5;
            $allow_degree['150'] = 5;
            $allow_degree['180'] = 5;
        }

        if (empty($planet_degree)) {
            $planet_degree['0'] = 0.5;
            $planet_degree['1'] = 0.5;
        }

        $planet = $this->planetSecondPhase($exSweTest, $life_data, $new_data, $allow_degree, $planet_degree, $in_out_time_list);


        $liuyue_ben=$planet["planet_second"][1];



        $data = $this->calculateScore($planet, $planet['house'], '月亮');

        $corpus_cre = $this->calculateCorpusList($planet);
        $SignNamesSimple = config('ext_astrology')['SignNamesSimple'];
        $PlanetNamesSimple = config('ext_astrology')['PlanetNamesSimple'];
        $PlanetNamesSimple = config('ext_astrology')['PlanetNamesSimple'];

//        $data['life_title'] = [
//            ['planet_id' => 0, 'planet_chinese' => $PlanetNamesSimple[0], 'sign_id' => $life_data['planet'][0]['sign']['sign_id'], 'sign_chinese' => $life_data['planet'][0]['sign']['sign_chinese']],
//            ['planet_id' => 1, 'planet_chinese' =>  $PlanetNamesSimple[1], 'sign_id' => $life_data['planet'][1]['sign']['sign_id'], 'sign_chinese' =>$life_data['planet'][1]['sign']['sign_chinese']],
//            ['planet_id' => 10, 'planet_chinese' =>  $PlanetNamesSimple[10], 'sign_id' => $life_data['planet'][10]['sign']['sign_id'], 'sign_chinese' => $life_data['ascmcs'][0]['sign']['sign_chinese']],
//        ];

        $data['corpus'] = array();
        $nengliang = 99999;
        $CorpusConstellation_miaoshu=array();
        //相位

        foreach ($corpus_cre as $key => $value) {


            if( ($value['end_day'] + $value['start_day'])>20){
                continue;
            }

            $xingluogong['title'] = $value['planet_second_chinese'] . $value['allow'] . $value['planet_one_chinese'];
            $xingluogong['attribution'] = "xingyu";
            $xingluogong['chartType'] = "5";
            $xingluogong['attribution_str'] = "流年相位";

            $CorpusConstellation = $this->logicCorpusUser->getCorpusUserInfo($xingluogong);
            if (!empty($CorpusConstellation)) {
                if ($value['start_day'] < 1 and $value['end_day'] < 1) {
                    $phase_situation = '今天开始今天结束';
                } else if ($value['start_day'] == 0) {
                    $phase_situation = '今天开始，' . intval($value['end_day']) . '天后结束';
                } else if ($value['end_day'] == 0) {
                    $phase_situation = '已经开始' .  intval($value['start_day']) . '天，今天结束';
                } else {
                    $phase_situation = '已经开始' .  intval($value['start_day']) . '天，' . intval($value['end_day']) . '天后结束';
                }

                $title_key=$xingluogong['title'];
                //通过||拆分$content1_key为数组，求出数组的个数，再用过当前多少号除以这个总数求余数是个title
                $content1_title_arr=explode('||',$CorpusConstellation['content1']);
                $content1_title_num=date('d',$new_time)%count($content1_title_arr);

                //建议的数值
                $content4_jian_arr=explode('||',$CorpusConstellation['content5']);
                $content4_jian_num1=date('d',$new_time)%count($content4_jian_arr);
                //我需要$content4_jian_num2时$content4_jian_num1+1的下标，但是需要注意超过总个数就是到第一个
                $content4_jian_num2=$content4_jian_num1+1;
                $content4_jian_num2=$content4_jian_num2>=count($content4_jian_arr)?0:$content4_jian_num2;

                //避免的数值
                $content4_bimian_arr=explode('||',$CorpusConstellation['content4']);
                $content4_bimian_num1=date('d',$new_time)%count($content4_bimian_arr);
                //我需要$content4_jian_num2时$content4_jian_num1+1的下标，但是需要注意超过总个数就是到第一个
                $content4_bimian_num2=$content4_bimian_num1+1;
                $content4_bimian_num2=$content4_bimian_num2>=count($content4_bimian_arr)?0:$content4_bimian_num2;


                $data['corpus'][]  = ['title' => $content1_title_arr[$content1_title_num],
                    'phase_str' => '行运' . $value['planet_second_chinese'] . $value['allow_cn'] . '本命' . $value['planet_one_chinese'],
                    'stat_date' => $value['start_day'],
                    'end_date' => $value['end_day'],
                    'code_second' => $value['code_second'],
                    'allow' => $value['allow'],
                    'code_one' => $value['code_one'],
                    'difference' => abs($value['difference'] - $value['allow']),
                    'phase_situation' => $phase_situation,
                    'content' => html_entity_decode($CorpusConstellation['content3']),
                    "label"=> $CorpusConstellation['content7']];


                $data_avoid_suggestion['avoid'] = [$content4_bimian_arr[$content4_bimian_num1],$content4_bimian_arr[$content4_bimian_num2]];
                $data_avoid_suggestion['suggestion'] = [$content4_jian_arr[$content4_jian_num1],$content4_jian_arr[$content4_jian_num2]];
                $data_avoid_suggestion['describe'] = html_entity_decode($CorpusConstellation['content2']);
                $data_avoid_suggestion['title_key'] = $title_key;

                if($value['planet_second_chinese']=='月亮'){
                    $CorpusConstellation_miaoshu[0]=$data_avoid_suggestion;
                }elseif ($value['planet_second_chinese']=='太阳'){
                    $CorpusConstellation_miaoshu[1]=$data_avoid_suggestion;
                }elseif ($value['planet_second_chinese']=='水星'){
                    $CorpusConstellation_miaoshu[2]=$data_avoid_suggestion;
                }elseif ($value['planet_second_chinese']=='金星'){
                    $CorpusConstellation_miaoshu[3]=$data_avoid_suggestion;
                }elseif ($value['planet_second_chinese']=='火星'){
                    $CorpusConstellation_miaoshu[4]=$data_avoid_suggestion;
                }elseif ($value['planet_second_chinese']=='土星'){
                    $CorpusConstellation_miaoshu[5]=$data_avoid_suggestion;
                }elseif ($value['planet_second_chinese']=='木星'){
                    $CorpusConstellation_miaoshu[6]=$data_avoid_suggestion;
                }elseif ($value['planet_second_chinese']=='天王星'){
                    $CorpusConstellation_miaoshu[7]=$data_avoid_suggestion;
                }

            } else {
                // dump($value);
            }
        }
        ksort($CorpusConstellation_miaoshu);

        $CorpusConstellation_miaoshu=array_values($CorpusConstellation_miaoshu);

        $data['avoid'] =   $CorpusConstellation_miaoshu[0]['avoid'] ;
        $data['suggestion'] = $CorpusConstellation_miaoshu[0]['suggestion'] ;
        $data['describe'] = $CorpusConstellation_miaoshu[0]['describe'] ;
        $data['title_key'] = $CorpusConstellation_miaoshu[0]['title_key'];

        //$title_key=$CorpusConstellation_miaoshu[0]['title'];
//
//
//
//
//        $data['avoid'] = [$content4_bimian_arr[$content4_bimian_num1],$content4_bimian_arr[$content4_bimian_num2]];
//        $data['suggestion'] = [$content4_jian_arr[$content4_jian_num1],$content4_jian_arr[$content4_jian_num2]];
//        $data['describe'] = html_entity_decode($CorpusConstellation['content2']);
//        $data['title_key'] = $title_key;

        $houseChinese = array("一宫", "二宫", "三宫", "四宫", "五宫", "六宫", "七宫", "八宫", "九宫", "十宫", "十一宫", "十二宫");

        //宫位
        foreach ($planet['planet_second'] as $key => $value) {


            if( ($value['end_day'] + $value['start_day'])>20){
                continue;
            }



            $xingluogong['title'] = $value['planet_chinese'] . $value['house_id'];
            $xingluogong['attribution'] = "xykj";
            $xingluogong['chartType'] = "5";
            $xingluogong['chartType'] = "流年落宫";

            $CorpusConstellation = $this->logicCorpusUser->getCorpusUserInfo($xingluogong);

            if (!empty($CorpusConstellation)) {

                if ($value['start_day'] < 1 and $value['end_day'] < 1) {
                    $phase_situation = '今天开始今天结束';
                } else if ($value['start_day'] == 0) {
                    $phase_situation = '今天开始，' . intval($value['end_day']) . '天后结束';
                } else if ($value['end_day'] == 0) {
                    $phase_situation = '已经开始' . intval($value['start_day']) . '天，今天结束';
                } else {
                    $phase_situation = '已经开始' . intval($value['start_day']) . '天，' . intval($value['end_day']) . '天后结束';
                }
                $content1_title_arr=explode('||',$CorpusConstellation['content1']);
                $content1_title_num=date('d',$new_time)%count($content1_title_arr);


                $data['corpus'][] = ['title' => $content1_title_arr[$content1_title_num],
                    'phase_str' => '行运' . $value['planet_chinese'] . '本命' . $value['house_id'] . '宫',
                    'stat_date' => $value['start_day'],
                    'end_date' => $value['end_day'],
                    'house_id' => $value['house_id'],
                    'code_name' => $value['code_name'],
                    'difference' => 13,
                    'phase_situation' => $phase_situation,
                    'content' => html_entity_decode($CorpusConstellation['content3']),
                    "label"=> $CorpusConstellation['content7']];
//                if (empty($data['title']) and $value['code_name'] == 1) {
//
//                    $data['describe'] = html_entity_decode($CorpusConstellation['content2']);
//
//                    $data['title'] = html_entity_decode($CorpusConstellation['content3']);
//                }
            }
        }
        $data['moon_sign']=$liuyue_ben["sign"]["sign_id"];
        return $data;
    }


    /**
     * 计算星座内获得相位
     */
    public function planetSecondPhase($exSweTest, $one_data = [], $second_data = [], $allow_degree = [], $planet_degree = [], $in_out_time_list = [],$new_date=0)
    {
        $house = $one_data['house'];
        $planet = $one_data['planet'];
        $planet_second = $second_data['planet'];


        $allow_degree_cn['0'] = '合';
        $allow_degree_cn['30'] = '十二分';
        $allow_degree_cn['45'] = '八分';
        $allow_degree_cn['60'] = '六合';
        $allow_degree_cn['90'] = '刑';
        $allow_degree_cn['120'] = '拱';
        $allow_degree_cn['150'] = '梅花';
        $allow_degree_cn['180'] = '冲';
        $planet_allow_degrees = array();


        foreach ($planet_second as $keyg => &$valueg) {
            foreach ($house as $keyh => &$valueh) {
                $house_cha = abs($valueh['longitude'] - $valueg['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }
                if ($keyh < 11) {
                    $last_house = $house[$keyh + 1];
                } else {
                    $last_house = $house[0];
                }


                $bu_zhi=0;
                if ($valueh['longitude'] > $last_house['longitude']) {
                    if ($valueg['longitude'] < $last_house['longitude']) {
                        $valueg['longitude'] += 360;
                    }
                    $bu_zhi=360;
                }
                $last_house['longitude'] += $bu_zhi;



                if ($valueg['longitude'] >= $valueh['longitude'] and $valueg['longitude'] < $last_house['longitude']) {
                    $house_array_angle = $exSweTest->Convert_deg_min($house_cha);
                    $valueg['house_id'] = $keyh + 1;
                    $valueg['house_longitude'] = $house_cha;
                    $valueg['house_deg'] = $house_array_angle['deg'];
                    $valueg['house_min'] = $house_array_angle['min'];
                    $valueg['house_sec'] = $house_array_angle['sec'];
                    $valueh['planet_second_list'][] = $valueg['code_name'];
                }


                //
                $in_out_start_time_list = array_reverse($in_out_time_list['start'][$valueg['code_name']]);


                $chazhi_min=9999;
                foreach ($in_out_start_time_list as $key_start => $value_start) {
                    $new_longitude_l = $value_start[3];


                    if ($bu_zhi == 360 and $new_longitude_l < ($last_house['longitude'] - $bu_zhi)) {
                        $new_longitude_l += $bu_zhi;
                    }


                    if ($new_longitude_l >= $valueh['longitude'] and $new_longitude_l < $last_house['longitude'] and $chazhi_min>abs($new_longitude_l-$valueh['longitude'])) {
                        $chazhi_min=abs($new_longitude_l-$valueh['longitude']);
                        $valueg['start_day'] = $key_start;
                    } else {
                        break;
                    }
                }


                $in_out_end_time_list = $in_out_time_list['end'][$valueg['code_name']];

                $chazhi_max=0;
                foreach ($in_out_end_time_list as $key_send => $value_send) {
                    $new_longitude_l = $value_send[3];

                    if ($bu_zhi == 360 and $new_longitude_l < ($last_house['longitude'] - $bu_zhi)) {
                        $new_longitude_l += $bu_zhi;

                    }

                    if ($new_longitude_l >= $valueh['longitude'] and $new_longitude_l < $last_house['longitude']  and $chazhi_max<abs($new_longitude_l-$valueh['longitude'])) {
                        $valueg['end_day'] = $key_send;
                        $chazhi_max=abs($new_longitude_l-$valueh['longitude']);

                    } else {
                        break;
                    }
                }
            }

        }
        foreach ($planet as $key => &$value) {
            foreach ($house as $keyh => &$valueh) {

                $house_cha = abs($valueh['longitude'] - $value['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }

                if ($keyh < 11) {
                    $last_house = $house[$keyh + 1];
                } else {
                    $last_house = $house[0];
                }

                if ($valueh['longitude'] > $last_house['longitude']) {
                    if ($value['longitude'] < $last_house['longitude']) {
                        $value['longitude'] += 360;
                    }
                    $last_house['longitude'] += 360;
                }

                if ($value['longitude'] >= $valueh['longitude'] and $value['longitude'] < $last_house['longitude']) {
                    $house_array_angle = $exSweTest->Convert_deg_min($house_cha);
                    $value['house_id'] = $keyh + 1;
                    $value['house_longitude'] = $house_cha;
                    $value['house_deg'] = $house_array_angle['deg'];
                    $value['house_min'] = $house_array_angle['min'];
                    $value['house_sec'] = $house_array_angle['sec'];
                    $valueh['planet_one_list'][] = $value['code_name'];
                }


            }
            if (empty($value['planet_allow_degree'])) {
                $value['planet_allow_degree'] = array();
            }

            foreach ($planet_second as $keyg => &$valueg) {
                if (empty($valueg['planet_allow_degree'])) {
                    $valueg['planet_allow_degree'] = array();
                }

                $chazhi = abs($value['longitude'] - $valueg['longitude']);

                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                $planet_degree_lgit = 0;

                !empty($planet_degree[$value['code_name']]) && $planet_degree_lgit += $planet_degree[$value['code_name']];

                !empty($planet_degree[$valueg['code_name']]) && $planet_degree_lgit += $planet_degree[$valueg['code_name']];


                foreach ($allow_degree as $keyAd => $valueAd) {

                    $valueAd += $planet_degree_lgit;

                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {

                        $in_out = '-1';

                        if ($value['speed'] > $valueg['speed']) {
                            if ((fmod($value['longitude'], 30) < fmod($valueg['longitude'], 30))) {
                                $in_out = '1';
                            }
                        } else {
                            if ((fmod($valueg['longitude'], 30) > fmod($value['longitude'], 30))) {
                                $in_out = '1';
                            }
                        }

                        $start_link = 9999;
                        $in_out_start_time_list = array_reverse($in_out_time_list['start'][$valueg['code_name']]);

                        foreach ($in_out_start_time_list as $key_start => $value_start) {
                            $chazhi_in_out = abs($value['longitude'] - $value_start[3]);
                            if ($chazhi_in_out > 180) {
                                $chazhi_in_out = 360 - $chazhi_in_out;
                            }
                            if ($chazhi_in_out <= ($keyAd + $valueAd) and $chazhi_in_out >= ($keyAd - $valueAd)) {
                                if (abs(($keyAd + $valueAd) - $chazhi_in_out) < $start_link) {
                                    $start_link = abs(($keyAd + $valueAd) - $chazhi_in_out);
                                }
                            } else {
                                break;
                            }
                        }


                        $end_link = 9999;
                        $in_out_end_time_list = ($in_out_time_list['end'][$valueg['code_name']]);
                        foreach ($in_out_end_time_list as $key_end => $value_end) {
                            $chazhi_in_out = abs($value['longitude'] - $value_end[3]);
                            if ($chazhi_in_out > 180) {
                                $chazhi_in_out = 360 - $chazhi_in_out;
                            }
                            if ($chazhi_in_out <= ($keyAd + $valueAd) and $chazhi_in_out >= ($keyAd - $valueAd)) {
                                if (abs($chazhi_in_out - ($keyAd - $valueAd)) < $end_link) {
                                    $end_link = abs($chazhi_in_out - ($keyAd - $valueAd));
                                }
                            } else {
                                break;
                            }
                        }
                        if($valueg['code_name']==1){
                            //dump($in_out_start_time_list);
                            //dump($in_out_end_time_list);

                        }
                        $planet_allow_degree = [
                            'planet_number' => $keyg,
                            'code_name' => $valueg['code_name'],
                            'planet_english' => $valueg['planet_english'],
                            'planet_chinese' => $valueg['planet_chinese'],
                            'current_longitude' => $valueg['longitude'],
                            'allow' => $keyAd,
                            'allow_float' => $valueAd,
                            'difference' => $chazhi_in_out,
                            'in_out' => $in_out,
                            'start_time' => $new_date-$value_start[2],
                            'end_time' => $value_end[2]-$new_date,
                            'start_day' => $key_start - 1,
                            'end_day' => $key_end - 1
                        ];


                        $planet_allow_degree = array_merge($planet_allow_degree, $exSweTest->Convert_sign_deg_min(abs(round(($chazhi - $keyAd), 4))));
                        $value['planet_allow_degree'][] = $planet_allow_degree;

                        $planett_allow_degree = [
                            'planet_number' => $key,
                            'code_name' => $value['code_name'],
                            'planet_english' => $value['planet_english'],
                            'planet_chinese' => $value['planet_chinese'],
                            'current_longitude' => $value['longitude'],
                            'allow' => $keyAd,
                            'allow_float' => $valueAd,
                            'difference' => $chazhi_in_out,
                            'in_out' => $in_out,
                            'start_time' => $new_date-$value_start[2],
                            'end_time' => $value_end[2]-$new_date,
                            'start_day' => $key_start - 1,
                            'end_day' => $key_end - 1
                        ];
                        $planett_allow_degree = array_merge($planett_allow_degree, $exSweTest->Convert_sign_deg_min(abs(round(($chazhi - $keyAd), 4))));
                        $valueg['planet_allow_degree'][] = $planett_allow_degree;


                        if($chazhi_in_out==175.5470727){

                        }
                        $planet_allow_degree = [
                            'code_one' => $value['code_name'],
                            'planet_one_english' => $value['planet_english'],
                            'planet_one_chinese' => $value['planet_chinese'],
                            'allow' => $keyAd,
                            'allow_cn' => $allow_degree_cn[$keyAd],
                            'code_second' => $valueg['code_name'],
                            'planet_second_english' => $valueg['planet_english'],
                            'planet_second_chinese' => $valueg['planet_chinese'],
                            'allow_float' => $valueAd,
                            'difference' => $chazhi_in_out,
                            'in_out' => $in_out,
                            'start_time' => $new_date-$value_start[2],
                            'end_time' => $value_end[2]-$new_date,
                            'start_day' => $key_start - 1,
                            'end_day' => $key_end - 1
                        ];

                        if($valueg['code_name']==1){
                            //  dump($planet_allow_degree);

                        }
                        $planet_allow_degrees[] = $planet_allow_degree;
                        if ($planet_allow_degree['start_day'] != 0 and $planet_allow_degree['end_day'] != 0) {
//                            dump('推运');
//                            dump($valueg);
//                            dump('开始');
//                            dump($value_start);
//                            dump('结束');
//                            dump($value_end);

                        }

                    }
                }
            }
        }
        return ['planet' => $planet, 'planet_second' => $planet_second, 'house' => $house, 'planet_allow_degree' => $planet_allow_degrees];
    }

    //计算爱情事业财富
    //        "love": 0,
    //        "career": 0,
    //        "wealth": 0,
    //        "overall": 0,
    public function calculateScore($planet, $house, $week = '月亮')
    {
        //爱情计算
        $data['love'] = 70;   //爱情
        $data['career'] = 70;  //职业
        $data['wealth'] = 70;   //财富
        $data['study'] = 70;    //学习
        $data['healthy'] = 70;    //健康
        $data['average'] = 70;
        $planet_one=$planet['planet'];   //本命
        $planet_second=$planet['planet_second'];
        $planetNameObject = get_sington_object('planetName', "astrology\\planetName");
        $sign_guardian_index = [4, 3, 2, 1, 0, 2, 3, 4, 5, 6, 6, 5];
        //感情
        //1.1.1

        if (!empty($house[4]['planet_second_list']) and !empty($house[6]['planet_second_list'])) {
            if ($this->calculateHouse($house, ['or', [5, 7]], ['or', [$week]], $planetNameObject)) {
                $data['love'] += 17;
            } else {
                $data['love'] += 9;
            }
        }
        $love_guandian_l = round($this->calculateAllow($planet_second, [$sign_guardian_index[$house[4]["sign"]["sign_id"]], $sign_guardian_index[$house[6]["sign"]["sign_id"]], 1, 3]));
        if ($love_guandian_l > 10) {
            $love_guandian_l = 10;
        }
        if ($love_guandian_l < -10) {
            $love_guandian_l = -10;
        }
        $data['love'] += $love_guandian_l;
        $sign_love_index = [3, 14, -9, 1, -5, 12, 15, -8, 7, -2, 11, -6, 4, 13, -10, 0];
        $latue_index_one=(int)($planet_one[1]['longitude'] % 16);
        $latue_index=(int)($planet_second[1]['longitude'] % 16);

        $data['love'] += (int)(($sign_love_index[$latue_index]+$sign_love_index[$latue_index_one])/2);
        //事业
        //2.1.1
        if (!empty($house[5]['planet_second_list']) and !empty($house[9]['planet_second_list'])) {
            if ($this->calculateHouse($house, ['or', [6, 10]], ['or', [$week]], $planetNameObject)) {
                $data['career'] += 12;
            } else {
                $data['career'] += 7;
            }
        }
        $love_guandian_l = round($this->calculateAllow($planet_second, [$sign_guardian_index[$house[5]["sign"]["sign_id"]], $sign_guardian_index[$house[9]["sign"]["sign_id"]], 2, 7]));
        if ($love_guandian_l > 12) {
            $love_guandian_l = 12;
        }
        if ($love_guandian_l < -9) {
            $love_guandian_l = -9;
        }
        $data['career'] += $love_guandian_l;

        $sign_love_index = [15, -7, 8, 1, -9, 14, 6, -3, 12, -10, 5, 13, -4, 10, -1, 7];
        $latue_index_one=(int)($planet_one[1]['longitude'] % 16);
        $latue_index=(int)($planet_second[1]['longitude'] % 16);

        $data['career'] += (int)(($sign_love_index[$latue_index]+$sign_love_index[$latue_index_one])/2);
        //学习
        //2.1.1
        if (!empty($house[3]['planet_second_list']) and !empty($house[7]['planet_second_list'])) {
            if ($this->calculateHouse($house, ['or', [4, 11]], ['or', [$week]], $planetNameObject)) {
                $data['study'] += 15;
            } else {
                $data['study'] += 9;
            }
        }
        $love_guandian_l = round($this->calculateAllow($planet_second, [$sign_guardian_index[$house[3]["sign"]["sign_id"]], $sign_guardian_index[$house[2]["sign"]["sign_id"]], 0, 6]));
        if ($love_guandian_l > 8) {
            $love_guandian_l = 8;
        }
        if ($love_guandian_l < -9) {
            $love_guandian_l = -9;
        }
        $data['study'] += $love_guandian_l;

        $sign_love_index = [-6, 11, 15, -8, 3, -10, 4, 14, -5, 9, -2, 13, 0, 12, -7, 1];
        $latue_index_one=(int)($planet_one[1]['longitude'] % 16);
        $latue_index=(int)($planet_second[1]['longitude'] % 16);

        $data['study'] += (int)(($sign_love_index[$latue_index]+$sign_love_index[$latue_index_one])/2);

        //财富
        //3.1.1
        if (!empty($house[2]['planet_second_list']) and !empty($house[6]['planet_second_list'])) {
            if ($this->calculateHouse($house, ['or', [3, 9]], ['or', [$week]], $planetNameObject)) {
                $data['wealth'] += 17;
            } else {
                $data['wealth'] += 8;
            }
        }
        $love_guandian_l = round($this->calculateAllow($planet_second, [$sign_guardian_index[$house[2]["sign"]["sign_id"]], $sign_guardian_index[$house[11]["sign"]["sign_id"]], 2, 9]));
        if ($love_guandian_l > 11) {
            $love_guandian_l = 11;
        }
        if ($love_guandian_l < -10) {
            $love_guandian_l = -10;
        }
        $data['wealth'] += $love_guandian_l;

        $sign_love_index = [10, -3, 1, 15, -6, 8, 13, -9, 4, 14, -2, 11, -7, 5, -10, 12];
        $latue_index_one=(int)($planet_one[1]['longitude'] % 16);
        $latue_index=(int)($planet_second[1]['longitude'] % 16);

        $data['wealth'] += (int)(($sign_love_index[$latue_index]+$sign_love_index[$latue_index_one])/2);


        //财富
        //3.1.1
        if (!empty($house[1]['planet_second_list']) and !empty($house[7]['planet_second_list'])) {
            if ($this->calculateHouse($house, ['or', [2, 8]], ['or', [$week]], $planetNameObject)) {
                $data['healthy'] += 8;
            } else {
                $data['healthy'] += 4;
            }
        }
        $love_guandian_l = round($this->calculateAllow($planet_second, [$sign_guardian_index[$house[9]["sign"]["sign_id"]], $sign_guardian_index[$house[8]["sign"]["sign_id"]], 3, 7]));
        if ($love_guandian_l > 12) {
            $love_guandian_l = 12;
        }
        if ($love_guandian_l < -9) {
            $love_guandian_l = -9;
        }
        $data['healthy'] += $love_guandian_l;

        $sign_love_index = [2, -9, 15, 7, -4, 13, -10, 6, 11, -1, 14, -8, 3, 0, 12, -5];
        $latue_index_one=(int)($planet_one[1]['longitude'] % 16);
        $latue_index=(int)($planet_second[1]['longitude'] % 16);

        $data['healthy'] += (int)(($sign_love_index[$latue_index]+$sign_love_index[$latue_index_one])/2);


        if ($data['love'] > 100) {
            $data['love'] = 100;
        }
        if ($data['career'] > 100) {
            $data['career'] = 100;
        }
        if ($data['wealth'] > 100) {
            $data['wealth'] = 100;
        }
        if ($data['study'] > 100) {
            $data['study'] = 100;
        }
        if ($data['healthy'] > 100) {
            $data['healthy'] = 100;
        }


        $data['average'] = round(($data['love'] + $data['career'] + $data['wealth']+ $data['study']+ $data['healthy']) / 5);
        return $data;
    }

    public function calculateHouse($house, $house_rules, $planet_rules, $planetNameObject)
    {

        $planetName = $planetNameObject::$planetChinese;
        $is_adel = false;
        foreach ($house as $keyad => $valuead) {
            if (!empty($valuead['planet_second_list'])) {
                if ($house_rules[0] == 'or' and in_array($valuead['house_id'], $house_rules[1])) {
                    if ($planet_rules[0] == 'or') {
                        foreach ($valuead['planet_second_list'] as $keylp => $vlep) {
                            if (in_array($planetName[$vlep], $planet_rules[1])) {
                                $is_adel = true;
                                break 2;
                            }
                        }
                    }
                }
            }
        }
        return $is_adel;
    }

    public function calculateAllow($planet, $life_id)
    {

        $data[0] = 0;
        $data[1] = 0;
        $data[2] = 0;

        $planer_sore[0][0] = 3;
        $planer_sore[0][30] = 1.8;
        $planer_sore[0][45] = -1.4;
        $planer_sore[0][60] = 3.2;
        $planer_sore[0][90] = -1.6;
        $planer_sore[0][120] = 4.2;
        $planer_sore[0][150] = -0.8;
        $planer_sore[0][180] = -1.6;
        $planer_sore[1][0] = 3.5;
        $planer_sore[1][30] = 1.8;
        $planer_sore[1][45] = -0.4;
        $planer_sore[1][60] = 1.2;
        $planer_sore[1][90] = -1.6;
        $planer_sore[1][120] = 1.2;
        $planer_sore[1][150] = -0.8;
        $planer_sore[1][180] = -1.6;
        $planer_sore[2][0] = 2;
        $planer_sore[2][30] = 3.4;
        $planer_sore[2][45] = -0.2;
        $planer_sore[2][60] = 2.6;
        $planer_sore[2][90] = -0.8;
        $planer_sore[2][120] = 2.6;
        $planer_sore[2][150] = -1.4;
        $planer_sore[2][180] = -1.8;
        $planer_sore[3][0] = 4;
        $planer_sore[3][30] = 0.8;
        $planer_sore[3][45] = 0;
        $planer_sore[3][60] = 1.2;
        $planer_sore[3][90] = 0;
        $planer_sore[3][120] = 2.2;
        $planer_sore[3][150] = 1.1;
        $planer_sore[3][180] = 1.2;
        $planer_sore[4][0] = -1;
        $planer_sore[4][30] = 0.4;
        $planer_sore[4][45] = -0.2;
        $planer_sore[4][60] = 3.6;
        $planer_sore[4][90] = -1.6;
        $planer_sore[4][120] = 0.6;
        $planer_sore[4][150] = -0.4;
        $planer_sore[4][180] = -1.6;

        $sore_adel = 0;


        //处重复$life_id
        $life_id = array_unique($life_id);


        foreach ($life_id as $keyad => $valuead) {

            if (!empty($planet[$valuead]['planet_allow_degree'])) {
                foreach ($planet[$valuead]['planet_allow_degree'] as $keyat => $valueat) {
                    if(!empty($planer_sore[$valueat["code_name"]][$valueat["allow"]])){
                        $sore_adel += $planer_sore[$valueat["code_name"]][$valueat["allow"]]*3;
                    }

                }
            }
        }
        return $sore_adel;
    }

    //计算语料数据

    public function calculateCorpusList($ex_data)
    {
        $life_house = $ex_data['house'];
        $life_planet = $ex_data['planet'];

        $second_planet = $ex_data['planet_second'];
        $planet_allow_degree = $ex_data['planet_allow_degree'];

        // dump($planet_allow_degree);
        foreach ($second_planet as $keypl => $valupl) {
            $second_planet_new[$valupl['code_name']] = $valupl;
        }
        foreach ($life_planet as $keypl => $valupl) {
            $life_planet_new[$valupl['code_name']] = $valupl;
        }
        $planet_allow_degree_new = array();


        foreach ($life_house as $keyad => $valuead) {
            // if (in_array($valuead['house_id'], [1, 4, 7, 10])) {
            if (!empty($valuead['planet_one_list'])) {
                foreach ($valuead['planet_one_list'] as $keylp => $vlep) {
                    // dump($life_planet_new[$vlep]);
                    $house_cha = abs($life_planet_new[$vlep]['longitude'] - $valuead['longitude']);
                    if ($house_cha < 4) {
                        foreach ($planet_allow_degree as $kesf => $valuef) {
                            if ($valuef['code_one'] == $vlep) {
                                $planet_allow_degree_new[] = $valuef;
                                unset($planet_allow_degree[$kesf]);
                                break;
                            }
                        }
                    }
                }
            }
            //  }
        }

        foreach ($life_house as $keyad => $valuead) {
            //  if (in_array($valuead['house_id'], [1, 4, 7, 10])) {
            if (!empty($valuead['planet_second_list'])) {
                foreach ($valuead['planet_second_list'] as $keylp => $vlep) {
                    // dump($life_planet_new[$vlep]);
                    $house_cha = abs($second_planet_new[$vlep]['longitude'] - $valuead['longitude']);
                    if ($house_cha < 4) {
                        foreach ($planet_allow_degree as $kesf => $valuef) {
                            if ($valuef['code_one'] == $vlep) {
                                $planet_allow_degree_new[] = $valuef;
                                unset($planet_allow_degree[$kesf]);
                                break;
                            }
                        }
                    }
                }
            }
            // }
        }

        $planet_list = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'm'];

        foreach ($planet_list as $keyk => $valuek) {
            foreach ($planet_allow_degree as $kesf => $valuef) {
                if ($valuef['code_one'] == $valuek) {
                    $planet_allow_degree_new[] = $valuef;
                    //unset($planet_allow_degree[$kesf]);
                    break;
                }
            }
        }

        //dump($planet_allow_degree_new);
        //本命星体在四轴（4度）的与流年星体有相位先显示
        return $planet_allow_degree_new;
    }


    public function getexPlain($life_conditions = [], $new_conditions = [])
    {

        $exSweTest = get_sington_object('SweTest', "astrology\\SweTestEmpty");
        $life_data = $exSweTest->calculate($life_conditions, ['0', '1']);

        $new_data = $exSweTest->calculate($new_conditions, ['0', '1']);

        $house = $life_data['house'];
        $planet_second = $new_data['planet'];

        $house_t[0] = '情绪冲动';
        $house_t[1] = '最爱享受';
        $house_t[2] = '我是墙头草';
        $house_t[3] = '寻找避风港';
        $house_t[4] = '渴望出风头';
        $house_t[5] = '希望自己是时间管理大师';
        $house_t[6] = '让自己精致起来';
        $house_t[7] = '治愈自己';
        $house_t[8] = '渴望自由';
        $house_t[9] = '野心家';
        $house_t[10] = '情绪超然';
        $house_t[11] = '梦想时刻';

        $house_c[0] = '你现在正在进入一个情绪冲动的时间段，强烈的感觉会主导你的生活。也许你会经历新的开始，也许是新的情感，也许是新习惯，但你一定确定它们是好的，因为它们会在未来相当长的一段时间内影响你！';
        $house_c[1] = '现在你在情感上特别希望平静和稳定。你深层的情感需求是需要稳定和持久的。音乐对你来说很可能会发挥更重要的作用，买买买可以满足你远离物质匮乏感。';
        $house_c[2] = '你现在正在进入一个情绪变化的时期：同时可能有两种情绪的表达方式，而且都不会太深入。你的习惯处于变化中；你的内心的需求是多样的。你会随时关注生活中的变化，你对学习和交流有一种本能的冲动。';
        $house_c[3] = '你现在渴望情感安全、归属感和受到照顾。你想要找到自己能依靠的基础；你渴望一种持久的亲密关系，就好像一座能抵御世界变迁的城堡。';
        $house_c[4] = '你希望成为大家关注的中心，渴望大家欣赏和礼物。这是一个爽朗的、有创造性的，甚至是浪漫的时间段，你会带着自豪和喜爱回顾这段时光。';
        $house_c[5] = '现在，健康和工作目标对你来说更加重要。你渴望有条理和效率的完成事情。你希望为所有的东西安排一个合适的位置，任何妨碍你的东西都会让你感到紧张。';
        $house_c[6] = '现在，让自己精致起来和良好的人际关系是你获得情感满足的关键。和谐与美丽让你深感满足，与他人的亲密关系，特别是婚姻和其他伙伴关系会影响你的感受，如果不够和谐，你的情绪会变的不安。';
        $house_c[7] = '现在你的情绪会强烈受到性、权力和金钱的影响。你本能中对秘密、禁忌和奥秘事情的渴望会呈现出来，你此时希望深入研究，并从头开始，彻底从源头治愈自己。';
        $house_c[8] = '现在只要你的眼界足够高远，那么你会有一切皆有可能的感觉。你会乐观，有信念，并且渴望于在情感中冒险。这是探索你的感受的时候，一种对新情感不安的体验。';
        $house_c[9] = '现在你的情感严肃、方向清晰，对事物本质有现实的认识。你会对自己非常严格，坚持认为任何对生活安全和长期目标没有贡献的东西都是微不足道的，你的野心会越来越大。';
        $house_c[10] = '现在你的情绪冷静或超然，特别的理想主义。现在对你而言，重要的是思想，而不仅仅是狭隘的个人问题，而且对和你观念不同的人几乎没有容忍度。你从本能上喜欢新的、不寻常的、前卫的东西。';
        $house_c[11] = '现在你会呈现出一种神秘的特质。充满了梦想和幻想、宽恕和理解。这会让你获得深刻的感受。过去和未来交织在一起，人与人之间的界限消失了，这让你心灵和能量与感受力增强。';

        $data = ['title' => '信心满满', 'phase_str' => '太阳1宫', 'planet_id' => 1, 'sign_id' => 1, 'content' => ''];

        foreach ($house as $keyh => $valueh) {
            $house_cha = abs($valueh['longitude'] - $planet_second[1]['longitude']);
            if ($house_cha > 180) {
                $house_cha = 360 - $house_cha;
            }
            if ($keyh < 11) {
                $last_house = $house[$keyh + 1];
            } else {
                $last_house = $house[0];
            }
            if ($valueh['longitude'] > $last_house['longitude']) {
                if ($planet_second[1]['longitude'] < $last_house['longitude']) {
                    $planet_second[1]['longitude'] += 360;
                }
                $last_house['longitude'] += 360;
            }

            if ($planet_second[1]['longitude'] >= $valueh['longitude'] and $planet_second[1]['longitude'] < $last_house['longitude']) {

                $data = ['title' => $house_t[$keyh], 'phase_str' => '月亮' . ($keyh + 1) . '宫', 'content' => $house_c[$keyh]];

                break;
            }
        }

        return $data;
    }


    /*
     * 数据分类计算
     * 2宫-财富
 *3宫-学习
 *10宫-事业
 *5宫-爱情

 *相位
 *任何流年星-本命中天 事业
 *任何流年星-本命金星 爱情
 *流日-本命土  事业
 *流金-本命木 财富
     * ['type'='house','id'=1]
     *
     * ['type'='phase','chartType'=5, 'planet_one'=1,'planet_two'=11]
     */

    public function getCategoryData($data)
    {
        $category='';
        if ($data['type'] == 'house') {
            switch ($data['id']) {
                case '2':
                    $category = '财富';
                    break;
                case '3':
                    $category = '学习';
                    break;
                case '5':
                    $category = '爱情';
                case '10':
                    $category = '事业';
                    break;
                default:
                    $category = '';
            }
        }
        if ($data['type'] == 'phase') {
            if($data['planet_one']=='11'){
                $category = '事业';
            }else if($data['planet_one']=='3'){
                $category = '爱情';
            }else if($data['planet_one']=='6' and $data['planet_two']=='1'){
                $category = '事业';
            }else if($data['planet_one']=='5' and $data['planet_two']=='3'){
                $category = '财富';
            }else if($data['planet_one']=='2'){
                $category = '学习';
            }
        }

        return $category;
    }

}
