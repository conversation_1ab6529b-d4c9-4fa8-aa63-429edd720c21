<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

use \Firebase\JWT\JWT;

// 解密user_token
function decoded_user_token($token = '')
{
    
    $decoded = JWT::decode($token, API_KEY . JWT_KEY, array('HS256'));

    return (array) $decoded;
}

// 获取解密信息中的data
function get_member_by_token($token = '')
{

    $result = decoded_user_token($token);

    if(!empty($result['code']) and  !empty($result['msg']) and  $result['code']>0 and !empty($result['msg'])){
        return false;
    }else{
        if(session('member_auth_sign')!=$result['data']->member_auth_sign ){
            return false;
        }
        return $result['data'];
    }
}

// 数据验签时数据字段过滤
function sign_field_filter($data = [])
{
    
    $data_sign_filter_field_array = config('data_sign_filter_field');

    foreach ($data_sign_filter_field_array as $v)
    {
        
        if (array_key_exists($v, $data)) {
            
            unset($data[$v]);
        }
    }
    
    return $data;
}

// 过滤后的数据生成数据签名
function create_sign_filter($data = [], $key = '')
{
    
    $filter_data = sign_field_filter($data);
    
    return empty($key) ? data_md5_key($filter_data, API_KEY) : data_md5_key($filter_data, $key);
}

function substrChinese($str)
{
    preg_match_all("/[\x{4e00}-\x{9fa5}]+/u",$str,$regs);//preg_match_all（“正则表达式”,"截取的字符串","成功之后返回的结果集（是数组）"）
    $s = join('',$regs[0]);//join("可选。规定数组元素之间放置的内容。默认是 ""（空字符串）。","要组合为字符串的数组。")把数组元素组合为一个字符串
    $s=mb_substr($s,0,80,'utf-8');//mb_substr用于字符串截取，可以防止中文乱码的情况
    return $s;

}