<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiapi\controller;

/**
 * 界面点击统计控制器
 */
class Clickstatistical extends ApiBase
{

    /**
     * 界面点击统计列表
     */
    public function clickStatisticalList()
    {

        $where = $this->logicClickStatistical->getWhere($this->param);

        $data=$this->logicClickStatistical->getClickStatisticalList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 界面点击统计无分页列表
     */
    public function clickStatisticalColumn()
    {

        $data=$this->logicClickStatistical->getClickStatisticalColumn($this->param);

		return $this->apiReturn($data);
    }
    /**
     * 界面点击统计添加
     */
    public function clickStatisticalAdd()
    {

	   $regit=$this->logicClickStatistical->clickStatisticalEdit($this->param);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 界面点击统计删除
     */
    public function clickStatisticalDel()
    {

       $regit=$this->logicClickStatistical->clickStatisticalDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
