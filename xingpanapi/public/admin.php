<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

// 绑定后台模块
define('BIND_MODULE', 'admin');

$ip = $_SERVER['REMOTE_ADDR'];
// 定义白名单IP数组
$allowedIPs = array('**************', '***************');
// 检查IP地址是否在白名单中
if (!in_array($ip, $allowedIPs)) {
    // 如果不在白名单中，返回错误或重定向到另一个页面
    header('HTTP/1.0 403 Forbidden');
    echo 'Access Forbidden. Your IP is not allowed.';
    exit; // 确保后面的代码不会被执行
}
// 加载公共引导文件
require './public.php';
