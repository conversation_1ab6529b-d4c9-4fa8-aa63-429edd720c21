<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

// PHP版本验证需要大于5.6.0
if (version_compare(PHP_VERSION, '5.6.0', '<')) {
    
    die('ThinkTree Require PHP > 5.6.0 !');
}
$ip = $_SERVER['REMOTE_ADDR'];

// 定义白名单IP数组
$allowedIPs = array('***************');

// 检查IP地址是否在黑名单中
if (in_array($ip, $allowedIPs)) {
    // 如果不在白名单中，返回错误或重定向到另一个页面
    header('HTTP/1.0 403 Forbidden');
    echo 'Access Forbidden. Your IP is not allowed.';
    exit; // 确保后面的代码不会被执行
}
// 设置跨域请求头
header("Access-Control-Allow-Origin: *");

// 如果需要自定义请求头时，直接把请求头添加到下面的参数里面即可
header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
// 定义应用目录
define('APP_PATH', __DIR__ . '/../app/');

// 检测是否安装
if (!file_exists(APP_PATH . 'database.php')) {
    
    header("location:./install.php");
    exit;
}

// 加载框架引导文件
require __DIR__ . '/../thinkphp/start.php';