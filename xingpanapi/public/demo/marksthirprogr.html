<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>马盘三限盘</title>
	
	<link href="static/css/style.css" rel="stylesheet" type="text/css">
	<link href="static/bootstrap.3.3.7/css/bootstrap.min.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" href="static/bootstrap.3.3.7/datetimepicker/css/datetimepicker.css">
	<script src="https://images.robustcn.com/public/static/module/common/jquery/jquery-2.2.3.min.js"></script>
	
	<script src="static/bootstrap.3.3.7/js/bootstrap.min.js"></script>
	
	<script src="static/js/picker.min.js"></script>
	<script src="static/js/city.js"></script>
	<script src="static/bootstrap.3.3.7/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
</head>
<body>
<div class="container">
	<h2 class="text-center">马盘三限盘</h2>
	<div class="row">
		<div class="col-md-6">
			<!-- AREA CHART -->
			<div class="panel panel-primary">
				<div class="panel-heading">
					<h3 class="panel-title">出生信息1</h3>
				</div>
				<table class="table table-bordered">
					<tbody>
					
					<tr>
						<th class="text-center">姓名</th>
						<td class="text-center">
							<div class="col-md-6 col-md-push-3">
								<input type="text" class="form-control" placeholder="姓名">
							</div>
						</td>
					</tr>
					<tr class="success">
						<th class="text-center">出生地点</th>
						<td class="text-center">
							<div class="col-md-6 col-md-push-3">
								<a href="#" class="btn btn-info active sel_city" role="button">上海市 上海市 上海市</a>
							</div>
						</td>
					</tr>
					<tr>
						<th class="text-center">出生时间</th>
						<td class="text-center">
							<div class="col-md-6 col-md-push-3">
								<input name="birthday" size="xs16" type="text"
								       value="2020-10-10 12:12"
								
								       class="form_datetime form-control">
							</div>
						</td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="col-md-6">
			<!-- AREA CHART -->
			<div class="panel panel-primary">
				<div class="panel-heading">
					<h3 class="panel-title">出生信息2</h3>
				</div>
				<table class="table table-bordered">
					<tbody>
					
					<tr>
						<th class="text-center">姓名</th>
						<td class="text-center">
							<div class="col-md-6 col-md-push-3">
								<input type="text" class="form-control" placeholder="姓名">
							</div>
						</td>
					</tr>
					<tr class="success">
						<th class="text-center">出生地点</th>
						<td class="text-center">
							<div class="col-md-6 col-md-push-3">
								<a href="#" class="btn btn-info active sel_city" role="button">上海市 上海市 上海市</a>
							</div>
						</td>
					</tr>
					<tr>
						<th class="text-center">出生时间</th>
						<td class="text-center">
							<div class="col-md-6 col-md-push-3">
								<input name="birthday" size="xs16" type="text"
								       value="2020-10-10 12:12"
								
								       class="form_datetime form-control">
							</div>
						</td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="col-md-12">
			<!-- AREA CHART -->
			<div class="panel panel-primary">
				
				<table class="table table-bordered">
					<tbody>
					<tr>
						<th class="text-center">推运到</th>
						<td class="text-center">
							<div class="col-md-6 col-md-push-3">
								<input name="transitday" size="xs16" type="text"
								       value="2020-10-10 12:12"
								
								       class="form_datetime form-control transitday">
							</div>
						</td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6 col-md-push-3">
			<table class="table table-bordered">
				<tbody>
				
				<tr class="success">
					<th class="text-center">宫位选择</th>
					<td class="text-center">
						<div class="col-md-6 col-md-push-3">
							<button class="btn btn-primary" data-toggle="modal" data-target="#advSettings">配置</button>
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6 col-md-push-3">
			<div class="bs-example" data-example-id="horizontal-dl">
				<dl class="dl-horizontal">
					<dt><h4>星盘种类:</h4></dt>
					<dd>
						<ul class="nav nav-pills">
							<li role="presentation"><a href="natal.html">本命盘</a></li>
							<li role="presentation"><a href="comparision.html">比较盘</a></li>
							<li role="presentation"><a href="secondarylimit.html">次限盘</a></li>
							<li role="presentation"><a href="solarreturn.html">太阳返照</a></li>
							<li role="presentation"><a href="lunarreturn.html">月亮返照</a></li>
							<li role="presentation"><a href="timesmidpoint.html">时空中点盘</a></li>
							<li role="presentation"><a href="secondarylimit.html">次限盘</a></li>
							<li role="presentation"><a href="transit.html">行运盘</a></li>
							<li role="presentation"><a href="composite.html">双人组合盘</a></li>
							<li role="presentation"><a href="marks.html">时空中点盘</a></li>
							<li role="presentation"><a href="thirdprogressed.html">本命三限盘</a></li>
							<li role="presentation"><a href="progressed.html">推进盘</a></li>
							<li role="presentation"><a href="synastry.html">配对盘</a></li>
							<li role="presentation"><a href="solararc.html">太阳弧</a></li>
							<li role="presentation"><a href="compositesecondary.html">组合次限</a></li>
							<li role="presentation"><a href="developed.html">法达盘</a></li>
						</ul>
					</dd>
				</dl>
				<dl class="dl-horizontal">
					<dt><h4>星盘模式:</h4></dt>
					<dd>
						<ul class="nav nav-pills">
							<li role="presentation"><a class="switch_web_ixingpan" bigt="web_ixingpan_cn">文字版</a></li>
							<li role="presentation"><a class="switch_web_ixingpan" bigt="web_ixingpan">符号版</a></li>
							<li role="presentation"><a class="switch_web_ixingpan" bigt="web_ixingpan_senior">高级盘</a></li>
						
						</ul>
					</dd>
				</dl>
			</div>
		</div>
	</div>
</div>


<div class='box-svg'>

</div>

<div id="tip_sign_add">

</div>
<!-- 模态框（Modal） -->
<div class="modal fade" id="advSettings" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
					&times;
				</button>
				<h4 class="modal-title" id="myModalLabel">
					新盘选择
				</h4>
			</div>
			<div class="modal-body">
				<form class="form-horizontal">
					<div class="form-group">
						<label class="col-sm-2 control-label">宫位系统</label>
						<div class="col-sm-10">
							<select class="form-control h_sys" id="hsys">
								<option value="K" selected="selected">Koch</option>
								<option value="P">Placidus</option>
								<option value="O">Porphyrius</option>
								<option value="R">Regiomontanus</option>
								<option value="C">Campanus</option>
								<option value="E" title="cusp 1 is Ascendant">Equal</option>
								<option value="V" title="Asc. in middle of house 1">Vehlow equal</option>
								<option value="W">Whole sign</option>
								<option value="X">Axial rotation system</option>
								<option value="H">Azimuthal or horizontal system</option>
								<option value="T" title="'topocentric' system">Polich/Page</option>
								<option value="B">Alcabitus</option>
								<option value="M">Morinus</option>
								<option value="U">Krusinski-Pisa</option>
							</select>
						
						
						</div>
					</div>
					<div class="form-group">
						<div class="col-sm-10">
							<h2>主要行星：</h2>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label"><input class='planets_q' type="checkbox">主星体</label>
						<div class="col-sm-10">
							<div class="checkbox">
								<label>
									<input class='will_choose planets' value="0" type="checkbox" checked="checked"
									       disabled="disabled">太阳
								</label>
								<label>
									<input class='will_choose planets' value="1" type="checkbox" checked="checked"
									       disabled="disabled">月亮
								</label>
								<label>
									<input class='planets' value="2" type="checkbox">水星
								</label>
								<label>
									<input class='planets' value="3" type="checkbox">金星
								</label>
								<label>
									<input class='planets' value="4" type="checkbox">火星
								</label>
								<label>
									<input class='planets' value="5" type="checkbox">木星
								</label>
								<label>
									<input class='planets' value="6" type="checkbox">土星
								</label>
								<label>
									<input class='planets' value="7" type="checkbox">天王星
								</label>
								<label>
									<input class='planets' value="8" type="checkbox">海王星
								</label>
								<label>
									<input class='planets' value="9" type="checkbox">冥王星
								</label>
								<label>
									<input class='planets' value="t" type="checkbox">真实的月切点
								</label>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label"><input class='planets_q' type="checkbox">小行星</label>
						<div class="col-sm-10">
							<div class="checkbox">
								<label>
									<input class='planets' value="D" type="checkbox">凯龙星
								</label>
								<label>
									<input class='planets' value="F" type="checkbox">谷神星
								</label>
								<label>
									<input class='planets' value="E" type="checkbox">人龙星
								</label>
								<label>
									<input class='planets' value="G" type="checkbox">智神星
								</label>
								<label>
									<input class='planets' value="H" type="checkbox">婚神星
								</label>
								<label>
									<input class='planets' value="I" type="checkbox">灶神星
								</label>
								<label>
									<input class='planet_xs' value="xs433" type="checkbox">爱神星
								</label>
								<label>
									<input class='planet_xs' value="xs16" type="checkbox">灵神星
								</label>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label"><input class='planets_q' type="checkbox">虚 点</label>
						<div class="col-sm-10">
							<div class="checkbox">
								<label>
									<input class='virtual' value="10" type="checkbox">上升
								</label>
								<label>
									<input class='virtual' value="11" type="checkbox">中天
								</label>
								<label>
									<input class='virtual' value="19" type="checkbox">天底点
								</label>
								<label>
									<input class='virtual' value="18" type="checkbox">下降点
								</label>
								<label>
									<input class='planets' value="m" type="checkbox">北交点
								</label>
								<label>
									<input class='virtual' value="21" type="checkbox">南交点
								</label>
								<label>
									<input class='planets' value="A" type="checkbox">莉莉丝点
								</label>
								<label>
									<input class='virtual' value="pFortune" type="checkbox">福点
								</label>
								<label>
									<input class='virtual' value="13" type="checkbox">宿命点
								</label>
								<label>
									<input class='virtual' value="14" type="checkbox">东升点
								</label>
								<label>
									<input class='virtual' value="20" type="checkbox">日月中点
								</label>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label"><input class='planets_q' type="checkbox">虚 星</label>
						<div class="col-sm-10">
							<div class="checkbox">
								<label>
									<input class='planets' name="planets" value="J" type="checkbox">丘比特
								</label>
								<label>
									<input class='planets' value="K" type="checkbox">哈迪斯
								</label>
								<label>
									<input class='planets' value="L" type="checkbox">宙斯
								</label>
								<label>
									<input class='planets' value="M" type="checkbox">克洛诺斯
								</label>
								<label>
									<input class='planets' value="N" type="checkbox">阿波罗
								</label>
								<label>
									<input class='planets' value="O" type="checkbox">阿德门图斯
								</label>
								<label>
									<input class='planets' value="P" type="checkbox">弗卡奴斯
								</label>
								<label>
									<input class='planets' value="Q" type="checkbox">波塞东
								</label>
							</div>
						</div>
					</div>
					<div class="form-group">
						<div class="col-sm-10">
							<h2>相位容许度设置(单位为度)：</h2>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">主要相位：</label>
						<div class="col-sm-10">
							
							<div class="checkbox">
								<label>
									<input class='phase' name="phase" value="0" checked="checked" type="checkbox">合相(0°)
									<select style="width:45px" name="phase_0" class="phase_0">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6" selected="selected">6</option>
										<option value="7">7</option>
										<option value="8">8</option>
										<option value="9">9</option>
										<option value="10">10</option>
										<option value="11">11</option>
										<option value="12">12</option>
										<option value="13">13</option>
										<option value="14">14</option>
										<option value="15">15</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="180" checked="checked" type="checkbox">冲相(180°)
									<select style="width:45px" name="phase_180" class="phase_180">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6" selected="selected">6</option>
										<option value="7">7</option>
										<option value="8">8</option>
										<option value="9">9</option>
										<option value="10">10</option>
										<option value="11">11</option>
										<option value="12">12</option>
										<option value="13">13</option>
										<option value="14">14</option>
										<option value="15">15</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="120" checked="checked" type="checkbox">拱相(120°)
									<select style="width:45px" name="phase_120" class="phase_120">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6" selected="selected">6</option>
										<option value="7">7</option>
										<option value="8">8</option>
										<option value="9">9</option>
										<option value="10">10</option>
										<option value="11">11</option>
										<option value="12">12</option>
										<option value="13">13</option>
										<option value="14">14</option>
										<option value="15">15</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="90" checked="checked" type="checkbox">刑相(90°)
									<select style="width:45px" name="phase_90" class="phase_90">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6" selected="selected">6</option>
										<option value="7">7</option>
										<option value="8">8</option>
										<option value="9">9</option>
										<option value="10">10</option>
										<option value="11">11</option>
										<option value="12">12</option>
										<option value="13">13</option>
										<option value="14">14</option>
										<option value="15">15</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="60" checked="checked" type="checkbox">六合相(60°)
									<select style="width:45px" name="phase_60" class="phase_60">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6" selected="selected">6</option>
										<option value="7">7</option>
										<option value="8">8</option>
										<option value="9">9</option>
										<option value="10">10</option>
										<option value="11">11</option>
										<option value="12">12</option>
										<option value="13">13</option>
										<option value="14">14</option>
										<option value="15">15</option>
									</select>
								</label>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">其它相位：</label>
						<div class="col-sm-10">
							
							<div class="checkbox">
								<label>
									<input class='phase' name="phase" value="30" checked="checked" type="checkbox">十二分相(30°)
									<select style="width:45px" name="phase_30" class="phase_30">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2" selected="selected">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="36" checked="checked" type="checkbox">十分相(36°)
									<select style="width:45px" name="phase_36" class="phase_36">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2" selected="selected">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="45" checked="checked" type="checkbox">八分相(45°)
									<select style="width:45px" name="phase_45" class="phase_45">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2" selected="selected">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="72" checked="checked" type="checkbox">五分相(72°)
									<select style="width:45px" name="phase_72" class="phase_72">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2" selected="selected">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="135" checked="checked" type="checkbox">补八分相(135°)
									<select style="width:45px" name="phase_135" class="phase_135">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="144" checked="checked" type="checkbox">补五分相(144°)
									<select style="width:45px" name="phase_144" class="phase_144">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2" selected="selected">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
									</select>
								</label>
								<label>
									<input class='phase' name="phase" value="150" checked="checked" type="checkbox">梅花相(150°)
									<select style="width:45px" name="phase_150" class="phase_150">
										<option value="0.5">0.5</option>
										<option value="1">1</option>
										<option value="1.5">1.5</option>
										<option value="2" selected="selected">2</option>
										<option value="3">3</option>
										<option value="4">4</option>
										<option value="5">5</option>
										<option value="6">6</option>
									</select>
								</label>
							</div>
						</div>
					</div>
				</form>
			
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-primary" data-dismiss="modal">生成</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal -->
</div>
</body>
</html>
<script>
    $(document).ready(function () {
        var nameEl, u_id;

        var first = []; /* 省，直辖市 */
        var second = []; /* 市 */
        var third = []; /* 镇 */

        var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */

        var checked = [0, 0, 0]; /* 已选选项 */

        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }

        var picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });

        picker.on('picker.select', function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';
            u_id = $('.sel_city').index(nameEl);

            nameEl.text(text1 + ' ' + text2 + ' ' + text3);
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {

            console.log(city[selectedVal[0]]['sub'][selectedVal[1]]['sub'][selectedVal[2]]['longitude']);
            console.log(userList);
            userList[u_id]['longitude'] = city[selectedVal[0]]['sub'][selectedVal[1]]['sub'][selectedVal[2]]['longitude'];
            userList[u_id]['latitude'] = city[selectedVal[0]]['sub'][selectedVal[1]]['sub'][selectedVal[2]]['latitude'];
            userList[u_id]['tz'] = city[selectedVal[0]]['sub'][selectedVal[1]]['sub'][selectedVal[2]]['tz'];

            console.log(userList);
        });

        $('.sel_city').on('click', function () {
            picker.show();
            nameEl = $(this);
        });

        //全选checkbox  type="checkbox"
        $(".planets_q").on("click", function () {
            var checked = this.checked;

            $(this).parent('label').next().find("input[type='checkbox']").each(function () {
                if (!$(this).hasClass('will_choose')) {
                    $(this).prop("checked", checked);
                }
            });

        });
    });
</script>


<script>

    $(".form_datetime").datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        initialDate: new Date(),
    })
	var userList={0:{'longitude':121.47,'latitude':31.23,'tz':8,'birthday':'2020-10-08 10:45'},1:{'longitude':121.47,'latitude':31.23,'tz':8,'birthday':'2020-10-08 10:45'}};

    var longitude = '121.47', latitude = '31.23', tz = '8.00', name, h_sys, starsCode, birthday, phase = {};

    var houseData, signData, planetData, svgplanet, SecondData;


    $(document).ready(function () {
        var ajax_post = {'access_token': '989f888c4283e2cc2d8a5aa4af60932c'};

        //星座滑过提示
        $(document).on('mouseenter', '.sign_font', function (e) {
            var sign_id = $(this).attr("serial");
            signInfo = signData[sign_id];
            var sign_html = '';
            var planet_count = 0;
            var sign_planets_html = '';
            if (signInfo['planet_array'] != null) {
                var planet_array = signInfo['planet_array'];
                for (var p in planet_array) {
                    planet_count++;
                    console.log(planet_array[p]);

                    var planet_info = planet_array[p];

                    sign_planets_html += '<span class="must_symbo_font planets_' + planet_info['planet_english'] + '">' + planet_info['planet_font'] + '</span>' +
                        '<span>' + planet_info['planet_chinese'] + '</span>' +
                        '<span class="must_symbo_font  sign_' + planet_info['sign_english'] + '">' + planet_info['sign_font'] + '</span>' +
                        '<span>' + planet_info['sign_chinese'] + '</span>' +
                        '<span> ' + planet_info['deg'] + '°' + planet_info['min'] + '′' + planet_info['sec'] + '″</span><br>';
                }
            } else {
                sign_planets_html = '<span class="">无</span>'
            }
            sign_html += '<div class="tip_sign_content">\n' +
                '<span class="must_symbo_font sign_' + signInfo['sign_english'] + '">' + signInfo['sign_font'] + '</span><b>' + signInfo['sign_chinese'] + '</b> - ' + signInfo['sign_attribute'].join("、") + '<br>' +
                '守护星：' + sign_guardian_html(signInfo['sign_guardian']) + '<br>' +
                '星体数：' + planet_count + '<br>' +
                '详情：<br>' + sign_planets_html;
            sign_html += '</div>';

            showXPTip(e.pageY, e.pageX, sign_html);

        });
        $(document).on('mouseleave', '.sign_font', function () {
            $("#tip_sign_add").hide();
        });


        //宫位滑过提示
        $(document).on('mouseenter', '.house_id', function (e) {
            var house_id = $(this).attr("serial");

            var house_info = houseData[house_id];
            var house_html = '';
            var planet_count = 0;
            var house_planets_html = '';


            if (house_info['planet_array']) {
                var planet_array = house_info['planet_array'];
                for (var p in planet_array) {
                    planet_count++;
                    house_planets_html += '<span class="must_symbo_font planets_' + planet_array[p]['planet_english'] + '">' + planet_array[p]['planet_font'] + '</span>' +
                        '<span>' + planet_array[p]['planet_chinese'] + '</span>' +

                        '<span>第' + house_info['house_id'] + '宫</span>' +
                        '<span> ' + planet_array[p]['deg'] + '°' + planet_array[p]['min'] + '′' + planet_array[p]['sec'] + '″</span><br>';

                }
            } else {
                house_planets_html = '<span class="">无</span>'
            }


            house_html += '<div class="tip_sign_content">\n' +
                '<span>第' + house_info['house_id'] + '宫 </span><b>【' + house_info['house_life'] + '】</b><br>' +

                '宫头：<span class="must_symbo_font sign_' + house_info['sign']['planet_english'] + '">' + house_info['sign']['sign_font'] + '</span>' + house_info['sign']['sign_chinese'] +
                '<span> ' + house_info['sign']['deg'] + '°' + house_info['sign']['min'] + '′' + house_info['sign']['sec'] + '″</span><br>' +
                '宫主星：' + sign_guardian_html(house_info['main_planet']) + '<br>' +
                '星体数：' + planet_count + '<br>' +
                '详情：<br>' + house_planets_html;
            house_html += '</div>';
            showXPTip(e.pageY, e.pageX, house_html);
        });
        $(document).on('mouseleave', '.house_id', function () {
            $("#tip_sign_add").hide();
        });

        //行星滑过提示
        $(document).on('mouseenter', '.planet_font,.guardian_font', function (e) {
            var planet_name = $(this).attr("serial");

            for (var pp in planetData) {
                if (planetData[pp]['planet_english'] == planet_name) {
                    var planet_info = planetData[pp];
                }
            }

            var planet_html = '';

            var planet_planets_html = '';

            if (planet_info == null) {
                console.log(planet_info);

                planet_html = '<span class="">还未选择当前行星</span>';
            } else {
                console.log(planet_info);
                if (planet_info['planet_allow_degree']) {
                    var planet_allow_degree = planet_info['planet_allow_degree'];

                    for (var p in planet_allow_degree) {

                        planet_planets_html += '与<span class="must_symbo_font planets_' + planet_allow_degree[p]['planet_english'] + '">' + planet_allow_degree[p]['planet_font'] + '</span>' +
                            '<span>' + planet_allow_degree[p]['planet_chinese'] + '成</span>' +
                            '<span>' + planet_allow_degree[p]['allow'] + '°</span>' +
                            '<span class="red"> ' + in_out(planet_allow_degree[p]['in_out']) + '</span>' +
                            '<span>（' + planet_allow_degree[p]['deg'] + '°' + planet_allow_degree[p]['min'] + '′' + planet_allow_degree[p]['sec'] + '）</span><br>';

                    }
                } else {
                    planet_planets_html = '<span class="">无</span><br>'
                }

                planet_html += '<div class="tip_sign_content">\n' +
                    '<span class="must_symbo_font planets_' + planet_info['planet_english'] + '">' + planet_info['planet_font'] + ' </span><b>' + planet_info['planet_chinese'] + '</b><span class="red">' + retrograde(planet_info['speed']) + '</span><br>' +

                    '落入星座：<span class="must_symbo_font sign_' + planet_info['sign']['sign_english'] + '">' + planet_info['sign']['sign_font'] + '</span>' + planet_info['sign']['sign_chinese'] +
                    '<span> ' + planet_info['sign']['deg'] + '°' + planet_info['sign']['min'] + '′' + planet_info['sign']['sec'] + '″</span><br>' +

                    '落入宫位：<span class="house_' + planet_info['house_id'] + '">' + planet_info['house_id'] + '</span>宫<span>' + planet_info['house_deg'] + '°' + planet_info['house_min'] + '′' + planet_info['house_sec'] + '″</span><br>' +
                    '相位：(<span class="red">+</span>为入相位，<span>-</span>为出相位，)<br>' +
                    '详情：<br>' + planet_planets_html;

                planet_html += '<span style="color: gray">** 相位后括号内是容许度</span></div>';
            }

            showXPTip(e.pageY, e.pageX, planet_html);
        });
        $(document).on('mouseleave', '.planet_font,.guardian_font', function () {
            $("#tip_sign_add").hide();
        });

        //鼠标滑动提示框
        function showXPTip(y, x, txt) {
            var documentHeight = $(document).height();
            var documentWidth = $(document).width();
            $("#tip_sign_add").empty().append(txt);

            var minWidth = $("#tip_sign_add").outerWidth(true);
            var minHeight = $("#tip_sign_add").outerHeight(true);

            var tip_sign_left = 0;
            var tip_sign_top = 0;

            if (documentWidth < (x + minWidth)) {
                tip_sign_left = x - minWidth;
            } else {
                tip_sign_left = x + 15;
            }

            if (minHeight > y) {
                tip_sign_top = 0;
            } else if (documentHeight < (y + minHeight)) {
                tip_sign_top = y - minHeight;
            } else {
                tip_sign_top = y + 15;
            }

            $("#tip_sign_add").css({top: tip_sign_top, left: tip_sign_left}).show();
        }


        //计算出入相位
        function retrograde(number) {
            if (number > 0) {
                return '<span> [顺行] </span>';
            }
            return '<span> [逆行] </span>';
        }

        //计算出入相位
        function in_out(number) {
            if (number > 0) {
                return '+';
            }
            return '-';
        }

        //守护行星html
        function sign_guardian_html(sign_guardian) {

            var guardian_html = '';
            for (var g in sign_guardian) {
                guardian_html += '<span class="must_symbo_font planets_' + sign_guardian[g]['planet_english'] + '">' + sign_guardian[g]['planet_font'] + '</span>' +
                    '<span class="">' + sign_guardian[g]['planet_chinese'] + '</span>';
            }
            return guardian_html;
        }

        //请求数据
        function getData(ajax_post) {
            $.ajax({
                type: "POST",
                url: window.location.protocol +"//"+window.location.host+"/astrology/chart/marksThirprogr",
                data: ajax_post,
                success: function (result) {
                    if (result.code > 0) {
                        alert(result.msg);
                        return false;
                    }
                    houseData = result.data.house;
                    signData = result.data.sign;
                    planetData = result.data.planet;
                    svg = result.data.svg;
                    $(".box-svg").empty().append(svg);
                },
                error: function (e) {
                    // alert('请求异常');
                }
            });
        }

        //关闭窗口获取
        $('#advSettings').on('hide.bs.modal', function () {

            h_sys = $('.h_sys').val();

            userList[0]['birthday'] = $('.form_datetime').eq(0).val()
            userList[1]['birthday'] = $('.form_datetime').eq(1).val()
            transitday=$('.transitday').val();

            var planets = [];
            var planet_xs = [];
            var virtual = [];

            if(!transitday){
                alert('请先选择推运日期')
                return false;
            }
            $.each($('.phase'), function () {
                if (this.checked) {
                    phase[$(this).val()] = $('.phase_' + $(this).val()).val();
                }
            });
            $.each($('.planet_xs'), function () {
                if (this.checked) {
                    planet_xs.push($(this).val());
                }
            });
            $.each($('.planets'), function () {
                if (this.checked) {
                    planets.push($(this).val());
                }
            });
            $.each($('.virtual'), function () {
                if (this.checked) {
                    virtual.push($(this).val());
                }
            });

            ajax_post['transitday']=transitday;
            ajax_post['h_sys'] = h_sys;
            ajax_post['user_list'] = userList;
            ajax_post['planets'] = planets;
            ajax_post['planet_xs'] = planet_xs;
            ajax_post['virtual'] = virtual;
            ajax_post['phase'] = phase;
            getData(ajax_post);
        })

        //版本切换
        $(".switch_web_ixingpan").on("click", function () {
            var bigt_name = $(this).attr("bigt");

			if(bigt_name == 'web_ixingpan_senior'){
				ajax_post['svg_type']=0;
				getData(ajax_post);
			}else{
				if(ajax_post['svg_type']==null || ajax_post['svg_type']==0){
					ajax_post['svg_type']=1;
					getData(ajax_post);
				}
			}

            $('.text_font').css("font-family", bigt_name);
            if (bigt_name == 'web_ixingpan') {
                $('.planet_font').css("font-size", 24);
                $('.sign_font').css("font-size", 34);
            } else if(bigt_name == 'web_ixingpan_cn') {
                $('.planet_font').css("font-size", 16);
                $('.sign_font').css("font-size", 28);
            }else {
			}

        });
    });

</script>



