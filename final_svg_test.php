<?php
/**
 * 最终 SVG 输出测试
 */

// 包含所有必要文件
require_once 'jyotish/src/Base/Traits/OptionTrait.php';
require_once 'jyotish/src/Base/Traits/GetTrait.php';
require_once 'jyotish/src/Base/Utility.php';
require_once 'jyotish/src/Renderer/AbstractRenderer.php';
require_once 'jyotish/src/Renderer/Svg.php';
require_once 'jyotish/src/Draw.php';

use Jyotish\Draw\Draw;

try {
    // 创建 400x300 像素的 SVG
    $draw = new Draw(400, 300, Draw::RENDERER_SVG);
    
    // 设置全局选项
    $draw->setOptions([
        'fontColor' => '#000080',
        'strokeColor' => '#FF0000',
        'strokeWidth' => 2,
        'fillColor' => '#FFE0E0'
    ]);
    
    // 绘制标题
    $draw->drawText("🎨 SVG setOptions 修复成功!", 200, 40, [
        'fontColor' => '#000080',
        'textAlign' => 'center'
    ]);
    
    // 绘制说明文字
    $draw->drawText("SVG 渲染器现在完全正常", 200, 70, [
        'fontColor' => '#333333',
        'textAlign' => 'center'
    ]);
    
    // 绘制三角形
    $trianglePoints = [
        200, 100,  // 顶点
        160, 160,  // 左下
        240, 160   // 右下
    ];
    $draw->drawPolygon($trianglePoints, [
        'strokeColor' => '#FF0000',
        'strokeWidth' => 3,
        'fillColor' => '#FFE0E0'
    ]);
    
    // 在三角形旁边添加标签
    $draw->drawText("三角形", 260, 130, [
        'fontColor' => '#FF0000'
    ]);
    
    // 绘制矩形
    $rectanglePoints = [
        50, 190,   // 左上
        350, 190,  // 右上
        350, 240,  // 右下
        50, 240    // 左下
    ];
    $draw->drawPolygon($rectanglePoints, [
        'strokeColor' => '#00AA00',
        'strokeWidth' => 2,
        'fillColor' => '#E0FFE0'
    ]);
    
    // 在矩形内部添加文字
    $draw->drawText("SVG setOptions 正常工作!", 200, 215, [
        'fontColor' => '#00AA00',
        'textAlign' => 'center'
    ]);
    
    // 绘制一个复杂的多边形（六边形）
    $hexagonPoints = [
        200, 250,  // 顶
        230, 265,  // 右上
        230, 295,  // 右下
        200, 310,  // 底
        170, 295,  // 左下
        170, 265   // 左上
    ];
    $draw->drawPolygon($hexagonPoints, [
        'strokeColor' => '#8000FF',
        'strokeWidth' => 2,
        'fillColor' => '#F0E0FF'
    ]);
    
    // 添加版权信息
    $draw->drawText("© 2024 SVG 修复版", 350, 280, [
        'fontColor' => '#666666',
        'textAlign' => 'right'
    ]);
    
    // 渲染并输出 SVG
    $draw->render();
    
} catch (Exception $e) {
    // 如果出错，输出错误信息
    header('Content-Type: text/plain');
    echo "生成 SVG 时发生错误: " . $e->getMessage() . "\n";
    echo "错误详情:\n" . $e->getTraceAsString();
}
?>
