<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;
/**
 * 通知公告控制器
 */
class Task extends AdminBase
{

    /**
     * 通知公告列表
     */
    public function list()
    {

        $where = $this->logicTask->getAdminWhere($this->param);

        $data=$this->logicTask->getTaskList($where);

        $this->assign('list',$data);
        $this->assign('type',['1'=>'后台币','2'=>'游戏物品']);

        return $this->fetch('list');
    }
    /**
     * api通知公告无分页列表
     */
    public function taskColumn()
    {

        $data=$this->logicTask->getTaskColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 通知公告添加
     */
    public function add()
    {

        IS_POST && $this->jump($this->logicTask->TaskAmindEdit($this->param));

        $info['start_time']= date("Y-m-d H:i:s");

        $server_column=$this->logicServer->getServerColumn(['status'=>1],'server_id,name,status','server_id');


        $info['end_time']=date('Y-m-d H:i:s',time());
        $this->assign('info', $info);
        $this->assign('type',['1'=>'后台币','2'=>'游戏物品']);
        $this->assign('server_column', $server_column);
        $this->assign('shop_column', $this->logicShop->getShopColumn(['status'=>1],'id,name,itemid'));


        return $this->fetch('edit');
		
    }
    /**
     * 通知公告修改
     */
    public function edit()
    {

        IS_POST && $this->jump($this->logicTask->TaskAmindEdit($this->param));

        $info = $this->logicTask->getTaskInfo(['id' => $this->param['id']], '*');

        $info['end_time']=date('Y-m-d H:i:s',$info['end_time']);

        $server_column=$this->logicServer->getServerColumn(['status'=>1],'server_id,name,status','server_id');
        $img_json_array=explode(',',$info['img_json']);
        $info['img_json_array']=$img_json_array;
        $this->assign('info', $info);
        $this->assign('type',['1'=>'后台币','2'=>'游戏物品']);
        $this->assign('server_column', $server_column);
        $this->assign('shop_column', $this->logicShop->getShopColumn(['status'=>1],'id,name,itemid'));

        return $this->fetch('edit');
    }
    /**
     * 通知公告删除
     */
    public function del($id)
    {
        $this->jump($this->logicTask->taskAdminDel(['id' => $id]));
    }
}
