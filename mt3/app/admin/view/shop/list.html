<div class="box">
	<div class="box-header">
		<div class="row search-form">

			<div class="col-sm-10">
				<div class="row">

					<div class="col-xs-2" style="float: right">
						<div class="input-group input-group-sm">
							<input type="text" name="search_data" class="form-control pull-right"
								   value="{:input('search_data')}" placeholder="">
							<div class="input-group-btn">
								<button type="button" id="search" url="{:url('')}"
										class="btn btn-info btn-flat"><i
										class="fa fa-search"></i></button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<!-- /.box-header -->
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<tr>
				<th>名称</th>
				<th>类型</th>
				<th>热卖</th>
				<th>图</th>
				<th>数量</th>
				<th>限购</th>
				<th class="sorting-event sorting" order-field="price">价格<i class="glyphicon glyphicon-sort"></i></th>
				<th>参与抽奖</th>
				<th>服区</th>
				<th class="sorting-event sorting" order-field="status">状态<i class="glyphicon glyphicon-sort"></i></th>
				<th>操作</th>
			</tr>
			</thead>
			
			{notempty name='list'}
			<tbody>
			{volist name='list' id='vo'}
			<tr>
				<td>{$vo.name}</td>
				<td>{$type[$vo.type]}</td>
				<td>{$vo.hot}</td>
				<td><img class="admin-list-img-size" height="50px" src="/upload/picture/{$vo.image}"></td>
				<td>{$vo.num}</td>
				<td>{$vo.limit_number}</td>
				<td><input type="text" class="sort-th post-text" href="{:url('setPrice')}" id="{$vo.id}" value="{$vo.price}" /></td>
				<td>{$vo.is_draw}</td>
				<td>{$vo.server_id}</td>
				<td><ob_link><a class="ajax-get" href="{:url('setStatus', array('ids' => $vo['id'], 'status' => (int)!$vo['status']))}">{$vo.status_text}</a></ob_link></td>
				<td>
					<ob_link>
						<a href="{:url('edit', array('id' => $vo['id']))}" class="btn btn-xs btn-warning"><i class="fa fa-edit"></i> 编辑</a>
					</ob_link>
					<ob_link>
						<a class="btn btn-xs btn-danger confirm ajax-get" href="{:url('del', array('id' => $vo['id']))}"><i class="fa fa-trash-o"></i> 删除</a>
					</ob_link>
				</td>
			</tr>
			{/volist}
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="8" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
		</table>
	</div>
	{include file="layout/special"/}
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>

</div>