<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<form action="{:url()}" method="post" class="form_single">
    <div class="box">
        <div class="box-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>物品名称</label>
                        <span>（物品名称）</span>
                        <input class="form-control" name="name" placeholder="请输入物品名称"
                               value="{$info['name']|default=''}" type="text">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>游戏id</label>
                        <span>（itemid）</span>
                        <input class="form-control" name="itemid" placeholder="请输入游戏里发送id"
                               value="{$info['itemid']|default=''}" type="text">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>价格</label>
                        <span>（后台币）</span>
                        <input class="form-control" name="price"
                               value="{$info['price']|default='1'}" type="number">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>封面图</label>
                        <span class="">封面图</span>{assign name="image"
                        value="$info.image|default=''" /}
                        {:widget('file/index', ['name' => 'image', 'value' => $image, 'type' => 'img'])}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>类型</label>
                        <span>（类型）</span>
                        <select class="form-control" name="type">

                            {volist name='$type' id='vo'}
                            <option value="{$key}">{$vo}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>描述</label>
                        <span>（描述）</span>
                        <textarea name="info" class="form-control">{$info.info}</textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>物品数量</label>
                        <span>（物品数量）</span>
                        <input class="form-control" name="num"
                               value="{$info['num']|default='1'}" type="number">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>购买限制数量</label>
                        <span>（0是无限制）</span>
                        <input class="form-control" name="limit_number"
                               value="{$info['limit_number']|default='0'}" type="number">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>服区</label>
                        <span>（服区）</span>
                        <select class="form-control" name="server_id">
                            <option value="0">不限制</option>
                            {volist name='server_column' id='vo'}
                            <option value="{$vo.server_id}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>是否热卖</label>
                        <span>（排名靠前）</span>
                        <div>
                            <label class="margin-r-5"><input type="radio" name="hot" value="1"> 是</label>
                            <label><input type="radio" name="hot"  value="0"> 否</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>是否设置抽奖</label>
                        <span>（是否设置抽奖）</span>
                        <div>
                            <label class="margin-r-5"><input type="radio" name="is_draw" value="1"> 是</label>
                            <label><input type="radio" name="is_draw"  value="0"> 否</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>占比概率</label>
                        <span>（设置抽奖才生效）</span>
                        <input class="form-control" name="probability"
                               value="{$info['probability']|default='1'}" type="number">
                    </div>
                </div>
            </div>
        </div>
        <div class="box-footer">
            <input type="hidden" name="id" value="{$info['id']|default='0'}"/>
            {include file="layout/edit_btn_group"/}
        </div>

    </div>

</form>
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});
        ob.setValue("server_id", {$info.server_id |default= 1});
        ob.setValue("type", {$info.type |default= 1});
        ob.setValue("hot", {$info.hot |default= 0});
        ob.setValue("is_draw", {$info.is_draw |default= 0});
    });
</script>