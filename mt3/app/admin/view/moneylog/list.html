<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<div class="box">
	<div class="box-header">
		<div class="row search-form">
			<div class="col-sm-2">
				<button class="btn btn-primary" data-toggle="modal" data-target="#myModal">充值</button>
			</div>
			<div class="col-sm-10">
				<div class="row">
					<div class="col-sm-3">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-primary" type="button">开始时间</button>
						</span>
							<input name="start_time" size="16" type="text"
								   value="{$Think.get.start_time|default=''}" readonly
								   class="form_datetime form-control">
						</div>
					</div>
					<div class="col-sm-3">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-primary" type="button">结束时间</button>
						</span>
							<input name="end_time" size="16" type="text"
								   value="{$Think.get.end_time|default=''}" readonly
								   class="form_datetime form-control">
						</div>
					</div>

					<div class="col-xs-2">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-primary" type="button">类型</button>
						</span>
							<select class="form-control" name="type">
								<option value="all">不限</option>
								{notempty name='money_type'}
								{volist name='money_type' id='vo'}
								<option value="{$key}">{$vo}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>

					<div class="col-xs-2">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-primary" type="button">状态</button>
						</span>
							<select class="form-control" name="status">
								<option value="all">不限</option>
								{notempty name='status'}
								{volist name='status' id='vo'}
								<option value="{$key}">{$vo}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-2" style="float: right">
						<div class="input-group input-group-sm">
							<input type="text" name="search_data" class="form-control pull-right"
								   value="{:input('search_data')}" placeholder="支持用户ID">
							<div class="input-group-btn">
								<button type="button" id="search" url="{:url('')}"
										class="btn btn-info btn-flat"><i
										class="fa fa-search"></i></button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<th>账号</th>
			<th>订单编号</th>
			<th>金额</th>
			<th>实额</th>
			<th>服区</th>
			<th>类型</th>
			<th>状态</th>
			<th>提交时间</th>
			<th>操作</th>
			</thead>
			
			{notempty name='list'}
			<tbody>
			
			{volist name='list' id='vo'}
			<tr>
				<td><a href="{:url('user/list', array('id' => $vo['uid']))}" class="logic_anyes User" logicName="User" logicIndex="id" logicFile="username"
					   logicId="{$vo.uid}" target="_blank">{$vo.uid}</a>
				</td>
				<td>{$vo.order_number}</td>
				<td>{$vo.amount}</td>
				<td>{$vo.price}</td>
				<td>{$vo.server_id}</td>
				<td>{$money_type[$vo.type]|default='未知'}
					{eq name="$vo.type" value="2"}
					 <img class="admin-list-img-size" src="/upload/picture/{$vo.image_url}" width="50px" height="50px">
					{/eq}
				</td>
				<td>{$status[$vo.status]}</td>
				<td>{$vo.create_time}</td>
				<td>
					{eq name="$vo.type" value="2"}
					   {eq name="$vo.status" value="0"}
					    <select id-data="{$vo.id}" class="status_val form-control">
							<option value="0">选择</option>
					    	<option value="-1">驳回</option>
							<option value="1">同意</option>
					    </select>
					   {/eq}
					{/eq}


				</td>
			</tr>
			{/volist}
			
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="7" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
			{include file="layout/special"/}
		</table>
	</div>
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>
</div>
<script type="text/javascript">
	ob.setValue("status","{present name='$_GET["status"]'}{$Think.get.status}{else}all{/present}");
	ob.setValue("type","{present name='$_GET["type"]'}{$Think.get.type}{else}all{/present}");

	$(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});

	//导出功能
    $(".export").click(function(){

        window.location.href = searchFormUrl($(".export"));
    });
</script>

<!-- 模态框（Modal） -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<form action="{:url('topUp')}" method="post" class="form_single">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">充值金额</h4>
				</div>
				<div class="modal-body">

					<div class="col-md-6">
						<div class="form-group">
							<label>用户id</label>
							<span>（请您看清楚，复制id过来）</span>
							<input class="form-control" name="user_id" placeholder="请输入ID" value="" type="number">
						</div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
							<label>后台</label>
							<span>（币）</span>
							<input class="form-control" name="score" placeholder="请输入" value="" type="number">
						</div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
							<label>真实金额</label>
							<span>（元）</span>
							<input class="form-control" name="price" placeholder="请输入" value="" type="number">
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-warning" data-dismiss="modal">关闭</button>
					<button  type="submit" class="btn btn-primary ladda-button ajax-post" data-style="slide-up" target-form="form_single">
						<span class="ladda-label"><i class="fa fa-send"></i> 确 定</span>
					</button>
				</div>
			</form>
		</div><!-- /.modal-content -->
	</div><!-- /.modal -->
</div>
<script type="text/javascript">
	$(document).ready(function(){
		var id=0;
		$(document).on('click', '.status_val', function () {
			id=$(this).attr('id-data');
			console.log(id);
		})

		$(document).on('change', '.status_val', function (event) {
			var status_val = $(this).val();

			console.log(status_val);

			$.ajax({
				url: "{:url('audit')}",
				type: 'post',
				dataType: "json",
				data: {id: id,status_val: status_val},
				success: function (res) {
					obalert(res);
				}
			});
		});
	});
</script>