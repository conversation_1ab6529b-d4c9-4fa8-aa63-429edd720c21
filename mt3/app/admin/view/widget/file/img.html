<link rel="stylesheet" href="/static/widget/admin/file/Huploadify.css">
    
<div id="upload_picture_{$widget_data['name']}"></div>

<input type="hidden" name="{$widget_data['name']}" id="{$widget_data['name']}" value="{$widget_data['value']}"/>

<div class="upload-img-box{$widget_data['name']}">
    {notempty name="$info[$widget_data['name']]"}
    <div class="upload-pre-item">
        
        <div style="cursor:pointer;" class="pic_del"  onclick="picDel{$widget_data.name}(this)" ><img src="/static/widget/admin/file/uploadify-cancel.png" /></div> 
        
        <a target="_blank"
                                    href="/upload/picture/{$info[$widget_data['name']]|default='0'}"><img
            style="max-width: {$widget_config['maxwidth']};"
            src="/upload/picture/{$info[$widget_data['name']]|default='0'}"/></a></div>
    {/notempty}
</div>

<script src="/static/widget/admin/file/jquery.Huploadify.js"></script>
<script src="/static/module/common/util/hex_sha1.js"></script>

<script type="text/javascript">

    var maxwidth = "{$widget_config['maxwidth']}";

    $("#upload_picture_{$widget_data.name}").Huploadify({
        auto: true,
        height: 30,
        fileObjName: "file",
        buttonText: "上传图片",
        uploader: "{:url('File/pictureUpload',array('session_id'=>session_id()))}",
        width: 120,
        removeTimeout: 1,
        fileSizeLimit:"{$widget_config['max_size']}",
        fileTypeExts: "{$widget_config['allow_postfix']}",
        onChange:changeFile{$widget_data.name},
        onUploadComplete: uploadPicture{$widget_data.name}
    });

    function uploadPicture{$widget_data.name}(file, data)
    {

        var data = $.parseJSON(data);

        $("#{$widget_data['name']}").val(data.path);

        var src = !data['url'] ? "__ROOT__/upload/picture/" + data.path : data.url;

        $(".upload-img-box{$widget_data['name']}").html('<div class="upload-pre-item">    <div style="cursor:pointer;" class="pic_del"  onclick="picDel{$widget_data.name}(this)" ><img src="/static/widget/admin/file/uploadify-cancel.png" /></div>        <a target="_blank" href="' + src + '"> <img style="max-width: ' + maxwidth + ';" src="' + src + '"/></a></div>');
    }
    
    function picDel{$widget_data.name}(obj)
    {
        
        var widget_name = "{$widget_data.name}";
        
        $("#" + widget_name).val(0);
        
        $(obj).parent().remove();
    }

    function changeFile{$widget_data.name}(file,fileCount,next) {
        var reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = function (ev) {
            var sha1 = hex_sha1(ev.target.result);
            $.post("{:url('File/checkPictureExists')}",{sha1:sha1}, function (res) {
                if(res.code) {
                    uploadPicture{$widget_data.name}(file,JSON.stringify(res.data));
                }else {
                    //不存在图片则调用下一步
                    next(file,fileCount);
                }
            });
        }
    }
</script>