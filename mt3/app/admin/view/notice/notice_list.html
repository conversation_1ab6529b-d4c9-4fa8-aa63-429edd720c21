<div class="box">
	<div class="box-header">
		<ob_link><a class="btn btn-primary" href="{:url('noticeAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
		<br/>
	</div>
	
	<!-- /.box-header -->
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<tr>
				<th>名称</th>
				<th>类型</th>
				<th>描述</th>
				<th>内容</th>
				<th>发布时间</th>
				<th>操作</th>
			</tr>
			</thead>
			
			{notempty name='list'}
			<tbody>
			{volist name='list' id='vo'}
			<tr>
				<td>{$vo.title}</td>
				<td>{$vo.type}</td>
				<td>{$vo.describe}</td>
				<td>{$vo.content}</td>
				<td>{$vo.content}</td>
				<td>{$vo.author}</td>
				<td>{$vo.start_time|date="Y-m-d H:i",###}</td>
				<td>
					<ob_link><a href="{:url('noticeEdit', array('id' => $vo['id']))}" class="btn btn-xs btn-warning"><i
							class="fa fa-edit"></i> 编辑</a></ob_link>
					<ob_link><a class="btn btn-xs btn-danger confirm ajax-get"
					            href="{:url('noticeDel', array('id' => $vo['id']))}"><i class="fa fa-trash-o"></i> 删
						除</a></ob_link>
				</td>
			</tr>
			{/volist}
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="8" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
		</table>
	</div>
	{include file="layout/special"/}
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>

</div>