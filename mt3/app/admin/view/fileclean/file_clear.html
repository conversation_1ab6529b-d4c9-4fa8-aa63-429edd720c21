<div class="callout callout-warning">
    <h4>注意：（文件清理）点击开始清理后系统目录upload下的图片与文件没有与图片或文件数据表关联的会被自动清理掉</h4>
    <h4>注意：（数据清理）点击开始清理后系统会自动清理数据表（若没有在设置 系统管理->系统设置->图片字段配置 文件数据会被清理掉）</h4>
</div>

<div class="box">
  <div class="box-header">
    
    <ob_link><a id="file_clear" class="btn btn-danger" url="{:url('cleanList')}"><i class="fa fa-close"></i> 开始清理</a></ob_link>
    
  </div>
    
  <div class="box-body table-responsive">
    <table  class="table table-bordered table-hover table-striped">
      <thead>
        <tr>
            <th>文件路径</th>
            <th>文件大小</th>
            <th>创建时间</th>
        </tr>
      </thead>
      
      {notempty name='list'}
        <tbody>
            {volist name='list' id='vo'}
                <tr>
                    <td>{$vo.file_path}\{$vo.file_name}</td>
                    <td>{$vo.file_size}</td>
                    <td>{$vo.file_ctime}</td>
                </tr>
            {/volist}
        </tbody>
        {else/}
        <tbody><tr class="odd"><td colspan="3" class="text-center" valign="top">{:config('empty_list_describe')}</td></tr></tbody>
      {/notempty}
    </table>
  </div>
</div>

<script type="text/javascript">

    function sendUrlRequest(id)
    {
        $('.fakeloader').show();

            $.post( $("#" + id).attr("url"),{}, function(data){

                $('.fakeloader').hide();

                obalert(data);

            },"json"
        );
    }
    
    $("#file_clear").click(function(){ sendUrlRequest('file_clear'); });
</script>