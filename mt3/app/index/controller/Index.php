<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

namespace app\index\controller;
use app\common\controller\ControllerBase;
/**
 * 前端首页控制器
 */
class Index extends ControllerBase
{

    // 首页
    public function index($cid = 0)
    {
        $html_title = '首页';
        $ShopList = $this->logicShop->getShopColumn(['status' => 1], 'id,name,info,img_id,image,price,num');
        // {"file":"itemicon9.png","x":"0","y":"64","w":"64","h":"64"}
        $notice_new = $this->logicNotice->getNoticeInfo(['red' => 1], 'title,describe,content');
        $this->assign('notice_new', $notice_new);
        $this->assign('shop_list', $ShopList);
        $this->assign('userAuth_sign', !empty(session('userAuth_sign')) ?session('userAuth_sign'):'0');
        $this->assign('html_title', $html_title);
        return $this->fetch('index');
    }
    public function index2()
    {
        $User = $this->logicUser->getUserInfo(['id' => UID]);

        $where['vip']=['gt',$User['vip']];

        $ShopList = $this->logicShop->getShopList($where, 'id,name,info,img_id,image,price');
        
        $this->assign('html_title', $html_title);

        return $this->fetch('index2');
    }

    //杂物列表
    public function itemlist()
    {
        $param = $this->param;
        $html_title = '武器';
        if(empty($param['type'])){
            $html_title = '搜索';
        }else{
            if ($param['type'] == 6) {
                $html_title = '宝宝';
            }
        }

        !empty($param['search']) && $this->assign('search', $param['search']);

        $this->assign('html_title', $html_title);
        $this->assign('userAuth_sign', !empty(session('userAuth_sign')) ?session('userAuth_sign'):'0');
        $this->assign('param', $param);
        return $this->fetch('itemList');
    }

    //杂物获取数据
    public function itemlistajx()
    {
        $param = $this->param;



        !empty($param['search']) && $where['name']= ['like','%'.$param['search'].'%'];

        !empty($param['type']) && $where['type']= $param['type'];

        $ShopList = $this->logicShop->getShopList($where, 'id,name,info,img_id,image,price');

        return json($ShopList);
    }

    //杂物详细
    public function itemInfo()
    {
        $html_title = '物品详细';
        $param = $this->param;

        $ShopInfo = $this->logicShop->getShopInfo(['id' => $param['id']], 'id,name,info,img_id,image,price');

        $this->assign('html_title', $html_title);
        $this->assign('info', $ShopInfo);
        return $this->fetch();
    }

    public function newNotice()
    {
        $html_title = '物品详细';
        $param = $this->param;

        $ShopInfo = $this->logicShop->getShopInfo(['id' => $param['id']], 'id,name,info,img_id,image,price');

        $this->assign('html_title', $html_title);
        $this->assign('info', $ShopInfo);
        return $this->fetch();
    }

}
