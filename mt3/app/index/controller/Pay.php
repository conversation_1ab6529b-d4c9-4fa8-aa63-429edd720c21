<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;

/**
 * 前端首页控制器
 */
class Pay extends IndexBase
{

    //享支付
    public function codewoPay()
    {
        //pid={商户ID}&type={支付方式}&out_trade_no={商户订单号}&notify_url={服务器异步通知地址}&return_url={页面跳转通知地址}&name={商品名称}&money={金额}&sitename={网站名称}&sign={签名字符串}&sign_type=MD5

        $param = $this->param;
        $key = '5tlyN8tptRqfM7DNtEfJAajnMe6Rnhx3NovB1gOFhM2pKsQwi1vJgX6js7NRmtqcsAGhAfCvxyiC731vXgxpFz3x38FivnVuhhOY9yTgZGfUqNc3bnII2XuduImYYJ5M';

        $type_array = ['alipay', 'qqpay', 'wxpay'];
        if (!in_array($param['pay_type'], $type_array)) {
            return json(['code' => 1006, 'msg' => '当前充值类型不存在']);
        }
        if ($param['price'] < 1) {
            return json(['code' => 1021, 'msg' => '充值金额不能小于1']);
        }
        $msg = '下单成功，正在去支付';
        $regi_url = '';
        if ($param['type'] == 2) {
            $msg = '提交成功，请等待系统审批';
            $out_trade_no = 'X' . UID . 'T' . time() . 'R' . rand(1000, 9999);
            if ($param['image_url'] < 1) {
               // return json(['code' => 1022, 'msg' => '转账凭证不能为空']);
            }
            $MoneyLogInfo = $this->logicMoneyLog->getMoneyLogInfo(['uid' => UID, 'type' => 2, 'server_id' => session('userAuth')['server_id'], 'status' => 0]);
            if (!empty($MoneyLogInfo)) {
                return json(['code' => 1023, 'msg' => '您已经有一笔在审核，请耐心等待，或联系收款人']);
            }
            if ($param['image_url'] >= 1) {
               // return json(['code' => 1022, 'msg' => '转账凭证不能为空']);
               $MoneyLogData['image_url'] = $param['image_url'];
            }
            //$MoneyLogData['pic'] = $param['image_url'];
        } else {
            $out_trade_no = 'S' . UID . 'T' . time() . 'R' . rand(1000, 9999);
            //构造要请求的参数数组，无需改动


            $parameter = [
                'appId' => '659f6793e4b09f6f19620ca5',
                'mchNo' => 'M1704945554',
                "mchOrderNo" => $out_trade_no,
                "amount" => $param['price']*100,
                'subject' => '云盘vip支付',
                'body' => '云盘vip支付',
                'currency' => "cny",
                "notifyUrl" => input('server.REQUEST_SCHEME') . '://' . input('server.SERVER_NAME') . url('index/Common/notify_url'),
                "returnUrl" => input('server.REQUEST_SCHEME') . '://' . input('server.SERVER_NAME') . url('index/User/index'),
                'mbrTel'=>'13042766592',
                'signType' => "MD5",
                'clientIp' => getIP(),
                'reqTime' => time() * 1000,
                "version" => "1.0"
            ];


            $parameter['sign'] = getSign($parameter, $key); 
            $httpheader[] = 'Content-Type: application/json'; 


            $regit = sendPostRequest('http://paygateway.wanbaojufu.com/api/pay/unifiedOrderH5', $parameter); 
//        https://pay.codewo.cn//Pay/console?trade_no=Y2023042702062537688

//        <script>window.location.href='/Pay/console?trade_no=Y2023042702062537688';</script>

           $regit_array=json_decode($regit,true);



            if ($regit_array) {
     
                if ($regit_array['msg'] == 'SUCCESS') {
           
                    $regi_url = $regit_array['data'];
                }
            } else {
                return json(['code' => 1023, 'msg' => '异常，请线下支付']);
            }
        }

        $MoneyLogData['order_number'] = $out_trade_no;
        $MoneyLogData['type'] = $param['type'];
        $MoneyLogData['pay_type'] = $param['pay_type'];
        $MoneyLogData['price'] = $param['price'];
        $MoneyLogData['amount'] = $param['price'] * config('ext_config.price_rate');
        $MoneyLogData['uid'] = UID;
        $MoneyLogData['server_id'] = session('userAuth')['server_id'];
        $MoneyLogData['status'] = 0;

        $MoneyLogEdit = $this->logicMoneyLog->MoneyLogEdit($MoneyLogData);

        if ($MoneyLogEdit) {
            return json(['code' => 0, 'msg' => $msg, 'url' => $regi_url]);
        }
        return json(['code' => 1023, 'msg' => '充值异常，请联系管理']);
    }


}
