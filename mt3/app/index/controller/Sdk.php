<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;

use app\common\controller\ControllerBase;

/**
 * 前端首页控制器
 */
class Sdk extends ControllerBase
{


    public function user()
    {
        $model = $_REQUEST;
        $sjinput = base64_decode(file_get_contents('php://input'));
       $ip = getIP();
     $param=$this->param;

        // file_put_contents('test1.txt', date('Y-m-d H:i:s',time()).' ------sjinput---- '.$ip.' -----'.$sjinput . '----\r\n\t'. PHP_EOL, FILE_APPEND);
 
       // file_put_contents('test1.txt', $model["s"] . '----2222\r\n\t'. PHP_EOL, FILE_APPEND);

        if(strpos($model["s"],'get_proble') !== false){
            $result = base64_encode('{"status":0,"balance":"0","bind_balance":"0","return_msg":"你在干嘛呢"}');
        }else if(strpos($model["s"],'user_logi') !== false){
            $result = $this->login(json_decode($sjinput,true));
        }else if(strpos($model["s"],'user_registe') !== false){
            $result = $this->registe(json_decode($sjinput,true));
        }else if(strpos($model["s"],'user_phone_registe') !== false){
            $result = $this->registe(json_decode($sjinput,true));
        }else if(strpos($model["s"],'user_platform_coi') !== false){
            $result = base64_encode('{"status":0,"balance":"0","bind_balance":"0","return_msg":"你在干嘛呢"}');
        }else if(strpos($model["s"],'send_sm') !== false){
            $result = base64_encode('{"status":0,"return_code":"fail","return_msg":"填写任意验证码注册"}');
        }else if(strpos($model["s"],'user_inf') !== false){
            $result = base64_encode('{"status":-1,"balance":"0","bind_balance":"0"}');
        }else{
            $result = base64_encode('{"status":0,"balance":"0","bind_balance":"0","return_msg":"你在干嘛呢-无人区"}');
        }
        exit($result);
    }
    //进入游戏验证
    //{"account":"cczsdk@4@@cczsdk","password":"123456"}
    public function mt3token()
    {
        
 $result = '{"Code":"1","Channel":"1","PlatformId":"1","Message":"通过"}';
 exit($result);
       $get_params = $_GET;
$param=array();
// file_put_contents('test.txt', json_encode($param) . '----11111\r\n\t'. PHP_EOL, FILE_APPEND);
  // 使用循环遍历GET参数
      foreach ($get_params as $key => $value) {
          $key=str_replace("amp;", "", $key);
          $param[$key]=$value;
       
          
      }
 
        if(empty($param['account'])){
             $result = '{"Code":"2","Channel":"1","PlatformId":"1","Message":"密码错误!"}';
        }else{
        if(strpos($param['account'],'renji') !== false){
            $result = '{"Code":"1","Channel":"1","PlatformId":"1","Message":"通过"}';
        }else{
            $username_array=explode("@",$param['account']);
            
            $password_array=explode("|",$param['password']);
            $username=$username_array[1];
        
          
            $User = $this->logicUser->getUserInfo(['id' => $username]);
           // file_put_contents('test.txt', json_encode($param) . '----11111\r\n\t'. PHP_EOL, FILE_APPEND);
            if(empty($User)){
                $result ='{"Code":"2","Channel":"1","PlatformId":"1","Message":"账号不存在"}';
            }else{
                if (strpos($param['password'], $User['pass']) !== false) {
                  $result = '{"Code":"1","Channel":"1","PlatformId":"1","Message":"通过"}';
                } else {
                   $result = '{"Code":"2","Channel":"1","PlatformId":"1","Message":"密码错误1!"}';
                }
            }
           }
        }
        exit($result);
    }

    // 注册
    //{"account":"366439","game_appid":"0F500D9B7A5D29AE5","game_id":"20","game_name":"我叫MT3","password":"123456","promote_account":"自然注册","promote_id":"0","md5_sign":"4d231342d0ad2e6f658fe2f3b65d8689"}
    public function registe($sjinput)
    {
 
        if(strlen($sjinput['account'])<6){
            return base64_encode('{"status":0,"return_code":"fail","return_msg":"账号长度不能小于6位"}');
        }
        if(strpos($sjinput['account'],'renji') !== false){
            return base64_encode('{"status":0,"return_code":"fail","return_msg":"账号不能带有敏感字符"}');
        }
        if(strlen($sjinput['password'])<6){
            return base64_encode('{"status":0,"return_code":"fail","return_msg":"密码长度不能小于6位"}');
        }

        $data['ip'] = getIP();

        $UserCount = $this->logicUser->getUserStat(['ip' => $data['ip'], 'create_time' => ['gt', (time() - 86400 * 2)]], 'count', 'id');
        if ($UserCount > 5) {
            $result = base64_encode('{"status":0,"return_code":"fail","return_msg":"注册失败，一天内您已注册' . 5 . '个"}');

        }else{
            $User = $this->logicUser->getUserInfo(['username' => $sjinput['account']]);
            if(!empty($User)){
                $result = base64_encode('{"status":0,"return_code":"fail","return_msg":"注册失败，帐号已存在"}');
            }else{

                $data['username'] = $sjinput['account'];
                $data['pass'] = $sjinput['password'];
                $data['game_id'] = $sjinput['game_id'];
                $data['promote_account'] = $sjinput['promote_account'];

                $data['pic'] = rand(1, 19);
                $this->logicUser->userEdit($data);
                $result = base64_encode('{"status":1,"return_code":"success","return_msg":"注册成功"}');
            }
        }
        return $result;
    }
    // 登录
    public function login($sjinput)
    {
        //{"account":"maoniuwq","game_appid":"0F500D9B7A5D29AE5","game_id":"20","game_name":"我叫MT3","password":"********","sdk_version":"0","md5_sign":"5468e6b1b26785847a1632f7bafd96da"}

        $ip = getIP();

        $UserCount = $this->logicUser->getUserStat(['ip' => $ip, 'last_time' => ['gt', (time() - 3600 * 2)]], 'count', 'id');
        if ($UserCount > 5) {
         // return base64_encode('{"status":0,"return_code":"fail","return_msg":"两个小时只能登录5个"}');

        }

        $User = $this->logicUser->getUserInfo(['username' => $sjinput['account']]);

        if(empty($User)){
            $result = base64_encode('{"status":-1,"return_code":"fail","return_msg":"登录失败，帐号不存在"}');
        }else{
            if($User['pass'] != $sjinput['password']){
                $result = base64_encode('{"status":-1,"return_code":"fail","return_msg":"登录失败，密码错误"}');
            }else{
                $this->logicUser->userEdit(['id'=>$User['id'],'last_time'=>time()]);
                $result = base64_encode('{"status":1,"return_code":"success","return_msg":"登录成功","user_id":"'.$User['id'].'","token":"'.$User['pass'].'","user_account":"'.$User['username'].'"}');
            }
        }

        //file_put_contents('test.txt','{"status":1,"return_code":"success","return_msg":"登录成功1","uid":"'.$User['id'].'","password":"'.$User['pass'].'","username":"'.$User['username'].'"}' . '----11111\r\n\t'. PHP_EOL, FILE_APPEND);
     
        exit($result);
    }

}
