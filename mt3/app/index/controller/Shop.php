<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;
/**
 * 前端首页控制器
 */
class Shop extends IndexBase
{


    public function goOrder()
    {
        $param=$this->param;
        $user_ServersInfo = $this->logicUserServers->getUserServersInfo(['uid' => UID, 'server_id' => session('userAuth')['server_id']]);

        $ShopInfo = $this->logicShop->getShopInfo(['id' => $param['id']], 'id,name,info,img_id,img_json,price,itemid,num');


        if($ShopInfo['price']>$user_ServersInfo['money']){
            return ['code' => 10245, 'msg' => '霄币不够，先请赚取币或充值'];
        }
        $this->logicOrder->orderEdit(['sid' => $param['id'],'uid' => UID,'money' =>  $ShopInfo['price'],'itemid' =>  $ShopInfo['itemid'],'server_id' =>$user_ServersInfo['server_id']]);

        $this->logicMoneyLog->MoneyLogEdit(['uid' => UID, 'server_id' => session('userAuth')['server_id'],'amount' => $ShopInfo['price'],'type' =>4,'status' =>1]);

        $this->logicUserServers->setUserServersIncDec(['id'=>$user_ServersInfo['id']], 'money', $ShopInfo['price'], 'setDec');

        $ServersInfo = $this->logicServer->getServerInfo(['server_id' =>$user_ServersInfo['server_id']], 'id,server_id,port');

        //$gmcmd='addbinditem ' . $ShopInfo['itemid'] . ' ' . $ShopInfo['num'];

        $gmcmd ='mail ' . $user_ServersInfo['game_id'] . ' 后台购买 '.$ShopInfo['name'].' 0 ' . $ShopInfo['itemid'] . '|' . $ShopInfo['num'];

        $fsdf=curl_request(config('game_cmd').'/cmd/postcmd.php',['game_id'=>$user_ServersInfo['game_id'],'type'=>'jmxc','port'=>$ServersInfo['port'],'gmcmd'=>$gmcmd]);

        return ['code' => 0, 'msg' => '购买成功'];
    }
}
