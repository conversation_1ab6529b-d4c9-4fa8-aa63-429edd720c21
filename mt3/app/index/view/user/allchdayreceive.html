<script src="__COMMON__/layer/layer.js"></script>
<link rel="stylesheet" type="text/css" href="__STATIC__/timeaxis/timeAxis1.css">
<div class="header">
    <a href="javascript:history.back();"><i class="iconfont">&#xe697;</i></a>
    <h4>每日累积领取</h4>
    <a href="{:url('allchReceive')}" class="sousuo">充值累积</a>
</div>

<div class="cx-time-main" id="cxTime" style="margin-top:50px;">
    <ul class="cx-time-box">
    </ul>
</div>

<script type="text/javascript" src="__STATIC__/js/jquery-3.6.4.js"></script>
<script type="text/javascript" src="__STATIC__/timeaxis/timeAxis1.js"></script>
<script type="text/javascript">
    var oTimeAxiosFun = null;
    $(function () {
        var param = {
            data: {$allch_day_list}, // 数据
            id: 'cxTime', //dom节点ID
            index: 2, // 选中时间节点
            sort: 'just', // 正序just，反序back, 其他表示不排序
            sortKey: 'time', //排序的参数key
            // activeColor: '#26a69a', // 选中颜色
            props: [{'key':'lines','color':'red'},{'key':'info'}], //展示的key
            then: function (data) {
                console.log(data);
            }, //点击事件回调方法
            // format: function (key, val) {
            // 	console.log(key, val);
            // 	return val
            // }, //数据格式化处理
        };
        oTimeAxiosFun = new oTimeAxios(param);
    })
    $(document).ready(function(){


        $(".submit").click(function(){

            var lines=$(this).attr('lines');
            if(lines == null || lines== ''){

                return false;
            }
            $.ajax({
                url: "{:url('')}",
                type: 'post',
                dataType: "json",
                data: {lines:lines},
                success: function (res) {
                    if(res.code>0){
                        layer.alert(res.msg, {
                            icon: 5,
                            title: "提示"
                        });
                    }else if(res.code==0){
                        layer.msg(res.msg, {
                            icon: 1, time: 1500, end: function () {
                                window.location.href = "{:url('')}";
                            }
                        });
                    }
                }
            });
        });
    })
</script>