<script src="__COMMON__/layer/layer.js"></script>
<div class="header">
    <a href="javascript:history.back();"><i class="iconfont">&#xe697;</i></a>
    <h4>密码修改</h4>
    <a href="{:url('kmsLog')}" class="sousuo">记录</a>
</div>
<div class="shezhi-content">
    <div class="shezhi-nr">
        <a href="javascript:;">
            <div class="xzyhk-nr">
                <div class="czsq-nr xzyhknr">
                    <span>账号</span>
                    <p>{$user.username}</p>
                </div>
            </div>
        </a>
    </div>
    <div class="shezhi-nr">
        <div class="xzyhk-nr">
            <div class="czsq-nr xzyhknr ">
                <span>旧密码</span>
                <input type="password" class="oldpass" name="km" placeholder="请输入旧密码">
            </div>
        </div>
    </div>

    <div class="shezhi-nr">
        <div class="xzyhk-nr">
            <div class="czsq-nr xzyhknr ">
                <span>新密码</span>
                <input type="password" class="pass" name="km" placeholder="请输入密码">
            </div>
        </div>
    </div>
    <div class="shezhi-nr">
        <div class="xzyhk-nr">
            <div class="czsq-nr xzyhknr xzyhknr2">
                <span>新密码</span>
                <input type="password" class="passwordcerify" name="km" placeholder="请再次输入确认密码">
            </div>
        </div>
    </div>

    <div class="tjxdz">
        <a href="javascript:;">
            <div class="login-nr-dl">
                <input type="button" name="" id="duihuan" value="修改">
            </div>
        </a>
    </div>
</div>

<script type="text/javascript">

    $(document).ready(function(){

        $("#duihuan").click(function(){

            var oldpass=$('.oldpass').val();
            var pass=$('.pass').val();
            var passwordcerify=$('.passwordcerify').val();
            if(oldpass == null || oldpass== ''){
                layer.msg('旧密码不能为空');
                return false;
            }
            if(pass == null || pass== ''){
                layer.msg('密码不能为空');
                return false;
            }
            if(passwordcerify != pass){
                layer.msg('重复密码不正确');
                return false;
            }
            $.ajax({
                url: "{:url('')}",
                type: 'post',
                dataType: "json",
                data: {oldpass: oldpass,pass: pass,passwordcerify: passwordcerify},
                success: function (res) {
                    if(res.code>0){
                        layer.alert(res.msg, {
                            icon: 5,
                            title: "提示"
                        });
                    }else if(res.code==0){
                        layer.msg(res.msg, {
                            icon: 1, time: 1500, end: function () {
                                window.location.href = "{:url('login/login')}";
                            }
                        });
                    }
                }
            });
        });
    });

</script>