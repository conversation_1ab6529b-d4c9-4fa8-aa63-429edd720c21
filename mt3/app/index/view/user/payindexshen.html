<style>
    .bohui{
        color: aliceblue;
        padding: 2px 4px;
        border-radius: 3px;
        background-color: red;
    }
    .daishen{
        color: aliceblue;
        padding: 2px 4px;
        border-radius: 3px;
        background-color: #c87f0a;
    }
    .cheng<PERSON>{
        color: aliceblue;
        padding: 2px 4px;
        border-radius: 3px;
        background-color: green;
    }
</style>
<div class="header">
    <a href="javascript:history.back();"><i class="iconfont">&#xe697;</i></a>
    <h4>后台币充值订单</h4>
</div>
<div class="wdlw-content">
    <div class="wdzs-nr">
        <ul>
            <li class="">订单号/金额</li>
            <li class="">后台币(赠)</li>
            <li class="">状态</li>
        </ul>
    </div>

</div>
<div class="wdlw-nr">
    <img style="display:none;" src="__STATIC__/picture/wdlw1.png">
    <p>当前列表为空</p>
</div>


<script type="text/javascript">
    var page = 1;
    var last_page = 99;
    var type = 1;


    var money_status = {'-1':'<span class="bohui">驳回</span>','0':'<span class="daishen">待审批</span>','1':'<span  class="chenggong">已审核</span>'};
    getListData();
    function getListData() {
        $.ajax({
            url: "{:url('payIndexShenLog')}",
            type: 'get',
            dataType: "json",
            data: {list_rows:20, page: page},
            success: function (res) {
                console.log(res);
                last_page = res.last_page;
                var list = res.data;

                var html_t='';
                for (var i in list) {
                    html_t += '<div class="yqg-nr-nr5">\n' +
                        '        <div class="yqg-nr-nr5-1">\n' +
                        '            <ul>\n' +
                        '                <li class="nr5-1li01">\n' +list[i]['order_number']+
                        '                </li>\n' +
                        '                 <li class="nr5-1li02">\n' +
                        '                     <span type="button" name="" id="" value="" >'+money_status[list[i]["status"]]+'</span>\n' +
                        '                </li>\n' +
                        '            </ul>\n' +
                        '        </div>\n' +
                        '        <div class="yqg-nr-nr5-2">\n' +
                        '            <ul>\n' +
                        '                <li class="nr5-2li01"><p>' + list[i]['price']+'米</p></li>\n' +
                        '                <li class="nr5-2li02">' + list[i]['amount']+'<span style="color:#999">('+list[i]['gift_amount']+')</li>\n' +
                        '                <li class="nr5-2li03"></li>\n' +
                        '            </ul>\n' +
                        '        </div>\n' +
                        '    </div>';
                }
                $('.wdlw-content').append(html_t);
                if (page  >= last_page) {
                    $('.wdlw-nr').children('p').text('没有更多了');
                }

                if(res.total<1){
                    $('.wdlw-nr').children('img').show();
                    $('.wdlw-nr').children('p').text('当前列表为空');
                }
                page++;
                console.log(page);
            }
        });
    }
</script>
