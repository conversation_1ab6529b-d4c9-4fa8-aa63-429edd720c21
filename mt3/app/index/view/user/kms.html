<script src="__COMMON__/layer/layer.js"></script>
<div class="header">
    <a href="javascript:history.back();"><i class="iconfont">&#xe697;</i></a>
    <h4>兑换码中心</h4>
    <a href="{:url('kmsLog')}" class="sousuo">记录</a>
</div>
<div class="shezhi-content">
    <div class="shezhi-nr">
        <a href="javascript:;">
            <div class="xzyhk-nr">
                <div class="czsq-nr xzyhknr">
                    <span>账号</span>
                    <p>{$user.username}</p>
                </div>
            </div>
        </a>
    </div>

    <div class="shezhi-nr">
        <a href="javascript:;">
            <div class="xzyhk-nr">
                <div class="czsq-nr xzyhknr xzyhknr2">
                    <span>当前区</span>
                    <p>{$user.server_str}</p>
                </div>
            </div>
        </a>
    </div>

    <div class="shezhi-nr">
        <div class="xzyhk-nr matop2">
            <div class="czsq-nr xzyhknr xzyhknr2">
                <span>卡 密</span>
                <input type="text" class="km" name="km" placeholder="请输入卡密">
                <i class="iconfont chaxun">&#xe6c8;</i>
            </div>
        </div>
    </div>

    <div class="shezhi-nr">
        <div class="xzyhk-nr matop2">
            <div class="czsq-nr xzyhknr xzyhknr2">
                <p class="jiage-dd" style="font-size: 1em;"></p>
            </div>
        </div>
    </div>

    <div class="tjxdz">
        <a href="javascript:;">
            <div class="login-nr-dl">
                <input type="button" name="" id="duihuan" value="兑换">
            </div>
        </a>
    </div>
</div>

<script type="text/javascript">

    $(document).ready(function(){


        $(".chaxun").click(function(){

            var km=$('.km').val();
            if(km == null || km== ''){
                layer.msg('卡密不能为空');
                return false;
            }
            $.ajax({
                url: "{:url('kmscha')}",
                type: 'post',
                dataType: "json",
                data: {km:km},
                success: function (res) {
                    $('.jiage-dd').html(res.msg);
                }
            });
        });

        $("#duihuan").click(function(){

            var km=$('.km').val();
            if(km == null || km== ''){
                layer.msg('卡密不能为空');
                return false;
            }
            $.ajax({
                url: "{:url('')}",
                type: 'post',
                dataType: "json",
                data: {km: km},
                success: function (res) {
                    if(res.code>0){
                        layer.alert(res.msg, {
                            icon: 5,
                            title: "提示"
                        });
                    }else if(res.code==0){
                        layer.msg(res.msg, {
                            icon: 1, time: 1500, end: function () {
                                window.location.href = "{:url('user/index')}";
                            }
                        });
                    }
                }
            });
        });
    });

</script>