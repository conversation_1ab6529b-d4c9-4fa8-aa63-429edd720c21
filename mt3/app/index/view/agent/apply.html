<div class="header">
    <a href="javascript:history.back();"><i class="iconfont">&#xe697;</i></a>
    <h4>代理申请</h4>
</div>

<div class="wdlw-content" >

    <div class="wddd-nr no-yes-lan" style="padding: 0; margin-top: 0;">

        <section class="aui-flexView">
            <section class="aui-scrollView">

                <div class="aui-title-main">
                    <h2>
                        微信收款码 <em>微信里保存自己收款码，(不要截图，不然图太大)</em></i>
                    </h2>
                </div>
                <div class="aui-upload-box">
                    <div class="aui-upload-pic">
                <span id="chose_pic_btn" style="">
                    <input type="file" class="shangchuanclass" accept="image/*">
                </span>
                    </div>
                </div>

                <div class="aui-title-main">
                    <h2>
                        支付宝收款码 <em> 支付宝里保存自己收款码，(不要截图，不然图太大)</em></i>
                    </h2>
                </div>
                <div class="aui-upload-box">
                    <div class="aui-upload-pic">
                <span id="chose_pic_btn" style="">
                    <input type="file"  class="shangchuanclass" accept="image/*">
                </span>
                    </div>
                </div>

                <div class="aui-main-button">
                    <button class="tijiao">提交</button>
                </div>
            </section>
        </section>

    </div>
</div>

<div id="outerdiv"
     style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src=""/>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="__STATIC__/uppic/base.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/uppic/home.css">
<script src="__STATIC__/uppic/localImg.js"></script>
<script src="__STATIC__/uppic/mobileImg.js"></script>
<script src="__COMMON__/layer/layer.js"></script>

<script type="text/javascript">

    layer.alert('不得乱提交申请', {
        icon: 5,
        title: "提示"
    });
    $(document).ready(function () {
        $(".tijiao").click(function () {

            if (!image_url[0]) {
                layer.alert('微信收款码不能为空', {
                    icon: 5,
                    title: "提示"
                });
                return false;
            }
            if (!image_url[1]) {
                layer.alert('支付宝收款码不能为空', {
                    icon: 5,
                    title: "提示"
                });
                return false;
            }
            $.ajax({
                url: "{:url('')}",
                type: 'post',
                dataType: "json",
                data: {agent_img: image_url},
                success: function (res) {
                    if(res.code>0){
                        layer.alert(res.msg, {
                            icon: 5,
                            title: "提示"
                        });
                    }else if(res.code==0){
                        layer.msg(res.msg, {
                            icon: 1, time: 1500, end: function () {
                                window.location.href = "{:url('agent/index')}";
                            }
                        });
                    }
                }
            });
        });
    });

    var image_url = {};

    //字数限制500字
    var uploading = false;
    var picArr = [];
    var index_img =-1;
    $(".shangchuanclass").click(function () {

         index_img = $('.shangchuanclass').index(this);

        console.log(index_img);
    })



    $('input:file').localResizeIMG({
        width: 800,
        quality: 1,
        // 图片压缩 默认1 不压缩
        success: function (result) {
            var img = new Image();
            img.src = result.base64;
            if (uploading) {
                layer.msg("文件正在上传中，请稍候", {icon: 1, time: 1500});
                return false;
            }

            let imagearr = result.base64.split(',');
            let mime = imagearr[0].match(/:(.*?);/)[1];

            var binaryImg = atob(imagearr[1]);
            var length = binaryImg.length;
            var ab = new ArrayBuffer(length);
            var ua = new Uint8Array(ab);
            for (var i = 0; i < length; i++) {
                ua[i] = binaryImg.charCodeAt(i);
            }
            var blob = new Blob([ab], {type: mime});
            var formData = new FormData();
            formData.append('file', blob, 'image.png');

            $.ajax({
                url: "{:url('Common/pictureUpload',array('session_id'=>rand(10,1000000)))}",
                type: 'POST',
                cache: false,
                data: formData,
                processData: false,
                contentType: false,
                dataType: "json",
                beforeSend: function () {
                    uploading = true;
                },
                success: function (data_file) {
                    uploading = false;
                    if (data_file) {
                        image_url[index_img] = data_file.path;

                        if (image_url[index_img]) {
                            let _str = "<span class='pic_look' style='background-image: url(/upload/picture/" + data_file.path + ")'><em class='delete_pic'></em></span>";
                            $('.shangchuanclass').eq(index_img).parent('span').before(_str);
                            $('.shangchuanclass').eq(index_img).parent('span').hide();
                        }
                        console.log(image_url);

                    } else {
                        alert('上传异常,请联系开发者');
                    }
                }
            });
        }
    });
    // 删除图片默认显示
    $(document).on('click', '.delete_pic', function (event) {
        var aa = $(this).parents(".pic_look").index();
        picArr.splice(aa, 1);
        image_url='';
        $(this).parents(".pic_look").remove();
        $('#chose_pic_btn').show();
        console.log(picArr);
    });


</script>
