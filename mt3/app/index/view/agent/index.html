<script src="__COMMON__/layer/layer.js"></script>
<style>
    .bohui {
        color: aliceblue;
        padding: 2px 4px;
        border-radius: 3px;
        background-color: red;
    }

    .daishen {
        color: aliceblue;
        padding: 2px 4px;
        border-radius: 3px;
        background-color: #c87f0a;
    }

    .chenggong {
        color: aliceblue;
        padding: 2px 4px;
        border-radius: 3px;
        background-color: green;
    }
</style>
<div class="header">
    <a href="javascript:history.back();"><i class="iconfont">&#xe697;</i></a>
    <h4>代理中心</h4>
    <a href="javascript:" class="sousuo jieshao">介绍</a>
</div>
<div class="wdpb-xxka">
    <ul>
        <li>
            {if condition="$user['level']==0"}
            <p><a href="{:url('apply')}" style="color: #ffffff">{$level_list[$user['level']]['title']}</a></p>

            {elseif condition="$user['level'] > 0"/}
            <p>{$level_list[$user['level']]['title']} - 返点：{$level_list[$user['level']]['point']}</p>
            {else/}
            <p>{$level_list[$user['level']]['title']}</p>
            {/if}

            <span class="xiaobi">{$user['withdrawal']} 米</span>
        </li>
        <li class="zzye">
           
            {if condition="$user['level']>0"}
             <a href="javascript:">
                <input type="" name="" class="tixian" value="交税">
            </a>
             <a href="javascript:">
                <input type="" name="" class="daichong" value="代充">
            </a> 
            {/if}
        </li>
    </ul>
</div>
<div class="wdbt-nr">
    <div class="wode-nr-top">
        下级账单总概
    </div>
    <div class="wdbt-nr-nr">
        <ul>
            <li>
                <div class="wodeli01wai">
                    <p class="topbu">总充值</p>
                    <span class="shuzhi">{$user['total_topup']}<span>米</span></span>
                </div>
            </li>
            <li>
                <div class="wodeli01wai">
                    <p class="topbu">总人数</p>
                    <span class="shuzhi">{$user['total_people']}<span>人</span></span>
                </div>
            </li>
        </ul>
    </div>
    <div class="matop2">
        <div class="shezhi-nr">
            <a href="{:url('totalPeople')}">
                <div class="xzyhk-nr">
                    <div class="czsq-nr xzyhknr">
                        <span>人员统计</span>
                        <i class="iconfont qianjin">&#xe6a7;</i>
                        <p class="qianjin"></p>
                    </div>
                </div>
            </a>
            <a href="{:url('promote')}">
                <div class="xzyhk-nr">
                    <div class="czsq-nr xzyhknr">
                        <span>我的推广</span>
                        <i class="iconfont qianjin">&#xe6a7;</i>
                        <p class="qianjin"></p>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <div class="wode-nr-top topxt"><span>下级充值审核</span>
    </div>
    <div class="wdlw-content" style="padding-top: 0px;">
        <div class="wdzs-nr">
            <ul>
                <li class="">凭证</li>
                <li class="">金额</li>
                <li class="">操作</li>
            </ul>
        </div>
    </div>
    <div class="wdlw-nr">
        <img style="display:none;" src="__STATIC__/picture/wdlw1.png">
        <p>当前列表为空</p>
    </div>
</div>

<div id="outerdiv"
     style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:99999;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src=""/>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {
        $(".jieshao").click(function () {
            layer.open({
                type: 0,
                title: '详细',
                content: '1.线下支付，是到代理自己的账户全额，那么需要扣除当前返点数余下的转到平台<br>2.线上支付，全额到平台，当前返点额度自动到账户<br>3.自己下级线下转账，请及时审批<br>4.当前提现额度小于-300时候，需要发请给平台账户线下转账进行审批<br>'
            });
            return false;
        }); 
        var withdrawal = {$user['withdrawal']};
        var tixianka=1;
        $(".tixian").click(function () {
            var type=2;
            layer.open({
                type: 1,
                title: '提现',
                closeBtn: 1,
                anim: 2,
                shadeClose: true,
                area: [document.body.clientWidth * 0.8 + 'px', '550px'],
                content: '<div id="mydiv" style="margin-left:20px;margin-top:20px;">' +
                    '<img style="width: 49%;" src="__STATIC__/image/weixin1.jpg"> <img style="width: 49%;"  src="__STATIC__/image/zhifubao1.jpg">' +

                    '<p style="color:red;">转账时一定要备注自己的账号备注账号账号</p>' +
                    '金额：<input id="ReturnReason1" style="width:60%" class="withdrawal" value="" placeholder="请输入金额">' +
                    '</div>',
                btn: ["确定", "取消"],
                yes: function (index, layero) {
                    var resason = top.$('#ReturnReason1').val();
                    console.log(resason);
                    
                    if(tixianka==0){
                       layer.msg('请勿重复点击');
                       return false;
                    }
                    tixianka=0;
                    $.ajax({
                        url: "{:url('withDraw')}",
                        type: 'post',
                        dataType: "json",
                        data: {money: resason,'type':type},
                        success: function (res) {
                            
                            if (res.code > 0) {
                                layer.alert(res.msg, {
                                    icon: 5,
                                    title: "提示"
                                });
                                tixianka=1;
                            } else if (res.code == 0) {
                                layer.msg(res.msg, {
                                    icon: 1, time: 1500, end: function () {
                                        window.location.href = "{:url('')}";
                                    }
                                });
                            }
                            
                        }
                    });
                    //layer.close(index);
                },
                cancel: function (index, layero) {
                    layer.close(index);
                },
            });
            return false;
        }); 
        
        $(".daichong").click(function () {
            var type=2;
            layer.open({
                type: 1,
                title: '代充',
                closeBtn: 1,
                anim: 2,
                shadeClose: true,
                area: [document.body.clientWidth * 0.6 + 'px', '300px'],
                content: '<div id="mydiv" style="margin-left:20px;margin-top:20px;">' +
                    '账号：<input id="agentUsername" style="width:60%" class="agentUsername" value="" placeholder="请输入账号">' +
                    '<p style="padding-bottom:10px;color:red;font-size: 0.8em;">区：<b style="color:green;" class="quming"></b> 游戏id：<b style="color:green;" class="youid"></b><br/></p>' +
                    '金额：<input type="number" id="agentPrice" style="width:60%" class="price" value="" placeholder="请输入金额">' +
                    '<p style="margin-bottom:10px;color:red;font-size: 0.8em;">真实转账金额<br/></p>' +
                    '赠送：<input type="number" id="agentAmount" style="width:60%" class="agentAmount" value="" placeholder="请输入霄币">' +
                    '<p style="color:red;font-size: 0.8em;">赠送额外霄币，不计入累充额度<br/></p>' +
                    '</div>',
                btn: ["确定", "取消"],
                yes: function (index, layero) {
                    var agentUsername = top.$('#agentUsername').val();
                    var agentPrice = top.$('#agentPrice').val();
                    var agentAmount = top.$('#agentAmount').val();
                    if(tixianka==0){
                       layer.msg('请勿重复点击');
                       return false;
                    }
                    tixianka=0;

                    $.ajax({
                        url: "{:url('agentRecharge')}",
                        type: 'post',
                        dataType: "json",
                        data: {username: agentUsername,price: agentPrice,gift_amount:agentAmount},
                        success: function (res) {
                            if (res.code > 0) {
                                layer.alert(res.msg, {
                                    icon: 5,
                                    title: "提示"
                                });
                                tixianka=1;
                                
                            } else if (res.code == 0) {
                                layer.msg(res.msg, {
                                    icon: 1, time: 1500, end: function () {
                                        window.location.href = "{:url('')}";
                                         
                                    }
                                });
                            }
                            
                        }
                    }); 
                    //layer.close(index);
                },
                cancel: function (index, layero) {
                    layer.close(index);
                },
            });
            return false;
        });
        $(document).on('change', '.agentUsername', function (event) {
            var agentUsername = $(this).val();
            $.ajax({
                        url: "{:url('user/getUserInfo')}",
                        type: 'post',
                        dataType: "json",
                        data: {username: agentUsername},
                        success: function (res) {
                            if (res.code > 0) {
                                layer.alert(res.msg, {
                                    icon: 5,
                                    title: "提示"
                                });
                                tixianka=1;
                                
                            } else{
                                  $(".quming").text(res.server_info.name)
                                  $(".youid").text(res.server_info.game_id)
                            }
                            
                        }
                    });
            console.log(agentUsername);
            
        }); 
        // 删除图片默认显示
        $(document).on('click', '.dianjifang', function (event) {
            imgShow("#outerdiv", "#innerdiv", "#bigimg", $(this).attr("src"));
        });
        // 删除图片默认显示
        $(document).on('click', '.daishen', function (event) {
            var id_tongyi = $(this).attr('data-id');

            layer.open({
                type: 0,
                title: '询问',
                closeBtn: 1,
                anim: 2,
                shadeClose: true,
                content: '是否收到转账金额',
                btn: ["确定", "取消"],
                yes: function (index, layero) {
                    console.log(id_tongyi);
                    $.ajax({
                        url: "{:url('orderConfirm')}",
                        type: 'post',
                        dataType: "json",
                        data: {id: id_tongyi},
                        success: function (res) {
                            if (res.code > 0) {
                                layer.alert(res.msg, {
                                    icon: 5,
                                    title: "提示"
                                });
                            } else if (res.code == 0) {
                                layer.msg(res.msg, {
                                    icon: 1, time: 1500, end: function () {
                                        window.location.href = "{:url('')}";
                                    }
                                });
                            }
                        }
                    });
                },
                cancel: function (index, layero) {
                    layer.close(index);
                },
            });
            return false;
        });
    });


    var page = 1;
    var last_page = 99;
    var money_status = {0: '<span class="daishen">确认</span>'};
    getListData();

    function getListData() {
        $.ajax({
            url: "{:url('orderRecords')}",
            type: 'get',
            dataType: "json",
            data: {list_rows: 20, page: page},
            success: function (res) {
                console.log(res);
                last_page = res.last_page;
                var list = res.data;

                var html_t = '';
                for (var i in list) {
                    html_t += '<div class="yqg-nr-nr5">\n' +
                        '\t\t\t\t<div class="yqg-nr-nr5-1">\n' +
                        '\t\t\t\t\t<ul>\n' +
                        '\t\t\t\t\t\t<li class="nr5-1li01">\n' + list[i]['username'] +
                        '\t\t\t\t\t\t\t\n' +
                        '\t\t\t\t\t\t</li>\n' +
                        '\t\t\t\t\t\t<li class="nr5-1li02">\n' + list[i]['create_time'].substring(0, list[i]['create_time'].length - 3) +
                        '\t\t\t\t\t\t\t\n' +
                        '\t\t\t\t\t\t</li>\n' +
                        '\t\t\t\t\t</ul>\n' +
                        '\t\t\t\t</div>\n' +
                        '\t\t\t\t<div class="yqg-nr-nr5-2">\n' +
                        '\t\t\t\t\t<ul>\n' +
                        '\t\t\t\t\t\t<li class="nr5-2li01"><p>' + list[i]['price'] + '米</p></li>\n' +
                        '\t\t\t\t\t\t<li class="nr5-2li02"><img class="dianjifang" style="height: 50px;" src="/upload/picture/' + list[i]['image_url'] + '"></li>\n' +
                        '\t\t\t\t\t\t<li class="nr5-2li03"><span data-id="' + list[i]['id'] + '" class="daishen">确认</span></li>\n' +
                        '\t\t\t\t\t</ul>\n' +
                        '\t\t\t\t</div>\n' +
                        '\t\t\t</div>';

                }
                $('.wdlw-content').append(html_t);
                if (page >= last_page) {
                    $('.wdlw-nr').children('p').text('没有更多了');
                }

                if (res.total < 1) {
                    $('.wdlw-nr').children('img').show();
                    $('.wdlw-nr').children('p').text('当前列表为空');
                }
                page++;
                console.log(page);
            }
        });
    }


    function imgShow(outerdiv, innerdiv, bigimg, src) {
        $(bigimg).attr("src", src);//设置#bigimg元素的src属性

        $("<img/>").attr("src", src).on('load', function () {
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            var scale = 0.8;//缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放
            if (realHeight > windowH * scale) {
                imgHeight = windowH * scale;//如大于窗口高度，图片高度进行缩放
                imgWidth = imgHeight / realHeight * realWidth;//等比例缩放宽度
                if (imgWidth > windowW * scale) {
                    imgWidth = windowW * scale;//再对宽度进行缩放
                }
            } else if (realWidth > windowW * scale) {
                imgWidth = windowW * scale;//如大于窗口宽度，图片宽度进行缩放
                imgHeight = imgWidth / realWidth * realHeight;//等比例缩放高度
            } else {
                imgWidth = realWidth;
                imgHeight = realHeight;
            }
            $(bigimg).css("width", imgWidth);//以最终的宽度对图片缩放
            var w = (windowW - imgWidth) / 2;//计算图片与窗口左边距
            var h = (windowH - imgHeight) / 2;//计算图片与窗口上边距
            $(innerdiv).css({"top": h, "left": w});//设置#innerdiv的top和left属性
            $(outerdiv).fadeIn("fast");//淡入显示#outerdiv及.pimg

        });

        $(outerdiv).click(function () {
            //再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }
</script>