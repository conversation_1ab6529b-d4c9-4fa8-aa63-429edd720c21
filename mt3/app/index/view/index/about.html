<!-- Theme Inner Banner ____________________________ -->
<section class="theme-inner-banner">
	<div class="inner-banner-opacity">
		<div class="container">
			<div class="inner-banner-title">
				<h1 style='font-family:"微软雅黑"'>关于我们</h1>
				<ul>
<!--					<li><a href="index.html">首页</a></li>-->
<!--					<li><span><i class="fa fa-arrow-right" aria-hidden="true"></i></span></li>-->
<!--					<li><a href="#">关于架构</a></li>-->
				</ul>
			</div> <!-- /.inner-banner-title -->
		</div> <!-- /.container -->
	</div> <!-- /.inner-banner-opact -->
</section> <!-- /.them-inner-banner -->

<!-- About Us  _________________________________ -->
<section class="about-seo-tools">
	<div class="container">
		<div class="row">
			<div class="col-md-4 col-sm-5 col-xs-12 float-right">
				<div class="seo-tools-img"><img src="__STATIC__/images/about/img-1.jpg" alt="image"></div>
				<div class="seo-tools-signature"><img src="__STATIC__/images/about/signature.jpg" alt="signature"></div>
			</div> <!-- /.seo-tools-img -->
			<div class="col-md-8 col-sm-7 col-xs-12">
				<div class="seo-tools-text">
					<h3>技术团队</h3>
					<p>我们是一个年轻有力的团队，成员充满活力，热爱挑战。对我们来说，“跳出框框”思考是家常便饭。我们的每一位员工都充满热情，全力以赴地工作。
						我们是一个由不同学术和工作背景的专业人士组成的令人兴奋的团体，走到一起来实现我们的愿景和梦想。对我们来说，工作比娱乐更有趣。</p>
<!--					<a href="#" class="a-comon hvr-float-shadow">Know more</a>-->
				</div> <!-- /.seo-tools-text -->
			</div> <!-- /.col -->
		</div> <!-- /.row -->
	</div> <!-- /.container -->
</section> <!-- /.about-seo-tools -->

<!-- short-story-about   _________________________________ -->
<section class="short-story-about">
	<div class="container">
		<div class="short-story-title">
			<h4>也是开发者的极品架构</h4>
			<p>内含一件生成代码文件功能，再也不用复制controller,logic,model,validate,view代码文件，只需要填写生成表字段和字段基础类型。基础功能 查询列表，新增数据，修改数据，删除数据，单条数据查询 等等功能都是生成出来。生成后台板块，自动菜单列表添加，数据表结构自动生成并且可去优化表结构，只需开发者开发一些定向数据关联逻辑。表单拖拽创建表单，自定义分享表单。</p>
		</div> <!-- /.short-story-title -->
		<div class="row">
			<div class="col-md-8 col-sm-6 col-xs-12">
				<div class="short-story-img"><img src="__STATIC__/images/about/img-2.jpg" alt="image"></div>
			</div> <!-- /.short-story-img -->
			<div class="col-md-4 col-sm-6 col-xs-12">
				<div class="short-story-side-style">
					<ul>
						<li>
							<p>后台基础架构搭建</p>
							<h4>2018, July 11</h4>
						</li>
						<li>
							<p>微信板块集成</p>
							<h4>2018, Nov 12</h4>
						</li>
						<li>
							<p>商城板块集成</p>
							<h4><span>now</span></h4>
						</li>
					</ul>
				</div> <!-- /.short-story-side-style -->
			</div> <!-- /.col -->
		</div> <!-- /.row -->
	</div> <!-- /.container -->
</section> <!-- /.short-story-about -->

<!-- your SEO business _________________________________ -->
<section class="your-SEO-business">
	<div class="row seo-business-row">
		<div class="col-md-6 col-xs-12 seo-business-col">
			<div class="left-side-img">
				<img src="__STATIC__/images/home/<USER>" alt="object">
			</div> <!-- /.left-side-img -->
		</div> <!-- /.seo-business-col -->
		<div class="col-md-6 col-xs-12 seo-business-col">
			<div class="right-side-content">
				<h2>对于 <span>爱神星</span> 业务目标和期望</h2>
				<span class="titleanimi"></span>
				<p><span>展现出</span>基础系统更加完善，更加加快开发者使用。微服务与模块开发模式结合，随项目而定选择。在更新过程中任何一个版本都是经典不会出现任何BUG正常使用。收集每一个使用者的意见，根据大家的需求意见开发出板块，随意使用，做到完全开源。尽心辅助每位客户操作使用。</p>
				<ul>
					<li><img src="__STATIC__/images/them-logo/icon-11.png" alt="icon">发展新的想法和市场</li>
					<li><img src="__STATIC__/images/them-logo/icon-11.png" alt="icon">任何一个版本Dome都可以用来使用</li>
					<li><img src="__STATIC__/images/them-logo/icon-11.png" alt="icon">360度看你的情况下使用我们的种子深体验</li>
					<li><img src="__STATIC__/images/them-logo/icon-11.png" alt="icon">建立强大的系统</li>
				</ul>
<!--				<a href="#" class="a-comon">SEO ConsultaTion</a>-->
<!--				<a href="#" class="a-comon two">Sign Up Free</a>-->
			</div> <!-- /.right-side-content -->
		</div> <!-- /.seo-business-col -->
	</div> <!-- /.seo-business-row -->
</section> <!-- /.your-SEO-business -->