<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

// 扩展配置文件，系统研发过程中需要的配置建议放在此处，与框架相关配置分离

return [
//充值记录状况
    'sex' => [
        '0' => '女',
        '1' => '男',
        '2' => '女'
    ],
    //消息类型
    'messageType' => [
        '1' => '聊天',
        '2' => '活动'
    ],
    //充值记录状况
    'money_type' => [
        '0' => '无',
        '1' => '线上充值',
        '2' => '线下充值',
        '3' => '签到',
        '4' => '购买',
        '5' => '兑换码',
        '6' => '后台充值',
        '7' => '任务获得'
    ],
    //充值比例
    'price_rate' => 10,
    //充值记录状况
    'money_status' => [
        '-1' => '已关闭',
        '0' => '充值中',
        '1' => '成功'
    ],
    //积分记录状况
    'integrallogtype' => [
        '-1' => '已关闭',
        '0' => '待定',
        '1' => '签到',
        '2' => '购买消耗',
        '3' => '分享获取'
    ],
    //用户分类 倔强青铜、秩序白银、荣耀黄金、尊贵铂金、永恒钻石、至尊星耀、最强王者
    'user_vip' => [['title' => '普通会员', 'level' => 0, 'lines' => 0, 'introduce' => '刚刚出山'],
        ['title' => '青铜会员', 'level' => 1, 'lines' => 500, 'introduce' => '享受会员'],
        ['title' => '白银会员', 'level' => 2, 'lines' => 1200, 'introduce' => '享受会员'],
        ['title' => '黄精会员', 'level' => 3, 'lines' => 2500, 'introduce' => '享受会员'],
        ['title' => '铂金会员', 'level' => 4, 'lines' => 3700, 'introduce' => '享受会员'],
        ['title' => '钻石会员', 'level' => 5, 'lines' => 5700, 'introduce' => '享受会员'],
        ['title' => '祖母会员', 'level' => 6, 'lines' => 8000, 'introduce' => '享受会员'],
        ['title' => '晶石会员', 'level' => 7, 'lines' => 11000, 'introduce' => '享受会员'],
        ['title' => '水晶会员', 'level' => 8, 'lines' => 15000, 'introduce' => '享受会员'],
        ['title' => '玛瑙会员', 'level' => 9, 'lines' => 19000, 'introduce' => '享受会员']
    ],
    //累积充值
    'allch_total_list' => [['items' => [['item' => 401054, 'number' => 1], ['item' => 400910, 'number' => 500]], 'number' => 1, 'lines' => 500, 'introduce' => '刚刚出山'],
        ['items' => [['item' => 401055, 'number' => 1], ['item' => 400910, 'number' => 1000]], 'number' => 1, 'lines' => 1000, 'introduce' => '享受会员'],
        ['items' => [['item' => 401056, 'number' => 1], ['item' => 400910, 'number' => 2000]], 'number' => 2, 'lines' => 2000, 'introduce' => '享受会员'],
        ['items' => [['item' => 401057, 'number' => 1], ['item' => 400910, 'number' => 3000]], 'number' => 3, 'lines' => 3000, 'introduce' => '享受会员'],
        ['items' => [['item' => 401058, 'number' => 1], ['item' => 400910, 'number' => 4000]], 'number' => 4, 'lines' => 4000, 'introduce' => '享受会员'],
        ['items' => [['item' => 401059, 'number' => 1], ['item' => 400910, 'number' => 5000]], 'number' => 5, 'lines' => 5000, 'introduce' => '享受会员']
    ],

    //日累充
    'allch_day_list' => [
        ['items' => [['item' => 401077, 'number' => 1], ['item' => 400910, 'number' => 40]], 'number' => 1, 'lines' => 68, 'introduce' => '龙马↑14,宠物成长内丹'],
        ['items' => [['item' => 401078, 'number' => 1], ['item' => 400910, 'number' => 200]], 'number' => 2, 'lines' => 128, 'introduce' => '元神丹初中级'],
        ['items' => [['item' => 401079, 'number' => 1], ['item' => 400910, 'number' => 500]], 'number' => 3, 'lines' => 328, 'introduce' => '宠物血脉进阶丹，'],
        ['items' => [['item' => 401080, 'number' => 1], ['item' => 400910, 'number' => 1000]], 'number' => 4, 'lines' => 648, 'introduce' => '享受会员'],
        ['items' => [['item' => 401081, 'number' => 1], ['item' => 400910, 'number' => 1800]], 'number' => 5, 'lines' => 1288, 'introduce' => '享受会员'],
        ['items' => [['item' => 401082, 'number' => 1], ['item' => 400910, 'number' => 3000]], 'number' => 6, 'lines' => 2000, 'introduce' => '享受会员']
    ],

    //代理等级配置
    'level_list' => [
        '-2' => ['title' => '禁止代理', 'level' => -2, 'point' => 0],
        '-1' => ['title' => '待审批', 'level' => -1, 'point' => 0],
        '0' => ['title' => '申请代理', 'level' => 0, 'point' => 0],
        '1' => ['title' => '一级经理', 'level' => 1, 'point' => 0.2],
        '2' => ['title' => '二级经理', 'level' => 2, 'point' => 0.3],
        '3' => ['title' => '三级经理', 'level' => 3, 'point' => 0.4],
        '4' => ['title' => '四级经理', 'level' => 4, 'point' => 0.5],
        '5' => ['title' => '五级经理', 'level' => 5, 'point' => 0.6],
        '6' => ['title' => '六级经理', 'level' => 6, 'point' => 0.7],
        '7' => ['title' => '终极经理', 'level' => 7, 'point' => 0.8]
    ],

    //签到列表
    'sign_list' => [
        '5' => 345014,
        '7' => 400910,
        '10' => 400910,
        '15' => 400910,
        '20' => 400910,
        '25' => 400910,
        '30' => 400910
    ],
];
