<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class Task extends LogicBase
{

      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }


     public function getTaskInfo($where = [], $field = '*')
     {

        return $this->modelTask->getInfo($where, $field);
     }

    public function getTaskList($where = [], $field = '', $order = 'id desc', $paginate = 0,$limit=0)
    {
        if(!empty($limit)){
            $this->modelTask->limit=$limit;
        }
        return $this->modelTask->getList($where, $field, $order, $paginate);
    }


    public function getTaskColumn($where = [], $field = '', $key = '')
    {
        return $this->modelTask->getColumn($where, $field , $key);
    }


    public function TaskEdit($data = [])
    {


        $result = $this->modelTask->setInfo($data);
        
        return $result ? $result : $this->modelTask->getError();
    }

    public function TaskDel($where = [], $is_true = false)
    {

        $result = $this->modelTask->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelTask->getError();
    }

    /**
     * 获取会员列表
     */
    public function getTaskStat($where = [], $stat_type = 'count', $field = 'id')
    {

        return $this->modelTask->stat($where, $stat_type, $field);
    }
    public function setTaskIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelTask->setIncDecInfo($where, $field, $number, $setType);
    }
	
	//Admin

    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['name|info'] = ['like', '%'.$data['search_data'].'%'];
        !empty($data['id']) && $where['id'] = $data['id'];

        return $where;
    }

    public function TaskAmindEdit($data = [])
    {

        $url = url('list');

        $data['end_time']=strtotime($data['end_time']);

        $image_json=explode(',',$data['img_json']);
        $data['image']=$image_json[0];

        $result=$this->modelTask->setInfo($data);

		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '任务' . $handle_text . '，id：' .$data['id']);
		
        return $result ? [RESULT_SUCCESS, '任务操作成功', $url] : [RESULT_ERROR, $this->modelTask->getError()];
    }

    public function TaskAdminDel($where = [])
    {

        $result = $this->modelTask->deleteInfo($where);
        
        $result && action_log('删除', '任务删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '任务删除成功'] : [RESULT_ERROR, $this->modelTask->getError()];
    }
}
