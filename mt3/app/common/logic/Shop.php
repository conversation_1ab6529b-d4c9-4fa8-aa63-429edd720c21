<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class Shop extends LogicBase
{

      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }


     public function getShopInfo($where = [], $field = '*')
     {

        return $this->modelShop->getInfo($where, $field);
     }

    public function getShopList($where = [], $field = '', $order = 'id desc', $paginate = 0,$limit=0)
    {
        if(!empty($limit)){
            $this->modelShop->limit=$limit;
        }
        return $this->modelShop->getList($where, $field, $order, $paginate);
    }


    public function getShopColumn($where = [], $field = '', $key = '')
    {
        return $this->modelShop->getColumn($where, $field , $key);
    }


    public function shopEdit($data = [])
    {
		
        $result = $this->modelShop->setInfo($data);
        
        return $result ? $result : $this->modelShop->getError();
    }

    public function ClickDel($where = [], $is_true = false)
    {

        $result = $this->modelShop->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelShop->getError();
    }

    public function setShopIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelShop->setIncDecInfo($where, $field, $number, $setType);
    }
	
	//Admin

    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['name|info'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    public function shopAdminEdit($data = [])
    {

        $url = url('list');
        
        $data['member_id'] = MEMBER_ID;

        $result=$this->modelShop->setInfo($data);

		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '物品' . $handle_text . '，id：' .$data['id']);
		
        return $result ? [RESULT_SUCCESS, '物品操作成功', $url] : [RESULT_ERROR, $this->modelShop->getError()];
    }

    public function ShopAdminDel($where = [])
    {

        $result = $this->modelShop->deleteInfo($where);
        
        $result && action_log('删除', '课程删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '课程删除成功'] : [RESULT_ERROR, $this->modelShopShopClick->getError()];
    }
}
