<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class Sign extends LogicBase
{

      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }


     public function getSignInfo($where = [], $field = '*')
     {

        return $this->modelSign->getInfo($where, $field);
     }

    public function getSignList($where = [], $field = '', $order = 'id desc', $paginate = 0,$limit=0)
    {
        if(!empty($limit)){
            $this->modelSign->limit=$limit;
        }
        return $this->modelSign->getList($where, $field, $order, $paginate);
    }


    public function getSignColumn($where = [], $field = '', $key = '')
    {
        return $this->modelSign->getColumn($where, $field , $key);
    }


    public function clickEdit($data = [])
    {
		
        $result = $this->modelSign->setInfo($data);
        
        return $result ? $result : $this->modelSign->getError();
    }

    public function ClickDel($where = [], $is_true = false)
    {

        $result = $this->modelSign->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelSign->getError();
    }

    public function setSignIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelSign->setIncDecInfo($where, $field, $number, $setType);
    }
	
	//Admin

    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

}
