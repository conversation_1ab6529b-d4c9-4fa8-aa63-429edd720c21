<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class Order extends LogicBase
{

      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }


     public function getOrderInfo($where = [], $field = '*')
     {

        return $this->modelOrder->getInfo($where, $field);
     }

    public function getOrderList($where = [], $field = '', $order = 'id desc', $paginate = 0,$limit=0)
    {
        if(!empty($limit)){
            $this->modelOrder->limit=$limit;
        }
        return $this->modelOrder->getList($where, $field, $order, $paginate);
    }


    public function getOrderColumn($where = [], $field = '', $key = '')
    {
        return $this->modelOrder->getColumn($where, $field , $key);
    }


    public function orderEdit($data = [])
    {
		
        $result = $this->modelOrder->setInfo($data);
        
        return $result ? $result : $this->modelOrder->getError();
    }

    public function orderDel($where = [], $is_true = false)
    {

        $result = $this->modelOrder->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelOrder->getError();
    }

    public function setOrderIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelOrder->setIncDecInfo($where, $field, $number, $setType);
    }
	
	//Admin

    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['uid'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

}
