<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class Server extends LogicBase
{

      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }


     public function getServerInfo($where = [], $field = '*')
     {

        return $this->modelServer->getInfo($where, $field);
     }

    public function getServerList($where = [], $field = '', $order = 'id desc', $paginate = 0,$limit=0)
    {
        if(!empty($limit)){
            $this->modelServer->limit=$limit;
        }
        return $this->modelServer->getList($where, $field, $order, $paginate);
    }


    public function getServerColumn($where = [], $field = '', $key = '')
    {
        return $this->modelServer->getColumn($where, $field , $key);
    }


    public function serverEdit($data = [])
    {
		
        $result = $this->modelServer->setInfo($data);
        
        return $result ? $result : $this->modelServer->getError();
    }

    public function ServerDel($where = [], $is_true = false)
    {

        $result = $this->modelServer->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelServer->getError();
    }

    public function setServerIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelServer->setIncDecInfo($where, $field, $number, $setType);
    }
	
	//Admin

    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    public function serverAdminEdit($data = [])
    {

        $url = url('list');
        
        $data['member_id'] = MEMBER_ID;

        $result = $this->modelServer->setInfo($data);

		$handle_text = empty($data['id']) ? '服区' : '编辑';
        
        $result && action_log($handle_text, '服区' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '服区操作成功', $url] : [RESULT_ERROR, $this->modelServer->getError()];
    }

    public function serverAdminDel($where = [])
    {

        $result = $this->modelServer->deleteInfo($where);
        
        $result && action_log('删除', '课程删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '课程删除成功'] : [RESULT_ERROR, $this->modelServerServer->getError()];
    }
}
