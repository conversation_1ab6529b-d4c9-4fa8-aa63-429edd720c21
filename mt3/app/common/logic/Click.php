<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class Click extends LogicBase
{

      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }


     public function getClickInfo($where = [], $field = '*')
     {

        return $this->modelClick->getInfo($where, $field);
     }

    public function getClickList($where = [], $field = '', $order = 'id desc', $paginate = 0,$limit=0)
    {
        if(!empty($limit)){
            $this->modelClick->limit=$limit;
        }
        return $this->modelClick->getList($where, $field, $order, $paginate);
    }


    public function getClickColumn($where = [], $field = '', $key = '')
    {
        return $this->modelClick->getColumn($where, $field , $key);
    }


    public function clickEdit($data = [])
    {
		
        $result = $this->modelClick->setInfo($data);
        
        return $result ? $result : $this->modelClick->getError();
    }

    public function ClickDel($where = [], $is_true = false)
    {

        $result = $this->modelClick->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelClick->getError();
    }

    public function setClickIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelClick->setIncDecInfo($where, $field, $number, $setType);
    }
	
	//Admin

    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    public function ClickAmindEdit($data = [])
    {


        $validate_result = $this->validateClick->scene('edit')->check($data);

        if (!$validate_result) {
            return [RESULT_ERROR, $this->validateClick->getError()];
        }

        $url = url('clickList');
        
        $data['member_id'] = MEMBER_ID;

        $data['start_time'] = strtotime($data['start_time']);

        $result = $this->modelClick->setInfo($data);

        if(!empty($data['id'])){
            $result=$data['id'];
        }

        if(!empty($data['d_id'])){
            $ClickDirectory=array();
            foreach ($data['d_id'] as $key=>$vole){
                if(!empty($data['d_title'][$key])){
                    $ClickDirectoryInfo['c_id']=$result;
                    $ClickDirectoryInfo['id']=$vole;
                    $ClickDirectoryInfo['title']=$data['d_title'][$key];
                    $ClickDirectoryInfo['describe']=$data['d_describe'][$key];
                    $ClickDirectoryInfo['sort']=$data['d_sort'][$key];
                    $ClickDirectory[]=$ClickDirectoryInfo;
                    $this->modelClickDirectory->setInfo($ClickDirectoryInfo);
                }
            }
        }
//        dump($ClickDirectory);
//        $this->logicClickDirectory->ClickDirectorySetList($ClickDirectory);

		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '课程' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '课程操作成功', $url] : [RESULT_ERROR, $this->modelClick->getError()];
    }

    public function clickAdminDel($where = [])
    {

        $result = $this->modelClick->deleteInfo($where);
        
        $result && action_log('删除', '课程删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '课程删除成功'] : [RESULT_ERROR, $this->modelClickClick->getError()];
    }
}
