{extend name="public/base"/}

{block name="header"}
    <li class="active"><a>安装协议</a></li>
    <li><a>环境检测</a></li>
    <li><a>创建数据库</a></li>
    <li><a>安装</a></li>
    <li><a>完成</a></li>
{/block}

{block name="body"}
    <h2> OneBase 安装协议</h2>
    <p>版权所有 &copy; 2017~2020，OneBase保留所有权利。</p>

    <p>感谢您选择OneBase，希望我们的努力能为您提供一套简单、快速的软件研发解决方案。</p>

    <p>OneBase拥有全部知识产权，包括商标和著作权。</p>
	<p>OneBase遵循Apache Licence2开源协议，并且免费使用，但不能未经授权抹除产品标志再次用于开源。Apache Licence是著名的非盈利开源组织Apache采用的协议，该协议和BSD类似，鼓励代码共享和尊重原作者的著作权，允许代码修改，再作为开源或商业软件发布。需要满足的条件：<br/>
1． 需要给用户一份Apache Licence；<br/>
2． 如果你修改了代码，需要在被修改的文件中说明；<br/>
3． 在延伸的代码中（修改和有源代码衍生的代码中）需要带有原来代码中的协议，商标，专利声明和其他原来作者规定需要包含的说明；<br/>
4． 如果再发布的产品中包含一个Notice文件，则在Notice文件中需要带有本协议内容。你可以在Notice中增加自己的许可，但不可以表现为对Apache Licence构成更改。</p>
{/block}

{block name="footer"}
    <a class="btn btn-primary btn-large" href="{:url('step1')}">同意安装协议</a>
    <a class="btn btn-large" href="https://onebase.org">不同意</a>
{/block}