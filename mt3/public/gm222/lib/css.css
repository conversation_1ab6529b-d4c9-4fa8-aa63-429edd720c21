	[v-cloak]{
		display: none;
	}
	#powerby{
		color:#c0b9b9;
		margin-top:35px
	}
	.item {
		margin-bottom: 10px;
	}
	.el-collapse-item__header{
		padding-left: 20px;
	}
	.el-card__body{
		padding:0;
	}
	.el-collapse{
		border-top:none;
	}
	.el-row{
		text-align:left;
	}
	html {
		overflow-y: scroll;
	}
	body{
		text-align:center;
	} 
	.bg1{
		background: url("../img/bg.jpg") no-repeat;
		background-position: center center;
		background-repeat: no-repeat;
		background-attachment: fixed;
		background-size: cover;
	}
	.bg{
		background:#5e5e5e url("../img/b2.jpg") no-repeat;
		background-position: -30% 55%;
		background-repeat: no-repeat;
		background-attachment: fixed;
		background-size: contain;
	}
	#app{
		background:#fff;
	}
	.el-form-item,.el-row{
		margin-bottom: 10px;
		&:last-child {
		margin-bottom: 0;
		}
	}
	.el-button-group{
		width:100%;
	}
	.el-button-group .el-button{
		width:33.33%;
	}
	.login-nofull{
		width:300px;
		margin:0 auto;
		box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
		padding:30px;
		border-radius: 6px;
		margin-top:15%;
	}
	.login-full{
		margin:25px 5px 5px 5px;
	}
	.nofull{
		width:720px;
		margin:0 auto;
		box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
		padding:10px;
		margin-top:35px;
	}
	.full{
		margin:5px;
	}
	.confirmcls{
		width:79%;
		top:30%;
	}