<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    

    
</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="form">
    <h1 class="title">FORM</h1>
    <div class="demo-item">
        <p class="demo-desc">通用设置类输入框</p>
        <div class="demo-block">
            <div class="ui-form ui-border-t">
                <form action="#">
                    <div class="ui-form-item ui-border-b">
                        <label>
                            列表标题
                        </label>
                        <input type="text" placeholder="18位身份证号码" />
                        <a href="#" class="ui-icon-close">
                        </a>
                    </div>
                    <div class="ui-form-item ui-form-item-link ui-border-b">
                        <label>
                            列表标题
                        </label>
                    </div>
                    <div class="ui-form-item ui-form-item-link ui-border-b">
                        <label>
                            标题
                        </label>
                    </div>
                    <div class="ui-btn-wrap">
                        <button class="ui-btn-lg ui-btn-primary">
                            确定
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="demo-block">
            <div class="ui-form ui-border-t">
                <form action="#">
                    <div class="ui-form-item ui-form-item-pure ui-border-b">
                        <input type="text" placeholder="列表标题">
                        <a href="#" class="ui-icon-close"></a>
                    </div>
                    <p class="ui-form-tips">4/20</p>
                </form>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">强引导类输入框</p>
        <div class="demo-block">
            <section class="ui-input-wrap ui-border-t">
                <div class="ui-input ui-border-radius">
                    <input type="text" name="" value="" placeholder="我也说一句...">
                </div>
                <button class="ui-btn">评论</button>
            </section>
            <section class="ui-input-wrap ui-border-t">
                <div class="ui-input ui-border-radius ui-input-text">
                    <input type="text" name="" value="" placeholder="我也说一句...">
                </div>
            </section>
         </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">表单开关项</p>
        <div class="demo-block">
            <div class="ui-form ui-border-t">
                <form action="#">
                    <div class="ui-form-item ui-form-item-switch ui-border-b">
                        <p>
                            对附近的人可见
                        </p>
                        <label class="ui-switch">
                            <input type="checkbox" />
                        </label>
                    </div>
                    <div class="ui-form-item ui-form-item-switch ui-border-b">
                        <p>
                            对附近的人可见
                        </p>
                        <label class="ui-switch">
                            <input type="checkbox" checked="" />
                        </label>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">表单下拉框</p>
        <div class="demo-block">
            <div class="ui-form ui-border-t">
                <form action="#">
                    <div class="ui-form-item ui-border-b">
                        <label>日期</label>
                        <div class="ui-select-group">
                            <div class="ui-select">
                                <select>
                                    <option>2014</option>
                                    <option selected="">2015</option>
                                    <option>2016</option>
                                </select>
                            </div>
                            <div class="ui-select">
                                <select>
                                    <option>03</option>
                                    <option selected="">04</option>
                                    <option>05</option>
                                </select>
                            </div>
                            <div class="ui-select">
                                <select>
                                    <option>21</option>
                                    <option selected="">22</option>
                                    <option>23</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="ui-form-item ui-border-b">
                        <label>日期</label>
                        <div class="ui-select">
                            <select>
                                <option>2014</option>
                                <option selected="">2015</option>
                                <option>2016</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">表单单选项</p>
        <div class="demo-block">
            <div class="ui-form ui-border-t">
                <form action="#">
                    <div class="ui-form-item ui-form-item-radio ui-border-b">

                        <label class="ui-radio" for="radio">
                            <input type="radio" name="radio" />
                        </label>
                        <p>表单中用于单选操作</p>
                    </div>
                    <div class="ui-form-item ui-form-item-radio ui-border-b">

                        <label class="ui-radio" for="radio">
                            <input type="radio" checked="" name="radio" />
                        </label>
                        <p>表单中用于单选操作</p>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">表单多选项,普通列表中多选项</p>
        <div class="demo-block">
            <div class="ui-form ui-border-t">
                <form action="#">
                    <div class="ui-form-item ui-form-item-checkbox ui-border-b">
                        <label class="ui-checkbox">
                            <input type="checkbox" />
                        </label>
                        <p>表单中用于多选操作</p>
                    </div>
                    <div class="ui-form-item ui-form-item-checkbox ui-border-b">
                        <label class="ui-checkbox">
                            <input type="checkbox" checked=""/>
                        </label>
                        <p>表单中用于多选操作</p>
                    </div>
                </form>
                <ul class="ui-list ui-list-text ui-list-checkbox ui-border-b">
                    <li class="ui-border-t">
                        <label class="ui-checkbox">
                            <input type="checkbox" />
                        </label>
                        <p>普通列表ui-list中用于多选操作</p>
                    </li>
                    <li class="ui-border-t">
                        <label class="ui-checkbox">
                            <input type="checkbox" checked="" />
                        </label>
                        <p>普通列表中用于多选操作</p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">多选框</p>
        <div class="demo-block">
            <p>
                <label class="ui-checkbox-s">
                    <input type="checkbox" name="checkbox" checked="" />
                </label>我已阅读并同意
            </p>
            <p>
                <label class="ui-checkbox-s">
                    <input type="checkbox" name="checkbox" checked="" />
                </label>我已阅读并同意
            </p>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">普通列表中单选项</p>
        <div class="demo-block">
            <ul class="ui-list ui-list-text ui-list-radio ui-border-tb">
                <li class="ui-border-t">
                    <label class="ui-radio" for="radio">
                        <input type="radio" name="radio" />
                    </label>
                    <p>普通列表中用于单选操作</p>
                </li>
                <li class="ui-border-t">
                    <label class="ui-radio" for="radio">
                        <input type="radio" checked="" name="radio" />
                    </label>
                    <p>普通列表中用于单选操作</p>
                </li>
            </ul>
        </div>
    </div>
</section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    

    
</body>
</html>
