<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    

    
<style>
.iconfont-list {
    overflow: hidden
}

.iconfont-list li {
    float: left;
    width: 75px;
    height: 80px;
    text-align: center
}

.iconfont-list .icon {
    font-size: 32px;
    line-height: 46px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    display: block;
    color: rgb(0,0,0);
    -webkit-transition: font-size .25s ease-out 0s;
    -moz-transition: font-size .25s ease-out 0s;
    transition: font-size .25s ease-out 0s
}

.iconfont-list .icon:hover {
    font-size: 100px
}

.iconfont-list .code {
    color: #008000;
    font-weight: bold
}
    @font-face {
        font-family: 'icon-min';

        src: url(data:font/ttf;charset=utf-8;base64,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) format('truetype');

    }
    .icon-min {
        font-family: 'icon-min';
    }
    @font-face {
        font-family: 'icon-checkbox';

        src: url(data:font/ttf;charset=utf-8;base64,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) format('truetype');

    }
    .icon-checkbox {
        font-family: 'icon-checkbox';
    }

    @font-face {
        font-family: 'icon-corner';

        src: url(data:font/ttf;charset=utf-8;base64,AAEAAAAKAIAAAwAgT1MvMkiTZ6gAAACsAAAAYGNtYXDyGOXTAAABDAAAAVJnbHlmJXUGNAAAAmAAAADUaGVhZAr8ouQAAAM0AAAANmhoZWEHwwOJAAADbAAAACRobXR4EiUBgAAAA5AAAAAUbG9jYQCcAMwAAAOkAAAADG1heHAACAAZAAADsAAAACBuYW1lGVKlzAAAA9AAAAGtcG9zdDjdfHcAAAWAAAAATAAEA6EBkAAFAAACmQLMAAAAjwKZAswAAAHrADMBCQAAAgAGAwAAAAAAAAAAAAEQAAAAAAAAAAAAAABQZkVkAMDyAPIDAyz/LABcAywA1AAAAAEAAAAAAxgAAAAAACAAAQAAAAMAAAADAAAAHAABAAAAAABMAAMAAQAAABwABAAwAAAACAAIAAIAAPIB8gLyA///AADyAPIC8gP//w4BDgIOAAABAAAAAAAAAAAAAAEGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAiAAABMgKqAAMABwAANxEhESczESMiARDuzMwAAqr9ViICZgAAAAEAUP/OA+UCkAAYAAABFhcWFwYHBgchKwEGFxYXJicmJz4BNzsBAuRsSkgDA0hIbP7YAmw/Dw8bbkVFAgTHlgkIApADS0twcEtLAjAzMxslXF17mcwEAAABAB4AtAQbAZsAAwAAAQchJwEE5gP95QGb5+cAAQCgALQDaQGZAAMAAAEHIScBhOQCyeUBmeXlAAEAUAC0A+gBmgADAAABByEnATbmA5jmAZrm5gABAAAAAQAA9eRLnl8PPPUACwQAAAAAANPRibgAAAAA09LVtwAe/84EGwKqAAAACAACAAAAAAAAAAEAAAMs/ywAXAQ5AB4AHgQbAAEAAAAAAAAAAAAAAAAAAAAFAXYAIgQ1AFAEOQAeBAkAoAQ4AFAAAAAUAEAATgBcAGoAAQAAAAUAGQACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAlgABAAAAAAABAAoAAAABAAAAAAACAAYACgABAAAAAAADABsAEAABAAAAAAAEAAoAKwABAAAAAAAFAB4ANQABAAAAAAAGAAoAUwADAAEECQABABQAXQADAAEECQACAAwAcQADAAEECQADADYAfQADAAEECQAEABQAswADAAEECQAFADwAxwADAAEECQAGABQBA2ZvbnRlZGl0b3JNZWRpdW1Gb250RWRpdG9yIDEuMCA6IGZvbnRlZGl0b3Jmb250ZWRpdG9yVmVyc2lvbiAxLjA7IEZvbnRFZGl0b3IgKHYxLjApZm9udGVkaXRvcgBmAG8AbgB0AGUAZABpAHQAbwByAE0AZQBkAGkAdQBtAEYAbwBuAHQARQBkAGkAdABvAHIAIAAxAC4AMAAgADoAIABmAG8AbgB0AGUAZABpAHQAbwByAGYAbwBuAHQAZQBkAGkAdABvAHIAVgBlAHIAcwBpAG8AbgAgADEALgAwADsAIABGAG8AbgB0AEUAZABpAHQAbwByACAAKAB2ADEALgAwACkAZgBvAG4AdABlAGQAaQB0AG8AcgAAAAACAAAAAAAAADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAECAQMBBAEFB3VuaUYyMDAHdW5pRjIwMQd1bmlGMjAzB3VuaUYyMDI=) format('truetype');

    }
    .icon-corner {
        font-family: 'icon-corner';
    }
    @font-face {
        font-family: 'icon-tag';

        src: url(data:font/ttf;charset=utf-8;base64,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) format('truetype');

    }
    .icon-tag {
        font-family: 'icon-tag';
    }
    @font-face {
        font-family: 'icon-notice';

        src: url(data:font/ttf;charset=utf-8;base64,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) format('truetype');

    }
    .icon-notice {
        font-family: 'icon-notice';
    }
</style>

</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="form">
    <h1 class="title">ICON</h1>
    <div class="demo-item">
        <p class="demo-desc">默认icon（其他icon单独引用在各自组件里）</p>
        <div class="demo-block">
            <ul class="iconfont-list">

                <li>
                    <i class="icon icon-min"></i>
                    <div class="code">\f001</div>
                </li>

                <li>
                    <i class="icon icon-min"></i>
                    <div class="code">\f002</div>
                </li>

                <li>
                    <i class="icon icon-min"></i>
                    <div class="code">\f003</div>
                </li>

                <li>
                    <i class="icon icon-min"></i>
                    <div class="code">\f004</div>
                </li>

                <li>
                    <i class="icon icon-min"></i>
                    <div class="code">\f000</div>
                </li>

                <li>
                    <i class="icon icon-min"></i>
                    <div class="code">\f005</div>
                </li>
                <li>
                    <i class="icon icon-min"></i>
                    <div class="code">\f006</div>
                </li>

            </ul>
        </div>
        <p class="demo-desc">checkbox使用的icon</p>
        <div class="demo-block">
            <ul class="iconfont-list">

                <li>
                    <i class="icon icon-checkbox"></i>
                    <div class="code">\f100</div>
                </li>

                <li>
                    <i class="icon icon-checkbox"></i>
                    <div class="code">\f101</div>
                </li>

                <li>
                    <i class="icon icon-checkbox"></i>
                    <div class="code">\f102</div>
                </li>

                <li>
                    <i class="icon icon-checkbox"></i>
                    <div class="code">\f103</div>
                </li>

            </ul>
        </div>
        <p class="demo-desc">角标使用的icon</p>
        <div class="demo-block">
            <ul class="iconfont-list">

                <li>
                    <i class="icon icon-corner"></i>
                    <div class="code">\f200</div>
                </li>

                <li>
                    <i class="icon icon-corner"></i>
                    <div class="code">\f201</div>
                </li>

                <li>
                    <i class="icon icon-corner"></i>
                    <div class="code">\f203</div>
                </li>

                <li>
                    <i class="icon icon-corner"></i>
                    <div class="code">\f202</div>
                </li>

            </ul>
        </div>
        <p class="demo-desc">标签使用的icon</p>
        <div class="demo-block">
            <ul class="iconfont-list">

                <li>
                    <i class="icon icon-tag"></i>
                    <div class="code">\f300</div>
                </li>

                <li>
                    <i class="icon icon-tag"></i>
                    <div class="code">\f301</div>
                </li>

                <li>
                    <i class="icon icon-tag"></i>
                    <div class="code">\f302</div>
                </li>

            </ul>
        </div>
        <p class="demo-desc">空白提示页的icon</p>
        <div class="demo-block">
            <ul class="iconfont-list">

                <li>
                    <i class="icon icon-notice"></i>
                    <div class="code">\f400</div>
                </li>

            </ul>
        </div>

    </div>
    </section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    

    
</body>
</html>
