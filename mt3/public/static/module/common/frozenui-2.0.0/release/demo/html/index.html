<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../css/frozenui.css">
    <link rel="stylesheet" href="../css/style.css">
    

    


</head>

<body ontouchstart>
    <section class="ui-container">
        
    <div class="index-wrap">
        <div class="header">
            <h1>FrozenUI</h1>
            <h2>体验更自然 触达更高效</h2>
        </div>
        <section id="list">
            <table class="demo-item ui-table ui-border">
                <tbody class="demo-block">
                    <tr class="">
                        <td class="" data-href="component/button/btn.html">
                            <div>
                                <div class="icon-button icon"></div>
                                <div class="tit">按钮</div>
                                <div class="sub-tit">Button</div>
                            </div>
                        </td>
                        <td class="" data-href="component/list/list.html">
                            <div>
                                <div class="icon-cell icon"></div>
                                <div class="tit">列表</div>
                                <div class="sub-tit">List</div>
                            </div>
                        </td>
                        <td data-href="component/tips/tips.html">
                            <div>
                                <div class="icon-toast icon"></div>
                                <div class="tit">提示</div>
                                <div class="sub-tit">Tips</div>
                            </div>
                        </td>
                    </tr>
                    <tr class="">
                        <td class="" data-href="component/dialog/dialog.html">
                            <div>
                                <div class="icon-dialog icon"></div>
                                <div class="tit">对话框</div>
                                <div class="sub-tit">Dialog</div>
                            </div>
                        </td>
                        <td class="" data-href="component/actionsheet/actionsheet.html">
                            <div>
                                <div class="icon-actionsheet icon"></div>
                                <div class="tit">动作</div>
                                <div class="sub-tit">ActionSheet</div>
                            </div>
                        </td>
                        <td class="" data-href="component/tab/tab.html">
                            <div>
                                <div class="icon-tab icon"></div>
                                <div class="tit">导航</div>
                                <div class="sub-tit">Tab</div>
                            </div>
                        </td>
                    </tr>
                    <tr class="">
                        <td class="" data-href="component/form/form.html">
                            <div>
                                <div class="icon-form icon"></div>
                                <div class="tit">表单</div>
                                <div class="sub-tit">Form</div>
                            </div>
                        </td>
                        <td class="" data-href="component/grid/grid.html">
                            <div>
                                <div class="icon-grid icon"></div>
                                <div class="tit">网格</div>
                                <div class="sub-tit">Grid</div>
                            </div>
                        </td>
                        <td class="" data-href="component/panel/panel.html">
                            <div>
                                <div class="icon-panel icon"></div>
                                <div class="tit">面板</div>
                                <div class="sub-tit">Panel</div>
                            </div>
                        </td>

                    </tr>
                    <tr>
                        <td class="" data-href="component/search/searchbar.html">
                            <div>
                                <div class="icon-searchbar icon"></div>
                                <div class="tit">搜索</div>
                                <div class="sub-tit">Searchbar</div>
                            </div>
                        </td>
                        <td class="" data-href="component/decorate/subscript.html">
                            <div>
                                <div class="icon-icons icon"></div>
                                <div class="tit">角标</div>
                                <div class="sub-tit">subscript</div>
                            </div>
                        </td>
                        <td class="" data-href="more.html">
                            <div>
                                <div class="icon-more icon"></div>
                                <div class="tit">More</div>
                                <div class="sub-tit">More</div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>
        <div class="footer">
            <div class="logo"></div>
        </div>
    </div>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../js/lib/zepto.min.js"></script>
    <script src="../js/index.js"></script>
    

    
</body>
</html>
