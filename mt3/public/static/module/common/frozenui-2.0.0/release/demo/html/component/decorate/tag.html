<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    

    
</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="tag">
    <h1 class="title">TAG</h1>
    <div class="demo-item">
        <p class="demo-desc">角标</p>
        <div class="demo-block">
            <ul class="ui-grid-halve">
                <li>
                    <div class="ui-grid-halve-img ui-tag-svip">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>
                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-vip">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>
                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-free">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>

                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-freelimit">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>
                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-last">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>

                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-limit">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>
                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-act">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>

                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-xy">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>
                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-selected">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)" class="ui-tag-new"></span>
                    </div>
                </li>
                <li>
                    <div class="ui-grid-halve-img ui-tag-selected ui-tag-vip">
                        <span style="background-image:url(http://placeholder.qiniudn.com/290x160)"></span>
                    </div>
                </li>
            </ul>
            <p class="ui-tag-wrap">自定义主题<i class="ui-tag-vip"></i></p>
        </div>
    </div>
</section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    

    
</body>
</html>
