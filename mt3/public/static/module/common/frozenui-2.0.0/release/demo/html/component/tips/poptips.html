<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    

    
</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="poptips">
  <div class="demo-item">
      <p class="demo-desc">浮层提示条</p>
      <div class="demo-block">
        <div class="ui-poptips ui-poptips-info" style="top:50px;">
            <div class="ui-poptips-cnt"><i></i>普通提示条</div>
        </div>
        <div class="ui-poptips ui-poptips-warn" style="top:100px;">
            <div class="ui-poptips-cnt"><i></i>警示提示条</div>
        </div>
        <div class="ui-poptips ui-poptips-success" style="top:150px;">
            <div class="ui-poptips-cnt"><i></i>成功提示条</div>
        </div>
      </div>
  </div>
</section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    

    
</body>
</html>
