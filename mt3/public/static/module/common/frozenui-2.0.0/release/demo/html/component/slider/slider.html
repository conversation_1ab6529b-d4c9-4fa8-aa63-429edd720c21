<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    

    
</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="slider">
    <div class="demo-item">
        <p class="demo-desc">轮播组件</p>
        <div class="demo-block">
            <div class="ui-slider">
                <ul class="ui-slider-content" style="width: 300%">
                    <li><span style="background-image:url(http://placeholder.qiniudn.com/640x200)"></span></li>
                    <li><span style="background-image:url(http://placeholder.qiniudn.com/640x200)"></span></li>
                    <li><span style="background-image:url(http://placeholder.qiniudn.com/640x200)"></span></li>
                </ul>
                <ul class="ui-slider-indicators"><li class="current">1</li><li class="">2</li><li class="">3</li></ul>
            </div>
            内容


        </div>

    </div>
</section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    

    
</body>
</html>
