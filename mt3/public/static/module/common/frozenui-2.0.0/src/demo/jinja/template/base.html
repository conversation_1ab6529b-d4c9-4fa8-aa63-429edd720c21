<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    {% if data.page == 'index' %}
    <link rel="stylesheet" href="../../css/frozenui.css">
    <link rel="stylesheet" href="../css/style.css">
    {% elif data.page == 'util' %}
    <link rel="stylesheet" href="../../../css/frozenui.css">
    <link rel="stylesheet" href="../../css/style.css">
    {% else %}
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    {% endif %}

    {% block style %}{% endblock %}
</head>

<body ontouchstart>
    <section class="ui-container">
        {% block content %}{% endblock %}
    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    {% if data.page == 'index' %}
    <script src="../js/lib/zepto.min.js"></script>
    <script src="../js/index.js"></script>
    {% elif data.page == 'util' %}
    <script src="../../js/lib/zepto.min.js"></script>
    <script src="../../js/index.js"></script>
    {% else %}
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    {% endif %}

    {% block script %}{% endblock %}
</body>
</html>
