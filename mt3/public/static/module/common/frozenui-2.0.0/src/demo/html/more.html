<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../css/frozenui.css">
    <link rel="stylesheet" href="../css/style.css">
    

    


</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="list">
    <h1 class="title">MORE</h1>
    <div class="demo-item">
        <div class="demo-block">
            <ul class="ui-list ui-list-pure ui-border-b">
                <li class="ui-border-t" data-href="base/styles/type.html">
                  文本
                </li>
                <li class="ui-border-t" data-href="base/icons/icon.html">
                  图标
                </li>
                <li class="ui-border-t" data-href="util/layout.html">
                  布局相关
                </li>
                <li class="ui-border-t" data-href="util/arrowlink.html">
                  箭头链接
                </li>
                <li class="ui-border-t" data-href="util/border.html">
                  1px解决方案
                </li>
                <li class="ui-border-t" data-href="util/nowrap.html">
                  文字截断
                </li>
                <li class="ui-border-t" data-href="util/placehold.html">
                  占位
                </li>
                <li class="ui-border-t" data-href="util/whitespace.html">
                  两端留白
                </li>
                <li class="ui-border-t" data-href="util/justify.html">
                  两端对齐
                </li>
                <li class="ui-border-t" data-href="util/avatar.html">
                  圆角头像
                </li>
                <li class="ui-border-t" data-href="component/vip/vip.html">
                  QQ会员图标
                </li>
                <li class="ui-border-t" data-href="component/vip/tooltips-vip.html">
                  会员开通栏
                </li>
                <li class="ui-border-t" data-href="component/decorate/badge.html">
                  徽章
                </li>
                <li class="ui-border-t" data-href="component/decorate/reddot.html">
                  红点
                </li>
                <li class="ui-border-t" data-href="component/decorate/tag.html">
                  角标
                </li>
                <li class="ui-border-t" data-href="component/loading/loading.html">
                  加载中
                </li>
                <li class="ui-border-t" data-href="component/table/table.html">
                  表格
                </li>
                <li class="ui-border-t" data-href="component/notice/notice.html">
                  通知
                </li>
                <li class="ui-border-t" data-href="component/progress/progress.html">
                  进度条
                </li>
            </ul>
        </div>
    </div>
</section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../js/lib/zepto.min.js"></script>
    <script src="../js/index.js"></script>
    

    
</body>
</html>
