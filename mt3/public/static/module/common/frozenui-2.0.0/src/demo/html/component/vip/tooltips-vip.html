<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    

    
</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="vip">
    <h1 class="title">VIP TOOLTIPS</h1>
    <div class="demo-item">
        <p class="demo-desc">开通栏</p>
        <div class="demo-block">
            <div class="ui-tooltips-vip">
                <div class="ui-tooltips-cnt">
                    <i class="ui-icon-svip"></i>
                    <span>开通超级会员尊享精彩表情</span>
                    <button class="ui-btn-vip">开通</button>
                </div>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <div class="demo-block">
            <div class="ui-tooltips-footer-vip">
            	<div class="ui-tooltips-cnt">
            		<button class="ui-btn-lg-vip">
            			<span class="ui-icon-svip"></span>
            			<span>开通超级会员</span>
            		</button>
            	</div>
            </div>
        </div>
    </div>
</section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    

    
</body>
</html>
