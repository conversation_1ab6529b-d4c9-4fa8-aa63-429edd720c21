<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="HandheldFriendly" content="true">
    <title>QUI Demo</title>
    <script type="text/javascript" src="http://tajs.qq.com/stats?sId=37342703" charset="UTF-8"></script>

    
    <link rel="stylesheet" href="../../../../css/frozenui.css">
    <link rel="stylesheet" href="../../../css/style.css">
    

    
</head>

<body ontouchstart>
    <section class="ui-container">
        
<section id="panel">
    <h1 class="title">PANEL</h1>
    <div class="demo-item">
        <p class="demo-desc">默认面板</p>
        <div class="demo-block">
            <section class="ui-panel ui-grid ui-grid-trisect">
                <h2 class="ui-arrowlink">猜你喜欢<span class="ui-panel-subtitle">1328条</span></h2>
                <ul>
                    <li>
                        <div class="ui-img-vertical">
                            <span style="background-image:url(http://placeholder.qiniudn.com/190x284)"></span>
                        </div>
                        <div class="ui-grid-info ui-border-r">
                            <h4 class="ui-nowrap-multi">我是主标题</h4>
                            <p class="ui-nowrap">这里是副标题</p>
                        </div>
                    </li>
                    <li>
                        <div class="ui-img-vertical">
                            <span style="background-image:url(http://placeholder.qiniudn.com/190x284)"></span>
                        </div>
                        <div class="ui-grid-info ui-border-r">
                            <h4 class="ui-nowrap-multi">我是主标题</h4>
                            <p class="ui-nowrap">这里是副标题</p>
                        </div>
                    </li>
                    <li>
                        <div class="ui-img-vertical">
                            <span style="background-image:url(http://placeholder.qiniudn.com/190x284)"></span>
                        </div>
                        <div class="ui-grid-info ui-border-r">
                            <h4 class="ui-nowrap-multi">我是主标题</h4>
                            <p class="ui-nowrap">这里是副标题</p>
                        </div>
                    </li>
                </ul>
                <div class="ui-panel-more ui-border-tb">
                    <span class="ui-arrowlink">查看全部</span>
                </div>
            </section>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">ui-panel-center 居中</p>
        <div class="demo-block">
            <section class="ui-panel ui-panel-center ui-border-tb">
                <h2 class="ui-arrowlink"><span>标题文字</span><span class="ui-panel-title-tips">分类更多</span></h2>
            </section>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">ui-panel-center-multi 居中两行</p>
        <div class="demo-block">
            <section class="ui-panel ui-panel-center-multi ui-border-tb">
                <h2 class="ui-arrowlink"><span>新货精选</span><span class="ui-panel-subtitle">鲜肉来咯擦干净口水好吗</span><span class="ui-panel-title-tips">分类更多</span></h2>
            </section>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">简单版</p>
        <div class="demo-block">
            <section class="ui-panel ui-border-t">
                <h4>历史搜索：</h4>
                <ul class="ui-list ui-list-pure ui-border-tb">
                    <li class="ui-border-t">
                        <h5><span>1.faycheng </span><span class="date"> 2月12日</span></h5>
                        <h4>这本书太赞了，每次看都有不一样的体会和感悟，超级喜欢！期待大结局。</h4>
                    </li>
                    <li class="ui-border-t">
                        <h5><span>2.faycheng </span><span class="date"> 2月12日</span></h5>
                        <h4>标题标题标题标题标题标题题标题标题标题标题标题标题标题标题标题</h4>
                    </li>
                    <li class="ui-border-t">
                        <h5><span>3.faycheng </span><span class="date"> 2月12日</span></h5>
                        <h4>标题标题标题标题标题标题题标题标题标题标题标题标题标题标题标题</h4>
                    </li>
                </ul>
            </section>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">简单版</p>
        <div class="demo-block">
            <section class="ui-panel-pure ui-border-t">
                <h3>历史搜索：</h3>
                <ul class="ui-list ui-list-pure ui-border-tb">
                    <li class="ui-border-t">
                        <h5><span>1.faycheng </span><span class="date"> 2月12日</span></h5>
                        <h4>这本书太赞了，每次看都有不一样的体会和感悟，超级喜欢！期待大结局。</h4>
                    </li>
                    <li class="ui-border-t">
                        <h5><span>2.faycheng </span><span class="date"> 2月12日</span></h5>
                        <h4>标题标题标题标题标题标题题标题标题标题标题标题标题标题标题标题</h4>
                    </li>
                    <li class="ui-border-t">
                        <h5><span>3.faycheng </span><span class="date"> 2月12日</span></h5>
                        <h4>标题标题标题标题标题标题题标题标题标题标题标题标题标题标题标题</h4>
                    </li>
                </ul>
            </section>
        </div>
    </div>
</section>

    </section>

    <script src="http://open.mobile.qq.com/sdk/qqapi.js?_bid=152"></script>
    <script>
    var _mtac = {};
    (function() {
        var mta = document.createElement("script");
        mta.src = "http://pingjs.qq.com/h5/stats.js?v2.0.2";
        mta.setAttribute("name", "MTAH5");
        mta.setAttribute("sid", "500421336");
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(mta, s);
    })();
    </script>

    
    <script src="../../../js/lib/zepto.min.js"></script>
    <script src="../../../js/index.js"></script>
    

    
</body>
</html>
