{% extends "template/base.html"%}

{% block content %}
<section id="tab">
    <h1 class="title">TAB</h1>
    <div class="demo-item">
        <p class="demo-desc">2TAB</p>
        <div class="demo-block">
            <!--
             -->
            <div class="ui-tab ">
                <ul class="ui-tab-nav ui-border-b ">
                    <li class="current"><span>热门</span></li>
                    <li><span>热门</span></li>
                </ul>
                <ul class="ui-tab-content" style="width:200%">
                    <li>内容1</li>
                    <li>内容2</li>
                </ul>
            </div>

            <!--  -->
            <div class="demo-block">
                <!--
                 -->
                <div class="ui-tab ">
                    <ul class="ui-tab-nav ui-tab-nav-bisect-lg ui-border-b ">
                        <li class="current"><span>热门直播类</span></li>
                        <li><span>热门直播类</span></li>
                    </ul>
                    <ul class="ui-tab-content" style="width:200%">
                        <li>内容1</li>
                        <li>内容2</li>
                    </ul>
                </div>
            </div>

        </div>
    </div>
</section>



<!--  -->
<section id="tab">
    <div class="demo-item">
        <p class="demo-desc">3TAB</p>
        <div class="demo-block">
            <div class="ui-tab">
                <ul class="ui-tab-nav ui-border-b">
                  <li class="current"><span>热门直播类</span></li>
                  <li><span>热门直播类</span></li>
                  <li><span>热门直播类</span></li>
                </ul>
                <ul class="ui-tab-content" style="width:300%">
                    <li>内容1</li>
                    <li>内容2</li>
                    <li>内容2</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<section id="tab">
    <div class="demo-item">
        <p class="demo-desc">4TAB</p>
        <div class="demo-block">
            <div class="ui-tab">
                <ul class="ui-tab-nav ui-border-b">
                  <li class="current"><span>热门直播类</span></li>
                  <li><span>热门直播类</span></li>
                  <li><span>热门直播类</span></li>
                  <li><span>热门直播类</span></li>
                </ul>
                <ul class="ui-tab-content" style="width:400%">
                    <li>内容1</li>
                    <li>内容2</li>
                    <li>内容3</li>
                    <li>内容4</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<section id="tab">
    <div class="demo-item">
        <p class="demo-desc">5TAB</p>
        <div class="demo-block">
            <div class="ui-tab">
                <ul class="ui-tab-nav ui-border-b">
                  <li class="current"><span>热门</span></li>
                  <li><span>热门</span></li>
                  <li><span>热门</span></li>
                  <li><span>热门</span></li>
                  <li><span>热门</span></li>
                </ul>
                <ul class="ui-tab-content" style="width:500%">
                    <li>内容1</li>
                    <li>内容2</li>
                    <li>内容3</li>
                    <li>内容4</li>
                    <li>内容5</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<section id="tab">
    <div class="demo-item">
        <p class="demo-desc">6TAB</p>
        <div class="demo-block">
            <div class="ui-tab ">
                <div class="ui-tab-nav-wrap">
                    <ul class="ui-tab-nav ui-tab-nav-multi ui-border-b " style="width: 498px">
                      <li class="current"><span>热门1</span></li>
                      <li><span>热门2</span></li>
                      <li><span>热门3</span></li>
                      <li><span>热门4</span></li>
                      <li><span>热门5</span></li>
                      <li><span>热门6</span></li>
                    </ul>
                </div>
                <ul class="ui-tab-content" style="width:600%">
                    <li>内容1</li>
                    <li>内容2</li>
                    <li>内容3</li>
                    <li>内容4</li>
                    <li>内容5</li>
                    <li>内容6</li>
                </ul>
            </div>
        </div>
    </div>
</section>
{% endblock%}

{% block script %}
<script type="text/javascript">
(function() {


    var record = 0;
    var origin_l;
    $('.ui-tab-nav').eq(0).find('li').on('click',function() {

                  //记录原始点的位置:
                //   if(!record){
                //       origin_l = $('.ui-tab-nav li.current i').offset().left;
                //       record++;
                //   }
                  $(this).parent().find('li').removeClass('current');
                  $(this).addClass('current');
                //   var l = $(this).find('span').offset().left;
                //   var w = $(this).find('span').offset().width;
                //   $('.ui-tab-nav li.current i').css({
                //       'transform':'translate3d('+(l-origin_l)+'px,0,0)',
                //       'width':w,
                //       'transition':'transform 0.5s linear'
                //   });
                  $('.ui-tab-content').eq(0).css({
                    'transform':'translate3d(-'+($(this).index()*$('.ui-tab-content li').offset().width)+'px,0,0)',
                    'transition':'transform 0.5s linear'
                })
    });

})(window, undefined)
</script>
{% endblock%}
