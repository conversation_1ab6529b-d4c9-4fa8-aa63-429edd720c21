{% extends "template/base.html"%}

{% block content %}
<section id="tooltips" style="padding-bottom:150px;">
    <h1 class="title">TIPS</h1>
    <div class="demo-item">
        <p class="demo-desc">提示</p>
        <div class="demo-block">
            <div class="ui-tooltips">
                <div class="ui-tooltips-cnt ui-tooltips-cnt-link ui-border-b">
                    无法连接服务器，请检查你的网络设置。
                </div>
            </div>
            <div class="ui-tooltips">
                <div class="ui-tooltips-cnt ui-border-b">
                    无法连接服务器，请检查你的网络设置。<a class="ui-icon-close"></a>
                </div>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">警示提示</p>
        <div class="demo-block">
            <div class="ui-tooltips ui-tooltips-warn">
                <div class="ui-tooltips-cnt ui-tooltips-cnt-link ui-border-b">
                    <i></i>无法连接服务器，请检查你的网络设置。
                </div>
            </div>
            <div class="ui-tooltips ui-tooltips-warn">
                <div class="ui-tooltips-cnt ui-border-b">
                    <i></i>无法连接服务器，请检查你的网络设置。<a class="ui-icon-close"></a>
                </div>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">引导提示</p>
        <div class="demo-block">
            <div class="ui-tooltips ui-tooltips-guide">
                <div class="ui-tooltips-cnt ui-tooltips-cnt-link ui-border-b">
                    <i class="ui-icon-talk"></i>进行时状态相关进行时状态相关进行时状态相关进行时状态相关
                </div>
            </div>
            <div class="ui-tooltips ui-tooltips-guide">
                <div class="ui-tooltips-cnt ui-border-b">
                    <i></i>新手引导、新功能推荐、广告推广等。新手引导、新功能推荐、广告推广等。<a class="ui-icon-close"></a>
                </div>
            </div>
            <div class="ui-tooltips ui-tooltips-guide ui-tooltips-function">
                <div class="ui-tooltips-cnt ui-border-b">
                    <i></i>新手引导、新功能推荐、广告推广等。新手引导、新功能推荐、广告推广等。<button class="ui-btn-s">开通</button>
                </div>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">黑色半透明提示条，在页面顶部为图片cover时使用</p>
        <div class="demo-block">
            <div class="ui-tooltips ui-tooltips-warn ui-tooltips-hignlight">
                <div class="ui-tooltips-cnt ui-tooltips-cnt-link ui-border-b">
                    <i></i>无法连接服务器，请检查你的网络设置。
                </div>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <div class="demo-block">
          <div class="ui-tooltips ui-tooltips-guide ui-tooltips-action">
              <div class="ui-tooltips-cnt ui-border-b">
                  <i></i>底部引导条<button class="ui-btn-s">开通</button><a class="ui-icon-close"></a>
              </div>
          </div>
        </div>
    </div>
</section>
{% endblock%}
