<!DOCTYPE html>
<!-- saved from url=(0039)http://font.baidu.com/editor/empty.html -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <title>fonteditor</title>
    <script src="./checkbox_files/hm.js"></script><script>
    var _hmt = _hmt || [];
    /baidu.com$/.test(location.hostname) && (function() {
      var hm = document.createElement("script");
      hm.src = "//hm.baidu.com/hm.js?65ce30cdeda584c416e39648915689f7";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
    </script>
</head>
<body data-pinterest-extension-installed="cr1.40"><style>*{margin:0;padding:0;list-style:none}blockquote,body,button,dd,dl,dt,fieldset,form,h1,h2,h3,h4,h5,h6,hr,input,legend,li,ol,p,pre,td,textarea,th,ul{margin:0;padding:0}body,button,input,select,textarea{font:12px/1.5 tahoma,arial,sans-serif}h1,h2,h3,h4,h5,h6{font-size:100%}address,cite,dfn,em,var{font-style:normal}code,kbd,pre,samp{font-family:courier new,courier,monospace}small{font-size:12px}ol,ul{list-style:none}a{text-decoration:none}a:hover{text-decoration:underline}legend{color:#000}fieldset,img{border:0}button,input,select,textarea{font-size:100%}table{border-collapse:collapse;border-spacing:0}.main{padding:30px 100px}.main h1{font-size:36px;color:#333;text-align:left;margin-bottom:30px;border-bottom:1px solid #eee}.helps{margin-top:40px}.helps pre{padding:20px;margin:10px 0;border:solid 1px #e7e1cd;background-color:#fffdef;overflow:auto}.iconfont-list{overflow:hidden}.iconfont-list li{float:left;width:100px;height:150px;text-align:center}.iconfont-list .icon{font-size:42px;line-height:100px;margin:10px 0;color:#333;font-style:normal;-webkit-transition:font-size .25s ease-out 0s;-moz-transition:font-size .25s ease-out 0s;transition:font-size .25s ease-out 0s}.iconfont-list .icon:hover{font-size:100px}.iconfont-list .code{color:#008000;font-weight:bold}</style>
<style>
    @font-face {
        font-family: 'fonteditor';
        
        src: url(data:font/ttf;charset=utf-8;base64,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) format('truetype');
        
    }
    .icon {
        font-family: 'fonteditor';
    }
</style>
<div class="main">
    <h1>预览ttf格式字体</h1>
    <ul class="iconfont-list">
        
        <li>
            <i class="icon"></i>
            <div class="code">\f100</div>
            <div class="name">uniF100</div>
        </li>
        
        <li>
            <i class="icon"></i>
            <div class="code">\f101</div>
            <div class="name">uniF101</div>
        </li>
        
        <li>
            <i class="icon"></i>
            <div class="code">\f102</div>
            <div class="name">uniF102</div>
        </li>
        
        <li>
            <i class="icon"></i>
            <div class="code">\f103</div>
            <div class="name">uniF103</div>
        </li>
        
    </ul>
    <div class="helps">第一步：使用font-face声明字体
<pre>@font-face {
    font-family: 'fonteditor';
    src: url('fonteditor.eot'); /* IE9*/
    src: url('fonteditor.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('fonteditor.woff') format('woff'), /* chrome、firefox */
    url('fonteditor.ttf') format('truetype'), /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
    url('fonteditor.svg#uxfonteditor') format('svg'); /* iOS 4.1- */
}
</pre>
第二步：定义使用fonteditor的样式
<pre>.fonteditor {
    font-family: "fonteditor" !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-tree:after {content: '\33';}
</pre>
第三步：挑选相应图标并获取字体编码，应用于页面
<pre>    &lt;i class="fonteditor"&gt;&amp;#x33&lt;/i&gt;

or

    &lt;i class="fonteditor icon-three"&gt;&lt;/i&gt;
</pre>
</div>
</div>
</body></html>