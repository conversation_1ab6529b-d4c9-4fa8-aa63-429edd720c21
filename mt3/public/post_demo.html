<!DOCTYPE html>  
<html lang="en">  
<head>  
<meta charset="UTF-8">  
<meta name="viewport" content="width=device-width, initial-scale=1.0">  
<title>Phase Situation Fetcher</title>  
</head>  
<body>  
  
<div id="phaseSituation"></div>  
  
<script>  
// 假设的API地址  
var apiUrl = 'http://zongjiao.xingpan.vip/astrology/luck/moon.html';  
  
// 初始化计时器ID  
var timerId;  
  
// 请求数据的函数  
function fetchData() {  
    fetch(apiUrl)  
        .then(response => response.json())  
        .then(data => {  
            // 显示phase_situation  
            var phaseSituationElement = document.getElementById('phaseSituation');  
            phaseSituationElement.textContent = data.corpus.phase_situation;  
  
            // 检查stat_date和end_date是否小于1  
            if (data.stat_date < 1 || data.end_date < 1) {  
                // 如果任一条件满足，则清除计时器并停止请求  
                clearInterval(timerId);  
                console.log('Stopped fetching data due to date condition.');  
            }  
        })  
        .catch(error => {  
            console.error('Error fetching data:', error);  
            // 在发生错误时也可以考虑停止请求  
            clearInterval(timerId);  
        });  
}  
  
// 开始每500毫秒请求一次数据  
timerId = setInterval(fetchData, 500);  
</script>  
  
</body>  
</html>