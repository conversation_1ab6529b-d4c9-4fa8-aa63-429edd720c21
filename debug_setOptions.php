<?php
/**
 * 调试 setOptions 方法问题
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试 setOptions 方法</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

// 按正确顺序包含文件
echo "<h2>1. 包含文件</h2>\n";

$files = [
    'jyotish/src/Base/Traits/OptionTrait.php',
    'jyotish/src/Base/Traits/GetTrait.php',
    'jyotish/src/Base/Utility.php',
    'jyotish/src/Renderer/AbstractRenderer.php',
    'jyotish/src/Renderer/Image.php',
    'jyotish/src/Draw.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        require_once $file;
        echo "<span class='success'>✓</span> 包含: $file<br>\n";
    } else {
        echo "<span class='error'>✗</span> 缺失: $file<br>\n";
        exit("无法继续");
    }
}

echo "<h2>2. 检查类和方法</h2>\n";

// 检查 trait 是否存在
if (trait_exists('Jyotish\\Base\\Traits\\OptionTrait')) {
    echo "<span class='success'>✓</span> OptionTrait 存在<br>\n";
} else {
    echo "<span class='error'>✗</span> OptionTrait 不存在<br>\n";
}

// 检查 AbstractRenderer 类
if (class_exists('Jyotish\\Draw\\Renderer\\AbstractRenderer')) {
    echo "<span class='success'>✓</span> AbstractRenderer 类存在<br>\n";
    
    $reflection = new ReflectionClass('Jyotish\\Draw\\Renderer\\AbstractRenderer');
    $traits = $reflection->getTraitNames();
    echo "<span class='info'>使用的 traits:</span> " . implode(', ', $traits) . "<br>\n";
    
    if ($reflection->hasMethod('setOptions')) {
        echo "<span class='success'>✓</span> AbstractRenderer 有 setOptions 方法<br>\n";
    } else {
        echo "<span class='error'>✗</span> AbstractRenderer 没有 setOptions 方法<br>\n";
    }
} else {
    echo "<span class='error'>✗</span> AbstractRenderer 类不存在<br>\n";
}

// 检查 Image 类
if (class_exists('Jyotish\\Draw\\Renderer\\Image')) {
    echo "<span class='success'>✓</span> Image 类存在<br>\n";
    
    $reflection = new ReflectionClass('Jyotish\\Draw\\Renderer\\Image');
    
    if ($reflection->hasMethod('setOptions')) {
        echo "<span class='success'>✓</span> Image 类有 setOptions 方法<br>\n";
    } else {
        echo "<span class='error'>✗</span> Image 类没有 setOptions 方法<br>\n";
    }
    
    // 列出所有方法
    $methods = $reflection->getMethods();
    echo "<span class='info'>Image 类的所有方法:</span><br>\n";
    foreach ($methods as $method) {
        echo "- " . $method->getName() . "<br>\n";
    }
} else {
    echo "<span class='error'>✗</span> Image 类不存在<br>\n";
}

echo "<h2>3. 实际测试</h2>\n";

try {
    use Jyotish\Draw\Draw;
    
    // 创建 Draw 实例
    $draw = new Draw(200, 150, Draw::RENDERER_IMAGE);
    echo "<span class='success'>✓</span> 成功创建 Draw 实例<br>\n";
    
    // 获取渲染器
    $renderer = $draw->Renderer;
    echo "<span class='info'>渲染器类型:</span> " . get_class($renderer) . "<br>\n";
    
    // 检查渲染器是否有 setOptions 方法
    if (method_exists($renderer, 'setOptions')) {
        echo "<span class='success'>✓</span> 渲染器有 setOptions 方法<br>\n";
        
        // 尝试调用 setOptions
        $options = ['fontColor' => '#FF0000'];
        $renderer->setOptions($options);
        echo "<span class='success'>✓</span> 成功调用 setOptions<br>\n";
        
    } else {
        echo "<span class='error'>✗</span> 渲染器没有 setOptions 方法<br>\n";
    }
    
    // 尝试通过 Draw 类调用 setOptions
    $draw->setOptions(['strokeColor' => '#00FF00']);
    echo "<span class='success'>✓</span> 通过 Draw 类成功调用 setOptions<br>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 测试失败: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>4. 解决方案</h2>\n";
echo "<p>如果上面的测试显示问题，可能的解决方案：</p>\n";
echo "<ol>\n";
echo "<li>确保文件包含顺序正确</li>\n";
echo "<li>检查 trait 是否正确定义</li>\n";
echo "<li>验证类继承关系</li>\n";
echo "<li>清除 PHP 缓存（如果使用 OPcache）</li>\n";
echo "</ol>\n";
?>
