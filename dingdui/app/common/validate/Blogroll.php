<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Licensed   | http://www.apache.org/licenses/LICENSE-2.0 )           |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+

namespace app\common\validate;

/**
 * 友情链接验证器
 */
class Blogroll extends ValidateBase
{
    
    // 验证规则
    protected $rule =   [
        
        'name'              => 'require|unique:blogroll',
        'url'               => 'require',
        'sort'              => 'require|number',
    ];

    // 验证提示
    protected $message  =   [
        
        'name.require'              => '链接名称不能为空',
        'name.unique'               => '链接名称已存在',
        'url.require'               => '链接URL不能为空',
        'sort.require'              => '排序值不能为空',
        'sort.number'               => '排序值必须为数字'
    ];

    // 应用场景
    protected $scene = [
        
        'edit' =>  ['name','url','sort'],
    ];
    
}
