<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\chart;

/**
 * 月返盘
 */
class LunarreTurn extends PlateSingle
{

    public function plateData($param)
    {
        $house = $param['birth_points'] . ',' . $param['h_sys'];

        $planet_degree=array();
        !empty($param['planet_degree']) && $planet_degree=$param['planet_degree'];

        $allow_degree=$this->getAllowDegree($param);

        $starsCode = $param['planets'];

        $birthdayToTime = strtotime($param['birthday']) - $param['time_zone'] * 3600 + $param['is_summer'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $arr = [
            'b' => $utdatenow,
            'p' => '1',
            'ut' => $utnow,
            'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec',

        ];

        $exSweTest = get_sington_object('SweTest', "astrology\\SweTest");

        $planet_json = $exSweTest->SweTest($arr,[1]);

        $lineInfo = explode(',', $planet_json[0]);

        $defug = trim($lineInfo[2], ' ');

        $transitToTime = strtotime($param['current_date'])- $param['time_zone'] * 3600 + $param['is_summer'] * 3600;

        $transit_birth =$transitToTime-86400*15;
        $arr = [
            'b' => date('d.m.Y', $transit_birth),
            'ut' => '00:00:0',
            'p' => '1',
            'n' => '16',
            's' => '1',
            'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'e' => 'swe',
            'head',
            'roundsec',

        ];

        $behold_planet = $exSweTest->transitsProgressed($arr, $defug, 'd');

        if(empty($behold_planet)){
            $transit_birth -= 86400*15;
            $arr = [
                'b' => date('d.m.Y', $transit_birth),
                'ut' => '00:00:0',
                'p' => '1',
                'n' => '16',
                's' => '1',
                'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'e' => 'swe',
                'head',
                'roundsec',

            ];
            $behold_planet = $exSweTest->transitsProgressed($arr, $defug, 'd') ;
        }
        $planets = implode('', $starsCode);

        $utdatenow = date('d.m.Y', $behold_planet);

        $utnow = date('H:i:s', $behold_planet);

        $arr = [
            'b' => $utdatenow,
            'p' => $planets,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $starsCode=$this->getStarsCode($param,$starsCode);
        $ay=false;
        if(isset($param['ay'])){
            $ay=$param['ay'];
        }

        $data = $exSweTest->calculate($arr, $starsCode,[],$ay);
        $planets_data['house'] = $this->housePlanet($exSweTest, $data);
        $planets_data['return_time'] = date('Y-m-d H:i', ($behold_planet+ $param['time_zone'] * 3600 - $param['is_summer'] * 3600));
        $sign_attribute = $this->signPlanet($data);
        $planets_data['sign'] = $sign_attribute['sign'];

        $planets_data['planet'] = $this->planetPhase($exSweTest, $data, $allow_degree,$planet_degree);

        if(!empty($param['svg_type']) and $param['svg_type']==1){
            $planets_data['svg'] = $this->simpleSvg($planets_data, $data,$param['format']);
        }else if(!empty($param['svg_type']) and $param['svg_type']==-1){

        }else{
            $planets_data['svg'] = $this->seniorSvg($planets_data, $data);
        }
        $planets_data['attribute'] = $sign_attribute['attribute'];

        if(!empty($param['is_corpus']) && $param['is_corpus']==1){
            $planets_data['corpus_list']=$this->corpusList($planets_data);
        }
        return $planets_data;
    }

    /**
     * 获取一句话
     */
    public function explainData($param = [])
    {

        $life_date = $param['birthday'];
        $transit_date = $param['current_date'];

        $birthdayToTime = strtotime($life_date) - $param['time_zone'] * 3600+ $param['is_summer'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $arr = [
            'b' => $utdatenow,
            'p' => '1',
            'ut' => $utnow,
            'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec',

        ];

        $p = get_sington_object('SweTest', "astrology\\SweTestEmpty");

        $planet_json = $p->SweTest($arr,[1]);

        $lineInfo = explode(',', $planet_json[0]);

        $defug = trim($lineInfo[2], ' ');

        $transit_birth =strtotime($transit_date)-86400*15;

        $utdatenow = date('d.m.Y', $transit_birth);

        $arr = [
            'b' => $utdatenow,
            'ut' => '00:00:0',
            'p' => '1',
            'n' => '16',
            's' => '1',
            'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'e' => 'swe',
            'head',
            'roundsec',

        ];
        $behold_planet = $p->transitsProgressed($arr, $defug, 'd') ;
        if(empty($behold_planet)){
            $transit_birth -= 86400*15;

            $utdatenow = date('d.m.Y', $transit_birth);

            $arr = [
                'b' => $utdatenow,
                'ut' => '00:00:0',
                'p' => '1',
                'n' => '16',
                's' => '1',
                'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'e' => 'swe',
                'head',
                'roundsec',

            ];
            $behold_planet = $p->transitsProgressed($arr, $defug, 'd') ;
        }

        $life_house = $param['birth_points'] . ',' . $param['h_sys'];

        $life_conditions = [
            'b' => date('d.m.Y', $behold_planet),
            'p' => '01',
            'house' => $life_house,
            'ut' => date('H:i:s', $behold_planet),
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $life_data = $p->SweTestQuery($life_conditions);

        $sumInfo = explode(',', $life_data[1]);

        $defug = floor(trim($sumInfo[3], ' '))-1;

        $houseChinese = array("一宫", "二宫", "三宫", "四宫", "五宫", "六宫", "七宫", "八宫", "九宫", "十宫", "十一宫", "十二宫");

        $CorpusConstellation = $this->logicCorpusConstellation->getCorpusConstellationInfo(['oneself' => '月亮', 'other' => $houseChinese[$defug], 'type' => 4, 'chartType' => 14], 'oneself,other,degree,keywords,content');

        $content = '';
        $title = '';
        $yue_time=$behold_planet + $param['time_zone'] * 3600 - $param['is_summer'] * 3600;

        if (!empty($CorpusConstellation)) {
            return ['title' => $CorpusConstellation['keywords'],
                'phase_str' => '月返月亮' . $houseChinese[$defug],
                'stat_date' =>  strtotime($transit_date)-$yue_time,
                'end_date' => 27.321582*86400-strtotime($transit_date)+$yue_time,
                'phase_situation' => date('Y-m-d H:i', $yue_time).'开始，持续一月',
                'content' => $CorpusConstellation['content']];
        } else {
            return '';
        }
    }

    public function corpusList($life_data)
    {
        $planet=$life_data['planet'];
        $houseChinese = array('',"一宫", "二宫", "三宫", "四宫", "五宫", "六宫", "七宫", "八宫", "九宫", "十宫", "十一宫", "十二宫");
        $allow_degree_cn = config('ext_astrology')['allow_degree_cn'];
        $planet_allow_degree_f=array();
        //宫位
        foreach ($planet as $key => $value) {

            $planet_allow_degree=$value['planet_allow_degree'];
//            foreach ($planet_allow_degree as $keys=>$item){
//                if(empty($planet_allow_degree_f[$item['code_name']][$value['code_name']])){
//                    $planet_allow_degree_f[$value['code_name']][$item['code_name']]=1;
//
//                    $CorpusConstellation = $this->logicCorpusConstellation->getCorpusConstellationInfo(['oneself' => $value['planet_chinese'], 'other' => $item['planet_chinese'], 'type' => 6, 'chartType' => 14], 'oneself,other,degree,keywords,content');
//                    if (!empty($CorpusConstellation)) {
//                        $corpus[] = ['title' => $CorpusConstellation['keywords'],
//                            'phase_str' => $value['planet_chinese']. $allow_degree_cn[$item['allow']]  .$item['planet_chinese'],
//                            'stat_date' => 1,
//                            'end_date' => 0,
//                            'phase_situation' => '',
//                            'content' => $CorpusConstellation['content']];
//                    }
//                }
//            }

            $CorpusConstellation = $this->logicCorpusConstellation->getCorpusConstellationInfo(['oneself' => $value['planet_chinese'], 'other' => $houseChinese[$value['house_id']], 'type' => 4, 'chartType' => 14], 'oneself,other,degree,keywords,content');
            if (!empty($CorpusConstellation)) {
                $corpus[] = ['title' => $CorpusConstellation['keywords'],
                    'phase_str' => '月返'.$value['planet_chinese'] . $houseChinese[$value['house_id']],
                    'stat_date' => 1,
                    'end_date' => 0,
                    'phase_situation' => '',
                    'content' => $CorpusConstellation['content']];
            }
        }
        return $corpus;
    }

}
