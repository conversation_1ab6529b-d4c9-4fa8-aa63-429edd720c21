<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

use astrology\SweTest as SweTest;

/**
 * 单盘数据生成
 */
class Natal extends LogicBase
{

    /**
     * 获取站点导航搜索条件
     */
    public function plateData($param)
    {
        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        if (!empty($param['phase'])) {
            foreach ($param['phase'] as $key => $value) {
                $allow_degree[$key] = $value;
            }
        }

        $planet_degree = array();
        !empty($param['planet_degree']) && $planet_degree = $param['planet_degree'];

        if (empty($allow_degree)) {
            $allow_degree['0'] = 5;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['180'] = 5;
        }

        $starsCode = $param['planets'];

        $birthdayToTime = strtotime($param['birthday']) - $param['tz'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $planets = implode('', $starsCode);

        $arr = [
            'b' => $utdatenow,
            'p' => $planets,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];
        $exSweTest = get_sington_object('exSweTest', SweTest::class);

        if (!empty($param['virtual']) and !empty($param['virtual'][0])) {
            $starsCode['virtual'] = $param['virtual'];
        }

        if (!empty($param['planet_xs']) and !empty($param['planet_xs'][0])) {
            $starsCode['planet_xs'] = $param['planet_xs'];
        }
        if (!empty($param['planet_xf']) and !empty($param['planet_xf'][0])) {
            $starsCode['planet_xf'] = $param['planet_xf'];
        }
        if (!empty($param['planet_hel']) and !empty($param['planet_hel'][0])) {
            $starsCode['planet_hel'] = $param['planet_hel'];
        }

        $ay = false;
        if (isset($param['ay'])) {
            $ay = $param['ay'];
        }
        $data = $exSweTest->calculate($arr, $starsCode, [], $ay);


        $planets_data['user'] = $param;
        $planets_data['house'] = $this->housePlanet($exSweTest, $data);
        $sign_attribute = $this->signPlanet($data);
        $planets_data['sign'] = $sign_attribute['sign'];

        $data['is_corpus'] = 1;
        if (empty($param['is_corpus'])) {
            $data['is_corpus'] = 0;
        }

        $planets_data['planet'] = $this->planetPhase($exSweTest, $data, $allow_degree, $planet_degree);

        if (!empty($param['tomorrow_type']) and $param['tomorrow_type'] == 1) {

            if(!empty($param['tomorrow_number'])){
                $data['tomorrow_number']=$param['tomorrow_number'];
            }

            $this->tomorrowValue($planets_data['planet'], $data, $exSweTest,$param);
        }

        if (!empty($data['is_corpus']) and !empty($data['corpus_where'])) {
            $corpus_array_where = $data['corpus_where'];
            $corpus_list = array();
            foreach ($corpus_array_where as $keys => $values) {
                $corpusConstellationWhere['chartType'] = 1;
                $corpusConstellationWhere['type'] = $values['type'];
                $corpusConstellationWhere['oneself'] = $values['oneself'];
                $corpusConstellationWhere['other'] = $values['other'];
                $corpusConstellationWhere['degree'] = $values['keyAd'];
                $corpus_info = $this->logicCorpusConstellation->getCorpusConstellationInfo($corpusConstellationWhere, 'oneself,other,type,chartType,degree,keywords,content', '', false);

                if (!empty($corpus_info)) {
                    $corpus_list[] = $corpus_info;
                }

            }

            $planets_data['corpus_list'] = $corpus_list;
        }
        if (!isset($param['format'])) {
            $param['format'] = 1;
        }

        if (!empty($param['svg_type']) and $param['svg_type'] == 1) {
            $planets_data['svg'] = $this->simpleSvg($planets_data, $data, $param['format']);
        } else if (!empty($param['svg_type']) and $param['svg_type'] == -1) {

        } else {
            $planets_data['svg'] = $this->seniorSvg($planets_data, $data);
        }
        $planets_data['attribute'] = $sign_attribute['attribute'];

        return $planets_data;
    }

    /**
     * 计算宫内获得行星
     */
    public function housePlanet($exSweTest, $starsDtat = [])
    {
        $house = $starsDtat['house'];
        $planet = $starsDtat['planet'];
        $planet = $starsDtat['planet'];

        foreach ($house as $keyh => &$valueh) {
            $valueh['house_life'] = $starsDtat['house_life'][$keyh];

            $sign_guardian_index = $starsDtat['sign_guardian_index'][$valueh['sign']['sign_id']];

            foreach ($sign_guardian_index as $key => $vole) {
                $main_planet['code_name'] = (string)array_search($vole, $starsDtat['planetEnglish']);
                $main_planet['planet_english'] = $vole;
                $main_planet['planet_chinese'] = $starsDtat['planetChinese'][$main_planet['code_name']];
                $main_planet['planet_font'] = $starsDtat['planetFont'][$vole];
                $valueh['main_planet'][] = $main_planet;
            }

            if (empty($valueh['planet_array'])) {
                $valueh['planet_array'] = array();
            }
            foreach ($planet as $keyp => $valuep) {
                $house_cha = abs($valueh['longitude'] - $valuep['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }

                if ($keyh < 11) {

                    $last_house = $house[$keyh + 1];
                } else {

                    $last_house = $house[0];
                }

                if ($valueh['longitude'] > $last_house['longitude']) {

                    if ($valuep['longitude'] < $last_house['longitude']) {
                        $valuep['longitude'] += 360;
                    }

                    $last_house['longitude'] += 360;
                }

                if ($valuep['longitude'] >= $valueh['longitude'] and $valuep['longitude'] < $last_house['longitude']) {

                    $planet_array_angle = $exSweTest->Convert_deg_min($house_cha);
                    $planet_array_angle['longitude'] = $house_cha;
                    $planet_array_angle['planet_number'] = $keyp;
                    $planet_array_angle['code_name'] = $valuep['code_name'];
                    $planet_array_angle['planet_english'] = $valuep['planet_english'];
                    $planet_array_angle['planet_chinese'] = $valuep['planet_chinese'];
                    $planet_array_angle['planet_font'] = $starsDtat['planetFont'][$valuep['planet_english']];
                    $valueh['planet_array'][] = $planet_array_angle;
                    unset($planet[$keyp]);
                }
            }
        }
        return $house;
    }


    /**
     * 计算星座内获得行星
     */
    public function signPlanet($starsDtat = [])
    {
        $house = $starsDtat['house'];
        $planet = $starsDtat['planet'];
        $sign_phase = $starsDtat['sign_phase'];
        $planet_ascendant = $starsDtat['planet_ascendant']['longitude'];
        $signPlanet = array();

        //Standard fixed changes


        for ($i = 0; $i < 12; $i++) {

            $signPlanet[$i]['sign_id'] = $i;
            $signPlanet[$i]['sign_english'] = $starsDtat['signEnglish'][$i];
            $signPlanet[$i]['sign_chinese'] = $starsDtat['signChinese'][$i];
            $signPlanet[$i]['sign_font'] = $starsDtat['signFont'][$i];

            $signPlanet[$i]['sign_attribute'] = $sign_phase[$i];
            $sign_guardian_index = $starsDtat['sign_guardian_index'][$i];

            foreach ($sign_guardian_index as $key => $vole) {
                $planet_array_angle['code_name'] = (string)array_search($vole, $starsDtat['planetEnglish']);
                $planet_array_angle['planet_number'] = (string)array_search($vole, $starsDtat['planetEnglish']);
                $planet_array_angle['planet_english'] = $vole;
                $planet_array_angle['planet_chinese'] = $starsDtat['planetChinese'][$planet_array_angle['code_name']];
                $planet_array_angle['planet_font'] = $starsDtat['planetFont'][$vole];
                $signPlanet[$i]['sign_guardian'][] = $planet_array_angle;
            }
            if (empty($value['planet_array'])) {
                $signPlanet[$i]['planet_array'] = array();
            }

        }

        $attribute_chinese = ['变动' => 'change', '固定' => 'fixed', '本位' => 'standard', '土相' => 'soil', '水相' => 'water', '火相' => 'fire', '风相' => 'wind'];

        foreach ($planet as $keyp => $valuep) {
            $sign_planet = $valuep;
            $sign_planet['planet_font'] = $starsDtat['planetFont'][$valuep['planet_english']];
            $sign_planet['deg'] = $valuep['sign']['deg'];
            $sign_planet['min'] = $valuep['sign']['min'];
            $sign_planet['sec'] = $valuep['sign']['sec'];
            $sign_planet['sign_id'] = $valuep['sign']['sign_id'];
            $sign_planet['sign_english'] = $valuep['sign']['sign_english'];
            $sign_planet['sign_chinese'] = $valuep['sign']['sign_chinese'];
            $sign_planet['sign_font'] = $valuep['sign']['sign_font'];
            unset($sign_planet['sign']);
            $signPlanet[$valuep['sign']['sign_id']]['planet_array'][] = $sign_planet;

            $attribute[$attribute_chinese[$sign_phase[$valuep['sign']['sign_id']][0]]][] = $sign_planet;
            $attribute[$attribute_chinese[$sign_phase[$valuep['sign']['sign_id']][1]]][] = $sign_planet;
        }

        return ['sign' => $signPlanet, 'attribute' => $attribute];
    }

    /**
     * 计算星座内获得相位
     */
    public function planetPhase($exSweTest, &$starsDtat = [], $allow_degree = [], $planet_degree = [])
    {
        $planetNameObject = get_sington_object('planetName', "astrology\\planetName");

        $houseChinese = $planetNameObject::$houseChinese;

        if (empty($planet_degree)) {
            $planet_degree['0'] = 1;
            $planet_degree['1'] = 1;
        }

        $corpus_where = array();
        $house = $starsDtat['house'];
        $planet = $starsDtat['planet'];
        $planet_twe = $planet;
        $sign_guardian = $starsDtat['sign_guardian_index'];

        foreach ($planet_twe as $key => &$value) {
            $value['planet_font'] = $starsDtat['planetFont'][$value['planet_english']];
            if (!empty($starsDtat['is_corpus'])) {
                $corpusConstellationWhere['chartType'] = 1;
                $corpusConstellationWhere['type'] = 1;
                $corpusConstellationWhere['oneself'] = $value['planet_chinese'];
                $corpusConstellationWhere['other'] = '';
                $corpusConstellationWhere['keyAd'] = -1;
                $corpus_where[] = $corpusConstellationWhere;
            }
            foreach ($house as $keyh => $valueh) {

                $house_cha = abs($valueh['longitude'] - $value['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }

                if ($keyh < 11) {

                    $last_house = $house[$keyh + 1];
                } else {

                    $last_house = $house[0];
                }
                $first_house = $value;

                if ($valueh['longitude'] > $last_house['longitude']) {

                    if ($first_house['longitude'] < $last_house['longitude']) {
                        $first_house['longitude'] += 360;
                    }

                    $last_house['longitude'] += 360;
                }


                if (round($first_house['longitude'],7) >= $valueh['longitude'] and round($first_house['longitude'],7) < $last_house['longitude']) {


                    $house_array_angle = $exSweTest->Convert_deg_min($house_cha);
                    $value['house_id'] = $keyh + 1;
                    $value['house_longitude'] = $house_cha;
                    $value['house_deg'] = $house_array_angle['deg'];
                    $value['house_min'] = $house_array_angle['min'];
                    $value['house_sec'] = $house_array_angle['sec'];

                    if (!empty($starsDtat['is_corpus'])) {
                        $corpusConstellationWhere['chartType'] = 1;
                        $corpusConstellationWhere['type'] = 4;
                        $corpusConstellationWhere['oneself'] = $value['planet_chinese'];
                        $corpusConstellationWhere['other'] = $houseChinese[$value['house_id'] - 1];
                        $corpusConstellationWhere['keyAd'] = -1;
                        $corpus_where[] = $corpusConstellationWhere;
                    }
                }
            }
            //星座语料
            if (!empty($starsDtat['is_corpus'])) {
                $corpusConstellationWhere['chartType'] = 1;
                $corpusConstellationWhere['type'] = 5;
                $corpusConstellationWhere['oneself'] = $value['planet_chinese'];
                $corpusConstellationWhere['other'] = $value['sign']['sign_chinese'];
                $corpusConstellationWhere['keyAd'] = -1;
                $corpus_where[] = $corpusConstellationWhere;
            }
            if (empty($value['planet_allow_degree'])) {
                $value['planet_allow_degree'] = array();
            }

            foreach ($planet as $keyg => $valueg) {

                if ($key == $keyg) {
                    continue;
                }
                $chazhi = abs($value['longitude'] - $valueg['longitude']);
                $chazhi_xian = $chazhi;
                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                $planet_degree_lgit = 0;

                !empty($planet_degree[$value['code_name']]) && $planet_degree_lgit += $planet_degree[$value['code_name']];

                !empty($planet_degree[$valueg['code_name']]) && $planet_degree_lgit += $planet_degree[$valueg['code_name']];

                $in_out_xu_long = abs($value['longitude'] - $valueg['longitude']);


                //如果第二星大于第一星值，相减绝对值小于180 那么就是第一追第二
                if ($value['longitude'] < $valueg['longitude']) {
                    if ($in_out_xu_long < 180) {
                        $a_out_info = $value;
                        $b_out_info = $valueg;
                    } else {
                        $a_out_info = $valueg;
                        $b_out_info = $value;
                    }
                } else {
                    if ($in_out_xu_long < 180) {
                        $a_out_info = $valueg;
                        $b_out_info = $value;
                    } else {
                        $a_out_info = $value;
                        $b_out_info = $valueg;
                    }
                }


                foreach ($allow_degree as $keyAd => $valueAd) {

                    $valueAd += $planet_degree_lgit;

                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {
                        $in_out = '-1';
                        $in_out_start = $a_out_info['longitude'] + $keyAd - $valueAd;

                        if ($in_out_start > 360) {
                            $in_out_start = $in_out_start - 360;
                        } elseif ($in_out_start < 0) {
                            $in_out_start = $in_out_start + 360;
                        }
                        $in_out_middle = $in_out_start + $valueAd;
                        if ($in_out_middle > 360) {
                            $in_out_middle = $in_out_middle - 360;
                        } elseif ($in_out_middle < 0) {
                            $in_out_middle = $in_out_middle + 360;
                        }
                        $in_out_end = $in_out_middle + $valueAd;
                        if ($in_out_end > 360) {
                            $in_out_end = $in_out_end - 360;
                        } elseif ($in_out_end < 0) {
                            $in_out_end = $in_out_end + 360;
                        }

                        if ($b_out_info['longitude'] < $in_out_middle) {
                            $location_zuo_you = 'nei';
                            if ($in_out_end < $in_out_middle and $b_out_info['longitude'] < $in_out_end) {
                                $location_zuo_you = 'wai';
                            }
                        } else {
                            $location_zuo_you = 'wai';
                            if ($in_out_start > $in_out_middle and $b_out_info['longitude'] > $in_out_end) {
                                $location_zuo_you = 'nei';
                            }
                        }

                        if ($b_out_info['speed'] > $a_out_info['speed']) {
                            if ($location_zuo_you == 'nei') {
                                $in_out = '1';
                            }
                        } else {
                            if ($location_zuo_you == 'wai') {
                                $in_out = '1';
                            }
                        }


                        $planet_allow_degree = [
                            'planet_number' => $keyg,
                            'code_name' => $valueg['code_name'],
                            'planet_english' => $valueg['planet_english'],
                            'planet_chinese' => $valueg['planet_chinese'],
                            'planet_font' => $starsDtat['planetFont'][$valueg['planet_english']],
                            'current_longitude' => $valueg['longitude'],
                            'allow' => $keyAd,
                            'in_out' => $in_out
                        ];
                        if (!empty($starsDtat['is_corpus'])) {
                            $corpusConstellationWhere['chartType'] = 1;
                            $corpusConstellationWhere['type'] = 6;
                            $corpusConstellationWhere['oneself'] = $value['planet_chinese'];
                            $corpusConstellationWhere['other'] = $valueg['planet_chinese'];
                            $corpusConstellationWhere['keyAd'] = $keyAd;
                            $corpus_where[] = $corpusConstellationWhere;
                        }
                        $planet_allow_degree = array_merge($planet_allow_degree, $exSweTest->Convert_sign_deg_min(abs(round(($chazhi - $keyAd), 4))));
                        $value['planet_allow_degree'][] = $planet_allow_degree;

                    }
                }
            }
            $value['longitude'] = $exSweTest->crunch($value['longitude']);
        }

        $starsDtat['corpus_where'] = $corpus_where;

        return $planet_twe;
    }

    /**
     * 后台尊贵计算分
     */
    public function tomorrowValue(&$planets_data = [], $starsDtat = [], $exSweTest,$param)
    {



        $house_list = $starsDtat['house'];
        $planetPhase = $planets_data;
        $code_array = ['0', '1', '2', '3', '4', '5', '6'];
        $code_speed_array = ['0' => 0.985556, '1' => 13.176389, '2' => 1.217222, '3' => 1.040556, '4' => 0.566111, '5' => 0.133056, '6' => 0.069444];
        $acceptance_type_array = ['5' => '主宰', '4' => '擢升', '3' => '三分主', '2' => '界主', '1' => '面主'];


        $birthdayToTime = strtotime($param['birthday']) - $param['tz'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $yue_conditions = ['b' => $utdatenow, 'ut' => $utnow, 'p' => 1, 'd' => 0, 'n' => 1, 's' => 1];
        $yue_conditions['f'] = 'pTlsj';
        $yue_conditions['g'] = ',';
        $yue_conditions['e'] = 'swe';
        $yue_conditions['head'] = '';
        $yue_conditions['roundsec'] = '';
        $yue_guang = $exSweTest->SweTest($yue_conditions);
        $lineInfo = explode(',', $yue_guang[0]);
        $yue_zeng_jian = trim($lineInfo[2], ' ');

        if ($house_list[0]['longitude'] > $house_list[6]['longitude']) {
            if ($planetPhase[0]['longitude'] <= $house_list[0]['longitude'] And $planetPhase[0]['longitude'] > $house_list[6]['longitude']) {
                $day_chart = true;
            } else {
                $day_chart = false;
            }
        } else {
            if ($planetPhase[0]['longitude'] > $house_list[0]['longitude'] And $planetPhase[0]['longitude'] <= $house_list[6]['longitude']) {
                $day_chart = false;
            } else {
                $day_chart = true;
            }
        }

        foreach ($planetPhase as $key => &$value) {
            if (in_array($value['code_name'], $code_array)) {
                $value['noble_score'] = 0;
                //落宫
                if ($value['house_id'] == 10 or $value['house_id'] == 1) {
                    $value['noble_score'] += 5;
                    $value['noble_list'][] = ['type' => 1, 'str' => '落在第' . $value['house_id'] . '宫'];
                }
                if ($value['house_id'] == 7 or $value['house_id'] == 4 or $value['house_id'] == 11) {
                    $value['noble_score'] += 4;
                    $value['noble_list'][] = ['type' => 1, 'str' => '落在第' . $value['house_id'] . '宫'];
                }
                if ($value['house_id'] == 2 or $value['house_id'] == 5) {
                    $value['noble_score'] += 3;
                    $value['noble_list'][] = ['type' => 1, 'str' => '落在第' . $value['house_id'] . '宫'];
                }
                if ($value['house_id'] == 9) {
                    $value['noble_score'] += 2;
                    $value['noble_list'][] = ['type' => 1, 'str' => '落在第' . $value['house_id'] . '宫'];
                }
                if ($value['house_id'] == 3) {
                    $value['noble_score'] += 1;
                    $value['noble_list'][] = ['type' => 1, 'str' => '落在第' . $value['house_id'] . '宫'];
                }
                if ($value['house_id'] == 12) {
                    $value['noble_score'] -= 5;
                    $value['noble_list'][] = ['type' => 1, 'str' => '落在第' . $value['house_id'] . '宫'];
                }
                if ($value['house_id'] == 8 or $value['house_id'] == 6) {
                    $value['noble_score'] -= 2;
                    $value['noble_list'][] = ['type' => 1, 'str' => '落在第' . $value['house_id'] . '宫'];
                }
                //互溶    自己落星座守护星==自己的守护星座里有这行星;
                $sign_guardian_index = ['4', '3', '2', '1', '0', '2', '3', '4', '5', '6', '6', '5'];
                //当前星座的守护星
                $hu_sign_planet = $sign_guardian_index[$value['sign']['sign_id']];
                foreach ($sign_guardian_index as $keyhh => $valuehh) {
                    if ($valuehh == $value['code_name']) {
                        foreach ($planetPhase as $keyhp => $valuehp) {
                            if ($hu_sign_planet == $valuehp['code_name'] and $valuehp['sign']['sign_id'] == $keyhh and $value['code_name'] != $hu_sign_planet) {
                                $value['into_list'][] = ['code_name' => $hu_sign_planet, 'planet_chinese' => $valuehp['planet_chinese']];
                            }
                        }
                    }
                }
                //接纳
                $plan_list_jie = $this->calculateT($value['sign']['sign_id'], $value['sign']['deg'], $day_chart);
                foreach ($plan_list_jie as $keysf => $valuesf) {
                    $value['acceptance_list'][] = ['planet_code' => $valuesf['p'], 'type' => $valuesf['t'], 'str' => $starsDtat['planetChinese'][$valuesf['p']] . $acceptance_type_array[$valuesf['t']]];
                }

                //得时计算
                if($day_chart and $value['house_id'] > 6 and in_array($value['sign']['sign_id'], [0, 2, 4, 6, 8, 10])){
                    if(in_array($value['code_name'], ["0", "5","6"])){
                        $value['get_when'] = '得时';
                    }
                    if(in_array($value['code_name'], ["1", "3","4"])){
                        $value['get_when'] = '失时';
                    }
                }

                if(!$day_chart and $value['house_id'] > 6 and in_array($value['sign']['sign_id'], [1, 3, 5, 7, 9, 11])){
                    if(in_array($value['code_name'], ["0", "5","6"])){
                        $value['get_when'] = '失时';
                    }
                    if(in_array($value['code_name'], ["1", "3","4"])){
                        $value['get_when'] = '得时';
                    }
                }


                //顺逆
                if (!in_array($value['code_name'], ['0', '1'])) {
                    if ($value['speed'] > 0) {
                        $value['noble_score'] += 4;
                        $value['noble_list'][] = ['type' => 2, 'str' => '顺行'];
                    } else {
                        $value['noble_score'] -= 2;
                        $value['noble_list'][] = ['type' => 2, 'str' => '逆行'];
                    }
                }
                //平均速度
                if ($value['speed'] > $code_speed_array[$value['code_name']]) {
                    $value['noble_score'] += 2;
                    $value['noble_list'][] = ['type' => 3, 'str' => '当日速度高于平均速度'];
                } else {
                    $value['noble_score'] -= 2;
                    $value['noble_list'][] = ['type' => 3, 'str' => '当日速度低于平均速度'];
                }
                //月光
                if ($yue_zeng_jian > 0) {
                    $value['noble_score'] += 4;
                    $value['noble_list'][] = ['type' => 4, 'str' => '月亮增光'];
                } else {
                    $value['noble_score'] -= 2;
                    $value['noble_list'][] = ['type' => 4, 'str' => '月亮减光'];
                }
                //日核内0-0.283  -  燃烧0.283-8.5  - 太阳光束下8.5-17    水星 12.3475178  太阳11.8180168
                if ($value['code_name'] != 0) {
                    $yang_cha = abs($planetPhase[0]['longitude'] - $value['longitude']);
                    if ($yang_cha >= 0 and $yang_cha < 0.283) {
                        $value['noble_score'] += 5;
                        $value['noble_list'][] = ['type' => 5, 'str' => '在日核内'];
                    } else if ($yang_cha >= 0.283 and $yang_cha < 8.5) {
                        $value['noble_score'] -= 5;
                        $value['noble_list'][] = ['type' => 5, 'str' => '燃烧'];
                    } else if ($yang_cha >= 8.5 and $yang_cha < 17) {
                        $value['noble_score'] -= 4;
                        $value['noble_list'][] = ['type' => 5, 'str' => '太阳光束下'];
                    } else {
                        $value['noble_score'] += 5;
                        $value['noble_list'][] = ['type' => 5, 'str' => '不燃烧不日核不太阳光束'];
                    }

                    //东西方计算 太阳大于5宫 且 行星在后面跟着就是东出
                    $dong_sheng = false;
                    if ($planetPhase[0]['house_id'] > 6) {
                        if ($planetPhase[0]['longitude'] > 180) {
                            $xu_yang_longitude = $planetPhase[0]['longitude'] - 180;
                            if ($value['longitude'] > $xu_yang_longitude and $value['longitude'] < $planetPhase[0]['longitude']) {
                                $dong_sheng = true;
                            } else {
                                $dong_sheng = false;
                            }
                        } else {
                            $xu_yang_longitude = $planetPhase[0]['longitude'] + 180;
                            if (($value['longitude'] > $xu_yang_longitude and 360 > $value['longitude']) or $value['longitude'] < $planetPhase[0]['longitude']) {
                                $dong_sheng = true;
                            } else {
                                $dong_sheng = false;
                            }
                        }
                    }
                    if (in_array($value['code_name'], ['4', '5', '6'])) {
                        if ($dong_sheng == true) {
                            $value['noble_score'] += 2;
                            $value['noble_list'][] = ['type' => 6, 'str' => $value['planet_chinese'] . '东方'];
                        } else {
                            $value['noble_score'] -= 2;
                            $value['noble_list'][] = ['type' => 6, 'str' => $value['planet_chinese'] . '西方'];
                        }
                    }
                    if (in_array($value['code_name'], ['3', '2'])) {
                        if ($dong_sheng == true) {
                            $value['noble_score'] += 2;
                            $value['noble_list'][] = ['type' => 6, 'str' => $value['planet_chinese'] . '东方'];
                        } else {
                            $value['noble_score'] -= 2;
                            $value['noble_list'][] = ['type' => 6, 'str' => $value['planet_chinese'] . '西方'];
                        }
                    }
                    //相位相关
                    $planet_allow_degree = $value['planet_allow_degree'];
                    foreach ($planet_allow_degree as $keyd => $valued) {
                        if ($valued['allow'] == 0) {
                            if (in_array($valued['code_name'], ['3', '5'])) {
                                $value['noble_score'] += 5;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同0度数'];
                            }
                            if (in_array($valued['code_name'], ['1', 'm'])) {
                                $value['noble_score'] += 4;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同0度数'];
                            }
                            if ($valued['code_name'] == 'Regulus') {
                                $value['noble_score'] += 6;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同0度数'];
                            }
                            if ($valued['code_name'] == 'Spica') {
                                $value['noble_score'] += 5;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同0度数'];
                            }
                            if ($valued['code_name'] == '21') {
                                $value['noble_score'] -= 4;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同0度数'];
                            }
                        }
                        if ($valued['allow'] == 60) {
                            if (in_array($valued['code_name'], ['3', '5'])) {
                                $value['noble_score'] += 3;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同60度数'];
                            }
                        }
                        if ($valued['allow'] == 90) {
                            if (in_array($valued['code_name'], ['4', '6'])) {
                                $value['noble_score'] -= 3;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同90度数'];
                            }
                        }
                        if ($valued['allow'] == 120) {
                            if (in_array($valued['code_name'], ['3', '5'])) {
                                $value['noble_score'] += 4;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同120度数'];
                            }
                        }
                        if ($valued['allow'] == 180) {
                            if (in_array($valued['code_name'], ['4', '6'])) {
                                $value['noble_score'] -= 4;
                                $value['noble_list'][] = ['type' => 7, 'str' => $valued['planet_chinese'] . '同180度数'];
                            }
                        }
                    }

                }
            }
            $value['congenital_score'] = $this->calculateNew($value['sign']['sign_id'], $value['code_name'], $value['sign']['deg'], $day_chart);
        }
        $planets_data = $planetPhase;
    }

    //先天位置计算
    public function calculateT($constellation_id, $degree, $day_chart)
    {
        //数组模式方便入库。
        $plan_list = array();

        $assoc[0] = [5 => 5, 4 => 1, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 12], 3 => [12, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [5, 1, 4], -5 => 4, -4 => 7];

        $assoc[1] = [5 => 4, 4 => 2, 3 => [4, 2, 5], 2 => [4 => [0, 8], 3 => [8, 14], 6 => [14, 22], 7 => [22, 27], 5 => [27, 30]], 1 => [3, 2, 7], -5 => 5, -4 => false];

        $assoc[2] = [5 => 3, 4 => false, 3 => [7, 3, 6], 2 => [3 => [0, 6], 6 => [7, 12], 4 => [12, 17], 5 => [17, 24], 7 => [24, 30]], 1 => [6, 5, 1], -5 => 6, -4 => false];

        $assoc[3] = [5 => 2, 4 => 6, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [6, 13], 3 => [13, 19], 6 => [19, 26], 7 => [26, 30]], 1 => [4, 3, 2], -5 => 7, -4 => 5];

        $assoc[4] = [5 => 1, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 11], 7 => [11, 18], 3 => [18, 24], 5 => [24, 30]], 1 => [7, 6, 5], -5 => 7, -4 => false];

        $assoc[5] = [5 => 3, 4 => 3, 3 => [4, 2, 5], 2 => [3 => [0, 7], 4 => [7, 17], 6 => [17, 21], 5 => [21, 28], 7 => [28, 30]], 1 => [1, 4, 3], -5 => 6, -4 => 4];

        $assoc[6] = [5 => 4, 4 => 7, 3 => [7, 3, 6], 2 => [7 => [0, 6], 3 => [6, 14], 6 => [14, 21], 4 => [21, 28], 5 => [28, 30]], 1 => [2, 7, 6], -5 => 5, -4 => 1];

        $assoc[7] = [5 => 5, 4 => false, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [6, 11], 3 => [11, 19], 6 => [19, 24], 7 => [24, 30]], 1 => [5, 1, 4], -5 => 4, -4 => 2];

        $assoc[8] = [5 => 6, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 12], 4 => [8, 17], 3 => [17, 21], 7 => [21, 26], 5 => [26, 30]], 1 => [3, 2, 7], -5 => 3, -4 => false];

        $assoc[9] = [5 => 7, 4 => 5, 3 => [4, 2, 5], 2 => [3 => [0, 7], 6 => [6, 14], 4 => [14, 22], 7 => [22, 26], 5 => [26, 30]], 1 => [6, 5, 1], -5 => 2, -4 => 6];

        $assoc[10] = [5 => 7, 4 => false, 3 => [7, 3, 6], 2 => [3 => [0, 7], 4 => [6, 13], 5 => [13, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [4, 3, 2], -5 => 1, -4 => false];

        $assoc[11] = [5 => 6, 4 => 4, 3 => [4, 5, 2], 2 => [4 => [0, 12], 6 => [8, 16], 3 => [16, 19], 5 => [19, 28], 7 => [28, 30]], 1 => [7, 6, 5], -5 => 3, -4 => 3];

        //计算5分
        $plan_list[] = ['t' => 5, 'p' => $assoc[$constellation_id][5] - 1];
        //计算4分
        if ($assoc[$constellation_id][4] != false) {
            $plan_list[] = ['t' => 4, 'p' => $assoc[$constellation_id][4] - 1];
        }
        //计算3分
        if ($day_chart) {
            $plan_list[] = ['t' => 3, 'p' => $assoc[$constellation_id][3][0] - 1];
        } else {
            $plan_list[] = ['t' => 3, 'p' => $assoc[$constellation_id][3][1] - 1];
        }
        //计算2分
        $two_plan = $assoc[$constellation_id][2];
        foreach ($two_plan as $kyes => $vdsa) {
            if ($vdsa[0] <= $degree and $vdsa[1] > $degree) {
                $plan_list[] = ['t' => 2, 'p' => $kyes - 1];
            }
        }
        //计算1分
        $one_plan = $assoc[$constellation_id][1];
        if (10 > $degree) {
            $plan_list[] = ['t' => 1, 'p' => $one_plan[0] - 1];
        } else if (20 > $degree and 10 <= $degree) {
            $plan_list[] = ['t' => 1, 'p' => $one_plan[1] - 1];
        } else if (20 <= $degree) {
            $plan_list[] = ['t' => 1, 'p' => $one_plan[2] - 1];
        }
        return $plan_list;
    }

    //先天分数计算
    public function calculateNew($constellation_name, $plan_name, $degree, $day_chart)
    {

        $score = 0; //分值
        $constellation['1'] = '0';
        $constellation['2'] = '1';
        $constellation['3'] = '2';
        $constellation['4'] = '3';
        $constellation['5'] = '4';
        $constellation['6'] = '5';
        $constellation['7'] = '6';
        $constellation['8'] = '7';
        $constellation['9'] = '8';
        $constellation['10'] = '9';
        $constellation['11'] = '10';
        $constellation['12'] = '11';

        $planet['1'] = '0';
        $planet['2'] = '1';
        $planet['3'] = '2';
        $planet['4'] = '3';
        $planet['5'] = '4';
        $planet['6'] = '5';
        $planet['7'] = '6';

        //数组模式方便入库。

        $assoc[1] = [5 => 5, 4 => 1, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 12], 3 => [12, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [5, 1, 4], -5 => 4, -4 => 7];

        $assoc[2] = [5 => 4, 4 => 2, 3 => [4, 2, 5], 2 => [4 => [0, 8], 3 => [8, 14], 6 => [14, 22], 7 => [22, 27], 5 => [27, 30]], 1 => [3, 2, 7], -5 => 5, -4 => false];

        $assoc[3] = [5 => 3, 4 => false, 3 => [7, 3, 6], 2 => [3 => [0, 6], 6 => [7, 12], 4 => [12, 17], 5 => [17, 24], 7 => [24, 30]], 1 => [6, 5, 1], -5 => 6, -4 => false];

        $assoc[4] = [5 => 2, 4 => 6, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [6, 13], 3 => [13, 19], 6 => [19, 26], 7 => [26, 30]], 1 => [4, 3, 2], -5 => 7, -4 => 5];

        $assoc[5] = [5 => 1, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 11], 7 => [11, 18], 3 => [18, 24], 5 => [24, 30]], 1 => [7, 6, 5], -5 => 7, -4 => false];

        $assoc[6] = [5 => 3, 4 => 3, 3 => [4, 2, 5], 2 => [3 => [0, 7], 4 => [7, 17], 6 => [17, 21], 5 => [21, 28], 7 => [28, 30]], 1 => [1, 4, 3], -5 => 6, -4 => 4];

        $assoc[7] = [5 => 4, 4 => 7, 3 => [7, 3, 6], 2 => [7 => [0, 6], 3 => [6, 14], 6 => [14, 21], 4 => [21, 28], 5 => [28, 30]], 1 => [2, 7, 6], -5 => 5, -4 => 1];

        $assoc[8] = [5 => 5, 4 => false, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [6, 11], 3 => [11, 19], 6 => [19, 24], 7 => [24, 30]], 1 => [5, 1, 4], -5 => 4, -4 => 2];

        $assoc[9] = [5 => 6, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 12], 4 => [8, 17], 3 => [17, 21], 7 => [21, 26], 5 => [26, 30]], 1 => [3, 2, 7], -5 => 3, -4 => false];

        $assoc[10] = [5 => 7, 4 => 5, 3 => [4, 2, 5], 2 => [3 => [0, 7], 6 => [6, 14], 4 => [14, 22], 7 => [22, 26], 5 => [26, 30]], 1 => [6, 5, 1], -5 => 2, -4 => 6];

        $assoc[11] = [5 => 7, 4 => false, 3 => [7, 3, 6], 2 => [3 => [0, 7], 4 => [6, 13], 5 => [13, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [4, 3, 2], -5 => 1, -4 => false];

        $assoc[12] = [5 => 6, 4 => 4, 3 => [4, 5, 2], 2 => [4 => [0, 12], 6 => [8, 16], 3 => [16, 19], 5 => [19, 28], 7 => [28, 30]], 1 => [7, 6, 5], -5 => 3, -4 => 3];
        //真正计算在线面，上面是方便入库数组模式

        //判断星座是否存在
        if (!$constellation_id = array_search($constellation_name, $constellation)) {
            return false;
        }
        //判断行星是否存在
        if (!$plan_id = array_search($plan_name, $planet)) {
            $plan_id = -1;
        }
        $score_str = 'P';
        //计算5分
        $plan_5_soc = ['type' => 5, 'planet_code' => $assoc[$constellation_id][5] - 1, 'into' => 0];
        if ($assoc[$constellation_id][5] == $plan_id) {
            $score += 5;
            $plan_5_soc['into'] = 1;
            $score_str = '';
        }
        $plan_list[] = $plan_5_soc;
        //计算4分
        if ($assoc[$constellation_id][4] != false) {
            if ($assoc[$constellation_id][4] == $plan_id) {
                $plan_list[] = ['type' => 4, 'planet_code' => $assoc[$constellation_id][4] - 1, 'into' => 1];
                $score += 4;
                $score_str = '';
            } else {
                $plan_list[] = ['type' => 4, 'planet_code' => $assoc[$constellation_id][4] - 1, 'into' => 0];
            }
        }
        //计算3分
        $planet_type_2_list = array();
        foreach ($assoc[$constellation_id][3] as $kyes => $vdsa) {
            if ($vdsa == $plan_id) {
                $planet_type_2_list[] = ['planet_code' => $vdsa - 1, 'into' => 1];
                $score += 3;
                $score_str = '';
            } else {
                $planet_type_2_list[] = ['planet_code' => $vdsa - 1, 'into' => 0];
            }
        }
        $plan_list[] = ['type' => 3, 'list' => $planet_type_2_list];
        //计算2分
//        if (!empty($assoc[$constellation_id][2][$plan_id])) {
//            $degree_array = $assoc[$constellation_id][2][$plan_id];
//            if ($degree >= $degree_array[0] and $degree < $degree_array[1]) {
//                $score += 2;
//            }
//        }
        $two_plan = $assoc[$constellation_id][2];
        foreach ($two_plan as $kyes => $vdsa) {
            if ($vdsa[0] <= $degree and $vdsa[1] > $degree) {
                if ($plan_id == $kyes) {
                    $plan_list[] = ['type' => 2, 'planet_code' => $kyes - 1, 'into' => 1];
                    $score += 2;
                    $score_str = '';
                } else {
                    $plan_list[] = ['type' => 2, 'planet_code' => $kyes - 1, 'into' => 0];
                }
            }
        }
        //计算1分
//        if (($index_key = array_search($plan_id, $assoc[$constellation_id][1])) > -1) {
//            if ($degree >= $index_key * 10 and $degree < ($index_key + 1) * 10) {
//                $score += 1;
//            }
//        }
        $one_plan = $assoc[$constellation_id][1];
        foreach ($one_plan as $kyes => $vdsa) {
            if ($degree >= $kyes * 10 and $degree < ($kyes + 1) * 10) {
                if ($vdsa == $plan_id) {
                    $plan_list[] = ['type' => 1, 'planet_code' => $vdsa - 1, 'into' => 1];
                    $score += 1;
                    $score_str = '';
                } else {
                    $plan_list[] = ['type' => 1, 'planet_code' => $vdsa - 1, 'into' => 0];
                }
            }
        }
        //计算-5分
        if ($assoc[$constellation_id][-5] == $plan_id) {
            $score -= 5;
            $score_str = '';
            $plan_list[] = ['type' => -5, 'planet_code' => $assoc[$constellation_id][-5] - 1, 'into' => 1];
        } else {
            $plan_list[] = ['type' => -5, 'planet_code' => $assoc[$constellation_id][-5] - 1, 'into' => 0];
        }
        //计算-4分
        if ($assoc[$constellation_id][-4] != false) {
            if ($assoc[$constellation_id][-4] == $plan_id) {
                $score -= 4;
                $score_str = '';
                $plan_list[] = ['type' => -4, 'planet_code' => $assoc[$constellation_id][-4] - 1, 'into' => 1];
            } else {
                $plan_list[] = ['type' => -4, 'planet_code' => $assoc[$constellation_id][-4] - 1, 'into' => 0];
            }
        }
        if ($plan_id == -1) {
            $score = 'N/A';
        } else {
            $score .= $score_str;
        }
        return ['list' => $plan_list, 'score' => $score];
    }

    /**
     * 计算星座内获得行星
     */
    public function simpleSvg(&$planets_data = [], $starsDtat = [], $format = 1)
    {
        $signPlanet = $planets_data['sign'];
        $housePlanet = $planets_data['house'];
        $planetPhase = $planets_data['planet'];
        $planetFont = $starsDtat['planetFont'];
        $signFont = $starsDtat['signFont'];

        $diameter = 600;
        $radius = $diameter / 2;
        $radius_one = $radius - 5;
        $radius_two = $radius - 75;
        $radius_three = $radius - 100;
        $radius_four = $radius - 140;

        $svgHtml = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:math="http://exslt.org/math" version="1.0" viewBox="0 0 ' . $diameter . ' ' . $diameter . '" id="chart">' .
            '<g id="main" transform="">';
        $svgHtml .= '<g id="chartbody"><circle id="zodiac" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_one . '"></circle>' .
            '<circle id="zodiac_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_two . '"></circle>' .
            '<circle id="hcircle" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_three . '"></circle>' .
            '<circle id="hcircle_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_four . '"></circle>';
        $svgHtml .= '<path d="M ' . $radius . ',' . ($radius - 5) . ' L ' . $radius . ',' . ($radius + 5) . '" class="origin"></path>' .
            '<path d="M ' . ($radius - 5) . ' ' . $radius . ' L ' . ($radius + 5) . ' ' . $radius . '" class="origin"></path>';

        $planet_ascendant = $housePlanet[0]['longitude'];

        //星座画图
        foreach ($signPlanet as $keys => $values) {

            $line_x_top = ((-$radius_one) * cos(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;
            $line_y_top = (($radius_one) * sin(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;
            $line_x_bottom = ((-$radius_two) * cos(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;
            $line_y_bottom = (($radius_two) * sin(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;

            $svgHtml .= '<line  class="zodiac_grid" x1="' . $line_x_top . '" y1="' . $line_y_top . '" x2="' . $line_x_bottom . '" y2="' . $line_y_bottom . '"/>';

            //星座字
            $zodiac_x_top = ((-$radius_two - 30) * cos(deg2rad(($keys * 30) + 15 - $planet_ascendant))) + $radius;
            $zodiac_y_top = (($radius_two + 30) * sin(deg2rad(($keys * 30) + 15 - $planet_ascendant))) + $radius;

            $guardian_sign_x = ((-$radius_two - 30) * cos(deg2rad(($keys * 30) + 8 - $planet_ascendant))) + $radius;
            $guardian_sign_y = (($radius_two + 30) * sin(deg2rad(($keys * 30) + 8 - $planet_ascendant))) + $radius;

            $svgHtml .= '<g id="' . $values['sign_english'] . '">';
            if (count($values['sign_guardian']) == 2) {
                $sign_guardian_n = $values['sign_guardian'][$format];
            } else {
                $sign_guardian_n = $values['sign_guardian'][0];
            }

            $svgHtml .= '<text class="text_font sign_font sign_' . $values['sign_english'] . '" x="' . $zodiac_x_top . '" y="' . $zodiac_y_top . '" serial="' . $values['sign_id'] . '">' . $signFont[$values['sign_id']] . '</text>';
            $svgHtml .= '<text class="text_font guardian_font planets_' . $sign_guardian_n['planet_english'] . '" x="' . $guardian_sign_x . '" y="' . $guardian_sign_y . '" serial="' . $sign_guardian_n['planet_english'] . '">' . $planetFont[$sign_guardian_n['planet_english']] . '</text>';
            $svgHtml .= '</g>';
        }
        //宫位画图
        foreach ($housePlanet as $keyh => &$valueh) {
            $angle = -($planet_ascendant - $valueh['longitude']);

            $house_grid_class = 'house_dark_grid';
            if (!($keyh % 3)) {
                $radius_top = $radius_one + 5;
                $house_grid_class = 'house_dark_grid house_dark_grid_attribute';
            } else {
                $radius_top = $radius_two;
            }

            $house_dark_x_top = ((-$radius_top) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_top = (($radius_top) * sin(deg2rad($angle))) + $radius;
            $house_dark_x_bottom = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_bottom = (($radius_four) * sin(deg2rad($angle))) + $radius;

            $svgHtml .= '<line shaft="' . $valueh['house_id'] . '"  class="' . $house_grid_class . '" x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';

            $svgHtml .= '<line shaft="' . $valueh['house_id'] . '"  class="house_grid" x1="' . $radius . '" y1="' . $radius . '" x2="' . $house_dark_x_bottom . '" y2="' . $house_dark_y_bottom . '"/>';

            if ($keyh == 11) {
                $zitijiaodu = abs($housePlanet[0]['longitude'] - $valueh['longitude']);

            } else {
                $zitijiaodu = abs($housePlanet[$keyh + 1]['longitude'] - $valueh['longitude']);
            }

            if ($zitijiaodu > 180) {
                $zitijiaodu = 360 - $zitijiaodu;
            }

            $house_id_x = ((-$radius_three - 13) * cos(deg2rad($angle + $zitijiaodu / 2))) + $radius;
            $house_id_y = (($radius_three + 13) * sin(deg2rad($angle + $zitijiaodu / 2))) + $radius;

            $valueh['z_x'] = $house_id_x;
            $valueh['z_y'] = $house_id_y;

            $svgHtml .= ' <g><text class="text_font house_id house_' . ($keyh + 1) . '" x="' . $house_id_x . '" y="' . $house_id_y . '"  serial="' . $keyh . '">' . ($keyh + 1) . '</text> </g>';
        }

        $planets_data['house'] = $housePlanet;
        //行星画图
        $planet_arrays = $planetPhase;

        array_multisort(array_column($planetPhase, 'longitude'), SORT_ASC, $planet_arrays);

        foreach ($planet_arrays as $keygf => &$volegf) {
            if ($keygf > 0) {
                if ($volegf['longitude'] < ($planet_arrays[$keygf - 1]['longitude'] + 7)) {

                    $volegf['longitude'] = $planet_arrays[$keygf - 1]['longitude'] + 7;
                }
            }
            $planet_array_code_name[$volegf['code_name']] = $volegf['longitude'];
        }

        $planet_count_code = count($planet_arrays);

        if (($planet_arrays[0]['longitude'] + 360) < ($planet_arrays[$planet_count_code - 1]['longitude'] + 7)) {

            $planet_arrays[0]['longitude'] = $planet_arrays[$planet_count_code - 1]['longitude'] + 7;

            ($planet_arrays[0]['longitude'] > 360) && $planet_arrays[0]['longitude'] -= 360;

            foreach ($planet_arrays as $keygft => &$volegft) {
                if ($keygft > 0) {
                    if ($volegft['longitude'] < ($planet_arrays[$keygft - 1]['longitude'] + 7)) {

                        $volegft['longitude'] = $planet_arrays[$keygft - 1]['longitude'] + 7;
                    } else {
                        break;
                    }
                }

                $planet_array_code_name[$volegft['code_name']] = $volegft['longitude'];
            }
        }


        foreach ($planetPhase as $keyp => &$valuep) {

            $angle = -($planet_ascendant - $valuep['longitude']);
            $planet_x_circle = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $planet_y_circle = (($radius_four) * sin(deg2rad($angle))) + $radius;

            $svgHtml .= '<circle class="planets_circle planets_' . $valuep['planet_english'] . '" cx="' . $planet_x_circle . '" cy="' . $planet_y_circle . '" r="1.5"></circle>';

            $planet_allow_degree = $valuep['planet_allow_degree'];
            foreach ($planet_allow_degree as $keya => $valuea) {
                $planet_x_bottom = ((-$radius_four) * cos(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $planet_y_bottom = (($radius_four) * sin(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $svgHtml .= '<line class="planet_sign_line planet_sign_line_' . $valuea['allow'] . '" style="stroke-width:1;stroke-dasharray: 1,' . $valuea['deg'] . ';" x1="' . $planet_x_circle . '" y1="' . $planet_y_circle . '" x2="' . $planet_x_bottom . '" y2="' . $planet_y_bottom . '"/>';
            }

            $text_angle = -($planet_ascendant - $planet_array_code_name[$valuep['code_name']]);
            $planet_x_text = ((-$radius_four - 20) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text = (($radius_four + 20) * sin(deg2rad($text_angle))) + $radius;

            $svgHtml .= '<g id="' . $valuep['planet_english'] . '">';
            $svgHtml .= '<line style="stroke: gray;stroke-linecap: round;stroke-dasharray: 1,2;" x1="' . $planet_x_circle . '" y1="' . $planet_y_circle . '" x2="' . $planet_x_text . '" y2="' . $planet_y_text . '"/>';

            $svgHtml .= '<text class="text_font planet_font planets_' . $valuep['planet_english'] . '" x="' . $planet_x_text . '" y="' . $planet_y_text . '" serial="' . $valuep['planet_english'] . '">' . $planetFont[$valuep['planet_english']] . '</text>';
            $svgHtml .= '</g>';
            $valuep['z_x'] = $planet_x_text;
            $valuep['z_y'] = $planet_y_text;

        }
        $planets_data['planet'] = $planetPhase;
        $svgHtml .= '</g></g></svg>';
        return $svgHtml;
    }

    /**
     * 计算星座内获得行星
     */
    public function seniorSvg($planets_data = [], $starsDtat = [])
    {
        $signPlanet = $planets_data['sign'];
        $housePlanet = $planets_data['house'];
        $planetPhase = $planets_data['planet'];
        $planetFont = $starsDtat['planetFont'];
        $signFont = $starsDtat['signFont'];

        $diameter = 600;
        $radius = $diameter / 2;
        $radius_one = $radius - 5;
        $radius_two = $radius - 40;
        $radius_three = $radius - 190;
        $radius_four = $radius - 220;


        $svgHtml = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:math="http://exslt.org/math" version="1.0" viewBox="0 0 ' . $diameter . ' ' . $diameter . '" id="chart">' .
            '<g id="main" transform="">';
        $svgHtml .= '<g id="chartbody"><circle id="zodiac" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_one . '"></circle>' .
            '<circle id="zodiac_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_two . '" style="fill: white;"></circle>' .
            '<circle id="hcircle" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_three . '" style="fill: #EFF;"></circle>' .
            '<circle id="hcircle_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_four . '"></circle>';
        $svgHtml .= '<path d="M ' . $radius . ',' . ($radius - 5) . ' L ' . $radius . ',' . ($radius + 5) . '" class="origin"></path>' .
            '<path d="M ' . ($radius - 5) . ' ' . $radius . ' L ' . ($radius + 5) . ' ' . $radius . '" class="origin"></path>';

        $planet_ascendant = $housePlanet[0]['longitude'];

        //宫位画图
        foreach ($housePlanet as $keyh => $valueh) {
            $angle = -($planet_ascendant - $valueh['longitude']);

            $house_dark_x_top = ((-$radius_two) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_top = (($radius_two) * sin(deg2rad($angle))) + $radius;
            $house_dark_x_bottom = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_bottom = (($radius_four) * sin(deg2rad($angle))) + $radius;


            if (!($keyh % 3)) {
                $house_beyond_dark_x_top = ((-$radius_one) * cos(deg2rad($angle))) + $radius;
                $house_beyond_dark_y_top = (($radius_one) * sin(deg2rad($angle))) + $radius;
                $radius_top = $radius_one + 5;
                $house_beyond_dark_x_bottom = ((-$radius_top) * cos(deg2rad($angle))) + $radius;
                $house_beyond_dark_y_bottom = (($radius_top) * sin(deg2rad($angle))) + $radius;
                $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="house_dark_grid_attribute" x1="' . $house_beyond_dark_x_top . '" y1="' . $house_beyond_dark_y_top . '" x2="' . $house_beyond_dark_x_bottom . '" y2="' . $house_beyond_dark_y_bottom . '"/>';
                $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="house_dark_grid_attribute"  x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';

            } else {
                $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="house_dark_grid" x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';
            }


            if ($keyh == 11) {
                $zitijiaodu = abs($housePlanet[0]['longitude'] - $valueh['longitude']);

            } else {
                $zitijiaodu = abs($housePlanet[$keyh + 1]['longitude'] - $valueh['longitude']);
            }

            if ($zitijiaodu > 180) {
                $zitijiaodu = 360 - $zitijiaodu;
            }

            $house_id_x = ((-$radius_four - 13) * cos(deg2rad($angle + $zitijiaodu / 2))) + $radius;
            $house_id_y = (($radius_four + 13) * sin(deg2rad($angle + $zitijiaodu / 2))) + $radius;

            $svgHtml .= ' <g><text class="must_symbo_font house_id house_' . ($keyh + 1) . '" x="' . $house_id_x . '" y="' . $house_id_y . '"  serial="' . $keyh . '">' . ($keyh + 1) . '</text> </g>';

            $hou_cha = -5;
            if ($angle > 180 or $angle < 270) {
                $hou_cha = 5;
            }

            $sign_var = $valueh['sign'];
            //星座度数
            $zodiac_signs_deg_x = ((-($radius_one - 15)) * cos(deg2rad($angle - $hou_cha))) + $radius;
            $zodiac_signs_deg_y = ((($radius_one - 15)) * sin(deg2rad($angle - $hou_cha))) + $radius;

            $svgHtml .= '<text class="must_symbo_font senior_planet_deg longitude_' . $sign_var['sign_id'] . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $sign_var['deg'] . '</text>';

            //星座字
            $zodiac_x_top = ((-($radius_one - 15)) * cos(deg2rad($angle))) + $radius;
            $zodiac_y_top = ((($radius_one - 15)) * sin(deg2rad($angle))) + $radius;
            $svgHtml .= '<g id="' . $sign_var['sign_english'] . '">';
            $svgHtml .= '<text class="must_symbo_font sign_font senior_sign_font sign_' . $sign_var['sign_english'] . '" x="' . $zodiac_x_top . '" y="' . $zodiac_y_top . '" serial="' . $sign_var['sign_id'] . '">' . $sign_var['sign_font'] . '</text>';
            $svgHtml .= '</g>';

            //星座秒数
            $zodiac_signs_deg_x = ((-($radius_one - 15)) * cos(deg2rad($angle + $hou_cha))) + $radius;
            $zodiac_signs_deg_y = ((($radius_one - 15)) * sin(deg2rad($angle + $hou_cha))) + $radius;

            $svgHtml .= '<text class="must_symbo_font senior_planet_min longitude_' . $sign_var['sign_id'] . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $sign_var['min'] . '</text>';


        }
//
//        //行星画图
        $planet_arrays = $planetPhase;

        array_multisort(array_column($planetPhase, 'longitude'), SORT_ASC, $planet_arrays);

        foreach ($planet_arrays as $keygf => &$volegf) {
            if ($keygf > 0) {
                if ($volegf['longitude'] < ($planet_arrays[$keygf - 1]['longitude'] + 8)) {

                    $volegf['longitude'] = $planet_arrays[$keygf - 1]['longitude'] + 8;
                }

            }
            $planet_array_code_name[$volegf['code_name']] = $volegf['longitude'];
        }

        $planet_count_code = count($planet_arrays);

        if (($planet_arrays[0]['longitude'] + 360) < ($planet_arrays[$planet_count_code - 1]['longitude'] + 7)) {

            $planet_arrays[0]['longitude'] = $planet_arrays[$planet_count_code - 1]['longitude'] + 7;

            ($planet_arrays[0]['longitude'] > 360) && $planet_arrays[0]['longitude'] -= 360;

            foreach ($planet_arrays as $keygft => &$volegft) {
                if ($keygft > 0) {
                    if ($volegft['longitude'] < ($planet_arrays[$keygft - 1]['longitude'] + 7)) {

                        $volegft['longitude'] = $planet_arrays[$keygft - 1]['longitude'] + 7;
                    } else {
                        break;
                    }
                }
                $planet_array_code_name[$volegft['code_name']] = $volegft['longitude'];
            }
        }


        foreach ($planetPhase as $keyp => $valuep) {

            $angle = -($planet_ascendant - $valuep['longitude']);
            $planet_x_circle = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $planet_y_circle = (($radius_four) * sin(deg2rad($angle))) + $radius;

            $svgHtml .= '<circle class="planets_circle planets_' . $valuep['planet_english'] . '" cx="' . $planet_x_circle . '" cy="' . $planet_y_circle . '" r="1.5"></circle>';


            $planet_allow_degree = $valuep['planet_allow_degree'];
            foreach ($planet_allow_degree as $keya => $valuea) {
                $planet_x_bottom = ((-$radius_four) * cos(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $planet_y_bottom = (($radius_four) * sin(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $svgHtml .= '<line class="planet_sign_line planet_sign_line_' . $valuea['allow'] . '" style="stroke-width:1;stroke-dasharray: 1,' . $valuea['deg'] . ';" x1="' . $planet_x_circle . '" y1="' . $planet_y_circle . '" x2="' . $planet_x_bottom . '" y2="' . $planet_y_bottom . '"/>';
            }
            $text_angle = -($planet_ascendant - $planet_array_code_name[$valuep['code_name']]);

            $planet_x_line_top = ((-$radius_two) * cos(deg2rad($angle))) + $radius;
            $planet_y_line_top = (($radius_two) * sin(deg2rad($angle))) + $radius;

            $planet_x_line_bottom = ((-($radius_two - 15)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_line_bottom = ((($radius_two - 15)) * sin(deg2rad($text_angle))) + $radius;

            $svgHtml .= '<line style="stroke: gray;stroke-linecap: round;stroke-dasharray: 1,2;" x1="' . $planet_x_line_top . '" y1="' . $planet_y_line_top . '" x2="' . $planet_x_line_bottom . '" y2="' . $planet_y_line_bottom . '"/>';

            $planet_x_text = ((-($radius_two - 25)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text = ((($radius_two - 25)) * sin(deg2rad($text_angle))) + $radius;

            $sign_var = $valuep['sign'];

            $svgHtml .= '<g id="' . $valuep['planet_english'] . '">';

            //行星字
            $svgHtml .= '<text class="must_symbo_font planet_font senior_planet_font planets_' . $valuep['planet_english'] . '" x="' . $planet_x_text . '" y="' . $planet_y_text . '" serial="' . $valuep['planet_english'] . '">' . $planetFont[$valuep['planet_english']] . '</text>';

            //行星度
            $planet_x_text_deg = ((-($radius_two - 55)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text_deg = ((($radius_two - 55)) * sin(deg2rad($text_angle))) + $radius;
            $svgHtml .= '<text class="must_symbo_font senior_planet_deg longitude_' . $sign_var['sign_id'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '">' . $sign_var['deg'] . '</text>';

            //行星星座
            $planet_x_text_deg = ((-($radius_two - 85)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text_deg = ((($radius_two - 85)) * sin(deg2rad($text_angle))) + $radius;

            $svgHtml .= '<text class="must_symbo_font sign_font senior_sign_font sign_' . $sign_var['sign_english'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '" serial="' . $sign_var['sign_id'] . '">' . $sign_var['sign_font'] . '</text>';

            //行星分
            $planet_x_text_deg = ((-($radius_two - 115)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text_deg = ((($radius_two - 115)) * sin(deg2rad($text_angle))) + $radius;
            $svgHtml .= '<text class="must_symbo_font senior_planet_min longitude_' . $sign_var['sign_id'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '">' . $sign_var['min'] . '</text>';

            if ($valuep['speed'] < 0) {
                //行星度
                $planet_x_text_deg = ((-($radius_two - 140)) * cos(deg2rad($text_angle))) + $radius;
                $planet_y_text_deg = ((($radius_two - 140)) * sin(deg2rad($text_angle))) + $radius;
                $svgHtml .= '<text class="must_symbo_font senior_house_sign_r longitude_' . $sign_var['sign_id'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '">Ú</text>';
            }


            $svgHtml .= '</g>';


        }
        $svgHtml .= '</g></g></svg>';
        return $svgHtml;
    }


    public function plateSweTestQuery($param)
    {
        $exSweTestEmpty = get_sington_object('exSweTestEmpty', "astrology\\SweTestEmpty");

        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        foreach ($param['birthday'] as $keys => $argvs) {

            $birthdayToTime = strtotime($argvs) - $param['tz'] * 3600;

            $utdatenow = date('d.m.Y', $birthdayToTime);

            $utnow = date('H:i:s', $birthdayToTime);

            $planets = implode('', $param['planets']);

            $arr = [
                'b' => $utdatenow,
                'p' => $planets,
                'ut' => $utnow,
                'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'head',
                'roundsec'
            ];
            $planets_data[] = $exSweTestEmpty->SweTestQuery($arr);
        }

        return $planets_data;
    }


    public function plateTransits($param)
    {
        $exSweTestEmpty = get_sington_object('exSweTestEmpty', "astrology\\SweTestEmpty");

        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        foreach ($param['birthday'] as $keys => $argvs) {

            $birthdayToTime = strtotime($argvs) - $param['tz'] * 3600;

            $utdatenow = date('d.m.Y', $birthdayToTime);

            $utnow = date('H:i:s', $birthdayToTime);

            $planets = implode('', $param['planets']);

            $arr = [
                'b' => $utdatenow,
                'p' => $planets,
                'house' => $house,
                'ut' => $utnow,
                'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'head',
                'roundsec'
            ];
            $planets_data[] = $exSweTestEmpty->SweTestQuery($arr);
        }

        return $planets_data;
    }

    //通用直接命令获取数据
    public function universalData($param)
    {

        if ($param['key'] != 123789) {
            return false;
        }
        unset($param['key']);
        unset($param['access_token']);
        if (!is_array($param['conditions'])) {
            $arr = json_decode(html_entity_decode($param['conditions']), true);
        } else {
            $arr = $param['conditions'];
        }
        $arr['g'] = ',';
        $arr['head'] = '';
        $arr['roundsec'] = '';
        $exSweTest = get_sington_object('exSweTest', SweTest::class);

        return $exSweTest->SweTest($arr);
    }

    //通用直接命令获取数据
    public function transitsProgressed($param)
    {

        unset($param['access_token']);

        if (!is_array($param['conditions'])) {
            $arr = json_decode(html_entity_decode($param['conditions']), true);
        } else {
            $arr = $param['conditions'];
        }

        $arr['f'] = 'pTlsj';
        $arr['g'] = ',';
        $arr['e'] = 'swe';
        $arr['head'] = '';
        $arr['roundsec'] = '';
        $exSweTest = get_sington_object('exSweTest', SweTest::class);

        $full_moon_time = $exSweTest->transitsProgressed($arr, $param['degree'], $param['cycle']);

        return $full_moon_time;
    }
}
