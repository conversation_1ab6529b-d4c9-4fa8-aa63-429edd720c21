<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 登录记录逻辑
 */
class LoginLog extends LogicBase
{

      /**
       * 获取登录记录搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获登录记录单条信息
      */
     public function getLoginLogInfo($where = [], $field = '*')
     {

        return $this->modelLoginLog->getInfo($where, $field);
     }

    /**
     * 获取登录记录列表
     */

    public function getLoginLogList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelLoginLog->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取登录记录无分页列表
     */
    public function getLoginLogColumn($where = [], $field = '', $key = '')
    {
        return $this->modelLoginLog->getColumn($where, $field , $key);
    }

    /**
     * 登录记录新增
     */
    public function loginLogAdd($data = [])
    {
        $start_time = strtotime(date('Y-m-d', time()));
        $end_time = $start_time-86400;

        $infoData = $this->logicWeiUserInfo->getWeiUserInfoInfo(['id' => $data['user_id']], 'id,login_count,login_continuous');
        $Yesterday=$this->modelLoginLog->getColumn(['user_id'=>$data['user_id'],'create_time'=>['gt',$end_time]],'id,user_id,create_time');

        $login_continuous=$infoData['login_continuous'];
        $dataUser['login_continuous']=1;
        $dataUser['id']=$data['user_id'];
        $dataUser['login_count']=$infoData['login_count']+1;
        $YesterdayCount=false;
        $todayCount=false;
        foreach ($Yesterday as $key=>$value){
            if($value['create_time']<$start_time and $value['create_time']>$end_time){
                $YesterdayCount=true;
            }
            if($value['create_time']>$start_time){
                $dataUser['login_count']=$infoData['login_count'];
                $todayCount=true;
                break;
            }
        }

        if($YesterdayCount and !$todayCount){
            $dataUser['login_continuous']=$infoData['login_continuous']+1;
            $login_continuous++;
        }

        if($dataUser['login_count']!=$infoData['login_count']){
            //新增连续登录次数
            $this->logicWeiUserInfo->weiUserWebEdit($dataUser);
            //连续登录查看设置徽章
            $this->logicAttainmentLog->attainmentLogAccess(['type'=>1,'user_id'=>$data['user_id'],'value'=>$dataUser['login_continuous']]);
        }
        $this->modelLoginLog->setInfo(['user_id'=>$data['user_id'],'continuous'=>$login_continuous,'choose_appid'=>$data['choose_appid']]);
        $dataUser['login_continuous']=$login_continuous;
        return $dataUser;
    }

    /**
     * 登录记录单条编辑
     */
    public function loginLogEdit($data = [])
    {
		
        $result = $this->modelLoginLog->setInfo($data);
        
        return $result ? $result : $this->modelLoginLog->getError();
    }

    /**
     * 登录记录删除
     */
    public function loginLogDel($where = [], $is_true = false)
    {

        $result = $this->modelLoginLog->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelLoginLog->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取登录记录搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 登录记录单条编辑
     */
    public function loginLogAdminEdit($data = [])
    {


        $url = url('loginLogList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelLoginLog->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '登录记录' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '登录记录操作成功', $url] : [RESULT_ERROR, $this->modelLoginLog->getError()];
    }

    /**
     * 登录记录删除
     */
    public function loginLogAdminDel($where = [])
    {

        $result = $this->modelLoginLog->deleteInfo($where);
        
        $result && action_log('删除', '登录记录删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '登录记录删除成功'] : [RESULT_ERROR, $this->modelLoginLog->getError()];
    }
}
