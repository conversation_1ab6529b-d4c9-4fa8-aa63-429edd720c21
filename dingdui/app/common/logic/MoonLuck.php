<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class MoonLuck extends LogicBase
{
    public function basicData($life_conditions = [], $new_conditions = [])
    {

        $exSweTest = get_sington_object('SweTest', "astrology\\SweTestEmpty");
        $life_data = $exSweTest->calculate($life_conditions, ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'm', 'virtual' => ['10', '11']]);

        $new_data = $exSweTest->calculate($new_conditions, ['0', '1', '2', '3', '4', '5', '6']);

        $new_conditions_s = $new_conditions;
        $new_conditions_s['b'] = date('d.m.Y', strtotime($new_conditions_s['b'] . "  -10 day"));
        $new_conditions_s['n'] = 21;
        $new_conditions_s['s'] = '1440m';
        $new_conditions_s['f'] = 'pPTlsj';
        unset($new_conditions_s['house']);

        $in_out_time_list = $exSweTest->universalPhaseProgressed($new_conditions_s,86400*10);

        if (empty($allow_degree)) {
            $allow_degree['0'] = 3;
            $allow_degree['60'] = 3;
            $allow_degree['90'] = 3;
            $allow_degree['120'] = 3;
            $allow_degree['180'] = 3;
        }
        if (empty($planet_degree)) {
            $planet_degree['0'] = 0.5;
            $planet_degree['1'] = 0.5;
        }
        $planet = $this->logicDayLuck->planetSecondPhase($exSweTest, $life_data, $new_data, $allow_degree, $planet_degree, $in_out_time_list);

        $data=$this->logicDayLuck->calculateScore($planet['planet_second'], $planet['house'], $planet['planet_allow_degree']);

        $corpus_cre = $this->logicDayLuck->calculateCorpusList($planet);

        $SignNamesSimple = config('ext_astrology')['SignNamesSimple'];
        $PlanetNamesSimple = config('ext_astrology')['PlanetNamesSimple'];

        $data['life_title'] = [
            ['planet_id' => 0, 'planet_chinese' => $PlanetNamesSimple[0], 'sign_id' => $life_data['planet'][0]['sign']['sign_id'], 'sign_chinese' => $SignNamesSimple[$life_data['planet'][0]['sign']['sign_id']]],
            ['planet_id' => 1, 'planet_chinese' =>  $PlanetNamesSimple[1], 'sign_id' => $life_data['planet'][1]['sign']['sign_id'], 'sign_chinese' =>$SignNamesSimple[$life_data['planet'][1]['sign']['sign_id']]],
            ['planet_id' => 10, 'planet_chinese' =>  $PlanetNamesSimple[10], 'sign_id' => $life_data['planet'][10]['sign']['sign_id'], 'sign_chinese' => $SignNamesSimple[$life_data['ascmcs'][0]['sign']['sign_id']]],
        ];

        $data['corpus'] = array();

        foreach ($corpus_cre as $key => $value) {

            $CorpusConstellation = $this->logicCorpusConstellation->getCorpusConstellationInfo(['oneself' => $value['planet_second_chinese'], 'other' => $value['planet_one_chinese'], 'type' => 6, 'chartType' => 8, 'degree' => $value['allow']], 'oneself,other,degree,keywords,content');
            if (!empty($CorpusConstellation)) {
                $data['corpus'][] = ['title' => $CorpusConstellation['keywords'],
                    'phase_str' => '三限'.$value['planet_second_chinese'] . $value['allow_cn'] .'本命'. $value['planet_one_chinese'],
                    'stat_date' => $value['start_day'],
                    'end_date' => $value['end_day'],
                    'phase_situation' => '已经开始' . $value['start_day']  . '月，' . $value['end_day'] . '月后结束',
                    'content' => $CorpusConstellation['content']];
            }
        }



        return $data;
    }


    //计算语料数据

    public function calculateCorpusList($ex_data)
    {
        $life_house = $ex_data['house'];
        $life_planet = $ex_data['planet'];

        $second_planet = $ex_data['planet_second'];
        $planet_allow_degree = $ex_data['planet_allow_degree'];

        foreach ($second_planet as $keypl => $valupl) {
            $second_planet_new[$valupl['code_name']] = $valupl;
        }
        foreach ($life_planet as $keypl => $valupl) {
            $life_planet_new[$valupl['code_name']] = $valupl;
        }
        $planet_allow_degree_new = array();


        foreach ($life_house as $keyad => $valuead) {
            if (in_array($valuead['house_id'], [1, 4, 7, 10])) {
                if (!empty($valuead['planet_one_list'])) {
                    foreach ($valuead['planet_one_list'] as $keylp => $vlep) {
                        // dump($life_planet_new[$vlep]);
                        $house_cha = abs($life_planet_new[$vlep]['longitude'] - $valuead['longitude']);
                        if ($house_cha < 4) {
                            foreach ($planet_allow_degree as $kesf => $valuef) {
                                if ($valuef['code_one'] == $vlep) {
                                    $planet_allow_degree_new[] = $valuef;
                                    unset($planet_allow_degree[$kesf]);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        foreach ($life_house as $keyad => $valuead) {
            if (in_array($valuead['house_id'], [1, 4, 7, 10])) {
                if (!empty($valuead['planet_second_list'])) {
                    foreach ($valuead['planet_second_list'] as $keylp => $vlep) {
                        // dump($life_planet_new[$vlep]);
                        $house_cha = abs($second_planet_new[$vlep]['longitude'] - $valuead['longitude']);
                        if ($house_cha < 4) {
                            foreach ($planet_allow_degree as $kesf => $valuef) {
                                if ($valuef['code_one'] == $vlep) {
                                    $planet_allow_degree_new[] = $valuef;
                                    unset($planet_allow_degree[$kesf]);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        $planet_list = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'm'];

        foreach ($planet_list as $keyk => $valuek) {
            foreach ($planet_allow_degree as $kesf => $valuef) {
                if ($valuef['code_one'] == $valuek) {
                    $planet_allow_degree_new[] = $valuef;
                    unset($planet_allow_degree[$kesf]);
                    break;
                }
            }
        }

        //本命星体在四轴（4度）的与流年星体有相位先显示
        return $planet_allow_degree_new;
    }
}
