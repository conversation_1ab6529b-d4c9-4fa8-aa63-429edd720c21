<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

use astrology\SweTestEmpty as SweTestEmpty;

/**
 * 配对盘数据生成
 */
class Synastry extends LogicBase
{

    public function plateData($param)
    {
        if (!empty($param['phase'])) {
            foreach ($param['phase'] as $key => $value) {
                $allow_degree[$key] = $value;
            }
        }

        $user_list = $param['user_list'];

        if (empty($allow_degree)) {
            $allow_degree['0'] = 5;
            $allow_degree['30'] = 5;
            $allow_degree['45'] = 5;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['180'] = 5;
        }

        $starsCode = $param['planets'];

        $p = implode('', $starsCode);

        if (!empty($param['virtual'])) {
            $starsCode['virtual'] = $param['virtual'];
        }

        if (!empty($param['planet_xs']) and !empty($param['planet_xs'][0])) {
            $starsCode['planet_xs'] = $param['planet_xs'];
        }

        $exSweTestEmpty = get_sington_object('exSweTestEmpty', SweTestEmpty::class);

        $user_one=$user_list[0];

        $birthdayToTime = strtotime($user_one['birthday']) - $user_one['tz'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $house = $user_one['longitude'] . ',' . $user_one['latitude'] . ',' . $param['h_sys'];

        $arr = [
            'b' => $utdatenow,
            'p' => '0',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];
        $data1 = $exSweTestEmpty->SweTest($arr,  [0]);

        unset($data1[0]);

        $user_second=$user_list[1];

        $birthdayToTime = strtotime($user_second['birthday']) - $user_second['tz'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $house = $user_second['longitude'] . ',' . $user_second['latitude'] . ',' . $param['h_sys'];

        $arr2 = [
            'b' => $utdatenow,
            'p' => $p,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec',

        ];


        $data2 = $exSweTestEmpty->calculatePlanet($arr2, $starsCode);


        $data3=array_merge($data2,$data1);

        $data  = $exSweTestEmpty->calculate($arr, $starsCode, $data3);


        $planets_data['user'] = $param;
        $planets_data['house'] = $this->logicNatal->housePlanet($exSweTestEmpty, $data);
        $sign_attribute = $this->logicNatal->signPlanet($data);
        $planets_data['sign'] = $sign_attribute['sign'];
        $planets_data['planet'] = $this->logicNatal->planetPhase($exSweTestEmpty, $data, $allow_degree);

        if(!empty($param['svg_type']) and $param['svg_type']==1){
            $planets_data['svg'] = $this->logicNatal->simpleSvg($planets_data, $data);
        }else if(!empty($param['svg_type']) and $param['svg_type']==-1){

        }else{
            $planets_data['svg'] = $this->logicNatal->seniorSvg($planets_data, $data);
        }

        $planets_data['attribute'] = $sign_attribute['attribute'];

        return $planets_data;
    }


}
