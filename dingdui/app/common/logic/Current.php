<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

use astrology\SweTest as SweTest;

/**
 * 天象盘数据生成
 */
class Current extends LogicBase
{
    public function plateData($param)
    {
        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        if (!empty($param['phase'])) {
            foreach ($param['phase'] as $key => $value) {
                $allow_degree[$key] = $value;
            }
        }

        $planet_degree = array();
        !empty($param['planet_degree']) && $planet_degree = $param['planet_degree'];

        if (empty($allow_degree)) {
            $allow_degree['0'] = 5;
            $allow_degree['30'] = 5;
            $allow_degree['45'] = 5;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['180'] = 5;
        }

        $starsCode = $param['planets'];

        $birthdayToTime = strtotime($param['birthday']) - $param['tz'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $planets = implode('', $starsCode);

        $arr = [
            'b' => $utdatenow,
            'p' => $planets,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $exSweTest = get_sington_object('exSweTest', SweTest::class);

        if (!empty($param['virtual'])) {
            $starsCode['virtual'] = $param['virtual'];
        }

        if (!empty($param['planet_xs']) and !empty($param['planet_xs'][0])) {
            $starsCode['planet_xs'] = $param['planet_xs'];
        }
        if (!empty($param['planet_xf']) and !empty($param['planet_xf'][0])) {
            $starsCode['planet_xf'] = $param['planet_xf'];
        }
        if (!empty($param['planet_hel']) and !empty($param['planet_hel'][0])) {
            $starsCode['planet_hel'] = $param['planet_hel'];
        }

        $ay = false;
        if (isset($param['ay'])) {
            $ay = $param['ay'];
        }
        $data = $exSweTest->calculate($arr, $starsCode, [], $ay);

        $planets_data['user'] = $param;
        $planets_data['house'] = $this->housePlanet($exSweTest, $data);
        $sign_attribute = $this->signPlanet($data);
        $planets_data['sign'] = $sign_attribute['sign'];

        $data['is_corpus'] = 1;
        if (empty($param['is_corpus'])) {
            $data['is_corpus'] = 0;
        }

        $planets_data['planet'] = $this->planetPhase($exSweTest, $data, $allow_degree, $planet_degree);

        if (!empty($data['is_corpus']) and !empty($data['corpus_where'])) {
            $corpusConstellationWhere['chartType'] = 1;
            $corpusConstellationWhere['type'] = ['in', array_column($data['corpus_where'], 'type')];
            $corpusConstellationWhere['oneself'] = ['in', array_column($data['corpus_where'], 'oneself')];
            $corpusConstellationWhere['other'] = ['in', array_column($data['corpus_where'], 'other')];

            $planets_data['corpus_list'] = $this->logicCorpusConstellation->getCorpusConstellationAllList($corpusConstellationWhere, 'oneself,other,type,chartType,degree,keywords,content', '', false);
        }
        if (empty($param['format'])) {
            $param['format'] = 0;
        }

        if (!empty($param['svg_type']) and $param['svg_type'] == 1) {
            $planets_data['svg'] = $this->simpleSvg($planets_data, $data, $param['format']);
        } else if (!empty($param['svg_type']) and $param['svg_type'] == -1) {

        } else {
            $planets_data['svg'] = $this->seniorSvg($planets_data, $data);
        }
        $planets_data['attribute'] = $sign_attribute['attribute'];

        return $planets_data;
    }

    /**
     * 计算宫内获得行星
     */
    public function housePlanet($exSweTest, $starsDtat = [])
    {
        $house = $starsDtat['house'];
        $planet = $starsDtat['planet'];
        $planet = $starsDtat['planet'];
        foreach ($house as $keyh => &$valueh) {
            $valueh['house_life'] = $starsDtat['house_life'][$keyh];

            $sign_guardian_index = $starsDtat['sign_guardian_index'][$valueh['sign']['sign_id']];

            foreach ($sign_guardian_index as $key => $vole) {
                $main_planet['code_name'] = (string)array_search($vole, $starsDtat['planetEnglish']);
                $main_planet['planet_english'] = $vole;
                $main_planet['planet_chinese'] = $starsDtat['planetChinese'][$main_planet['code_name']];
                $main_planet['planet_font'] = $starsDtat['planetFont'][$vole];
                $valueh['main_planet'][] = $main_planet;
            }

            if (empty($valueh['planet_array'])) {
                $valueh['planet_array'] = array();
            }
            foreach ($planet as $keyp => $valuep) {
                $house_cha = abs($valueh['longitude'] - $valuep['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }

                if ($keyh < 11) {

                    $last_house = $house[$keyh + 1];
                } else {

                    $last_house = $house[0];
                }

                if ($valueh['longitude'] > $last_house['longitude']) {

                    if ($valuep['longitude'] < $last_house['longitude']) {
                        $valuep['longitude'] += 360;
                    }

                    $last_house['longitude'] += 360;
                }

                if ($valuep['longitude'] >= $valueh['longitude'] and $valuep['longitude'] < $last_house['longitude']) {

                    $planet_array_angle = $exSweTest->Convert_deg_min($house_cha);
                    $planet_array_angle['longitude'] = $house_cha;
                    $planet_array_angle['planet_number'] = $keyp;
                    $planet_array_angle['code_name'] = $valuep['code_name'];
                    $planet_array_angle['planet_english'] = $valuep['planet_english'];
                    $planet_array_angle['planet_chinese'] = $valuep['planet_chinese'];
                    $planet_array_angle['planet_font'] = $starsDtat['planetFont'][$valuep['planet_english']];
                    $valueh['planet_array'][] = $planet_array_angle;
                    unset($planet[$keyp]);
                }
            }
        }
        return $house;
    }


    /**
     * 计算星座内获得行星
     */
    public function signPlanet($starsDtat = [])
    {
        $house = $starsDtat['house'];
        $planet = $starsDtat['planet'];
        $sign_phase = $starsDtat['sign_phase'];
        $planet_ascendant = $starsDtat['planet_ascendant']['longitude'];
        $signPlanet = array();

        //Standard fixed changes


        for ($i = 0; $i < 12; $i++) {

            $signPlanet[$i]['sign_id'] = $i;
            $signPlanet[$i]['sign_english'] = $starsDtat['signEnglish'][$i];
            $signPlanet[$i]['sign_chinese'] = $starsDtat['signChinese'][$i];
            $signPlanet[$i]['sign_font'] = $starsDtat['signFont'][$i];

            $signPlanet[$i]['sign_attribute'] = $sign_phase[$i];
            $sign_guardian_index = $starsDtat['sign_guardian_index'][$i];

            foreach ($sign_guardian_index as $key => $vole) {
                $planet_array_angle['code_name'] = (string)array_search($vole, $starsDtat['planetEnglish']);
                $planet_array_angle['planet_number'] = (string)array_search($vole, $starsDtat['planetEnglish']);
                $planet_array_angle['planet_english'] = $vole;
                $planet_array_angle['planet_chinese'] = $starsDtat['planetChinese'][$planet_array_angle['code_name']];
                $planet_array_angle['planet_font'] = $starsDtat['planetFont'][$vole];
                $signPlanet[$i]['sign_guardian'][] = $planet_array_angle;
            }
            if (empty($value['planet_array'])) {
                $signPlanet[$i]['planet_array'] = array();
            }

        }

        $attribute_chinese = ['变动' => 'change', '固定' => 'fixed', '本位' => 'standard', '土相' => 'soil', '水相' => 'water', '火相' => 'fire', '风相' => 'wind'];

        foreach ($planet as $keyp => $valuep) {
            $sign_planet = $valuep;
            $sign_planet['planet_font'] = $starsDtat['planetFont'][$valuep['planet_english']];
            $sign_planet['deg'] = $valuep['sign']['deg'];
            $sign_planet['min'] = $valuep['sign']['min'];
            $sign_planet['sec'] = $valuep['sign']['sec'];
            $sign_planet['sign_id'] = $valuep['sign']['sign_id'];
            $sign_planet['sign_english'] = $valuep['sign']['sign_english'];
            $sign_planet['sign_chinese'] = $valuep['sign']['sign_chinese'];
            $sign_planet['sign_font'] = $valuep['sign']['sign_font'];
            unset($sign_planet['sign']);
            $signPlanet[$valuep['sign']['sign_id']]['planet_array'][] = $sign_planet;

            $attribute[$attribute_chinese[$sign_phase[$valuep['sign']['sign_id']][0]]][] = $sign_planet;
            $attribute[$attribute_chinese[$sign_phase[$valuep['sign']['sign_id']][1]]][] = $sign_planet;
        }

        return ['sign' => $signPlanet, 'attribute' => $attribute];
    }

    /**
     * 计算星座内获得相位
     */
    public function planetPhase($exSweTest, &$starsDtat = [], $allow_degree = [], $planet_degree = [])
    {
        $planetNameObject = get_sington_object('planetName', "astrology\\planetName");

        $houseChinese = $planetNameObject::$houseChinese;

        if (empty($planet_degree)) {
            $planet_degree['0'] = 1;
            $planet_degree['1'] = 1;
        }

        $corpus_where = array();

        $house = $starsDtat['house'];
        $planet = $starsDtat['planet'];
        $planet_twe = $planet;
        $sign_guardian = $starsDtat['sign_guardian_index'];
        foreach ($planet_twe as $key => &$value) {
            $value['planet_font'] = $starsDtat['planetFont'][$value['planet_english']];
            foreach ($house as $keyh => $valueh) {

                $house_cha = abs($valueh['longitude'] - $value['longitude']);
                if ($house_cha > 180) {
                    $house_cha = 360 - $house_cha;
                }

                if ($keyh < 11) {

                    $last_house = $house[$keyh + 1];
                } else {

                    $last_house = $house[0];
                }

                if ($valueh['longitude'] > $last_house['longitude']) {

                    if ($value['longitude'] < $last_house['longitude']) {
                        $value['longitude'] += 360;
                    }

                    $last_house['longitude'] += 360;
                }

                if ($value['longitude'] >= $valueh['longitude'] and $value['longitude'] < $last_house['longitude']) {

                    $house_array_angle = $exSweTest->Convert_deg_min($house_cha);
                    $value['house_id'] = $keyh + 1;
                    $value['house_longitude'] = $house_cha;
                    $value['house_deg'] = $house_array_angle['deg'];
                    $value['house_min'] = $house_array_angle['min'];
                    $value['house_sec'] = $house_array_angle['sec'];

                    if (!empty($starsDtat['is_corpus'])) {
                        $corpusConstellationWhere['chartType'] = 1;
                        $corpusConstellationWhere['type'] = 3;
                        $corpusConstellationWhere['oneself'] = $value['planet_chinese'];
                        $corpusConstellationWhere['other'] = $houseChinese[$value['house_id'] - 1];
                        $corpus_where[] = $corpusConstellationWhere;
                    }
                }
            }
            //星座语料
            if (!empty($starsDtat['is_corpus'])) {
                $corpusConstellationWhere['chartType'] = 1;
                $corpusConstellationWhere['type'] = 5;
                $corpusConstellationWhere['oneself'] = $value['planet_chinese'];
                $corpusConstellationWhere['other'] = $value['sign']['sign_chinese'];
                $corpus_where[] = $corpusConstellationWhere;
            }
            if (empty($value['planet_allow_degree'])) {
                $value['planet_allow_degree'] = array();
            }

            foreach ($planet as $keyg => $valueg) {

                if ($key == $keyg) {
                    continue;
                }
                $chazhi = abs($value['longitude'] - $valueg['longitude']);

                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }

                $planet_degree_lgit = 0;

                !empty($planet_degree[$value['code_name']]) && $planet_degree_lgit += $planet_degree[$value['code_name']];

                !empty($planet_degree[$valueg['code_name']]) && $planet_degree_lgit += $planet_degree[$valueg['code_name']];

                $in_out_xu_long = abs($value['longitude'] - $valueg['longitude']);

                //如果第二星大于第一星值，相减绝对值小于180 那么就是第一追第二
                if ($value['longitude'] < $valueg['longitude']) {
                    if ($in_out_xu_long < 180) {
                        $a_out_info = $value;
                        $b_out_info = $valueg;
                    } else {
                        $a_out_info = $valueg;
                        $b_out_info = $value;
                    }
                } else {
                    if ($in_out_xu_long < 180) {
                        $a_out_info = $valueg;
                        $b_out_info = $value;
                    } else {
                        $a_out_info = $value;
                        $b_out_info = $valueg;
                    }
                }
                foreach ($allow_degree as $keyAd => $valueAd) {

                    $valueAd += $planet_degree_lgit;

                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {

                        $in_out = '-1';
                        $in_out_start = $a_out_info['longitude'] + $keyAd - $valueAd;

                        if ($in_out_start > 360) {
                            $in_out_start = $in_out_start - 360;
                        } elseif ($in_out_start < 0) {
                            $in_out_start = $in_out_start + 360;
                        }
                        $in_out_middle = $in_out_start + $valueAd;
                        if ($in_out_middle > 360) {
                            $in_out_middle = $in_out_middle - 360;
                        } elseif ($in_out_middle < 0) {
                            $in_out_middle = $in_out_middle + 360;
                        }
                        $in_out_end = $in_out_middle + $valueAd;
                        if ($in_out_end > 360) {
                            $in_out_end = $in_out_end - 360;
                        } elseif ($in_out_end < 0) {
                            $in_out_end = $in_out_end + 360;
                        }

                        if ($b_out_info['longitude'] < $in_out_middle) {
                            $location_zuo_you = 'nei';
                            if ($in_out_end < $in_out_middle and $b_out_info['longitude'] < $in_out_end) {
                                $location_zuo_you = 'wai';
                            }
                        } else {
                            $location_zuo_you = 'wai';
                            if ($in_out_start > $in_out_middle and $b_out_info['longitude'] > $in_out_end) {
                                $location_zuo_you = 'nei';
                            }
                        }

                        if ($b_out_info['speed'] > $a_out_info['speed']) {
                            if ($location_zuo_you == 'nei') {
                                $in_out = '1';
                            }
                        } else {
                            if ($location_zuo_you == 'wai') {
                                $in_out = '1';
                            }
                        }

                        $planet_allow_degree = [
                            'planet_number' => $keyg,
                            'code_name' => $valueg['code_name'],
                            'planet_english' => $valueg['planet_english'],
                            'planet_chinese' => $valueg['planet_chinese'],
                            'planet_font' => $starsDtat['planetFont'][$valueg['planet_english']],
                            'current_longitude' => $valueg['longitude'],
                            'allow' => $keyAd,
                            'in_out' => $in_out
                        ];
                        if (!empty($starsDtat['is_corpus'])) {
                            $corpusConstellationWhere['chartType'] = 1;
                            $corpusConstellationWhere['type'] = 6;
                            $corpusConstellationWhere['oneself'] = $value['planet_chinese'];
                            $corpusConstellationWhere['other'] = $valueg['planet_chinese'];
                            $corpus_where[] = $corpusConstellationWhere;
                        }
                        $planet_allow_degree = array_merge($planet_allow_degree, $exSweTest->Convert_sign_deg_min(abs(round(($chazhi - $keyAd), 4))));
                        $value['planet_allow_degree'][] = $planet_allow_degree;

                    }
                }
            }
            $value['longitude'] = $exSweTest->crunch($value['longitude']);
        }

        $starsDtat['corpus_where'] = $corpus_where;
        return $planet_twe;
    }

    /**
     * 计算星座内获得行星
     */
    public function simpleSvg(&$planets_data = [], $starsDtat = [], $format = 1)
    {
        $signPlanet = $planets_data['sign'];
        $housePlanet = $planets_data['house'];
        $planetPhase = $planets_data['planet'];
        $planetFont = $starsDtat['planetFont'];
        $signFont = $starsDtat['signFont'];

        $diameter = 600;
        $radius = $diameter / 2;
        $radius_one = $radius - 5;
        $radius_two = $radius - 75;
        $radius_three = $radius - 100;
        $radius_four = $radius - 140;

        $svgHtml = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:math="http://exslt.org/math" version="1.0" viewBox="0 0 ' . $diameter . ' ' . $diameter . '" id="chart">' .
            '<g id="main" transform="">';
        $svgHtml .= '<g id="chartbody"><circle id="zodiac" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_one . '"></circle>' .
            '<circle id="zodiac_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_two . '"></circle>' .
            '<circle id="hcircle" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_three . '"></circle>' .
            '<circle id="hcircle_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_four . '"></circle>';
        $svgHtml .= '<path d="M ' . $radius . ',' . ($radius - 5) . ' L ' . $radius . ',' . ($radius + 5) . '" class="origin"></path>' .
            '<path d="M ' . ($radius - 5) . ' ' . $radius . ' L ' . ($radius + 5) . ' ' . $radius . '" class="origin"></path>';

        $planet_ascendant = $housePlanet[0]['longitude'];

        //星座画图
        foreach ($signPlanet as $keys => $values) {

            $line_x_top = ((-$radius_one) * cos(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;
            $line_y_top = (($radius_one) * sin(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;
            $line_x_bottom = ((-$radius_two) * cos(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;
            $line_y_bottom = (($radius_two) * sin(deg2rad(($keys * 30) - $planet_ascendant))) + $radius;

            $svgHtml .= '<line  class="zodiac_grid" x1="' . $line_x_top . '" y1="' . $line_y_top . '" x2="' . $line_x_bottom . '" y2="' . $line_y_bottom . '"/>';

            //星座字
            $zodiac_x_top = ((-$radius_two - 30) * cos(deg2rad(($keys * 30) + 15 - $planet_ascendant))) + $radius;
            $zodiac_y_top = (($radius_two + 30) * sin(deg2rad(($keys * 30) + 15 - $planet_ascendant))) + $radius;

            $guardian_sign_x = ((-$radius_two - 30) * cos(deg2rad(($keys * 30) + 8 - $planet_ascendant))) + $radius;
            $guardian_sign_y = (($radius_two + 30) * sin(deg2rad(($keys * 30) + 8 - $planet_ascendant))) + $radius;

            $svgHtml .= '<g id="' . $values['sign_english'] . '">';
            if (count($values['sign_guardian']) == 2) {
                $sign_guardian_n = $values['sign_guardian'][$format];
            } else {
                $sign_guardian_n = $values['sign_guardian'][0];
            }

            $svgHtml .= '<text class="text_font sign_font sign_' . $values['sign_english'] . '" x="' . $zodiac_x_top . '" y="' . $zodiac_y_top . '" serial="' . $values['sign_id'] . '">' . $signFont[$values['sign_id']] . '</text>';
            $svgHtml .= '<text class="text_font guardian_font planets_' . $sign_guardian_n['planet_english'] . '" x="' . $guardian_sign_x . '" y="' . $guardian_sign_y . '" serial="' . $sign_guardian_n['planet_english'] . '">' . $planetFont[$sign_guardian_n['planet_english']] . '</text>';
            $svgHtml .= '</g>';
        }
        //宫位画图
        foreach ($housePlanet as $keyh => &$valueh) {
            $angle = -($planet_ascendant - $valueh['longitude']);

            $house_grid_class = 'house_dark_grid';
            if (!($keyh % 3)) {
                $radius_top = $radius_one + 5;
                $house_grid_class = 'house_dark_grid house_dark_grid_attribute';
            } else {
                $radius_top = $radius_two;
            }

            $house_dark_x_top = ((-$radius_top) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_top = (($radius_top) * sin(deg2rad($angle))) + $radius;
            $house_dark_x_bottom = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_bottom = (($radius_four) * sin(deg2rad($angle))) + $radius;

            $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="' . $house_grid_class . '" x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';

            $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="house_grid" x1="' . $radius . '" y1="' . $radius . '" x2="' . $house_dark_x_bottom . '" y2="' . $house_dark_y_bottom . '"/>';

            if ($keyh == 11) {
                $zitijiaodu = abs($housePlanet[0]['longitude'] - $valueh['longitude']);

            } else {
                $zitijiaodu = abs($housePlanet[$keyh + 1]['longitude'] - $valueh['longitude']);
            }

            if ($zitijiaodu > 180) {
                $zitijiaodu = 360 - $zitijiaodu;
            }

            $house_id_x = ((-$radius_three - 13) * cos(deg2rad($angle + $zitijiaodu / 2))) + $radius;
            $house_id_y = (($radius_three + 13) * sin(deg2rad($angle + $zitijiaodu / 2))) + $radius;

            $valueh['z_x'] = $house_id_x;
            $valueh['z_y'] = $house_id_y;

            $svgHtml .= ' <g><text class="text_font house_id house_' . ($keyh + 1) . '" x="' . $house_id_x . '" y="' . $house_id_y . '"  serial="' . $keyh . '">' . ($keyh + 1) . '</text> </g>';
        }

        $planets_data['house'] = $housePlanet;
        //行星画图
        $planet_arrays = $planetPhase;

        array_multisort(array_column($planetPhase, 'longitude'), SORT_ASC, $planet_arrays);

        foreach ($planet_arrays as $keygf => &$volegf) {
            if ($keygf > 0) {
                if ($volegf['longitude'] < ($planet_arrays[$keygf - 1]['longitude'] + 7)) {

                    $volegf['longitude'] = $planet_arrays[$keygf - 1]['longitude'] + 7;
                }
            }
            $planet_array_code_name[$volegf['code_name']] = $volegf['longitude'];
        }

        $planet_count_code = count($planet_arrays);

        if (($planet_arrays[0]['longitude'] + 360) < ($planet_arrays[$planet_count_code - 1]['longitude'] + 7)) {

            $planet_arrays[0]['longitude'] = $planet_arrays[$planet_count_code - 1]['longitude'] + 7;

            ($planet_arrays[0]['longitude'] > 360) && $planet_arrays[0]['longitude'] -= 360;

            foreach ($planet_arrays as $keygft => &$volegft) {
                if ($keygft > 0) {
                    if ($volegft['longitude'] < ($planet_arrays[$keygft - 1]['longitude'] + 7)) {

                        $volegft['longitude'] = $planet_arrays[$keygft - 1]['longitude'] + 7;
                    } else {
                        break;
                    }
                }

                $planet_array_code_name[$volegft['code_name']] = $volegft['longitude'];
            }
        }


        foreach ($planetPhase as $keyp => &$valuep) {

            $angle = -($planet_ascendant - $valuep['longitude']);
            $planet_x_circle = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $planet_y_circle = (($radius_four) * sin(deg2rad($angle))) + $radius;

            $svgHtml .= '<circle class="planets_circle planets_' . $valuep['planet_english'] . '" cx="' . $planet_x_circle . '" cy="' . $planet_y_circle . '" r="1.5"></circle>';

            $planet_allow_degree = $valuep['planet_allow_degree'];
            foreach ($planet_allow_degree as $keya => $valuea) {
                $planet_x_bottom = ((-$radius_four) * cos(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $planet_y_bottom = (($radius_four) * sin(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $svgHtml .= '<line class="planet_sign_line planet_sign_line_' . $valuea['allow'] . '" style="stroke-width:1;stroke-dasharray: 1,' . $valuea['deg'] . ';" x1="' . $planet_x_circle . '" y1="' . $planet_y_circle . '" x2="' . $planet_x_bottom . '" y2="' . $planet_y_bottom . '"/>';
            }

            $text_angle = -($planet_ascendant - $planet_array_code_name[$valuep['code_name']]);
            $planet_x_text = ((-$radius_four - 20) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text = (($radius_four + 20) * sin(deg2rad($text_angle))) + $radius;

            $svgHtml .= '<g id="' . $valuep['planet_english'] . '">';
            $svgHtml .= '<line style="stroke: gray;stroke-linecap: round;stroke-dasharray: 1,2;" x1="' . $planet_x_circle . '" y1="' . $planet_y_circle . '" x2="' . $planet_x_text . '" y2="' . $planet_y_text . '"/>';

            $svgHtml .= '<text class="text_font planet_font planets_' . $valuep['planet_english'] . '" x="' . $planet_x_text . '" y="' . $planet_y_text . '" serial="' . $valuep['planet_english'] . '">' . $planetFont[$valuep['planet_english']] . '</text>';
            $svgHtml .= '</g>';
            $valuep['z_x'] = $planet_x_text;
            $valuep['z_y'] = $planet_y_text;

        }
        $planets_data['planet'] = $planetPhase;
        $svgHtml .= '</g></g></svg>';
        return $svgHtml;
    }

    /**
     * 计算星座内获得行星
     */
    public function seniorSvg($planets_data = [], $starsDtat = [])
    {
        $signPlanet = $planets_data['sign'];
        $housePlanet = $planets_data['house'];
        $planetPhase = $planets_data['planet'];
        $planetFont = $starsDtat['planetFont'];
        $signFont = $starsDtat['signFont'];

        $diameter = 600;
        $radius = $diameter / 2;
        $radius_one = $radius - 5;
        $radius_two = $radius - 40;
        $radius_three = $radius - 190;
        $radius_four = $radius - 220;


        $svgHtml = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:math="http://exslt.org/math" version="1.0" viewBox="0 0 ' . $diameter . ' ' . $diameter . '" id="chart">' .
            '<g id="main" transform="">';
        $svgHtml .= '<g id="chartbody"><circle id="zodiac" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_one . '"></circle>' .
            '<circle id="zodiac_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_two . '" style="fill: white;"></circle>' .
            '<circle id="hcircle" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_three . '" style="fill: #EFF;"></circle>' .
            '<circle id="hcircle_min" cx="' . $radius . '" cy="' . $radius . '" r="' . $radius_four . '"></circle>';
        $svgHtml .= '<path d="M ' . $radius . ',' . ($radius - 5) . ' L ' . $radius . ',' . ($radius + 5) . '" class="origin"></path>' .
            '<path d="M ' . ($radius - 5) . ' ' . $radius . ' L ' . ($radius + 5) . ' ' . $radius . '" class="origin"></path>';

        $planet_ascendant = $housePlanet[0]['longitude'];

        //宫位画图
        foreach ($housePlanet as $keyh => $valueh) {
            $angle = -($planet_ascendant - $valueh['longitude']);

            $house_dark_x_top = ((-$radius_two) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_top = (($radius_two) * sin(deg2rad($angle))) + $radius;
            $house_dark_x_bottom = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $house_dark_y_bottom = (($radius_four) * sin(deg2rad($angle))) + $radius;


            if (!($keyh % 3)) {
                $house_beyond_dark_x_top = ((-$radius_one) * cos(deg2rad($angle))) + $radius;
                $house_beyond_dark_y_top = (($radius_one) * sin(deg2rad($angle))) + $radius;
                $radius_top = $radius_one + 5;
                $house_beyond_dark_x_bottom = ((-$radius_top) * cos(deg2rad($angle))) + $radius;
                $house_beyond_dark_y_bottom = (($radius_top) * sin(deg2rad($angle))) + $radius;
                $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="house_dark_grid_attribute" x1="' . $house_beyond_dark_x_top . '" y1="' . $house_beyond_dark_y_top . '" x2="' . $house_beyond_dark_x_bottom . '" y2="' . $house_beyond_dark_y_bottom . '"/>';
                $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="house_dark_grid_attribute"  x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';

            } else {
                $svgHtml .= '<line shaft="' . $valueh['house_id'] . '" class="house_dark_grid" x1="' . $house_dark_x_bottom . '" y1="' . $house_dark_y_bottom . '" x2="' . $house_dark_x_top . '" y2="' . $house_dark_y_top . '"/>';
            }


            if ($keyh == 11) {
                $zitijiaodu = abs($housePlanet[0]['longitude'] - $valueh['longitude']);

            } else {
                $zitijiaodu = abs($housePlanet[$keyh + 1]['longitude'] - $valueh['longitude']);
            }

            if ($zitijiaodu > 180) {
                $zitijiaodu = 360 - $zitijiaodu;
            }

            $house_id_x = ((-$radius_four - 13) * cos(deg2rad($angle + $zitijiaodu / 2))) + $radius;
            $house_id_y = (($radius_four + 13) * sin(deg2rad($angle + $zitijiaodu / 2))) + $radius;

            $svgHtml .= ' <g><text class="must_symbo_font house_id house_' . ($keyh + 1) . '" x="' . $house_id_x . '" y="' . $house_id_y . '"  serial="' . $keyh . '">' . ($keyh + 1) . '</text> </g>';

            $hou_cha = -5;
            if ($angle > 180 or $angle < 270) {
                $hou_cha = 5;
            }

            $sign_var = $valueh['sign'];
            //星座度数
            $zodiac_signs_deg_x = ((-($radius_one - 15)) * cos(deg2rad($angle - $hou_cha))) + $radius;
            $zodiac_signs_deg_y = ((($radius_one - 15)) * sin(deg2rad($angle - $hou_cha))) + $radius;

            $svgHtml .= '<text class="must_symbo_font senior_planet_deg longitude_' . $sign_var['sign_id'] . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $sign_var['deg'] . '</text>';

            //星座字
            $zodiac_x_top = ((-($radius_one - 15)) * cos(deg2rad($angle))) + $radius;
            $zodiac_y_top = ((($radius_one - 15)) * sin(deg2rad($angle))) + $radius;
            $svgHtml .= '<g id="' . $sign_var['sign_english'] . '">';
            $svgHtml .= '<text class="must_symbo_font sign_font senior_sign_font sign_' . $sign_var['sign_english'] . '" x="' . $zodiac_x_top . '" y="' . $zodiac_y_top . '" serial="' . $sign_var['sign_id'] . '">' . $sign_var['sign_font'] . '</text>';
            $svgHtml .= '</g>';

            //星座秒数
            $zodiac_signs_deg_x = ((-($radius_one - 15)) * cos(deg2rad($angle + $hou_cha))) + $radius;
            $zodiac_signs_deg_y = ((($radius_one - 15)) * sin(deg2rad($angle + $hou_cha))) + $radius;

            $svgHtml .= '<text class="must_symbo_font senior_planet_min longitude_' . $sign_var['sign_id'] . '" x="' . $zodiac_signs_deg_x . '" y="' . $zodiac_signs_deg_y . '">' . $sign_var['min'] . '</text>';


        }
//
//        //行星画图
        $planet_arrays = $planetPhase;

        array_multisort(array_column($planetPhase, 'longitude'), SORT_ASC, $planet_arrays);

        foreach ($planet_arrays as $keygf => &$volegf) {
            if ($keygf > 0) {
                if ($volegf['longitude'] < ($planet_arrays[$keygf - 1]['longitude'] + 8)) {

                    $volegf['longitude'] = $planet_arrays[$keygf - 1]['longitude'] + 8;
                }

            }
            $planet_array_code_name[$volegf['code_name']] = $volegf['longitude'];
        }

        $planet_count_code = count($planet_arrays);

        if (($planet_arrays[0]['longitude'] + 360) < ($planet_arrays[$planet_count_code - 1]['longitude'] + 7)) {

            $planet_arrays[0]['longitude'] = $planet_arrays[$planet_count_code - 1]['longitude'] + 7;

            ($planet_arrays[0]['longitude'] > 360) && $planet_arrays[0]['longitude'] -= 360;

            foreach ($planet_arrays as $keygft => &$volegft) {
                if ($keygft > 0) {
                    if ($volegft['longitude'] < ($planet_arrays[$keygft - 1]['longitude'] + 7)) {

                        $volegft['longitude'] = $planet_arrays[$keygft - 1]['longitude'] + 7;
                    } else {
                        break;
                    }
                }
                $planet_array_code_name[$volegft['code_name']] = $volegft['longitude'];
            }
        }


        foreach ($planetPhase as $keyp => $valuep) {

            $angle = -($planet_ascendant - $valuep['longitude']);
            $planet_x_circle = ((-$radius_four) * cos(deg2rad($angle))) + $radius;
            $planet_y_circle = (($radius_four) * sin(deg2rad($angle))) + $radius;

            $svgHtml .= '<circle class="planets_circle planets_' . $valuep['planet_english'] . '" cx="' . $planet_x_circle . '" cy="' . $planet_y_circle . '" r="1.5"></circle>';


            $planet_allow_degree = $valuep['planet_allow_degree'];
            foreach ($planet_allow_degree as $keya => $valuea) {
                $planet_x_bottom = ((-$radius_four) * cos(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $planet_y_bottom = (($radius_four) * sin(deg2rad(-($planet_ascendant - $valuea['current_longitude'])))) + $radius;
                $svgHtml .= '<line class="planet_sign_line planet_sign_line_' . $valuea['allow'] . '" style="stroke-width:1;stroke-dasharray: 1,' . $valuea['deg'] . ';" x1="' . $planet_x_circle . '" y1="' . $planet_y_circle . '" x2="' . $planet_x_bottom . '" y2="' . $planet_y_bottom . '"/>';
            }
            $text_angle = -($planet_ascendant - $planet_array_code_name[$valuep['code_name']]);

            $planet_x_line_top = ((-$radius_two) * cos(deg2rad($angle))) + $radius;
            $planet_y_line_top = (($radius_two) * sin(deg2rad($angle))) + $radius;

            $planet_x_line_bottom = ((-($radius_two - 15)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_line_bottom = ((($radius_two - 15)) * sin(deg2rad($text_angle))) + $radius;

            $svgHtml .= '<line style="stroke: gray;stroke-linecap: round;stroke-dasharray: 1,2;" x1="' . $planet_x_line_top . '" y1="' . $planet_y_line_top . '" x2="' . $planet_x_line_bottom . '" y2="' . $planet_y_line_bottom . '"/>';

            $planet_x_text = ((-($radius_two - 25)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text = ((($radius_two - 25)) * sin(deg2rad($text_angle))) + $radius;

            $sign_var = $valuep['sign'];

            $svgHtml .= '<g id="' . $valuep['planet_english'] . '">';

            //行星字
            $svgHtml .= '<text class="must_symbo_font planet_font senior_planet_font planets_' . $valuep['planet_english'] . '" x="' . $planet_x_text . '" y="' . $planet_y_text . '" serial="' . $valuep['planet_english'] . '">' . $planetFont[$valuep['planet_english']] . '</text>';

            //行星度
            $planet_x_text_deg = ((-($radius_two - 55)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text_deg = ((($radius_two - 55)) * sin(deg2rad($text_angle))) + $radius;
            $svgHtml .= '<text class="must_symbo_font senior_planet_deg longitude_' . $sign_var['sign_id'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '">' . $sign_var['deg'] . '</text>';

            //行星星座
            $planet_x_text_deg = ((-($radius_two - 85)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text_deg = ((($radius_two - 85)) * sin(deg2rad($text_angle))) + $radius;

            $svgHtml .= '<text class="must_symbo_font sign_font senior_sign_font sign_' . $sign_var['sign_english'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '" serial="' . $sign_var['sign_id'] . '">' . $sign_var['sign_font'] . '</text>';

            //行星分
            $planet_x_text_deg = ((-($radius_two - 115)) * cos(deg2rad($text_angle))) + $radius;
            $planet_y_text_deg = ((($radius_two - 115)) * sin(deg2rad($text_angle))) + $radius;
            $svgHtml .= '<text class="must_symbo_font senior_planet_min longitude_' . $sign_var['sign_id'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '">' . $sign_var['min'] . '</text>';

            if ($valuep['speed'] < 0) {
                //行星度
                $planet_x_text_deg = ((-($radius_two - 140)) * cos(deg2rad($text_angle))) + $radius;
                $planet_y_text_deg = ((($radius_two - 140)) * sin(deg2rad($text_angle))) + $radius;
                $svgHtml .= '<text class="must_symbo_font senior_house_sign_r longitude_' . $sign_var['sign_id'] . '" x="' . $planet_x_text_deg . '" y="' . $planet_y_text_deg . '">Ú</text>';
            }


            $svgHtml .= '</g>';


        }
        $svgHtml .= '</g></g></svg>';
        return $svgHtml;
    }


    public function plateSweTestQuery($param)
    {
        $exSweTestEmpty = get_sington_object('exSweTestEmpty', "astrology\\SweTestEmpty");

        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        foreach ($param['birthday'] as $keys => $argvs) {

            $birthdayToTime = strtotime($argvs) - $param['tz'] * 3600;

            $utdatenow = date('d.m.Y', $birthdayToTime);

            $utnow = date('H:i:s', $birthdayToTime);

            $planets = implode('', $param['planets']);

            $arr = [
                'b' => $utdatenow,
                'p' => $planets,
                'ut' => $utnow,
                'f' => 'pTlsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'head',
                'roundsec'
            ];
            $planets_data[] = $exSweTestEmpty->SweTestQuery($arr);
        }

        return $planets_data;
    }


    public function plateTransits($param)
    {
        $exSweTestEmpty = get_sington_object('exSweTestEmpty', "astrology\\SweTestEmpty");

        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        foreach ($param['birthday'] as $keys => $argvs) {

            $birthdayToTime = strtotime($argvs) - $param['tz'] * 3600;

            $utdatenow = date('d.m.Y', $birthdayToTime);

            $utnow = date('H:i:s', $birthdayToTime);

            $planets = implode('', $param['planets']);

            $arr = [
                'b' => $utdatenow,
                'p' => $planets,
                'house' => $house,
                'ut' => $utnow,
                'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'head',
                'roundsec'
            ];
            $planets_data[] = $exSweTestEmpty->SweTestQuery($arr);
        }

        return $planets_data;
    }

    //通用直接命令获取数据
    public function universalData($param)
    {

        if ($param['key'] != 123789) {
            return false;
        }
        unset($param['key']);
        unset($param['access_token']);
        if (!is_array($param['conditions'])) {
            $arr = json_decode(html_entity_decode($param['conditions']), true);
        } else {
            $arr = $param['conditions'];
        }
        $arr['g'] = ',';
        $arr['head'] = '';
        $arr['roundsec'] = '';
        $exSweTest = get_sington_object('exSweTest', SweTest::class);

        return $exSweTest->SweTest($arr);
    }

    //通用直接命令获取数据
    public function transitsProgressed($param)
    {

        unset($param['access_token']);

        if (!is_array($param['conditions'])) {
            $arr = json_decode(html_entity_decode($param['conditions']), true);
        } else {
            $arr = $param['conditions'];
        }

        $arr['f'] = 'pTlsj';
        $arr['g'] = ',';
        $arr['e'] = 'swe';
        $arr['head'] = '';
        $arr['roundsec'] = '';

        $exSweTest = get_sington_object('exSweTest', SweTest::class);

        $full_moon_time = $exSweTest->transitsProgressed($arr, $param['degree'], $param['cycle']);

        return $full_moon_time;
    }
}
