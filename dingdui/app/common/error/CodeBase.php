<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\common\error;

class CodeBase
{
    
    public static $success              = [API_CODE_NAME => 0,         API_MSG_NAME => '操作成功'];
    
    public static $accessTokenError     = [API_CODE_NAME => 1000001,   API_MSG_NAME => '访问access_token错误'];

    public static $accessTokenFrequent     = [API_CODE_NAME => 1000002,   API_MSG_NAME => '访问access_token频繁'];

    public static $accessTokenLimit     = [API_CODE_NAME => 1000003,   API_MSG_NAME => '访问access_token上限'];

    public static $apiUrlError          = [API_CODE_NAME => 1000006,   API_MSG_NAME => '接口路径错误'];

    public static $dataFieldIsMull        = [API_CODE_NAME => 1000007,   API_MSG_NAME => '请求字段为空'];

    public static $dataSignError        = [API_CODE_NAME => 1000009,   API_MSG_NAME => '数据签名错误'];

    public static $userTokenError       = [API_CODE_NAME => 1000010,   API_MSG_NAME => '您还未登录'];

    public static $apiUrlBlacklist          = [API_CODE_NAME => 1000011,   API_MSG_NAME => '你访问权限被拉入黑名单，请联系客服'];

    public static $passwordError            = [API_CODE_NAME => 1010001, API_MSG_NAME => '登录密码错误'];

    public static $usernameOrPasswordEmpty  = [API_CODE_NAME => 1010002, API_MSG_NAME => '用户名或验证码不能为空'];

    public static $registerFail             = [API_CODE_NAME => 1010003, API_MSG_NAME => '注册失败'];

    public static $oldOrNewPassword         = [API_CODE_NAME => 1010004, API_MSG_NAME => '旧密码或新密码不能为空'];

    public static $changePasswordFail       = [API_CODE_NAME => 1010005, API_MSG_NAME => '密码修改失败'];

    public static $smsIsError               = [API_CODE_NAME => 1010007, API_MSG_NAME => '短信验证码错误'];
}
