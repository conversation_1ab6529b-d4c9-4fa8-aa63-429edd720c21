<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\astrology\controller;

class Common extends ApiBase
{

    public function planetConfig(){

        $p = get_sington_object('planetName', "astrology\\planetName");
        $planets = array('0' => '太阳',
            '1' => '月亮',
            '2' => '水星',
            '3' => '金星',
            '4' => '火星',
            '5' => '木星',
            '6' => '土星',
            '7' => '天王星',
            '8' => '海王星',
            '9' => '冥王星',
            't' => '真实的月切点',
            'D' => '凯龙',
            'E' => '人龙',
            'F' => '谷神',
            'G' => '智神',
            'H' => '婚神',
            'I' => '灶神',
            'm' => '北交',
            'A' => '莉莉丝',
            'J' => '丘比特',
            'K' => '哈迪斯',
            'L' => '宙斯',
            'M' => '克洛诺斯',
            'N' => '阿波罗',
            'O' => '安德门图斯',
            'P' => '弗卡奴斯',
            'Q' => '波塞冬');
        $virtual = array(
            '10' => '上升',
            '11' => '中天',
            '19' => '天底',
            '18' => '下降',
            '21' => '南交',
            'pFortune' => '福点',
            '13' => '宿命',
            '14' => '东升',
            '17' => '上升的',
            '20' => '日月中');
        $planet_xs = array(
            'xs433' => '爱神',
            'xs16' => '灵神星',
        );
        $planet_xf = array(
            'Regulus' => '轩辕十四',
            'Spica' => '角',
            'Capulus' => '南船二',
            'Algol' => '大棱五',
            'Syrma' => '亢',
            'Zubenelakribi' => '氐',
            'Acrab' => '房',
            'Antares' => '心',
            'Grafias' => '尾',
            'Alnasl' => '箕',
            'KausBorealis' => '斗',
            'GiediPrima' => '牛',
            'Albali' => '女',
            'Sadalsuud' => '虛',
            'Sadalmelik' => '危',
            'Scheat' => '室',
            'Algenib' => '壁',
            'Mirach' => '奎',
            'Sheratan' => '婁',
            'Bharani' => '胃',
            'Alcyone' => '昴',
            'Aldebaran' => '畢',
            'Meissa' => '觜',
            'Mintaka' => '参',
            'Castor' => '井',
            'PraesepeCluster' => '鬼',
            'Mautinah' => '柳',
            'Alphard' => '星',
            'Zhang' => '張',
            'Alkes' => '翼',
            'Gienah' => '軫'
        );

        return $this->apiReturn([
            'planets'=>$planets,
            'virtual'=>$virtual,
            'planet_xs'=>$planet_xs,
            'planet_xf'=>$planet_xf,

            'planetEnglish'=>$p::$planetEnglish,
            'planetChinese'=>$p::$planetChinese,
            'planetFont'=>$p::$planetFont,
            'signFont'=>$p::$signFont,
            'signEnglish'=>$p::$signEnglish,
            'sign_guardian_index'=>$p::$sign_guardian_index,
            'house_life'=>$p::$house_life,
            'sign_phase'=>$p::$sign_phase,
            'signChinese'=>$p::$signChinese

            ]);
    }




}
