<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\index\controller;

use app\common\controller\ControllerBase;

use think\Cache;

class Timing extends ControllerBase
{


    public function apistatistical()
    {

        $create_time=mktime(date('H'),0,0,date('m'),date('d'),date('Y'));
        $create_time-=3600;
        $list=$this->logicMember->getMemberColumn(['status'=>['gt',-2]]);

        foreach ($list as $key=>&$value){

            $data_post['count']=Cache::store('redis')->get($value['id'].'_count_'.$create_time);
            $data_post['create_time']=$create_time;
            $data_post['member_id']=$value['id'];
            //$data[]=$data_post;
            if(!$data_post['count']){
                $data_post['count']=0;
            }
            dump($data_post);
            $data_where=db('api_statistical')->where(['create_time'=>$create_time,'member_id'=>$value['id']])->find();
            dump($data_where);
            if(!empty($data_where)){
                db('api_statistical')->where('id', $data_where["id"])->update($data_post);
            }else{
                db('api_statistical')->insert($data_post);
            }
            delRedis($value['id'].'_count_'.$create_time);
            //Cache::store('redis')->rm($value['id'].'_count_'.$create_time);
        }

        //$this->logicApiStatistical->setApiStatisticalColumn($data);
    }
}
