<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;

use app\common\controller\ControllerBase;
use think\Hook;

/**
 * 前端模块基类控制器
 */
class IndexBase extends ControllerBase
{
    // 请求参数
    protected $companyInfo;
    /**
     * 构造方法
     */
    public function __construct()
    {
        
        // 执行父类构造方法
        parent::__construct();
        
        // 前台控制器钩子
        // Hook::listen('hook_controller_index_base', $this->request);
        $this->company();
    }

    public function company()
    {

        //获取所有友情链接信息
        $this->assign('blogroll_column', $this->logicBlogroll->getBlogrollColumn(['status' => 1]));

        //获取所有分类信息
        $navigation_list = $this->logicNavigation->getNavigationList(['status'=>1],true,'sort asc',false);

        $seo_ids = array_column($navigation_list, 'seo_id');

        foreach ($navigation_list as $keyNa=>&$valueNa){

            $valueNa['subset'] = $this->logicNavigation->getNavigationList(['pid'=>$valueNa['id'],'status'=>1],true,'sort asc',false);

        }
        $this->assign('navigation_list', $navigation_list);

        //获取所有轮播信息
        $where_swiper['status']=1;
        $where_swiper['location']=0;
        foreach ($navigation_list as $key=>$value){
            if($value['url']==ACTION_NAME){
                $where_swiper['location']=$value['id'];
                continue;
            }
        }

        $this->assign('swiper_column',array_values($this->logicSwiper->getSwiperList($where_swiper,'*','sort asc',false)));

        $company_info = ['id'=>1,'title'=>'免费提供专业星盘API，星盘接口',
            'keywords'=>'星盘接口,星盘API,占星api,星盘算法,占星',
            'description'=>'免费提供专业星盘API，星盘接口，包括本命星盘，各种推运星盘，次限盘，三限盘，合盘，比较盘，synastry配对盘，组合中点盘，时空中点盘，马克思盘，法达，占星骰子，塔罗，牌阵，卜卦盘，择日盘，古典占星盘，印度占星盘等定制化服务。星盘接口,星盘API,占星api'];

        $this->companyInfo = $company_info;

        $this->assign('navigation_id', $where_swiper['location']);

        $this->assign('company_info', $company_info);
    }


}
