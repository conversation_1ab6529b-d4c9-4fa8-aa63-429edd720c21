<style>
	.label {
		cursor: pointer;
		display: inline-block;
		margin: 2px;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;

	}
</style>
<form action="{:url()}" method="post" class="form_single">

	<div class="box">
		<ul id="myTab" class="nav nav-tabs">
			<li class="active"><a href="#home" data-toggle="tab">基础信息</a></li>
			<li><a href="#seo" data-toggle="tab">SEO配置</a></li>
			<li><a href="#aiccc" data-toggle="tab">爱神星发布</a></li>
			<li><a href="#astrology" data-toggle="tab">占星网发布</a></li>
		</ul>
		<div class="box-body">
			<div id="myTabContent" class="tab-content">
				<div class="tab-pane fade in active" id="home">
					<div class="row">
						<div class="col-md-12">
							<div class="form-group">
								<label>标题</label>
								<span class="">（文章标题名称）</span>
								<input class="form-control  name_title" name="name" placeholder="请输入文章标题名称"
									value="{$info['name']|default=''}" type="text">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>分类</label>
								<span class="">（列表显示）</span>
								<select class="form-control" name="category_id">
									{notempty name='category_column'}
									{volist name='category_column' id='vo'}
									<option value="{$vo.id}">{$vo.name}</option>
									{/volist}
									{/notempty}
								</select>
							</div>
						</div>


						<div class="col-md-12">
							<div class="form-group">
								<label>标签</label>
								<span class="">（双击删除）</span>
								<input class="label_ids" type="hidden" name="label_id" value="">
								<div class="panel panel-default">
									<div class="panel-body label_html">
										{notempty name='labelAccessColumn'}
										{volist name='labelAccessColumn' id='vo'}
										<span label_id="{$vo.id}"
											class="lable_there label label-success">{$vo.title}</span>
										{/volist}
										{/notempty}
									</div>
								</div>
								<div class="panel panel-default">
									<div class="panel-heading">
										<h4 class="panel-title">
											<a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo">
												已有标签库+
											</a>
										</h4>
									</div>
									<div id="collapseTwo" class="panel-collapse collapse">
										<div class="panel-body">
											{notempty name='label_column'}
											{volist name='label_column' id='vo'}
											<span label_id="{$vo.id}"
												class="lable_add label label-info">{$vo.title}</span>
											{/volist}
											{/notempty}
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>默认浏览次数</label>
								<span class="">（输入次数）</span>
								<input class="form-control" name="view" placeholder="0"
									value="{$info['view']|default='99'}" type="number">
							</div>
						</div>

						<div class="col-md-12">
							<div class="form-group">
								<label>作者</label>
								<span class="">（文章作者）</span>
								<input class="form-control" name="author" placeholder="请输入作者"
									value="{$info['author']|default='刘轶liuyilook'}" type="text">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>描述</label>
								<span class="">（文章描述信息/简介）</span>
								<textarea class="form-control" name="describe" rows="3"
									placeholder="请输入文章描述信息/简介">{$info['describe']|default='十二星座每日运势'}</textarea>
							</div>
						</div>

						<div class="col-md-12">
							<div class="form-group">
								<label>创建时间</label>
								<span class="">（创建时间）</span>
								<input name="create_time" size="16" type="text"
									value="{$info.create_time|default=date('Y-m-d H:i')}" readonly
									class="form_datetime form-control">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>发布时间</label>
								<span class="">（发布时间）</span>
								<input name="release_time" size="16" type="text"
									value="{$info.release_time|default=time()|date='Y-m-d H:i',###}" readonly
									class="form_datetime form-control">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>封面图片</label>
								<span class="">（请上传单张封面图片）</span>
								<br />

								{assign name="cover_id" value="$info.cover_id|default='0'" /}
								{:widget('file/index', ['name' => 'cover_id', 'value' => $cover_id, 'type' => 'img'])}
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-12">
							<div class="form-group">
								<label>多图介绍</label>
								<span class="">（请上传图片）</span>
								<br />
								{assign name="img_ids" value="$info.img_ids|default='0'" /}
								{:widget('file/index', ['name' => 'img_ids', 'value' => $img_ids, 'type' => 'imgs'])}
							</div>
						</div>

					</div>
					<div class="row">
						<div class="col-md-12">
							<div class="form-group">
								<label>文章内容</label>
								<a href="http://collect.xingpan.vip/index.php?s=/Admin/Index/index"
									target="_blank">采集器配置</a>
								<p><br /></p>

								<a class="btn btn-primary" href="javascript:void(-1);" onclick="show_modal();">抓取</a>

								<a href="javascript:void(-1);" type="submit" class="btn Areplace">
									<span class="ladda-label">全部替换</span>
								</a>
								<a href="javascript:void(-1);" type="submit" class="btn Areplace1">
									<span class="ladda-label">格式替换</span>
								</a>
								<a href="javascript:void(-1);" type="submit" class="btn Areplace2">
									<span class="ladda-label">关键词替换</span>
								</a>
								<p><br /></p>

								<textarea id="ke" class="form-control textarea_editor" name="content"
									placeholder="请输入文章内容">{$info['content']|default=''}</textarea>
								{:widget('editor/index', array('name'=> 'content','value'=> ''))}
							</div>
						</div>

					</div>
				</div>

				<div class="tab-pane fade" id="seo">
					<input type="hidden" name="seo[id]" value="{$info['seo_id']|default='0'}" />
					<div class="row">
						<div class="col-md-12">
							<div class="form-group">
								<label>Title</label>
								<span class="">（网页标题）</span>
								<input class="form-control seo_title" name="seo[title]" placeholder="请输入文章网页标题"
									value="{$seo['title']|default=''}" type="text">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>Keywords</label>
								<span class="">（网页关键词）</span>
								<input class="form-control seo_keywords" name="seo[keywords]" placeholder="关键字字数100字内"
									value="{$seo['keywords']|default=''}" type="text">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>Description</label>
								<span class="">（网页描述）</span>
								<input class="form-control seo_description" name="seo[description]"
									placeholder="描述字250字内，最好问题内容相关" value="{$seo['description']|default=''}" type="text">
							</div>
						</div>
					</div>
				</div>


				<div class="tab-pane fade" id="aiccc">
					<div class="row">
						<div class="col-md-12">
							<div class="form-group">
								<label>分类</label>
								<span class="">（列表显示）</span>
								<input class="form-control" name="ac_id" value="{$info['ac_id']|default=0}" type=hidden>

								<select class="form-control" name="ac_category_list">
									{notempty name='ac_category_list'}
									{volist name='ac_category_list' id='asxvo'}
									<option value="{$asxvo.id}">{$asxvo.name}</option>
									{/volist}
									{/notempty}
								</select>
							</div>
						</div>
					</div>
				</div>

				<div class="tab-pane fade" id="astrology">
					<div class="row">

					</div>

					<div class="row">
						<div class="col-md-12">
							<div class="col-md-12">
								<div class="form-group">
									<label>分类</label>
									<span class="">（列表显示）</span>
									<select class="form-control" name="astrology_category_list">
										{notempty name='ac_category_list'}
										{volist name='ac_category_list' id='asxvo'}
										<option value="{$asxvo.id}">{$asxvo.name}</option>
										{/volist}
										{/notempty}
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<input type="hidden" name="id" value="{$info['id']|default='0'}" />
				{include file="layout/edit_btn_group"/}
			</div>
		</div>
	</div>
</form>


<!-- 模态框（Modal） -->
<div class="modal fade bs-example-modal-lg" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
	aria-hidden="true">
	<div class="modal-dialog modal-lg" style="margin-top: 15%;">
		<div class="modal-content">
			<div class="modal-body">
				<div class="row">
					<div class="col-md-6">
						<div class="form-group">
							<label>任务</label>
							<span class="">（抓取数据任务选择）</span>
							<select class="form-control task_id">
								{notempty name='skycaijiTaskList'}
								{volist name='skycaijiTaskList' id='vo'}
								<option value="{$vo.id}">{$vo.name}</option>
								{/volist}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
							<label>时间</label>
							<span class="">（时间，默认今天）</span>
							<input size="16" type="text" value="{:date('Y-m-d')}" readonly
								class="form_datetime_d form-control task_time">
						</div>
					</div>
				</div>

			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				<button type="button" class="btn btn-primary get_task_va">
					获取
				</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal -->
</div>

<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<script type="text/javascript">
	function show_modal() {

		$('#myModal').modal('show');
	}

	$(function() {
		var category_id = {$info['category_id']|default = 0};

		if (category_id > 0) {
			ob.setValue("category_id", category_id);
		}

		$(".form_datetime").datetimepicker({
			format: 'yyyy-mm-dd hh:ii'
		});
		$(".form_datetime_d").datetimepicker({
			minView: "month",
			format: "yyyy-mm-dd",
			autoclose: true,
			todayBtn: true
		});
		//ajax post submit请求
		$('.get_task_va').click(function() {
			var task_time = $(".task_time").val();
			var task_id = $(".task_id").val();


			$.ajax({
				url: "http://collect.xingpan.vip/data/txt/" + task_id + "/" + task_time + ".txt",
				type: 'get',
				data: "",
				async: false, //true 异步提交，false 同步提交
				cache: false,
				contentType: false,
				processData: false,
				dataType: "text",
				success: function(data) {
					console.log(data);
					KindEditor.instances[0].html(data)
				},
				error: function() {
					alert('数据为空，请检查是否抓取到');
				}
			});


		})


	
		
		var myDate = Math.round(new Date()) + 86400000;
		myDate = new Date(myDate);
		var title_year = myDate.getFullYear();
		var title_month = myDate.getMonth() + 1;
		var title_day = myDate.getDate();
		var title_r = title_year + '年' + title_month + '月' + title_day + '日，十二星座运势'
		var oldTitle = $('.name_title').val();
		console.log(title_r)
		if (!oldTitle) {
			$('.name_title').val(title_r)
			$('.seo_keywords').val(title_r)
			$('.seo_description').val(title_r)
		}


		var isChange = false
		
		
		
		$('.Areplace').click(function() {
			isChange = true
			var str = KindEditor.instances[0].html()
			console.log(str);
			// htmlspecialchars_decode()
			if (str.indexOf("</p>") != -1) {

				console.log('有P标签')

				var str = KindEditor.instances[0].html()
				//去样式
				str = str.replace(/style\s*?=\s*?(['"])[\s\S]*?\1/g, '');
				//去span
				str = str.replace(/(<\/?span.*?>)/gi, '')
				//去class
				str = str.replace(/class=[\"|'](.*?)[\"|'].*?/g, '')

				// str = str.replace(/(<\/?span.*?>)/gi,'')
				// str = str.replace(/class=[\"|'](.*?)[\"|'].*?/g, '')
				console.log(str)

				console.log(str)
				str = str.replace(/(<br[^>]*>|\s*)/g, '');
				str = str.replace(/&amp;/g, '&');
				str = str.replace(/&lt;/g, '<');
				str = str.replace(/&gt;/g, '>');
				str = str.replace(/&quot;/g, '"');
				str = str.replace(/&#039;/g, "'");
				str = str.replace(/\n/g, "");
				str = str.replace(/class="MsoNormal"/g, '');
				str = str.replace(/座的教育/g, '座教育')
				str = str.replace(/座的钱/g, '座财运')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/职业运势运势/g, '职业运势')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/座运势:每日/g, '座运势\n')
				str = str.replace(/座运势:今天/g, '座运势\n')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的星座/g, '座运势')
				str = str.replace(/座爱情情/g, '座爱情')
				str = str.replace(/天秤的爱/g, '天秤座爱情\n')
				str = str.replace(/座:每日/g, '座运势\n')
				str = str.replace(/座:今天/g, '座运势\n')
				str = str.replace(/座:今日/g, '座运势\n')
				str = str.replace(/座的事业/g, '座的事业')
				str = str.replace(/:每日/g, '\n')
				str = str.replace(/:今日/g, '\n')
				str = str.replace(/:每日星象/g, '\n')
				str = str.replace(/:今日星象/g, '\n')
				str = str.replace(/:今天/g, '\n')
				str = str.replace(/: Daily/g, '\n\1')
				str = str.replace(/: Today(.*)$/g, '\n\1')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/摩羯的事业/g, '摩羯的事业')
				str = str.replace(/天体能量/g, '天象')
				str = str.replace(/Health(.*)$/g, 'Health\n\1')
				str = str.replace(/座的星座/g, '座')
				str = str.replace(/座的健康/g, '座健康')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/<p>(&nbsp;)*<\/p>/g, "")
				str = str.replace("\\n", "\n>")
				str = str.replace('<p>(.*)座爱情</p>',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座爱情</strong></p>'
					)
				str = str.replace('<p>(.*)座职业</p>	',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座职业</strong></p>'
					)
				str = str.replace('<p>(.*)座健康</p>',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座健康</strong></p>'
					)
				str = str.replace('<p><strong class="text-dp">',
					'<p style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">'
					)
				str = str.replace('<p>白羊座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x01.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座</strong></h3>'
					)
				str = str.replace('<p>白羊座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座职业</strong></h3>'
					)
				str = str.replace('<p>白羊座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座爱情</strong></h3>'
					)
				str = str.replace('<p>白羊座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座健康</strong></h3>'
					)
				str = str.replace('<p>金牛座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x02.jpg"   class="imgb center-block" ></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座</strong></h3>'
					)
				str = str.replace('<p>金牛座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座职业</strong></h3>'
					)
				str = str.replace('<p>金牛座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座爱情</strong></h3>'
					)
				str = str.replace('<p>金牛座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座健康</strong></h3>'
					)
				str = str.replace('<p>双子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x03.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座</strong></h3>'
					)
				str = str.replace('<p>双子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座职业</strong></h3>'
					)
				str = str.replace('<p>双子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座爱情</strong></h3>'
					)
				str = str.replace('<p>双子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座健康</strong></h3>'
					)
				str = str.replace('<p>巨蟹座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x04.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座</strong></h3>'
					)
				str = str.replace('<p>巨蟹座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座职业</strong></h3>'
					)
				str = str.replace('<p>巨蟹座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座爱情</strong></h3>'
					)
				str = str.replace('<p>巨蟹座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座健康</strong></h3>'
					)
				str = str.replace('<p>狮子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x05.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座</strong></h3>'
					)
				str = str.replace('<p>狮子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座职业</strong></h3>'
					)
				str = str.replace('<p>狮子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座爱情</strong></h3>'
					)
				str = str.replace('<p>狮子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座健康</strong></h3>'
					)
				str = str.replace('<p>处女座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x06.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座</strong></h3>'
					)
				str = str.replace('<p>处女座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座职业</strong></h3>'
					)
				str = str.replace('<p>处女座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座爱情</strong></h3>'
					)
				str = str.replace('<p>处女座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座健康</strong></h3>'
					)
				str = str.replace('<p>天秤座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x07.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座</strong></h3>'
					)
				str = str.replace('<p>天秤座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座职业</strong></h3>'
					)
				str = str.replace('<p>天秤座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座爱情</strong></h3>'
					)
				str = str.replace('<p>天秤座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座健康</strong></h3>'
					)
				str = str.replace('<p>天蝎座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x08.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座</strong></h3>'
					)
				str = str.replace('<p>天蝎座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座职业</strong></h3>'
					)
				str = str.replace('<p>天蝎座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座爱情</strong></h3>'
					)
				str = str.replace('<p>天蝎座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座健康</strong></h3>'
					)
				str = str.replace('<p>射手座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x09.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座</strong></h3>'
					)
				str = str.replace('<p>射手座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座职业</strong></h3>'
					)
				str = str.replace('<p>射手座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座爱情</strong></h3>'
					)
				str = str.replace('<p>射手座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座健康</strong></h3>'
					)

				str = str.replace('<p>摩羯座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x10.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座</strong></h3>'
					)
				str = str.replace('<p>摩羯座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座职业</strong></h3>'
					)
				str = str.replace('<p>摩羯座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座爱情</strong></h3>'
					)
				str = str.replace('<p>摩羯座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座健康</strong></h3>'
					)
				str = str.replace('<p>水瓶座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x11.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座</strong></h3>'
					)
				str = str.replace('<p>水瓶座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座职业</strong></h3>'
					)
				str = str.replace('<p>水瓶座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座爱情</strong></h3>'
					)
				str = str.replace('<p>水瓶座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座健康</strong></h3>'
					)
				str = str.replace('<p>双鱼座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x12.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座</strong></h3>'
					)
				str = str.replace('<p>双鱼座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座职业</strong></h3>'
					)
				str = str.replace('<p>双鱼座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座爱情</strong></h3>'
					)
				str = str.replace('<p>双鱼座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座健康</strong></h3>'
					)
				str = str.replace('<p>每日行星概览</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				str = str.replace('<p>每日行星概述</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				
				str = str.replace('<p>白羊座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座事业</strong></h3>'
				)		
				str = str.replace('<p>金牛座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座事业</strong></h3>'
				)		
				str = str.replace('<p>双子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座事业</strong></h3>'
				)	
				str = str.replace('<p>巨蟹座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座事业</strong></h3>'
				)	
				str = str.replace('<p>狮子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座事业</strong></h3>'
				)	
				str = str.replace('<p>处女座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座事业</strong></h3>'
				)	
				str = str.replace('<p>天秤座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座事业</strong></h3>'
				)	
				str = str.replace('<p>天蝎座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座事业</strong></h3>'
				)	
				str = str.replace('<p>射手座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座事业</strong></h3>'
				)	
				str = str.replace('<p>摩羯座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座事业</strong></h3>'
				)	
				str = str.replace('<p>水瓶座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座事业</strong></h3>'
				)	
				str = str.replace('<p>双鱼座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座事业</strong></h3>'
				)	
					
					
				str = str.replace('&nbsp;', ' ')
				str = str.replace('&rsquo;', '’')
				str = str.replace('&lsquo;', '‘')
				str = str.replace('&ldquo;', '“')
				str = str.replace('&rdquo;', '”')
				str = str.replace('&mdash;', '——')
				str = str.replace('&ndash;', '–')
				str = str.replace('&hellip;', '...')
				str = str.replace(/Weekly Education and Knowledge Horoscope/g, 'Education')
				str = str.replace(/Weekly Money and Finances Horoscope/g, 'Money')
				str = str.replace(/Weekly Love and Relationships Horoscope/g, 'Love')
				str = str.replace(/Weekly Health and Well Being Horoscope/g, 'Health')
				str = str.replace(/Weekly Career and Business Horoscope/g, 'Career')

				str = str.replace(
					'Weekly Horoscope.For a more in depth and personalised astrological analysis, check your Daily Natal HoroscopeAries daily horoscopeAries monthly horoscopeAries yearly horoscopeSee also:Today’s horoscopeTomorrow&#39;s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ,',
					'')
				str = str.replace(
					/Get now more details about the impact of the planets this week, based on your ➳	/g,
					'')
				str = str.replace(/Health &amp; Wellness/g, 'Health ')
				str = str.replace('\\n\\n\\n', '')
				str = str.replace(
					' | Horoscope.com\tYesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n \\n\\n	',
					'')
				str = str.replace(': Daily & Today | Horoscope.com', '')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n',
					'')
				str = str.replace('Overview\\nLove\\nCareer\\nMoney\\nHealth\\n\\n', '')
				str = str.replace(' | Horoscope.com', '')
				str = str.replace('&amp; Wellness Horoscope', '')
				str = str.replace('Career Horoscope', 'Career\n')
				str = str.replace('Love Horoscope', 'Love\n')
				str = str.replace('Horoscope\t ', 'Horoscope\t \n\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n\\n...\\n\\nWeekly\\nMonthly\\n2020\\n \\n \\n\\n',
					' \n\n')
				str = str.replace('\\n', '')
				str = str.replace('\\n', '')
				str = str.replace('Health\t', 'Health\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n 2020\\n\\n \\n \\n\\n',
					'')
				str = str.replace('\\n', '座财运')
				str = str.replace('http：//', 'http://')

				str = str.replace('<p>(.*)座</p>',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">\1座</strong></p>'
					)

				str = str.replace(' \t', ' ')

				str = str.replace('<p><strong class="text-dp">',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">'
					)
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/双子星座/g, '双子座')
				str = str.replace(/正方形/g, '刑相位')
				str = str.replace(/四分之一/g, '刑相位')
				str = str.replace(/座运势/g, '运势')
				str = str.replace(/Horoscope/g, ' ')
				str = str.replace(/双子的爱/g, '双子座爱情')
				str = str.replace('<p>爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p>职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p>健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p>财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p>学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)

				str = str.replace('<p class="MsoNormal">爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)

				str = str.replace(/wed./g, 'wednesday')
				str = str.replace(/六分之一/g, '六合')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/间隔/g, '时间段')
				str = str.replace(/维纳斯/g, '金星')
				str = str.replace(/地球星座/g, '土象星座')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/迹象/g, '星座')
				str = str.replace(/冲相位/g, '')
				str = str.replace(/主宰/g, '刑相位')
				str = str.replace(/马尔斯/g, '火星')
				str = str.replace(/六分之一/g, '六分相')
				str = str.replace(/朱庇特/g, '木星')
				str = str.replace(/基督教占星术/g, '基督占星')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/固定标志/g, '固定星座')
				str = str.replace(/基本标志/g, '本位星座')
				str = str.replace(/易变标志/g, '变动星座')
				str = str.replace(/放置/g, '落入')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/常见的星座/g, '变动星座')
				str = str.replace(/主要星座/g, '本位星座')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/可变的星座/g, '变动星座')
				str = str.replace(/可变星座/g, '变动星座')
				str = str.replace(/继承房屋/g, '续宫')
				str = str.replace(/节奏的房屋/g, '果宫')
				str = str.replace(/南部节点/g, '南交点')
				str = str.replace(/海图/g, '星盘')
				str = str.replace(/平方/g, '刑相位')
				str = str.replace(/节点/g, '月交点')
				str = str.replace(/直接移动/g, '顺行')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/正方形/g, '刑相位')
				str = str.replace(/人马/g, '射手')
				str = str.replace(/反中情局/g, '映点')
				str = str.replace(/尖端/g, '宫头')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/当地人/g, '盘主')
				str = str.replace(/流动的/g, '良好的')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/南节点/g, '南交点')
				str = str.replace(/空气/g, '风')
				str = str.replace(/三倍尺/g, '三分主宰星')
				str = str.replace(/地球/g, '土')
				str = str.replace(/墨丘利/g, '水星')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/部门/g, '领域')
				str = str.replace(/运输/g, '行运')
				str = str.replace(/对齐/g, '准确成相位')
				str = str.replace(/对准/g, '准确成相位')
				str = str.replace(/过境/g, '流年')
				str = str.replace(/处理死记硬背/g, '例行公事')
				str = str.replace(/过渡/g, '行运')
				str = str.replace(/凌日/g, '行运')
				str = str.replace(/滑进/g, '进入')
				str = str.replace(/船员/g, '职员')
				str = str.replace(/螃蟹/g, '巨蟹座')
				str = str.replace(/.*座偶像/g, '')
				str = str.replace(/.*图标/g, ' ')
				str = str.replace(/地区/g, '领域')
				str = str.replace(/间歇/g, '一段时间')
				str = str.replace(/生长/g, '良好发展')
				str = str.replace(/口音/g, '重点')
				str = str.replace(/奇龙/g, '凯龙')
				str = str.replace(/希拉龙/g, '凯龙')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/希龙/g, '凯龙')
				str = str.replace(/出生表/g, '出生星盘')
				str = str.replace(/火灾星座/g, '火象星座')
				str = str.replace(/空中星座/g, '风象星座')
				str = str.replace(/主宰者/g, '主宰星')
				str = str.replace(/尖顶/g, '宫头')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/圆润/g, '开心')
				str = str.replace(/行星方面/g, '行星相位')
				str = str.replace(/晚餐/g, '晚上')
				str = str.replace(/会引导你/g, '你会')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/纳塔尔/g, '本命')
				str = str.replace(/乌拉纳斯/g, '天王星')
				str = str.replace(/娜塔玛斯/g, '火星')
				str = str.replace(/星体配置/g, '星象')
				str = str.replace(/座星座/g, '座')
				str = str.replace(/天空中行星的运动/g, '天象')
				str = str.replace(/行星的运动/g, '天象')
				str = str.replace(/交通/g, '天象')
				str = str.replace(/天体的位置/g, '天象')
				str = str.replace(/倒退/g, '逆行')
				str = str.replace(/水银/g, '水星')
				str = str.replace(/后旋/g, '逆行')
				str = str.replace(/下旋/g, '逆行')
				str = str.replace(/三分之一/g, '三合')
				str = str.replace(/出生星图/g, '出生星盘')
				str = str.replace(/新月亮/g, '新月亮')
				str = str.replace(/固定星/g, '恒星')
				str = str.replace(/逆行站/g, '逆行停滞')
				str = str.replace(/倒退/g, '倒退')
				str = str.replace(/水星站/g, '水星停滞')
				str = str.replace(/方形/g, '刑相位')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/六分符/g, '六分相位')
				str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/Get an Accurate Tarot Reading/g, '')
				str = str.replace(/Find out with a Tarot Reading/g, '')
				str = str.replace(
					/You are right where you belong with a psychic love reading.  Chat with a psychic for free!/g,
					'')
				str = str.replace(/Chat with a psychic now for instant answers.Read More Horoscopes:/g, '')
				str = str.replace(/Daily\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Singles Love\n/g, '')
				str = str.replace(/Couples Love\n/g, '')
				str = str.replace(/Work\n/g, '')
				str = str.replace(/Romantic\n/g, '')
				str = str.replace(/SexScope\n/g, '')
				str = str.replace(/Business\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Chinese\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Tarot\n/g, '')
				str = str.replace(/Psychics/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Still confused about your relationship？/g, '')
				str = str.replace(/You are right where you belong with a psychic love reading./g,
					' Chat with a psychic now for instant answers.')
				str = str.replace(/Is your relationship worth fighting for？/g,
					' Find out with a Free Tarot Reading.')
				str = str.replace(
					/Take this relationship questionnaire and find local compatible singles!/g, '')
				str = str.replace(
					/Try the new eharmony compatibility quiz and get matched with local singles today!/g,
					'')
				str = str.replace('\n\n\n\n \n\n\n\n\n\n\n\n\n', '')
				str = str.replace('\n\n\n\n\n\n', '')

				str = str.replace(' Your Love', ' \nYour Love')
				str = str.replace('Your Career\n', '\nYour Career\n')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/朱诺/g, '婚神星')
				str = str.replace(
					/For a more in depth and personalised astrological analysis， check your Daily Natal.Taurus daily horoscopeTaurus monthly horoscopeTaurus yearly horoscopeSee also:Today’s horoscopeTomorrow&#39；s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(
					/For a more in depth and personalised .* CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(/Weekly./g, '')
				str = str.replace(/后代/g, '下降')
				str = str.replace(/主星座/g, '本位星座')
				str = str.replace(/基本星座/g, '本位星座')
				str = str.replace(/恒星座/g, '固定星座')
				str = str.replace(/星球/g, '行星')
				str = str.replace(/图表/g, '星盘')
				str = str.replace(/图轮/g, '星盘')
				str = str.replace(/轮子/g, '星盘')
				str = str.replace(/房子/g, '宫位')
				str = str.replace(/星体能量/g, '星象')
				str = str.replace(/行星流年/g, '星象')
				str = str.replace(/行星的大气层/g, '星象')
				// str = str.replace(/http：///g,'http://')
				str = str.replace(/----------------------------------	星象/g, '')
				str = str.replace(/可变/g, '变动')
				str = str.replace(/火相/g, '火象')
				str = str.replace(/方位角/g, '相位')
				str = str.replace(/成方/g, '成刑相位')
				str = str.replace(/对位/g, '冲相位')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/对话/g, '商谈')
				str = str.replace(/尝试/g, '试着')
				str = str.replace(/将带给您/g, '会有')
				str = str.replace(/将与带给您/g, '会有')
				str = str.replace(/意外/g, '临时')
				str = str.replace(/广播/g, '传播')
				str = str.replace(/不可否认/g, '难得')
				str = str.replace(/於/g, '于')
				str = str.replace(/後/g, '后')
				str = str.replace(/後/g, '后')
				str = str.replace(/舆/g, '与')
				str = str.replace(/太阳回归图/g, '太阳返照星盘')
				str = str.replace(/麽/g, '么')
				str = str.replace(/蠍/g, '蝎')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/土著人/g, '盘主')
				str = str.replace(/土著/g, '盘主')
				str = str.replace(/月亮大厦/g, '月宿')
				str = str.replace(/纳夏特拉/g, '月宿')
				str = str.replace(/兼容性/g, '感情匹配度')
				str = str.replace(/选举占星术/g, '择日占星术')
				str = str.replace(/原住民/g, '盘主')
				str = str.replace(/次级进展/g, '次限')
				str = str.replace(/太阳弧方向/g, '太阳弧')
				str = str.replace(/太阳返回图/g, '太阳返照')
				str = str.replace(/相容性/g, '匹配度')
				str = str.replace(/本生/g, '本命')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/合成的/g, '组合盘')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/复合星盘/g, '组合盘')

				str = str.replace('标题1', '<p class="img_box"> <img src="http://ke.12sign.top/img/01h.png"> </p>')
				str = str.replace('标题2', '<p class="img_box"> <img src="http://ke.12sign.top/img/02h.png"> </p>')
				str = str.replace('标题3', '<p class="img_box"> <img src="http://ke.12sign.top/img/03h.png"> </p>')
				str = str.replace('标题4', '<p class="img_box"> <img src="http://ke.12sign.top/img/04h.png"> </p>')
				str = str.replace('标题5', '<p class="img_box"> <img src="http://ke.12sign.top/img/05h.png"> </p>')
				str = str.replace('标题6', '<p class="img_box"> <img src="http://ke.12sign.top/img/06h.png"> </p>')
				str = str.replace('标题7', '<p class="img_box"> <img src="http://ke.12sign.top/img/07h.png"> </p>')
				str = str.replace('标题8', '<p class="img_box"> <img src="http://ke.12sign.top/img/08h.png"> </p>')
				str = str.replace('标题9', '<p class="img_box"> <img src="http://ke.12sign.top/img/09h.png"> </p>')
				str = str.replace('标题10', '<p class="img_box"> <img src="http://ke.12sign.top/img/10h.png"> </p>')

				str = str.replace('Four Quarters （Padas）', '四部分')
				str = str.replace(/人马/g, '射手座')
				str = str.replace(/星象图/g, '星盘')
				str = str.replace(/出生图/g, '星盘')

				str = str.replace(/房屋/g, '宫位')
				str = str.replace(/外观方面/g, '相位')
				str = str.replace(/Sun Sign/g, '太阳星座')
				str = str.replace(/集成电路/g, '天底')
				str = str.replace(/后裔/g, '下降点')
				str = str.replace(/对立/g, '冲相位')
				str = str.replace(/相反/g, '冲相位')
				str = str.replace(/反对/g, '冲相位')
				str = str.replace(/对面/g, '冲相位')
				str = str.replace(/相对的/g, '冲相位')

				str = str.replace(/直接的/g, '顺行的')
				str = str.replace(/相对/g, '冲相位')
				str = str.replace(/结合/g, '合相')
				str = str.replace(/巨蟹座：/g, '巨蟹座\n')
				str = str.replace(/狮子：/g, '狮子座\n')
				str = str.replace(/处女座：/g, '处女座\n')
				str = str.replace(/天秤：/g, '天秤座\n')
				str = str.replace(/摩羯座：/g, '摩羯座\n')
				str = str.replace(/双鱼座：/g, '双鱼座\n')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/行星的排列/g, '天象')
				str = str.replace(/行星配置/g, '天象')
				str = str.replace('â€™', "'")
				str = str.replace('â€”', '-')
				str = str.replace('â€“', '-')
				str = str.replace('â€œ', "'")
				str = str.replace('â€', "'")
				// str = str.replace(';','；')
				// str = str.replace(',','，')
				// str = str.replace(',','，')
				// str = str.replace('?','？')
				str = str.replace(/Daily/g, '')
				// str = str.replace('!','！')
				// str = str.replace(')','）')
				// str = str.replace('(','（')
				str = "<div class='blog_box' style='box-sizing: border-box;'>" + str +'</div>'
			} else {
				
				str = '<p>' + str
				str = str.replace(/<br\s*\/?>/gi, "</p><p>")
				console.log('No')
				str = str.replace(/(<br[^>]*>|\s*)/g, '')
				console.log(str)
				str = str.replace(/&amp;/g, '&');
				str = str.replace(/&lt;/g, '<');
				str = str.replace(/&gt;/g, '>');
				str = str.replace(/&quot;/g, '"');
				str = str.replace(/&#039;/g, "'");
				str = str.replace(/\n/g, "");
				str = str.replace(/class="MsoNormal"/g, '');
				str = str.replace(/座的教育/g, '座教育')
				str = str.replace(/座的钱/g, '座财运')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/职业运势运势/g, '职业运势')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/座运势:每日/g, '座运势\n')
				str = str.replace(/座运势:今天/g, '座运势\n')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的星座/g, '座运势')
				str = str.replace(/座爱情情/g, '座爱情')
				str = str.replace(/天秤的爱/g, '天秤座爱情\n')
				str = str.replace(/座:每日/g, '座运势\n')
				str = str.replace(/座:今天/g, '座运势\n')
				str = str.replace(/座:今日/g, '座运势\n')
				str = str.replace(/座的事业/g, '座的事业')
				str = str.replace(/:每日/g, '\n')
				str = str.replace(/:今日/g, '\n')
				str = str.replace(/:每日星象/g, '\n')
				str = str.replace(/:今日星象/g, '\n')
				str = str.replace(/:今天/g, '\n')
				str = str.replace(/: Daily/g, '\n\1')
				str = str.replace(/: Today(.*)$/g, '\n\1')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/摩羯的事业/g, '摩羯的事业')
				str = str.replace(/天体能量/g, '天象')
				str = str.replace(/Health(.*)$/g, 'Health\n\1')
				str = str.replace(/座的星座/g, '座')
				str = str.replace(/座的健康/g, '座健康')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/<p>(&nbsp;)*<\/p>/g, "")
				str = str.replace("\\n", "\n>")
				str = str.replace('<p>(.*)座爱情</p>',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座爱情</strong></p>'
					)
				str = str.replace('<p>(.*)座职业</p>	',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座职业</strong></p>'
					)
				str = str.replace('<p>(.*)座健康</p>',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座健康</strong></p>'
					)
				str = str.replace('<p><strong class="text-dp">',
					'<p style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">'
					)
				str = str.replace('<p>白羊座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x01.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座</strong></h3>'
					)
				str = str.replace('<p>白羊座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座职业</strong></h3>'
					)
					
				str = str.replace('<p>白羊座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座爱情</strong></h3>'
					)
				str = str.replace('<p>白羊座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座健康</strong></h3>'
					)
				str = str.replace('<p>金牛座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x02.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座</strong></h3>'
					)
				str = str.replace('<p>金牛座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座职业</strong></h3>'
					)
				str = str.replace('<p>金牛座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座爱情</strong></h3>'
					)
				str = str.replace('<p>金牛座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座健康</strong></h3>'
					)
				str = str.replace('<p>双子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x03.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座</strong></h3>'
					)
				str = str.replace('<p>双子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座职业</strong></h3>'
					)
				str = str.replace('<p>双子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座爱情</strong></h3>'
					)
				str = str.replace('<p>双子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座健康</strong></h3>'
					)
				str = str.replace('<p>巨蟹座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x04.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座</strong></h3>'
					)
				str = str.replace('<p>巨蟹座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座职业</strong></h3>'
					)
				str = str.replace('<p>巨蟹座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座爱情</strong></h3>'
					)
				str = str.replace('<p>巨蟹座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座健康</strong></h3>'
					)
				str = str.replace('<p>狮子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x05.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座</strong></h3>'
					)
				str = str.replace('<p>狮子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座职业</strong></h3>'
					)
				str = str.replace('<p>狮子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座爱情</strong></h3>'
					)
				str = str.replace('<p>狮子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座健康</strong></h3>'
					)
				str = str.replace('<p>处女座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x06.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座</strong></h3>'
					)
				str = str.replace('<p>处女座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座职业</strong></h3>'
					)
				str = str.replace('<p>处女座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座爱情</strong></h3>'
					)
				str = str.replace('<p>处女座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座健康</strong></h3>'
					)
				str = str.replace('<p>天秤座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x07.jpg"   class="imgb center-block" ></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座</strong></h3>'
					)
				str = str.replace('<p>天秤座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座职业</strong></h3>'
					)
				str = str.replace('<p>天秤座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座爱情</strong></h3>'
					)
				str = str.replace('<p>天秤座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座健康</strong></h3>'
					)
				str = str.replace('<p>天蝎座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x08.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座</strong></h3>'
					)
				str = str.replace('<p>天蝎座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座职业</strong></h3>'
					)
				str = str.replace('<p>天蝎座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座爱情</strong></h3>'
					)
				str = str.replace('<p>天蝎座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座健康</strong></h3>'
					)
				str = str.replace('<p>射手座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x09.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座</strong></h3>'
					)
				str = str.replace('<p>射手座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座职业</strong></h3>'
					)
				str = str.replace('<p>射手座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座爱情</strong></h3>'
					)
				str = str.replace('<p>射手座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座健康</strong></h3>'
					)
				
				str = str.replace('<p>摩羯座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x10.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座</strong></h3>'
					)
				str = str.replace('<p>摩羯座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座职业</strong></h3>'
					)
				str = str.replace('<p>摩羯座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座爱情</strong></h3>'
					)
				str = str.replace('<p>摩羯座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座健康</strong></h3>'
					)
				str = str.replace('<p>水瓶座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x11.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座</strong></h3>'
					)
				str = str.replace('<p>水瓶座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座职业</strong></h3>'
					)
				str = str.replace('<p>水瓶座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座爱情</strong></h3>'
					)
				str = str.replace('<p>水瓶座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座健康</strong></h3>'
					)
				str = str.replace('<p>双鱼座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x12.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座</strong></h3>'
					)
				str = str.replace('<p>双鱼座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座职业</strong></h3>'
					)
				str = str.replace('<p>双鱼座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座爱情</strong></h3>'
					)
				str = str.replace('<p>双鱼座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座健康</strong></h3>'
					)
				str = str.replace('<p>白羊座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座职业</strong></h3>'
				)	
					
				str = str.replace('<p>白羊座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座事业</strong></h3>'
				)		
				str = str.replace('<p>金牛座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座事业</strong></h3>'
				)		
				str = str.replace('<p>双子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座事业</strong></h3>'
				)	
				str = str.replace('<p>巨蟹座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座事业</strong></h3>'
				)	
				str = str.replace('<p>狮子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座事业</strong></h3>'
				)	
				str = str.replace('<p>处女座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座事业</strong></h3>'
				)	
				str = str.replace('<p>天秤座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座事业</strong></h3>'
				)	
				str = str.replace('<p>天蝎座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座事业</strong></h3>'
				)	
				str = str.replace('<p>射手座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座事业</strong></h3>'
				)	
				str = str.replace('<p>摩羯座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座事业</strong></h3>'
				)	
				str = str.replace('<p>水瓶座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座事业</strong></h3>'
				)	
				str = str.replace('<p>双鱼座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座事业</strong></h3>'
				)	
					
					
					
					
					
					
				str = str.replace('<p>每日行星概览</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				str = str.replace('<p>每日行星概述</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				str = str.replace('&nbsp;', ' ')
				str = str.replace('&rsquo;', '’')
				str = str.replace('&lsquo;', '‘')
				str = str.replace('&ldquo;', '“')
				str = str.replace('&rdquo;', '”')
				str = str.replace('&mdash;', '——')
				str = str.replace('&ndash;', '–')
				str = str.replace('&hellip;', '...')
				str = str.replace(/Weekly Education and Knowledge Horoscope/g, 'Education')
				str = str.replace(/Weekly Money and Finances Horoscope/g, 'Money')
				str = str.replace(/Weekly Love and Relationships Horoscope/g, 'Love')
				str = str.replace(/Weekly Health and Well Being Horoscope/g, 'Health')
				str = str.replace(/Weekly Career and Business Horoscope/g, 'Career')

				str = str.replace(
					'Weekly Horoscope.For a more in depth and personalised astrological analysis, check your Daily Natal HoroscopeAries daily horoscopeAries monthly horoscopeAries yearly horoscopeSee also:Today’s horoscopeTomorrow&#39;s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ,',
					'')
				str = str.replace(
					/Get now more details about the impact of the planets this week, based on your ➳	/g,
					'')
				str = str.replace(/Health &amp; Wellness/g, 'Health ')
				str = str.replace('\\n\\n\\n', '')
				str = str.replace(
					' | Horoscope.com\tYesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n \\n\\n	',
					'')
				str = str.replace(': Daily & Today | Horoscope.com', '')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n',
					'')
				str = str.replace('Overview\\nLove\\nCareer\\nMoney\\nHealth\\n\\n', '')
				str = str.replace(' | Horoscope.com', '')
				str = str.replace('&amp; Wellness Horoscope', '')
				str = str.replace('Career Horoscope', 'Career\n')
				str = str.replace('Love Horoscope', 'Love\n')
				str = str.replace('Horoscope\t ', 'Horoscope\t \n\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n\\n...\\n\\nWeekly\\nMonthly\\n2020\\n \\n \\n\\n',
					' \n\n')
				str = str.replace('\\n', '')
				str = str.replace('\\n', '')
				str = str.replace('Health\t', 'Health\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n 2020\\n\\n \\n \\n\\n',
					'')
				str = str.replace('\\n', '座财运')
				str = str.replace('http：//', 'http://')

				str = str.replace('<p>(.*)座</p>',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">\1座</strong></p>'
					)

				str = str.replace(' \t', ' ')

				str = str.replace('<p><strong class="text-dp">',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">'
					)
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/双子星座/g, '双子座')
				str = str.replace(/正方形/g, '刑相位')
				str = str.replace(/四分之一/g, '刑相位')
				str = str.replace(/座运势/g, '运势')
				str = str.replace(/Horoscope/g, ' ')
				str = str.replace(/双子的爱/g, '双子座爱情')
				str = str.replace('<p>爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p>职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p>健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p>财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p>学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)

				str = str.replace('<p class="MsoNormal">爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)

				str = str.replace(/wed./g, 'wednesday')
				str = str.replace(/六分之一/g, '六合')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/间隔/g, '时间段')
				str = str.replace(/维纳斯/g, '金星')
				str = str.replace(/地球星座/g, '土象星座')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/迹象/g, '星座')
				str = str.replace(/冲相位/g, '')
				str = str.replace(/主宰/g, '刑相位')
				str = str.replace(/马尔斯/g, '火星')
				str = str.replace(/六分之一/g, '六分相')
				str = str.replace(/朱庇特/g, '木星')
				str = str.replace(/基督教占星术/g, '基督占星')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/固定标志/g, '固定星座')
				str = str.replace(/基本标志/g, '本位星座')
				str = str.replace(/易变标志/g, '变动星座')
				str = str.replace(/放置/g, '落入')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/常见的星座/g, '变动星座')
				str = str.replace(/主要星座/g, '本位星座')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/可变的星座/g, '变动星座')
				str = str.replace(/可变星座/g, '变动星座')
				str = str.replace(/继承房屋/g, '续宫')
				str = str.replace(/节奏的房屋/g, '果宫')
				str = str.replace(/南部节点/g, '南交点')
				str = str.replace(/海图/g, '星盘')
				str = str.replace(/平方/g, '刑相位')
				str = str.replace(/节点/g, '月交点')
				str = str.replace(/直接移动/g, '顺行')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/正方形/g, '刑相位')
				str = str.replace(/人马/g, '射手')
				str = str.replace(/反中情局/g, '映点')
				str = str.replace(/尖端/g, '宫头')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/当地人/g, '盘主')
				str = str.replace(/流动的/g, '良好的')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/南节点/g, '南交点')
				str = str.replace(/空气/g, '风')
				str = str.replace(/三倍尺/g, '三分主宰星')
				str = str.replace(/地球/g, '土')
				str = str.replace(/墨丘利/g, '水星')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/部门/g, '领域')
				str = str.replace(/运输/g, '行运')
				str = str.replace(/对齐/g, '准确成相位')
				str = str.replace(/对准/g, '准确成相位')
				str = str.replace(/过境/g, '流年')
				str = str.replace(/处理死记硬背/g, '例行公事')
				str = str.replace(/过渡/g, '行运')
				str = str.replace(/凌日/g, '行运')
				str = str.replace(/滑进/g, '进入')
				str = str.replace(/船员/g, '职员')
				str = str.replace(/螃蟹/g, '巨蟹座')
				str = str.replace(/.*座偶像/g, '')
				str = str.replace(/.*图标/g, ' ')
				str = str.replace(/地区/g, '领域')
				str = str.replace(/间歇/g, '一段时间')
				str = str.replace(/生长/g, '良好发展')
				str = str.replace(/口音/g, '重点')
				str = str.replace(/奇龙/g, '凯龙')
				str = str.replace(/希拉龙/g, '凯龙')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/希龙/g, '凯龙')
				str = str.replace(/出生表/g, '出生星盘')
				str = str.replace(/火灾星座/g, '火象星座')
				str = str.replace(/空中星座/g, '风象星座')
				str = str.replace(/主宰者/g, '主宰星')
				str = str.replace(/尖顶/g, '宫头')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/圆润/g, '开心')
				str = str.replace(/行星方面/g, '行星相位')
				str = str.replace(/晚餐/g, '晚上')
				str = str.replace(/会引导你/g, '你会')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/纳塔尔/g, '本命')
				str = str.replace(/乌拉纳斯/g, '天王星')
				str = str.replace(/娜塔玛斯/g, '火星')
				str = str.replace(/星体配置/g, '星象')
				str = str.replace(/座星座/g, '座')
				str = str.replace(/天空中行星的运动/g, '天象')
				str = str.replace(/行星的运动/g, '天象')
				str = str.replace(/交通/g, '天象')
				str = str.replace(/天体的位置/g, '天象')
				str = str.replace(/倒退/g, '逆行')
				str = str.replace(/水银/g, '水星')
				str = str.replace(/后旋/g, '逆行')
				str = str.replace(/下旋/g, '逆行')
				str = str.replace(/三分之一/g, '三合')
				str = str.replace(/出生星图/g, '出生星盘')
				str = str.replace(/新月亮/g, '新月亮')
				str = str.replace(/固定星/g, '恒星')
				str = str.replace(/逆行站/g, '逆行停滞')
				str = str.replace(/倒退/g, '倒退')
				str = str.replace(/水星站/g, '水星停滞')
				str = str.replace(/方形/g, '刑相位')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/六分符/g, '六分相位')
				str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/Get an Accurate Tarot Reading/g, '')
				str = str.replace(/Find out with a Tarot Reading/g, '')
				str = str.replace(
					/You are right where you belong with a psychic love reading.  Chat with a psychic for free!/g,
					'')
				str = str.replace(/Chat with a psychic now for instant answers.Read More Horoscopes:/g, '')
				str = str.replace(/Daily\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Singles Love\n/g, '')
				str = str.replace(/Couples Love\n/g, '')
				str = str.replace(/Work\n/g, '')
				str = str.replace(/Romantic\n/g, '')
				str = str.replace(/SexScope\n/g, '')
				str = str.replace(/Business\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Chinese\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Tarot\n/g, '')
				str = str.replace(/Psychics/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Still confused about your relationship？/g, '')
				str = str.replace(/You are right where you belong with a psychic love reading./g,
					' Chat with a psychic now for instant answers.')
				str = str.replace(/Is your relationship worth fighting for？/g,
					' Find out with a Free Tarot Reading.')
				str = str.replace(
					/Take this relationship questionnaire and find local compatible singles!/g, '')
				str = str.replace(
					/Try the new eharmony compatibility quiz and get matched with local singles today!/g,
					'')
				str = str.replace('\n\n\n\n \n\n\n\n\n\n\n\n\n', '')
				str = str.replace('\n\n\n\n\n\n', '')

				str = str.replace(' Your Love', ' \nYour Love')
				str = str.replace('Your Career\n', '\nYour Career\n')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/朱诺/g, '婚神星')
				str = str.replace(
					/For a more in depth and personalised astrological analysis， check your Daily Natal.Taurus daily horoscopeTaurus monthly horoscopeTaurus yearly horoscopeSee also:Today’s horoscopeTomorrow&#39；s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(
					/For a more in depth and personalised .* CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(/Weekly./g, '')
				str = str.replace(/后代/g, '下降')
				str = str.replace(/主星座/g, '本位星座')
				str = str.replace(/基本星座/g, '本位星座')
				str = str.replace(/恒星座/g, '固定星座')
				str = str.replace(/星球/g, '行星')
				str = str.replace(/图表/g, '星盘')
				str = str.replace(/图轮/g, '星盘')
				str = str.replace(/轮子/g, '星盘')
				str = str.replace(/房子/g, '宫位')
				str = str.replace(/星体能量/g, '星象')
				str = str.replace(/行星流年/g, '星象')
				str = str.replace(/行星的大气层/g, '星象')
				// str = str.replace(/http：///g,'http://')
				str = str.replace(/----------------------------------	星象/g, '')
				str = str.replace(/可变/g, '变动')
				str = str.replace(/火相/g, '火象')
				str = str.replace(/方位角/g, '相位')
				str = str.replace(/成方/g, '成刑相位')
				str = str.replace(/对位/g, '冲相位')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/对话/g, '商谈')
				str = str.replace(/尝试/g, '试着')
				str = str.replace(/将带给您/g, '会有')
				str = str.replace(/将与带给您/g, '会有')
				str = str.replace(/意外/g, '临时')
				str = str.replace(/广播/g, '传播')
				str = str.replace(/不可否认/g, '难得')
				str = str.replace(/於/g, '于')
				str = str.replace(/後/g, '后')
				str = str.replace(/後/g, '后')
				str = str.replace(/舆/g, '与')
				str = str.replace(/太阳回归图/g, '太阳返照星盘')
				str = str.replace(/麽/g, '么')
				str = str.replace(/蠍/g, '蝎')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/土著人/g, '盘主')
				str = str.replace(/土著/g, '盘主')
				str = str.replace(/月亮大厦/g, '月宿')
				str = str.replace(/纳夏特拉/g, '月宿')
				str = str.replace(/兼容性/g, '感情匹配度')
				str = str.replace(/选举占星术/g, '择日占星术')
				str = str.replace(/原住民/g, '盘主')
				str = str.replace(/次级进展/g, '次限')
				str = str.replace(/太阳弧方向/g, '太阳弧')
				str = str.replace(/太阳返回图/g, '太阳返照')
				str = str.replace(/相容性/g, '匹配度')
				str = str.replace(/本生/g, '本命')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/合成的/g, '组合盘')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/复合星盘/g, '组合盘')

				str = str.replace('标题1', '<p class="img_box"> <img src="http://ke.12sign.top/img/01h.png"></p>')
				str = str.replace('标题2', '<p class="img_box"> <img src="http://ke.12sign.top/img/02h.png"></p>')
				str = str.replace('标题3', '<p class="img_box"> <img src="http://ke.12sign.top/img/03h.png"></p>')
				str = str.replace('标题4', '<p class="img_box"> <img src="http://ke.12sign.top/img/04h.png"></p>')
				str = str.replace('标题5', '<p class="img_box"> <img src="http://ke.12sign.top/img/05h.png"></p>')
				str = str.replace('标题6', '<p class="img_box"> <img src="http://ke.12sign.top/img/06h.png"></p>')
				str = str.replace('标题7', '<p class="img_box"> <img src="http://ke.12sign.top/img/07h.png"></p>')
				str = str.replace('标题8', '<p class="img_box"> <img src="http://ke.12sign.top/img/08h.png"></p>')
				str = str.replace('标题9', '<p class="img_box"> <img src="http://ke.12sign.top/img/09h.png"></p>')
				str = str.replace('标题10', '<p class="img_box"> <img src="http://ke.12sign.top/img/10h.png"></p>')

				str = str.replace('Four Quarters （Padas）', '四部分')
				str = str.replace(/人马/g, '射手座')
				str = str.replace(/星象图/g, '星盘')
				str = str.replace(/出生图/g, '星盘')

				str = str.replace(/房屋/g, '宫位')
				str = str.replace(/外观方面/g, '相位')
				str = str.replace(/Sun Sign/g, '太阳星座')
				str = str.replace(/集成电路/g, '天底')
				str = str.replace(/后裔/g, '下降点')
				str = str.replace(/对立/g, '冲相位')
				str = str.replace(/相反/g, '冲相位')
				str = str.replace(/反对/g, '冲相位')
				str = str.replace(/对面/g, '冲相位')
				str = str.replace(/相对的/g, '冲相位')

				str = str.replace(/直接的/g, '顺行的')
				str = str.replace(/相对/g, '冲相位')
				str = str.replace(/结合/g, '合相')
				str = str.replace(/巨蟹座：/g, '巨蟹座\n')
				str = str.replace(/狮子：/g, '狮子座\n')
				str = str.replace(/处女座：/g, '处女座\n')
				str = str.replace(/天秤：/g, '天秤座\n')
				str = str.replace(/摩羯座：/g, '摩羯座\n')
				str = str.replace(/双鱼座：/g, '双鱼座\n')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/行星的排列/g, '天象')
				str = str.replace(/行星配置/g, '天象')
				str = str.replace('â€™', "'")
				str = str.replace('â€”', '-')
				str = str.replace('â€“', '-')
				str = str.replace('â€œ', "'")
				str = str.replace('â€', "'")
				// str = str.replace(';','；')
				// str = str.replace(',','，')
				// str = str.replace(',','，')
				// str = str.replace('?','？')
				str = str.replace(/Daily/g, '')
				// str = str.replace('!','！')
				// str = str.replace(')','）')
				// str = str.replace('(','（')
					str = "<div class='blog_box' style='box-sizing: border-box;'>" + str +'</div>'

			}
			console.log(str)
			KindEditor.instances[0].html(str)
			$('#ke').val(str)
		});
		
		$('.Areplace1').click(function() {
			isChange = true
			var str = KindEditor.instances[0].html()
			console.log(str);
			// htmlspecialchars_decode()
			if (str.indexOf("</p>") != -1) {
		
				console.log('有P标签')
		
				var str = KindEditor.instances[0].html()
				//去样式
				str = str.replace(/style\s*?=\s*?(['"])[\s\S]*?\1/g, '');
				//去span
				str = str.replace(/(<\/?span.*?>)/gi, '')
				//去class
				str = str.replace(/class=[\"|'](.*?)[\"|'].*?/g, '')
		
				// str = str.replace(/(<\/?span.*?>)/gi,'')
				// str = str.replace(/class=[\"|'](.*?)[\"|'].*?/g, '')
				console.log(str)
		
				console.log(str)
				str = str.replace(/(<br[^>]*>|\s*)/g, '');
				str = str.replace(/&amp;/g, '&');
				str = str.replace(/&lt;/g, '<');
				str = str.replace(/&gt;/g, '>');
				str = str.replace(/&quot;/g, '"');
				str = str.replace(/&#039;/g, "'");
				str = str.replace(/\n/g, "");
				str = str.replace(/class="MsoNormal"/g, '');
				// str = str.replace(/座的教育/g, '座教育')
				// str = str.replace(/座的钱/g, '座财运')
				// str = str.replace(/每周职业和商业星座/g, '职业运势')
				// str = str.replace(/每周职业和商业星座/g, '职业运势')
				// str = str.replace(/职业运势运势/g, '职业运势')
				// str = str.replace(/癌症/g, '巨蟹座')
				// str = str.replace(/座运势:每日/g, '座运势\n')
				// str = str.replace(/座运势:今天/g, '座运势\n')
				// str = str.replace(/座的职业/g, '座职业')
				// str = str.replace(/座的星座/g, '座运势')
				// str = str.replace(/座爱情情/g, '座爱情')
				// str = str.replace(/天秤的爱/g, '天秤座爱情\n')
				// str = str.replace(/座:每日/g, '座运势\n')
				// str = str.replace(/座:今天/g, '座运势\n')
				// str = str.replace(/座:今日/g, '座运势\n')
				// str = str.replace(/座的事业/g, '座的事业')
				// str = str.replace(/:每日/g, '\n')
				// str = str.replace(/:今日/g, '\n')
				// str = str.replace(/:每日星象/g, '\n')
				// str = str.replace(/:今日星象/g, '\n')
				// str = str.replace(/:今天/g, '\n')
				// str = str.replace(/: Daily/g, '\n\1')
				// str = str.replace(/: Today(.*)$/g, '\n\1')
				// str = str.replace(/人马座/g, '射手座')
				// str = str.replace(/摩羯的事业/g, '摩羯的事业')
				// str = str.replace(/天体能量/g, '天象')
				// str = str.replace(/Health(.*)$/g, 'Health\n\1')
				// str = str.replace(/座的星座/g, '座')
				// str = str.replace(/座的健康/g, '座健康')
				// str = str.replace(/座的职业/g, '座职业')
				// str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/<p>(&nbsp;)*<\/p>/g, "")
				str = str.replace("\\n", "\n>")
				
				str = str.replace('<p><strong class="text-dp">',
					'<p style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">'
					)
				str = str.replace('<p>白羊座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x01.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座</strong></h3>'
					)
				str = str.replace('<p>白羊座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座职业</strong></h3>'
					)
				str = str.replace('<p>白羊座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座爱情</strong></h3>'
					)
				str = str.replace('<p>白羊座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座健康</strong></h3>'
					)
				str = str.replace('<p>金牛座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x02.jpg"   class="imgb center-block" ></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座</strong></h3>'
					)
				str = str.replace('<p>金牛座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座职业</strong></h3>'
					)
				str = str.replace('<p>金牛座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座爱情</strong></h3>'
					)
				str = str.replace('<p>金牛座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座健康</strong></h3>'
					)
				str = str.replace('<p>双子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x03.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座</strong></h3>'
					)
				str = str.replace('<p>双子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座职业</strong></h3>'
					)
				str = str.replace('<p>双子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座爱情</strong></h3>'
					)
				str = str.replace('<p>双子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座健康</strong></h3>'
					)
				str = str.replace('<p>巨蟹座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x04.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座</strong></h3>'
					)
				str = str.replace('<p>巨蟹座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座职业</strong></h3>'
					)
				str = str.replace('<p>巨蟹座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座爱情</strong></h3>'
					)
				str = str.replace('<p>巨蟹座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座健康</strong></h3>'
					)
				str = str.replace('<p>狮子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x05.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座</strong></h3>'
					)
				str = str.replace('<p>狮子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座职业</strong></h3>'
					)
				str = str.replace('<p>狮子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座爱情</strong></h3>'
					)
				str = str.replace('<p>狮子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座健康</strong></h3>'
					)
				str = str.replace('<p>处女座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x06.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座</strong></h3>'
					)
				str = str.replace('<p>处女座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座职业</strong></h3>'
					)
				str = str.replace('<p>处女座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座爱情</strong></h3>'
					)
				str = str.replace('<p>处女座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座健康</strong></h3>'
					)
				str = str.replace('<p>天秤座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x07.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座</strong></h3>'
					)
				str = str.replace('<p>天秤座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座职业</strong></h3>'
					)
				str = str.replace('<p>天秤座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座爱情</strong></h3>'
					)
				str = str.replace('<p>天秤座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座健康</strong></h3>'
					)
				str = str.replace('<p>天蝎座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x08.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座</strong></h3>'
					)
				str = str.replace('<p>天蝎座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座职业</strong></h3>'
					)
				str = str.replace('<p>天蝎座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座爱情</strong></h3>'
					)
				str = str.replace('<p>天蝎座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座健康</strong></h3>'
					)
				str = str.replace('<p>射手座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x09.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座</strong></h3>'
					)
				str = str.replace('<p>射手座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座职业</strong></h3>'
					)
				str = str.replace('<p>射手座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座爱情</strong></h3>'
					)
				str = str.replace('<p>射手座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座健康</strong></h3>'
					)
		
				str = str.replace('<p>摩羯座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x10.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座</strong></h3>'
					)
				str = str.replace('<p>摩羯座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座职业</strong></h3>'
					)
				str = str.replace('<p>摩羯座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座爱情</strong></h3>'
					)
				str = str.replace('<p>摩羯座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座健康</strong></h3>'
					)
				str = str.replace('<p>水瓶座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x11.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座</strong></h3>'
					)
				str = str.replace('<p>水瓶座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座职业</strong></h3>'
					)
				str = str.replace('<p>水瓶座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座爱情</strong></h3>'
					)
				str = str.replace('<p>水瓶座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座健康</strong></h3>'
					)
				str = str.replace('<p>双鱼座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x12.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座</strong></h3>'
					)
				str = str.replace('<p>双鱼座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座职业</strong></h3>'
					)
				str = str.replace('<p>双鱼座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座爱情</strong></h3>'
					)
				str = str.replace('<p>双鱼座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座健康</strong></h3>'
					)
				str = str.replace('<p>每日行星概览</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				str = str.replace('<p>每日行星概述</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				
				str = str.replace('<p>白羊座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座事业</strong></h3>'
				)		
				str = str.replace('<p>金牛座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座事业</strong></h3>'
				)		
				str = str.replace('<p>双子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座事业</strong></h3>'
				)	
				str = str.replace('<p>巨蟹座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座事业</strong></h3>'
				)	
				str = str.replace('<p>狮子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座事业</strong></h3>'
				)	
				str = str.replace('<p>处女座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座事业</strong></h3>'
				)	
				str = str.replace('<p>天秤座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座事业</strong></h3>'
				)	
				str = str.replace('<p>天蝎座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座事业</strong></h3>'
				)	
				str = str.replace('<p>射手座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座事业</strong></h3>'
				)	
				str = str.replace('<p>摩羯座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座事业</strong></h3>'
				)	
				str = str.replace('<p>水瓶座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座事业</strong></h3>'
				)	
				str = str.replace('<p>双鱼座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座事业</strong></h3>'
				)	
					
					
				str = str.replace('&nbsp;', ' ')
				str = str.replace('&rsquo;', '’')
				str = str.replace('&lsquo;', '‘')
				str = str.replace('&ldquo;', '“')
				str = str.replace('&rdquo;', '”')
				str = str.replace('&mdash;', '——')
				str = str.replace('&ndash;', '–')
				str = str.replace('&hellip;', '...')
				// str = str.replace(/Weekly Education and Knowledge Horoscope/g, 'Education')
				// str = str.replace(/Weekly Money and Finances Horoscope/g, 'Money')
				// str = str.replace(/Weekly Love and Relationships Horoscope/g, 'Love')
				// str = str.replace(/Weekly Health and Well Being Horoscope/g, 'Health')
				// str = str.replace(/Weekly Career and Business Horoscope/g, 'Career')
		
				// str = str.replace(
				// 	'Weekly Horoscope.For a more in depth and personalised astrological analysis, check your Daily Natal HoroscopeAries daily horoscopeAries monthly horoscopeAries yearly horoscopeSee also:Today’s horoscopeTomorrow&#39;s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ,',
				// 	'')
				// str = str.replace(
				// 	/Get now more details about the impact of the planets this week, based on your ➳	/g,
				// 	'')
				// str = str.replace(/Health &amp; Wellness/g, 'Health ')
				str = str.replace('\\n\\n\\n', '')
				str = str.replace(
					' | Horoscope.com\tYesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n \\n\\n	',
					'')
				str = str.replace(': Daily & Today | Horoscope.com', '')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n',
					'')
				str = str.replace('Overview\\nLove\\nCareer\\nMoney\\nHealth\\n\\n', '')
				str = str.replace(' | Horoscope.com', '')
				str = str.replace('&amp; Wellness Horoscope', '')
				str = str.replace('Career Horoscope', 'Career\n')
				str = str.replace('Love Horoscope', 'Love\n')
				str = str.replace('Horoscope\t ', 'Horoscope\t \n\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n\\n...\\n\\nWeekly\\nMonthly\\n2020\\n \\n \\n\\n',
					' \n\n')
				str = str.replace('\\n', '')
				str = str.replace('\\n', '')
				str = str.replace('Health\t', 'Health\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n 2020\\n\\n \\n \\n\\n',
					'')
				// str = str.replace('\\n', '座财运')
				str = str.replace('http：//', 'http://')
		
				str = str.replace('<p>(.*)座</p>',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">\1座</strong></p>'
					)
		
				str = str.replace(' \t', ' ')
		
				str = str.replace('<p><strong class="text-dp">',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">'
					)
				// str = str.replace(/月球/g, '月亮')
				// str = str.replace(/双子星座/g, '双子座')
				// str = str.replace(/正方形/g, '刑相位')
				// str = str.replace(/四分之一/g, '刑相位')
				// str = str.replace(/座运势/g, '运势')
				str = str.replace(/Horoscope/g, ' ')
				// str = str.replace(/双子的爱/g, '双子座爱情')
				str = str.replace('<p>爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p>职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p>健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p>财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p>学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)
		
				str = str.replace('<p class="MsoNormal">爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)
		
				// str = str.replace(/wed./g, 'wednesday')
				// str = str.replace(/六分之一/g, '六合')
				// str = str.replace(/月球/g, '月亮')
				// str = str.replace(/利奥/g, '狮子座')
				// str = str.replace(/间隔/g, '时间段')
				// str = str.replace(/维纳斯/g, '金星')
				// str = str.replace(/地球星座/g, '土象星座')
				// str = str.replace(/癌症/g, '巨蟹座')
				// str = str.replace(/迹象/g, '星座')
				// str = str.replace(/冲相位/g, '')
				// str = str.replace(/主宰/g, '刑相位')
				// str = str.replace(/马尔斯/g, '火星')
				// str = str.replace(/六分之一/g, '六分相')
				// str = str.replace(/朱庇特/g, '木星')
				// str = str.replace(/基督教占星术/g, '基督占星')
				// str = str.replace(/宫尖/g, '宫头')
				// str = str.replace(/固定标志/g, '固定星座')
				// str = str.replace(/基本标志/g, '本位星座')
				// str = str.replace(/易变标志/g, '变动星座')
				// str = str.replace(/放置/g, '落入')
				// str = str.replace(/红衣主教/g, '本位星座')
				// str = str.replace(/常见的星座/g, '变动星座')
				// str = str.replace(/主要星座/g, '本位星座')
				// str = str.replace(/符号/g, '星座')
				// str = str.replace(/可变的星座/g, '变动星座')
				// str = str.replace(/可变星座/g, '变动星座')
				// str = str.replace(/继承房屋/g, '续宫')
				// str = str.replace(/节奏的房屋/g, '果宫')
				// str = str.replace(/南部节点/g, '南交点')
				// str = str.replace(/海图/g, '星盘')
				// str = str.replace(/平方/g, '刑相位')
				// str = str.replace(/节点/g, '月交点')
				// str = str.replace(/直接移动/g, '顺行')
				// str = str.replace(/直角/g, '刑相位')
				// str = str.replace(/正方形/g, '刑相位')
				// str = str.replace(/人马/g, '射手')
				// str = str.replace(/反中情局/g, '映点')
				// str = str.replace(/尖端/g, '宫头')
				// str = str.replace(/奇隆/g, '凯龙')
				// str = str.replace(/当地人/g, '盘主')
				// str = str.replace(/流动的/g, '良好的')
				// str = str.replace(/汞/g, '水星')
				// str = str.replace(/南节点/g, '南交点')
				// str = str.replace(/空气/g, '风')
				// str = str.replace(/三倍尺/g, '三分主宰星')
				// str = str.replace(/地球/g, '土')
				// str = str.replace(/墨丘利/g, '水星')
				// str = str.replace(/汞/g, '水星')
				// str = str.replace(/广场/g, '刑相位')
				// str = str.replace(/方格/g, '刑相位')
				// str = str.replace(/部门/g, '领域')
				// str = str.replace(/运输/g, '行运')
				// str = str.replace(/对齐/g, '准确成相位')
				// str = str.replace(/对准/g, '准确成相位')
				// str = str.replace(/过境/g, '流年')
				// str = str.replace(/处理死记硬背/g, '例行公事')
				// str = str.replace(/过渡/g, '行运')
				// str = str.replace(/凌日/g, '行运')
				// str = str.replace(/滑进/g, '进入')
				// str = str.replace(/船员/g, '职员')
				// str = str.replace(/螃蟹/g, '巨蟹座')
				// str = str.replace(/.*座偶像/g, '')
				// str = str.replace(/.*图标/g, ' ')
				// str = str.replace(/地区/g, '领域')
				// str = str.replace(/间歇/g, '一段时间')
				// str = str.replace(/生长/g, '良好发展')
				// str = str.replace(/口音/g, '重点')
				// str = str.replace(/奇龙/g, '凯龙')
				// str = str.replace(/希拉龙/g, '凯龙')
				// str = str.replace(/奇隆/g, '凯龙')
				// str = str.replace(/希龙/g, '凯龙')
				// str = str.replace(/出生表/g, '出生星盘')
				// str = str.replace(/火灾星座/g, '火象星座')
				// str = str.replace(/空中星座/g, '风象星座')
				// str = str.replace(/主宰者/g, '主宰星')
				// str = str.replace(/尖顶/g, '宫头')
				// str = str.replace(/红衣主教/g, '本位星座')
				// str = str.replace(/圆润/g, '开心')
				// str = str.replace(/行星方面/g, '行星相位')
				// str = str.replace(/晚餐/g, '晚上')
				// str = str.replace(/会引导你/g, '你会')
				// str = str.replace(/宫尖/g, '宫头')
				// str = str.replace(/纳塔尔/g, '本命')
				// str = str.replace(/乌拉纳斯/g, '天王星')
				// str = str.replace(/娜塔玛斯/g, '火星')
				// str = str.replace(/星体配置/g, '星象')
				// str = str.replace(/座星座/g, '座')
				// str = str.replace(/天空中行星的运动/g, '天象')
				// str = str.replace(/行星的运动/g, '天象')
				// str = str.replace(/交通/g, '天象')
				// str = str.replace(/天体的位置/g, '天象')
				// str = str.replace(/倒退/g, '逆行')
				// str = str.replace(/水银/g, '水星')
				// str = str.replace(/后旋/g, '逆行')
				// str = str.replace(/下旋/g, '逆行')
				// str = str.replace(/三分之一/g, '三合')
				// str = str.replace(/出生星图/g, '出生星盘')
				// str = str.replace(/新月亮/g, '新月亮')
				// str = str.replace(/固定星/g, '恒星')
				// str = str.replace(/逆行站/g, '逆行停滞')
				// str = str.replace(/倒退/g, '倒退')
				// str = str.replace(/水星站/g, '水星停滞')
				// str = str.replace(/方形/g, '刑相位')
				// str = str.replace(/符号/g, '星座')
				// str = str.replace(/方格/g, '刑相位')
				// str = str.replace(/广场/g, '刑相位')
				// str = str.replace(/六分符/g, '六分相位')
				// str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/Get an Accurate Tarot Reading/g, '')
				str = str.replace(/Find out with a Tarot Reading/g, '')
				str = str.replace(
					/You are right where you belong with a psychic love reading.  Chat with a psychic for free!/g,
					'')
				str = str.replace(/Chat with a psychic now for instant answers.Read More Horoscopes:/g, '')
				str = str.replace(/Daily\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Singles Love\n/g, '')
				str = str.replace(/Couples Love\n/g, '')
				str = str.replace(/Work\n/g, '')
				str = str.replace(/Romantic\n/g, '')
				str = str.replace(/SexScope\n/g, '')
				str = str.replace(/Business\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Chinese\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Tarot\n/g, '')
				str = str.replace(/Psychics/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Still confused about your relationship？/g, '')
				str = str.replace(/You are right where you belong with a psychic love reading./g,
					' Chat with a psychic now for instant answers.')
				str = str.replace(/Is your relationship worth fighting for？/g,
					' Find out with a Free Tarot Reading.')
				str = str.replace(
					/Take this relationship questionnaire and find local compatible singles!/g, '')
				str = str.replace(
					/Try the new eharmony compatibility quiz and get matched with local singles today!/g,
					'')
				str = str.replace('\n\n\n\n \n\n\n\n\n\n\n\n\n', '')
				str = str.replace('\n\n\n\n\n\n', '')
		
				str = str.replace(' Your Love', ' \nYour Love')
				str = str.replace('Your Career\n', '\nYour Career\n')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/朱诺/g, '婚神星')
				str = str.replace(
					/For a more in depth and personalised astrological analysis， check your Daily Natal.Taurus daily horoscopeTaurus monthly horoscopeTaurus yearly horoscopeSee also:Today’s horoscopeTomorrow&#39；s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(
					/For a more in depth and personalised .* CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(/Weekly./g, '')
				// str = str.replace(/后代/g, '下降')
				// str = str.replace(/主星座/g, '本位星座')
				// str = str.replace(/基本星座/g, '本位星座')
				// str = str.replace(/恒星座/g, '固定星座')
				// str = str.replace(/星球/g, '行星')
				// str = str.replace(/图表/g, '星盘')
				// str = str.replace(/图轮/g, '星盘')
				// str = str.replace(/轮子/g, '星盘')
				// str = str.replace(/房子/g, '宫位')
				// str = str.replace(/星体能量/g, '星象')
				// str = str.replace(/行星流年/g, '星象')
				// str = str.replace(/行星的大气层/g, '星象')
				// str = str.replace(/http：///g,'http://')
				str = str.replace(/----------------------------------	星象/g, '')
				// str = str.replace(/可变/g, '变动')
				// str = str.replace(/火相/g, '火象')
				// str = str.replace(/方位角/g, '相位')
				// str = str.replace(/成方/g, '成刑相位')
				// str = str.replace(/对位/g, '冲相位')
				// str = str.replace(/直角/g, '刑相位')
				// str = str.replace(/对话/g, '商谈')
				// str = str.replace(/尝试/g, '试着')
				// str = str.replace(/将带给您/g, '会有')
				// str = str.replace(/将与带给您/g, '会有')
				// str = str.replace(/意外/g, '临时')
				// str = str.replace(/广播/g, '传播')
				// str = str.replace(/不可否认/g, '难得')
				// str = str.replace(/於/g, '于')
				// str = str.replace(/後/g, '后')
				// str = str.replace(/後/g, '后')
				// str = str.replace(/舆/g, '与')
				// str = str.replace(/太阳回归图/g, '太阳返照星盘')
				// str = str.replace(/麽/g, '么')
				// str = str.replace(/蠍/g, '蝎')
				// str = str.replace(/人马座/g, '射手座')
				// str = str.replace(/土著人/g, '盘主')
				// str = str.replace(/土著/g, '盘主')
				// str = str.replace(/月亮大厦/g, '月宿')
				// str = str.replace(/纳夏特拉/g, '月宿')
				// str = str.replace(/兼容性/g, '感情匹配度')
				// str = str.replace(/选举占星术/g, '择日占星术')
				// str = str.replace(/原住民/g, '盘主')
				// str = str.replace(/次级进展/g, '次限')
				// str = str.replace(/太阳弧方向/g, '太阳弧')
				// str = str.replace(/太阳返回图/g, '太阳返照')
				// str = str.replace(/相容性/g, '匹配度')
				// str = str.replace(/本生/g, '本命')
				// str = str.replace(/复合图/g, '组合盘')
				// str = str.replace(/合成的/g, '组合盘')
				// str = str.replace(/复合图/g, '组合盘')
				// str = str.replace(/复合星盘/g, '组合盘')
		
				str = str.replace('标题1', '<p class="img_box"> <img src="http://ke.12sign.top/img/01h.png"> </p>')
				str = str.replace('标题2', '<p class="img_box"> <img src="http://ke.12sign.top/img/02h.png"> </p>')
				str = str.replace('标题3', '<p class="img_box"> <img src="http://ke.12sign.top/img/03h.png"> </p>')
				str = str.replace('标题4', '<p class="img_box"> <img src="http://ke.12sign.top/img/04h.png"> </p>')
				str = str.replace('标题5', '<p class="img_box"> <img src="http://ke.12sign.top/img/05h.png"> </p>')
				str = str.replace('标题6', '<p class="img_box"> <img src="http://ke.12sign.top/img/06h.png"> </p>')
				str = str.replace('标题7', '<p class="img_box"> <img src="http://ke.12sign.top/img/07h.png"> </p>')
				str = str.replace('标题8', '<p class="img_box"> <img src="http://ke.12sign.top/img/08h.png"> </p>')
				str = str.replace('标题9', '<p class="img_box"> <img src="http://ke.12sign.top/img/09h.png"> </p>')
				str = str.replace('标题10', '<p class="img_box"> <img src="http://ke.12sign.top/img/10h.png"> </p>')
		
		// 		str = str.replace('Four Quarters （Padas）', '四部分')
		// 		str = str.replace(/人马/g, '射手座')
		// 		str = str.replace(/星象图/g, '星盘')
		// 		str = str.replace(/出生图/g, '星盘')
		
		// 		str = str.replace(/房屋/g, '宫位')
		// 		str = str.replace(/外观方面/g, '相位')
		// 		str = str.replace(/Sun Sign/g, '太阳星座')
		// 		str = str.replace(/集成电路/g, '天底')
		// 		str = str.replace(/后裔/g, '下降点')
		// 		str = str.replace(/对立/g, '冲相位')
		// 		str = str.replace(/相反/g, '冲相位')
		// 		str = str.replace(/反对/g, '冲相位')
		// 		str = str.replace(/对面/g, '冲相位')
		// 		str = str.replace(/相对的/g, '冲相位')
		
				// str = str.replace(/直接的/g, '顺行的')
				// str = str.replace(/相对/g, '冲相位')
				// str = str.replace(/结合/g, '合相')
				// str = str.replace(/巨蟹座：/g, '巨蟹座\n')
				// str = str.replace(/狮子：/g, '狮子座\n')
				// str = str.replace(/处女座：/g, '处女座\n')
				// str = str.replace(/天秤：/g, '天秤座\n')
				// str = str.replace(/摩羯座：/g, '摩羯座\n')
				// str = str.replace(/双鱼座：/g, '双鱼座\n')
				// str = str.replace(/月球/g, '月亮')
				// str = str.replace(/行星的排列/g, '天象')
				// str = str.replace(/行星配置/g, '天象')
				str = str.replace('â€™', "'")
				str = str.replace('â€”', '-')
				str = str.replace('â€“', '-')
				str = str.replace('â€œ', "'")
				str = str.replace('â€', "'")
				// str = str.replace(';','；')
				// str = str.replace(',','，')
				// str = str.replace(',','，')
				// str = str.replace('?','？')
				str = str.replace(/Daily/g, '')
				// str = str.replace('!','！')
				// str = str.replace(')','）')
				// str = str.replace('(','（')
				str = "<div class='blog_box' style='box-sizing: border-box;'>" + str +'</div>'
			} else {
				
				str = '<p>' + str
				str = str.replace(/<br\s*\/?>/gi, "</p><p>")
				console.log('No')
				str = str.replace(/(<br[^>]*>|\s*)/g, '')
				console.log(str)
				str = str.replace(/&amp;/g, '&');
				str = str.replace(/&lt;/g, '<');
				str = str.replace(/&gt;/g, '>');
				str = str.replace(/&quot;/g, '"');
				str = str.replace(/&#039;/g, "'");
				str = str.replace(/\n/g, "");
				str = str.replace(/class="MsoNormal"/g, '');
				// str = str.replace(/座的教育/g, '座教育')
				// str = str.replace(/座的钱/g, '座财运')
				// str = str.replace(/每周职业和商业星座/g, '职业运势')
				// str = str.replace(/每周职业和商业星座/g, '职业运势')
				// str = str.replace(/职业运势运势/g, '职业运势')
				// str = str.replace(/癌症/g, '巨蟹座')
				// str = str.replace(/座运势:每日/g, '座运势\n')
				// str = str.replace(/座运势:今天/g, '座运势\n')
				// str = str.replace(/座的职业/g, '座职业')
				// str = str.replace(/座的星座/g, '座运势')
				// str = str.replace(/座爱情情/g, '座爱情')
				// str = str.replace(/天秤的爱/g, '天秤座爱情\n')
				// str = str.replace(/座:每日/g, '座运势\n')
				// str = str.replace(/座:今天/g, '座运势\n')
				// str = str.replace(/座:今日/g, '座运势\n')
				// str = str.replace(/座的事业/g, '座的事业')
				str = str.replace(/:每日/g, '\n')
				str = str.replace(/:今日/g, '\n')
				str = str.replace(/:每日星象/g, '\n')
				str = str.replace(/:今日星象/g, '\n')
				str = str.replace(/:今天/g, '\n')
				str = str.replace(/: Daily/g, '\n\1')
				str = str.replace(/: Today(.*)$/g, '\n\1')
				// str = str.replace(/人马座/g, '射手座')
				// str = str.replace(/摩羯的事业/g, '摩羯的事业')
				// str = str.replace(/天体能量/g, '天象')
				// str = str.replace(/Health(.*)$/g, 'Health\n\1')
				// str = str.replace(/座的星座/g, '座')
				// str = str.replace(/座的健康/g, '座健康')
				// str = str.replace(/座的职业/g, '座职业')
				// str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/<p>(&nbsp;)*<\/p>/g, "")
				str = str.replace("\\n", "\n>")
				str = str.replace('<p>(.*)座爱情</p>',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座爱情</strong></p>'
					)
				str = str.replace('<p>(.*)座职业</p>	',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座职业</strong></p>'
					)
				str = str.replace('<p>(.*)座健康</p>',
					'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座健康</strong></p>'
					)
				str = str.replace('<p><strong class="text-dp">',
					'<p style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">'
					)
				str = str.replace('<p>白羊座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x01.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座</strong></h3>'
					)
				str = str.replace('<p>白羊座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座职业</strong></h3>'
					)
				str = str.replace('<p>白羊座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座爱情</strong></h3>'
					)
				str = str.replace('<p>白羊座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座健康</strong></h3>'
					)
				str = str.replace('<p>金牛座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x02.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座</strong></h3>'
					)
				str = str.replace('<p>金牛座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座职业</strong></h3>'
					)
				str = str.replace('<p>金牛座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座爱情</strong></h3>'
					)
				str = str.replace('<p>金牛座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座健康</strong></h3>'
					)
				str = str.replace('<p>双子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x03.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座</strong></h3>'
					)
				str = str.replace('<p>双子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座职业</strong></h3>'
					)
				str = str.replace('<p>双子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座爱情</strong></h3>'
					)
				str = str.replace('<p>双子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座健康</strong></h3>'
					)
				str = str.replace('<p>巨蟹座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x04.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座</strong></h3>'
					)
				str = str.replace('<p>巨蟹座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座职业</strong></h3>'
					)
				str = str.replace('<p>巨蟹座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座爱情</strong></h3>'
					)
				str = str.replace('<p>巨蟹座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座健康</strong></h3>'
					)
				str = str.replace('<p>狮子座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x05.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座</strong></h3>'
					)
				str = str.replace('<p>狮子座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座职业</strong></h3>'
					)
				str = str.replace('<p>狮子座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座爱情</strong></h3>'
					)
				str = str.replace('<p>狮子座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座健康</strong></h3>'
					)
				str = str.replace('<p>处女座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x06.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座</strong></h3>'
					)
				str = str.replace('<p>处女座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座职业</strong></h3>'
					)
				str = str.replace('<p>处女座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座爱情</strong></h3>'
					)
				str = str.replace('<p>处女座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座健康</strong></h3>'
					)
				str = str.replace('<p>天秤座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x07.jpg"   class="imgb center-block" ></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座</strong></h3>'
					)
				str = str.replace('<p>天秤座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座职业</strong></h3>'
					)
				str = str.replace('<p>天秤座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座爱情</strong></h3>'
					)
				str = str.replace('<p>天秤座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座健康</strong></h3>'
					)
				str = str.replace('<p>天蝎座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x08.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座</strong></h3>'
					)
				str = str.replace('<p>天蝎座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座职业</strong></h3>'
					)
				str = str.replace('<p>天蝎座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座爱情</strong></h3>'
					)
				str = str.replace('<p>天蝎座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座健康</strong></h3>'
					)
				str = str.replace('<p>射手座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x09.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座</strong></h3>'
					)
				str = str.replace('<p>射手座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座职业</strong></h3>'
					)
				str = str.replace('<p>射手座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座爱情</strong></h3>'
					)
				str = str.replace('<p>射手座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座健康</strong></h3>'
					)
				
				str = str.replace('<p>摩羯座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x10.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座</strong></h3>'
					)
				str = str.replace('<p>摩羯座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座职业</strong></h3>'
					)
				str = str.replace('<p>摩羯座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座爱情</strong></h3>'
					)
				str = str.replace('<p>摩羯座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座健康</strong></h3>'
					)
				str = str.replace('<p>水瓶座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x11.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座</strong></h3>'
					)
				str = str.replace('<p>水瓶座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座职业</strong></h3>'
					)
				str = str.replace('<p>水瓶座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座爱情</strong></h3>'
					)
				str = str.replace('<p>水瓶座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座健康</strong></h3>'
					)
				str = str.replace('<p>双鱼座</p>',
					'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x12.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座</strong></h3>'
					)
				str = str.replace('<p>双鱼座职业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座职业</strong></h3>'
					)
				str = str.replace('<p>双鱼座爱情</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座爱情</strong></h3>'
					)
				str = str.replace('<p>双鱼座健康</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座健康</strong></h3>'
					)
					
				
				str = str.replace('<p>白羊座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座事业</strong></h3>'
				)		
				str = str.replace('<p>金牛座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座事业</strong></h3>'
				)		
				str = str.replace('<p>双子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座事业</strong></h3>'
				)	
				str = str.replace('<p>巨蟹座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座事业</strong></h3>'
				)	
				str = str.replace('<p>狮子座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座事业</strong></h3>'
				)	
				str = str.replace('<p>处女座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座事业</strong></h3>'
				)	
				str = str.replace('<p>天秤座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座事业</strong></h3>'
				)	
				str = str.replace('<p>天蝎座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座事业</strong></h3>'
				)	
				str = str.replace('<p>射手座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座事业</strong></h3>'
				)	
				str = str.replace('<p>摩羯座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座事业</strong></h3>'
				)	
				str = str.replace('<p>水瓶座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座事业</strong></h3>'
				)	
				str = str.replace('<p>双鱼座事业</p>',
					'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座事业</strong></h3>'
				)	
					
					
						
					
				str = str.replace('<p>每日行星概览</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				str = str.replace('<p>每日行星概述</p>',
					'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
					)
				str = str.replace('&nbsp;', ' ')
				str = str.replace('&rsquo;', '’')
				str = str.replace('&lsquo;', '‘')
				str = str.replace('&ldquo;', '“')
				str = str.replace('&rdquo;', '”')
				str = str.replace('&mdash;', '——')
				str = str.replace('&ndash;', '–')
				str = str.replace('&hellip;', '...')
				str = str.replace(/Weekly Education and Knowledge Horoscope/g, 'Education')
				str = str.replace(/Weekly Money and Finances Horoscope/g, 'Money')
				str = str.replace(/Weekly Love and Relationships Horoscope/g, 'Love')
				str = str.replace(/Weekly Health and Well Being Horoscope/g, 'Health')
				str = str.replace(/Weekly Career and Business Horoscope/g, 'Career')
		
				str = str.replace(
					'Weekly Horoscope.For a more in depth and personalised astrological analysis, check your Daily Natal HoroscopeAries daily horoscopeAries monthly horoscopeAries yearly horoscopeSee also:Today’s horoscopeTomorrow&#39;s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ,',
					'')
				str = str.replace(
					/Get now more details about the impact of the planets this week, based on your ➳	/g,
					'')
				str = str.replace(/Health &amp; Wellness/g, 'Health ')
				str = str.replace('\\n\\n\\n', '')
				str = str.replace(
					' | Horoscope.com\tYesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n \\n\\n	',
					'')
				str = str.replace(': Daily & Today | Horoscope.com', '')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n',
					'')
				str = str.replace('Overview\\nLove\\nCareer\\nMoney\\nHealth\\n\\n', '')
				str = str.replace(' | Horoscope.com', '')
				str = str.replace('&amp; Wellness Horoscope', '')
				str = str.replace('Career Horoscope', 'Career\n')
				str = str.replace('Love Horoscope', 'Love\n')
				str = str.replace('Horoscope\t ', 'Horoscope\t \n\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n\\n...\\n\\nWeekly\\nMonthly\\n2020\\n \\n \\n\\n',
					' \n\n')
				str = str.replace('\\n', '')
				str = str.replace('\\n', '')
				str = str.replace('Health\t', 'Health\n')
				str = str.replace(
					'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n 2020\\n\\n \\n \\n\\n',
					'')
				str = str.replace('\\n', '座财运')
				str = str.replace('http：//', 'http://')
		
				str = str.replace('<p>(.*)座</p>',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">\1座</strong></p>'
					)
		
				str = str.replace(' \t', ' ')
		
				str = str.replace('<p><strong class="text-dp">',
					'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">'
					)
				// str = str.replace(/月球/g, '月亮')
				// str = str.replace(/双子星座/g, '双子座')
				// str = str.replace(/正方形/g, '刑相位')
				// str = str.replace(/四分之一/g, '刑相位')
				// str = str.replace(/座运势/g, '运势')
				// str = str.replace(/Horoscope/g, ' ')
				// str = str.replace(/双子的爱/g, '双子座爱情')
				str = str.replace('<p>爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p>职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p>健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p>财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p>学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)
		
				str = str.replace('<p class="MsoNormal">爱情运势</p>',
					'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">职业运势</p>',
					'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">健康运势</p>',
					'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">财富运势</p>',
					'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
					)
				str = str.replace('<p class="MsoNormal">学习运势</p>',
					'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
					)
		
				str = str.replace(/wed./g, 'wednesday')
				// str = str.replace(/六分之一/g, '六合')
				// str = str.replace(/月球/g, '月亮')
				// str = str.replace(/利奥/g, '狮子座')
				// str = str.replace(/间隔/g, '时间段')
				// str = str.replace(/维纳斯/g, '金星')
				// str = str.replace(/地球星座/g, '土象星座')
				// str = str.replace(/癌症/g, '巨蟹座')
				// str = str.replace(/迹象/g, '星座')
				// str = str.replace(/冲相位/g, '')
				// str = str.replace(/主宰/g, '刑相位')
				// str = str.replace(/马尔斯/g, '火星')
				// str = str.replace(/六分之一/g, '六分相')
				// str = str.replace(/朱庇特/g, '木星')
				// str = str.replace(/基督教占星术/g, '基督占星')
				// str = str.replace(/宫尖/g, '宫头')
				// str = str.replace(/固定标志/g, '固定星座')
				// str = str.replace(/基本标志/g, '本位星座')
				// str = str.replace(/易变标志/g, '变动星座')
				// str = str.replace(/放置/g, '落入')
				// str = str.replace(/红衣主教/g, '本位星座')
				// str = str.replace(/常见的星座/g, '变动星座')
				// str = str.replace(/主要星座/g, '本位星座')
				// str = str.replace(/符号/g, '星座')
				// str = str.replace(/可变的星座/g, '变动星座')
				// str = str.replace(/可变星座/g, '变动星座')
				// str = str.replace(/继承房屋/g, '续宫')
				// str = str.replace(/节奏的房屋/g, '果宫')
				// str = str.replace(/南部节点/g, '南交点')
				// str = str.replace(/海图/g, '星盘')
				// str = str.replace(/平方/g, '刑相位')
				// str = str.replace(/节点/g, '月交点')
				// str = str.replace(/直接移动/g, '顺行')
				// str = str.replace(/直角/g, '刑相位')
				// str = str.replace(/正方形/g, '刑相位')
				// str = str.replace(/人马/g, '射手')
				// str = str.replace(/反中情局/g, '映点')
				// str = str.replace(/尖端/g, '宫头')
				// str = str.replace(/奇隆/g, '凯龙')
				// str = str.replace(/当地人/g, '盘主')
				// str = str.replace(/流动的/g, '良好的')
				// str = str.replace(/汞/g, '水星')
				// str = str.replace(/南节点/g, '南交点')
				// str = str.replace(/空气/g, '风')
				// str = str.replace(/三倍尺/g, '三分主宰星')
				// str = str.replace(/地球/g, '土')
				// str = str.replace(/墨丘利/g, '水星')
				// str = str.replace(/汞/g, '水星')
				// str = str.replace(/广场/g, '刑相位')
				// str = str.replace(/方格/g, '刑相位')
				// str = str.replace(/部门/g, '领域')
				// str = str.replace(/运输/g, '行运')
				// str = str.replace(/对齐/g, '准确成相位')
				// str = str.replace(/对准/g, '准确成相位')
				// str = str.replace(/过境/g, '流年')
				// str = str.replace(/处理死记硬背/g, '例行公事')
				// str = str.replace(/过渡/g, '行运')
				// str = str.replace(/凌日/g, '行运')
				// str = str.replace(/滑进/g, '进入')
				// str = str.replace(/船员/g, '职员')
				// str = str.replace(/螃蟹/g, '巨蟹座')
				// str = str.replace(/.*座偶像/g, '')
				// str = str.replace(/.*图标/g, ' ')
				// str = str.replace(/地区/g, '领域')
				// str = str.replace(/间歇/g, '一段时间')
				// str = str.replace(/生长/g, '良好发展')
				// str = str.replace(/口音/g, '重点')
				// str = str.replace(/奇龙/g, '凯龙')
				// str = str.replace(/希拉龙/g, '凯龙')
				// str = str.replace(/奇隆/g, '凯龙')
				// str = str.replace(/希龙/g, '凯龙')
				// str = str.replace(/出生表/g, '出生星盘')
				// str = str.replace(/火灾星座/g, '火象星座')
				// str = str.replace(/空中星座/g, '风象星座')
				// str = str.replace(/主宰者/g, '主宰星')
				// str = str.replace(/尖顶/g, '宫头')
				// str = str.replace(/红衣主教/g, '本位星座')
				// str = str.replace(/圆润/g, '开心')
				// str = str.replace(/行星方面/g, '行星相位')
				// str = str.replace(/晚餐/g, '晚上')
				// str = str.replace(/会引导你/g, '你会')
				// str = str.replace(/宫尖/g, '宫头')
				// str = str.replace(/纳塔尔/g, '本命')
				// str = str.replace(/乌拉纳斯/g, '天王星')
				// str = str.replace(/娜塔玛斯/g, '火星')
				// str = str.replace(/星体配置/g, '星象')
				// str = str.replace(/座星座/g, '座')
				// str = str.replace(/天空中行星的运动/g, '天象')
				// str = str.replace(/行星的运动/g, '天象')
				// str = str.replace(/交通/g, '天象')
				// str = str.replace(/天体的位置/g, '天象')
				// str = str.replace(/倒退/g, '逆行')
				// str = str.replace(/水银/g, '水星')
				// str = str.replace(/后旋/g, '逆行')
				// str = str.replace(/下旋/g, '逆行')
				// str = str.replace(/三分之一/g, '三合')
				// str = str.replace(/出生星图/g, '出生星盘')
				// str = str.replace(/新月亮/g, '新月亮')
				// str = str.replace(/固定星/g, '恒星')
				// str = str.replace(/逆行站/g, '逆行停滞')
				// str = str.replace(/倒退/g, '倒退')
				// str = str.replace(/水星站/g, '水星停滞')
				// str = str.replace(/方形/g, '刑相位')
				// str = str.replace(/符号/g, '星座')
				// str = str.replace(/方格/g, '刑相位')
				// str = str.replace(/广场/g, '刑相位')
				// str = str.replace(/六分符/g, '六分相位')
				// str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/Get an Accurate Tarot Reading/g, '')
				str = str.replace(/Find out with a Tarot Reading/g, '')
				str = str.replace(
					/You are right where you belong with a psychic love reading.  Chat with a psychic for free!/g,
					'')
				str = str.replace(/Chat with a psychic now for instant answers.Read More Horoscopes:/g, '')
				str = str.replace(/Daily\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Singles Love\n/g, '')
				str = str.replace(/Couples Love\n/g, '')
				str = str.replace(/Work\n/g, '')
				str = str.replace(/Romantic\n/g, '')
				str = str.replace(/SexScope\n/g, '')
				str = str.replace(/Business\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Chinese\n/g, '')
				str = str.replace(/Flirt\n/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Tarot\n/g, '')
				str = str.replace(/Psychics/g, '')
				str = str.replace(/Finance\n/g, '')
				str = str.replace(/Still confused about your relationship？/g, '')
				str = str.replace(/You are right where you belong with a psychic love reading./g,
					' Chat with a psychic now for instant answers.')
				str = str.replace(/Is your relationship worth fighting for？/g,
					' Find out with a Free Tarot Reading.')
				str = str.replace(
					/Take this relationship questionnaire and find local compatible singles!/g, '')
				str = str.replace(
					/Try the new eharmony compatibility quiz and get matched with local singles today!/g,
					'')
				str = str.replace('\n\n\n\n \n\n\n\n\n\n\n\n\n', '')
				str = str.replace('\n\n\n\n\n\n', '')
		
				str = str.replace(' Your Love', ' \nYour Love')
				str = str.replace('Your Career\n', '\nYour Career\n')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/Read More Horoscopes:/g, '')
				str = str.replace(/朱诺/g, '婚神星')
				str = str.replace(
					/For a more in depth and personalised astrological analysis， check your Daily Natal.Taurus daily horoscopeTaurus monthly horoscopeTaurus yearly horoscopeSee also:Today’s horoscopeTomorrow&#39；s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(
					/For a more in depth and personalised .* CharacteristicsYour Ascendant Characteristics" ，/g,
					'')
				str = str.replace(/Weekly./g, '')
				// str = str.replace(/后代/g, '下降')
				// str = str.replace(/主星座/g, '本位星座')
				// str = str.replace(/基本星座/g, '本位星座')
				// str = str.replace(/恒星座/g, '固定星座')
				// str = str.replace(/星球/g, '行星')
				// str = str.replace(/图表/g, '星盘')
				// str = str.replace(/图轮/g, '星盘')
				// str = str.replace(/轮子/g, '星盘')
				// str = str.replace(/房子/g, '宫位')
				// str = str.replace(/星体能量/g, '星象')
				// str = str.replace(/行星流年/g, '星象')
				// str = str.replace(/行星的大气层/g, '星象')
				// str = str.replace(/http：///g,'http://')
				str = str.replace(/----------------------------------	星象/g, '')
				// str = str.replace(/可变/g, '变动')
				// str = str.replace(/火相/g, '火象')
				// str = str.replace(/方位角/g, '相位')
				// str = str.replace(/成方/g, '成刑相位')
				// str = str.replace(/对位/g, '冲相位')
				// str = str.replace(/直角/g, '刑相位')
				// str = str.replace(/对话/g, '商谈')
				// str = str.replace(/尝试/g, '试着')
				// str = str.replace(/将带给您/g, '会有')
				// str = str.replace(/将与带给您/g, '会有')
				// str = str.replace(/意外/g, '临时')
				// str = str.replace(/广播/g, '传播')
				// str = str.replace(/不可否认/g, '难得')
				// str = str.replace(/於/g, '于')
				// str = str.replace(/後/g, '后')
				// str = str.replace(/後/g, '后')
				// str = str.replace(/舆/g, '与')
				// str = str.replace(/太阳回归图/g, '太阳返照星盘')
				// str = str.replace(/麽/g, '么')
				// str = str.replace(/蠍/g, '蝎')
				// str = str.replace(/人马座/g, '射手座')
				// str = str.replace(/土著人/g, '盘主')
				// str = str.replace(/土著/g, '盘主')
				// str = str.replace(/月亮大厦/g, '月宿')
				// str = str.replace(/纳夏特拉/g, '月宿')
				// str = str.replace(/兼容性/g, '感情匹配度')
				// str = str.replace(/选举占星术/g, '择日占星术')
				// str = str.replace(/原住民/g, '盘主')
				// str = str.replace(/次级进展/g, '次限')
				// str = str.replace(/太阳弧方向/g, '太阳弧')
				// str = str.replace(/太阳返回图/g, '太阳返照')
				// str = str.replace(/相容性/g, '匹配度')
				// str = str.replace(/本生/g, '本命')
				// str = str.replace(/复合图/g, '组合盘')
				// str = str.replace(/合成的/g, '组合盘')
				// str = str.replace(/复合图/g, '组合盘')
				// str = str.replace(/复合星盘/g, '组合盘')
		
				str = str.replace('标题1', '<p class="img_box"> <img src="http://ke.12sign.top/img/01h.png"></p>')
				str = str.replace('标题2', '<p class="img_box"> <img src="http://ke.12sign.top/img/02h.png"></p>')
				str = str.replace('标题3', '<p class="img_box"> <img src="http://ke.12sign.top/img/03h.png"></p>')
				str = str.replace('标题4', '<p class="img_box"> <img src="http://ke.12sign.top/img/04h.png"></p>')
				str = str.replace('标题5', '<p class="img_box"> <img src="http://ke.12sign.top/img/05h.png"></p>')
				str = str.replace('标题6', '<p class="img_box"> <img src="http://ke.12sign.top/img/06h.png"></p>')
				str = str.replace('标题7', '<p class="img_box"> <img src="http://ke.12sign.top/img/07h.png"></p>')
				str = str.replace('标题8', '<p class="img_box"> <img src="http://ke.12sign.top/img/08h.png"></p>')
				str = str.replace('标题9', '<p class="img_box"> <img src="http://ke.12sign.top/img/09h.png"></p>')
				str = str.replace('标题10', '<p class="img_box"> <img src="http://ke.12sign.top/img/10h.png"></p>')
		
		// 		str = str.replace('Four Quarters （Padas）', '四部分')
		// 		str = str.replace(/人马/g, '射手座')
		// 		str = str.replace(/星象图/g, '星盘')
		// 		str = str.replace(/出生图/g, '星盘')
		
		// 		str = str.replace(/房屋/g, '宫位')
		// 		str = str.replace(/外观方面/g, '相位')
		// 		str = str.replace(/Sun Sign/g, '太阳星座')
		// 		str = str.replace(/集成电路/g, '天底')
		// 		str = str.replace(/后裔/g, '下降点')
		// 		str = str.replace(/对立/g, '冲相位')
		// 		str = str.replace(/相反/g, '冲相位')
		// 		str = str.replace(/反对/g, '冲相位')
		// 		str = str.replace(/对面/g, '冲相位')
		// 		str = str.replace(/相对的/g, '冲相位')
		
		// 		str = str.replace(/直接的/g, '顺行的')
		// 		str = str.replace(/相对/g, '冲相位')
		// 		str = str.replace(/结合/g, '合相')
		// 		str = str.replace(/巨蟹座：/g, '巨蟹座\n')
		// 		str = str.replace(/狮子：/g, '狮子座\n')
		// 		str = str.replace(/处女座：/g, '处女座\n')
		// 		str = str.replace(/天秤：/g, '天秤座\n')
		// 		str = str.replace(/摩羯座：/g, '摩羯座\n')
		// 		str = str.replace(/双鱼座：/g, '双鱼座\n')
		// 		str = str.replace(/月球/g, '月亮')
		// 		str = str.replace(/行星的排列/g, '天象')
		// 		str = str.replace(/行星配置/g, '天象')
				str = str.replace('â€™', "'")
				str = str.replace('â€”', '-')
				str = str.replace('â€“', '-')
				str = str.replace('â€œ', "'")
				str = str.replace('â€', "'")
				// str = str.replace(';','；')
				// str = str.replace(',','，')
				// str = str.replace(',','，')
				// str = str.replace('?','？')
				str = str.replace(/Daily/g, '')
				// str = str.replace('!','！')
				// str = str.replace(')','）')
				// str = str.replace('(','（')
					str = "<div class='blog_box' style='box-sizing: border-box;'>" + str +'</div>'
		
			}
			console.log(str)
			KindEditor.instances[0].html(str)
			$('#ke').val(str)
		});
		$('.Areplace2').click(function() {
			isChange = true
			var str = KindEditor.instances[0].html()
			console.log(str);
			// htmlspecialchars_decode()
			if (str.indexOf("</p>") != -1) {
		
				console.log('有P标签')
		
				var str = KindEditor.instances[0].html()
				//去样式
				str = str.replace(/style\s*?=\s*?(['"])[\s\S]*?\1/g, '');
				//去span
				str = str.replace(/(<\/?span.*?>)/gi, '')
				//去class
				str = str.replace(/class=[\"|'](.*?)[\"|'].*?/g, '')
		
				// str = str.replace(/(<\/?span.*?>)/gi,'')
				// str = str.replace(/class=[\"|'](.*?)[\"|'].*?/g, '')
				console.log(str)
		
				console.log(str)
				str = str.replace(/(<br[^>]*>|\s*)/g, '');
				// str = str.replace(/&amp;/g, '&');
				// str = str.replace(/&lt;/g, '<');
				// str = str.replace(/&gt;/g, '>');
				// str = str.replace(/&quot;/g, '"');
				// str = str.replace(/&#039;/g, "'");
				// str = str.replace(/\n/g, "");
				// str = str.replace(/class="MsoNormal"/g, '');
				str = str.replace(/座的教育/g, '座教育')
				str = str.replace(/座的钱/g, '座财运')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/职业运势运势/g, '职业运势')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/座运势:每日/g, '座运势\n')
				str = str.replace(/座运势:今天/g, '座运势\n')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的星座/g, '座运势')
				str = str.replace(/座爱情情/g, '座爱情')
				str = str.replace(/天秤的爱/g, '天秤座爱情\n')
				str = str.replace(/座:每日/g, '座运势\n')
				str = str.replace(/座:今天/g, '座运势\n')
				str = str.replace(/座:今日/g, '座运势\n')
				str = str.replace(/座的事业/g, '座的事业')
				str = str.replace(/:每日/g, '\n')
				str = str.replace(/:今日/g, '\n')
				str = str.replace(/:每日星象/g, '\n')
				str = str.replace(/:今日星象/g, '\n')
				str = str.replace(/:今天/g, '\n')
				str = str.replace(/: Daily/g, '\n\1')
				str = str.replace(/: Today(.*)$/g, '\n\1')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/摩羯的事业/g, '摩羯的事业')
				str = str.replace(/天体能量/g, '天象')
				str = str.replace(/Health(.*)$/g, 'Health\n\1')
				str = str.replace(/座的星座/g, '座')
				str = str.replace(/座的健康/g, '座健康')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的职业/g, '座职业')
	
				str = str.replace(/Weekly Education and Knowledge Horoscope/g, 'Education')
				str = str.replace(/Weekly Money and Finances Horoscope/g, 'Money')
				str = str.replace(/Weekly Love and Relationships Horoscope/g, 'Love')
				str = str.replace(/Weekly Health and Well Being Horoscope/g, 'Health')
				str = str.replace(/Weekly Career and Business Horoscope/g, 'Career')
		
				str = str.replace(
					'Weekly Horoscope.For a more in depth and personalised astrological analysis, check your Daily Natal HoroscopeAries daily horoscopeAries monthly horoscopeAries yearly horoscopeSee also:Today’s horoscopeTomorrow&#39;s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ,',
					'')
				str = str.replace(
					/Get now more details about the impact of the planets this week, based on your ➳	/g,
					'')
				str = str.replace(/Health &amp; Wellness/g, 'Health ')
	
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/双子星座/g, '双子座')
				str = str.replace(/正方形/g, '刑相位')
				str = str.replace(/四分之一/g, '刑相位')
				str = str.replace(/座运势/g, '运势')
				str = str.replace(/Horoscope/g, ' ')
		

				str = str.replace(/wed./g, 'wednesday')
				str = str.replace(/六分之一/g, '六合')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/间隔/g, '时间段')
				str = str.replace(/维纳斯/g, '金星')
				str = str.replace(/地球星座/g, '土象星座')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/迹象/g, '星座')
				str = str.replace(/冲相位/g, '')
				str = str.replace(/主宰/g, '刑相位')
				str = str.replace(/马尔斯/g, '火星')
				str = str.replace(/六分之一/g, '六分相')
				str = str.replace(/朱庇特/g, '木星')
				str = str.replace(/基督教占星术/g, '基督占星')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/固定标志/g, '固定星座')
				str = str.replace(/基本标志/g, '本位星座')
				str = str.replace(/易变标志/g, '变动星座')
				str = str.replace(/放置/g, '落入')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/常见的星座/g, '变动星座')
				str = str.replace(/主要星座/g, '本位星座')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/可变的星座/g, '变动星座')
				str = str.replace(/可变星座/g, '变动星座')
				str = str.replace(/继承房屋/g, '续宫')
				str = str.replace(/节奏的房屋/g, '果宫')
				str = str.replace(/南部节点/g, '南交点')
				str = str.replace(/海图/g, '星盘')
				str = str.replace(/平方/g, '刑相位')
				str = str.replace(/节点/g, '月交点')
				str = str.replace(/直接移动/g, '顺行')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/正方形/g, '刑相位')
				str = str.replace(/人马/g, '射手')
				str = str.replace(/反中情局/g, '映点')
				str = str.replace(/尖端/g, '宫头')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/当地人/g, '盘主')
				str = str.replace(/流动的/g, '良好的')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/南节点/g, '南交点')
				str = str.replace(/空气/g, '风')
				str = str.replace(/三倍尺/g, '三分主宰星')
				str = str.replace(/地球/g, '土')
				str = str.replace(/墨丘利/g, '水星')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/部门/g, '领域')
				str = str.replace(/运输/g, '行运')
				str = str.replace(/对齐/g, '准确成相位')
				str = str.replace(/对准/g, '准确成相位')
				str = str.replace(/过境/g, '流年')
				str = str.replace(/处理死记硬背/g, '例行公事')
				str = str.replace(/过渡/g, '行运')
				str = str.replace(/凌日/g, '行运')
				str = str.replace(/滑进/g, '进入')
				str = str.replace(/船员/g, '职员')
				str = str.replace(/螃蟹/g, '巨蟹座')
				str = str.replace(/.*座偶像/g, '')
				str = str.replace(/.*图标/g, ' ')
				str = str.replace(/地区/g, '领域')
				str = str.replace(/间歇/g, '一段时间')
				str = str.replace(/生长/g, '良好发展')
				str = str.replace(/口音/g, '重点')
				str = str.replace(/奇龙/g, '凯龙')
				str = str.replace(/希拉龙/g, '凯龙')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/希龙/g, '凯龙')
				str = str.replace(/出生表/g, '出生星盘')
				str = str.replace(/火灾星座/g, '火象星座')
				str = str.replace(/空中星座/g, '风象星座')
				str = str.replace(/主宰者/g, '主宰星')
				str = str.replace(/尖顶/g, '宫头')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/圆润/g, '开心')
				str = str.replace(/行星方面/g, '行星相位')
				str = str.replace(/晚餐/g, '晚上')
				str = str.replace(/会引导你/g, '你会')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/纳塔尔/g, '本命')
				str = str.replace(/乌拉纳斯/g, '天王星')
				str = str.replace(/娜塔玛斯/g, '火星')
				str = str.replace(/星体配置/g, '星象')
				str = str.replace(/座星座/g, '座')
				str = str.replace(/天空中行星的运动/g, '天象')
				str = str.replace(/行星的运动/g, '天象')
				str = str.replace(/交通/g, '天象')
				str = str.replace(/天体的位置/g, '天象')
				str = str.replace(/倒退/g, '逆行')
				str = str.replace(/水银/g, '水星')
				str = str.replace(/后旋/g, '逆行')
				str = str.replace(/下旋/g, '逆行')
				str = str.replace(/三分之一/g, '三合')
				str = str.replace(/出生星图/g, '出生星盘')
				str = str.replace(/新月亮/g, '新月亮')
				str = str.replace(/固定星/g, '恒星')
				str = str.replace(/逆行站/g, '逆行停滞')
				str = str.replace(/倒退/g, '倒退')
				str = str.replace(/水星站/g, '水星停滞')
				str = str.replace(/方形/g, '刑相位')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/六分符/g, '六分相位')
				str = str.replace(/利奥/g, '狮子座')

				str = str.replace(/后代/g, '下降')
				str = str.replace(/主星座/g, '本位星座')
				str = str.replace(/基本星座/g, '本位星座')
				str = str.replace(/恒星座/g, '固定星座')
				str = str.replace(/星球/g, '行星')
				str = str.replace(/图表/g, '星盘')
				str = str.replace(/图轮/g, '星盘')
				str = str.replace(/轮子/g, '星盘')
				str = str.replace(/房子/g, '宫位')
				str = str.replace(/星体能量/g, '星象')
				str = str.replace(/行星流年/g, '星象')
				str = str.replace(/行星的大气层/g, '星象')
			
				str = str.replace(/----------------------------------	星象/g, '')
				str = str.replace(/可变/g, '变动')
				str = str.replace(/火相/g, '火象')
				str = str.replace(/方位角/g, '相位')
				str = str.replace(/成方/g, '成刑相位')
				str = str.replace(/对位/g, '冲相位')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/对话/g, '商谈')
				str = str.replace(/尝试/g, '试着')
				str = str.replace(/将带给您/g, '会有')
				str = str.replace(/将与带给您/g, '会有')
				str = str.replace(/意外/g, '临时')
				str = str.replace(/广播/g, '传播')
				str = str.replace(/不可否认/g, '难得')
				str = str.replace(/於/g, '于')
				str = str.replace(/後/g, '后')
				str = str.replace(/後/g, '后')
				str = str.replace(/舆/g, '与')
				str = str.replace(/太阳回归图/g, '太阳返照星盘')
				str = str.replace(/麽/g, '么')
				str = str.replace(/蠍/g, '蝎')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/土著人/g, '盘主')
				str = str.replace(/土著/g, '盘主')
				str = str.replace(/月亮大厦/g, '月宿')
				str = str.replace(/纳夏特拉/g, '月宿')
				str = str.replace(/兼容性/g, '感情匹配度')
				str = str.replace(/选举占星术/g, '择日占星术')
				str = str.replace(/原住民/g, '盘主')
				str = str.replace(/次级进展/g, '次限')
				str = str.replace(/太阳弧方向/g, '太阳弧')
				str = str.replace(/太阳返回图/g, '太阳返照')
				str = str.replace(/相容性/g, '匹配度')
				str = str.replace(/本生/g, '本命')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/合成的/g, '组合盘')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/复合星盘/g, '组合盘')
		
				str = str.replace(/人马/g, '射手座')
				str = str.replace(/星象图/g, '星盘')
				str = str.replace(/出生图/g, '星盘')
		
				str = str.replace(/房屋/g, '宫位')
				str = str.replace(/外观方面/g, '相位')
				str = str.replace(/Sun Sign/g, '太阳星座')
				str = str.replace(/集成电路/g, '天底')
				str = str.replace(/后裔/g, '下降点')
				str = str.replace(/对立/g, '冲相位')
				str = str.replace(/相反/g, '冲相位')
				str = str.replace(/反对/g, '冲相位')
				str = str.replace(/对面/g, '冲相位')
				str = str.replace(/相对的/g, '冲相位')
		
				str = str.replace(/直接的/g, '顺行的')
				str = str.replace(/相对/g, '冲相位')
				str = str.replace(/结合/g, '合相')
				str = str.replace(/巨蟹座：/g, '巨蟹座\n')
				str = str.replace(/狮子：/g, '狮子座\n')
				str = str.replace(/处女座：/g, '处女座\n')
				str = str.replace(/天秤：/g, '天秤座\n')
				str = str.replace(/摩羯座：/g, '摩羯座\n')
				str = str.replace(/双鱼座：/g, '双鱼座\n')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/行星的排列/g, '天象')
				str = str.replace(/行星配置/g, '天象')
				str = str.replace('â€™', "'")
				str = str.replace('â€”', '-')
				str = str.replace('â€“', '-')
				str = str.replace('â€œ', "'")
				str = str.replace('â€', "'")
				// str = str.replace(';','；')
				// str = str.replace(',','，')
				// str = str.replace(',','，')
				// str = str.replace('?','？')
				str = str.replace(/Daily/g, '')
				// str = str.replace('!','！')
				// str = str.replace(')','）')
				// str = str.replace('(','（')
				str = "<div class='blog_box' style='box-sizing: border-box;'>" + str +'</div>'
			} else {
				
				str = '<p>' + str
				str = str.replace(/<br\s*\/?>/gi, "</p><p>")
			
				str = str.replace(/(<br[^>]*>|\s*)/g, '')
				
				str = str.replace(/&amp;/g, '&');
				str = str.replace(/&lt;/g, '<');
				str = str.replace(/&gt;/g, '>');
				str = str.replace(/&quot;/g, '"');
				str = str.replace(/&#039;/g, "'");
				str = str.replace(/\n/g, "");
				str = str.replace(/class="MsoNormal"/g, '');
				str = str.replace(/座的教育/g, '座教育')
				str = str.replace(/座的钱/g, '座财运')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/每周职业和商业星座/g, '职业运势')
				str = str.replace(/职业运势运势/g, '职业运势')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/座运势:每日/g, '座运势\n')
				str = str.replace(/座运势:今天/g, '座运势\n')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的星座/g, '座运势')
				str = str.replace(/座爱情情/g, '座爱情')
				str = str.replace(/天秤的爱/g, '天秤座爱情\n')
				str = str.replace(/座:每日/g, '座运势\n')
				str = str.replace(/座:今天/g, '座运势\n')
				str = str.replace(/座:今日/g, '座运势\n')
				str = str.replace(/座的事业/g, '座的事业')
				str = str.replace(/:每日/g, '\n')
				str = str.replace(/:今日/g, '\n')
				str = str.replace(/:每日星象/g, '\n')
				str = str.replace(/:今日星象/g, '\n')
				str = str.replace(/:今天/g, '\n')
				str = str.replace(/: Daily/g, '\n\1')
				// str = str.replace(/: Today(.*)$/g, '\n\1')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/摩羯的事业/g, '摩羯的事业')
				str = str.replace(/天体能量/g, '天象')
				str = str.replace(/Health(.*)$/g, 'Health\n\1')
				str = str.replace(/座的星座/g, '座')
				str = str.replace(/座的健康/g, '座健康')
				str = str.replace(/座的职业/g, '座职业')
				str = str.replace(/座的职业/g, '座职业')
		// 		str = str.replace(/<p>(&nbsp;)*<\/p>/g, "")
		// 		str = str.replace("\\n", "\n>")
		// 		str = str.replace('<p>(.*)座爱情</p>',
		// 			'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座爱情</strong></p>'
		// 			)
		// 		str = str.replace('<p>(.*)座职业</p>	',
		// 			'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座职业</strong></p>'
		// 			)
		// 		str = str.replace('<p>(.*)座健康</p>',
		// 			'<p style="color:#FFB6C1;"><strong style="color:#FFB6C1;" class="text-dp">\1座健康</strong></p>'
		// 			)
		// 		str = str.replace('<p><strong class="text-dp">',
		// 			'<p style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">'
		// 			)
		// 		str = str.replace('<p>白羊座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x01.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>白羊座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>白羊座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>白羊座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">白羊座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>金牛座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x02.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>金牛座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>金牛座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>金牛座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">金牛座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双子座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x03.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双子座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双子座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双子座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双子座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>巨蟹座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x04.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>巨蟹座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>巨蟹座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>巨蟹座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">巨蟹座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>狮子座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x05.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>狮子座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>狮子座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>狮子座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">狮子座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>处女座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x06.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>处女座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>处女座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>处女座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">处女座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天秤座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x07.jpg"   class="imgb center-block" ></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天秤座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天秤座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天秤座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天秤座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天蝎座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x08.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;" class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天蝎座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天蝎座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>天蝎座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">天蝎座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>射手座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x09.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>射手座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>射手座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>射手座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">射手座健康</strong></h3>'
		// 			)
				
		// 		str = str.replace('<p>摩羯座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x10.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>摩羯座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>摩羯座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>摩羯座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">摩羯座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>水瓶座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x11.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>水瓶座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>水瓶座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>水瓶座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">水瓶座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双鱼座</p>',
		// 			'<p class="img_box"> <img src="https://xingpanzaixian.oss-cn-beijing.aliyuncs.com/picture/12sign/x12.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:16px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双鱼座职业</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座职业</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双鱼座爱情</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座爱情</strong></h3>'
		// 			)
		// 		str = str.replace('<p>双鱼座健康</p>',
		// 			'<h3  style="text-align: center;color:#FFB6C1;font-size:14px;"  class="text-center"><strong style="color:#FFB6C1;" class="text-dp">双鱼座健康</strong></h3>'
		// 			)
		// 		str = str.replace('<p>每日行星概览</p>',
		// 			'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
		// 			)
		// 		str = str.replace('<p>每日行星概述</p>',
		// 			'<p class="img_box"> <img src="http://ke.12sign.top/img/gs.jpg"   class="imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;"  class="text-center"> <strong style="color:#FFB6C1;" class="text-dp">每日行星概述 </strong></h3>'
		// 			)
		// 		str = str.replace('&nbsp;', ' ')
		// 		str = str.replace('&rsquo;', '’')
		// 		str = str.replace('&lsquo;', '‘')
		// 		str = str.replace('&ldquo;', '“')
		// 		str = str.replace('&rdquo;', '”')
		// 		str = str.replace('&mdash;', '——')
		// 		str = str.replace('&ndash;', '–')
		// 		str = str.replace('&hellip;', '...')
		// 		str = str.replace(/Weekly Education and Knowledge Horoscope/g, 'Education')
		// 		str = str.replace(/Weekly Money and Finances Horoscope/g, 'Money')
		// 		str = str.replace(/Weekly Love and Relationships Horoscope/g, 'Love')
		// 		str = str.replace(/Weekly Health and Well Being Horoscope/g, 'Health')
		// 		str = str.replace(/Weekly Career and Business Horoscope/g, 'Career')
		
		// 		str = str.replace(
		// 			'Weekly Horoscope.For a more in depth and personalised astrological analysis, check your Daily Natal HoroscopeAries daily horoscopeAries monthly horoscopeAries yearly horoscopeSee also:Today’s horoscopeTomorrow&#39;s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ,',
		// 			'')
		// 		str = str.replace(
		// 			/Get now more details about the impact of the planets this week, based on your ➳	/g,
		// 			'')
		// 		str = str.replace(/Health &amp; Wellness/g, 'Health ')
		// 		str = str.replace('\\n\\n\\n', '')
		// 		str = str.replace(
		// 			' | Horoscope.com\tYesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n \\n\\n	',
		// 			'')
		// 		str = str.replace(': Daily & Today | Horoscope.com', '')
		// 		str = str.replace(
		// 			'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n2020\\n\\n \\n',
		// 			'')
		// 		str = str.replace('Overview\\nLove\\nCareer\\nMoney\\nHealth\\n\\n', '')
		// 		str = str.replace(' | Horoscope.com', '')
		// 		str = str.replace('&amp; Wellness Horoscope', '')
		// 		str = str.replace('Career Horoscope', 'Career\n')
		// 		str = str.replace('Love Horoscope', 'Love\n')
		// 		str = str.replace('Horoscope\t ', 'Horoscope\t \n\n')
		// 		str = str.replace(
		// 			'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n\\n...\\n\\nWeekly\\nMonthly\\n2020\\n \\n \\n\\n',
		// 			' \n\n')
		// 		str = str.replace('\\n', '')
		// 		str = str.replace('\\n', '')
		// 		str = str.replace('Health\t', 'Health\n')
		// 		str = str.replace(
		// 			'Yesterday\\nToday\\nTomorrow\\nWeekly\\nMonthly\\n2020\\n...\\n\\nWeekly\\nMonthly\\n 2020\\n\\n \\n \\n\\n',
		// 			'')
		// 		str = str.replace('\\n', '座财运')
		// 		str = str.replace('http：//', 'http://')
		
		// 		str = str.replace('<p>(.*)座</p>',
		// 			'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">\1座</strong></p>'
		// 			)
		
		// 		str = str.replace(' \t', ' ')
		
		// 		str = str.replace('<p><strong class="text-dp">',
		// 			'<p  style="text-align: center;"  class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">'
		// 			)
				// str = str.replace(/月球/g, '月亮')
				// str = str.replace(/双子星座/g, '双子座')
				// str = str.replace(/正方形/g, '刑相位')
				// str = str.replace(/四分之一/g, '刑相位')
				// str = str.replace(/座运势/g, '运势')
				// str = str.replace(/Horoscope/g, ' ')
				// str = str.replace(/双子的爱/g, '双子座爱情')
		// 		str = str.replace('<p>爱情运势</p>',
		// 			'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p>职业运势</p>',
		// 			'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p>健康运势</p>',
		// 			'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p>财富运势</p>',
		// 			'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p>学习运势</p>',
		// 			'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
		// 			)
		
		// 		str = str.replace('<p class="MsoNormal">爱情运势</p>',
		// 			'<p class="img_box"> <img src="" class ="aqimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">爱情运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p class="MsoNormal">职业运势</p>',
		// 			'<p class="img_box"> <img src="" class ="zyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">职业运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p class="MsoNormal">健康运势</p>',
		// 			'<p class="img_box"> <img src="" class ="jkimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">健康运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p class="MsoNormal">财富运势</p>',
		// 			'<p class="img_box"> <img src="" class ="cyimg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">财富运势</strong></h3>'
		// 			)
		// 		str = str.replace('<p class="MsoNormal">学习运势</p>',
		// 			'<p class="img_box"> <img src="" class ="xximg imgb center-block"></p><h3  style="text-align: center;color:#FFB6C1;font-size:18px;" class="text-center"><strong  style="color:#FFB6C1;" class="text-dp">学习运势</strong></h3>'
		// 			)
		
				str = str.replace(/wed./g, 'wednesday')
				str = str.replace(/六分之一/g, '六合')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/利奥/g, '狮子座')
				str = str.replace(/间隔/g, '时间段')
				str = str.replace(/维纳斯/g, '金星')
				str = str.replace(/地球星座/g, '土象星座')
				str = str.replace(/癌症/g, '巨蟹座')
				str = str.replace(/迹象/g, '星座')
				str = str.replace(/冲相位/g, '')
				str = str.replace(/主宰/g, '刑相位')
				str = str.replace(/马尔斯/g, '火星')
				str = str.replace(/六分之一/g, '六分相')
				str = str.replace(/朱庇特/g, '木星')
				str = str.replace(/基督教占星术/g, '基督占星')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/固定标志/g, '固定星座')
				str = str.replace(/基本标志/g, '本位星座')
				str = str.replace(/易变标志/g, '变动星座')
				str = str.replace(/放置/g, '落入')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/常见的星座/g, '变动星座')
				str = str.replace(/主要星座/g, '本位星座')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/可变的星座/g, '变动星座')
				str = str.replace(/可变星座/g, '变动星座')
				str = str.replace(/继承房屋/g, '续宫')
				str = str.replace(/节奏的房屋/g, '果宫')
				str = str.replace(/南部节点/g, '南交点')
				str = str.replace(/海图/g, '星盘')
				str = str.replace(/平方/g, '刑相位')
				str = str.replace(/节点/g, '月交点')
				str = str.replace(/直接移动/g, '顺行')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/正方形/g, '刑相位')
				str = str.replace(/人马/g, '射手')
				str = str.replace(/反中情局/g, '映点')
				str = str.replace(/尖端/g, '宫头')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/当地人/g, '盘主')
				str = str.replace(/流动的/g, '良好的')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/南节点/g, '南交点')
				str = str.replace(/空气/g, '风')
				str = str.replace(/三倍尺/g, '三分主宰星')
				str = str.replace(/地球/g, '土')
				str = str.replace(/墨丘利/g, '水星')
				str = str.replace(/汞/g, '水星')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/部门/g, '领域')
				str = str.replace(/运输/g, '行运')
				str = str.replace(/对齐/g, '准确成相位')
				str = str.replace(/对准/g, '准确成相位')
				str = str.replace(/过境/g, '流年')
				str = str.replace(/处理死记硬背/g, '例行公事')
				str = str.replace(/过渡/g, '行运')
				str = str.replace(/凌日/g, '行运')
				str = str.replace(/滑进/g, '进入')
				str = str.replace(/船员/g, '职员')
				str = str.replace(/螃蟹/g, '巨蟹座')
				str = str.replace(/.*座偶像/g, '')
				str = str.replace(/.*图标/g, ' ')
				str = str.replace(/地区/g, '领域')
				str = str.replace(/间歇/g, '一段时间')
				str = str.replace(/生长/g, '良好发展')
				str = str.replace(/口音/g, '重点')
				str = str.replace(/奇龙/g, '凯龙')
				str = str.replace(/希拉龙/g, '凯龙')
				str = str.replace(/奇隆/g, '凯龙')
				str = str.replace(/希龙/g, '凯龙')
				str = str.replace(/出生表/g, '出生星盘')
				str = str.replace(/火灾星座/g, '火象星座')
				str = str.replace(/空中星座/g, '风象星座')
				str = str.replace(/主宰者/g, '主宰星')
				str = str.replace(/尖顶/g, '宫头')
				str = str.replace(/红衣主教/g, '本位星座')
				str = str.replace(/圆润/g, '开心')
				str = str.replace(/行星方面/g, '行星相位')
				str = str.replace(/晚餐/g, '晚上')
				str = str.replace(/会引导你/g, '你会')
				str = str.replace(/宫尖/g, '宫头')
				str = str.replace(/纳塔尔/g, '本命')
				str = str.replace(/乌拉纳斯/g, '天王星')
				str = str.replace(/娜塔玛斯/g, '火星')
				str = str.replace(/星体配置/g, '星象')
				str = str.replace(/座星座/g, '座')
				str = str.replace(/天空中行星的运动/g, '天象')
				str = str.replace(/行星的运动/g, '天象')
				str = str.replace(/交通/g, '天象')
				str = str.replace(/天体的位置/g, '天象')
				str = str.replace(/倒退/g, '逆行')
				str = str.replace(/水银/g, '水星')
				str = str.replace(/后旋/g, '逆行')
				str = str.replace(/下旋/g, '逆行')
				str = str.replace(/三分之一/g, '三合')
				str = str.replace(/出生星图/g, '出生星盘')
				str = str.replace(/新月亮/g, '新月亮')
				str = str.replace(/固定星/g, '恒星')
				str = str.replace(/逆行站/g, '逆行停滞')
				str = str.replace(/倒退/g, '倒退')
				str = str.replace(/水星站/g, '水星停滞')
				str = str.replace(/方形/g, '刑相位')
				str = str.replace(/符号/g, '星座')
				str = str.replace(/方格/g, '刑相位')
				str = str.replace(/广场/g, '刑相位')
				str = str.replace(/六分符/g, '六分相位')
				str = str.replace(/利奥/g, '狮子座')
		// 		str = str.replace(/Get an Accurate Tarot Reading/g, '')
		// 		str = str.replace(/Find out with a Tarot Reading/g, '')
		// 		str = str.replace(
		// 			/You are right where you belong with a psychic love reading.  Chat with a psychic for free!/g,
		// 			'')
		// 		str = str.replace(/Chat with a psychic now for instant answers.Read More Horoscopes:/g, '')
		// 		str = str.replace(/Daily\n/g, '')
		// 		str = str.replace(/Flirt\n/g, '')
		// 		str = str.replace(/Finance\n/g, '')
		// 		str = str.replace(/Singles Love\n/g, '')
		// 		str = str.replace(/Couples Love\n/g, '')
		// 		str = str.replace(/Work\n/g, '')
		// 		str = str.replace(/Romantic\n/g, '')
		// 		str = str.replace(/SexScope\n/g, '')
		// 		str = str.replace(/Business\n/g, '')
		// 		str = str.replace(/Flirt\n/g, '')
		// 		str = str.replace(/Finance\n/g, '')
		// 		str = str.replace(/Chinese\n/g, '')
		// 		str = str.replace(/Flirt\n/g, '')
		// 		str = str.replace(/Finance\n/g, '')
		// 		str = str.replace(/Tarot\n/g, '')
		// 		str = str.replace(/Psychics/g, '')
		// 		str = str.replace(/Finance\n/g, '')
		// 		str = str.replace(/Still confused about your relationship？/g, '')
		// 		str = str.replace(/You are right where you belong with a psychic love reading./g,
		// 			' Chat with a psychic now for instant answers.')
		// 		str = str.replace(/Is your relationship worth fighting for？/g,
		// 			' Find out with a Free Tarot Reading.')
		// 		str = str.replace(
		// 			/Take this relationship questionnaire and find local compatible singles!/g, '')
		// 		str = str.replace(
		// 			/Try the new eharmony compatibility quiz and get matched with local singles today!/g,
		// 			'')
		// 		str = str.replace('\n\n\n\n \n\n\n\n\n\n\n\n\n', '')
		// 		str = str.replace('\n\n\n\n\n\n', '')
		
		// 		str = str.replace(' Your Love', ' \nYour Love')
		// 		str = str.replace('Your Career\n', '\nYour Career\n')
		// 		str = str.replace(/Read More Horoscopes:/g, '')
		// 		str = str.replace(/Read More Horoscopes:/g, '')
		// 		str = str.replace(/朱诺/g, '婚神星')
		// 		str = str.replace(
		// 			/For a more in depth and personalised astrological analysis， check your Daily Natal.Taurus daily horoscopeTaurus monthly horoscopeTaurus yearly horoscopeSee also:Today’s horoscopeTomorrow&#39；s HoroscopeMonthly HoroscopeYearly horoscopeYour Zodiac CharacteristicsYour Ascendant Characteristics" ，/g,
		// 			'')
		// 		str = str.replace(
		// 			/For a more in depth and personalised .* CharacteristicsYour Ascendant Characteristics" ，/g,
		// 			'')
		// 		str = str.replace(/Weekly./g, '')
				str = str.replace(/后代/g, '下降')
				str = str.replace(/主星座/g, '本位星座')
				str = str.replace(/基本星座/g, '本位星座')
				str = str.replace(/恒星座/g, '固定星座')
				str = str.replace(/星球/g, '行星')
				str = str.replace(/图表/g, '星盘')
				str = str.replace(/图轮/g, '星盘')
				str = str.replace(/轮子/g, '星盘')
				str = str.replace(/房子/g, '宫位')
				str = str.replace(/星体能量/g, '星象')
				str = str.replace(/行星流年/g, '星象')
				str = str.replace(/行星的大气层/g, '星象')
				// str = str.replace(/http：///g,'http://')
				str = str.replace(/----------------------------------	星象/g, '')
				str = str.replace(/可变/g, '变动')
				str = str.replace(/火相/g, '火象')
				str = str.replace(/方位角/g, '相位')
				str = str.replace(/成方/g, '成刑相位')
				str = str.replace(/对位/g, '冲相位')
				str = str.replace(/直角/g, '刑相位')
				str = str.replace(/对话/g, '商谈')
				str = str.replace(/尝试/g, '试着')
				str = str.replace(/将带给您/g, '会有')
				str = str.replace(/将与带给您/g, '会有')
				str = str.replace(/意外/g, '临时')
				str = str.replace(/广播/g, '传播')
				str = str.replace(/不可否认/g, '难得')
				str = str.replace(/於/g, '于')
				str = str.replace(/後/g, '后')
				str = str.replace(/後/g, '后')
				str = str.replace(/舆/g, '与')
				str = str.replace(/太阳回归图/g, '太阳返照星盘')
				str = str.replace(/麽/g, '么')
				str = str.replace(/蠍/g, '蝎')
				str = str.replace(/人马座/g, '射手座')
				str = str.replace(/土著人/g, '盘主')
				str = str.replace(/土著/g, '盘主')
				str = str.replace(/月亮大厦/g, '月宿')
				str = str.replace(/纳夏特拉/g, '月宿')
				str = str.replace(/兼容性/g, '感情匹配度')
				str = str.replace(/选举占星术/g, '择日占星术')
				str = str.replace(/原住民/g, '盘主')
				str = str.replace(/次级进展/g, '次限')
				str = str.replace(/太阳弧方向/g, '太阳弧')
				str = str.replace(/太阳返回图/g, '太阳返照')
				str = str.replace(/相容性/g, '匹配度')
				str = str.replace(/本生/g, '本命')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/合成的/g, '组合盘')
				str = str.replace(/复合图/g, '组合盘')
				str = str.replace(/复合星盘/g, '组合盘')
		
				// str = str.replace('标题1', '<p class="img_box"> <img src="http://ke.12sign.top/img/01h.png"></p>')
				// str = str.replace('标题2', '<p class="img_box"> <img src="http://ke.12sign.top/img/02h.png"></p>')
				// str = str.replace('标题3', '<p class="img_box"> <img src="http://ke.12sign.top/img/03h.png"></p>')
				// str = str.replace('标题4', '<p class="img_box"> <img src="http://ke.12sign.top/img/04h.png"></p>')
				// str = str.replace('标题5', '<p class="img_box"> <img src="http://ke.12sign.top/img/05h.png"></p>')
				// str = str.replace('标题6', '<p class="img_box"> <img src="http://ke.12sign.top/img/06h.png"></p>')
				// str = str.replace('标题7', '<p class="img_box"> <img src="http://ke.12sign.top/img/07h.png"></p>')
				// str = str.replace('标题8', '<p class="img_box"> <img src="http://ke.12sign.top/img/08h.png"></p>')
				// str = str.replace('标题9', '<p class="img_box"> <img src="http://ke.12sign.top/img/09h.png"></p>')
				// str = str.replace('标题10', '<p class="img_box"> <img src="http://ke.12sign.top/img/10h.png"></p>')
		
				str = str.replace('Four Quarters （Padas）', '四部分')
				str = str.replace(/人马/g, '射手座')
				str = str.replace(/星象图/g, '星盘')
				str = str.replace(/出生图/g, '星盘')
		
				str = str.replace(/房屋/g, '宫位')
				str = str.replace(/外观方面/g, '相位')
				str = str.replace(/Sun Sign/g, '太阳星座')
				str = str.replace(/集成电路/g, '天底')
				str = str.replace(/后裔/g, '下降点')
				str = str.replace(/对立/g, '冲相位')
				str = str.replace(/相反/g, '冲相位')
				str = str.replace(/反对/g, '冲相位')
				str = str.replace(/对面/g, '冲相位')
				str = str.replace(/相对的/g, '冲相位')
		
				str = str.replace(/直接的/g, '顺行的')
				str = str.replace(/相对/g, '冲相位')
				str = str.replace(/结合/g, '合相')
				str = str.replace(/巨蟹座：/g, '巨蟹座\n')
				str = str.replace(/狮子：/g, '狮子座\n')
				str = str.replace(/处女座：/g, '处女座\n')
				str = str.replace(/天秤：/g, '天秤座\n')
				str = str.replace(/摩羯座：/g, '摩羯座\n')
				str = str.replace(/双鱼座：/g, '双鱼座\n')
				str = str.replace(/月球/g, '月亮')
				str = str.replace(/行星的排列/g, '天象')
				str = str.replace(/行星配置/g, '天象')
				str = str.replace('â€™', "'")
				str = str.replace('â€”', '-')
				str = str.replace('â€“', '-')
				str = str.replace('â€œ', "'")
				str = str.replace('â€', "'")
				// str = str.replace(';','；')
				// str = str.replace(',','，')
				// str = str.replace(',','，')
				// str = str.replace('?','？')
				str = str.replace(/Daily/g, '')
				// str = str.replace('!','！')
				// str = str.replace(')','）')
				// str = str.replace('(','（')
				str = "<div class='blog_box' style='box-sizing: border-box;'>" + str +'</div>'
		
			}
			console.log(str)
			KindEditor.instances[0].html(str)
			$('#ke').val(str)
		});
		
		//标签写入
		var label_list = {$labelAccessColumn|json_encode};
		var label_id = [];

		for (var leba_id in label_list) {
			label_id.push(label_list[leba_id]['id'])
		}

		$(".label_ids").val(label_id.join(','));
		$(".lable_add").click(function() {
			var id = parseInt($(this).attr('label_id'));
			console.log(label_id);
			console.log(id);
			if (label_id.indexOf(id) == -1) {
				label_id.push(id);
				$(".label_ids").val(label_id.join(','));
				$(".label_html").append("<span label_id='" + id +
					"'class='lable_there label label-info'>" + $(this).text() + "</span>");
			}
		});
		$(document).on('dblclick', '.lable_there', function() {
			var id = parseInt($(this).attr('label_id'));
			label_id.splice(label_id.indexOf(id), 1);
			$(this).remove();
			$(".label_ids").val(label_id.join(','));
		});


	});
</script>
