<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<ul id="myTab" class="nav nav-tabs">
			<li class="active"><a href="#home" data-toggle="tab">基础信息</a></li>
			<li><a href="#seo" data-toggle="tab">SEO配置</a></li>
		</ul>
		<div class="box-body">
			<div id="myTabContent" class="tab-content">
				<div class="tab-pane fade in active" id="home">
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<label>名称</label>
								<span class="">（名称）</span><input class="form-control" name="name" placeholder="请输入名称"
								                                 value="{$info['name']|default=''}" type="text">
							</div>
						</div>
						
						<div class="col-md-6">
							<div class="form-group">
								<label>上级导航</label>
								<span>（所属的上级导航）</span>
								<select name="pid" class="form-control">
									<option value="0">顶级导航</option>
									{volist name='list' id='vo'}
									<option value="{$vo.id}">{$vo.name}</option>
									{/volist}
								</select>
							</div>
						</div>
						
						<div class="col-md-6">
							<div class="form-group">
								<label>是否主导航</label>
								<span class="">（是否主导航）</span>
								<div>
									<label class="margin-r-5"><input type="radio" name="mainly" value="1"> 是</label>
									<label><input type="radio" name="mainly" value="0"> 否</label>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label>图标</label>
								<span>（菜单小图标，为空则显示系统默认图标）</span>
								{assign name="icon" value="$info.icon|default=''" /}
								{:widget('icon/index', ['name' => 'icon', 'value' => $icon])}
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label>地址</label>
								<span class="">（地址）</span><input class="form-control" name="url" placeholder="请输入地址"
								                                 value="{$info['url']|default=''}" type="text">
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label>序号</label>
								<span class="">（序号）</span><input class="form-control" name="sort" placeholder="请输入序号"
								                                 value="{$info['sort']|default=''}" type="text">
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label>描述</label>
								<span class="">（描述）</span><input class="form-control" name="describe" placeholder="请输入描述"
								                                 value="{$info['describe']|default=''}" type="text">
							</div>
						</div>
					</div>
				</div>
				<div class="tab-pane fade" id="seo">
					<input type="hidden" name="seo[id]" value="{$info['seo_id']|default='0'}"/>
					<div class="row">
						<div class="col-md-12">
							<div class="form-group">
								<label>Title</label>
								<span class="">（网页标题）</span>
								<input class="form-control" name="seo[title]" placeholder="请输入文章网页标题"
								       value="{$seo['title']|default=''}" type="text">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>Keywords</label>
								<span class="">（网页关键词）</span>
								<input class="form-control" name="seo[keywords]" placeholder="请输入文章网页标题"
								       value="{$seo['keywords']|default=''}" type="text">
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<label>Description</label>
								<span class="">（网页描述）</span>
								<input class="form-control" name="seo[description]" placeholder="请输入文章网页标题"
								       value="{$seo['description']|default=''}" type="text">
							</div>
						</div>
					</div>
				</div>
			
			</div>
			<div class="box-footer">
				
				<input type="hidden" name="id" value="{$info['id']|default='0'}"/>
				
				{include file="layout/edit_btn_group"/}
			
			</div>
		
		</div>
		
		
		
	</div>
</form>
<script type="text/javascript">

    ob.setValue("mainly", {$info.mainly |default = 1});

</script>