<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 师资标签控制器
 */
class Label extends AdminBase
{

    /**
     * 师资标签列表
     */
    public function labelList()
    {

        $where = $this->logicLabel->getAdminWhere($this->param);

        $this->assign('list', $this->logicLabel->getLabelList($where, '', ''));

        return $this->fetch('label_list');
    }

    /**
     * 师资标签添加
     */
    public function labelAdd()
    {
        IS_POST && $this->jump($this->logicLabel->labelAdminEdit($this->param));
        return $this->fetch('label_edit');
    }

    /**
     * 师资标签编辑
     */
    public function labelEdit()
    {
        IS_POST && $this->jump($this->logicLabel->labelAdminEdit($this->param));

        $info = $this->logicLabel->getLabelInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('label_edit');
    }


    /**
     * 师资标签删除
     */
    public function labelDel($id = 0)
    {
        $this->jump($this->logicLabel->labelAdminDel(['id' => $id]));
    }

    /**
     * 数据状态设置
     */
    public function setStatus()
    {

        $this->jump($this->logicAdminBase->setStatus('Label', $this->param));
    }

    /**
     * 数据状态设置
     */
    public function setallstatus()
    {

        $this->jump($this->logicAdminBase->setAllStatus('Label', $this->param));
    }
}
