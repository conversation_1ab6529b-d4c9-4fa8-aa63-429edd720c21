<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 文件控制器
 */
class File extends AdminBase
{
    
    /**
     * 图片上传
     */
    public function pictureUpload()
    {
        
        $result = $this->logicFile->pictureUpload();

        return json($result);
    }
    
    /**
     * 文件上传
     */
    public function fileUpload()
    {
        
        $result = $this->logicFile->fileUpload();

        return json($result);
    }

    public function checkPictureExists() {
        $result = $this->logicFile->checkPictureExists($this->param);
        $return_result = [];
        if($result) {
            $return_result['code'] = 1;
            $return_result['msg'] = '该图片已存在';
            $return_result['data'] = $result;
        }else {
            $return_result['code'] = 0;
            $return_result['msg'] = '该图片不存在';
            $return_result['data'] = '';
        }
        return json($return_result);
    }

    public function checkFileExists() {
        $result = $this->logicFile->checkFileExists($this->param);
        $return_result = [];
        if($result) {
            $return_result['code'] = 1;
            $return_result['msg'] = '该文件已存在';
            $return_result['data'] = $result;
        }else {
            $return_result['code'] = 0;
            $return_result['msg'] = '该文件不存在';
            $return_result['data'] = '';
        }
        return json($return_result);
    }

    /**
     * 自定义路劲和文件名称上传
     */
    public function fileUploadOrig()
    {
        $path='';
        !empty($this->param['path']) && $path=$this->param['path'];

        $result = $this->logicFile->fileUpload('file','',$path);

        return json($result);
    }

    /**
     * 图片空间管理获取列表
     */
    public function fileManagerJson()
    {
        $path=date("Ymd");

        $path ='picture';

        !empty($this->param['path']) && $path = str_replace("/","",$this->param['path']);

        if($path=='picture'){

            $dataNumber=floor((time()-1582992000)/86400);

            for ($x=0; $x<$dataNumber; $x++) {
                $file_list[] = ['is_dir' => true, 'has_file' => true, 'filesize' => 0, 'dir_path' => 'picture',
                    'filename' => date("Ymd",(time()-86400*$x)),
                    'is_photo' => false, 'filetype' => '','datetime'=>0];
            }

            $data['current_dir_path'] = '';
            $data['current_url'] = 'https://media.xingpan.vip/upload/picture/';
            $data['file_list'] = $file_list;
            $data['moveup_dir_path'] = '';
            $data['total_count'] = $dataNumber-1;
            return json($data);

        }
        $where['path']= ['like', $path.'%'];
        $pictureColumn = $this->logicFile->getPictureColumn($where);

        $file_list = array();
        $filetype = 'png';
        foreach ($pictureColumn as $key => $value) {

            if (!empty($value['url'])) {
                if (preg_match("/^http(s)?:\\/\\/.+/", $value['url'])) {
                    $img_url = $value['url'];
                } else {
                    $img_url = config('static_domain') . SYS_DS_PROS . $value['url'];
                }
            } else if (!empty($value['path'])) {
                $img_url = DOMAIN . '/upload/picture/' . $value['path'].DS. $value['name'];
            }
            $file_ext = explode(".", $value['name']);

            if (!empty($file_ext[1])) {
                $filetype = $file_ext[1];
            }

            $file_list[] = ['is_dir' => false, 'has_file' => false, 'filesize' => $value['size'], 'dir_path' => $path,
                'filename' =>$value['name'],
                'is_photo' => true, 'filetype' => $filetype,'datetime'=>$value['create_time']];
        }

        $data['current_dir_path'] = $path;
        $data['current_url'] = 'https://media.xingpan.vip/upload/picture/'.$path.DS;
        $data['file_list'] = $file_list;
        $data['moveup_dir_path'] = 'picture';
        $data['total_count'] = count($pictureColumn);
        return json($data);
    }
}
