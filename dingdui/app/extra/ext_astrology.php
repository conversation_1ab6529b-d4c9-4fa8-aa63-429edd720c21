<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

// 扩展配置文件，系统研发过程中需要的配置建议放在此处，与框架相关配置分离

return [
    //星盘英文种类
    'chartEnglishType' => [
        '0'=>'current',
        '1'=>'natal',
        '2'=>'合盘',
        '3'=>'推运盘',
        '4'=>'生时矫正',
        '5'=>'transit',
        '6'=>'secondaryLimit',
        '7'=>'secondaryLimitDouble',
        '8'=>'thirdProgressed',
        '9'=>'thirdProgressedDouble',
        '10'=>'solarreturn',
        '11'=>'solarreturnDouble',
        '12'=>'太阳返照比较盘2',
        '13'=>'solararc',
        '14'=>'lunarreturn',
        '15'=>'lunarreturnDouble',
        '16'=>'月亮返照盘2',
        '17'=>'developed',

        '19'=>'comparision',
        '20'=>'compositeSecprogr',
        '21'=>'composite',
        '22'=>'timesMidPoint',
        '23'=>'marks',

    ],

    //星盘种类
    'chartType' => [
        '0'=>'天象盘',
        '1'=>'本命盘',
        '2'=>'合盘',
        '3'=>'推运盘',
        '4'=>'生时矫正',
        '5'=>'行运盘',
        '6'=>'次限盘',
        '7'=>'次相比较盘',
        '8'=>'三限盘',
        '9'=>'三限比较盘',
        '10'=>'太阳返照盘',
        '11'=>'太阳返照比较盘1',
        '12'=>'太阳返照比较盘2',
        '13'=>'太阳弧盘',
        '14'=>'月亮返照盘',
        '15'=>'月亮返照盘1',
        '16'=>'月亮返照盘2',
        '17'=>'法达盘',
        '18'=>'法达盘2',
        '19'=>'比较盘1',
        '20'=>'比较盘2',
        '21'=>'组合盘',
        '22'=>'时空中点盘',
        '23'=>'马克思盘',
        '24'=>'马克思盘2',
        '25'=>'比较次限盘',
        '26'=>'比较次限盘2',
        '97'=>'综合1',
        '98'=>'综合2',
        '99'=>'综合3'
    ],

    //相位名称    var phase_list={0:'合相',30:'十二分相',36:'十分相',45:'八分相',60:'六合相',72:'五分相',90:'刑相',120:'拱相',135:'补八分相',144:'补五分相',150:'梅花相',180:'冲相'};
    'allow_degree_cn' => [
        '0'=>'合',
        '30'=>'十二分',
        '36'=>'十分',
        '45'=>'八分',
        '60'=>'六合',
        '72'=>'五分',
        '90'=>'刑',
        '120'=>'拱',
        '135'=>'补八分',
        '144'=>'补五分',
        '150'=>'梅花',
        '180'=>'冲'
    ],
    'PlanetNamesSimple' => array('0' => '日',
        '1' => '月',
        '2' => '水',
        '3' => '金',
        '4' => '火',
        '5' => '木',
        '6' => '土',
        '7' => '天',
        '8' => '海',
        '9' => '冥',
        '10' => '升',
        '11' => '中'),
    'SignNamesCN' => array("白羊", "金牛", "双子", "巨蟹", "狮子", "处女", "天秤", "天蝎", "射手", "摩羯", "水瓶", "双鱼"),

    'SignNamesSimple' => array("羊", "牛", "双", "蟹", "狮", "处", "秤", "蝎", "射", "摩", "瓶", "鱼"),
    //星宿
    'xingxiu' => array(['室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎'],
        ['奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃'],
        ['胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕'],
        ['毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参'],
        ['参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼'],
        ['鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星'],
        ['张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸'],
        ['角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐'],
        ['氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心'],
        ['心', '尾', '箕', '斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕'],
        ['斗', '女', '虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚'],
        ['虚', '危', '室', '壁', '奎', '娄', '胃', '昴', '毕', '觜', '参', '井', '鬼', '柳', '星', '张', '翼', '轸', '角', '亢', '氐', '房', '心', '尾', '箕', '斗', '女', '虚', '危', '室']
    ),

    //星宿单个
    'xingxiu_word' => array("角", "轸", "翼", "张", "星", "柳", "鬼", "井", "参", "觜", "毕", "昴", "胃", "娄", "奎", "壁", "室", "危", "虚", "女", "斗", "箕", "尾", "心", "房", "氐", "亢"),

    //关系
    'Connection' => array("危", "安", "衰", "荣", "业", "亲", "友", "坏", "成", "危", "安", "衰", "荣", "命", "亲", "友", "坏", "成", "危", "安", "衰", "荣", "胎", "亲", "友", "坏", "成"),

    //远近
    'FarNear' => array("远距离", "远距离", "远距离", "远距离", "", "中距离", "中距离", "中距离", "中距离", "近距离", "近距离", "近距离", "近距离",
        "", "近距离", "近距离", "近距离", "近距离", "中距离", "中距离", "中距离", "中距离", "", "远距离", "远距离", "远距离", "远距离"),

    //星盘语料类型
    'corpusType' => [
        0=>'无',1=>'单行星',2=>'单星座',3=>'单宫位','4'=>'行星落宫位','5'=>'行星落星座','6'=>'行星与行星相位'
    ],
];
