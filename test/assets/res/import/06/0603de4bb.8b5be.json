[1, ["ecpdLyjvZBwrvm+cedCcQy", "440RjU4JhEEJLGsmvEbM1v", "1f061b705", "3cAZ/1aL5Ip7zo7Ao5FhIa", "3ae7efMv1CLq2ilvUY/tQi", "777uvitn1Lp7DuMlE1OUZa", "f7njjgEjlGCK6nZwGTM6Mt", "4d7yoLpGhL4LdeC+0NWB8i", "4cojHNV2RL44q+D/DUBdXD", "1f1iT4BNFB47y3dAtt0PDj", "42nR4TEtlLvY8iS7+6QeeK"], ["node", "_spriteFrame", "_textureSetter", "root", "itemRed", "maskSelected", "maskNode", "tab", "imag", "num", "bg", "clickArea", "data", "_N$file"], [["cc.Node", ["_name", "_active", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_color"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["ec9b2VXqb5G6KuBtM0tMCNc", ["node", "clickArea", "bg", "num", "imag", "tab", "maskNode", "maskSelected", "itemRed"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Animation", ["node"], 3, 1], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -5, 1, 3]], [[4, 0, 1, 2, 2], [1, 0, 3, 4, 5, 2], [1, 3, 4, 5, 1], [10, 0, 1], [0, 0, 1, 5, 6, 2, 3], [0, 0, 5, 3, 2, 8, 4, 2], [0, 0, 5, 3, 2, 4, 2], [2, 0, 1, 2, 3, 4, 2], [5, 0, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 1, 5, 3, 2, 4, 3], [0, 0, 5, 3, 2, 4, 7, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [2, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 6, 5, 2], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [7, 0, 1], [4, 1, 2, 1], [1, 3, 4, 1], [1, 0, 1, 3, 4, 3], [1, 2, 0, 3, 4, 5, 3], [8, 0, 1, 2, 2], [9, 0, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9]], [[[[8, "bagItem"], [9, "node", [-12, -13, -14, -15, -16, -17, -18, -19, -20], [[15, -10, -9, -8, -7, -6, -5, -4, -3, -2], [16, -11]], [17, -1, 0], [5, 119, 119]], [13, "imag", 1, [[[18, -21, [2]], -22], 4, 1], [0, "f9e/SSg1dN8b5W8OPa4JrB", 1, 0], [5, 66, 65], [0.129, -0.226, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "mask", false, 1, [-23, -24], [0, "97a6FfzpdETLmw8hzhaWoo", 1, 0]], [4, "maskSelected", false, 1, [-25, -26], [0, "cdOKRQugNHQ6KUq4rvQA5g", 1, 0]], [7, "bg", 1, [[[1, 0, -27, [0], 1], -28], 4, 1], [0, "90Zer1WydCBYCWUp2Re/6f", 1, 0], [5, 119, 119]], [14, "num", 1, [[-29, [21, 3, -30, [4, 4281341443]]], 1, 4], [0, "550ASGVNBJWbr+YqE+WddB", 1, 0], [5, 51, 25], [0, 1, 0], [54.133, -49.838, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "tab", 1, [[[19, 0, false, -31, [4]], -32], 4, 1], [0, "b11tXjB+hCnJLJY2X6IhRQ", 1, 0], [5, 119, 119]], [10, "isGetStaut", false, 1, [[20, 1, 0, -33, [5], 6], [22, -34]], [0, "86FhEruPRFHKVPMyzmjWF6", 1, 0], [5, 133, 133]], [11, "red", 1, [[1, 0, -35, [7], 8]], [0, "0a6RgUhyVBhLt0NQdJrL4h", 1, 0], [5, 31, 34], [44.209, 46.16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, 5], [3, 2], [23, "999", 25, 25, false, -12, 1, 2, 1, 6, [3]], [3, 7], [12, "lockIcon", false, 1, [[2, -36, [9], 10]], [0, "de+0owQl1GibhEzAb6nKCC", 1, 0], [5, 33, 39], [44.878, 49.958, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "singleColor", 3, [[1, 0, -37, [11], 12]], [0, "e5dgSmUqJApZ7oGgVvEhrx", 1, 0], [4, 4278190080], [5, 115, 115]], [6, "img_weijianding", 3, [[2, -38, [13], 14]], [0, "b2ujeK5RdGiLwirm95tuu6", 1, 0], [5, 38, 54]], [5, "singleColor", 4, [[1, 0, -39, [15], 16]], [0, "37dQALPgpPL7b1Qd2qzveg", 1, 0], [4, 4278190080], [5, 115, 115]], [6, "img_weijianding", 4, [[2, -40, [17], 18]], [0, "fftnn24Y9FyYrAFgQKFiiH", 1, 0], [5, 63, 55]]], 0, [0, 3, 1, 0, 4, 9, 0, 5, 4, 0, 6, 3, 0, 7, 13, 0, 8, 11, 0, 9, 12, 0, 10, 10, 0, 11, 2, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 14, 0, -8, 3, 0, -9, 4, 0, 0, 2, 0, -2, 11, 0, -1, 15, 0, -2, 16, 0, -1, 17, 0, -2, 18, 0, 0, 5, 0, -2, 10, 0, -1, 12, 0, 0, 6, 0, 0, 7, 0, -2, 13, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 12, 1, 40], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12], [-1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 13], [0, 3, 0, 4, 0, 0, 5, 0, 6, 0, 7, 0, 1, 0, 8, 0, 1, 0, 9, 10]], [[{"name": "img_zhanling_lock", "rect": [299, 803, 33, 39], "offset": [0, 0], "originalSize": [33, 39], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [2]], [[{"name": "img_xuanzhong_guang", "rect": [3, 753, 99, 96], "offset": [0, 0], "originalSize": [99, 96], "capInsets": [28, 28, 28, 28]}], [3], 0, [0], [2], [2]]]]