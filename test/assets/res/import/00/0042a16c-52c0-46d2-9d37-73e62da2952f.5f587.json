[1, ["92ah1vnIpNX6qNY3SJK3v7", "7adcN5igRBPpxRVmsEfvi3", "afOljcH4VDLoIrX0D1r9LI", "e3teg1K+FL17/iUC/tcn/q", "7bSxvVgG1PC48lhKnSCN+x", "60yVyDXb1A1pl93GtBpL+/", "dadYjRs0NOh7SZ+2baEt3N", "e8Xk2M9ZxJe7Fa5Dz/64FU", "1fvrS4ZEBE9JfJDqBmB/Jz", "e2oDh1GAFN5Y8Oy26+B6sE", "77pxIfKeZBsJzK9/badByA", "39qwxzxhtLbpKEeOsLuMRy", "9cnUtCRHxPo7fcEyggTpqF"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "mine", 0.375, 24, 0.5, 2, [{"props": {"y": [{"frame": 0, "value": -13}], "x": [{"frame": 0, "value": 6.56}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.041666666666666664}, "value", 6, 1], [{"frame": 0.08333333333333333}, "value", 6, 2], [{"frame": 0.125}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4], [{"frame": 0.20833333333333334}, "value", 6, 5], [{"frame": 0.25}, "value", 6, 6], [{"frame": 0.2916666666666667}, "value", 6, 7], [{"frame": 0.3333333333333333}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "paths", 11, [{}, "futou", 11, [{"props": {"position": [{"frame": 0, "curve": "constant", "value": [-11.437, 37.692, 0]}, {"frame": 0.041666666666666664, "curve": "constant", "value": [5.267, 54.902, 0]}, {"frame": 0.08333333333333333, "curve": "constant", "value": [51.548, 68.522, 0]}, {"frame": 0.125, "curve": "constant", "value": [59.986, 56.415, 0]}, {"frame": 0.16666666666666666, "curve": "constant", "value": [61.376, 49.573, 0]}, {"frame": 0.20833333333333334, "curve": "constant", "value": [20.549, 86.895, 0]}, {"frame": 0.25, "curve": "constant", "value": [-23.194, 22.163, 0]}, {"frame": 0.2916666666666667, "curve": "constant", "value": [-27.929, 24.636, 0]}, {"frame": 0.3333333333333333, "value": [-22.341, 25.884, 0]}], "angle": [{"frame": 0, "value": 54.778999999999996, "curve": "constant"}, {"frame": 0.041666666666666664, "value": -10, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -74.961, "curve": "constant"}, {"frame": 0.125, "value": -111.61, "curve": "constant"}, {"frame": 0.16666666666666666, "value": -122.782, "curve": "constant"}, {"frame": 0.20833333333333334, "value": -33.691, "curve": "constant"}, {"frame": 0.25, "value": 88.027, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 68.243, "curve": "constant"}, {"frame": 0.3333333333333333, "value": 78.275}], "active": [{"frame": 0, "value": true}], "anchorX": [{"frame": 0, "value": 0.42}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 9]], 11]]]], "futou/shou", 11, [{"props": {"position": [{"frame": 0, "value": [24.626, -26.576, 0]}], "active": [{"frame": 0, "value": true}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 10]], 11]]]], "futou/wei", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.25, "value": 255, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0.20833333333333334, "value": 12.253, "curve": "constant"}, {"frame": 0.25, "value": -87.206}], "position": [{"frame": 0.20833333333333334, "curve": "constant", "value": [79.325, 25.135, 0]}, {"frame": 0.25, "value": [75.546, 32.245, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334, "curve": "constant"}, "value", 8, [1, 1.7, 1.238, 1]], [{"frame": 0.25}, "value", 8, [1, 1.2, 1.67, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 11], [{"frame": 0.25}, "value", 6, 12]], 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]