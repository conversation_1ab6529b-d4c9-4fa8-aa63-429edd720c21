[1, ["ecpdLyjvZBwrvm+cedCcQy", "3eJYcUMHdGHauutMteV/TT", "a2MjXRFdtLlYQ5ouAFv/+R", "2eGyYOWX5JE7Cq3+bQuOoA"], ["node", "_spriteFrame", "root", "rigidBody", "body", "skin", "anim", "data"], [["cc.Node", ["_name", "_parent", "_components", "_prefab", "_children", "_contentSize"], 2, 1, 9, 4, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_children", "_components", "_prefab", "_trs"], 1, 2, 12, 4, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.Animation", ["playOnLoad", "node", "_clips"], 2, 1, 12], ["6f987iEmGpFs7oQJ+Xpz5XM", ["node"], 3, 1], ["cc.<PERSON>", ["tag", "node", "_size"], 2, 1, 5], ["e6f05YZAHRGiorL9OHBeRYw", ["node"], 3, 1], ["f6123H0uppA2Zmo5qpWWxKq", ["node", "anim", "skin", "body", "rigidBody"], 3, 1, 1, 1, 1, 1], ["cc.RigidBody", ["_gravityScale", "_fixedRotation", "bullet", "awakeOnLoad", "node"], -1, 1], ["cc.PhysicsBoxCollider", ["node", "_offset", "_size"], 3, 1, 5, 5]], [[2, 0, 1, 2, 2], [3, 0, 2], [4, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 4, 2, 3, 2], [0, 0, 1, 2, 3, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 1, 3, 4, 3], [1, 2, 0, 3, 4, 5, 3], [6, 0, 1, 2, 2], [2, 1, 2, 1], [7, 0, 1], [8, 0, 1, 2, 2], [9, 0, 1], [10, 0, 1, 2, 3, 4, 1], [11, 0, 1, 2, 3, 4, 5], [12, 0, 1, 2, 1]], [[1, "4"], [2, "Player", 2, [-9, -10], [[[13, -6, -5, -4, -3, -2], -7, [15, -8, [0, 0, 50], [5, 50, 100]]], 4, 1, 4], [9, -1, 0], [0, 48, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Body", 1, [-12], [[10, -11]], [0, "bdu76iD25KvK9dn7FsTup5", 1, 0]], [4, "FootTrigger", 1, [[11, 1, -13, [5, 20, 10]], [7, false, 0, -14, [2], 3], [12, -15]], [0, "4fLzOkV/FD+6F02lqhlLMj", 1, 0], [5, 20, 10]], [5, "Skin", 2, [-16, -17], [0, "016Qk3+TtGbbQ2jhFubcDw", 1, 0], [5, 264, 216], [0, 0.5, 0], [2, -87.363, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [6, 2, false, 4, [0]], [8, true, 4, [[1, null, null, null, null, null, null, null], 6, 0, 0, 0, 0, 0, 0, 0]], [14, 0, true, true, false, 1]], 0, [0, 2, 1, 0, 3, 7, 0, 4, 2, 0, 5, 5, 0, 6, 6, 0, 0, 1, 0, -2, 7, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, -2, 6, 0, 7, 1, 17], [0, 0, 0, 0, 5], [-1, -1, -1, 1, 1], [0, 1, 0, 2, 3]]