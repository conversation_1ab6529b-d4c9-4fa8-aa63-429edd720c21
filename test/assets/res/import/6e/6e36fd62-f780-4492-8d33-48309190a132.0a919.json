[1, ["dcPQkPOe9Hrr4+itXxdeeb", "9eS2R6OaNNKodNHxDyVAzg", "70r7/yb35JWbOWTDJuZx6R", "54l1QnlDBAxqzkX7MvFAXG", "7bX7F91uRClYACwC5BJtRH", "ceuWOwlmVK4oAAzeqsGdPp", "f1j3gmW/RPi4ayg3uMpvhp", "43HUwQPC1OjrnkDUVBs7nh"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], 0, 11]], [[0, 0, 1, 2, 3, 4]], [[0, "effect_lzz_01_qy", 2.9166666666666665, 24, [{}, "paths", 11, [{"qy": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 2.9166666666666665, "value": 255}]}}}, "qy/dianqi_001", 11, [{"props": {"opacity": [{"frame": 0.4583333333333333, "value": 0, "curve": "constant"}, {"frame": 0.5, "value": 255}, {"frame": 1.7916666666666667, "value": 255}, {"frame": 1.875, "value": 0}], "angle": [{"frame": 0.7916666666666666, "value": 0, "curve": "constant"}, {"frame": 0.8333333333333334, "value": 180, "curve": "constant"}, {"frame": 1.4583333333333333, "value": 0, "curve": "constant"}, {"frame": 1.5, "value": 180, "curve": "constant"}], "position": []}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.5}, "value", 6, 0], [{"frame": 0.5416666666666666}, "value", 6, 1], [{"frame": 0.5833333333333334}, "value", 6, 2], [{"frame": 0.625}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4], [{"frame": 0.7083333333333334}, "value", 6, 5], [{"frame": 0.75}, "value", 6, 6], [{"frame": 0.7916666666666666}, "value", 6, 7], [{"frame": 0.8333333333333334}, "value", 6, 8], [{"frame": 0.875}, "value", 6, 9], [{"frame": 0.9166666666666666}, "value", 6, 10], [{"frame": 0.9583333333333334}, "value", 6, 11], [{"frame": 1}, "value", 6, 12], [{"frame": 1.0416666666666667}, "value", 6, 13], [{"frame": 1.0833333333333333}, "value", 6, 14], [{"frame": 1.125}, "value", 6, 15], [{"frame": 1.1666666666666667}, "value", 6, 16], [{"frame": 1.2083333333333333}, "value", 6, 17], [{"frame": 1.25}, "value", 6, 18], [{"frame": 1.2916666666666667}, "value", 6, 19], [{"frame": 1.3333333333333333}, "value", 6, 20], [{"frame": 1.375}, "value", 6, 21], [{"frame": 1.4166666666666667}, "value", 6, 22], [{"frame": 1.4583333333333333}, "value", 6, 23], [{"frame": 1.5}, "value", 6, 24], [{"frame": 1.5416666666666667}, "value", 6, 25], [{"frame": 1.5833333333333333}, "value", 6, 26], [{"frame": 1.625}, "value", 6, 27], [{"frame": 1.6666666666666667}, "value", 6, 28], [{"frame": 1.7083333333333333}, "value", 6, 29], [{"frame": 1.75}, "value", 6, 30], [{"frame": 1.7916666666666667}, "value", 6, 31], [{"frame": 1.8333333333333333}, "value", 6, 32], [{"frame": 1.875}, "value", 6, 33]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "qy/dianqi_002", 11, [{"props": {"opacity": [{"frame": 0.4583333333333333, "value": 0, "curve": "constant"}, {"frame": 0.5, "value": 255}, {"frame": 1.7916666666666667, "value": 255}, {"frame": 1.875, "value": 0}], "angle": [{"frame": 0.75, "value": 135, "curve": "constant"}, {"frame": 0.7916666666666666, "value": -45, "curve": "constant"}, {"frame": 1.4166666666666667, "value": 135, "curve": "constant"}, {"frame": 1.4583333333333333, "value": -45, "curve": "constant"}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.5}, "value", 6, 34], [{"frame": 0.5416666666666666}, "value", 6, 35], [{"frame": 0.5833333333333334}, "value", 6, 36], [{"frame": 0.625}, "value", 6, 37], [{"frame": 0.6666666666666666}, "value", 6, 38], [{"frame": 0.7083333333333334}, "value", 6, 39], [{"frame": 0.75}, "value", 6, 40], [{"frame": 0.7916666666666666}, "value", 6, 41], [{"frame": 0.8333333333333334}, "value", 6, 42], [{"frame": 0.875}, "value", 6, 43], [{"frame": 0.9166666666666666}, "value", 6, 44], [{"frame": 0.9583333333333334}, "value", 6, 45], [{"frame": 1}, "value", 6, 46], [{"frame": 1.0416666666666667}, "value", 6, 47], [{"frame": 1.0833333333333333}, "value", 6, 48], [{"frame": 1.125}, "value", 6, 49], [{"frame": 1.1666666666666667}, "value", 6, 50], [{"frame": 1.2083333333333333}, "value", 6, 51], [{"frame": 1.25}, "value", 6, 52], [{"frame": 1.2916666666666667}, "value", 6, 53], [{"frame": 1.3333333333333333}, "value", 6, 54], [{"frame": 1.375}, "value", 6, 55], [{"frame": 1.4166666666666667}, "value", 6, 56], [{"frame": 1.4583333333333333}, "value", 6, 57], [{"frame": 1.5}, "value", 6, 58], [{"frame": 1.5416666666666667}, "value", 6, 59], [{"frame": 1.5833333333333333}, "value", 6, 60], [{"frame": 1.625}, "value", 6, 61], [{"frame": 1.6666666666666667}, "value", 6, 62], [{"frame": 1.7083333333333333}, "value", 6, 63], [{"frame": 1.75}, "value", 6, 64], [{"frame": 1.7916666666666667}, "value", 6, 65], [{"frame": 1.8333333333333333}, "value", 6, 66], [{"frame": 1.875}, "value", 6, 67]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 4, 5, 2, 3, 6, 7, 0, 1, 4, 5, 2, 3, 6, 7, 0, 1, 4, 5, 2, 3, 6, 7, 0, 1, 4, 5, 2, 3, 6, 7, 0, 1, 2, 3, 6, 7, 0, 1, 4, 5, 2, 3, 6, 7, 0, 1, 4, 5, 2, 3, 6, 7, 0, 1, 4, 5, 2, 3, 6, 7, 0, 1, 4, 5, 2, 3]]