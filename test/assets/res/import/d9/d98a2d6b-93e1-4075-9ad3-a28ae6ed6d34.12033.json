[1, ["ecpdLyjvZBwrvm+cedCcQy", "ddNJp530FNp5SCV8dZ0vkV", "cf57MmtiZLjJ8Bl6FcWj/B", "d9dkwSdGNJFK8Jvz7lzizp", "777hdgr7VKgZPJJACUcjJt", "42nR4TEtlLvY8iS7+6QeeK", "effLm/4+pIOaAUAuziKHwn", "09pr8gYHxFbJ4sOz5oPiH1", "29fu0eIcNNj78SNBokZejx", "50ZWJmXytPcbnplWq5/CVL", "28To8NthxH5JU//otAwEeM", "da/FiDPpNJA6FiYavKRdbD", "3fpR6hAT5Eubm90+snJwJj", "a9gljmMTdMYbNMaRH789Cz", "21Zeo5MvBC8IkHUpmaaBWL", "1bN5YWAqdML7MZqoI0RSYX", "ed+3HLCVhEubpg0JAqzTyU", "1b+v/SrZdIZKLtHxmfU0CR", "feKMBU/D5J/4g+aA8Ikpm0", "3axP4mKX5JmqVOAz2UJaiw", "190NQN59FP/bqe2flIrOYV", "9dpx/1MatKt7g6kt9Q0E9k", "2fXgmKkzJDHp8jkOJiuu5e", "983dbR6F5JLbzpqCo+MMp3", "04GrnpAy5CcaqVjakwLrw1", "c4zF2Vw9tLv6ZH3MqkWAdH", "a8Km4tDrtKMJdfMuqaGM6N", "7aUwX4CfhMcLLSJngWOFcS", "5dc9CUy91L8aDiyxz6YFlC", "a5Q5lh4rlDnp6xSM08sZfS", "a4nyCvLkVAeaOBF6p4anPh", "6bKLHDqA1Np5U2LhSRwnoh", "9ap6blto9KHr5E8Ng6azgR", "0ftpR+3vxBMKOovWVDzVz9", "163b7+409DiZ+asSyOi06j"], ["node", "_spriteFrame", "_N$file", "_parent", "heroCardArrayPref", "hero<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crystal", "heroPosLockPref", "root", "btnTactics", "lvPower", "btnNumber", "nodeCrystal", "defenderBtn", "attackerBtn", "heroMax<PERSON>um", "hero<PERSON><PERSON><PERSON><PERSON>", "contentNode", "stageNode", "btnType3", "btnType2", "btnType1", "btnAll", "autoUpBtn", "closeBtn", "adaptiveBgBox", "_N$content", "data"], [["cc.Node", ["_name", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint", "_eulerAngles"], 1, 4, 9, 5, 1, 7, 2, 5, 5, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_top", "_originalHeight", "_originalWidth", "_left", "_right", "alignMode", "_horizontalCenter", "_verticalCenter", "node"], -7, 1], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint", "_color"], 0, 1, 12, 4, 5, 7, 2, 5, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_spacingX", "_N$cacheMode", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["3039cCGLWFIeqCPOGQnWhje", ["buttonType", "checked_", "scaleMin", "scaleMax", "node", "normalNode", "selectedNode"], -1, 1, 1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingLeft", "_N$spacingX", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["cc.BlockInputEvents", ["node"], 3, 1], ["ab337gkteBCfZkYnIRIWXhI", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "cancelInnerEvents", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["a5bffqzNDdD/oJfqCgZCJig", ["node", "adaptiveBgBox", "closeBtn", "autoUpBtn", "btnAll", "btnType1", "btnType2", "btnType3", "stageNode", "contentNode", "hero<PERSON><PERSON><PERSON><PERSON>", "heroMax<PERSON>um", "attackerBtn", "defenderBtn", "nodeCrystal", "btnNumber", "lvPower", "btnTactics", "heroCardArrayPref", "hero<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crystal", "heroPosLockPref"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6]], [[6, 0, 1, 2, 2], [0, 0, 5, 3, 2, 4, 6, 2], [5, 2, 3, 4, 1], [5, 1, 0, 2, 3, 4, 3], [5, 0, 2, 3, 4, 2], [0, 0, 5, 7, 3, 2, 4, 6, 2], [12, 0, 1, 2, 2], [0, 0, 1, 5, 3, 2, 4, 6, 3], [9, 0, 1, 2, 3, 4, 5, 6, 2], [0, 0, 5, 3, 2, 4, 2], [11, 0, 1], [0, 0, 7, 2, 4, 6, 2], [1, 0, 2, 10, 3], [4, 0, 1, 4, 5, 6, 3], [3, 0, 3, 5, 1, 4, 2, 8, 9, 10, 7], [0, 0, 5, 7, 3, 2, 6, 2], [2, 0, 3, 8, 4, 5, 6, 7, 2], [4, 4, 1], [0, 0, 5, 3, 2, 8, 4, 2], [2, 0, 3, 4, 5, 6, 7, 2], [1, 0, 10, 2], [1, 0, 4, 3, 10, 4], [4, 0, 4, 5, 6, 2], [3, 0, 3, 5, 1, 6, 4, 2, 8, 9, 8], [8, 0, 2], [0, 0, 7, 3, 2, 4, 6, 2], [0, 0, 1, 5, 7, 3, 2, 4, 3], [0, 0, 5, 7, 3, 2, 4, 2], [0, 0, 1, 5, 7, 3, 2, 4, 6, 3], [0, 0, 7, 3, 2, 4, 9, 6, 2], [0, 0, 3, 2, 4, 9, 6, 2], [0, 0, 1, 5, 3, 2, 4, 6, 10, 3], [0, 0, 1, 5, 3, 2, 8, 4, 3], [0, 0, 7, 3, 2, 4, 2], [2, 0, 1, 2, 3, 8, 4, 5, 6, 7, 4], [2, 0, 3, 4, 5, 10, 6, 9, 7, 2], [2, 0, 3, 4, 5, 6, 9, 7, 2], [10, 0, 1], [1, 0, 2, 1, 4, 3, 10, 6], [1, 7, 0, 1, 10, 4], [1, 0, 3, 10, 3], [1, 0, 2, 1, 3, 10, 5], [1, 0, 5, 6, 2, 1, 4, 3, 10, 8], [1, 0, 1, 8, 10, 4], [1, 0, 5, 1, 10, 4], [1, 0, 5, 2, 10, 4], [1, 0, 4, 10, 3], [1, 0, 6, 2, 1, 9, 3, 10, 7], [6, 1, 2, 1], [4, 2, 3, 4, 3], [3, 0, 3, 1, 4, 2, 8, 9, 10, 6], [3, 0, 1, 2, 8, 9, 4], [3, 0, 3, 5, 1, 4, 2, 7, 8, 9, 10, 8], [7, 0, 1, 2, 5, 6, 4], [7, 3, 4, 5, 6, 3], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5, 6, 7, 7], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 1]], [[24, "arenaArrayUI"], [25, "node", [-21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31], [[21, 45, 768, 1334, -2], [57, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, 120, 121, 122, 123]], [48, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "pos10", [-33, -34, -35, -36, -37, -38, -39, -40, -41, -42], [[21, 45, 100, 100, -32]], [0, "30cNCZAchBRLd76/YdYU2J", 1, 0], [5, 768, 610]], [15, "top", 1, [-45, -46, -47, -48, -49, -50], [[12, 17, 45, -43], [10, -44]], [0, "be6bQ/3olO8JnoSt69c2nO", 1, 0], [0, 638, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "nodeCrystal", 1, [-53, -54, -55, -56], [[12, 1, 345, -51], [10, -52]], [0, "2fSsbAqkpGk6wDsz8iEnEI", 1, 0], [0, 338, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "da<PERSON><PERSON>", 1, [-59, -60, -61, -62], [[54, 156, 50, -57, [5, 768, 80]], [43, 20, 282.201, 88.529, -58]], [0, "d14vUb0K1L97CstCRU8i5c", 1, 0], [5, 768, 80], [88.529, -360.799, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "nodeNum", 512, false, 1, [-66, -67, -68], [[[12, 1, 522, -63], [10, -64], -65], 4, 4, 1], [0, "a44TUAtlxJ7qhwrM/PeHoC", 1, 0], [5, 100, 100], [0, 111, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "tacticsBtn", 1, [-73, -74], [[[3, 1, 0, -69, [118], 119], -70, [47, 33, 57.275000000000006, 519.278, 770.072, 117.072, 60, -71], [10, -72]], 4, 1, 4, 4], [0, "8aiO3rBD5P0b1MhyGUn957", 1, 0], [5, 180, 75], [236.72500000000002, 126.22199999999998, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bottom", 1, [-76, -77, -78], [[39, 2, 20, 206.632, -75]], [0, "eak7FjLnBB/YfmMwCCOLcv", 1, 0], [0, -476.368, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "item ScrollView", [-83], [[56, false, 0.75, 0.23, false, null, null, -80, -79], [41, 5, 25, 99, 330, -81], [3, 1, 0, -82, [33], 34]], [0, "36Jv+ed3hPA50y+Ntc2LYB", 1, 0], [5, 680, 276], [0, 0.5, 1], [0, 175, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "content", [[53, 1, 3, 30, -84, [5, 680, 470]], [20, 1, -85]], [0, "66v9LbUHZIL5Q50mZq9bRV", 1, 0], [5, 680, 470], [0, 0.5, 1], [0, 138, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "allLight", [-86, -87, -88], [0, "25mVYSYjpGTbWC/oVhe4yZ", 1, 0], [5, 123, 80], [1, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "type1Light", [-89, -90, -91], [0, "36VCSVFW9NZ7L34xRO0wbh", 1, 0], [5, 123, 80], [1, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "type2Light", [-92, -93, -94], [0, "d7OBw4gAlHgo/NNLqA81ED", 1, 0], [5, 123, 80], [1, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "type3Light", [-95, -96, -97], [0, "09wrROOkZNdoZlOCR6JYw/", 1, 0], [5, 123, 80], [1, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "stage", 1, [2], [[46, 41, 768, -98], [10, -99]], [0, "d9z76R7phFr4++vrZg3VEB", 1, 0], [5, 768, 610], [0, 378, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_hero_shangzhen", 1, [[3, 1, 0, -100, [0], 1], [37, -101], [38, 44, 16, 134, 768, 1334, -102]], [0, "87gUGm63FBRoWkJM3kShzp", 1, 0], [5, 768, 2048], [0, 475, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btn_ts", 8, [-105], [[[3, 1, 0, -103, [14], 15], -104], 4, 1], [0, "96T/xEdaRMnIAZpebB8gu5", 1, 0], [5, 260, 82], [-5.089, 507.195, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "attackerBtn", 8, [-107, -108], [-106], [0, "f2DVLCjKVNMaYUzztIhrIR", 1, 0], [5, 204, 112], [19, 40.424, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "normal", false, 18, [-110], [[3, 1, 0, -109, [18], 19]], [0, "8eRE0Hos5Fmp5JKne4yEpy", 1, 0], [5, 204, 82]], [5, "select", 18, [-112], [[3, 1, 0, -111, [22], 23]], [0, "a6n0QnSaxLu5pn304drlPk", 1, 0], [5, 204, 112], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "defenderBtn", 8, [-114, -115], [-113], [0, "acme2ebEJL3btwRlCvrBW/", 1, 0], [5, 204, 112], [228, 40.424, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "normal", 21, [-117], [[3, 1, 0, -116, [26], 27]], [0, "518FkDmpZMBq3IcOc79nFl", 1, 0], [5, 204, 82]], [28, "select", false, 21, [-119], [[3, 1, 0, -118, [30], 31]], [0, "fcUpzCc2FKmqZYrk4w4Xkz", 1, 0], [5, 204, 112], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "bg", 1, [9], [[[3, 1, 0, -120, [35], 36], -121], 4, 1], [0, "b6xYKkq7lCsaGWXDj7OhX2", 1, 0], [5, 719, 400], [0.25, -208.25, 0, 0, 0, 0, 1, 1, 1, 0.988]], [5, "view", 9, [10], [[55, 0, -122, [32]], [40, 5, 330, -123]], [0, "257AQ+SLFO7r8J1UiGMt4y", 1, 0], [5, 680, 276], [0, -138, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "typeAll", 5, [11, -125], [-124], [0, "2cJGan2+JGjaLM2+b39pwO", 1, 0], [5, 100, 80], [-248, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "unbtnAll", 26, [-127], [[4, 0, -126, [45], 46]], [0, "a7ymcMciFAQ7Tox29iM471", 1, 0], [5, 60, 60], [1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "type1", 5, [12, -129], [-128], [0, "c6Os8Wa5BNfILx76UqNaCi", 1, 0], [5, 100, 80], [-141, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "unbtnType1", 28, [-131], [[4, 0, -130, [55], 56]], [0, "dbyUVIbvpEn7CYc6OVlm/b", 1, 0], [5, 60, 60], [1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "type2", 5, [13, -133], [-132], [0, "e0MSSgkxRCHZ3QVNbJMPsY", 1, 0], [5, 100, 80], [-34, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "unbtnType2", 30, [-135], [[4, 0, -134, [65], 66]], [0, "7bjho5cmRCH5QUoWOS5LsC", 1, 0], [5, 60, 60], [1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "type3", 5, [14, -137], [-136], [0, "61QXHLNSlOhLQUtG+L6OSl", 1, 0], [5, 100, 80], [73, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "unbtnType3", 32, [-139], [[4, 0, -138, [75], 76]], [0, "c3278szaFEhpimfEg53juN", 1, 0], [5, 60, 60], [1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "closeBtn", 1, [[[4, 0, -140, [77], 78], -141, [44, 12, 30, 154.20000000000005, -142]], 4, 1, 4], [0, "4408Y0oyVBP6x5zol4SwsW", 1, 0], [5, 110, 110], [-299, -473.79999999999995, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "ui_guanyun1", false, 1, [[2, -143, [2], 3], [12, 17, 85, -144]], [0, "6a4POpbXxM/rlN8fp2upTg", 1, 0], [5, 714, 495], [0, 350.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lab", 19, [[14, "进攻阵容", 35, 35, false, 1, 1, -145, [16], 17], [6, 3, -146, [4, 4278979596]]], [0, "81yCnVlZVP9aMVoqY7TXfx", 1, 0], [4, 4287669287], [5, 146, 50.1]], [1, "lab", 20, [[14, "进攻阵容", 35, 35, false, 1, 1, -147, [20], 21], [6, 3, -148, [4, 4278979596]]], [0, "3bQ7yn1d5IHroMJBfmJO86", 1, 0], [5, 146, 50.1], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lab", 22, [[14, "防守阵容", 35, 35, false, 1, 1, -149, [24], 25], [6, 3, -150, [4, 4278979596]]], [0, "01CLiOY1FKcr3XZfhjbx2a", 1, 0], [4, 4287669287], [5, 146, 50.1]], [1, "lab", 23, [[14, "防守阵容", 35, 35, false, 1, 1, -151, [28], 29], [6, 3, -152, [4, 4278979596]]], [0, "b3kXnAVlNEfaLwKKG3CJ7Z", 1, 0], [5, 146, 50.1], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "txt_title", false, 3, [[50, "拖至队长位更换队长", 25, false, 1, 1, -153, [83], 84], [6, 2, -154, [4, 4278190080]]], [0, "c8OqjyBBVC4oW1Vp/9WJ/J", 1, 0], [4, 4293394369], [5, 229, 54.4]], [1, "New Sprite", 3, [[4, 0, -155, [87], 88], [45, 2, -150.466, -10.75, -156]], [0, "28QrO8Qi1HfoPkJdpTaVL9", 1, 0], [5, 52, 45], [-128.788, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "lbpower", 3, [[-157, [20, 2, -158]], 1, 4], [0, "7ezyryCQJKq78vUvC9v5M9", 1, 0], [5, 143.59, 40], [8.66, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "labCurNum", 6, [[-159, [6, 2, -160, [4, 4278190080]]], 1, 4], [0, "37E06j/glL2a5jrII/yYUl", 1, 0], [4, 4282187649], [5, 58, 35], [0, 1, 0.5], [-5.698, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "labLine", 6, [[52, "/", 35, 35, false, 1, 1, 2, -161, [111], 112], [6, 2, -162, [4, 4278190080]]], [0, "19nkz0DCxMzqRnlx01FC2f", 1, 0], [5, 35, 35]], [36, "labMaxNum", 6, [[-163, [6, 2, -164, [4, 4278190080]]], 1, 4], [0, "05dF82zRtJdbOiesYOc6Ce", 1, 0], [5, 58, 35], [0, 0, 0.5], [5.108, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "img_icon_guandizhu", false, 4, [[2, -165, [4], 5]], [0, "aeMHYVqNxLP61ZZf0tB0Nk", 1, 0], [5, 166, 151], [0.81, 3.232, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "img_icon_qicaishi", false, 4, [[2, -166, [6], 7]], [0, "8crCsrQTlN+I9in5loj1Cx", 1, 0], [5, 64, 92], [0.704, 43.112, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "img_yun1", false, 4, [[2, -167, [8], 9]], [0, "23H5xYQFZBd5d6k2nV8mhL", 1, 0], [5, 49, 28], [44.422, 17.951, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "img_yun2", false, 4, [[2, -168, [10], 11]], [0, "cbiunFv3xO94QmLVQM2Vus", 1, 0], [5, 58, 43], [-35.481, 45.497, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_title_zidongbuzhen", 17, [[2, -169, [12], 13]], [0, "44sjNZdnpGR7FTEQ1JfSAI", 1, 0], [5, 138, 34], [0, 2.841, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 17], [13, 2, true, 18, 19, 20], [13, 2, true, 21, 22, 23], [42, 5, 24.75, 24.25, 691.25, 274.75, 719, 591, 24], [9, "ui_selected_types", 11, [[3, 1, 0, -170, [37], 38]], [0, "a7boQ42sVBuLSUHLAv5KD5", 1, 0], [5, 123, 82]], [1, "btnAll", 11, [[4, 0, -171, [39], 40]], [0, "87YTro5clPd7aljOaThdfN", 1, 0], [5, 60, 60], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_all", 11, [[2, -172, [41], 42]], [0, "175obd7M5Ek4m+Bt2aMX8/", 1, 0], [5, 64, 64], [-1, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_all", 27, [[2, -173, [43], 44]], [0, "12gFKRkFVKzJe1kGWKImEw", 1, 0], [5, 64, 64], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, 2, true, 26, 27, 11], [9, "ui_selected_types", 12, [[3, 1, 0, -174, [47], 48]], [0, "28BqRyRLBE46BM+lRsggWa", 1, 0], [5, 123, 82]], [1, "btnAll", 12, [[4, 0, -175, [49], 50]], [0, "2dH6i3ffNJaLj2b/5JbYev", 1, 0], [5, 60, 60], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_1", 12, [[2, -176, [51], 52]], [0, "b1yYOGdwJJlKSjq+t+gj2z", 1, 0], [5, 64, 64], [-1, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_1", 29, [[2, -177, [53], 54]], [0, "caFVJ2LM1IY6chYc2bOOXo", 1, 0], [5, 64, 64], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, 2, true, 28, 29, 12], [9, "ui_selected_types", 13, [[3, 1, 0, -178, [57], 58]], [0, "947/cDmcxEyawB/JjG3O+R", 1, 0], [5, 123, 82]], [1, "btnPower", 13, [[4, 0, -179, [59], 60]], [0, "f4VocY4ANIHIVRkYhyZCd7", 1, 0], [5, 60, 60], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_2", 13, [[2, -180, [61], 62]], [0, "57ipgicmxICbgbiHMI0fFR", 1, 0], [5, 64, 64], [-1, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_2", 31, [[2, -181, [63], 64]], [0, "a9os1uf/hBGpOG7fp5xEgp", 1, 0], [5, 64, 64], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 2, 30, 31, 13], [9, "ui_selected_types", 14, [[3, 1, 0, -182, [67], 68]], [0, "b617q14RBGM5wVt7VX/G+Y", 1, 0], [5, 123, 82]], [1, "btnFast", 14, [[4, 0, -183, [69], 70]], [0, "3bQWbu/ShObqoX/THdCa9U", 1, 0], [5, 60, 60], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_3", 14, [[2, -184, [71], 72]], [0, "944G0rOlhLuIukw7XpzJyt", 1, 0], [5, 64, 64], [-1, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_types_3", 33, [[2, -185, [73], 74]], [0, "34CXUoqnZGup77q1aunLVE", 1, 0], [5, 64, 64], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 2, 32, 33, 14], [17, 34], [7, "ui_link_02", false, 3, [[3, 1, 0, -186, [79], 80]], [0, "5eb0pSu4tKoKRcKI5kQcxM", 1, 0], [5, 129, 13], [204, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "ui_link_02", false, 3, [[3, 1, 0, -187, [81], 82]], [0, "1a9f36r9RAdpQFsxGusKEY", 1, 0], [5, 129, 13], [-199, 0, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [1, "New Sprite", 3, [[3, 1, 0, -188, [85], 86]], [0, "e0Tg77qLtNPKSi/Hq3RSYg", 1, 0], [5, 249, 46], [-18, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "12345", false, 1, 42, [89]], [1, "pos10_1", 2, [[2, -189, [90], 91]], [0, "31jrMzyIdG34fGYf8j4k6f", 1, 0], [5, 89, 59], [2, 131.779, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_2", 2, [[2, -190, [92], 93]], [0, "06TmvrFCpFibhE0V+Jy75q", 1, 0], [5, 77, 47], [165, 113, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_3", 2, [[2, -191, [94], 95]], [0, "89VMG7XlhBC6YdC5/SEXoX", 1, 0], [5, 77, 47], [255, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_4", 2, [[2, -192, [96], 97]], [0, "46ajMB4ZpHzJ2nQcopaank", 1, 0], [5, 77, 47], [250, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_5", 2, [[2, -193, [98], 99]], [0, "61KpGKQ3xGEr+7FzmY9RlT", 1, 0], [5, 77, 47], [148, -195, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_6", 2, [[2, -194, [100], 101]], [0, "5dVuiGqp5Dy6SBDL3rjewE", 1, 0], [5, 77, 47], [2, -235, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_7", 2, [[2, -195, [102], 103]], [0, "ef6VovWGFJhIXcO+j2Nqy6", 1, 0], [5, 77, 47], [-148, -195, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_8", 2, [[2, -196, [104], 105]], [0, "31Jsh7RFhEMK+v5EBeHfI8", 1, 0], [5, 77, 47], [-250, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_9", 2, [[2, -197, [106], 107]], [0, "2cMaxhcOZMDpUMpEwvHGW/", 1, 0], [5, 77, 47], [-255, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pos10_10", 2, [[2, -198, [108], 109]], [0, "24grJxPKlDxa/bw14UKO6z", 1, 0], [5, 77, 47], [-165, 113, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "12", 35, 35, false, -12, 1, 1, 43, [110]], [23, "12", 35, 35, false, -12, 1, 1, 45, [113]], [49, 1, 1, 6], [1, "New Sprite", 7, [[4, 0, -199, [114], 115]], [0, "8fYXuOiotIB5haWs1sfC4g", 1, 0], [5, 133, 32], [-17.368, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Sprite", 7, [[2, -200, [116], 117]], [0, "c4Wwt50v1Clb9gYQMvqqNe", 1, 0], [5, 35, 35], [67.75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 7]], 0, [0, 8, 1, 0, 0, 1, 0, 9, 95, 0, 10, 79, 0, 11, 92, 0, 12, 4, 0, 13, 53, 0, 14, 52, 0, 15, 91, 0, 16, 90, 0, 17, 10, 0, 18, 15, 0, 19, 74, 0, 20, 69, 0, 21, 64, 0, 22, 59, 0, 23, 51, 0, 24, 75, 0, 25, 54, 0, 0, 1, 0, -1, 16, 0, -2, 35, 0, -3, 4, 0, -4, 8, 0, -5, 24, 0, -6, 5, 0, -7, 34, 0, -8, 3, 0, -9, 15, 0, -10, 6, 0, -11, 7, 0, 0, 2, 0, -1, 80, 0, -2, 81, 0, -3, 82, 0, -4, 83, 0, -5, 84, 0, -6, 85, 0, -7, 86, 0, -8, 87, 0, -9, 88, 0, -10, 89, 0, 0, 3, 0, 0, 3, 0, -1, 76, 0, -2, 77, 0, -3, 40, 0, -4, 78, 0, -5, 41, 0, -6, 42, 0, 0, 4, 0, 0, 4, 0, -1, 46, 0, -2, 47, 0, -3, 48, 0, -4, 49, 0, 0, 5, 0, 0, 5, 0, -1, 26, 0, -2, 28, 0, -3, 30, 0, -4, 32, 0, 0, 6, 0, 0, 6, 0, -3, 92, 0, -1, 43, 0, -2, 44, 0, -3, 45, 0, 0, 7, 0, -2, 95, 0, 0, 7, 0, 0, 7, 0, -1, 93, 0, -2, 94, 0, 0, 8, 0, -1, 17, 0, -2, 18, 0, -3, 21, 0, 26, 10, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 25, 0, 0, 10, 0, 0, 10, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, -1, 60, 0, -2, 61, 0, -3, 62, 0, -1, 65, 0, -2, 66, 0, -3, 67, 0, -1, 70, 0, -2, 71, 0, -3, 72, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -2, 51, 0, -1, 50, 0, -1, 52, 0, -1, 19, 0, -2, 20, 0, 0, 19, 0, -1, 36, 0, 0, 20, 0, -1, 37, 0, -1, 53, 0, -1, 22, 0, -2, 23, 0, 0, 22, 0, -1, 38, 0, 0, 23, 0, -1, 39, 0, 0, 24, 0, -2, 54, 0, 0, 25, 0, 0, 25, 0, -1, 59, 0, -2, 27, 0, 0, 27, 0, -1, 58, 0, -1, 64, 0, -2, 29, 0, 0, 29, 0, -1, 63, 0, -1, 69, 0, -2, 31, 0, 0, 31, 0, -1, 68, 0, -1, 74, 0, -2, 33, 0, 0, 33, 0, -1, 73, 0, 0, 34, 0, -2, 75, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, -1, 79, 0, 0, 42, 0, -1, 90, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, -1, 91, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 93, 0, 0, 94, 0, 27, 1, 2, 3, 15, 9, 3, 24, 10, 3, 25, 11, 3, 26, 12, 3, 28, 13, 3, 30, 14, 3, 32, 200], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 90, 91], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, -1, 1, -1, 1, -1, 1, 4, 5, 6, 7, 2, 2, 2], [0, 13, 0, 14, 0, 15, 0, 16, 0, 17, 0, 18, 0, 19, 0, 20, 0, 3, 0, 6, 0, 3, 0, 7, 0, 3, 0, 6, 0, 3, 0, 7, 0, 0, 21, 0, 22, 0, 4, 0, 2, 0, 8, 0, 8, 0, 2, 0, 4, 0, 2, 0, 9, 0, 9, 0, 2, 0, 4, 0, 2, 0, 10, 0, 10, 0, 2, 0, 4, 0, 2, 0, 11, 0, 11, 0, 2, 0, 23, 0, 12, 0, 12, 0, 3, 0, 24, 0, 25, 0, 0, 26, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 5, 0, 0, 27, 0, 28, 0, 29, 30, 31, 32, 33, 34, 5, 5]]