[1, ["ecpdLyjvZBwrvm+cedCcQy", "440RjU4JhEEJLGsmvEbM1v", "f7i0FuCl9AGYL8XBbgiqZX", "42nR4TEtlLvY8iS7+6QeeK", "a6XQlubZdDubHS4WYNI5ob", "d9dkwSdGNJFK8Jvz7lzizp", "41D7kWhyFGY7q4NDlzkazn", "a7RQJXq4RKgqXIc0XVquW5", "51hb9gFhBHeY6h3jGxeojN", "3cAZ/1aL5Ip7zo7Ao5FhIa", "3ae7efMv1CLq2ilvUY/tQi", "f7njjgEjlGCK6nZwGTM6Mt", "4cojHNV2RL44q+D/DUBdXD", "1f1iT4BNFB47y3dAtt0PDj", "64cil1WtVLfY6hTaM/r1tm", "190NQN59FP/bqe2flIrOYV", "a0PwLQkhRD6pAydWnjBEdt"], ["node", "_spriteFrame", "_N$file", "root", "maskSelected", "maskNode", "tab", "imag", "num", "bg", "clickArea", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs", "_color"], 0, 4, 1, 9, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_spacingX", "_styleFlags", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["ec9b2VXqb5G6KuBtM0tMCNc", ["node", "clickArea", "bg", "num", "imag", "tab", "maskNode", "maskSelected"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["df0cciZdkRP7YVIdcbr0q1M", ["node"], 3, 1]], [[4, 0, 1, 2, 2], [0, 0, 4, 5, 3, 6, 8, 2], [1, 0, 3, 4, 5, 2], [1, 3, 4, 5, 1], [1, 1, 0, 3, 4, 5, 3], [0, 0, 4, 7, 5, 3, 6, 8, 2], [0, 0, 4, 5, 3, 6, 2], [9, 0, 1], [8, 0, 1], [11, 0, 1], [0, 0, 1, 4, 7, 3, 3], [0, 0, 4, 5, 3, 9, 6, 2], [3, 0, 1, 2, 3, 4, 2], [7, 0, 1, 2, 3, 4], [2, 0, 1, 2, 3, 4, 5, 8, 9, 10, 7], [5, 0, 2], [0, 0, 7, 5, 3, 6, 8, 2], [0, 0, 2, 4, 5, 3, 9, 6, 3], [0, 0, 1, 4, 5, 3, 6, 8, 3], [0, 0, 1, 4, 7, 5, 3, 6, 8, 3], [0, 0, 4, 7, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [1, 3, 4, 1], [1, 0, 2, 3, 4, 3], [4, 1, 2, 1], [2, 0, 1, 2, 3, 6, 7, 4, 5, 8, 9, 9], [2, 0, 1, 2, 3, 6, 7, 4, 5, 8, 9, 10, 9], [10, 0, 1, 2, 3, 4, 5, 6, 7, 1], [12, 0, 1, 2, 2], [13, 0, 1]], [[15, "QuickBuyMsgBox"], [16, "QuickBuyMsgBox", [-5, -6, -7, -8, -9, -10, -11, -12], [[8, -2], [13, 45, 750, 1334, -3], [30, -4]], [25, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bagItem", 1, [-22, -23, -24, -25, -26, -27, -28], [[28, -20, -19, -18, -17, -16, -15, -14, -13], [8, -21]], [0, "bcjm7EAgFPS4ErZ3YkoUw2", 1, 0], [5, 119, 119], [0, 45.51, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_ok", 1, [-31, -32], [[4, 1, 0, -29, [33], 34], [9, -30]], [0, "f4UGrI3VpO/pSOtXu5qzeR", 1, 0], [5, 217, 81], [123.136, -88.952, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "mask", 204, 1, [[2, 0, -33, [0], 1], [13, 45, 750, 2201, -34], [8, -35]], [0, "94iog4yHhAd543cakvW+GS", 1, 0], [4, 4278190080], [5, 768, 1366]], [21, "imag", 2, [[[23, -36, [8]], -37], 4, 1], [0, "ea9/I944VDjqmKORrph/3O", 1, 0], [5, 66, 65], [0.129, -0.226, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "mask", false, 2, [-38, -39], [0, "de50oUQBdERKyFJRwXC9Zb", 1, 0]], [10, "maskSelected", false, 2, [-40, -41], [0, "c8Id+KAWRH35HJX07RDi4j", 1, 0]], [5, "btn_cancel", 1, [-44], [[4, 1, 0, -42, [23], 24], [9, -43]], [0, "6dUGBVCpVF47NcmXtAD/Or", 1, 0], [5, 217, 81], [-120.62100000000001, -88.541, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "needBox", 3, [-46, -47], [[4, 1, 0, -45, [31], 32]], [0, "97R9DKyhNF96LS1RbqtruX", 1, 0], [5, 100, 30], [0, 17.812, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btn_goto", false, 1, [-50], [[4, 1, 0, -48, [37], 38], [9, -49]], [0, "d643rc7jBPaoDAGdCcy6ms", 1, 0], [5, 217, 81], [123.136, -88.952, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg", 2, [[[2, 0, -51, [6], 7], -52], 4, 1], [0, "d4MHnrumdPd64eMXxcrvVz", 1, 0], [5, 119, 119]], [12, "tab", 2, [[[24, 0, false, -53, [10]], -54], 4, 1], [0, "2b7xYjl8tGWKgqHT9hsxSU", 1, 0], [5, 119, 119]], [1, "img_icon", 9, [[3, -55, [27], 28], [7, -56]], [0, "79/fkVoRtORLXhFbf0a6eQ", 1, 0], [5, 61, 66], [-42.999, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [20, "labelLayer", 1, [-57, -58], [0, "a0ynhwhBhJe53bw0HNKlQN", 1, 0]], [1, "txt_title", 14, [[14, "金币不足", 32, 32, false, 1, 1, -59, [39], 40], [29, 3, -60, [4, 4281341443]]], [0, "494pcj8U5EtZH7BOlXHUmZ", 1, 0], [5, 134, 46.32], [0, 189.742, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_box", 1, [[4, 1, 0, -61, [2], 3]], [0, "1a2wtUifBOib+XhnRjKkjD", 1, 0], [5, 533, 405], [0.5, 33.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_link", 1, [[2, 0, -62, [4], 5]], [0, "8eobHw1WBB5LdE3gt/cXoe", 1, 0], [5, 510, 2], [0, -32.033, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 11], [7, 5], [22, "num", 2, [-63], [0, "5ex/AUSVxEMpImxg3DFc9Q", 1, 0], [5, 51, 25], [0, 1, 0], [54.133, -49.838, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "999", 25, 25, false, -12, 1, 2, 1, 20, [9]], [7, 12], [18, "red", false, 2, [[2, 0, -64, [11], 12]], [0, "d7o/vMNKxMO6Xeb5i+NSSe", 1, 0], [5, 31, 34], [44.209, 46.16, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "singleColor", 6, [[2, 0, -65, [13], 14]], [0, "61xb9NPdJBo4yUGNdHJ5lK", 1, 0], [4, 4278190080], [5, 115, 115]], [6, "img_weijianding", 6, [[3, -66, [15], 16]], [0, "89W4zvVlFJgKCb/DKLmmRJ", 1, 0], [5, 38, 54]], [11, "singleColor", 7, [[2, 0, -67, [17], 18]], [0, "ec3vuFr41PK42lOJlw9AeO", 1, 0], [4, 4278190080], [5, 115, 115]], [6, "img_weijianding", 7, [[3, -68, [19], 20]], [0, "bbQGQ7Q4hKOZYUsYgoE7OA", 1, 0], [5, 63, 55]], [6, "btn_tquxiao", 8, [[3, -69, [21], 22]], [0, "6e5XLxBKNH+pKw7hPKZYzc", 1, 0], [5, 58, 26]], [1, "img", 3, [[3, -70, [25], 26]], [0, "e3cUeMBz5IzIBjtnI6MUkM", 1, 0], [5, 65, 29], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "txt_expendNum", 9, [[27, "9999", 25, 25, false, -12, 1, 1, 1, -71, [29], 30]], [0, "44zaM2RkhKl7VF2dhC7Xa/", 1, 0], [5, 64, 25], [7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "img", 10, [[3, -72, [35], 36]], [0, "42c4LLhPNN0aadRefGezev", 1, 0], [5, 65, 29]], [1, "txt_des", 14, [[14, "是否消耗仙玉购买该道具", 25, 25, false, 1, 1, -73, [41], 42]], [0, "ca9wObkkJB+rQqBiazbzRd", 1, 0], [5, 274.98, 31.5], [0, 130, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 16, 0, -3, 17, 0, -4, 2, 0, -5, 8, 0, -6, 3, 0, -7, 10, 0, -8, 14, 0, 4, 7, 0, 5, 6, 0, 6, 22, 0, 7, 19, 0, 8, 21, 0, 9, 18, 0, 10, 5, 0, 0, 2, 0, 0, 2, 0, -1, 11, 0, -2, 5, 0, -3, 20, 0, -4, 12, 0, -5, 23, 0, -6, 6, 0, -7, 7, 0, 0, 3, 0, 0, 3, 0, -1, 29, 0, -2, 9, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 19, 0, -1, 24, 0, -2, 25, 0, -1, 26, 0, -2, 27, 0, 0, 8, 0, 0, 8, 0, -1, 28, 0, 0, 9, 0, -1, 13, 0, -2, 30, 0, 0, 10, 0, 0, 10, 0, -1, 31, 0, 0, 11, 0, -2, 18, 0, 0, 12, 0, -2, 22, 0, 0, 13, 0, 0, 13, 0, -1, 15, 0, -2, 32, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, -1, 21, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 11, 1, 73], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, 2], [0, 6, 0, 7, 0, 8, 0, 9, 0, 10, 0, 0, 11, 0, 1, 0, 12, 0, 1, 0, 13, 0, 14, 0, 15, 0, 2, 0, 16, 0, 3, 0, 1, 0, 4, 0, 2, 0, 4, 0, 5, 0, 5, 3]]