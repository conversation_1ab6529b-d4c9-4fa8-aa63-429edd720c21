[1, ["8c9wm7G/1ENaYzVC6fhnw/", "b1SRuylZFPxoWJMEWPq2MM", "a1YTTZ3hFE7oFs+XHS1p+j", "25Qu/JGwROoYT7RW/KWSrM", "d6k1mawjhC9J+dZvf0A9tC", "50Rj2u3P9L27L6cGx97dN7", "e0U9Wf+q9GqI7T5qHdIDe8", "a1AdetRbJP76Dy67A1/UBL", "96N2YsF6tCpaA0KaaAfy6w", "1ejuyHDq9Jfpl4MwRgNcPP", "7e9fr/7ehDDaoJfG+AMdjG", "87h5BHhshHjaLj9r0FbgWd"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "buff_bz_02_back_loop", 0.20833333333333334, 24, 0.5, 2, [{"props": {"opacity": [{"frame": 0, "value": 255}]}}, "paths", 11, [{"02/effect_bz_02_lizi": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 119}]}}}, "02/effect_bz_02_Cast": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}]}}}}, "02/effext_bz_02_guangquan", 11, [{"props": {"opacity": [{"frame": 0, "value": 255}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.041666666666666664}, "value", 6, 1], [{"frame": 0.08333333333333333}, "value", 6, 2], [{"frame": 0.125}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4]], 11, 11, 11, 11, 11]]]], "02/effect_bz_02_tiger", 11, [{"props": {"opacity": [{"frame": 0, "value": 255}], "position": [{"frame": 0, "value": [55.635, 150.348, 0]}], "angle": [{"frame": 0, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 5], [{"frame": 0.041666666666666664}, "value", 6, 6], [{"frame": 0.125}, "value", 6, 7], [{"frame": 0.16666666666666666}, "value", 6, 8]], 11, 11, 11, 11]]]], "02/effect_bz_02_clow", 11, [{"props": {"opacity": [{"frame": 0, "value": 255}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.041666666666666664}, "value", 6, 9], [{"frame": 0.125}, "value", 6, 10], [{"frame": 0.16666666666666666}, "value", 6, 11]], 11, 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]]