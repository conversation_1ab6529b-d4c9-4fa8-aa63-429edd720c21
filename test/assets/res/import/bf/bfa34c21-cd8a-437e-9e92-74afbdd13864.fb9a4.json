[1, ["ecpdLyjvZBwrvm+cedCcQy", "d9dkwSdGNJFK8Jvz7lzizp", "41D7kWhyFGY7q4NDlzkazn", "7aIOnjerZDG6IUzm07qLJ9", "190NQN59FP/bqe2flIrOYV", "eaBstxicRLTZh5LTDrpn/C", "a6XQlubZdDubHS4WYNI5ob", "e3OWrXCAVEo6V7t7TmPDDE", "18kAgZsmlAVJGhfiiohNje"], ["node", "_spriteFrame", "_N$file", "bagItemPrefab", "root", "selectItemNum", "btnOk", "btnOut", "itemContent", "closeBtnBg", "_N$content", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 2, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_originalHeight", "_originalWidth", "node"], -2, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_styleFlags", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_color", "_trs", "_children"], 1, 1, 12, 4, 5, 5, 7, 2], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["3039cCGLWFIeqCPOGQnWhje", ["scaleMin", "scaleMax", "node"], 1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["a574bFLV5xESqNRNNCh/ztT", ["node", "closeBtnBg", "itemContent", "btnOut", "btnOk", "selectItemNum", "bagItemPrefab"], 3, 1, 1, 1, 1, 1, 1, 6]], [[6, 0, 1, 2, 2], [4, 1, 0, 2, 3, 4, 3], [0, 0, 4, 1, 2, 7, 3, 5, 2], [7, 0, 1, 2, 2], [0, 0, 4, 6, 1, 2, 3, 2], [0, 0, 4, 1, 2, 3, 5, 2], [0, 0, 4, 1, 2, 3, 2], [3, 0, 2, 8, 3, 4, 5, 7, 2], [1, 0, 4, 3, 5, 4], [1, 0, 1, 2, 3, 5, 5], [1, 0, 5, 2], [1, 0, 2, 5, 3], [5, 2, 1], [2, 0, 1, 2, 6, 3, 4, 7, 8, 7], [2, 0, 1, 2, 5, 7, 8, 9, 5], [7, 1, 2, 1], [8, 0, 2], [0, 0, 6, 1, 2, 3, 5, 2], [0, 0, 4, 6, 1, 2, 3, 5, 2], [0, 0, 4, 1, 2, 3, 8, 5, 2], [3, 0, 1, 2, 3, 4, 6, 5, 3], [3, 0, 2, 3, 4, 6, 5, 7, 2], [4, 0, 2, 3, 4, 2], [4, 1, 0, 2, 3, 3], [1, 0, 1, 2, 5, 4], [1, 0, 1, 2, 4, 3, 5, 6], [1, 0, 1, 5, 3], [5, 0, 1, 2, 3], [6, 1, 2, 1], [2, 0, 1, 2, 3, 4, 7, 8, 6], [2, 0, 1, 2, 5, 3, 4, 7, 8, 9, 7], [9, 0, 1, 2, 3, 4, 5, 5], [10, 0, 1, 1], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 3, 4, 5, 6, 1]], [[16, "wakenItemSelect"], [17, "wakenItemSelect", [-9, -10, -11], [[34, -7, -6, -5, -4, -3, -2, 23], [8, 45, 768, 1334, -8]], [28, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg", 1, [-13, -14, -15, -16, -17], [[1, 1, 0, -12, [21], 22]], [0, "0ckJZHcd1GpJuokaqyKb1M", 1, 0], [5, 669, 603]], [7, "btn_ok", 2, [-21, -22, -23], [[[1, 1, 0, -18, [19], 20], -19, [11, 4, 37.420999999999964, -20]], 4, 1, 4], [0, "75tFbi/sdIcYyOquuOlU+L", 1, 0], [5, 187, 80], [165, -224.07900000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btn_out", 2, [-27, -28], [[[1, 1, 0, -24, [12], 13], -25, [11, 4, 37.42100000000002, -26]], 4, 1, 4], [0, "732PqAFMpMy6TFqJQ27vAH", 1, 0], [5, 187, 80], [-165, -224.07899999999995, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "upScrollView", 2, [-33], [[23, 1, 0, -29, [6]], [33, false, 0.75, 0.23, null, null, -31, -30], [9, 5, 77.50000000000003, 211.5, 242, -32]], [0, "58DyTDrR5IrZIX6Z6UIQXx", 1, 0], [5, 637, 314], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "closeBg", 204, 1, [[[22, 0, -34, [0], 1], [8, 45, 750, 1334, -35], -36], 4, 4, 1], [0, "eeIppSGfFJqYEwvwgviNHY", 1, 0], [4, 4278190080], [5, 768, 1366]], [4, "view", 5, [-39], [[32, -37, [5]], [25, 45, -0.049999999999954525, -0.049999999999954525, 677, 242, -38]], [0, "6fzCScMfZH2rI1qSmQzl/C", 1, 0], [5, 637, 314.0999999999999]], [19, "content", 7, [[31, 1, 1, 10, 3, -40, [5, 637, 119]]], [0, "7dQ8aK2RFIbr15wIRGhZ3u", 1, 0], [5, 637, 119], [0, 0, 0.5], [-316.078, 83.747, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 2, [[30, "选择所需碎片", 36, 36, false, 1, 1, -41, [7], 8], [3, 3, -42, [4, 4279963905]], [26, 1, 15.771999999999991, -43]], [0, "2aDO8gBLhBh6YBYRQiw4OI", 1, 0], [4, 4288787000], [5, 221.96, 51.36], [0, 260.048, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btn_title_out", 4, [[10, 16, -44], [14, "一键放入", 30, 30, false, -45, [10], 11], [3, 3, -46, [4, 4288235533]]], [0, "984TupdiFFL6uUwyQnSo75", 1, 0], [5, 126, 43.8]], [6, "btn_title_ok", 3, [[10, 16, -47], [14, "确认", 30, 30, false, -48, [17], 18], [3, 3, -49, [4, 4278209683]]], [0, "c6Ywq+vNhKH4QQG85ug7sG", 1, 0], [5, 66, 43.8]], [2, "CloseTips", 1, [[29, "点击空白区域关闭界面", 24, 0, 1, 1, -50, [2]], [24, 4, 1190, 173.72500000000002, -51]], [0, "dbCEb+S3lIIIQ0Kc+ky63j", 1, 0], [4, 4288059030], [5, 240, 30.24], [0, -494.155, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Sprite", 2, [[1, 1, 0, -52, [3], 4], [9, 5, 77.5, 211.5, 314, -53]], [0, "71MswrQVtIPZUsDZnGr5y4", 1, 0], [5, 637, 314], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 4, [[13, "长按可持续加入", 20, 20, 1, 1, 1, -54, [9]], [15, -55, [4, 4278190080]]], [0, "56mihyOxVHtpF8BMtUGOgc", 1, 0], [4, 4288787000], [5, 142, 27.2], [0, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "selectitemnum", 3, [[-56, [15, -57, [4, 4278190080]]], 1, 4], [0, "9cZbjEx+VELJJkyK4lJ3lj", 1, 0], [4, 4293911687], [5, 109.8, 27.2], [0, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [27, 1, 1, 6], [12, 4], [5, "New Sprite", 3, [[1, 1, 0, -58, [14], 15]], [0, "92WFVlPqZJGbPMtebbdZSG", 1, 0], [5, 165, 41], [0, 64.919, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "已选中：0/3", 20, 20, 1, 1, 1, 15, [16]], [12, 3]], 0, [0, 4, 1, 0, 5, 19, 0, 6, 20, 0, 7, 17, 0, 8, 8, 0, 9, 16, 0, 0, 1, 0, 0, 1, 0, -1, 6, 0, -2, 12, 0, -3, 2, 0, 0, 2, 0, -1, 13, 0, -2, 5, 0, -3, 9, 0, -4, 4, 0, -5, 3, 0, 0, 3, 0, -2, 20, 0, 0, 3, 0, -1, 18, 0, -2, 15, 0, -3, 11, 0, 0, 4, 0, -2, 17, 0, 0, 4, 0, -1, 14, 0, -2, 10, 0, 0, 5, 0, 10, 8, 0, 0, 5, 0, 0, 5, 0, -1, 7, 0, 0, 6, 0, 0, 6, 0, -3, 16, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, -1, 19, 0, 0, 15, 0, 0, 18, 0, 11, 1, 58], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, -1, 2, -1, -1, 2, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, 3], [0, 2, 0, 0, 3, 0, 0, 0, 1, 0, 0, 1, 0, 4, 0, 5, 0, 0, 1, 0, 6, 0, 7, 8]]