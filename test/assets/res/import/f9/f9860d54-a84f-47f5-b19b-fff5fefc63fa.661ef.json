[1, ["33w5zm3o9AMogPU+/LTulu", "31LNB+57NLE6KmJEkZsgwp", "81CWPVDRRNQLxDiS5Q30Sy", "94DwrZ53pMjLAXOrBpLAud", "23C5EfMSNJ8LVwrbozgqMl", "e8vmoD8SdMTbpMcEXUA8eX", "02Jg21FzpHjK8O9KO2BJjt", "9fEYrlbPdHaIAi0kJa+Shf", "0c//dKKM9Il5Rk851QUnr+", "1dzCNlwyRAQLqlWpON378q", "77pxIfKeZBsJzK9/badByA", "39qwxzxhtLbpKEeOsLuMRy", "9cnUtCRHxPo7fcEyggTpqF"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "cuttrees", 0.375, 24, 0.5, 2, [{"props": {"y": [{"frame": 0, "value": -7.17}], "x": [{"frame": 0, "value": 7.125}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.041666666666666664}, "value", 6, 1], [{"frame": 0.08333333333333333}, "value", 6, 2], [{"frame": 0.125}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4], [{"frame": 0.20833333333333334}, "value", 6, 5], [{"frame": 0.25}, "value", 6, 6], [{"frame": 0.2916666666666667}, "value", 6, 7], [{"frame": 0.3333333333333333}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "paths", 11, [{}, "futou", 11, [{"props": {"active": [{"frame": 0, "value": true}], "position": [{"frame": 0, "curve": "constant", "value": [-14.324, 20.043, 0]}, {"frame": 0.041666666666666664, "curve": "constant", "value": [14.299, 57.33, 0]}, {"frame": 0.08333333333333333, "curve": "constant", "value": [36.933, 59.866, 0]}, {"frame": 0.125, "curve": "constant", "value": [47.68, 44.089, 0]}, {"frame": 0.16666666666666666, "curve": "constant", "value": [51.473, 37.518, 0]}, {"frame": 0.20833333333333334, "curve": "constant", "value": [-2.636, 86.77, 0]}, {"frame": 0.25, "curve": "constant", "value": [-27.972, 12.634, 0]}, {"frame": 0.2916666666666667, "curve": "constant", "value": [-24.211, 10.037, 0]}, {"frame": 0.3333333333333333, "value": [-9.876, 5.212, 0]}], "angle": [{"frame": 0, "value": 54.778999999999996, "curve": "constant"}, {"frame": 0.041666666666666664, "value": -10, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -74.961, "curve": "constant"}, {"frame": 0.125, "value": -111.61, "curve": "constant"}, {"frame": 0.16666666666666666, "value": -122.782, "curve": "constant"}, {"frame": 0.20833333333333334, "value": -6.176000000000002, "curve": "constant"}, {"frame": 0.25, "value": 88.027, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 107.322, "curve": "constant"}, {"frame": 0.3333333333333333, "value": 104.943}], "anchorX": [{"frame": 0, "value": 0.5}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 9]], 11]]]], "futou/shou", 11, [{"props": {"position": [{"frame": 0, "value": [24.626, -26.576, 0]}], "active": [{"frame": 0, "value": true}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 10]], 11]]]], "futou/wei", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.25, "value": 255, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0.20833333333333334, "value": 12.253, "curve": "constant"}, {"frame": 0.25, "value": -87.206}], "position": [{"frame": 0.20833333333333334, "curve": "constant", "value": [82.587, 8.032, 0]}, {"frame": 0.25, "value": [63.418, 18.76, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334, "curve": "constant"}, "value", 8, [1, 1.7, 1.7, 1]], [{"frame": 0.25}, "value", 8, [1, 1.916, 2.327, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 11], [{"frame": 0.25}, "value", 6, 12]], 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]