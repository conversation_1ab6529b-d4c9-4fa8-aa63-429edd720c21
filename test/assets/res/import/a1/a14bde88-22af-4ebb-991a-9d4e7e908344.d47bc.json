[1, 0, 0, [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "effect_bx_01", 0.8, 30, 0.5, 2, [{}, "paths", 11, [{"effect_bx_sweep/effect_bx_light": {"props": {"position": [{"frame": 0.2, "value": [-131.079, 131.079, 0]}, {"frame": 0.5, "value": [105.975, -105.475, 0]}]}}}, "effect_bx_star/effect_bx_star1", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.3, 0.3, 0.6]], [{"frame": 0.1}, "value", 8, [1, 0.6, 0.6, 0.6]], [{"frame": 0.2}, "value", 8, [1, 0.3, 0.3, 0.6]], [{"frame": 0.3}, "value", 8, [1, 0.6, 0.6, 0.6]], [{"frame": 0.4}, "value", 8, [1, 0.3, 0.3, 0.6]], [{"frame": 0.5}, "value", 8, [1, 0.6, 0.6, 0.6]], [{"frame": 0.6}, "value", 8, [1, 0.3, 0.3, 0.6]], [{"frame": 0.7}, "value", 8, [1, 0.6, 0.6, 0.6]], [{"frame": 0.8}, "value", 8, [1, 0.3, 0.3, 0.6]]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "effect_bx_star/effect_bx_star2", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.5833333333333333, 0.5833333333333333, 0.7]], [{"frame": 0.06666666666666667}, "value", 8, [1, 0.35, 0.35, 0.7]], [{"frame": 0.16666666666666666}, "value", 8, [1, 0.7, 0.7, 0.7]], [{"frame": 0.2}, "value", 8, [1, 0.5833333333333333, 0.5833333333333333, 0.7]], [{"frame": 0.26666666666666666}, "value", 8, [1, 0.35, 0.35, 0.7]], [{"frame": 0.36666666666666664}, "value", 8, [1, 0.7, 0.7, 0.7]], [{"frame": 0.4}, "value", 8, [1, 0.5833333333333333, 0.5833333333333333, 0.7]], [{"frame": 0.4666666666666667}, "value", 8, [1, 0.35, 0.35, 0.7]], [{"frame": 0.5666666666666667}, "value", 8, [1, 0.7, 0.7, 0.7]], [{"frame": 0.6}, "value", 8, [1, 0.5833333333333333, 0.5833333333333333, 0.7]], [{"frame": 0.6666666666666666}, "value", 8, [1, 0.35, 0.35, 0.7]], [{"frame": 0.7666666666666667}, "value", 8, [1, 0.7, 0.7, 0.7]], [{"frame": 0.8}, "value", 8, [1, 0.5833333333333333, 0.5833333333333333, 0.7]]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "effect_bx_star/effect_bx_star3", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.2666666666666666, 0.2666666666666666, 0.3]], [{"frame": 0.03333333333333333}, "value", 8, [1, 0.2, 0.2, 0.3]], [{"frame": 0.13333333333333333}, "value", 8, [1, 0.4, 0.4, 0.3]], [{"frame": 0.2}, "value", 8, [1, 0.2666666666666666, 0.2666666666666666, 0.3]], [{"frame": 0.23333333333333334}, "value", 8, [1, 0.2, 0.2, 0.3]], [{"frame": 0.3333333333333333}, "value", 8, [1, 0.4, 0.4, 0.3]], [{"frame": 0.4}, "value", 8, [1, 0.2666666666666666, 0.2666666666666666, 0.3]], [{"frame": 0.43333333333333335}, "value", 8, [1, 0.2, 0.2, 0.3]], [{"frame": 0.5333333333333333}, "value", 8, [1, 0.4, 0.4, 0.3]], [{"frame": 0.6}, "value", 8, [1, 0.2666666666666666, 0.2666666666666666, 0.3]], [{"frame": 0.6333333333333333}, "value", 8, [1, 0.2, 0.2, 0.3]], [{"frame": 0.7333333333333333}, "value", 8, [1, 0.4, 0.4, 0.3]], [{"frame": 0.8}, "value", 8, [1, 0.2666666666666666, 0.2666666666666666, 0.3]]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]]], 0, 0, [], [], []]