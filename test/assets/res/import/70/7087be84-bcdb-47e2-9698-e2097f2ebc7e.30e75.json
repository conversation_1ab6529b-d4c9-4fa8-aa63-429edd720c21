[1, ["ecpdLyjvZBwrvm+cedCcQy", "b6xC/ncU1Itbl4wEYeSYXu", "4bpDRT34JO9paXMsTwg2HE", "d7+j+ehjRC9LYeM5K9p4Rr", "41Vie/mg5GPometkqSTNNT", "b2aHrECZ5APKGS/0d2hvT1", "abtPC9pLlIjYeRLvk3Sw2N"], ["node", "_spriteFrame", "_file", "_defaultClip", "root", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_eulerAngles"], 1, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_dstBlendFactor", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Mask", ["_type", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "endSize", "startSpin", "endSpin", "endSpinVar", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccel", "radialAccelVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "_file", "_spriteFrame"], -15, 1, 3, 8, 8, 8, 8, 6, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[2, 0, 1, 2, 2], [0, 0, 1, 4, 3, 2, 5, 7, 8, 3], [1, 2, 3, 4, 1], [3, 0, 2], [0, 0, 6, 3, 2, 5, 2], [0, 0, 1, 4, 6, 2, 3], [0, 0, 1, 4, 3, 2, 5, 3], [0, 0, 4, 6, 3, 2, 5, 2], [0, 0, 4, 3, 2, 2], [1, 0, 1, 2, 3, 4, 3], [2, 1, 2, 1], [4, 0, 1, 2, 3, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 19], [6, 0, 1, 2, 3, 2]], [[3, "ui_wuping<PERSON><PERSON>o"], [4, "ui_wuping<PERSON><PERSON>o", [-3], [[13, true, -2, [12], 11]], [10, -1, 0], [5, 100, 100]], [5, "effect", 230, 1, [-4, -5, -6], [0, "675h6eMm5Gmp3qUA1ZZG99", 1, 0]], [7, "<PERSON><PERSON><PERSON>", 2, [-8, -9], [[11, 2, -7, [6], 7]], [0, "6bJmdNNQ1E46oGvL9hl+Te", 1, 0], [5, 100, 100]], [6, "ui_itemeffet", 240, 2, [[9, 1, 0, -10, [0], 1]], [0, "4b1p46R9dMb7ZOPEn8HArk", 1, 0], [5, 110, 110]], [1, "ui_itemeffect_saoguang01", 220, 3, [[2, -11, [2], 3]], [0, "b2687+lV1GMpWqFj0fLN4U", 1, 0], [5, 128, 128], [-100, 100, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 0.7, 2, 1], [1, 0, 0, -45]], [1, "ui_itemeffect_saoguang02", 220, 3, [[2, -12, [4], 5]], [0, "99CMgyo2VC/IXcOzF+PNDQ", 1, 0], [5, 128, 128], [-100, 100, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 0.7, 2, 1], [1, 0, 0, -45]], [8, "xingxing", 2, [[12, true, 20, 999, 0.5, 0.2, -90, 360, 30, 30, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 200, 10, -62.1100006, -300, 10, -13, [8], [4, 4294704895], [4, 838860800], [4, 2533359615], [4, 335544320], 9, 10]], [0, "d2mmQCBQxF574QHf7I+h/3", 1, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 2, 0, -1, 4, 0, -2, 3, 0, -3, 7, 0, 0, 3, 0, -1, 5, 0, -2, 6, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 5, 1, 13], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, 3, -1], [0, 3, 0, 1, 0, 1, 0, 4, 0, 5, 6, 2, 2]]