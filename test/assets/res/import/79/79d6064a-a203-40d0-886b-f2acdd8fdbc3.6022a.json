[1, ["ecpdLyjvZBwrvm+cedCcQy", "c8/5lFzxtMMrvh3iM2K/Eo", "2f9U/IlQ9LXKwFhq4UOQ4X", "b7ArFDStRAs59szCPjQ6Bi", "03eu84pZpKpYQqIsvqkoBD", "2bKDZOMtxOUKgEpSiRd8En", "8dneIxR/BA/aHQRzwH9SiN", "17SSgDHfFAyJeZcSWy8DbC", "290AumeORPHosWfQL33MpC", "03TC/8KaBOCpCE8T734QFT", "05QQVoL5pPdLONHCR2NQy3", "1auhPQHZpFy5pYsCfSAhw/"], ["node", "_spriteFrame", "_defaultClip", "root", "data"], [["cc.Node", ["_name", "_opacity", "_skewX", "_skewY", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], -1, 4, 1, 9, 5, 7, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Mask", ["_N$alphaThreshold", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[3, 0, 1, 2, 2], [1, 2, 3, 4, 1], [0, 0, 5, 6, 4, 7, 2], [0, 0, 5, 6, 4, 7, 8, 2], [2, 1, 2, 3, 4, 2], [0, 0, 5, 6, 4, 7, 10, 8, 2], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 4, 3], [0, 0, 5, 9, 6, 4, 7, 8, 2], [0, 0, 5, 9, 6, 4, 7, 2], [0, 0, 5, 6, 4, 11, 7, 8, 12, 2], [0, 0, 2, 3, 5, 9, 6, 4, 7, 8, 4], [0, 0, 1, 2, 3, 5, 6, 4, 7, 8, 5], [2, 2, 3, 1], [4, 0, 2], [0, 0, 9, 6, 4, 2], [0, 0, 1, 5, 9, 4, 8, 3], [0, 0, 5, 9, 4, 2], [0, 0, 1, 5, 6, 4, 7, 8, 3], [0, 0, 5, 9, 6, 4, 7, 10, 8, 2], [0, 0, 1, 5, 6, 4, 7, 10, 8, 3], [3, 1, 2, 1], [2, 0, 2, 3, 2], [5, 0, 1, 2, 3, 2]], [[14, "effect_nw_02_back"], [15, "effect_nw_02_back", [-3], [[23, true, -2, [64], 63]], [21, -1, 0]], [16, "effect_nw_02_back", 0, 1, [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [0, "85ijbKTn5HKpUr3LkwkoQs", 1, 0], [0, 112.383, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [17, "effect_nw_zhucang", 2, [-16, -17, -18, -19, -20, -21, -22, -23, -24, -25], [0, "b3ZPNu39lLZZmnjOWNa2Ur", 1, 0]], [19, "effect_nw_02_bianjie01", 2, [-27, -28], [[22, 0, -26, [28]]], [0, "afik3zCDBFLYNDwi6IVNxI", 1, 0], [5, 500, 115], [0, 0.5, 0], [0, 170.42, 0, 0, 0, 0, 1, 1.1, 1, 1]], [8, "effect_nw_zhuzi01", 2, [-30, -31], [[4, 2, -29, [37], 38]], [0, "31SJS3mLNIyLYwX3iK56j+", 1, 0], [5, 115, 199], [-300, 270, 0, 0, 0, 0, 1, -1.5, 1.5, 0.5]], [8, "effect_nw_zhuzi02", 2, [-33, -34], [[4, 2, -32, [45], 46]], [0, "a5aab2v5pLVpTJsOIiXLrV", 1, 0], [5, 115, 199], [300, 264.297, 0, 0, 0, 0, 1, 1.5, 1.5, 0.5]], [11, "effect_nw_02_bianjie02", 1, -80.5, 2, [-36, -37], [[13, -35, [51]]], [0, "75du6alKVDhKC3KMG3xtX0", 1, 0], [5, 205, 500], [414.81, 49.148, 0, 0, 0, 0, 1, 0.9, 0.3, 1]], [11, "effect_nw_02_bianjie05", 1, -80.5, 2, [-39, -40], [[13, -38, [58]]], [0, "40ffAh6bxL86w+BTYkhlZb", 1, 0], [5, 205, 500], [-406.114, 50.1, 0, 0, 0, 0, 1, -0.9, 0.3, -1]], [9, "sweep", 5, [-42], [[4, 2, -41, [35], 36]], [0, "505NAvTGtEPYPzRhKc40D4", 1, 0], [5, 65, 152]], [9, "sweep", 6, [-44], [[4, 2, -43, [43], 44]], [0, "1fEAqxjEZKg4xaxhrz25+d", 1, 0], [5, 65, 152]], [3, "nw_02_di", 2, [[1, -45, [0], 1]], [0, "73qclZB2BOFbj2NsqyB8mX", 1, 0], [5, 454, 322], [7.364, -173.319, 0, 0, 0, 0, 1, 2.25, 2.2, 1]], [2, "fx_teleport_di_001_001", 3, [[1, -46, [2], 3]], [0, "eeY5aF39RH7YXtLdv4nwxF", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_001_002", 3, [[1, -47, [4], 5]], [0, "79EKdQT+pLXLQ4JjY8T1Br", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_002_001", 3, [[1, -48, [6], 7]], [0, "7dJxBRXrRAY4/MQX5l4Emg", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_002_002", 3, [[1, -49, [8], 9]], [0, "3cUm3/qzZAw5s/qSlSZ2pk", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_003_001", 3, [[1, -50, [10], 11]], [0, "b52iIEVa9JC7OX2wMSIxss", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_003_002", 3, [[1, -51, [12], 13]], [0, "fdiitkA1tOf6iW6+mlPft9", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_004_001", 3, [[1, -52, [14], 15]], [0, "45kRlMPAJOpZ0NXbktS5ea", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_004_002", 3, [[1, -53, [16], 17]], [0, "b9+L9TBjRH3LM6dLAzWuEL", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_005_001", 3, [[1, -54, [18], 19]], [0, "4aZ9fqgvVAT51y4SWiXqyl", 1, 0], [5, 86, 52]], [2, "fx_teleport_di_005_002", 3, [[1, -55, [20], 21]], [0, "d68D8GvG9O1qWIvX9Lj4Az", 1, 0], [5, 86, 52]], [18, "effect_nw_bo_001", 150, 2, [[1, -56, [22], 23]], [0, "bbpYlgWAFE451U7Q6ZF/fd", 1, 0], [5, 231, 231], [0, -97.184, 0, 0, 0, 0, 1, 1, 0.6, 1]], [5, "effect_nw_02_bianjie01", 4, [[6, 0, -57, [24], 25]], [0, "7d+DQWhw1P/JhPhtGEM0rH", 1, 0], [5, 500, 115], [0, 0.5, 0], [-244.012, 0, 0, 0, 0, 0, 1, 2, 1, 1]], [3, "nw_02_niao01", 4, [[1, -58, [26], 27]], [0, "caS8mygHVLtKmJQVTRgk9N", 1, 0], [5, 987, 68], [-743.333, 36.357, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "effect_nw_02_bianjie01_l", 150, 2, [[7, 1, 0, -59, [29], 30]], [0, "88Q9OV00ZLrZBv+UQmZlCH", 1, 0], [5, 500, 115], [0, 0.5, 0], [0, 170.42, 0, 0, 0, 0, 1, 1.1, 1.2, 1]], [3, "effect_nw_zhu01", 5, [[1, -60, [31], 32]], [0, "b4IDyT5IBDQ77mppbT2Wlf", 1, 0], [5, 115, 199], [0, 1.248, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "guang", 9, [[1, -61, [33], 34]], [0, "352dKjT11BX68DljtP11St", 1, 0], [4, 4294965735], [5, 128, 128], [71.961, -84.681, 0, 0, 0, 0.8660254037844386, 0.5000000000000001, 0.4, 2, 1], [1, 0, 0, 120]], [3, "effect_nw_zhu02", 6, [[1, -62, [39], 40]], [0, "44mvmQapBPy7dH8DoHNPUZ", 1, 0], [5, 115, 199], [0, 5.08, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "guang", 10, [[1, -63, [41], 42]], [0, "17tAwBpotDsqN67ydkwqq5", 1, 0], [4, 4294965735], [5, 128, 128], [71.961, -84.681, 0, 0, 0, 0.8660254037844386, 0.5000000000000001, 0.4, 2, 1], [1, 0, 0, 120]], [5, "effect_nw_02_bianjie01", 7, [[6, 0, -64, [47], 48]], [0, "01KjMP/qRLWrZ2X8k+nnol", 1, 0], [5, 500, 115], [0, 0.5, 0], [-115.061, -214.895, 0, 0, 0, 0, 1, 0.8, 4.5, 1]], [3, "nw_02_niao01", 7, [[1, -65, [49], 50]], [0, "952Ll+Mb5Nn5o1knWsCe+a", 1, 0], [5, 987, 68], [-196.19, -56.108, 0, 0, 0, 0, 1, 0.4, 4.5, 1]], [12, "effect_nw_02_bianjie02_l", 150, 1, -80.5, 2, [[7, 1, 0, -66, [52], 53]], [0, "82D+wT7PhH85btO0QGT1zR", 1, 0], [5, 205, 500], [415.232, 52.598, 0, 0, 0, 0, 1, 0.9, 0.3, 1]], [5, "effect_nw_02_bianjie01", 8, [[6, 0, -67, [54], 55]], [0, "94cwyCuMtIAJlVBDbks3Jd", 1, 0], [5, 500, 115], [0, 0.5, 0], [95.929, -213.285, 0, 0, 0, 0, 1, 0.8, 4.5, 1]], [3, "nw_02_niao01", 8, [[1, -68, [56], 57]], [0, "1c5ugO5PJOhZo5JJY3RApw", 1, 0], [5, 987, 68], [188.393, -47.059, 0, 0, 0, 0, 1, -0.4, 4.5, 1]], [12, "effect_nw_02_bianjie05_l", 150, 1, -80.5, 2, [[7, 1, 0, -69, [59], 60]], [0, "f9bae44jBPg44YWai2cwBt", 1, 0], [5, 205, 500], [-406.114, 50.1, 0, 0, 0, 0, 1, -0.9, 0.3, -1]], [3, "effect_nw_fazhen", 2, [[1, -70, [61], 62]], [0, "44yTtNodFH56teByR8oLnk", 1, 0], [5, 661, 381], [0, -112.572, 0, 0, 0, 0, 1, 1.7, 1.7, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -1, 11, 0, -2, 3, 0, -3, 22, 0, -4, 4, 0, -5, 25, 0, -6, 5, 0, -7, 6, 0, -8, 7, 0, -9, 32, 0, -10, 8, 0, -11, 35, 0, -12, 36, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, -6, 17, 0, -7, 18, 0, -8, 19, 0, -9, 20, 0, -10, 21, 0, 0, 4, 0, -1, 23, 0, -2, 24, 0, 0, 5, 0, -1, 26, 0, -2, 9, 0, 0, 6, 0, -1, 28, 0, -2, 10, 0, 0, 7, 0, -1, 30, 0, -2, 31, 0, 0, 8, 0, -1, 33, 0, -2, 34, 0, 0, 9, 0, -1, 27, 0, 0, 10, 0, -1, 29, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 4, 1, 70], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, 2, -1], [0, 9, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 10, 0, 3, 0, 4, 0, 0, 5, 0, 2, 0, 6, 0, 7, 0, 2, 0, 2, 0, 6, 0, 7, 0, 2, 0, 3, 0, 4, 0, 0, 5, 0, 3, 0, 4, 0, 0, 5, 0, 11, 8, 8]]