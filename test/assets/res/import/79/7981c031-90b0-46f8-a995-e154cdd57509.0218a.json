[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "17X+rO34RJaLE2Fu17LcVM", "a5hJI5OtNB0Yq0rp/qEfl/", "a17y/JnFdBio9pa+2aeg5/", "75oX5nSYZEk4q/f/ugvp6Q", "b26Ypy2ahEipSGLqFM5fnP", "013y6YvJdMkbGCvULm5buk", "d6f1jKq3dJaYBmMM2u7DUt", "90Q9ufMY9HOq/TTLYrpK1c"], ["node", "_spriteFrame", "_file", "_parent", "root", "_texture", "_mesh", "_defaultClip", "data"], [["cc.Node", ["_name", "_opacity", "_is3DNode", "_prefab", "_children", "_components", "_parent", "_trs", "_eulerAngles", "_color", "_contentSize"], 0, 4, 2, 9, 1, 7, 5, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSizeVar", "_positionType", "speed", "tangentialAccel", "radialAccel", "radialAccelVar", "endSize", "startSpinVar", "startSpin", "endSpin", "endSpinVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_file", "_spriteFrame", "gravity"], -17, 1, 3, 8, 8, 8, 8, 5, 6, 6, 5], ["cc.MotionStreak", ["_fadeTime", "_minSeg", "_stroke", "_dstBlendFactor", "node", "_materials", "_color", "_texture"], -1, 1, 3, 5, 6], ["cc.Prefab", ["_name"], 2], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["node", "_materials", "_mesh"], 3, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [0, 0, 6, 5, 3, 2], [1, 0, 1, 2], [6, 0, 1, 2, 1], [0, 0, 6, 4, 3, 7, 2], [0, 0, 6, 4, 3, 2], [0, 0, 2, 4, 5, 3, 3], [0, 0, 2, 4, 5, 3, 7, 8, 3], [0, 0, 1, 6, 5, 3, 9, 7, 8, 3], [0, 0, 1, 6, 5, 3, 9, 3], [0, 0, 6, 5, 3, 10, 7, 8, 2], [0, 0, 4, 3, 2], [3, 3, 0, 1, 2, 4, 5, 6, 7, 5], [3, 0, 1, 2, 4, 5, 6, 7, 4], [5, 0, 1, 2, 1], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 15, 9, 17, 18, 19, 10, 11, 12, 13, 14, 20, 21, 22, 23, 24, 25, 26, 27, 28, 20], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 15, 9, 16, 10, 11, 12, 13, 14, 20, 21, 22, 23, 24, 25, 26, 29, 27, 28, 18], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 16, 10, 11, 12, 13, 14, 20, 21, 22, 23, 24, 25, 26, 29, 27, 28, 17], [4, 0, 2], [0, 0, 4, 5, 3, 2], [0, 0, 1, 6, 4, 3, 7, 3], [1, 1, 2, 1], [7, 0, 1, 2, 3, 2]], [[18, "buff_7000514_02"], [19, "buff_7000514_02", [-3], [[22, true, -2, [96], 95]], [21, -1, 0]], [20, "feiwu", 0, 1, [-4, -5, -6, -7, -8], [0, "5bPLPbDppPY5mjmpv6hoqE", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.25, 0.25, 1]], [7, "huaban2", true, [-10, -11, -12], [[3, -9, [6], 7]], [0, "0dPufGvZVPf5ze9aq0+GeW", 1, 0], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [7, "huaban2", true, [-14, -15, -16], [[3, -13, [25], 26]], [0, "926J5fkEFKI4ZcfawI99R+", 1, 0], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [7, "huaban2", true, [-18, -19, -20], [[3, -17, [44], 45]], [0, "71wJcDbJhM25yldMEqm2ar", 1, 0], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [7, "huaban2", true, [-22, -23, -24], [[3, -21, [63], 64]], [0, "8bokQW6BVPA7B9xNp24iF3", 1, 0], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [7, "huaban2", true, [-26, -27, -28], [[3, -25, [82], 83]], [0, "8d5yV2BXNKJqp48ZnmoVWE", 1, 0], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [11, "snow", [-29, -30, -31], [0, "0f75VYrW5IppNgJFyuKToK", 1, 0]], [11, "snow", [-32, -33, -34], [0, "ee8EdazLJBN4eOsQiTG0Fc", 1, 0]], [11, "snow", [-35, -36, -37], [0, "11clUlUG9PObFeQPSJwuLp", 1, 0]], [11, "snow", [-38, -39, -40], [0, "44f6bwzLJHqpG9creulSI8", 1, 0]], [11, "snow", [-41, -42, -43], [0, "d0vxlzpwNIgrQsncBLO4mq", 1, 0]], [4, "1", 2, [-44, 8], [0, "8dlYZsGAlEyI6cSaEUgqsA", 1, 0], [-154.752, -273.928, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "huaban", true, [3], [[3, -45, [8], 9]], [0, "c8ZZAg+rxPta7c0b/doEM4", 1, 0]], [8, "effect_ty_twL", 50, 3, [[12, 1, 0.6, 0.1, 36, -47, [0], [4, 855628732], 1]], [2, "45YPSgGNZGmq57EKhj0kSB", -46], [4, 4294958012], [0, 0, 0, 0, 0, 0.8700289168705821, 0.49300069351776954, 1, 1, 1], [1, 0, 0, 120.92399999999999]], [9, "effect_ty_tw", 100, 3, [[13, 0.6, 0.1, 36, -49, [2], [4, 1677689442], 3]], [2, "88o4L6y75CQazr1BntI0hX", -48], [4, 4294935138]], [4, "2", 2, [-50, 9], [0, "3aDT0MOcdEbKgx7WAfCo+v", 1, 0], [-156.594, 564.066, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "huaban", true, [4], [[3, -51, [27], 28]], [0, "541OoD2JZHNLe7dvUhKZn6", 1, 0]], [8, "effect_ty_twL", 50, 4, [[12, 1, 0.6, 0.1, 36, -53, [19], [4, 855628732], 20]], [2, "45YPSgGNZGmq57EKhj0kSB", -52], [4, 4294958012], [0, 0, 0, 0, 0, 0.8700289168705821, 0.49300069351776954, 1, 1, 1], [1, 0, 0, 120.92399999999999]], [9, "effect_ty_tw", 100, 4, [[13, 0.6, 0.1, 36, -55, [21], [4, 1677689442], 22]], [2, "88o4L6y75CQazr1BntI0hX", -54], [4, 4294935138]], [4, "3", 2, [-56, 10], [0, "c31CmfiOdDBoQ4ws3bnGi2", 1, 0], [376.54, 259.418, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "huaban", true, [5], [[3, -57, [46], 47]], [0, "4462DGFo5EjovvXd0ZBX8I", 1, 0]], [8, "effect_ty_twL", 50, 5, [[12, 1, 0.6, 0.1, 36, -59, [38], [4, 855628732], 39]], [2, "45YPSgGNZGmq57EKhj0kSB", -58], [4, 4294958012], [0, 0, 0, 0, 0, 0.8700289168705821, 0.49300069351776954, 1, 1, 1], [1, 0, 0, 120.92399999999999]], [9, "effect_ty_tw", 100, 5, [[13, 0.6, 0.1, 36, -61, [40], [4, 1677689442], 41]], [2, "88o4L6y75CQazr1BntI0hX", -60], [4, 4294935138]], [4, "4", 2, [-62, 11], [0, "fcnPEIPXhDxp2IBi2n1rhm", 1, 0], [-404.317, 548.705, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "huaban", true, [6], [[3, -63, [65], 66]], [0, "d28sugxfNOrIRpPfUMrXxt", 1, 0]], [8, "effect_ty_twL", 50, 6, [[12, 1, 0.6, 0.1, 36, -65, [57], [4, 855628732], 58]], [2, "45YPSgGNZGmq57EKhj0kSB", -64], [4, 4294958012], [0, 0, 0, 0, 0, 0.8700289168705821, 0.49300069351776954, 1, 1, 1], [1, 0, 0, 120.92399999999999]], [9, "effect_ty_tw", 100, 6, [[13, 0.6, 0.1, 36, -67, [59], [4, 1677689442], 60]], [2, "88o4L6y75CQazr1BntI0hX", -66], [4, 4294935138]], [4, "5", 2, [-68, 12], [0, "ccL+8bzqBHo6GV0arQy9UQ", 1, 0], [474.2, -106.33, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "huaban", true, [7], [[3, -69, [84], 85]], [0, "fdtJv7kLlDwaCYFIdegMV+", 1, 0]], [8, "effect_ty_twL", 50, 7, [[12, 1, 0.6, 0.1, 36, -71, [76], [4, 855628732], 77]], [2, "45YPSgGNZGmq57EKhj0kSB", -70], [4, 4294958012], [0, 0, 0, 0, 0, 0.8700289168705821, 0.49300069351776954, 1, 1, 1], [1, 0, 0, 120.92399999999999]], [9, "effect_ty_tw", 100, 7, [[13, 0.6, 0.1, 36, -73, [78], [4, 1677689442], 79]], [2, "88o4L6y75CQazr1BntI0hX", -72], [4, 4294935138]], [5, "zidan", 13, [14], [0, "a03aKE3zFLNYEjNWLA7Mvu", 1, 0]], [10, "effect_dst_danhuaban", 3, [[14, -74, [4], 5]], [0, "3e8AwA8S9GuZyEknA6w6b7", 1, 0], [5, 99, 200], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [1, "smoke", 8, [[15, true, 5, 999, 0.3, 0.1, 0, 360, 100, 10, 20, 5, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 240, -62.1100006, -340, 50, -75, [10], [4, 1023147775], [4, 335544320], [4, 352321535], [4, 0], [0, 10, 10], 11, 12]], [0, "36/r5QfvVPOadWSs7EDqy2", 1, 0]], [1, "ice", 8, [[16, true, 3, 999, 0.2, 0.1, 0, 360, 110, 5, 20, 5, 60, 1, 240, -62.1100006, -340, 50, -76, [13], [4, 1694494164], [4, 838860800], [4, 1694494681], [4, 335544320], [0, 10, 10], [0, 10, 10], 14, 15]], [0, "31waEpZdhAbqkDdNqtD9Tv", 1, 0]], [1, "pice", 8, [[17, true, 3, 999, 0.2, 0.1, 0, 360, 100, 5, 5, 60, 1, 240, -62.1100006, -340, 50, -77, [16], [4, 1694498815], [4, 838860800], [4, 1694498815], [4, 335544320], [0, 10, 10], [0, 10, 10], 17, 18]], [0, "a2zJLMC89FVYJGTETOSRn6", 1, 0]], [5, "zidan", 17, [18], [0, "292kvrbuJF3ZEEdSQhzXhy", 1, 0]], [10, "effect_dst_danhuaban", 4, [[14, -78, [23], 24]], [0, "43P7mWBbNIjKzGFgI1E2f7", 1, 0], [5, 99, 200], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [1, "smoke", 9, [[15, true, 5, 999, 0.3, 0.1, 0, 360, 100, 10, 20, 5, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 240, -62.1100006, -340, 50, -79, [29], [4, 1023147775], [4, 335544320], [4, 352321535], [4, 0], [0, 10, 10], 30, 31]], [0, "d6zTWiRTRJt40irlKSwgHI", 1, 0]], [1, "ice", 9, [[16, true, 3, 999, 0.2, 0.1, 0, 360, 110, 5, 20, 5, 60, 1, 240, -62.1100006, -340, 50, -80, [32], [4, 1694494164], [4, 838860800], [4, 1694494681], [4, 335544320], [0, 10, 10], [0, 10, 10], 33, 34]], [0, "bca2upwJ9IC7848A4dbVYx", 1, 0]], [1, "pice", 9, [[17, true, 3, 999, 0.2, 0.1, 0, 360, 100, 5, 5, 60, 1, 240, -62.1100006, -340, 50, -81, [35], [4, 1694498815], [4, 838860800], [4, 1694498815], [4, 335544320], [0, 10, 10], [0, 10, 10], 36, 37]], [0, "57rTyO2/lGw50mUEkupo6d", 1, 0]], [5, "zidan", 21, [22], [0, "49G283LRxLpLHUaDOfxshE", 1, 0]], [10, "effect_dst_danhuaban", 5, [[14, -82, [42], 43]], [0, "02jl+R3tlJ2owCa9qycTyv", 1, 0], [5, 99, 200], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [1, "smoke", 10, [[15, true, 5, 999, 0.3, 0.1, 0, 360, 100, 10, 20, 5, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 240, -62.1100006, -340, 50, -83, [48], [4, 1023147775], [4, 335544320], [4, 352321535], [4, 0], [0, 10, 10], 49, 50]], [0, "f9gnzX9HRCtpYsZyELpA+f", 1, 0]], [1, "ice", 10, [[16, true, 3, 999, 0.2, 0.1, 0, 360, 110, 5, 20, 5, 60, 1, 240, -62.1100006, -340, 50, -84, [51], [4, 1694494164], [4, 838860800], [4, 1694494681], [4, 335544320], [0, 10, 10], [0, 10, 10], 52, 53]], [0, "108Sk3RQFCj6Ms+XWrbT8E", 1, 0]], [1, "pice", 10, [[17, true, 3, 999, 0.2, 0.1, 0, 360, 100, 5, 5, 60, 1, 240, -62.1100006, -340, 50, -85, [54], [4, 1694498815], [4, 838860800], [4, 1694498815], [4, 335544320], [0, 10, 10], [0, 10, 10], 55, 56]], [0, "e24u6yBcpNHrnXn/WDYEUd", 1, 0]], [5, "zidan", 25, [26], [0, "b5bHF5DVVDA6Q5UPvBngaP", 1, 0]], [10, "effect_dst_danhuaban", 6, [[14, -86, [61], 62]], [0, "3bXaUdQ+dJM7+oK4wr06GI", 1, 0], [5, 99, 200], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [1, "smoke", 11, [[15, true, 5, 999, 0.3, 0.1, 0, 360, 100, 10, 20, 5, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 240, -62.1100006, -340, 50, -87, [67], [4, 1023147775], [4, 335544320], [4, 352321535], [4, 0], [0, 10, 10], 68, 69]], [0, "bctSnhv2hKyZckqMrrzu5f", 1, 0]], [1, "ice", 11, [[16, true, 3, 999, 0.2, 0.1, 0, 360, 110, 5, 20, 5, 60, 1, 240, -62.1100006, -340, 50, -88, [70], [4, 1694494164], [4, 838860800], [4, 1694494681], [4, 335544320], [0, 10, 10], [0, 10, 10], 71, 72]], [0, "592HUZHQVD1ox+VWQTUKLC", 1, 0]], [1, "pice", 11, [[17, true, 3, 999, 0.2, 0.1, 0, 360, 100, 5, 5, 60, 1, 240, -62.1100006, -340, 50, -89, [73], [4, 1694498815], [4, 838860800], [4, 1694498815], [4, 335544320], [0, 10, 10], [0, 10, 10], 74, 75]], [0, "93dO4Zl3xMuZT5/smVk+uF", 1, 0]], [5, "zidan", 29, [30], [0, "59DM+ScQBLzZjLpmqadK4f", 1, 0]], [10, "effect_dst_danhuaban", 7, [[14, -90, [80], 81]], [0, "f4FddFuppEsptyAHvFOMWh", 1, 0], [5, 99, 200], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [1, "smoke", 12, [[15, true, 5, 999, 0.3, 0.1, 0, 360, 100, 10, 20, 5, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 240, -62.1100006, -340, 50, -91, [86], [4, 1023147775], [4, 335544320], [4, 352321535], [4, 0], [0, 10, 10], 87, 88]], [0, "26BnOXmMFBV5MUxCwJ+HTF", 1, 0]], [1, "ice", 12, [[16, true, 3, 999, 0.2, 0.1, 0, 360, 110, 5, 20, 5, 60, 1, 240, -62.1100006, -340, 50, -92, [89], [4, 1694494164], [4, 838860800], [4, 1694494681], [4, 335544320], [0, 10, 10], [0, 10, 10], 90, 91]], [0, "7e3BX3IGpJN6/LuyubsH2d", 1, 0]], [1, "pice", 12, [[17, true, 3, 999, 0.2, 0.1, 0, 360, 100, 5, 5, 60, 1, 240, -62.1100006, -340, 50, -93, [92], [4, 1694498815], [4, 838860800], [4, 1694498815], [4, 335544320], [0, 10, 10], [0, 10, 10], 93, 94]], [0, "0cyfFkHXVFg7Dr5OfKZoxi", 1, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 2, 0, -1, 13, 0, -2, 17, 0, -3, 21, 0, -4, 25, 0, -5, 29, 0, 0, 3, 0, -1, 15, 0, -2, 16, 0, -3, 34, 0, 0, 4, 0, -1, 19, 0, -2, 20, 0, -3, 39, 0, 0, 5, 0, -1, 23, 0, -2, 24, 0, -3, 44, 0, 0, 6, 0, -1, 27, 0, -2, 28, 0, -3, 49, 0, 0, 7, 0, -1, 31, 0, -2, 32, 0, -3, 54, 0, -1, 35, 0, -2, 36, 0, -3, 37, 0, -1, 40, 0, -2, 41, 0, -3, 42, 0, -1, 45, 0, -2, 46, 0, -3, 47, 0, -1, 50, 0, -2, 51, 0, -3, 52, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, -1, 33, 0, 0, 14, 0, 4, 15, 0, 0, 15, 0, 4, 16, 0, 0, 16, 0, -1, 38, 0, 0, 18, 0, 4, 19, 0, 0, 19, 0, 4, 20, 0, 0, 20, 0, -1, 43, 0, 0, 22, 0, 4, 23, 0, 0, 23, 0, 4, 24, 0, 0, 24, 0, -1, 48, 0, 0, 26, 0, 4, 27, 0, 0, 27, 0, 4, 28, 0, 0, 28, 0, -1, 53, 0, 0, 30, 0, 4, 31, 0, 0, 31, 0, 4, 32, 0, 0, 32, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 8, 1, 3, 3, 14, 4, 3, 18, 5, 3, 22, 6, 3, 26, 7, 3, 30, 8, 3, 13, 9, 3, 17, 10, 3, 21, 11, 3, 25, 12, 3, 29, 14, 3, 33, 18, 3, 38, 22, 3, 43, 26, 3, 48, 30, 3, 53, 93], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 5, -1, 5, -1, 1, -1, 6, -1, 6, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 5, -1, 5, -1, 1, -1, 6, -1, 6, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 5, -1, 5, -1, 1, -1, 6, -1, 6, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 5, -1, 5, -1, 1, -1, 6, -1, 6, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 5, -1, 5, -1, 1, -1, 6, -1, 6, -1, 2, 1, -1, 2, 1, -1, 2, 1, 7, -1], [0, 2, 0, 2, 0, 5, 3, 4, 3, 4, 0, 1, 6, 0, 1, 7, 0, 1, 8, 0, 2, 0, 2, 0, 5, 3, 4, 3, 4, 0, 1, 6, 0, 1, 7, 0, 1, 8, 0, 2, 0, 2, 0, 5, 3, 4, 3, 4, 0, 1, 6, 0, 1, 7, 0, 1, 8, 0, 2, 0, 2, 0, 5, 3, 4, 3, 4, 0, 1, 6, 0, 1, 7, 0, 1, 8, 0, 2, 0, 2, 0, 5, 3, 4, 3, 4, 0, 1, 6, 0, 1, 7, 0, 1, 8, 9, 9]]