[1, ["ecpdLyjvZBwrvm+cedCcQy", "dcxSX/++xE9YJZhNAF93Oy", "c88nPJ23hA3aFxFkGGKRzf", "1eaDItib1MxaUCThIJGJhr", "f7VEFHE71JbKh434Ak2YWH", "b2aHrECZ5APKGS/0d2hvT1", "8ccJ9QcrBGi4R0lofIChDc", "47VN3tSZlABoPX4ud1jeSt", "fddjE/tXJGN7lx2YNmXMIG", "12pTTpDx5GmoBC/nY7DeOR"], ["node", "_spriteFrame", "_file", "_parent", "root", "data", "_defaultClip"], [["cc.Node", ["_name", "_prefab", "_parent", "_components", "_children", "_trs", "_contentSize"], 2, 4, 1, 9, 2, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_dstBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "life", "angleVar", "startSize", "endSize", "endSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "lifeVar", "angle", "startSizeVar", "startSpin", "endSpinVar", "emitterMode", "tangentialAccelVar", "radialAccel", "radialAccelVar", "startRadius", "startRadiusVar", "rotatePerSVar", "rotationIsDir", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "_file", "_spriteFrame", "posVar", "gravity"], -23, 1, 3, 8, 8, 8, 8, 6, 6, 5, 5], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [0, 0, 2, 3, 1, 6, 2], [2, 1, 2, 3, 1], [2, 0, 1, 2, 3, 2], [0, 0, 4, 1, 5, 2], [0, 0, 2, 3, 1, 2], [3, 0, 1, 2, 3, 4, 13, 14, 5, 6, 15, 7, 16, 8, 17, 9, 18, 10, 11, 12, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 31, 34, 35, 32, 33, 26], [4, 0, 1, 2, 3], [5, 0, 2], [0, 0, 4, 3, 1, 2], [0, 0, 2, 4, 1, 2], [0, 0, 2, 4, 1, 5, 2], [0, 0, 2, 3, 1, 6, 5, 2], [0, 0, 2, 3, 1, 5, 2], [6, 0, 1, 2, 3, 2], [1, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 25, 26, 27, 28, 29, 30, 31, 32, 33, 15]], [[[[7, "effect_7000514_01_qy", 0.5833333333333334, [{}, "paths", 11, [{"01": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.5833333333333334, "value": 255}]}}, "01/effect_dst_01_lianghua": {"props": {"angle": [{"frame": 0.26666666666666666, "value": 360}, {"frame": 0.4, "value": 0}], "opacity": [{"frame": 0.35, "value": 0}, {"frame": 0.36666666666666664, "value": 255, "curve": "constant"}, {"frame": 0.4, "value": 0}], "y": [{"frame": 0.36666666666666664, "value": 112.6395}], "scale": []}}, "01/effect_dst_01_lianghua_L": {"props": {"angle": [{"frame": 0.26666666666666666, "value": 360}, {"frame": 0.38333333333333336, "value": 0}], "y": [{"frame": 0.3, "value": 88.926}, {"frame": 0.36666666666666664, "value": 112.6395}], "opacity": [{"frame": 0.36666666666666664, "value": 255}, {"frame": 0.38333333333333336, "value": 0}]}}, "01/effect_dst_01_lianghua_L/effect_dst_lianhuadi": {"props": {"opacity": [{"frame": 0.31666666666666665, "value": 0}, {"frame": 0.3333333333333333, "value": 255}]}}, "01/effect_dst_01_lianghua_L/effect_dst_lianghuaxing": {"props": {"opacity": [{"frame": 0.2833333333333333, "value": 0}, {"frame": 0.3, "value": 255}]}}, "01/effect_dst_01_lianghua_L/effect_dst_lianghuaban": {"props": {"opacity": [{"frame": 0.3, "value": 0}, {"frame": 0.31666666666666665, "value": 255}]}}, "01/effect_dst_01_xuli/xuli_lizi": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.016666666666666666, "value": 0}, {"frame": 0.03333333333333333, "value": 999.999985098839}, {"frame": 0.26666666666666666, "value": 999.999985098839}, {"frame": 0.2833333333333333, "value": 0}]}}}, "01/effect_dst_01_xuli/xuli_lizi02": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.016666666666666666, "value": 0}, {"frame": 0.03333333333333333, "value": 999.999985098839}, {"frame": 0.26666666666666666, "value": 999.999985098839}, {"frame": 0.2833333333333333, "value": 0}]}}}, "01/effect_dst_01_xuli/flash_1": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.3, "value": 0}, {"frame": 0.31666666666666665, "value": 30}, {"frame": 0.3333333333333333, "value": 0}]}}}}, "01/effect_dst_01_lianghua_L/effect_dst_zhuzi", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.31666666666666665, "value": 0}, {"frame": 0.3333333333333333, "value": 255}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.5, 1.5, 1]], [{"frame": 0.35}, "value", 8, [1, 1, 1, 1]]], 11, 11]]]]]]], 0, 0, [], [], []], [[[8, "effect_7000514_01_qy"], [9, "effect_7000514_01_qy", [-3], [[14, true, -2, [26], 25]], [15, -1, 0]], [4, "effect_dst_01_lian<PERSON>ua", [-4, -5, -6, -7], [0, "66tJpdSEpDyYwd7kzMLitB", 1, 0], [0, 150, 0, 0, 0, 0, 1, -0.5, 0.5, -1]], [4, "effect_dst_01_lianghua_L", [-8, -9, -10, -11], [0, "fc5asxekJHIZpbzu3e26NS", 1, 0], [0, 150, 0, 0, 0, 0, 1, -0.5, 0.5, 1]], [10, "01", 1, [2, 3, -12], [0, "30g/wSKFFHSpQU91cKZNNs", 1, 0]], [11, "effect_dst_01_xuli", 4, [-13, -14, -15], [0, "3fExroHk5DrZL4VMuJwckK", 1, 0], [0, 37.545, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_dst_zhuzi", 2, [[2, -16, [0], 1]], [0, "16Hj1FY7JMeKhn23kQT0z0", 1, 0], [5, 221, 195]], [1, "effect_dst_lianhuadi", 2, [[2, -17, [2], 3]], [0, "b3vC3ozu9Hbpc8OsGvxi2J", 1, 0], [5, 185, 209]], [1, "effect_dst_lianghuaxing", 2, [[2, -18, [4], 5]], [0, "32vWw2HltI9pQ2idRl2HVx", 1, 0], [5, 83, 83]], [1, "effect_dst_lian<PERSON><PERSON>ban", 2, [[2, -19, [6], 7]], [0, "44zSJ+T39Cp4dTux/Q9hnp", 1, 0], [5, 139, 119]], [12, "effect_dst_zhuzi", 3, [[3, 1, -20, [8], 9]], [0, "0a06qTwylCuZOUuBhr+myO", 1, 0], [5, 221, 195], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [1, "effect_dst_lianhuadi", 3, [[3, 1, -21, [10], 11]], [0, "31l2M9ZAhHI7cYjoqVFkUW", 1, 0], [5, 185, 209]], [1, "effect_dst_lianghuaxing", 3, [[3, 1, -22, [12], 13]], [0, "5eifE02SZKCoW3kLNRKiWM", 1, 0], [5, 83, 83]], [1, "effect_dst_lian<PERSON><PERSON>ban", 3, [[3, 1, -23, [14], 15]], [0, "43yFFHocJJxqnmnb+RQ6OJ", 1, 0], [5, 139, 119]], [5, "xuli_lizi", 5, [[6, 1, true, 10, 999.999985098839, 0.20000000298023224, 0.5, 360, 360, 3.369999885559082, 25, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 250, 150, 120, -24, [16], [4, 2751463423], [4, 335544320], [4, 520093695], [4, 3154116608], [0, 7, 7], [0, 0.25, 0.8600000143051147], 17, 18]], [0, "42CYMfUBRFLaqdPVb7p8Xp", 1, 0]], [5, "xuli_lizi02", 5, [[6, 1, true, 10, 999.999985098839, 0.20000000298023224, 0.5, 360, 360, 3.369999885559082, 25, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 250, 150, 120, -25, [19], [4, 2751448832], [4, 335544320], [4, 520081664], [4, 3154116608], [0, 7, 7], [0, 0.25, 0.8600000143051147], 20, 21]], [0, "e5YRJAWU5AmrBVLgr2bFRu", 1, 0]], [13, "flash_1", 5, [[16, 1, true, 3, 30, 0.2, 360, 300, 120, 10, 1, 5, 0, 0, true, -26, [22], [4, 4294955264], [4, 0], [4, 855631360], [4, 0], 23, 24]], [0, "22Cfdmb5VFrKWhULO45oY/", 1, 0], [0, 31.954, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 4, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, -3, 5, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 5, 1, 2, 3, 4, 3, 3, 4, 26], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, 6, -1], [0, 1, 0, 2, 0, 3, 0, 4, 0, 1, 0, 2, 0, 3, 0, 4, 0, 5, 6, 0, 5, 6, 0, 8, 9, 7, 7]]]]