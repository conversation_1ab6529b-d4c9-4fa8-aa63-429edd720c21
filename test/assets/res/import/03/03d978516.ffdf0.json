[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "b2Sk2xyX9EJq9g7dETFZsL", "bez0bI/UpJza5l/WCRTKWp", "8bTLRQ2JhCcLgjh3G+TFbk", "6aPDwNhNFHDoN7rdUmova7", "4do833UCpNNIa9cvMvGlTP", "a5hJI5OtNB0Yq0rp/qEfl/", "a17y/JnFdBio9pa+2aeg5/"], ["node", "_spriteFrame", "_file", "root", "data", "_mesh", "_defaultClip"], [["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "lifeVar", "angleVar", "startSize", "endSize", "startSpin", "startSpinVar", "endSpin", "endSpinVar", "_positionType", "emitterMode", "speed", "speedVar", "tangentialAccel", "rotationIsDir", "startRadius", "rotatePerS", "angle", "endRadius", "_dstBlendFactor", "rotatePerSVar", "endSizeVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "_file", "_spriteFrame", "posVar"], -21, 1, 3, 8, 8, 8, 8, 6, 6, 5], ["cc.Node", ["_name", "_opacity", "_is3DNode", "_prefab", "_components", "_parent", "_trs", "_children", "_eulerAngles", "_contentSize"], 0, 4, 9, 1, 7, 2, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["node", "_materials", "_mesh"], 3, 1, 3, 6], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -3]], [[2, 0, 1, 2, 2], [1, 0, 5, 4, 3, 6, 2], [1, 0, 5, 7, 4, 3, 6, 2], [0, 24, 25, 26, 27, 28, 29, 1], [1, 0, 5, 4, 3, 9, 6, 2], [0, 0, 1, 2, 3, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 18, 24, 25, 26, 27, 28, 29, 30, 31, 22], [0, 0, 1, 2, 3, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 24, 25, 26, 27, 28, 29, 30, 31, 21], [0, 21, 0, 1, 2, 3, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 22, 24, 25, 26, 27, 28, 29, 30, 31, 23], [0, 21, 0, 1, 2, 3, 19, 4, 5, 6, 23, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 18, 24, 25, 26, 27, 28, 29, 30, 31, 24], [6, 0, 1, 2, 1], [3, 0, 2], [1, 0, 7, 4, 3, 2], [1, 0, 5, 7, 3, 6, 2], [1, 0, 1, 2, 5, 7, 4, 3, 6, 8, 4], [4, 0, 1, 2, 3, 2], [2, 1, 2, 1], [0, 0, 1, 2, 3, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 18, 24, 25, 26, 27, 28, 29, 32, 30, 31, 22], [0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 18, 24, 25, 26, 27, 28, 29, 30, 31, 21], [0, 0, 1, 2, 3, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 24, 25, 26, 27, 28, 29, 32, 30, 31, 21], [0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 24, 25, 26, 27, 28, 29, 30, 31, 20], [0, 21, 0, 1, 2, 3, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 22, 24, 25, 26, 27, 28, 29, 32, 30, 31, 23], [0, 21, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 22, 24, 25, 26, 27, 28, 29, 30, 31, 22], [0, 21, 0, 1, 2, 3, 19, 4, 5, 6, 23, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 18, 24, 25, 26, 27, 28, 29, 32, 30, 31, 24], [0, 21, 0, 1, 2, 3, 4, 5, 6, 23, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 18, 24, 25, 26, 27, 28, 29, 30, 31, 23], [5, 0, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 7]], [[[[10, "buff_ssbq_02"], [11, "buff_ssbq_02", [-3], [[14, true, -2, [59], 58]], [15, -1, 0]], [12, "fazhen", 1, [-4, -5, -6, -7, -8], [0, "a0jlo+OItPGIi4IZDKUnEI", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [2, "bq_02_smoke01", 2, [-10, -11, -12, -13], [[3, -9, [18], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "79EqdsLudEfbyXz9YfDDKm", 1, 0], [-0.686, 82.321, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "bq_02_smoke02", 2, [-15, -16, -17, -18], [[3, -14, [31], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "b9CreXAZ9AJo77B2RobK/1", 1, 0], [-0.686, 82.321, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "bq_02_ice01", 2, [-20, -21, -22, -23], [[3, -19, [44], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "a9slZrfvhMkYa3LxZpCBI5", 1, 0], [-0.686, 82.321, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "bq_02_ice02", 2, [-25, -26, -27, -28], [[3, -24, [57], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "62oFGobulAW6pC5aZgbJ3a", 1, 0], [-0.686, 82.321, 0, 0, 0, 0, 1, 3, 3, 1]], [13, "plane", 0, true, 2, [-30, -31], [[24, -29, [4], 5]], [0, "57JHK0IFpGaYuACAQtuLez", 1, 0], [0, 0, 0, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [4, "ssbq_02_fazhen", 7, [[9, -32, [0], 1]], [0, "3cteukv+RN86180SwCV9MJ", 1, 0], [5, 273, 273], [0, 0, 0, 0, 0, 0, 1, 4, 4, 1]], [4, "ssbq_02_ling", 7, [[9, -33, [2], 3]], [0, "877uH4pbZDkbWbRFpf/er8", 1, 0], [5, 95, 111], [0, 0, 0, 0, 0, 0, 1, 4, 4, 1]], [1, "effect_ht_02_lizi_6", 3, [[5, true, 8, 0, 0.2, 180, 10, 35, 20, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 160, 160, 5, -34, [6], [4, 687845226], [4, 335544320], [4, 184527199], [4, 167772160], 7, 8]], [0, "45dDNB1qFEvqSrQkSUyZc4", 1, 0], [-25.697, -23.188, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "effect_ht_02_lizi_5", 3, [[16, true, 8, 0, 0.2, 0, 10, 35, 20, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 160, 160, 5, -35, [9], [4, 687845226], [4, 335544320], [4, 184527199], [4, 167772160], [0, 10, 10], 10, 11]], [0, "31BwEMPsxNubRjIbUNvhKK", 1, 0], [22.56, -28.202, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_4", 3, [[5, true, 60, 0, 0.2, -90, 55, 35, 20, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 160, 160, 5, -36, [12], [4, 687845226], [4, 335544320], [4, 184527199], [4, 167772160], 13, 14]], [0, "2cHbWPC7VO4q3mKmywIrnw", 1, 0], [0, -89.344, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_3", 3, [[17, true, 60, 0, 0.2, 55, 35, 20, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 160, 160, 5, -37, [15], [4, 687845226], [4, 335544320], [4, 184527199], [4, 167772160], 16, 17]], [0, "ac2aB24ZtH7LzgyP7P6bvu", 1, 0], [-6.267, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_6", 4, [[6, true, 3, 0, 0.2, 180, 10, 35, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 5, -38, [19], [4, 1358933866], [4, 335544320], [4, 184527199], [4, 167772160], 20, 21]], [0, "deV8V7peBD2JumCnWs6hCe", 1, 0], [-25.697, -23.188, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "effect_ht_02_lizi_5", 4, [[18, true, 3, 0, 0.2, 0, 10, 35, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 5, -39, [22], [4, 1358933866], [4, 335544320], [4, 184527199], [4, 167772160], [0, 10, 10], 23, 24]], [0, "a6FfyOijpNPb4fHUAofe6/", 1, 0], [22.56, -28.202, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_4", 4, [[6, true, 6, 0, 0.2, -90, 55, 35, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 5, -40, [25], [4, 1358933866], [4, 335544320], [4, 184527199], [4, 167772160], 26, 27]], [0, "bavLqr41ZIBqgEfaHJTrOy", 1, 0], [0, -89.344, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_3", 4, [[19, true, 6, 0, 0.2, 55, 35, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 5, -41, [28], [4, 1358933866], [4, 335544320], [4, 184527199], [4, 167772160], 29, 30]], [0, "06z4JFrAhM0JRjk6jhRhTc", 1, 0], [-6.267, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_6", 5, [[7, 1, true, 3, 0, 0.2, 180, 10, 15, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 20, 40, -42, [32], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], 33, 34]], [0, "cfOi9hJPZA65fhNBf6e47e", 1, 0], [-25.697, -23.188, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "effect_ht_02_lizi_5", 5, [[20, 1, true, 3, 0, 0.2, 0, 10, 15, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 20, 40, -43, [35], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], [0, 10, 10], 36, 37]], [0, "c5tE4WUBlPWojoQyWTYRTQ", 1, 0], [22.56, -28.202, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_4", 5, [[7, 1, true, 6, 0, 0.2, -90, 55, 15, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 20, 40, -44, [38], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], 39, 40]], [0, "55wVTn0J5LooK77maCm7V4", 1, 0], [0, -89.344, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_3", 5, [[21, 1, true, 6, 0, 0.2, 55, 15, 5, 30, 150, 50, 120, 1, 1, 400, 0, 0, true, 140, 20, 40, -45, [41], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], 42, 43]], [0, "c13Kmh9T9F8L8xNPAy/MGl", 1, 0], [-6.267, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_6", 6, [[8, 1, true, 2, 0, 0.2, 180, 10, 15, 12, 5, 30, 40, 50, 30, 1, 1, 400, 0, 0, true, 160, 160, 5, -46, [45], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], 46, 47]], [0, "cdzY0KtANHf6PAkpP4EW+d", 1, 0], [-25.697, -23.188, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "effect_ht_02_lizi_5", 6, [[22, 1, true, 2, 0, 0.2, 0, 10, 15, 12, 5, 30, 40, 50, 30, 1, 1, 400, 0, 0, true, 160, 160, 5, -47, [48], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], [0, 10, 10], 49, 50]], [0, "fcdGDbCsxBgr/8SnXU+Vbt", 1, 0], [22.56, -28.202, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_4", 6, [[8, 1, true, 10, 0, 0.2, -90, 55, 15, 12, 5, 30, 40, 50, 30, 1, 1, 400, 0, 0, true, 160, 160, 5, -48, [51], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], 52, 53]], [0, "24+0LYugJIoZSXbi5IoRMk", 1, 0], [0, -89.344, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ht_02_lizi_3", 6, [[23, 1, true, 10, 0, 0.2, 55, 15, 12, 5, 30, 40, 50, 30, 1, 1, 400, 0, 0, true, 160, 160, 5, -49, [54], [4, 1023402438], [4, 335544320], [4, 184539834], [4, 167772160], 55, 56]], [0, "9dH4E3Uq9IS4VRLNmyLk7Q", 1, 0], [-6.267, 36, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -1, 7, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 6, 0, 0, 3, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, 0, 4, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, 0, 5, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, 0, 6, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, -4, 25, 0, 0, 7, 0, -1, 8, 0, -2, 9, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 4, 1, 49], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 5, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 6, -1], [0, 5, 0, 6, 7, 8, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, 1, 3, 0, 1, 3, 0, 1, 3, 0, 1, 3, 0, 0, 1, 3, 0, 1, 3, 0, 1, 3, 0, 1, 3, 0, 4, 4]], [[[25, "buff_ssbq_02", 2.4583333333333335, 24, 0.5, 2, {"paths": {"fazhen/plane": {"props": {"opacity": [{"frame": 0, "value": 150}]}}, "fazhen/plane/ssbq_02_fazhen": {"props": {"angle": [{"frame": 0, "value": 0}, {"frame": 2.4583333333333335, "value": 360}]}}, "fazhen/plane/ssbq_02_ling": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.5833333333333334, "value": 100}, {"frame": 1.2083333333333333, "value": 255}, {"frame": 1.8333333333333333, "value": 100}, {"frame": 2.4583333333333335, "value": 255}]}}, "fazhen/bq_02_smoke01/effect_ht_02_lizi_6": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_smoke01/effect_ht_02_lizi_5": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_smoke01/effect_ht_02_lizi_4": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_smoke01/effect_ht_02_lizi_3": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_smoke02/effect_ht_02_lizi_6": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_smoke02/effect_ht_02_lizi_5": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_smoke02/effect_ht_02_lizi_4": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_smoke02/effect_ht_02_lizi_3": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice01/effect_ht_02_lizi_6": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice01/effect_ht_02_lizi_5": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice01/effect_ht_02_lizi_4": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice01/effect_ht_02_lizi_3": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice02/effect_ht_02_lizi_6": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice02/effect_ht_02_lizi_5": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice02/effect_ht_02_lizi_4": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}, "fazhen/bq_02_ice02/effect_ht_02_lizi_3": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 50}]}}}}}]], 0, 0, [], [], []]]]