[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "10Hc0K34RBp6HQiWJgFmeI"], ["node", "root", "data", "_file"], [["cc.Node", ["_name", "_components", "_prefab", "_parent", "_trs", "_children", "_color", "_contentSize", "_anchorPoint"], 2, 9, 4, 1, 7, 2, 5, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips"], 3, 1, 3], ["cc.Sprite", ["_dstBlendFactor", "_sizeMode", "_isTrimmedMode", "node", "_materials"], 0, 1, 3], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angleVar", "startSize", "endSize", "endSizeVar", "startSpinVar", "_positionType", "speed", "speedVar", "tangentialAccel", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_file"], -12, 1, 3, 8, 8, 8, 8, 5, 6]], [[1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 6], [3, 0, 2], [0, 0, 5, 1, 2, 2], [0, 0, 3, 1, 2, 6, 7, 8, 4, 2], [0, 0, 3, 1, 2, 4, 2], [4, 0, 1, 1], [1, 1, 2, 1], [5, 0, 1, 2, 3, 4, 4], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 16]], [[[[1, "effect_item_ssr", 0.8, 30, 0.5, 2, [{}, "paths", 11, [{}, "effect_item_ssr_light", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 4, 4, 1]], [{"frame": 0.4}, "value", 8, [1, 3.5, 3.5, 1]], [{"frame": 0.8}, "value", 8, [1, 4, 4, 1]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[2, "effect_item_ssr"], [3, "effect_item_ssr", [-3, -4], [[6, -2, [3]]], [7, -1, 0]], [4, "effect_item_ssr_light", 1, [[8, 1, 2, false, -5, [0]]], [0, "56XKmM2/ZErZYGc7eXN/gv", 1, 0], [4, 4294902010], [5, 128, 128], [0, 0.5, 0.2], [0, 0, 0, 0, 0, 0, 1, 4, 4, 1]], [5, "effect_item_ssr_star", 1, [[9, 1, true, 10, 8, 0.4, 0.1, 5, 140, 100, 20, 60, 1, 300, 0, 0, -6, [1], [4, 4291821823], [4, 335544320], [4, 520028390], [4, 167772160], [0, 80, 80], 2]], [0, "32Hj9Dri1Aq4mOG8Ypt2eP", 1, 0], [0, 41.12, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 1, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 3, 0, 2, 1, 6], [0, 0, 0, 0], [-1, -1, 3, -1], [0, 0, 1, 2]]]]