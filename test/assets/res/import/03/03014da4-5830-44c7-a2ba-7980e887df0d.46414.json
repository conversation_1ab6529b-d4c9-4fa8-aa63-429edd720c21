[1, ["ecpdLyjvZBwrvm+cedCcQy", "3cfZUxmdlEEYpEZ30q/IBA", "d9dkwSdGNJFK8Jvz7lzizp", "a2MjXRFdtLlYQ5ouAFv/+R", "a6h5Low0JLQ6ljAnCJr3Xc", "5aaVMebNJIJ77cV2UrGVUu", "a6XQlubZdDubHS4WYNI5ob", "07B4EtkZpKaa2YZMTy2aS1", "30vpf9VttF8aiWUorNxd34", "77iLRzYk5DH4lTfgxUYdLo", "70622YYJRN44HCM3GtrmUg", "1f1iT4BNFB47y3dAtt0PDj", "52nMayP2pCXJt6p/VT95A+", "b4ZXsSXIlOs54YMo0G3CCR", "8046mEZINE4KVnRyWhzmRc"], ["node", "_spriteFrame", "_N$file", "root", "btnClose", "btnUse", "btnMax", "btnAdd", "btnSub", "btnMin", "inputBox", "itemParent", "itemPrefab", "btnTouch", "select2", "select", "icon", "_N$content", "data"], [["cc.Node", ["_name", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_anchorPoint", "_color"], 2, 4, 5, 1, 9, 7, 2, 5, 5], ["cc.Node", ["_name", "_opacity", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_children"], 0, 1, 12, 4, 5, 7, 5, 5, 2], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_N$horizontalAlign", "_N$verticalAlign", "_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_enableWrapText", "_N$overflow", "_styleFlags", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "node"], -2, 1], ["3039cCGLWFIeqCPOGQnWhje", ["scaleMin", "scaleMax", "stopPropagation", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingTop", "_N$paddingBottom", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -4, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 1, 1, 1], ["9d5d9Vd/BBMo7pf+dGBfTsu", ["node", "icon", "select", "select2", "btnTouch"], 3, 1, 1, 1, 1, 1], ["693778GB3xHDLJlZpA9aH4r", ["node", "itemPrefab", "itemParent", "inputBox", "btnMin", "btnSub", "btnAdd", "btnMax", "btnUse", "btnClose"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 1, 2, 2], [2, 2, 3, 4, 1], [2, 1, 0, 2, 3, 4, 3], [0, 0, 3, 4, 1, 2, 2], [1, 0, 3, 10, 4, 5, 6, 7, 2], [5, 3, 1], [4, 0, 1, 2, 5, 4], [0, 0, 3, 4, 1, 2, 5, 2], [10, 0, 1, 2, 2], [0, 0, 3, 4, 1, 8, 2, 5, 2], [1, 0, 3, 4, 5, 6, 2], [2, 1, 0, 2, 3, 3], [4, 3, 0, 4, 1, 2, 5, 6], [7, 0, 2], [0, 0, 6, 4, 1, 2, 5, 2], [0, 0, 3, 6, 4, 1, 2, 7, 5, 2], [0, 0, 3, 6, 4, 1, 2, 7, 2], [0, 0, 3, 4, 1, 2, 7, 2], [0, 0, 3, 6, 1, 2, 5, 2], [0, 0, 3, 6, 4, 1, 2, 5, 2], [0, 0, 3, 1, 2, 2], [1, 0, 1, 3, 4, 5, 8, 6, 3], [1, 0, 2, 3, 4, 5, 6, 9, 7, 3], [1, 0, 3, 4, 5, 8, 6, 9, 7, 2], [8, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 2, 3, 4, 2], [4, 3, 0, 1, 2, 5, 5], [5, 0, 1, 3, 3], [5, 2, 3, 2], [6, 1, 2, 1], [9, 0, 1], [3, 2, 3, 4, 5, 0, 1, 9, 10, 11, 7], [3, 2, 3, 4, 0, 1, 9, 10, 6], [3, 6, 5, 0, 1, 7, 9, 10, 6], [3, 2, 3, 4, 6, 5, 8, 0, 1, 7, 9, 10, 10], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 3, 4, 5, 3], [15, 0, 1, 2, 3, 4, 1], [16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1]], [[13, "BagGiftSelect"], [14, "BagGiftView", [-13, -14, -15, -16, -17, -18, -19, -20], [[40, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2], [6, 45, 600, 600, -12]], [29, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "<PERSON><PERSON><PERSON>", 1, [-26, -27, -28, -29], [[39, -25, -24, -23, -22, -21]], [0, "73KO4H4o9Ol5Xwfg+Lp//I", 1, 0], [5, 119, 119], [-753.4100000000001, 97.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "conBtns", 1, [-30, -31, -32, -33, -34], [0, "e3VKL79rZLXLKENMngiIxw", 1, 0], [5, 510, 60], [0, -124.024, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "buyEditBox", 3, [-36, -37, -38], [-35], [0, "a3J8dXYidKF4LE1TdC/p1l", 1, 0], [5, 160, 50], [0.632, 1.509, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "mask", 128, 1, [[[25, 0, -39, [0], 1], [6, 45, 100, 100, -40], -41], 4, 4, 1], [0, "06Gzt/TcdMtau4PE1Zo/wn", 1, 0], [4, 4278190080], [5, 768, 1366]], [4, "btnSelect", 1, [-44], [[[2, 1, 0, -42, [9], 10], -43], 4, 1], [0, "45srZeD09FmapAJto+WdKo", 1, 0], [5, 216, 81], [0, -211.623, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", 1, [-48], [[11, 1, 0, -45, [12]], [37, false, 0.75, 0.23, null, null, -47, -46]], [0, "4aq4JajstGYYRZeE5wa2yI", 1, 0], [5, 550, 250], [0, 0, 1], [-271.211, 165, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "view", 7, [-51], [[36, 0, -49, [11]], [6, 45, 240, 250, -50]], [0, "41MpJUWDJAIppYzpSoJNiI", 1, 0], [5, 550, 250], [0, 0, 1]], [17, "content", 8, [[35, 1, 3, 22, 8, 8, 8, 8, -52, [5, 550, 135]]], [0, "c6N0jY0ClMfa2mcJ8vdTzI", 1, 0], [5, 550, 135], [0, 0, 1]], [4, "btnMin", 3, [-55], [[[2, 1, 0, -53, [15], 16], -54], 4, 1], [0, "87QiEIyjdMKYp73Al7UNs2", 1, 0], [5, 75, 56], [-195.303, 1.257, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "TEXT_LABEL", false, 4, [[-56, [12, 0, 45, 2, 158, 40, -57], [8, 2, -58, [4, 4278190080]]], 1, 4, 4], [0, "410OLtRH1Lqqp63E5scS0S", 1, 0], [5, 140, 39], [0, 0, 1], [-69, 19.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnReduce", 3, [-61], [[[1, -59, [22], 23], -60], 4, 1], [0, "3cheuvuedM6b9WMvlEQUE3", 1, 0], [5, 51, 52], [-117.802, 1.001, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnAdd", 3, [-64], [[[2, 1, 0, -62, [26], 27], -63], 4, 1], [0, "c5afCdmr9CRbzEL79iCWdS", 1, 0], [5, 56, 56], [122, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnMax", 3, [-67], [[[2, 1, 0, -65, [30], 31], -66], 4, 1], [0, "1cFIK5hAVFiohOa7/veVMW", 1, 0], [5, 75, 56], [199.67, 1.256999999999998, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bg", 1, [[2, 1, 0, -68, [2], 3], [30, -69]], [0, "35NqrkgSZCcIg1skSL9LCz", 1, 0], [5, 600, 600], [-2.694, -1.217, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "labName", 1, [[31, "自选礼包", 30, 30, false, 1, 1, -70, [4], 5], [8, 3, -71, [4, 4281341443]]], [0, "d0tBnaJHFNFKAjU67aM7f1", 1, 0], [4, 4294777032], [5, 126, 43.8], [0, 260, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lab", 1, [[32, "请选择一个奖励", 24, 0, 1, 1, -72, [6]], [8, 3, -73, [4, 4279963905]]], [0, "486hVEZOhDb4EMzAckgdow", 1, 0], [4, 4294624041], [5, 174, 36.239999999999995], [0, 204.58799999999997, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "BACKGROUND_SPRITE", 4, [[-74, [26, 0, 45, 160, 40, -75]], 1, 4], [0, "94TXwKZfNIQb4Nwio0rzhy", 1, 0], [5, 160, 50]], [23, "PLACEHOLDER_LABEL", 4, [[-76, [12, 0, 45, 2, 158, 40, -77]], 1, 4], [0, "afEtpc7otMdIRJVJRbUeUj", 1, 0], [4, 4290493371], [5, 158, 50], [0, 0, 1], [-78, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "imgGou", 2, [[1, -78, [32], 33]], [0, "f0r6viymhN8ImCa/GIPbw8", 1, 0], [5, 63, 55]], [3, "img<PERSON>uang", 2, [[2, 1, 0, -79, [34], 35]], [0, "45Q3kism9AjotpT16+/7UT", 1, 0], [5, 148, 148]], [10, "btnTouch", 2, [[-80, [6, 45, 100, 100, -81]], 1, 4], [0, "28a4q12zhK0pwoKI6BpTc0", 1, 0], [5, 119, 119]], [27, 1, 1, 5], [3, "imgUse", 6, [[1, -82, [7], 8]], [0, "8en/HvzTNI0Y6Dz0E/Imns", 1, 0], [5, 69, 33]], [5, 6], [7, "img_min", 10, [[1, -83, [13], 14]], [0, "966f0Yl9VN1JqYPdBvv9wl", 1, 0], [5, 52, 28], [0, 0, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [5, 10], [11, 1, 0, 18, [17]], [33, false, false, 1, 1, 1, 11, [18]], [34, "输入数量", 30, 30, false, false, 2, 1, 1, 1, 19, [19]], [38, 8, 3, 4, 29, 30, 28], [3, "reduce", 12, [[1, -84, [20], 21]], [0, "57s2SF/H9E/IuDJIJg/iHl", 1, 0], [5, 35, 14]], [5, 12], [3, "add", 13, [[1, -85, [24], 25]], [0, "1dg+Bb35pHjJkeVSsHOmZG", 1, 0], [5, 35, 36]], [5, 13], [7, "img_max", 14, [[1, -86, [28], 29]], [0, "ecqpS3u75GCpGl3oNJh0Z/", 1, 0], [5, 53, 28], [0, 0, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [5, 14], [20, "grid", 2, [0, "68YbKAlXVDmpdDMuJ+Xrii", 1, 0], [5, 119, 119]], [28, true, 22]], 0, [0, 3, 1, 0, 4, 23, 0, 5, 25, 0, 6, 37, 0, 7, 35, 0, 8, 33, 0, 9, 27, 0, 10, 31, 0, 11, 9, 0, 12, 2, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, -5, 6, 0, -6, 7, 0, -7, 3, 0, -8, 2, 0, 13, 39, 0, 14, 21, 0, 15, 20, 0, 16, 38, 0, 0, 2, 0, -1, 38, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -1, 10, 0, -2, 4, 0, -3, 12, 0, -4, 13, 0, -5, 14, 0, -1, 31, 0, -1, 18, 0, -2, 11, 0, -3, 19, 0, 0, 5, 0, 0, 5, 0, -3, 23, 0, 0, 6, 0, -2, 25, 0, -1, 24, 0, 0, 7, 0, 17, 9, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 10, 0, -2, 27, 0, -1, 26, 0, -1, 29, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 33, 0, -1, 32, 0, 0, 13, 0, -2, 35, 0, -1, 34, 0, 0, 14, 0, -2, 37, 0, -1, 36, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 28, 0, 0, 18, 0, -1, 30, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, -1, 39, 0, 0, 22, 0, 0, 24, 0, 0, 26, 0, 0, 32, 0, 0, 34, 0, 0, 36, 0, 18, 1, 86], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 29, 30], [-1, 1, -1, 1, -1, 2, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 1, 2, 2], [0, 3, 0, 4, 0, 2, 0, 0, 5, 0, 6, 0, 0, 0, 7, 0, 1, 0, 0, 0, 0, 8, 0, 1, 0, 9, 0, 1, 0, 10, 0, 1, 0, 11, 0, 12, 13, 14, 2]]