[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "ccM2XXu6tDCaecUtlNJseq", "61EqQ4yvZGcI1Cr+4KcGsL", "06Y9mGVtBFzoDxOF8lcEvb", "f1unEI4d1NqZMwhc5ngIjt", "4b12tE40pAObRrOTPw309T", "c4H2EP8JNHO7nhhLcT7+PP", "a56zSUR6tEI5Jw70T8ZLxk", "c2kuxJ0ElNvZ8egKzLvFYZ"], ["node", "_spriteFrame", "_file", "root", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_parent", "_components", "_children", "_contentSize", "_trs"], 1, 4, 1, 9, 2, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "angleVar", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_file", "_spriteFrame"], -6, 1, 3, 8, 8, 8, 8, 5, 6, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "curveData"], -1, 11]], [[1, 0, 1, 2, 2], [4, 0, 1, 2, 1], [0, 0, 3, 4, 2, 2], [0, 0, 3, 4, 2, 6, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 10], [0, 0, 3, 5, 4, 2, 6, 7, 2], [0, 0, 1, 3, 4, 2, 6, 7, 3], [2, 0, 2], [0, 0, 5, 4, 2, 2], [0, 0, 1, 3, 5, 2, 7, 3], [0, 0, 3, 5, 2, 2], [0, 0, 3, 5, 4, 2, 6, 2], [3, 0, 1, 2, 3, 2], [1, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5]], [[[[7, "effect_7000508_01_xl"], [8, "effect_7000508_01_xl", [-3], [[12, true, -2, [47], 46]], [13, -1, 0]], [9, "effect_7000508_01_xl", 0, 1, [-4, -5, -6, -7, -8, -9, -10, -11, -12], [0, "95c+T3djtC7b64LA4fS1AK", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [10, "fuwen", 2, [-13, -14, -15, -16, -17, -18], [0, "8aMlhcGOxA3InRNIjNVMtq", 1, 0]], [5, "7000508_fuwen_001", 2, [-20], [[1, -19, [24], 25]], [0, "91G230eB5Hq4PIo8EwUI9o", 1, 0], [5, 210, 244], [-135.512, -1.266, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "7000508_fuwen_002", 2, [-22], [[1, -21, [28], 29]], [0, "353Cay/PtKXb+rTaflQfuu", 1, 0], [5, 248, 256], [140.577, -1.266, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "7000508_fuwen_003", 2, [-24], [[1, -23, [32], 33]], [0, "1bt7n9OeRNp7hE4eveXH6G", 1, 0], [5, 180, 256], [181.99, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "7000508_fuwen_004", 2, [-26], [[1, -25, [36], 37]], [0, "a8b1rA0vJHn4/9hrvDvJm/", 1, 0], [5, 184, 256]], [5, "7000508_fuwen_005", 2, [-28], [[1, -27, [40], 41]], [0, "e1Er+hUpdOLYrCKDMPzy0+", 1, 0], [5, 238, 256], [-210.696, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "7000508_fuwen_006", 2, [-30], [[1, -29, [44], 45]], [0, "9bIPr0xOxG84/WeY9KX1B8", 1, 0], [5, 190, 256], [45.894, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "fuwen001", 3, [[4, true, 3, 0, 0, 50, 1, 500, 0, 0, -31, [0], [4, 4294967295], [4, 0], [4, 1694498815], [4, 0], [0, 200, 7], 1, 2]], [0, "86zi+V5ddPjpdr0C8bd7w6", 1, 0]], [2, "fuwen002", 3, [[4, true, 3, 0, 0, 50, 1, 500, 0, 0, -32, [3], [4, 4294967295], [4, 0], [4, 1694498815], [4, 0], [0, 200, 7], 4, 5]], [0, "c2qxff3+9CTLczwSboDLYS", 1, 0]], [2, "fuwen003", 3, [[4, true, 3, 0, 0, 50, 1, 500, 0, 0, -33, [6], [4, 4294967295], [4, 0], [4, 1694498815], [4, 0], [0, 200, 7], 7, 8]], [0, "6e+gsvHQ9Ls4kj6YBSh+Xk", 1, 0]], [2, "fuwen004", 3, [[4, true, 3, 0, 0, 50, 1, 500, 0, 0, -34, [9], [4, 4294967295], [4, 0], [4, 1694498815], [4, 0], [0, 200, 7], 10, 11]], [0, "35PKN1wxxFoqasNma2Tiex", 1, 0]], [2, "fuwen005", 3, [[4, true, 3, 0, 0, 50, 1, 500, 0, 0, -35, [12], [4, 4294967295], [4, 0], [4, 1694498815], [4, 0], [0, 200, 7], 13, 14]], [0, "1e/eqyCBdBvrUJDEM2RtgJ", 1, 0]], [2, "fuwen006", 3, [[4, true, 3, 0, 0, 50, 1, 500, 0, 0, -36, [15], [4, 4294967295], [4, 0], [4, 1694498815], [4, 0], [0, 200, 7], 16, 17]], [0, "7bBfVaCxBPV5uBdVqqQQeO", 1, 0]], [6, "7000508_fuwen02", 0, 2, [[1, -37, [18], 19]], [0, "fdva3l/ChOb6QpRaTCc2aE", 1, 0], [5, 546, 541], [0, 180.946, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "7000508_fuwen01", 0, 2, [[1, -38, [20], 21]], [0, "53RJdcC59NTKV1RiXx1dDe", 1, 0], [5, 546, 541], [0, 180.946, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "7000508_fuwen_001", 4, [[1, -39, [22], 23]], [0, "f8507xmglDgIj8/ubnwYPq", 1, 0], [5, 210, 244]], [3, "7000508_fuwen_002", 5, [[1, -40, [26], 27]], [0, "35RHw/tShCELCKyzAatZ70", 1, 0], [5, 248, 256]], [3, "7000508_fuwen_003", 6, [[1, -41, [30], 31]], [0, "12Al1+iEpFZJNFMaebCyhS", 1, 0], [5, 180, 256]], [3, "7000508_fuwen_004", 7, [[1, -42, [34], 35]], [0, "df+Iw08odNMblUxBmqs4iq", 1, 0], [5, 184, 256]], [3, "7000508_fuwen_005", 8, [[1, -43, [38], 39]], [0, "7aVD7mEBtAkoultTekXVTj", 1, 0], [5, 238, 256]], [3, "7000508_fuwen_006", 9, [[1, -44, [42], 43]], [0, "12uBM+BvZIupuNrY8y/LgC", 1, 0], [5, 190, 256]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, -2, 16, 0, -3, 17, 0, -4, 4, 0, -5, 5, 0, -6, 6, 0, -7, 7, 0, -8, 8, 0, -9, 9, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, -5, 14, 0, -6, 15, 0, 0, 4, 0, -1, 18, 0, 0, 5, 0, -1, 19, 0, 0, 6, 0, -1, 20, 0, 0, 7, 0, -1, 21, 0, 0, 8, 0, -1, 22, 0, 0, 9, 0, -1, 23, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 4, 1, 44], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 5, -1], [0, 1, 2, 0, 1, 3, 0, 1, 4, 0, 1, 5, 0, 1, 6, 0, 1, 7, 0, 8, 0, 8, 0, 2, 0, 2, 0, 3, 0, 3, 0, 4, 0, 4, 0, 5, 0, 5, 0, 6, 0, 6, 0, 7, 0, 7, 9, 9]], [[[14, "effect_7000508_01_xl", 3.5416666666666665, 24, 0.5, [{}, "paths", 11, [{"effect_7000508_01_xl/fuwen": {"comps": {"cc.ParticleSystem3D": {"rateOverTime": []}}, "props": {"opacity": [{"frame": 1.125, "value": 255}, {"frame": 1.25, "value": 0}]}}, "effect_7000508_01_xl": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 3.5416666666666665, "value": 255}]}}, "effect_7000508_01_xl/fuwen/fuwen001": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 2, "curve": "constant"}, {"frame": 1.125, "value": 0}]}}}, "effect_7000508_01_xl/fuwen/fuwen002": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.08333333333333333, "value": 0, "curve": "constant"}, {"frame": 0.125, "value": 2, "curve": "constant"}, {"frame": 1.125, "value": 0}]}}}, "effect_7000508_01_xl/fuwen/fuwen003": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 2, "curve": "constant"}, {"frame": 1.125, "value": 0}]}}}, "effect_7000508_01_xl/fuwen/fuwen004": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.25, "value": 0, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 2, "curve": "constant"}, {"frame": 1.125, "value": 0}]}}}, "effect_7000508_01_xl/fuwen/fuwen005": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.3333333333333333, "value": 0, "curve": "constant"}, {"frame": 0.375, "value": 2, "curve": "constant"}, {"frame": 1.125, "value": 0}]}}}, "effect_7000508_01_xl/fuwen/fuwen006": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.4166666666666667, "value": 0, "curve": "constant"}, {"frame": 0.4583333333333333, "value": 2, "curve": "constant"}, {"frame": 1.125, "value": 0}]}}}}, "effect_7000508_01_xl/7000508_fuwen02", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.1666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.2083333333333333, "value": 200}, {"frame": 1.25, "value": 255}, {"frame": 1.375, "value": 0}]}, "scale", 12, [[[{"frame": 1.2083333333333333}, "value", 8, [1, 0.1, 0.1, 1]], [{"frame": 1.25}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.375}, "value", 8, [1, 1.1, 1.1, 1]]], 11, 11, 11]]], "effect_7000508_01_xl/7000508_fuwen01", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.2083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.25, "value": 255, "curve": "cubicOut"}, {"frame": 1.3333333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 1.25}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.2916666666666667}, "value", 8, [1, 1.3, 1.3, 1]]], 11, 11]]], "effect_7000508_01_xl/7000508_fuwen_001", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.2916666666666667, "value": 255}, {"frame": 0.375, "value": 0}], "y": [{"frame": 0.16666666666666666, "value": -214.032, "curve": "cubicOut"}, {"frame": 0.375, "value": 410.334}]}, "scale", 12, [[[{"frame": 0.2916666666666667, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]]], 11]]], "effect_7000508_01_xl/7000508_fuwen_001/7000508_fuwen_001", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.25, "value": 0}, {"frame": 0.2916666666666667, "value": 255, "curve": [0.06, 0.12, 0.2377726218097447, 1]}, {"frame": 0.4166666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 0.2916666666666667, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.375}, "value", 8, [1, 2, 2, 1]]], 11, 11]]], "effect_7000508_01_xl/7000508_fuwen_002", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.3333333333333333, "value": 0, "curve": "constant"}, {"frame": 0.375, "value": 255}, {"frame": 0.4583333333333333, "value": 255}, {"frame": 0.5, "value": 0}], "y": [{"frame": 0.3333333333333333, "value": -138.044, "curve": "cubicOut"}, {"frame": 0.5416666666666666, "value": 473.658}]}, "scale", 12, [[[{"frame": 0.4166666666666667}, "value", 8, [1, 1, 1, 1]]], 11]]], "effect_7000508_01_xl/7000508_fuwen_002/7000508_fuwen_002", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.4166666666666667, "value": 0}, {"frame": 0.4583333333333333, "value": 255, "curve": [0.06, 0.12, 0.2377726218097447, 1]}, {"frame": 0.5833333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.4583333333333333, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.5416666666666666}, "value", 8, [1, 2, 2, 1]]], 11, 11]]], "effect_7000508_01_xl/7000508_fuwen_003", 11, [{}, "props", 11, [{"y": [{"frame": 0.5, "value": -214.032, "curve": "cubicOut"}, {"frame": 0.7083333333333334, "value": 436.93}], "opacity": [{"frame": 0.5, "value": 0, "curve": "constant"}, {"frame": 0.5416666666666666, "value": 255}, {"frame": 0.625, "value": 255}, {"frame": 0.6666666666666666, "value": 0}]}, "scale", 12, [[[{"frame": 0.5833333333333334}, "value", 8, [1, 1, 1, 1]]], 11]]], "effect_7000508_01_xl/7000508_fuwen_003/7000508_fuwen_003", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.5833333333333334, "value": 0}, {"frame": 0.625, "value": 255, "curve": [0.06, 0.12, 0.2377726218097447, 1]}, {"frame": 0.75, "value": 0}]}, "scale", 12, [[[{"frame": 0.625, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.7083333333333334}, "value", 8, [1, 2, 2, 1]]], 11, 11]]], "effect_7000508_01_xl/7000508_fuwen_004", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.6666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.7083333333333334, "value": 255}, {"frame": 0.7916666666666666, "value": 255}, {"frame": 0.8333333333333334, "value": 0}], "y": [{"frame": 0.6666666666666666, "value": -148.113, "curve": "cubicOut"}, {"frame": 0.875, "value": 421.391}]}, "scale", 12, [[[{"frame": 0.75}, "value", 8, [1, 1, 1, 1]]], 11]]], "effect_7000508_01_xl/7000508_fuwen_004/7000508_fuwen_004", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.75, "value": 0}, {"frame": 0.7916666666666666, "value": 255, "curve": [0.06, 0.12, 0.2377726218097447, 1]}, {"frame": 0.9166666666666666, "value": 0}]}, "scale", 12, [[[{"frame": 0.7916666666666666, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.875}, "value", 8, [1, 2, 2, 1]]], 11, 11]]], "effect_7000508_01_xl/7000508_fuwen_005", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.8333333333333334, "value": 0}, {"frame": 0.875, "value": 255}, {"frame": 0.9583333333333334, "value": 255}, {"frame": 1, "value": 0}], "y": [{"frame": 0.8333333333333334, "value": -162.715, "curve": "cubicOut"}, {"frame": 1.0416666666666667, "value": 456.855}]}, "scale", 12, [[[{"frame": 0.9166666666666666}, "value", 8, [1, 1, 1, 1]]], 11]]], "effect_7000508_01_xl/7000508_fuwen_005/7000508_fuwen_005", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.9166666666666666, "value": 0}, {"frame": 0.9583333333333334, "value": 255, "curve": [0.06, 0.12, 0.2377726218097447, 1]}, {"frame": 1.0833333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 0.9583333333333334, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.0416666666666667}, "value", 8, [1, 2, 2, 1]]], 11, 11]]], "effect_7000508_01_xl/7000508_fuwen_006", 11, [{}, "props", 11, [{"opacity": [{"frame": 1, "value": 0}, {"frame": 1.0416666666666667, "value": 255}, {"frame": 1.125, "value": 255}, {"frame": 1.1666666666666667, "value": 0}], "y": [{"frame": 1, "value": -175.232, "curve": "cubicOut"}, {"frame": 1.2083333333333333, "value": 375.497}]}, "scale", 12, [[[{"frame": 1.0833333333333333}, "value", 8, [1, 1, 1, 1]]], 11]]], "effect_7000508_01_xl/7000508_fuwen_006/7000508_fuwen_006", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.0833333333333333, "value": 0}, {"frame": 1.125, "value": 255, "curve": [0.06, 0.12, 0.2377726218097447, 1]}, {"frame": 1.25, "value": 0}]}, "scale", 12, [[[{"frame": 1.125, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.2083333333333333}, "value", 8, [1, 2, 2, 1]]], 11, 11]]]]]]], 0, 0, [], [], []]]]