[1, ["ecpdLyjvZBwrvm+cedCcQy", "d9dkwSdGNJFK8Jvz7lzizp", "8046mEZINE4KVnRyWhzmRc", "14GgDOeiVCvouRA+U0oYsl", "03UrZmEFJPza+ukb/TPyH3", "a2MjXRFdtLlYQ5ouAFv/+R", "82pBEiBedKyoljaBkZqgD1", "b2OllrSt9Hn7GW6qd97832", "14NfJyXi9Nf69Z6Z9nTyWG", "d2Y39lvT5IVqoEDn/8rZUC", "03Dm+GyLtDEJRrU4CwUfnE", "c6+tvCgmNLfotB5LUqVvXH", "51qGDtDAxHj6RXYYL3VF9v", "a6XQlubZdDubHS4WYNI5ob", "e32AxSAmFE2K3vYMLamvSY"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$font", "root", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attrValue4", "attrValue3", "attrValue2", "attrValue1", "attrLable4", "attrLable3", "attrLable2", "attrLable1", "itemImg", "itemDesc", "heroItemDesc", "upBtn", "heroDesc", "<PERSON><PERSON><PERSON>", "heroType", "heroImag", "cardType", "bgImag", "btnClose", "data", "_textureSetter"], [["cc.Node", ["_name", "_prefab", "_contentSize", "_children", "_components", "_trs", "_parent", "_anchorPoint", "_color"], 2, 4, 5, 2, 9, 7, 1, 5, 5], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_children"], 1, 1, 12, 4, 5, 7, 5, 5, 2], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalHeight", "_originalWidth", "_top", "_bottom", "node"], -2, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 2, 4, 5, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["3039cCGLWFIeqCPOGQnWhje", ["scaleMin", "scaleMax", "node"], 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "node"], -1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["6b390WhenNBJ4k51a0Q6J9S", ["node", "btnClose", "bgImag", "cardType", "heroImag", "heroType", "<PERSON><PERSON><PERSON>", "heroDesc", "upBtn", "heroItemDesc", "itemDesc", "itemImg", "attrLable1", "attrLable2", "attrLable3", "attrLable4", "attrValue1", "attrValue2", "attrValue3", "attrValue4", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Layout", ["_N$layoutType", "node", "_layoutSize"], 2, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["<PERSON><PERSON>", ["horizontal", "inertia", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1]], [[6, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 7, 6, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 7], [2, 1, 0, 3, 4, 5, 3], [0, 0, 6, 4, 1, 2, 5, 2], [2, 3, 4, 5, 1], [17, 0, 1], [0, 0, 6, 3, 4, 1, 2, 5, 2], [1, 0, 2, 3, 4, 5, 6, 2], [2, 0, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [15, 0, 1, 2, 2], [0, 0, 3, 4, 1, 2, 5, 2], [0, 0, 6, 4, 1, 8, 2, 5, 2], [0, 0, 6, 3, 4, 1, 2, 2], [0, 0, 3, 1, 2, 5, 2], [0, 0, 6, 4, 1, 2, 2], [1, 0, 2, 9, 3, 4, 5, 6, 2], [13, 0, 1], [11, 0, 2], [0, 0, 3, 4, 1, 2, 7, 5, 2], [0, 0, 6, 3, 1, 2, 5, 2], [0, 0, 6, 3, 1, 2], [1, 0, 1, 2, 3, 4, 7, 5, 3], [1, 0, 2, 3, 4, 7, 5, 8, 6, 2], [1, 0, 2, 3, 4, 5, 8, 6, 2], [5, 0, 1, 2, 3, 4, 5, 6, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1], [4, 0, 5, 2], [4, 0, 2, 1, 5, 4], [4, 0, 3, 4, 1, 5, 5], [6, 1, 2, 1], [2, 0, 2, 3, 4, 3], [14, 0, 1, 2, 2], [3, 0, 1, 4, 5, 6, 7, 5], [3, 0, 1, 2, 3, 6, 7, 5], [16, 0, 1, 2, 3, 4, 5, 6, 7, 7], [7, 0, 1, 2, 2], [7, 1, 2, 1], [8, 0, 1, 2, 3], [8, 2, 1], [9, 0, 1, 2, 3, 4, 5], [9, 0, 1, 2, 4, 4]], [[[[19, "HandBookUpLevel"], [12, "HandBookUpLevel", [-24, -25], [[27, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2], [28, 45, -23]], [31, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "New Node", 1, [-27, -28, -29, -30, -31, -32, -33], [[18, -26]], [0, "879WetsaJHIZT1osPs43IO", 1, 0], [5, 636, 739]], [12, "New Node", [-35, -36, -37, -38], [[3, 1, 0, -34, [29], 30]], [0, "68EmUmRbBDeYDo2t3lC+N6", 1, 0], [5, 581, 54], [0, -55.783, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "New Node", [-40, -41, -42, -43], [[3, 1, 0, -39, [41], 42]], [0, "edLEy1M/NMAqlOAZbFN2aP", 1, 0], [5, 581, 54], [0, -55.783, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "mask", 204, 1, [[[9, 0, -44, [0], 1], [29, 45, 100, 100, -45], [18, -46], -47], 4, 4, 4, 1], [0, "03tHGxV89O2Yix3f331IGk", 1, 0], [4, 4278190080], [5, 768, 1366]], [20, "content", [-49], [[33, 2, -48, [5, 375, 90.4]]], [0, "51yAxG22RPVpZdGpb9U6Wb", 1, 0], [5, 375, 90.4], [0, 0.5, 1], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "cardNode", 2, [-50, -51, -52, -53], [0, "4c9dL4YKNJZZoVoRrco7iK", 1, 0], [5, 120, 152], [-218.845, 183.894, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "New Node", [-54, -55, -56, 3], [0, "2ddKemyWBNbIFJDFEPXZZB", 1, 0], [5, 640, 50], [0, 105.189, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "New Node", [-57, -58, -59, 4], [0, "48Ld1n9mhEVofWIwQw+nHB", 1, 0], [5, 640, 50], [0, -26.57, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Sprite", 2, [-61, -62], [[9, 0, -60, [9], 10]], [0, "9a74Hx36dEu6i4/a3JwF5H", 1, 0], [5, 420, 4], [61.046, 198.183, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Node", 2, [8, 9], [[3, 1, 0, -63, [43], 44]], [0, "e0GuETmz1P77u8AvmhTpDO", 1, 0], [5, 581, 271], [0, -43.505, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "upBtnNode", 2, [-64, -65, -66], [0, "a1WM9qIftEnrjJEGHVutea", 1, 0]], [17, "itemImg", 12, [-69], [[[5, -67, [46], 47], -68], 4, 1], [0, "b7NSPBMnZLl62GkiuQo4Nm", 1, 0], [5, 103, 95], [-46.938, -209.014, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [17, "upBtn", 12, [-72], [[[3, 1, 0, -70, [50], 51], -71], 4, 1], [0, "beMYn5RdRF27fmee1YN4cv", 1, 0], [5, 187, 80], [0, -276.026, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title", 2, [[10, "升级", 36, 36, false, 1, 1, -73, [4], 5], [11, 3, -74, [4, 4279963905]]], [0, "06OxgCl2NDQ7aw+KJocCqB", 1, 0], [5, 78, 51.36], [0, 328.796, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lblClose", 2, [[34, "点击空白区域关闭界面", 29, 1, 1, -75, [6]], [30, 1, 861.101, 93.80499999999996, 50.4, -76]], [0, "70qsuPhzhOqIAycUARS9iB", 1, 0], [4, 4289901234], [5, 290, 50.4], [0, -516.801, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "<PERSON><PERSON><PERSON>", 10, [[-77, [11, 3, -78, [4, 4279636240]]], 1, 4], [0, "306FMSf1VBG47S2kX1H2zH", 1, 0], [4, 4284312575], [5, 83.36, 53.88], [0, 0, 0.5], [-195.175, 34.431, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "descScrollView", 10, [-80], [[36, false, false, 0.75, 0.23, null, null, -79, 6]], [0, "c3oEs+vC1Kl6b5rBknoXMU", 1, 0], [5, 375, 100], [-10.166, -53.956, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "view", 18, [6], [[37, 0, -81, [8]]], [0, "97DmRnRO5HuY8Zr5cTJPbd", 1, 0], [5, 375, 100]], [8, "bgImag", 7, [[[9, 0, -82, [11], 12], -83], 4, 1], [0, "82MPStFBRLybG980cVglNj", 1, 0], [5, 120, 152], [1.388, -1.087, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "heroImag", 7, [-85], [[38, -84, [14]]], [0, "d3bK1dT6BPS6nG8KSw6rkQ", 1, 0], [5, 110, 142], [-2.717, -1.452, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "heroImag", 21, [[[32, 2, false, -86, [13]], -87], 4, 1], [0, "8aVd0jPalPwIpiSr0ivcon", 1, 0], [5, 204, 310], [5.263, -11.577, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [8, "cardType", 7, [[[3, 1, 0, -88, [15], 16], -89], 4, 1], [0, "48Fq+H4+1PJ4F81uOqkUYh", 1, 0], [5, 120, 152], [1.283, 0.038, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "heroType", 7, [[[9, 0, -90, [17], 18], -91], 4, 1], [0, "09ykV8hilKOLSnYZk2RP6+", 1, 0], [5, 25, 35], [-44.551, 56.494, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]], [25, "itemDesc", 13, [[-92, [11, 3, -93, [4, 4278190080]]], 1, 4], [0, "36bzDUapFGAas7TRqTWa7W", 1, 0], [5, 78.54, 51.36], [0, 0, 0.5], [77.941, -19.256, 0, 0, 0, 0, 1, 2, 2, 2]], [16, "New Label", 14, [[10, "升级", 30, 30, false, 1, 1, -94, [48], 49], [11, 3, -95, [4, 4278209683]]], [0, "5edCQmDl5KYrjZEi8wOoF3", 1, 0], [5, 66, 43.8]], [39, 1, 1, 5], [16, "imgBg", 2, [[3, 1, 0, -96, [2], 3]], [0, "8a33l93SVIao2F04bM8Qsw", 1, 0], [5, 636, 739]], [2, "伏羲", 38.68, 38.68, false, 1, 1, 17, [7]], [1, "heroDesc", 6, [-97], [0, "e27iYDBttP3aPTs9apeHKO", 1, 0], [4, 4286219567], [5, 375, 90.39999999999999], [0, 0, 1], [-187, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [41, false, "获得更多伙伴，即可激活更多 的图鉴。", 28, 375, 30], [6, 20], [6, 22], [6, 23], [6, 24], [4, "New Sprite", 8, [[5, -98, [19], 20]], [0, "ec4anZg9NBqavhHt66KXOk", 1, 0], [5, 106, 17], [136.953, -1.302, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "New Label", 8, [[10, "本级属性", 32, 32, false, 1, 1, -99, [21], 22]], [0, "e57qyl/VpPkagTzbQbJY8n", 1, 0], [4, 4286219567], [5, 128, 40.32], [0, -0.061, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Sprite", 8, [[5, -100, [23], 24]], [0, "49mP09OzlM/Lhw51/sP3a0", 1, 0], [5, 106, 17], [-139.595, -1.302, 0, 0, 0, 0, 1, -1, 1, 1]], [1, "attrLable1", 3, [-101], [0, "59SjwJPJxF7povI1jPoKzF", 1, 0], [4, 4286214951], [5, 52, 32.76], [0, 0, 0.5], [-248.716, -0.929, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "血量", 26, 26, false, 1, 1, 39, [25]], [1, "attrValue1", 3, [-102], [0, "ccRjBmZsBE5Jl5vmQudidI", 1, 0], [4, 4284544767], [5, 41.6, 40.32], [0, 0, 0.5], [-121.165, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "200", 32, 32, false, 1, 1, 41, [26]], [1, "attrLable2", 3, [-103], [0, "92wLg+wXVBM7iTp4NwDi3q", 1, 0], [4, 4286214951], [5, 52, 32.76], [0, 0, 0.5], [32.242, -0.929, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "攻击", 26, 26, false, 1, 1, 43, [27]], [1, "attrValue2", 3, [-104], [0, "92vr65dXNPY6+sjzLqdddt", 1, 0], [4, 4284544767], [5, 41.6, 40.32], [0, 0, 0.5], [173.051, -0.929, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "200", 32, 32, false, 1, 1, 45, [28]], [4, "New Sprite", 9, [[5, -105, [31], 32]], [0, "adyQDPuCBFGp95DQoym0d2", 1, 0], [5, 106, 17], [136.953, -1.302, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "New Label", 9, [[10, "下级属性", 32, 32, false, 1, 1, -106, [33], 34]], [0, "c9gc9/FHpGg7giaePBeHDv", 1, 0], [4, 4286219567], [5, 128, 40.32], [0, -0.061, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Sprite", 9, [[5, -107, [35], 36]], [0, "15R1CVoDpA9qTV8d0zy1gx", 1, 0], [5, 106, 17], [-139.595, -1.302, 0, 0, 0, 0, 1, -1, 1, 1]], [1, "attrLable3", 4, [-108], [0, "0649hXihNPQbfiQJ0D67+5", 1, 0], [4, 4286214951], [5, 52, 32.76], [0, 0, 0.5], [-248.716, -0.929, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "血量", 26, 26, false, 1, 1, 50, [37]], [1, "attrValue3", 4, [-109], [0, "11BZdHfSJIBLQNRThclexZ", 1, 0], [4, 4284544767], [5, 41.6, 40.32], [0, 0, 0.5], [-121.165, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "200", 32, 32, false, 1, 1, 52, [38]], [1, "attrLable4", 4, [-110], [0, "e3mDRyoS9AyZo0vXaJKGbo", 1, 0], [4, 4286214951], [5, 52, 32.76], [0, 0, 0.5], [32.242, -0.929, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "攻击", 26, 26, false, 1, 1, 54, [39]], [1, "attrValue4", 4, [-111], [0, "43j5aC08ZF74dIH+YJawFm", 1, 0], [4, 4284544767], [5, 41.6, 40.32], [0, 0, 0.5], [173.051, -0.929, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "200", 32, 32, false, 1, 1, 56, [40]], [35, "30/40", 36, 36, false, 25, [45]], [6, 13], [26, "heroItemDesc", 12, [-112], [0, "2aDTQga/lJDbqpwk5w35km", 1, 0], [4, 4288014384], [5, 99.63, 50.4], [0.477, -209, 0, 0, 0, 0, 1, 1, 1, 1]], [42, false, "60/30", 36, 60], [40, 14]], 0, [0, 5, 1, 0, 6, 6, 0, 7, 57, 0, 8, 53, 0, 9, 46, 0, 10, 42, 0, 11, 55, 0, 12, 51, 0, 13, 44, 0, 14, 40, 0, 15, 59, 0, 16, 58, 0, 17, 61, 0, 18, 62, 0, 19, 31, 0, 20, 29, 0, 21, 35, 0, 22, 33, 0, 23, 34, 0, 24, 32, 0, 25, 27, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, 0, 2, 0, -1, 28, 0, -2, 15, 0, -3, 16, 0, -4, 10, 0, -5, 7, 0, -6, 11, 0, -7, 12, 0, 0, 3, 0, -1, 39, 0, -2, 41, 0, -3, 43, 0, -4, 45, 0, 0, 4, 0, -1, 50, 0, -2, 52, 0, -3, 54, 0, -4, 56, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -4, 27, 0, 0, 6, 0, -1, 30, 0, -1, 20, 0, -2, 21, 0, -3, 23, 0, -4, 24, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -1, 47, 0, -2, 48, 0, -3, 49, 0, 0, 10, 0, -1, 17, 0, -2, 18, 0, 0, 11, 0, -1, 13, 0, -2, 60, 0, -3, 14, 0, 0, 13, 0, -2, 59, 0, -1, 25, 0, 0, 14, 0, -2, 62, 0, -1, 26, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 29, 0, 0, 17, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, 0, 20, 0, -2, 32, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, -2, 33, 0, 0, 23, 0, -2, 34, 0, 0, 24, 0, -2, 35, 0, -1, 58, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 28, 0, -1, 31, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, -1, 40, 0, -1, 42, 0, -1, 44, 0, -1, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, -1, 51, 0, -1, 53, 0, -1, 55, 0, -1, 57, 0, -1, 61, 0, 26, 1, 3, 3, 8, 4, 3, 9, 6, 3, 19, 8, 3, 11, 9, 3, 11, 112], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 31, 40, 42, 44, 46, 51, 53, 55, 57, 58, 61], [-1, 1, -1, 1, -1, 2, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 2, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 2, -1, 1, 2, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 4], [0, 5, 0, 6, 0, 1, 0, 0, 0, 0, 7, 0, 8, 0, 0, 0, 9, 0, 10, 0, 3, 0, 1, 0, 3, 0, 0, 0, 0, 0, 4, 0, 3, 0, 1, 0, 3, 0, 0, 0, 0, 0, 4, 0, 11, 0, 0, 12, 0, 1, 0, 13, 1, 1, 1, 2, 1, 2, 1, 2, 1, 2, 2, 1]], [[{"name": "1040001", "rect": [0, 8, 103, 95], "offset": [0, -4], "originalSize": [103, 103], "capInsets": [0, 0, 0, 0]}], [10], 0, [0], [27], [14]]]]