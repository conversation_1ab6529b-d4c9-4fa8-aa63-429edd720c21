[1, ["ecpdLyjvZBwrvm+cedCcQy", "832JD4FvFOoIFwTtQeXE6t", "c7D68qYPBCW7v6SaRIf9cc", "a2MjXRFdtLlYQ5ouAFv/+R", "bfVCoHdp1MN6gdBuwhB24D", "8chWxj+FJK5qqL2Vky0dG4", "1eb9p20NpPpa1oZ1yDR2Yn", "44kPOKdqtBhbBSr+huC0Sf", "a1iXCafldJSJ7yq7evXGXu", "b2aHrECZ5APKGS/0d2hvT1"], ["node", "_spriteFrame", "root", "data", "_parent", "_file", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_trs", "_parent", "_contentSize", "_children", "_color", "_eulerAngles"], 1, 4, 9, 7, 1, 5, 2, 5, 5], ["cc.ParticleSystem", ["_custom", "totalParticles", "life", "lifeVar", "angleVar", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "_dstBlendFactor", "emissionRate", "angle", "startSizeVar", "endSizeVar", "startSpin", "endSpin", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_spriteFrame", "_file"], -14, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], 0, 11]], [[2, 0, 1, 2, 2], [3, 1, 2, 3, 1], [0, 0, 5, 3, 2, 4, 9, 2], [0, 0, 5, 3, 2, 6, 4, 2], [1, 10, 0, 1, 11, 2, 3, 12, 4, 13, 5, 14, 15, 16, 6, 7, 8, 9, 17, 18, 19, 20, 21, 22, 23, 24, 25, 18], [1, 10, 0, 1, 11, 2, 3, 4, 13, 5, 14, 15, 16, 6, 7, 8, 9, 17, 18, 19, 20, 21, 22, 23, 24, 25, 17], [4, 0, 2], [0, 0, 1, 7, 3, 2, 4, 3], [0, 0, 7, 2, 4, 2], [0, 0, 5, 7, 3, 2, 6, 4, 2], [0, 0, 1, 5, 3, 2, 8, 6, 3], [0, 0, 1, 5, 3, 2, 6, 4, 3], [0, 0, 5, 3, 2, 2], [5, 0, 1, 2, 3, 2], [2, 1, 2, 1], [3, 0, 1, 2, 3, 2], [1, 0, 1, 2, 3, 12, 4, 5, 6, 7, 8, 9, 17, 18, 19, 20, 21, 22, 23, 24, 26, 25, 12], [6, 0, 1, 2, 3, 4]], [[[[6, "Fb_zhiyuan"], [7, "Fb_zhiyuan", 0, [-3, -4, -5, -6, -7, -8], [[13, true, -2, [24], 23]], [14, -1, 0], [383.524, 684.273, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lizi", [-9, -10, -11, -12], [0, "51lZNJaqRCCIgRJ3ZjgYJb", 1, 0], [-19.606, -6.684, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btn_title", 1, [-14, 2], [[1, -13, [18], 19]], [0, "63N7CZ32JKhrhdTjSJ+T5M", 1, 0], [5, 275, 147], [185.501, 48.816, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "New Sprite(Splash)", 100, 1, [[15, 0, -15, [0], 1]], [0, "feLVOEQQdFabcq7PjjsVHZ", 1, 0], [4, 4278190080], [5, 768, 1366]], [3, "bg_title_jinengdengchang_di", 1, [[1, -16, [2], 3]], [0, "e9IRCIsklHOqQyzB8ugedF", 1, 0], [5, 750, 767], [0, 75.77, 0, 0, 0, 0, 1, 1.027, 1.027, 1]], [11, "img_dgd", 70, 1, [[1, -17, [4], 5]], [0, "aaq1N8TmdDoZnXCs1BgRaZ", 1, 0], [5, 307, 364], [-217.001, -41.106, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [3, "bg_jineng", 1, [[1, -18, [6], 7]], [0, "5f+YsyA85Lu5X1ecOp3ij6", 1, 0], [5, 392, 422], [-188.363, -40.643, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_grade", 3, [[1, -19, [8], 9]], [0, "d8BGnvb8NAq7J/5UtS1Hr+", 1, 0], [5, 103, 88], [-174.501, -0.816, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_lock_on_lizi_4", 2, [[4, 1, true, 4, 300, 0.3, 0.03, 180, 40, 10, 40, 7, 120, 10, 1, 720, 70, 0, -20, [10], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 50, 0], [0, 0, -1800], 11]], [0, "b4Nt4u/AZHrILoZLGJ5H1y", 1, 0], [0, 30, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [2, "effect_lock_on_lizi_3", 2, [[5, 1, true, 4, 300, 0.3, 0.03, 30, 10, 40, 7, 120, 10, 1, 720, 70, 0, -21, [12], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 0, 50], [0, -1800, 0], 13]], [0, "51FfN2+AZC2LLJGC4eXIpE", 1, 0], [30, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [2, "effect_lock_on_lizi_2", 2, [[5, 1, true, 4, 300, 0.3, 0.03, 30, 10, 40, 7, 120, 10, 1, 720, 70, 0, -22, [14], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 0, 50], [0, 1800, 0], 15]], [0, "68NizGf9hO7olsK+X+NmNZ", 1, 0], [-30, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "effect_lock_on_lizi_1", 2, [[4, 1, true, 4, 300, 0.3, 0.03, 0, 40, 10, 40, 7, 120, 10, 1, 720, 70, 0, -23, [16], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 50, 0], [0, 0, 1800], 17]], [0, "65bOKQUo9LO4KKO78VNzw5", 1, 0], [0, -30, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [12, "lizi", 1, [[16, true, 20, 0.4, 0.1, 30, 0, 10, 1, 100, 0, 0, -24, [20], [4, 4294967295], [4, 184549376], [4, 3607101439], [4, 0], [0, 350, 100], [0, 0.25, 0.8600000143051147], 21, 22]], [0, "4bOUmjfJpD2ZWsVdX+V60m", 1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 3, 0, -6, 13, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 3, 1, 2, 4, 3, 24], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 5, 1, 6, -1], [0, 3, 0, 4, 0, 5, 0, 6, 0, 7, 0, 1, 0, 1, 0, 1, 0, 1, 0, 8, 0, 9, 1, 2, 2]], [[[17, "fb_zhiyuan", 3.125, 24, [{"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 3, "value": 255, "curve": "linear"}, {"frame": 3.125, "value": 0}]}}, "paths", 11, [{"New Sprite(Splash)": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.08333333333333333, "value": 100}]}}, "lizi": {"props": {"opacity": [{"frame": 0.3333333333333333, "value": 0, "curve": "constant"}, {"frame": 0.375, "value": 255}]}}, "bg_jineng": {"props": {"position": [{"frame": 0.4166666666666667, "value": [-591.923, -169.123, 0], "curve": [0.031963630806846006, 1.018934902200489, 0.4172620838818883, 1.3492203075042317]}, {"frame": 0.5416666666666666, "value": [-188.363, -40.643, 0]}]}}, "img_dgd": {"props": {"opacity": [{"frame": 0.375, "value": 0}, {"frame": 0.4166666666666667, "value": 70}]}}, "btn_title/lizi/effect_lock_on_lizi_4": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.75, "value": 0, "curve": "constant"}, {"frame": 0.7916666666666666, "value": 300, "curve": [0.05083129584352079, 1.1193887530562345, 0.58, 1]}, {"frame": 0.8333333333333334, "value": 0}]}}}, "btn_title/lizi/effect_lock_on_lizi_2": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.75, "value": 0, "curve": "constant"}, {"frame": 0.7916666666666666, "value": 300, "curve": [0.05083129584352079, 1.1193887530562345, 0.58, 1]}, {"frame": 0.8333333333333334, "value": 0}]}}}, "btn_title/lizi/effect_lock_on_lizi_3": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.75, "value": 0, "curve": "constant"}, {"frame": 0.7916666666666666, "value": 300, "curve": [0.05083129584352079, 1.1193887530562345, 0.58, 1]}, {"frame": 0.8333333333333334, "value": 0}]}}}, "btn_title/lizi/effect_lock_on_lizi_1": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.75, "value": 0, "curve": "constant"}, {"frame": 0.7916666666666666, "value": 300, "curve": [0.05083129584352079, 1.1193887530562345, 0.58, 1]}, {"frame": 0.8333333333333334, "value": 0}]}}}}, "bg_title_jinengdengchang_di", 11, [{}, "props", 11, [{"position": [{"frame": 0.20833333333333334, "value": [-780.764, -225.664, 0]}, {"frame": 0.2916666666666667, "value": [0, 75.77, 0]}]}, "scale", 12, [[[{"frame": 0.2916666666666667}, "value", 8, [1, 1.027, 1.027, 1]], [{"frame": 0.3333333333333333}, "value", 8, [1, 1.127, 1.027, 1]], [{"frame": 0.375}, "value", 8, [1, 1.027, 1.027, 1]]], 11, 11, 11]]], "btn_title", 11, [{}, "props", 11, [{"position": [{"frame": 0.8333333333333334, "value": [185.501, 48.816, 0]}], "opacity": [{"frame": 0.6666666666666666, "value": 0}, {"frame": 0.75, "value": 255}], "angle": [{"frame": 0.75, "value": 6.12, "curve": "linear"}, {"frame": 0.7916666666666666, "value": -10.21499999999999, "curve": "linear"}, {"frame": 0.8333333333333334, "value": 3}, {"frame": 0.875, "value": -2}, {"frame": 0.9166666666666666, "value": 0}]}, "scale", 12, [[[{"frame": 0.625, "curve": [0.18, 0.89, 0.31, 1.21]}, "value", 8, [1, 3, 3, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 1, 1]]], 11, 11]]]]]]], 0, 0, [], [], []]]]