[1, ["ecpdLyjvZBwrvm+cedCcQy", "d9dkwSdGNJFK8Jvz7lzizp", "785VA4V0hC/aoaiWTnV1Uh", "65fyJDMVBCG67McLTVt3p+", "99DIZNAIFPVZDK1TwC9mZI", "74H4WIlMlLz4ezg7eLXYcF", "39AUuleapP2rbFUvUtMCUo", "35PSCFXrZOV627Z82nekwy", "45NOIXfDJCkK2k6RBHtfWc", "3eYVH4cxFJa4m8NRbBmLLn"], ["node", "_spriteFrame", "_N$file", "root", "img_free", "img_redPoint", "rebateTxt", "scrollContent", "buyBtn", "limitTxt", "txt_saleOut", "giftName", "data", "_parent"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 1, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_isSystemFontUsed", "_enableWrapText", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_left", "_top", "_right", "_originalWidth", "_originalHeight", "node"], -3, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_children"], 2, 1, 12, 4, 5, 7, 5, 5, 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["3039cCGLWFIeqCPOGQnWhje", ["node", "buttonTitle"], 3, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["_enabled", "horizontal", "vertical", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -4, 1, 1], ["0e3fa1BCvpPS7OLdCuczs4L", ["node", "giftName", "txt_saleOut", "limitTxt", "buyBtn", "scrollContent", "rebateTxt", "img_redPoint", "img_free"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 1, 2, 2], [0, 0, 5, 7, 2, 3, 4, 6, 2], [0, 0, 5, 7, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 4, 5, 2, 3, 7, 8, 7], [9, 0, 1, 2, 2], [3, 1, 0, 2, 3, 4, 3], [3, 0, 2, 3, 4, 2], [3, 2, 3, 4, 1], [2, 0, 1, 2, 6, 4], [8, 0, 2], [0, 0, 7, 2, 3, 4, 2], [0, 0, 5, 2, 3, 8, 4, 6, 2], [0, 0, 5, 2, 3, 4, 6, 2], [0, 0, 1, 5, 2, 3, 4, 3], [0, 0, 2, 3, 4, 9, 6, 2], [0, 0, 1, 5, 2, 3, 4, 6, 3], [4, 0, 1, 2, 3, 6, 4, 7, 5, 2], [4, 0, 1, 8, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 6, 4, 5, 2], [1, 0, 1, 4, 6, 5, 2, 3, 7, 8, 8], [1, 0, 1, 4, 2, 3, 7, 8, 6], [1, 0, 1, 5, 2, 3, 7, 8, 9, 6], [1, 0, 1, 4, 5, 2, 3, 7, 8, 9, 7], [6, 1, 2, 1], [2, 0, 3, 6, 3], [2, 0, 6, 2], [2, 0, 4, 5, 6, 4], [7, 0, 1, 3, 4, 3], [7, 0, 1, 2, 3, 4, 4], [10, 0, 1, 1], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1]], [[10, "ZbgUiTeHuiItem"], [11, "zbgTeHuiItem", [-12, -13, -14, -15, -16, -17, -18], [[33, -10, -9, -8, -7, -6, -5, -4, -3, -2], [6, 1, 0, -11, [19], 20]], [24, -1, 0], [5, 674, 163]], [18, "buyBtn", 1, [-22, -23], [[[7, 0, -19, [14], 15], -20, [25, 32, 36.78199999999998, -21]], 4, 1, 4], [0, "9bnWA/xAJG44AlTO6Jeu9h", 1, 0], [5, 185, 66], [207.71800000000002, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "content", [[29, 1, 1, -12, -24, [5, 440, 120]], [26, 8, -25]], [0, "ecRVwp1iNPx6Ws/7BXfssD", 1, 0], [5, 440, 120], [0, 0, 0.5], [-217, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nameBg", 1, [-28], [[6, 1, 0, -26, [1], 2], [9, 9, -1.1129999999999995, -20, -27]], [0, "9bxQsvSt9Hapz4DYLiPmjS", 1, 0], [5, 295, 51], [-190.613, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "txt_des", 2, [-30, -31], [[28, 1, 1, -29, [5, 105, 50]]], [0, "a83ueKsORH8afp9EoBET8R", 1, 0], [5, 105, 50]], [1, "ScrollView", 1, [-34], [[32, false, false, false, 0.75, 0.23, null, null, -32, 3], [9, 8, 8.113000000000014, 32.5, -33]], [0, "d3hI8I251DipQ0213qO5Bq", 1, 0], [5, 434, 120], [-111.887, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "view", 6, [3], [[31, 0, -35, [16]], [27, 45, 300, 80, -36]], [0, "05IB+SowlM1KDO/EOJGqHB", 1, 0], [5, 434, 120]], [17, "txt_title", 4, [[-37, [5, 3, -38, [4, 4279120030]]], 1, 4], [0, "b0D9SdFz5AhIkE4Tj3wOiY", 1, 0], [4, 4291624954], [5, 233.29, 43.8], [0, 0, 0.5], [-138, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "txt_saleOut", 1, [[22, "已售罄", 30, false, 1, 1, -39, [4], 5]], [0, "9980UaZD9GDoUzzWLY9qbl", 1, 0], [4, 4279520398], [5, 90, 50.4], [214.226, -9.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_rebate", 1, [-41], [[7, 0, -40, [7], 8]], [0, "a9ipwbHlJP64uVw182OOHa", 1, 0], [5, 57, 63], [307.956, 73.231, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "unitTxt", 5, [[23, "元", 32, 0, false, 1, 1, -42, [10], 11], [5, 3, -43, [4, 4278203558]]], [0, "3bsUaDNBpCCpdzmYtkA7Zu", 1, 0], [5, 38, 46.32], [33.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "img_free", false, 2, [[8, -44, [12], 13]], [0, "22F+AKA+5JKqJhvYUcUfEn", 1, 0], [5, 75, 39]], [16, "img_redPoint", false, 1, [[8, -45, [17], 18]], [0, "f23Dhs0fJERICkz5+ekxtW", 1, 0], [5, 32, 32], [292.927, 13.538, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "每日礼包（6元）", 30, 30, false, false, 1, 1, 8, [0]], [19, "txt_limit", 1, [-46], [0, "82hU2pZlVCEaxO9ORBYdSa", 1, 0], [4, 4279520398], [5, 134.75, 37.8], [209.992, 42.472, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "永久限购1/1", 25, 30, 1, 1, 15, [3]], [3, "txt_rebate", 10, [-47], [0, "2c90iidNVHObindk+UGGVX", 1, 0], [5, 60.71, 25], [2.497, -8.114, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "500%", 25, 25, false, 1, 1, 17, [6]], [3, "priceTxt", 5, [-48], [0, "71sleLa9lMNrJIz+txHDBs", 1, 0], [5, 67, 35], [-19, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "648", 35, 35, false, 1, 1, 19, [9]], [30, 2, 20]], 0, [0, 3, 1, 0, 4, 12, 0, 5, 13, 0, 6, 18, 0, 7, 3, 0, 8, 21, 0, 9, 16, 0, 10, 9, 0, 11, 14, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 15, 0, -3, 9, 0, -4, 10, 0, -5, 2, 0, -6, 6, 0, -7, 13, 0, 0, 2, 0, -2, 21, 0, 0, 2, 0, -1, 5, 0, -2, 12, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, 0, 5, 0, -1, 19, 0, -2, 11, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, -1, 17, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, -1, 16, 0, -1, 18, 0, -1, 20, 0, 12, 1, 3, 13, 7, 48], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 18, 20], [-1, -1, 1, -1, -1, 2, -1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, -1, 1, -1, 1, 2, 2, 2], [0, 0, 2, 0, 0, 1, 0, 0, 3, 0, 0, 1, 0, 4, 0, 5, 0, 0, 6, 0, 7, 1, 8, 9]]