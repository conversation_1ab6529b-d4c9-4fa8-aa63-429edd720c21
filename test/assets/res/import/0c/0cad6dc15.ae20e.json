[1, ["ecpdLyjvZBwrvm+cedCcQy", "5caODJK6pNr4KwTFnR8huj", "dcfbuaShVJ7J0nXNYsMxlo", "72H/5wr4xM8Ym4UvOQhpTf", "0dQEnMlv9KDL8p2vH1AUPN", "38MzG8pLBBiogf58nKhVDz", "440RjU4JhEEJLGsmvEbM1v", "8cE3DoTIZBL6aQ1tIrFARt", "950smPavtJ+7se/j72nz1k", "5bXrOGItNETpp4rtEPDvyK", "a5odtZj6RNY7R+q5r89Cq3", "a0uj8YfTRE07oLzluTaIRM", "42nR4TEtlLvY8iS7+6QeeK"], ["node", "_spriteFrame", "_defaultClip", "root", "boxImg", "lightNode", "labPoints", "anim", "data", "normalBoxOpenImg", "normalBoxCloseImg", "bigBoxOpenImg", "bigBoxCloseImg", "_N$file"], [["cc.Node", ["_name", "_active", "_prefab", "_children", "_parent", "_components", "_contentSize", "_trs"], 1, 4, 2, 1, 12, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_children", "_trs"], 2, 1, 9, 4, 5, 2, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "wrapMode", "curveData"], -1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 2, 4, 5], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["44b20pm/UdHf6ba/aLiPluu", ["node", "anim", "labPoints", "lightNode", "boxImg", "normalBoxOpenImg", "normalBoxCloseImg", "bigBoxOpenImg", "bigBoxCloseImg"], 3, 1, 1, 1, 1, 1, 6, 6, 6, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "node", "_layoutSize"], -2, 1, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -4, 1, 3]], [[2, 0, 1, 2, 2], [2, 0, 1, 2], [3, 2, 3, 4, 1], [1, 0, 1, 5, 2, 3, 2], [1, 0, 1, 2, 3, 4, 6, 2], [1, 0, 1, 2, 3, 4, 2], [4, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 3], [5, 0, 1, 2, 3, 5], [6, 0, 2], [0, 0, 3, 5, 2, 6, 2], [0, 0, 4, 3, 2, 2], [0, 0, 4, 5, 2, 6, 2], [0, 0, 1, 4, 3, 2, 7, 3], [1, 0, 1, 5, 2, 3, 4, 6, 2], [7, 0, 1, 2, 3, 4, 2], [8, 0, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [2, 1, 2, 1], [4, 1, 2, 1], [3, 2, 3, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8]], [[[[8, "boxShake", 2.0166666666666666, 2, {"paths": {"box": {"props": {"angle": [{"frame": 0, "value": 0}, {"frame": 0.5, "value": 15}, {"frame": 1.5333333333333334, "value": -33.263999999999996}, {"frame": 2.0166666666666666, "value": 0}]}}}}]], 0, 0, [], [], []], [[[9, "DailyUiBox"], [10, "DailyUiBox", [-9, -10, -11, -12], [[[16, -2], -3, [17, -8, -7, -6, -5, -4, 17, 18, 19, 20]], 4, 1, 4], [18, -1, 0], [5, 80, 80]], [11, "dailytask_guangxuanzhuang", 1, [-14, -15], [1, "c69sm0vRdNcKLGNPfCcj6+", -13]], [3, "dailytask_guangxuanzhuang01", 2, [-17], [[6, true, -16, [3], 2]], [1, "56VqamJYBMLbVyq68dcVfI", 2]], [3, "dailytask_guangxuanzhuang02", 2, [-19], [[6, true, -18, [7], 6]], [1, "53016bO4xKpY8qlfMr4Q27", 2]], [14, "base", 1, [-21], [[7, 1, 0, -20, [9], 10]], [0, "27zhnKtBRKkpYxcNG8RfdA", 1, 0], [5, 60, 30], [0, -55.07, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lab<PERSON>um", 5, [[-22, [21, 2, -23, [4, 4278190080]]], 1, 4], [0, "31CjyoZ3VELIgkjYsGTy+0", 1, 0], [5, 38, 20]], [13, "boxTipsNode", false, 1, [-24, -25], [0, "0aAWZjAo1HjJCD0xnBpBXh", 1, 0], [0, -39.243, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "resParent", 7, [[22, 1, 1, 20, 20, 10, -26, [5, 328, 114]], [7, 1, 0, -27, [14], 15]], [0, "73cbybU2pFM4nI7lp73jHP", 1, 0], [5, 328, 114], [0, -67.83, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ui_texiaoguangxuanzhuang", 3, [[2, -28, [0], 1]], [1, "d6EwwT8KNHBar0gykWiedm", 2], [5, 98, 98]], [5, "ui_texiaoguangxuanzhuang", 4, [[2, -29, [4], 5]], [1, "2eMeT/+8JJLrh2xD4ggnRp", 2], [5, 98, 98]], [23, "25", 25, 20, false, -12, 1, 1, 6, [8]], [15, "boxImg", 1, [-30], [0, "42lU9+TcBN97QYOjKX7MY3", 1, 0], [5, 73, 55]], [20, 12, [11]], [4, "flag", 7, [[2, -31, [12], 13]], [0, "20SbW0G7NES5kcvb1mLH1f", 1, 0], [5, 31, 23], [0, 0.136, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 1, [16]]], 0, [0, 3, 1, 0, 0, 1, 0, -2, 15, 0, 4, 13, 0, 5, 2, 0, 6, 11, 0, 7, 15, 0, 0, 1, 0, -1, 2, 0, -2, 5, 0, -3, 12, 0, -4, 7, 0, 3, 2, 0, -1, 3, 0, -2, 4, 0, 0, 3, 0, -1, 9, 0, 0, 4, 0, -1, 10, 0, 0, 5, 0, -1, 6, 0, -1, 11, 0, 0, 6, 0, -1, 14, 0, -2, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, -1, 13, 0, 0, 14, 0, 8, 1, 31], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 13, 15], [-1, 1, 2, -1, -1, 1, 2, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 9, 10, 11, 12, 13, 1, 2], [0, 1, 2, 2, 0, 1, 3, 3, 0, 0, 6, 0, 0, 7, 0, 8, 4, 9, 5, 10, 11, 12, 5, 4]]]]