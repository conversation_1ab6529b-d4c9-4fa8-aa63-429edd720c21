[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7wPg4TdpBur7KbRzepxqd", "51dhIPaxNNcbQNO9gyY9cE", "c514NCSiBN+Lfzx3zJ7Oa5", "42nR4TEtlLvY8iS7+6QeeK"], ["node", "_spriteFrame", "_N$file", "root", "assetIcon", "btnAdd", "lab<PERSON>um", "bg", "data"], [["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 9, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 2, 4, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_spacingX", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials"], -6, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["5453efBS+ZHxKjOMSq82MlK", ["node", "bg", "lab<PERSON>um", "btnAdd", "assetIcon"], 3, 1, 1, 1, 1, 1]], [[1, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 2], [2, 0, 2], [3, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 2], [0, 0, 2, 3, 2], [0, 1, 2, 3, 4, 2], [0, 2, 3, 4, 1], [1, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 10], [7, 0, 1, 2, 2], [8, 0, 1], [9, 0, 1], [10, 0, 1, 2, 3, 4, 1]], [[2, "AssetComp"], [3, "AssetComp", [-7, -8, -9, -10], [[13, -6, -5, -4, -3, -2]], [8, -1, 0], [5, 190, 40], [594, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lab<PERSON>um", 1, [[-11, [10, 3, -12, [4, 4281341443]]], 1, 4], [0, "a8rI/5c2ZAGqNEQZZPFn8B", 1, 0], [5, 95, 30], [8.527, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "assetIcon", 1, [[[6, 0, -13, [2], 3], -14], 4, 1], [0, "59Deso/ulPHqlrENlEQhBS", 1, 0], [5, 45, 45], [-59, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnAdd", 1, [[[7, -15, [4], 5], -16], 4, 1], [0, "a0VRCnRD1D56srA7ASnz4X", 1, 0], [5, 24, 23], [-43, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg", 1, [-17], [0, "f0tqlMzMNP/IVmuvRbuAnW", 1, 0], [5, 125, 40]], [5, 1, 5, [0]], [9, "1234569", 25, 30, false, false, -12, 1, 1, 2, 2, [1]], [11, 3], [12, 4]], 0, [0, 3, 1, 0, 4, 8, 0, 5, 9, 0, 6, 7, 0, 7, 6, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, -3, 3, 0, -4, 4, 0, -1, 7, 0, 0, 2, 0, 0, 3, 0, -2, 8, 0, 0, 4, 0, -2, 9, 0, -1, 6, 0, 8, 1, 17], [0, 0, 0, 0, 0, 0, 6, 7], [-1, -1, -1, 1, -1, 1, 1, 2], [0, 0, 0, 1, 0, 2, 3, 4]]