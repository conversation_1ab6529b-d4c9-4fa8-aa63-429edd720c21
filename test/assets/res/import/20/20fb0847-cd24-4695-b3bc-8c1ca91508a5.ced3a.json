[1, ["db0NA2895NJaaWY/no2Vkm", "c9LA2DAE5Mqq6XMOzZCX+W", "ab448NhHhLjbluTubUZboc", "b5fw38iPNFibl4MPwj+tIm", "e33yb0DtpDSJp/JAlVhg12", "30O9Xe0FNF0r3e4o1xWH2C", "a7lN5DW6lH3KmDXbfW2s7/", "df7aNDmkxLbrqTTIwXuP+L", "baz3BeIWhEr6gauJAs6fTf", "e2+1O8YBNEIrAw+AaL3w+s", "aeEdTCRhhDRb/OSip0epBw", "bbaFYCVSRE4LTvjhyca1iB", "17trFofOpIvby98Ndfi5a0", "9dlrhEpudCDLGM1AhFMPGS", "f4/UFlhLBGbr4mAqxufwUn", "b9zLoFFZRGk5BtxtGGNYRa", "f7R51bD6pPpoGB6QSwh8LZ", "f8GruVDqpMy4yEwUV6lE/0", "67Pw8SWx9DM6tq+xZSuLZi", "51crdv3CVGwZVKE+yNh1Tu"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], 0, 11]], [[0, 0, 1, 2, 3, 4]], [[0, "effect_nz_02", 1.1666666666666667, 24, [{}, "paths", 11, [{"02": {"props": {"opacity": [{"frame": 0, "value": 255}]}}}, "02/dici01/7000505_dh_01", 11, [{"props": {"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 255}, {"frame": 0.4583333333333333, "value": 255}, {"frame": 0.5416666666666666, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.041666666666666664}, "value", 6, 0], [{"frame": 0.08333333333333333}, "value", 6, 1], [{"frame": 0.125}, "value", 6, 2], [{"frame": 0.16666666666666666}, "value", 6, 3], [{"frame": 0.20833333333333334}, "value", 6, 4], [{"frame": 0.25}, "value", 6, 5], [{"frame": 0.2916666666666667}, "value", 6, 6], [{"frame": 0.3333333333333333}, "value", 6, 7], [{"frame": 0.375}, "value", 6, 8], [{"frame": 0.4166666666666667}, "value", 6, 9], [{"frame": 0.4583333333333333}, "value", 6, 10], [{"frame": 0.5}, "value", 6, 11]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "02/dici01/nz_dici_001", 11, [{"props": {"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 255}, {"frame": 1.0833333333333333, "value": 255}, {"frame": 1.1666666666666667, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.041666666666666664}, "value", 6, 12], [{"frame": 0.08333333333333333}, "value", 6, 13], [{"frame": 0.125}, "value", 6, 14], [{"frame": 1}, "value", 6, 15], [{"frame": 1.0416666666666667}, "value", 6, 16], [{"frame": 1.0833333333333333}, "value", 6, 17]], 11, 11, 11, 11, 11, 11]]]], "02/dici01/7000505_xh_1", 11, [{"props": {"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 255}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.20833333333333334, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.041666666666666664}, "value", 6, 18], [{"frame": 0.08333333333333333}, "value", 6, 19], [{"frame": 0.125}, "value", 6, 20], [{"frame": 0.16666666666666666}, "value", 6, 21]], 11, 11, 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 1, 14, 1, 0, 15, 16, 17, 18, 19]]