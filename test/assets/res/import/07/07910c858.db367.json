[1, ["ecpdLyjvZBwrvm+cedCcQy", "6feltxbPFC5anTi4sayok4", "b2aHrECZ5APKGS/0d2hvT1", "62h544GKVCsI5sXNgGIcWg", "e7RgqEoxJIb5nwNeiPwt9c", "a5hJI5OtNB0Yq0rp/qEfl/", "a17y/JnFdBio9pa+2aeg5/"], ["node", "_spriteFrame", "root", "data", "_parent", "_file", "_mesh", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_is3DNode", "_prefab", "_components", "_children", "_trs", "_parent", "_eulerAngles", "_color", "_contentSize"], 0, 4, 9, 2, 7, 1, 5, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["node", "_materials", "_mesh"], 3, 1, 3, 6], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angleVar", "startSize", "startSizeVar", "endSize", "endSizeVar", "startSpin", "endSpin", "endSpinVar", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccel", "radialAccelVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_file", "_spriteFrame"], -16, 1, 3, 8, 8, 8, 8, 5, 6, 6], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "curveData"], -1, 11]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 5, 4, 3, 2], [0, 0, 2, 5, 4, 3, 6, 8, 3], [0, 0, 1, 7, 5, 3, 6, 3], [0, 0, 7, 4, 3, 2], [0, 0, 1, 7, 4, 3, 9, 10, 6, 8, 3], [3, 0, 1, 2, 3, 2], [1, 1, 2, 1], [4, 0, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 20], [6, 0, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5]], [[[[1, "effect_ssbq_01_back"], [2, "effect_ssbq_01_back", [-3], [[7, true, -2, [8], 7]], [8, -1, 0]], [3, "Plane", true, [-5, -6], [[9, -4, [5], 6]], [0, "016zax/v1FKJQ13Bb06WO0", 1, 0], [0, 0, 0, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [4, "buff_ssbq_01_back", 0, 1, [2], [0, "50aXxwgJtBQJfwSHcEIhi1", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "effect_yj_smoke", 2, [[10, true, 20, 0, 0.5, 0.2, 65, 150, 20, 80, 10, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 640, 20, -62.1100006, -970, 100, -7, [0], [4, 855375615], [4, 167772160], [4, 16777215], [4, 167772160], [0, 10, 10], 1, 2]], [0, "f5FAxPN71NkojXDtKK/pF9", 1, 0]], [6, "Fx_Circlering_02", 150, 2, [[11, -8, [3], 4]], [0, "4eD/s7uGBEramo9njqMSOp", 1, 0], [4, 4292927712], [5, 102, 102], [0, 0, 0, 0, 0, 0, 1, 0, 0, 1], [1, 50, 0, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, 0, 4, 0, 0, 5, 0, 3, 1, 2, 4, 3, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 5, 1, -1, 1, -1, 6, 7, -1], [0, 2, 3, 0, 4, 5, 6, 1, 1]], [[[12, "effect_ssbq_01_back", 1.6666666666666667, 24, 0.5, [{}, "paths", 11, [{"buff_ssbq_01_back/Plane/effect_yj_smoke": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.041666666666666664, "value": 0, "curve": "constant"}, {"frame": 0.08333333333333333, "value": 9999}, {"frame": 0.125, "value": 0}]}}, "props": {"opacity": [{"frame": 0.125, "value": 0, "curve": "constant"}, {"frame": 0.16666666666666666, "value": 255}]}}, "buff_ssbq_01_back": {"props": {"opacity": [{"frame": 0, "value": 255, "curve": "constant"}, {"frame": 1.6666666666666667, "value": 0}]}}}, "buff_ssbq_01_back/Plane/Fx_Circlering_02", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 150}]}, "scale", 12, [[[{"frame": 0.041666666666666664}, "value", 8, [1, 4, 4, 1]], [{"frame": 0.125}, "value", 8, [1, 0, 0, 1]]], 11, 11]]]]]]], 0, 0, [], [], []]]]