[1, ["ecpdLyjvZBwrvm+cedCcQy", "89p5W7CZ5FPIK7HNsB5mun", "9fPQbgY/tKHYtG2QgekARP", "437s0ICrdABoVN6ppfV+JQ", "b3B9tKBm1MGJUXbW6FMDrm"], ["node", "_spriteFrame", "root", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_children", "_contentSize", "_trs", "_color", "_eulerAngles"], 1, 4, 9, 1, 2, 5, 7, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_dstBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Mask", ["_type", "_N$alphaThreshold", "node", "_materials"], 1, 1, 3]], [[1, 0, 1, 2, 2], [0, 0, 4, 3, 2, 6, 7, 2], [2, 1, 2, 3, 1], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5], [4, 0, 2], [0, 0, 5, 3, 2, 2], [0, 0, 1, 4, 5, 2, 3], [0, 0, 4, 5, 3, 2, 6, 7, 2], [0, 0, 1, 4, 5, 3, 2, 8, 6, 7, 9, 3], [0, 0, 1, 4, 3, 2, 8, 6, 3], [5, 0, 1, 2, 3, 2], [1, 1, 2, 1], [6, 0, 1, 2, 3, 3]], [[[[4, "buff_wudi", 1.0416666666666667, 24, 2, [{}, "paths", 11, [{"New Node": {"props": {"opacity": [{"frame": 0, "value": 255}]}}, "New Node/mask2/dg03": {"props": {"position": [{"frame": 0.3333333333333333, "value": [-79.158, 72.987, 0]}, {"frame": 0.9166666666666666, "value": [84.473, -69.256, 0]}]}}}, "New Node/wudizhao_01", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.5}, "value", 8, [1, 1.05, 1.05, 1]], [{"frame": 1.0416666666666667}, "value", 8, [1, 1, 1, 1]]], 11, 11, 11]]], "New Node/mask2", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.5}, "value", 8, [1, 1.05, 1.05, 1]], [{"frame": 1.0416666666666667}, "value", 8, [1, 1, 1, 1]]], 11, 11, 11]]], "New Node/wudizhao_02", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.4583333333333333, "value": 0}], "position": [{"frame": 0.08333333333333333, "curve": "linear", "value": [-23.225, 35, 0]}, {"frame": 0.16666666666666666, "value": [-37.582, 35, 0]}, {"frame": 0.5, "value": [-41.035, 35, 0]}]}, "scale", 12, [[[{"frame": 0.08333333333333333, "curve": "linear"}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1.079, 1.079, 1.079]], [{"frame": 0.5}, "value", 8, [1, 1.1, 1.1, 1.079]]], 11, 11, 11]]], "New Node/wudizhao_02r", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.4583333333333333, "value": 0}], "position": [{"frame": 0.08333333333333333, "value": [23.225, 35, 0]}, {"frame": 0.16666666666666666, "value": [37.582, 35, 0]}, {"frame": 0.5, "value": [41.035, 35, 0]}]}, "scale", 12, [[[{"frame": 0.08333333333333333, "curve": "linear"}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, -1.079, 1.079, 1.079]], [{"frame": 0.5}, "value", 8, [1, -1.1, 1.1, 1.079]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[5, "buff_wudi"], [6, "buff_wudi", [-3], [[11, true, -2, [16], 15]], [12, -1, 0]], [7, "New Node", 0, 1, [-4, -5, -6, -7], [0, "619K8N+wpA4rl6Nj3DRw5u", 1, 0]], [8, "mask2", 2, [-9, -10, -11], [[13, 2, 0.3, -8, [10]]], [0, "b7LrbnFvpEYooxQ5GwO1IO", 1, 0], [5, 88, 126], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "dg03", 150, 3, [-13], [[2, -12, [8], 9]], [0, "80uAp9JxpPLZ1HGB5PmBXj", 1, 0], [4, 4281325055], [5, 132, 128], [4.996, -0.167, 0, 0, 0, -0.35020738125946743, 0.9366721892483976, -0.725, 1, 1], [1, 0, 0, -41]], [1, "wudizhao_01", 2, [[2, -14, [0], 1]], [0, "82i6avOXNGKpcOTXUH+IwS", 1, 0], [5, 106, 144], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dg01", 3, [[3, 1, -15, [2], 3]], [0, "25A2AInaRNVpx5SUpizXSp", 1, 0], [5, 132, 128], [82, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dg02", 3, [[3, 1, -16, [4], 5]], [0, "4fQi9UE4JLUZFfZkTvs6vo", 1, 0], [5, 132, 128], [-82, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [10, "dg03", 100, 4, [[3, 1, -17, [6], 7]], [0, "b2K88yT1pB1oYTm+ZXYKZR", 1, 0], [4, 4290769663], [5, 132, 128]], [1, "wudizhao_02", 2, [[2, -18, [11], 12]], [0, "07pjAAUOJKt6mvxNRnGlhp", 1, 0], [5, 44, 126], [-32.515, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wudizhao_02r", 2, [[2, -19, [13], 14]], [0, "376gEdcM1G/JkzLwm03LD5", 1, 0], [5, 44, 126], [32.515, 35, 0, 0, 0, 0, 1, -1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 2, 0, -1, 5, 0, -2, 3, 0, -3, 9, 0, -4, 10, 0, 0, 3, 0, -1, 6, 0, -2, 7, 0, -3, 4, 0, 0, 4, 0, -1, 8, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 3, 1, 19], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, 4, -1], [0, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 2, 0, 2, 3, 3]]]]