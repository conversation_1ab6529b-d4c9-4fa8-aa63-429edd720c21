[1, ["ecpdLyjvZBwrvm+cedCcQy", "4dp3nSWhxLNrRXd01PLyCC", "dfDRHzFAFNSI36eihs6ZFP", "42nR4TEtlLvY8iS7+6QeeK", "8855/VlrRKd6H0MSRnFxAU", "983dbR6F5JLbzpqCo+MMp3", "2ftr38PAdHf6X2Ck0lr//w", "564PdhpXdLx6ZNupdLuhZG", "e5QkxxggdOD6CzFA3yzbfg", "0fHbNgXntP1aSoRpo9YtLq"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$barSprite", "data", "_N$file"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint"], 2, 9, 4, 5, 7, 1, 2, 5], ["cc.Sprite", ["_type", "_sizeMode", "_enabled", "_fillType", "_fillRange", "node", "_materials", "_spriteFrame", "_fillCenter"], -2, 1, 3, 6, 5], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_originalWidth", "_left", "_bottom", "node"], -1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_spacingX", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_eulerAngles"], 2, 1, 2, 4, 5, 7, 5], ["79040rRVQdAV40/6AC59yEI", ["node"], 3, 1], ["cc.ProgressBar", ["_N$mode", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[4, 0, 1, 2, 2], [0, 0, 5, 1, 2, 3, 4, 2], [6, 0, 2], [0, 0, 6, 1, 2, 3, 4, 2], [0, 0, 5, 6, 1, 2, 3, 7, 4, 2], [0, 0, 5, 6, 1, 2, 3, 4, 2], [0, 0, 5, 1, 2, 3, 7, 4, 2], [0, 0, 5, 1, 2, 3, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [3, 0, 1, 4, 3], [3, 0, 4, 2], [3, 0, 2, 3, 4, 4], [8, 0, 1], [4, 1, 2, 1], [1, 2, 0, 1, 5, 6, 7, 4], [1, 1, 5, 6, 7, 2], [1, 0, 1, 5, 6, 7, 3], [1, 5, 6, 7, 1], [1, 0, 3, 4, 5, 6, 8, 4], [9, 0, 1, 2, 3, 3], [10, 0, 1], [5, 0, 1, 2, 3, 6, 7, 5], [5, 0, 1, 4, 5, 2, 3, 6, 7, 8, 7], [11, 0, 1, 2, 2]], [[[[2, "tower<PERSON><PERSON><PERSON>"], [3, "node", [-4, -5], [[9, 45, 768, -2], [12, -3]], [13, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "conTop", 1, [-7, -8, -9, -10, -11], [[10, 1, -6]], [0, "93ktyViphODJtL0vJA16kc", 1, 0], [5, 400, 200], [0, 0.5, 1], [0, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "timePb", 2, [-15], [[14, false, 1, 0, -12, [8], 9], [19, 2, 0.9, -14, -13]], [0, "b65NL+5n9D+bghpMo5X3hm", 1, 0], [5, 104, 104], [-3.379, -143.607, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnExit", 1, [[15, 0, -16, [10], 11], [20, -17], [11, 12, 41, 41, -18]], [0, "a5sVo7Z4pCmI8BE8yl4Bi3", 1, 0], [5, 104, 104], [-291, -590, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lblLayer", 2, [[21, "第59层", 30, 1, 1, -19, [2]], [23, 2, -20, [4, 4278190080]]], [0, "4a1oD45pJO6ao/ECOgC68D", 1, 0], [5, 97.37, 54.4], [0, 0.5, 1], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "layerBg", 2, [[16, 1, 0, -21, [0], 1]], [0, "94rvVzhKVM0IYyTYGPAVSI", 1, 0], [5, 232, 45], [0, 0.5, 1]], [1, "imgCdBg", 2, [[17, -22, [3], 4]], [0, "e6lriMyptOYKXRzqv3/D+Y", 1, 0], [5, 112, 112], [-3, -143, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lblCdTime", 2, [[22, "99", 50, false, -20, 1, 1, -23, [5], 6]], [0, "d4bqKJBgFP5JWd84uBMosa", 1, 0], [5, 80, 40], [-3, -144.168, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bar", 3, [-24], [0, "a9JLGb4/BDPb9w59Pbg6sU", 1, 0], [5, 104, 104], [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [18, 3, 2, 0.9, 9, [7], [0, 0.5, 0.5]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 4, 0, 0, 2, 0, -1, 6, 0, -2, 5, 0, -3, 7, 0, -4, 8, 0, -5, 3, 0, 0, 3, 0, 4, 10, 0, 0, 3, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, -1, 10, 0, 5, 1, 24], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10], [-1, 1, -1, -1, 1, -1, 6, -1, -1, 1, -1, 1, 1], [0, 1, 0, 0, 2, 0, 3, 0, 0, 4, 0, 5, 6]], [[{"name": "ui_title_tontianta_xuanzhua<PERSON>o", "rect": [0, 0, 104, 104], "offset": [0, 0], "originalSize": [104, 104], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [7]], [[{"name": "ui_fuben<PERSON><PERSON><PERSON>_di", "rect": [0, 0, 92, 45], "offset": [0, 0], "originalSize": [92, 45], "capInsets": [42, 0, 42, 0]}], [2], 0, [0], [2], [8]], [[{"name": "ui_title_tontianta_xuanzhuangtiao_di", "rect": [0, 0, 112, 112], "offset": [0, 0], "originalSize": [112, 112], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [9]]]]