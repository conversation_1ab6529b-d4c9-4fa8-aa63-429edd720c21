[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "b8pxy/xk9H2Ikj4BcZ6i8C", "33aI8zgRdPbYJsdSwscQwD", "2aaLNJ79lAtqQfs0MctdOu", "bfah7f9NpIPqg0uv9zh4uV"], ["node", "_spriteFrame", "_file", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_eulerAngles", "_color"], 0, 4, 9, 1, 5, 7, 2, 5, 5], ["cc.Sprite", ["_dstBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "startSpin", "endSpin", "endSpinVar", "speed", "speedVar", "tangentialAccel", "tangentialAccelVar", "radialAccel", "radialAccelVar", "_dstBlendFactor", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -17, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.RigidBody", ["_allowSleep", "_gravityScale", "enabledContactListener", "bullet", "node"], -1, 1], ["cc.PhysicsCircleCollider", ["_density", "_sensor", "_radius", "node"], 0, 1]], [[2, 0, 1, 2, 2], [0, 0, 1, 5, 4, 3, 3], [1, 1, 2, 3, 1], [4, 0, 2], [0, 0, 1, 8, 4, 3, 3], [0, 0, 1, 5, 8, 3, 6, 7, 3], [0, 0, 2, 1, 5, 4, 3, 10, 6, 7, 9, 4], [0, 0, 1, 5, 4, 3, 6, 3], [0, 0, 1, 5, 4, 3, 6, 7, 9, 3], [1, 0, 1, 2, 3, 2], [2, 1, 2, 1], [3, 19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 21], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 20], [5, 0, 1, 2, 3, 4, 5], [6, 0, 1, 2, 3, 4]], [[3, "bullet_7000516_00"], [4, "bullet_7000516_00", 4, [-4], [[13, false, 0, true, true, -2], [14, 0, true, 24, -3]], [10, -1, 0]], [5, "effect_xh_02_bullet01", 4, 1, [-5, -6, -7, -8, -9], [0, "a1Tg/9wBtPBaHqHhceopn8", 1, 0], [5, 103, 103], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 0.487]], [6, "guang<PERSON>", 199.92000000000002, 4, 2, [[9, 1, -10, [0], 1]], [0, "9apqt2cwZK/pMNR108Nd2Y", 1, 0], [4, 4278203391], [5, 256, 256], [-10.492, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 0.7, 1, 1], [1, 0, 0, -90]], [1, "effect_xh_02_lizi", 4, 2, [[11, 1, true, 12, 999.999985098839, 0.20000000298023224, 0.1, 360, 360, 3.369999885559082, 5, 5.3199997, -47.369998931884766, -47.369998931884766, -142.11000061035156, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -11, [2], [4, 2734702079], [4, 3321888768], [4, 838876671], [4, 167772160], [0, 10, 10], [0, 0.25, 0.8600000143051147], 3, 4]], [0, "70HY4wcxpOq64F9orO4bWq", 1, 0]], [1, "effect_xh_02_lizi_a", 4, 2, [[12, true, 12, 999.999985098839, 0.20000000298023224, 0.1, 360, 360, 3.369999885559082, 5, 5.3199997, -47.369998931884766, -47.369998931884766, -142.11000061035156, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -12, [5], [4, 2738259711], [4, 3321888768], [4, 842368767], [4, 167772160], [0, 10, 10], [0, 0.25, 0.8600000143051147], 6, 7]], [0, "7a3ckj5L9JgoaNnFUoSdWX", 1, 0]], [7, "effect_xh_02_qiu", 4, 2, [[2, -13, [8], 9]], [0, "57+HTKu29L4oTNcMS3R1C+", 1, 0], [5, 103, 103]], [8, "effect_xh_qizao", 4, 2, [[2, -14, [10], 11]], [0, "373LAZfhBCd4+k/TBGLYWP", 1, 0], [5, 233, 296], [0, 0, 0, 0, 0, 1, 6.123233995736766e-17, 0.7, 0.5, 1], [1, 0, 0, 180]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 4, 1, 14], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1], [0, 3, 0, 1, 2, 0, 1, 2, 0, 4, 0, 5]]