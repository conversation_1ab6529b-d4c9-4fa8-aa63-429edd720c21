[1, ["ecpdLyjvZBwrvm+cedCcQy", "62m3Q1gh1MJoQLW78yorn0", "acqG36EK5NX4IHhkNiXlp6", "440RjU4JhEEJLGsmvEbM1v", "42nR4TEtlLvY8iS7+6QeeK", "b0USooZzhESoUJwJSO03Ci", "180VcatoxCE792VW/v20zH", "a6XQlubZdDubHS4WYNI5ob", "f4GKy6AfJKHbL6tdNcWrwo", "9fGbeUle9LXJlZVQWXuFKb", "a2MjXRFdtLlYQ5ouAFv/+R", "2fy/GAtVBOEZodVNr2KsXa", "24oQSaj41PMpcrCiExnOfQ", "55Uv8jlGhOHoZG8YWkCplm", "15FVfWn2RKH4SGe6bMMOwd", "a6h5Low0JLQ6ljAnCJr3Xc", "3ae7efMv1CLq2ilvUY/tQi", "6bOhGXFb1KuqGyaZWEg/rT", "cdxym2XxZPwL6P4o9wyr+N", "8e7NTulNJH268OG6iiWPki", "29MDRZ90JGUL/GFQXEKEer", "6cWIXyUw1KLLsW4sPliBHO", "983dbR6F5JLbzpqCo+MMp3", "99t84/zCNG3YsnxdfsAgKZ", "ea3feP7VNB4bFuBeBR6iZz", "3ce6gZsh9CWL/qp+0cQd6b", "abwuvOXkBICLC8amiY9x3i", "b5k+Od3uJJsIU0YOJirVBM", "c7X1Gi4XJJVpwsxE2zNfoO", "1fcuo4xxRPLYT1ArlPCPx0", "2bPUlnZSRA4o7nyXmd6QET", "d9dkwSdGNJFK8Jvz7lzizp", "88BNCFXIlB3aHy5AtjMGTo", "24KxG4VVdAurGRQUUGTrb8"], ["node", "_spriteFrame", "_N$file", "root", "lab<PERSON>um", "icon", "_textureSetter", "btn_return", "anim<PERSON><PERSON>", "itemTipCostIcon", "itemTipCost", "itemTipDesc", "itemTipName", "itemTipNode", "expertTalentParent", "generalTalent<PERSON>arent", "lineParent", "levelParent", "nextUnlockFlagHigh", "nextUnlockFlag", "scrollView", "data", "_parent", "levelPrefab", "linePrefab", "generalTalentPrefab", "expertTalentPrefab", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], 0, 4, 1, 9, 5, 7, 2, 5, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_originalHeight", "_left", "_originalWidth", "_right", "node"], -4, 1], ["cc.Sprite", ["_sizeMode", "_type", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_spacingX", "_N$overflow", "node", "_materials"], -5, 1, 3], ["cc.ParticleSystem", ["_custom", "_stopped", "totalParticles", "emissionRate", "life", "lifeVar", "angleVar", "startSizeVar", "endSizeVar", "startSpin", "endSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "_dstBlendFactor", "angle", "endSize", "startSize", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_spriteFrame"], -16, 1, 3, 8, 8, 8, 8, 5, 5, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 12, 4, 5, 7, 2], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 2, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "_enabled", "node", "clickEvents"], 0, 1, 9], "cc.SpriteFrame", ["cc.Prefab", ["_name", "optimizationPolicy"], 1], ["e79ce9QRrdK+rN6PzJZ3KhV", ["lineWidth", "node", "scrollView", "nextUnlockFlag", "nextUnlockFlagHigh", "levelParent", "lineParent", "generalTalent<PERSON>arent", "expertTalentParent", "itemTipNode", "itemTipName", "itemTipDesc", "itemTipCost", "itemTipCostIcon", "itemTipBtnArr", "anim<PERSON><PERSON>", "btn_return", "levelPrefab", "linePrefab", "generalTalentPrefab", "expertTalentPrefab"], 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 6, 6, 6, 6], ["651f0h98wRMFpVJtiJ5MDTm", ["resType", "node", "icon", "lab<PERSON>um"], 2, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingTop", "_N$paddingBottom", "_N$spacingX", "_N$spacingY", "_N$verticalDirection", "node", "_layoutSize"], -4, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["<PERSON><PERSON>", ["horizontal", "bounceDuration", "node", "_N$content"], 1, 1, 1], ["cc.RichText", ["_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node"], 0, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Animation", ["node", "_clips"], 3, 1, 3], ["cc.AnimationClip", ["_name", "_duration", "speed", "curveData"], 0, 11]], [[7, 0, 1, 2, 2], [2, 3, 4, 5, 1], [7, 0, 1, 2], [2, 1, 0, 3, 4, 5, 3], [0, 0, 4, 5, 3, 6, 7, 2], [0, 0, 4, 5, 3, 7, 11, 2], [0, 0, 4, 5, 3, 6, 2], [5, 0, 2, 3, 4, 5, 6, 2], [0, 0, 4, 8, 5, 3, 6, 7, 2], [2, 0, 3, 4, 5, 2], [17, 0, 1, 2, 2], [0, 0, 2, 4, 5, 3, 6, 7, 3], [1, 0, 7, 2], [1, 0, 4, 6, 1, 2, 5, 3, 7, 8], [1, 0, 5, 3, 7, 4], [18, 0, 1], [0, 0, 4, 8, 3, 7, 2], [0, 0, 2, 4, 3, 6, 9, 7, 3], [6, 0, 2, 7, 3, 4, 5, 6, 2], [1, 0, 1, 7, 3], [1, 0, 1, 2, 3, 7, 5], [1, 0, 6, 2, 3, 7, 5], [12, 0, 1, 2, 3, 2], [13, 0, 1], [14, 0, 1, 1], [15, 0, 1], [3, 0, 1, 2, 5, 6, 3, 4, 7, 8, 9, 9], [4, 15, 0, 1, 2, 3, 4, 5, 16, 6, 7, 17, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24, 25, 26, 27, 19], [4, 15, 0, 1, 2, 3, 4, 5, 6, 7, 17, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24, 25, 26, 27, 18], [4, 0, 1, 2, 3, 4, 5, 16, 6, 18, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24, 25, 26, 27, 18], [4, 0, 1, 2, 3, 4, 5, 6, 18, 7, 8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24, 25, 26, 27, 17], [10, 0, 1, 3], [0, 0, 8, 5, 3, 6, 7, 2], [0, 0, 4, 8, 5, 3, 6, 2], [0, 0, 8, 3, 2], [0, 0, 4, 5, 3, 6, 9, 7, 2], [0, 0, 1, 4, 5, 3, 10, 6, 3], [0, 0, 1, 4, 5, 3, 10, 6, 7, 3], [0, 0, 4, 3, 7, 2], [0, 0, 2, 4, 5, 3, 6, 3], [0, 0, 2, 1, 4, 5, 3, 6, 4], [0, 0, 1, 4, 5, 3, 6, 7, 3], [5, 0, 2, 7, 3, 4, 5, 6, 2], [5, 0, 1, 2, 7, 3, 4, 5, 6, 3], [6, 0, 1, 2, 7, 3, 4, 5, 6, 3], [6, 0, 2, 3, 4, 5, 6, 2], [1, 0, 4, 2, 7, 4], [1, 0, 4, 7, 3], [1, 0, 4, 1, 7, 4], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 2], [7, 1, 2, 1], [16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [2, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 8, 9, 6], [3, 0, 1, 2, 5, 3, 4, 8, 9, 7], [3, 0, 1, 2, 5, 6, 3, 4, 8, 9, 8], [19, 0, 1, 2, 3, 3], [20, 0, 1, 2, 3, 4], [8, 0, 1, 3, 3], [8, 2, 0, 1, 3, 4, 4], [8, 3, 1], [21, 0, 1, 2, 3], [22, 0, 1, 1], [23, 0, 1, 2, 3, 4]], [[[[31, "trainingView", 1], [32, "trainingView", [-21, -22, -23], [[12, 45, -2], [49, 25, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, [-5, -6, -7], -4, -3, 78, 79, 80, 81]], [50, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "content", 1, [-25, -26, -27, -28, -29, -30, -31, -32, -33, -34], [[12, 45, -24]], [0, "c9VWgF+ntJU6J8hUXolHtP", 1, 0], [5, 768, 1366]], [8, "ResWidget", 2, [-40, -41, -42, -43], [[22, 6, -38, -37, -36], [19, 1, 227.40499999999997, -39]], [2, "98HFRgkoNEALPZiWmlUKVz", -35], [5, 173, 32], [56.067, 439.595, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "ResWidget", 2, [-49, -50, -51, -52], [[22, 2000001, -47, -46, -45], [19, 1, 227.40499999999997, -48]], [2, "a6rE8P01tPh4ZZtCrqhz7V", -44], [5, 173, 32], [244.911, 439.595, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "itemTip", 2, [-54, -55, -56, -57, -58, -59, -60], [[23, -53]], [0, "f2csfvVZ9BIbZcuxn0OoR/", 1, 0], [5, 318.6, 309.5], [-105, 107.95, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "effect_lock_on", [-61, -62, -63, -64, -65, -66, -67, -68, -69], [0, "cejarpNypEqonLyUE7rTns", 1, 0]], [42, "view", 2, [-73, -74, -75, -76], [[-70, [24, -71, [25]], [20, 5, 302.1, 293, 846, -72]], 1, 4, 4], [0, "dd1Te2xrxIIISrExrnygpV", 1, 0], [5, 750, 770.9], [0, -4.550000000000011, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "btnUnlockState", false, 5, [-79, -80, -81, -82, -83], [[-77, [25, -78]], 1, 4], [0, "52+QCNrbZDWIIEkjmTAqsU", 1, 0], [5, 210, 89], [0, -80, 0, 0, 0, 0, 1, 1, 1, 0]], [18, "effect", 1, [-85, -86, -87, -88, 6], [-84], [0, "f7BgwsSIRFibus7NQ2Yx1J", 1, 0], [5, 110.8, 136.6], [1063.378, 53.444, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "layoutLevel", 7, [[51, 1, 2, 50, 50, 67.7000000000001, 150, 0, -89, [5, 0, 150]], [12, 4, -90]], [0, "91FkWwIoBNZZSh2ijdzxQU", 1, 0], [5, 0, 150], [0, 0.5, 0], [-295.687, -385.45, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn_return", 2, [[9, 0, -91, [48], 49], [46, 12, 30, 30, -92], [25, -93]], [0, "dcpMboBOBD7ZR/DCp//w1r", 1, 0], [5, 110, 110], [-299, -598, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "mask", 204, 1, [[9, 0, -94, [0], 1], [14, 45, 100, 100, -95], [23, -96]], [0, "b63fBKaJVAtLfRHEXCVWAO", 1, 0], [4, 4278190080], [5, 768, 1366]], [7, "icon", 3, [[[9, 0, -97, [6], 7], [47, 8, -1.2780000000000058, -98], -99], 4, 4, 1], [2, "7bbn0aADlCCouht2f0ToN9", 3], [5, 38, 38], [-68.778, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lab<PERSON>um", 3, [[-100, [10, 3, -101, [4, 4281341443]], [21, 37, 11.014999999999995, -3, 37.8, -102]], 1, 4, 4], [2, "3bWenBZd1Fs7SVqtMGF4jd", 3], [5, 130, 35], [10.485, -1.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "icon", 4, [[[9, 0, -103, [13], 14], [12, 8, -104], -105], 4, 4, 1], [2, "7bbn0aADlCCouht2f0ToN9", 4], [5, 38, 38], [-67.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lab<PERSON>um", 4, [[-106, [10, 3, -107, [4, 4281341443]], [21, 37, 11.014999999999995, -3, 37.8, -108]], 1, 4, 4], [2, "b3pZ7DNC5MCJG4hPswLUdD", 4], [5, 130, 35], [10.485, -1.5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "ui_title_di", 2, [-111], [[9, 0, -109, [20], 21], [48, 1, -1.1379999999999768, 179.22500000000002, -110]], [0, "fedu2zBYBC+Yz6bqC3N8df", 1, 0], [5, 228, 76], [-250.972, 465.775, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "mask_bg", 2, [-114], [[24, -112, [24]], [20, 5, 267, 226.79999999999995, 840.2, -113]], [0, "d4l9fNiexE4pui2Sz/Guz3", 1, 0], [5, 685.4, 872.2], [-0.1, -20.100000000000023, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnLockState", 5, [-116, -117], [-115], [0, "62tPfxG7NDH7LpVElbAfkx", 1, 0], [5, 210, 89], [0, -80.193, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "lab", false, 9, [[53, "+12", 25, 25, 1, 1, -118, [56]], [13, 44, 31.195, 31.195, -11.237, 5.585000000000001, 67.85, 50.4, -119], [10, 3, -120, [4, 4283379201]]], [0, "c5Z5XF0IVKLYQjoj80WU6n", 1, 0], [5, 48.41, 37.5], [0, -43.964999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "ui_frame_02", 2, [[3, 1, 0, -121, [2], 3], [13, 5, 206, 206, 200, 200, 356, 192, -122]], [0, "c2ml87d8dKq6EJGgMD80KB", 1, 0], [5, 728, 966]], [6, "bg", 3, [[3, 1, 0, -123, [4], 5], [13, 45, 16, 16, -2.5, -2.5, 173, 32, -124]], [2, "c5+vjvu1xHNa1dm/Q9kgXB", 3], [5, 141, 37]], [6, "bg", 4, [[3, 1, 0, -125, [11], 12], [13, 45, 16, 16, -2.5, -2.5, 173, 32, -126]], [2, "c5+vjvu1xHNa1dm/Q9kgXB", 4], [5, 141, 37]], [7, "labTitle", 5, [[-127, [10, 3, -128, [4, 4279963905]]], 1, 4], [0, "86Fc96i8VIUpzMmJGfXI+9", 1, 0], [5, 66, 43.8], [0, 112.902, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btn_grey", 19, [[3, 1, 0, -129, [31], 32], [14, 45, 204.8, 81, -130]], [0, "dbZemNNJBABKBc8UtmLd97", 1, 0], [5, 210, 89]], [6, "btn_yellow", 8, [[3, 1, 0, -131, [35], 36], [14, 45, 69, 81, -132]], [0, "c55MA7xp1Ms5eUblenhCRg", 1, 0], [5, 210, 89]], [7, "labCost", 8, [[-133, [10, 2, -134, [4, 4278334849]]], 1, 4], [0, "2dviY5wBdBi4bBVU1hg84L", 1, 0], [5, 64, 20], [0, 17.969, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "imgCost", 8, [[[1, -135, [42], 43], -136], 4, 1], [0, "7eEeyOc55HiIf+bsCYPA1W", 1, 0], [5, 43, 46], [-56.921, 18.637, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [44, "btnActiveState", false, 5, [-138], [-137], [0, "e9fkAV29lG8IhC4pg8x2Sb", 1, 0], [5, 210, 89], [0, -80.193, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "nextUnlock", 2, [-139], [0, "cfb/69iitDS5eWxyc9T31h", 1, 0], [614.436, 53.444, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "nextUnlock2", 2, [-140], [0, "696tFyz9xEDI/G8I0xAJGy", 1, 0], [699.603, 53.444, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 13], [26, "999999", 35, 35, false, -15, 1, 1, 2, 14, [8]], [11, "btnAdd", false, 3, [[1, -141, [9], 10]], [2, "aaGM9/xk5AaJFlUcj25Suc", 3], [5, 38, 38], [71.817, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 15], [26, "999999", 35, 35, false, -15, 1, 1, 2, 16, [15]], [11, "btnAdd", false, 4, [[1, -142, [16], 17]], [2, "aaGM9/xk5AaJFlUcj25Suc", 4], [5, 38, 38], [71.817, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "ui_title_shentan", 17, [[1, -143, [18], 19]], [0, "ad1qKLRVVP1Yns2QA+RkhU", 1, 0], [5, 92, 47], [-42.383, 4.24, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "bg_diwen", 100, 18, [[52, 1, -144, [22], 23]], [0, "4dcmGMIyVAepnLtDEQ7YCY", 1, 0], [4, 4289769648], [5, 501, 530], [0.1, 10.415, 0, 0, 0, 0, 1, 1.2, 1.2, 2]], [38, "lines", 7, [0, "c908xdOo1NjJTgGuW1o7GU", 1, 0], [-185.577, -446, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutGeneral", false, 7, [0, "74JJIn62ZGjb4kvJqEyWhp", 1, 0], [5, 0, 500], [0, 0.5, 0], [-185.577, -446, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutExpert", false, 7, [0, "cfLC/tAkVG65GtXkHN33B4", 1, 0], [5, 0, 500], [0, 0.5, 0], [209.586, -446, 0, 0, 0, 0, 1, 1, 1, 1]], [56, false, 0.2, 7, 10], [6, "ui_frame_02", 5, [[3, 1, 0, -145, [26], 27]], [0, "54xJSI3sVKyLDavCJcQrTj", 1, 0], [5, 314, 307]], [4, "ui_bar_skill_03", 5, [[3, 1, 0, -146, [28], 29]], [0, "c75SrwIutAGZEl4qxLNGeL", 1, 0], [5, 260, 100], [0, 24, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "生命", 30, 0, false, 1, 1, 24, [30]], [45, "labDesc", 5, [-147], [0, "63vu3cZB9CqKAvvcinAyJj", 1, 0], [5, 95.86, 31.5], [1.4, 24.7, 0, 0, 0, 0, 1, 1, 1, 1]], [57, 1, 25, 25, 47], [6, "btn_title_weijihuo", 19, [[1, -148, [33], 34]], [0, "e8nZ93iVpCHpq1LhiYY1jy", 1, 0], [5, 90, 32]], [58, 0.9, 3, 19], [4, "btn_title_jiesuo", 8, [[1, -149, [37], 38]], [0, "a7cg7A0A5Bh5X2gL5rcU8Q", 1, 0], [5, 61, 30], [0, -18.506, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "ui_bar_skill_03", 8, [[3, 1, 0, -150, [39], 40]], [0, "bel+sjLZxFbLydEpujD8uq", 1, 0], [5, 126, 26], [0, 19.02, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "3000", 25, 20, false, -12, 1, 1, 27, [41]], [15, 28], [59, false, 0.9, 3, 8, [[61, "e79ce9QRrdK+rN6PzJZ3KhV", "onBtnActive", 1]]], [4, "img_tick_1", 29, [[1, -151, [44], 45]], [0, "c0DPQVVr1J/ZTsEKU5/wDQ", 1, 0], [5, 63, 55], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1.5]], [60, 29], [4, "img_upgradation", 30, [[1, -152, [46], 47]], [0, "8f5Uv8NOlNCanEGwigszyQ", 1, 0], [5, 49, 49], [45, 45, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_upgradation", 31, [[1, -153, [50], 51]], [0, "a3eezmkf5EPYnkP5B9J1jU", 1, 0], [5, 49, 49], [45, 45, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "base", false, 9, [[1, -154, [52], 53]], [0, "f97a6afN1AaZflIfzXmKmW", 1, 0], [5, 119, 145]], [11, "icon", false, 9, [[1, -155, [54], 55]], [0, "52oINu9YlFFpr4UzEWXWvH", 1, 0], [5, 66, 56], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "mask", false, 150, 9, [[3, 1, 0, -156, [57], 58]], [0, "e1w8NNLWdNPou0KiuOaQDf", 1, 0], [5, 119, 145]], [41, "effect_lock_on_Circlering", 100, 6, [[1, -157, [59], 60]], [0, "78+vMqDMZEwpV55Xma7F0p", 1, 0], [5, 128, 128], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [5, "effect_lock_on_lizi_4", 6, [[27, 1, true, false, 3, 300, 0.3, 0.03, 180, 40, 10, 40, 7, 120, 10, 1, 620, 70, 0, -158, [61], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 50, 0], [0, 0, -1800], 62]], [0, "cao9ikQHNO/4YqbyCzKOrZ", 1, 0], [0, 30, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [5, "effect_lock_on_lizi_3", 6, [[28, 1, true, false, 3, 300, 0.3, 0.03, 30, 10, 40, 7, 120, 10, 1, 620, 70, 0, -159, [63], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 0, 50], [0, -1800, 0], 64]], [0, "f1PauscvxDO5Yt9+RbYNqJ", 1, 0], [30, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [5, "effect_lock_on_lizi_2", 6, [[28, 1, true, false, 3, 300, 0.3, 0.03, 30, 10, 40, 7, 120, 10, 1, 620, 70, 0, -160, [65], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 0, 50], [0, 1800, 0], 66]], [0, "85blNjZIFFn5sB6QrHtvzQ", 1, 0], [-30, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [5, "effect_lock_on_lizi_1", 6, [[27, 1, true, false, 3, 300, 0.3, 0.03, 0, 40, 10, 40, 7, 120, 10, 1, 620, 70, 0, -161, [67], [4, 4294967295], [4, 822083584], [4, 1191182335], [4, 167772160], [0, 50, 0], [0, 0, 1800], 68]], [0, "71EhNVBgxEpIsvpaVOG40P", 1, 0], [0, -30, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [5, "effect_lock_on_star_4", 6, [[29, true, false, 2, 300, 0.3, 0.03, 180, 40, 80, 10, 7, 120, 10, 1, 620, 70, 0, -162, [69], [4, 2533359615], [4, 822083584], [4, 3372220415], [4, 167772160], [0, 50, 0], [0, 0, -1800], 70]], [0, "74Wv654QZEfp8RZVheZyHi", 1, 0], [0, 30, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [5, "effect_lock_on_star_3", 6, [[30, true, false, 2, 300, 0.3, 0.03, 30, 80, 10, 7, 120, 10, 1, 620, 70, 0, -163, [71], [4, 2533359615], [4, 822083584], [4, 3372220415], [4, 167772160], [0, 0, 50], [0, -1800, 0], 72]], [0, "dbgYX/rvxCM5cX+8E8YlDV", 1, 0], [30, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [5, "effect_lock_on_star_2", 6, [[30, true, false, 2, 300, 0.3, 0.03, 30, 80, 10, 7, 120, 10, 1, 620, 70, 0, -164, [73], [4, 2533359615], [4, 822083584], [4, 3372220415], [4, 167772160], [0, 0, 50], [0, 1800, 0], 74]], [0, "9607anu6dLe5vnm+n6npjV", 1, 0], [-30, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [5, "effect_lock_on_star_1", 6, [[29, true, false, 2, 300, 0.3, 0.03, 0, 40, 80, 10, 7, 120, 10, 1, 620, 70, 0, -165, [75], [4, 2533359615], [4, 822083584], [4, 3372220415], [4, 167772160], [0, 50, 0], [0, 0, 1800], 76]], [0, "3crvNrP2JCr7kx1gX+Ozga", 1, 0], [0, -30, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [62, 9, [77]]], 0, [0, 3, 1, 0, 0, 1, 0, 7, 11, 0, 8, 72, 0, -1, 50, 0, -2, 55, 0, -3, 57, 0, 9, 54, 0, 10, 53, 0, 11, 48, 0, 12, 46, 0, 13, 5, 0, 14, 42, 0, 15, 41, 0, 16, 40, 0, 17, 10, 0, 18, 31, 0, 19, 30, 0, 20, 43, 0, 0, 1, 0, -1, 12, 0, -2, 2, 0, -3, 9, 0, 0, 2, 0, -1, 21, 0, -2, 3, 0, -3, 4, 0, -4, 17, 0, -5, 18, 0, -6, 7, 0, -7, 5, 0, -8, 30, 0, -9, 11, 0, -10, 31, 0, 3, 3, 0, 4, 33, 0, 5, 32, 0, 0, 3, 0, 0, 3, 0, -1, 22, 0, -2, 13, 0, -3, 14, 0, -4, 34, 0, 3, 4, 0, 4, 36, 0, 5, 35, 0, 0, 4, 0, 0, 4, 0, -1, 23, 0, -2, 15, 0, -3, 16, 0, -4, 37, 0, 0, 5, 0, -1, 44, 0, -2, 45, 0, -3, 24, 0, -4, 47, 0, -5, 19, 0, -6, 8, 0, -7, 29, 0, -1, 63, 0, -2, 64, 0, -3, 65, 0, -4, 66, 0, -5, 67, 0, -6, 68, 0, -7, 69, 0, -8, 70, 0, -9, 71, 0, -1, 43, 0, 0, 7, 0, 0, 7, 0, -1, 40, 0, -2, 10, 0, -3, 41, 0, -4, 42, 0, -1, 55, 0, 0, 8, 0, -1, 26, 0, -2, 51, 0, -3, 52, 0, -4, 27, 0, -5, 28, 0, -1, 72, 0, -1, 60, 0, -2, 61, 0, -3, 20, 0, -4, 62, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -3, 32, 0, -1, 33, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -3, 35, 0, -1, 36, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 38, 0, 0, 18, 0, 0, 18, 0, -1, 39, 0, -1, 50, 0, -1, 25, 0, -2, 49, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, -1, 46, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, -1, 53, 0, 0, 27, 0, 0, 28, 0, -2, 54, 0, -1, 57, 0, -1, 56, 0, -1, 58, 0, -1, 59, 0, 0, 34, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 44, 0, 0, 45, 0, -1, 48, 0, 0, 49, 0, 0, 51, 0, 0, 52, 0, 0, 56, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 21, 1, 6, 22, 9, 165], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 36, 46, 53, 72], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 23, 24, 25, 26, 2, 2, 2, 2, 27], [0, 10, 0, 11, 0, 3, 0, 5, 0, 0, 6, 0, 3, 0, 5, 0, 0, 6, 0, 12, 0, 13, 0, 14, 0, 0, 0, 15, 0, 3, 0, 16, 7, 0, 17, 0, 7, 0, 18, 0, 19, 0, 0, 20, 0, 21, 0, 8, 0, 22, 0, 8, 0, 23, 0, 24, 0, 0, 25, 0, 26, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2, 0, 2, 9, 27, 28, 29, 30, 4, 4, 31, 4, 9]], [[{"name": "6", "rect": [29, 26, 63, 67], "offset": [1, 0], "originalSize": [119, 119], "capInsets": [0, 0, 0, 0]}], [9], 0, [0], [6], [32]], [[{"name": "ui_bar_skill_03", "rect": [0, 0, 26, 26], "offset": [0, 0], "originalSize": [26, 26], "capInsets": [8, 8, 7, 9]}], [9], 0, [0], [6], [33]], [[[63, "generalTalentItemEff", 0.3333333333333333, 0.75, [{}, "paths", 11, [{"mask": {"props": {"opacity": [{"frame": 0.016666666666666666, "value": 0}]}}, "effect_lock_on/effect_lock_on_lizi_4": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}, "effect_lock_on/effect_lock_on_lizi_3": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}, "effect_lock_on/effect_lock_on_lizi_2": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}, "effect_lock_on/effect_lock_on_lizi_1": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}, "effect_lock_on/effect_lock_on_star_4": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}, "effect_lock_on/effect_lock_on_star_3": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}, "effect_lock_on/effect_lock_on_star_2": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}, "effect_lock_on/effect_lock_on_star_1": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 300}, {"frame": 0.26666666666666666, "value": 0}]}}}}, "effect_lock_on/effect_lock_on_Circlering", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 0.25, "value": 100}, {"frame": 0.2833333333333333, "value": 150}, {"frame": 0.3, "value": 150}, {"frame": 0.3333333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 0.23333333333333334, "curve": [0.55, 0.08, 0.83625, 0.43340909090909097]}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.2833333333333333}, "value", 8, [1, 2.338523774798986, 2.338523774798986, 1]], [{"frame": 0.3333333333333333}, "value", 8, [1, 3, 3, 1]]], 11, 11, 11]]]], "props", 11, [{"skewY": [{"frame": 0.016666666666666666, "value": 0}, {"frame": 0.06666666666666667, "value": -1}, {"frame": 0.11666666666666667, "value": 0}, {"frame": 0.16666666666666666, "value": 1}, {"frame": 0.21666666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.016666666666666666}, "value", 8, [1, 0.7, 0.7, 1]], [{"frame": 0.06666666666666667}, "value", 8, [1, 0, 0.72, 1]], [{"frame": 0.11666666666666667}, "value", 8, [1, -0.7, 0.7, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 0, 0.72, 1]], [{"frame": 0.21666666666666667}, "value", 8, [1, 0.7, 0.7, 1]], [{"frame": 0.23333333333333334}, "value", 8, [1, 0.7, 0.7, 1]], [{"frame": 0.26666666666666666}, "value", 8, [1, 1.2, 1.2, 1]], [{"frame": 0.2833333333333333}, "value", 8, [1, 1, 1, 1]]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]], 0, 0, [], [], []]]]