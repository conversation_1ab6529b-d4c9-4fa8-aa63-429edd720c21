[1, ["ecpdLyjvZBwrvm+cedCcQy", "927OBafolJ2pkmys0RPI5+", "fddjE/tXJGN7lx2YNmXMIG", "a2MjXRFdtLlYQ5ouAFv/+R", "b4ID1UXhVC3KCtv2kfmoJQ", "42nR4TEtlLvY8iS7+6QeeK", "a5hJI5OtNB0Yq0rp/qEfl/", "a17y/JnFdBio9pa+2aeg5/", "08CkzpcfVLiqslYxLKPhvy", "eb+gXtXDJIS5/CgKJCEcOL", "0c4IXK+XtEzLFRosHFG5oa", "07ACD7yBtJ3LoSSnHRiqhV", "efMVhMIHdBg5DxqS6zKGwG", "f3IxQhKYNF946oLoMr5DNk", "2akII3YllAU7SQ5a/0zLct", "29UxZziq5DRIqSrnKuWGgB", "b2aHrECZ5APKGS/0d2hvT1", "cdO96DRhRGWb/nPu9KT7Bf", "2dy1e4xRlGKY3coA85Tu3E", "fayxLdfZpHV5GdWzijqQol", "b8he+lAyhAj4C9NbBCOaaT", "e7P5rW1DdPWab3XyBNFod0", "0awdSm3/hOKLlOnAv91FeI", "4dwFWRme1K9LBWPvKbkB9s"], ["node", "_spriteFrame", "_file", "_N$file", "_mesh", "_defaultClip", "root", "data"], [["cc.Node", ["_name", "_opacity", "_is3DNode", "_prefab", "_parent", "_components", "_trs", "_contentSize", "_children", "_anchorPoint", "_eulerAngles", "_color"], 0, 4, 1, 9, 7, 5, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "angleVar", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "rotationIsDir", "lifeVar", "angle", "startSize", "endSpin", "endSpinVar", "endRadius", "_dstBlendFactor", "startSizeVar", "startSpin", "emitterMode", "tangentialAccelVar", "radialAccel", "radialAccelVar", "startRadius", "startRadiusVar", "rotatePerS", "rotatePerSVar", "startSpinVar", "endSizeVar", "endRadiusVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "_file", "_spriteFrame", "gravity", "posVar"], -28, 1, 3, 8, 8, 8, 8, 6, 6, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Mask", ["_type", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["node", "_materials", "_mesh"], 3, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_spacingX", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[3, 0, 1, 2, 2], [1, 0, 1, 3, 4, 5, 3], [0, 0, 1, 4, 5, 3, 7, 6, 3], [1, 3, 4, 5, 1], [0, 0, 1, 4, 5, 3, 7, 9, 6, 3], [0, 0, 4, 5, 3, 11, 7, 6, 2], [0, 0, 1, 4, 8, 5, 3, 7, 6, 3], [0, 0, 4, 8, 5, 3, 7, 6, 2], [0, 0, 4, 5, 3, 6, 2], [1, 0, 3, 4, 5, 2], [4, 0, 1, 2, 3, 2], [2, 17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 31, 32, 33, 34, 35, 36, 37, 38, 13], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [0, 0, 2, 4, 8, 5, 3, 6, 10, 3], [6, 0, 1, 2, 1], [0, 0, 1, 4, 5, 3, 7, 9, 6, 10, 3], [4, 1, 2, 1], [0, 0, 4, 8, 5, 3, 7, 9, 6, 10, 2], [0, 0, 4, 8, 3, 6, 2], [0, 0, 1, 4, 5, 3, 6, 3], [0, 0, 1, 4, 5, 3, 11, 7, 6, 10, 3], [0, 0, 1, 4, 5, 3, 11, 7, 6, 3], [0, 0, 4, 8, 3, 2], [2, 0, 1, 2, 3, 11, 12, 4, 13, 18, 5, 19, 14, 15, 6, 20, 7, 8, 9, 21, 22, 23, 24, 25, 16, 26, 27, 31, 32, 33, 34, 35, 36, 40, 39, 37, 38, 27], [5, 0, 2], [0, 0, 8, 5, 3, 6, 2], [0, 0, 4, 8, 5, 3, 7, 9, 2], [0, 0, 4, 5, 3, 7, 9, 6, 2], [1, 2, 3, 4, 5, 2], [3, 1, 2, 1], [2, 0, 1, 2, 3, 4, 13, 5, 28, 14, 15, 6, 7, 8, 9, 31, 32, 33, 34, 35, 36, 37, 38, 15], [2, 0, 1, 2, 3, 11, 12, 4, 5, 29, 6, 7, 8, 9, 10, 16, 30, 31, 32, 33, 34, 35, 36, 39, 37, 38, 17], [8, 0, 1, 2, 3, 2]], [[24, "skill_xy"], [25, "skill_xy", [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33], [[32, true, -2, [209], 208]], [29, -1, 0], [0, 0, 0, 0, 0, 0, 1, 0.458, 0.458, 0.458]], [18, "shanghai", 1, [-34, -35, -36, -37, -38, -39, -40, -41], [0, "00HG+p5+RGn6vfYcdfKB6A", 1, 0], [0, 126.223, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "shousuo_plane", true, 1, [-43, -44, -45, -46], [[14, -42, [8], 9]], [0, "advWHG35VG2ZfUViN37G1G", 1, 0], [0, 0, 0, 0.42261826174069944, 0, 0, 0.9063077870366499, 0.8, 0.8, 1], [1, 50, 0, 0]], [18, "monster", 1, [-47, -48, -49, -50], [0, "6aMx6rG1FE665SOMXECjZj", 1, 0], [0, 64.327, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "effect_ty_jz_xl", 1, [-51, -52, -53, -54], [0, "d1CUdb8lVPdpZszMIvd/xQ", 1, 0], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [22, "monster2", 1, [-55, -56, -57, -58], [0, "6dYfEjfS5Mq7+wzgK5BDu2", 1, 0]], [13, "effect_xy_02_henji_001", true, 1, [-60, -61], [[14, -59, [14], 15]], [0, "75vyvIW0BPnarx9gHAScWd", 1, 0], [273.516, -222.372, 57.2, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [13, "effect_xy_02_henji_002", true, 1, [-63, -64], [[14, -62, [20], 21]], [0, "58UFHLictHTbT63zNBG6W8", 1, 0], [149.655, 147.588, 32.562, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [13, "effect_xy_02_henji_003", true, 1, [-66, -67], [[14, -65, [26], 27]], [0, "67c3D6kw5Ni7rZsZ5vI+ws", 1, 0], [-209.612, -240.303, 16.723, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [13, "effect_xy_02_henji_004", true, 1, [-69, -70], [[14, -68, [32], 33]], [0, "d44HLQCBND6IvOShwu6oFw", 1, 0], [-336.286, 7.346, 34.322, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [13, "effect_xy_02_henji_005", true, 1, [-72, -73], [[14, -71, [38], 39]], [0, "9aZzeMEGJMiaIcmMAy07My", 1, 0], [422.723, -24.774, 36.082, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [13, "effect_xy_02_henji_006", true, 1, [-75, -76], [[14, -74, [44], 45]], [0, "dcK/TJ5jZHkLilW9uNh7Xg", 1, 0], [29.868, -20.344, 30.802, -0.42261826174069944, 0, 0, 0.9063077870366499, 1, 1, 1], [1, -50, 0, 0]], [7, "1", 4, [-78, -79], [[1, 2, false, -77, [53], 54]], [0, "a6zWsG+2BNh4PtUhL4b+ql", 1, 0], [5, 126, 73], [243.072, 11.613, 0, 0, 0, 0, 1, 2, 2, 1]], [7, "2", 4, [-81, -82], [[1, 2, false, -80, [62], 63]], [0, "db1F3P25ZKyJoLM4IUz9AW", 1, 0], [5, 126, 73], [140.723, 193.541, 0, 0, 0, 0, 1, 2, 2, 1]], [7, "7", 4, [-84, -85], [[1, 2, false, -83, [71], 72]], [0, "af13XXkURGMLwCtA2X+9ag", 1, 0], [5, 126, 73], [-158.134, 54.941, 0, 0, 0, 0, 1, -2, 2, 1]], [7, "8", 4, [-87, -88], [[1, 2, false, -86, [80], 81]], [0, "4c54v3dvVGN7+8f+KddYOH", 1, 0], [5, 126, 73], [-57.01, 195.095, 0, 0, 0, 0, 1, -2, 2, 1]], [17, "effect_xy_02_jian_down02", 1, [-90, -91], [[16, -89, [98]]], [0, "e3Wo9w/MlL76cHRgn+v/Av", 1, 0], [5, 400, 1500], [0, 0.5, 0], [127.723, 154.615, 0, 0, 0, -0.09584575252022398, 0.9953961983671789, 1, 1, 1], [1, 0, 0, -11]], [17, "effect_xy_02_jian_down04", 1, [-93, -94], [[16, -92, [103]]], [0, "57wWSpkHpNoIz3SwCPjo6P", 1, 0], [5, 400, 1500], [0, 0.5, 0], [-336.573, 23.847999999999956, 0, 0, 0, 0.13917310096006544, 0.9902680687415704, 1, 1, 1], [1, 0, 0, 16]], [17, "effect_xy_02_jian_down05", 1, [-96, -97], [[16, -95, [108]]], [0, "aeGTk+V51LJ6gNqn2DUWhD", 1, 0], [5, 400, 1500], [0, 0.5, 0], [408.29300000000006, -6.794999999999959, 0, 0, 0, -0.1908089953765448, 0.981627183447664, 1, 1, 1], [1, 0, 0, -22]], [26, "effect_xy_02_jian_down06", 1, [-99, -100], [[16, -98, [113]]], [0, "638pI7uItOw7qs8PCXQRwy", 1, 0], [5, 600, 1500], [0, 0.5, 0]], [7, "3", 6, [-102, -103], [[1, 2, false, -101, [135], 136]], [0, "45+XmQoVhJx6tpwafr9bfL", 1, 0], [5, 126, 73], [139.345, -208.171, 0, 0, 0, 0, 1, 2, 2, 2]], [7, "4", 6, [-105, -106], [[1, 2, false, -104, [144], 145]], [0, "6848f1r/JA1I9FouAKfgca", 1, 0], [5, 126, 73], [150.42, -77.343, 0, 0, 0, 0, 1, 2, 2, 2]], [7, "5", 6, [-108, -109], [[1, 2, false, -107, [153], 154]], [0, "2eY8lVrDlBaqjDkLp6Ei+M", 1, 0], [5, 126, 73], [-70.135, -169.595, 0, 0, 0, 0, 1, -2, 2, -2]], [7, "6", 6, [-111, -112], [[1, 2, false, -110, [162], 163]], [0, "0eEg5S5chCzbLlry6a938P", 1, 0], [5, 126, 73], [-236.549, -104.912, 0, 0, 0, 0, 1, -2, 2, -2]], [17, "effect_xy_02_jian_down01", 1, [-114, -115], [[16, -113, [168]]], [0, "deq2TGCMBDSqGoa59TURqI", 1, 0], [5, 400, 1500], [0, 0.5, 0], [257.434, -202.44, 0, 0, 0, -0.052335956242943835, 0.9986295347545738, 1, 1, 1], [1, 0, 0, -6]], [17, "effect_xy_02_jian_down03", 1, [-117, -118], [[16, -116, [173]]], [0, "0frdHamx9Cr4C3yNWTIY3K", 1, 0], [5, 400, 1500], [0, 0.5, 0], [-222.357, -227.545, 0, 0, 0, 0.06104853953485687, 0.9981347984218669, 1, 1, 1], [1, 0, 0, 7]], [6, "New Node", 0, 13, [-120], [[10, 2, -119, [48], 49]], [0, "06wiYpnYBOJatj+xsrK1j5", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "New Node", 0, 14, [-122], [[10, 2, -121, [57], 58]], [0, "08yGhF/UBInKNTGBrak497", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "New Node", 0, 15, [-124], [[10, 2, -123, [66], 67]], [0, "18PEIAvFFOko94ZPBBslRs", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "New Node", 0, 16, [-126], [[10, 2, -125, [75], 76]], [0, "d6ioO/UtNHJJ2pUAM6BI7m", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "New Node", 0, 21, [-128], [[10, 2, -127, [130], 131]], [0, "a9xRytzpBFaKe85gjP+hcq", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "New Node", 0, 22, [-130], [[10, 2, -129, [139], 140]], [0, "f5LgvJ/SFLlZBJsYLASZN1", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "New Node", 0, 23, [-132], [[10, 2, -131, [148], 149]], [0, "234Or71gJEt7ptvzTWSyL4", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "New Node", 0, 24, [-134], [[10, 2, -133, [157], 158]], [0, "d8my9lRuFLMbh5E6CjhxY/", 1, 0], [5, 200, 122], [0, 0, 0, 0, 0, 0, 1, 0.629, 0.629, 1.13]], [6, "effect_xy_blade_01", 0, 1, [-136], [[1, 2, false, -135, [206], 207]], [0, "2aKqX80S5CcazE71KKjC5Z", 1, 0], [5, 128, 352], [-7.05, 1964.414, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [18, "effect_xy_02_glow", 35, [-137, -138], [0, "c9080sAF1Mj4+mMYIZhAKP", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0, 0, 1]], [20, "glow_0014_2", 0, 3, [[3, -139, [0], 1]], [0, "721pgap71HULVaqiQx4O+7", 1, 0], [4, 4284111615], [5, 350, 350], [0, 0, 0, 0, 0, 0, 1, 0, 0, 1], [1, -50, 0, 0]], [20, "glow_0014_3", 0, 3, [[3, -140, [2], 3]], [0, "396pFlNY5M1Jba9KJ4K/pz", 1, 0], [4, 4284111615], [5, 350, 350], [0, 0, 0, 0, 0, 0, 1, 0, 0, 1], [1, -50, 0, 0]], [21, "shou<PERSON><PERSON>", 0, 3, [[3, -141, [4], 5]], [0, "a5skFBCmBISKhssAxI8er2", 1, 0], [4, 4280624639], [5, 365, 363], [0, 0, 0, 0, 0, 0, 1, 0, 0, 1]], [21, "shousuo02", 0, 3, [[3, -142, [6], 7]], [0, "5fN/eE8YRPxYhxZkmxERGp", 1, 0], [4, 4280624639], [5, 365, 363], [0, 0, 0, 0, 0, 0, 1, 0, 0, 1]], [2, "xy_02_xuanwo", 0, 7, [[1, 2, false, -143, [10], 11]], [0, "a2YmnHNUhDyoC+cOHKupQm", 1, 0], [5, 381, 187], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [2, "xy_02_ring", 0, 7, [[3, -144, [12], 13]], [0, "5fD9gN4BRHgaqlQNzcrDND", 1, 0], [5, 193, 193], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "xy_02_xuanwo", 0, 8, [[1, 2, false, -145, [16], 17]], [0, "67Wfv9SiJI5J1cRjjwY/DK", 1, 0], [5, 381, 187], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [2, "xy_02_ring", 0, 8, [[3, -146, [18], 19]], [0, "acEgl9lCtEG4vcSOL/Ejz7", 1, 0], [5, 193, 193], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "xy_02_xuanwo", 0, 9, [[1, 2, false, -147, [22], 23]], [0, "d9QGzrtRxE7Y6J/MEryJ/7", 1, 0], [5, 381, 187], [0, 0, -61.205, 0, 0, 0, 1, 1.3, 1.3, 1]], [2, "xy_02_ring", 0, 9, [[3, -148, [24], 25]], [0, "fen/fyHH9HdpFxtmHkzVIm", 1, 0], [5, 193, 193], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "xy_02_xuanwo", 0, 10, [[1, 2, false, -149, [28], 29]], [0, "1cYSe6j49LdojBN1qhNZk5", 1, 0], [5, 381, 187], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [2, "xy_02_ring", 0, 10, [[3, -150, [30], 31]], [0, "24SNt3bB1Bx4znziBIZPEM", 1, 0], [5, 193, 193], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "xy_02_xuanwo", 0, 11, [[1, 2, false, -151, [34], 35]], [0, "b3VeUskXNEobrXzZunBIcD", 1, 0], [5, 381, 187], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [2, "xy_02_ring", 0, 11, [[3, -152, [36], 37]], [0, "18O0K+C2RP7LZK/CqgAHIB", 1, 0], [5, 193, 193], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [2, "xy_02_xuanwo", 0, 12, [[1, 2, false, -153, [40], 41]], [0, "d04ZxmBdZCFpXNX/fREFiV", 1, 0], [5, 381, 187], [-6.892, -3.212, -7.94, 0, 0, 0, 1, 2, 2, 1]], [2, "xy_02_ring", 0, 12, [[3, -154, [42], 43]], [0, "32or9Yj5xIlos6JDFi9Pe2", 1, 0], [5, 193, 193], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [5, "New Sprite(Splash)", 27, [[9, 0, -155, [46], 47]], [0, "d5mkBm4U9Gt4FKWhd1ExxD", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [8, "hit", 13, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -156, [50], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 51, 52]], [0, "7dsneQxNFCD50AwK8cTsPw", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [5, "New Sprite(Splash)", 28, [[9, 0, -157, [55], 56]], [0, "b44smpOGZOLo65Bhx9czLP", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [8, "hit", 14, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -158, [59], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 60, 61]], [0, "8bBMrKuodAC5KZ4AzJNTww", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [5, "New Sprite(Splash)", 29, [[9, 0, -159, [64], 65]], [0, "96X6vMNmRHYo9Id6TJ8KWz", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [8, "hit", 15, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -160, [68], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 69, 70]], [0, "8dyR28tn5Omafnn7vDd4AU", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [5, "New Sprite(Splash)", 30, [[9, 0, -161, [73], 74]], [0, "43PVK4aKVCgI+get6LHdJC", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [8, "hit", 16, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -162, [77], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 78, 79]], [0, "45KR6R7sdBx4tRyH0fZASl", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [8, "effect_ty_jz_xl_lizi", 5, [[23, true, 50, 0, 0.15, 0.05, 360, 360, 30, 2, 10, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 1, 200, 190.7899932861328, -92.1100006, 265.7900009, -671.0499878, 265.7900009, 250, 50, 10, -200, 100, -163, [82], [4, 2516637695], [4, 838860800], [4, 838916095], [4, 0], [0, 100, 100], [0, 1.25, 1.86], 83, 84]], [0, "26j6oo8V5LQplJI99NL00E", 1, 0], [2.382, 41.367, 0, 0, 0, 0, 1, 1.7, 1.7, 1.7]], [8, "effect_ty_jz_xl_lizi2", 5, [[23, true, 100, 0, 0.3, 0.1, 360, 360, 30, 2, 10, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 1, 200, 190.7899932861328, -92.1100006, 265.7900009, -671.0499878, 265.7900009, 230, 50, 10, 200, 100, -164, [85], [4, 2516582655], [4, 838860800], [4, 838861055], [4, 0], [0, 100, 100], [0, 1.25, 1.86], 86, 87]], [0, "e4oufcVm9MSZEa/zOzK7KC", 1, 0], [2.382, 41.367, 0, 0, 0, 0, 1, 1.7, 1.7, 1.7]], [8, "effect_ty_jz_xl_flash", 5, [[30, true, 2, 0, 0.13, 360, 400, 240, 30, 10, 30, 1, 0, 0, 0, -165, [88], [4, 4278190335], [4, 0], [4, 838861045], [4, 0], 89, 90]], [0, "44zplAy6REqJTXfRQ12jIj", 1, 0], [2.382, 41.367, 0, 0, 0, 0, 1, 1.7, 1.7, 1.7]], [8, "effect_ty_jz_xl_flas_lizi", 5, [[31, true, 8, 0, 0.15, 0.05, 360, 360, 20, 2, 1, 1000, 70, 0, true, 60, 5, -166, [91], [4, 4278190332], [4, 0], [4, 1677721852], [4, 0], [0, 0.25, 0.8600000143051147], 92, 93]], [0, "7dJ9AFahpOUo8KFLNzmCDS", 1, 0], [2.382, 41.367, 0, 0, 0, 0, 1, 1.7, 1.7, 1.7]], [2, "effect_xy_blade_02", 0, 17, [[3, -167, [94], 95]], [0, "c9hKQST4hIEaJcghJf/1Il", 1, 0], [5, 128, 352], [0, 84.489, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [15, "effect_xy_blade_01", 0, 17, [[1, 2, false, -168, [96], 97]], [0, "5dF0qIuTJDA7S71rbMHrKq", 1, 0], [5, 126, 412], [0, 0.5, 0], [0, -126.646, 0, 0, 0, 1, 6.123233995736766e-17, 2, -2, 0.8], [1, 0, 0, 180]], [2, "effect_xy_blade_02", 0, 18, [[3, -169, [99], 100]], [0, "02l8Gs/iVFdrBjRMiHQQtH", 1, 0], [5, 128, 352], [0, 84.489, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [15, "effect_xy_blade_01", 0, 18, [[1, 2, false, -170, [101], 102]], [0, "f8aSq0LNZB5ZjYU8QiodEE", 1, 0], [5, 126, 412], [0, 0.5, 0], [0, -126.646, 0, 0, 0, 1, 6.123233995736766e-17, 2, -2, 0.8], [1, 0, 0, 180]], [2, "effect_xy_blade_02", 0, 19, [[3, -171, [104], 105]], [0, "30Exv5/j9FxrpMQDzpc5wo", 1, 0], [5, 128, 352], [0, 84.489, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [15, "effect_xy_blade_01", 0, 19, [[1, 2, false, -172, [106], 107]], [0, "a3NbNwc71Gx7+C33FBod6t", 1, 0], [5, 126, 412], [0, 0.5, 0], [0, -126.646, 0, 0, 0, 1, 6.123233995736766e-17, 2, -2, 0.8], [1, 0, 0, 180]], [2, "effect_xy_blade_02", 0, 20, [[3, -173, [109], 110]], [0, "32qVaB6VdFHYqSQIEBEGSG", 1, 0], [5, 128, 352], [0, 188.94, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [15, "effect_xy_blade_01", 0, 20, [[1, 2, false, -174, [111], 112]], [0, "d2KX8bqpVJe4lUwVbccUP6", 1, 0], [5, 126, 412], [0, 0.5, 0], [0, -230.312, 0, 0, 0, 1, 6.123233995736766e-17, 4, -4, 0.8], [1, 0, 0, 180]], [4, "7000507_bz1_06", 0, 1, [[1, 2, false, -175, [114], 115]], [0, "65C58+QfRI5a9BPDc30/4U", 1, 0], [5, 340, 377], [0, 0.5, 0.1], [0, 0, 0, 0, 0, 0, 1, 2, 2, 0.7]], [4, "7000507_bz1_01", 0, 1, [[1, 2, false, -176, [116], 117]], [0, "557XfD01RDwZtXej2rALZs", 1, 0], [5, 340, 377], [0, 0.5, 0.1], [151.173, 228.95, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [4, "7000507_bz1_03", 0, 1, [[1, 2, false, -177, [118], 119]], [0, "b16oZoRGRFx6vIo4Jjq6O2", 1, 0], [5, 340, 377], [0, 0.5, 0.1], [-233.046, -211.841, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [4, "effect_xy_Skill_02_brust_006", 0, 1, [[1, 2, false, -178, [120], 121]], [0, "6fhyY+UW5Mno+TyXo8zcGY", 1, 0], [5, 181, 113], [0, 0.5, 0.1], [0, 0, 0, 0, 0, 0, 1, 3.5, 3.5, 1]], [4, "effect_xy_Skill_02_brust_001", 0, 1, [[1, 2, false, -179, [122], 123]], [0, "cdzOOsF+JGqZVVcUZ2GvhE", 1, 0], [5, 181, 113], [0, 0.5, 0.1], [255.246, -211.975, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [4, "effect_xy_Skill_02_brust_003", 0, 1, [[1, 2, false, -180, [124], 125]], [0, "eb+scs/m1EOql5ckOWkNPA", 1, 0], [5, 181, 113], [0, 0.5, 0.1], [-206.53, -229.832, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [22, "Body", 1, [-181], [0, "17KHdToqpMQ6DhYpR3KIf4", 1, 0]], [27, "Skin", 79, [[1, 2, false, -182, [126], 127]], [0, "53pulp131JPqGpRE0mffSq", 1, 0], [5, 341, 264], [0, 0.5, 0], [-132.693, -154.771, 0, 0, 0, 0, 1, 2, 2, 1]], [5, "New Sprite(Splash)", 31, [[9, 0, -183, [128], 129]], [0, "570msta3NNfISL5IL8wp19", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [19, "hit", 0, 21, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -184, [132], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 133, 134]], [0, "631J/44/lBnYuQyZBsNGqt", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [5, "New Sprite(Splash)", 32, [[9, 0, -185, [137], 138]], [0, "76ZdEDJPBO07+iXWw/gGpD", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [19, "hit", 0, 22, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -186, [141], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 142, 143]], [0, "f7lUoQUrtM1ZnS5tbcbuRO", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [5, "New Sprite(Splash)", 33, [[9, 0, -187, [146], 147]], [0, "b8RmCh2qJMwJXU0OFqxva8", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [19, "hit", 0, 23, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -188, [150], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 151, 152]], [0, "0a8w/G2ItH5oHEb+bFTvGg", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [5, "New Sprite(Splash)", 34, [[9, 0, -189, [155], 156]], [0, "1e8kK/ultBVIeLRNn7VHHu", 1, 0], [4, 4278190288], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [19, "hit", 0, 24, [[11, 1, true, 1, 0, 0.115, 180, 150, 1, 5, 0, 0, true, -190, [159], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 160, 161]], [0, "71bMFShiBNi690Ud4uypiQ", 1, 0], [9.727, -4.863, 0, 0, 0, 0, 1, 0.4, 0.4, 0.7548]], [2, "effect_xy_blade_02", 0, 25, [[3, -191, [164], 165]], [0, "50KKMuwlFCto1uLwXShJGO", 1, 0], [5, 128, 352], [0, 84.489, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [15, "effect_xy_blade_01", 0, 25, [[1, 2, false, -192, [166], 167]], [0, "88B9LzegNHeIGEndtqPdFv", 1, 0], [5, 126, 412], [0, 0.5, 0], [0, -126.646, 0, 0, 0, 1, 6.123233995736766e-17, 2, -2, 0.8], [1, 0, 0, 180]], [2, "effect_xy_blade_02", 0, 26, [[3, -193, [169], 170]], [0, "2boBrPKjtLzan/tXa4pDGV", 1, 0], [5, 128, 352], [0, 84.489, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [15, "effect_xy_blade_01", 0, 26, [[1, 2, false, -194, [171], 172]], [0, "6cpyVRaetGm4Xw39V0OdpJ", 1, 0], [5, 126, 412], [0, 0.5, 0], [0, -126.646, 0, 0, 0, 1, 6.123233995736766e-17, 2, -2, 0.8], [1, 0, 0, 180]], [4, "7000507_bz1_05", 0, 1, [[1, 2, false, -195, [174], 175]], [0, "d8fazh1YlPm7+msi/jDVWc", 1, 0], [5, 340, 377], [0, 0.5, 0.1], [415.144, 20.143, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [4, "7000507_bz1_02", 0, 1, [[1, 2, false, -196, [176], 177]], [0, "89MesUTK9AHppiPce6GhQ9", 1, 0], [5, 340, 377], [0, 0.5, 0.1], [257.246, -181.198, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [4, "7000507_bz1_04", 0, 1, [[1, 2, false, -197, [178], 179]], [0, "71ZICtfjhGWpOLVwjRIqr/", 1, 0], [5, 340, 377], [0, 0.5, 0.1], [-338.776, 70.273, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [4, "effect_xy_Skill_02_brust_005", 0, 1, [[1, 2, false, -198, [180], 181]], [0, "b90V8k53hIPJwu7BCJwoGB", 1, 0], [5, 181, 113], [0, 0.5, 0.1], [431.032, -29.475, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [4, "effect_xy_Skill_02_brust_002", 0, 1, [[1, 2, false, -199, [182], 183]], [0, "67lPY4pm5Dk7pvshG9PcwT", 1, 0], [5, 181, 113], [0, 0.5, 0.1], [125.068, 145.957, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [4, "effect_xy_Skill_02_brust_004", 0, 1, [[1, 2, false, -200, [184], 185]], [0, "16kHQjg0VOypKx9k3JSfLa", 1, 0], [5, 181, 113], [0, 0.5, 0.1], [-317.32, 15.087, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [2, "1", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -201, [186], 187]], [0, "1eNyCWgfJH9p7v0Oz/L3gA", 1, 0], [5, 220, 40], [324.556, 221.794, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "2", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -202, [188], 189]], [0, "85qhR/DvFOlKcFBVI0+3RA", 1, 0], [5, 220, 40], [228.128, 413.258, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "3", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -203, [190], 191]], [0, "3684IJfz5F5rZfp7mEfYo1", 1, 0], [5, 220, 40], [180.353, 80.024, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "4", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -204, [192], 193]], [0, "aaiMg996FBU4D0NjxDxcxp", 1, 0], [5, 220, 40], [171.992, -66.886, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "5", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -205, [194], 195]], [0, "a2o1NrCU1L5bWQ93/mDdM5", 1, 0], [5, 220, 40], [-272.32, 53.747, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -206, [196], 197]], [0, "a2jn6uGvBCRY/TeqV7wroQ", 1, 0], [5, 220, 40], [-243.655, 240.072, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -207, [198], 199]], [0, "ee8O1ze2RM96ZMFhNX2CIg", 1, 0], [5, 220, 40], [-139.743, 396.536, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "8", 0, 2, [[12, "-9999", 60, false, -20, 1, 1, -208, [200], 201]], [0, "3eAMsOBJ5JpY9nz7kXvdh/", 1, 0], [5, 220, 40], [-91.967, -32.249, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "effect_xy_02_glow", 36, [[3, -209, [202], 203]], [0, "ccxWAgwzdGYKgCujQagcYs", 1, 0], [4, 4278190335], [5, 124, 124], [0, 0, 0, 0, 0, 0, 1, 0.9, 2.8, 0.9]], [5, "effect_xy_02_glow_L", 36, [[28, 1, -210, [204], 205]], [0, "cbynvbT7lPyKHTKdmSFOjn", 1, 0], [4, 4278190335], [5, 124, 124], [0, 0, 0, 0, 0, 0, 1, 1, 3, 1]]], 0, [0, 6, 1, 0, 0, 1, 0, -1, 3, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, -8, 4, 0, -9, 5, 0, -10, 17, 0, -11, 18, 0, -12, 19, 0, -13, 20, 0, -14, 73, 0, -15, 74, 0, -16, 75, 0, -17, 76, 0, -18, 77, 0, -19, 78, 0, -20, 79, 0, -21, 6, 0, -22, 25, 0, -23, 26, 0, -24, 93, 0, -25, 94, 0, -26, 95, 0, -27, 96, 0, -28, 97, 0, -29, 98, 0, -30, 2, 0, -31, 35, 0, -1, 99, 0, -2, 100, 0, -3, 101, 0, -4, 102, 0, -5, 103, 0, -6, 104, 0, -7, 105, 0, -8, 106, 0, 0, 3, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 40, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 16, 0, -1, 61, 0, -2, 62, 0, -3, 63, 0, -4, 64, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, -4, 24, 0, 0, 7, 0, -1, 41, 0, -2, 42, 0, 0, 8, 0, -1, 43, 0, -2, 44, 0, 0, 9, 0, -1, 45, 0, -2, 46, 0, 0, 10, 0, -1, 47, 0, -2, 48, 0, 0, 11, 0, -1, 49, 0, -2, 50, 0, 0, 12, 0, -1, 51, 0, -2, 52, 0, 0, 13, 0, -1, 27, 0, -2, 54, 0, 0, 14, 0, -1, 28, 0, -2, 56, 0, 0, 15, 0, -1, 29, 0, -2, 58, 0, 0, 16, 0, -1, 30, 0, -2, 60, 0, 0, 17, 0, -1, 65, 0, -2, 66, 0, 0, 18, 0, -1, 67, 0, -2, 68, 0, 0, 19, 0, -1, 69, 0, -2, 70, 0, 0, 20, 0, -1, 71, 0, -2, 72, 0, 0, 21, 0, -1, 31, 0, -2, 82, 0, 0, 22, 0, -1, 32, 0, -2, 84, 0, 0, 23, 0, -1, 33, 0, -2, 86, 0, 0, 24, 0, -1, 34, 0, -2, 88, 0, 0, 25, 0, -1, 89, 0, -2, 90, 0, 0, 26, 0, -1, 91, 0, -2, 92, 0, 0, 27, 0, -1, 53, 0, 0, 28, 0, -1, 55, 0, 0, 29, 0, -1, 57, 0, 0, 30, 0, -1, 59, 0, 0, 31, 0, -1, 81, 0, 0, 32, 0, -1, 83, 0, 0, 33, 0, -1, 85, 0, 0, 34, 0, -1, 87, 0, 0, 35, 0, -1, 36, 0, -1, 107, 0, -2, 108, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, -1, 80, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 90, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, 7, 1, 210], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 3, -1, 3, -1, 3, -1, 3, -1, 3, -1, 3, -1, 3, -1, 3, -1, 1, -1, 1, -1, 1, 5, -1], [0, 14, 0, 14, 0, 15, 0, 15, 6, 7, 0, 8, 0, 9, 6, 7, 0, 8, 0, 9, 6, 7, 0, 8, 0, 9, 6, 7, 0, 8, 0, 9, 6, 7, 0, 8, 0, 9, 6, 7, 0, 8, 0, 9, 6, 7, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 16, 17, 0, 16, 17, 0, 2, 20, 0, 2, 21, 0, 10, 0, 11, 0, 0, 10, 0, 11, 0, 0, 10, 0, 11, 0, 0, 10, 0, 11, 0, 0, 12, 0, 12, 0, 12, 0, 13, 0, 13, 0, 13, 0, 22, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 3, 0, 1, 0, 2, 4, 0, 1, 0, 10, 0, 11, 0, 0, 10, 0, 11, 0, 0, 12, 0, 12, 0, 12, 0, 13, 0, 13, 0, 13, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 18, 0, 18, 0, 23, 19, 19]]