[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "a4mD+Lbc1HmoZgN4zFJyED", "29usNOBTJIvIIUYL5fJKW2", "e5/fZATxlISoMz2b6W0H0z", "4cTX2B4YNETrOVHftLOGBE", "277UNUOwFM4LFL9fnqQexT"], ["node", "_spriteFrame", "_file", "root", "_texture", "data"], [["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_trs", "_contentSize", "_color"], 1, 1, 9, 4, 7, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_groupIndex", "_children", "_prefab", "_components", "_trs", "_parent"], 1, 2, 4, 12, 7, 1], ["cc.Sprite", ["_sizeMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "_stopped", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "endSizeVar", "startSpin", "endSpin", "endSpinVar", "speed", "speedVar", "tangentialAccel", "tangentialAccelVar", "radialAccelVar", "startSpinVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_file", "_spriteFrame"], -19, 1, 3, 8, 8, 8, 8, 5, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.MotionStreak", ["_fadeTime", "_minSeg", "_stroke", "node", "_materials", "_color", "_texture"], 0, 1, 3, 5, 6], ["cc.RigidBody", ["_gravityScale", "_fixedRotation", "enabledContactListener", "node"], 0, 1], ["cc.PhysicsBoxCollider", ["_sensor", "node", "_offset", "_size"], 2, 1, 5, 5]], [[1, 0, 1, 2, 2], [0, 0, 2, 3, 4, 5, 2], [5, 0, 2], [2, 0, 1, 2, 4, 3, 5, 3], [2, 0, 6, 2, 3, 2], [0, 0, 2, 3, 4, 6, 5, 2], [0, 0, 1, 2, 3, 4, 7, 6, 5, 3], [6, 0, 1, 2, 3, 4, 5, 6, 4], [1, 0, 1, 2], [1, 1, 2, 1], [3, 0, 2, 3, 4, 2], [3, 1, 2, 3, 4, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 22], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 21, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 23], [7, 0, 1, 2, 3, 4], [8, 0, 1, 2, 3, 2]], [[2, "bullet_hy_01"], [3, "effect_hy_01", 4, [-4], [[[14, 0, true, true, -2], [15, true, -3, [0, -1, 1], [5, 54, 19]], null], 4, 4, 0], [9, -1, 0], [213.03, 636.668, 0, 0, 0, 0, 1, -1, 1, -1]], [4, "bullet", 1, [-5, -6, -7, -8, -9], [0, "99t6X3/apJuZpqMCo/s/Zj", 1, 0]], [1, "effect_ty_tw", 2, [[7, 0.2, 0.2, 12, -11, [0], [4, 1684761599], 1]], [8, "afcmXYpcxKgY4glG6HNj0p", -10], [29.177, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "effect_hy_01_bullet", 2, [[10, 0, -12, [2], 3]], [0, "07h5CodMVMfoQCoiNnh122", 1, 0], [5, 150, 62], [0, 0, 0, 0, 0, 0, 1, 0.3, 0.3, 0.3]], [1, "effect_hy_01_lizi", 2, [[12, 1, true, false, 30, 25, 0.5, 0.3, 0, 15, 40, 5, 30, 5, -47.369998931884766, -47.369998931884766, -142.11000061035156, 0, 0, 0, 100, 100, -13, [4], [4, 3364549870], [4, 0], [4, 9102836], [4, 0], [0, 30, 30], 5, 6]], [0, "94p4F71UtAm6MmISgkPrLN", 1, 0], [-21.002, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_hy_01_pice", 2, [[13, 1, true, false, 25, 20, 0.7, 0.2, 0, 0, 10, 5, 7, 5, -47.369998931884766, 142, -47.369998931884766, -142.11000061035156, 0, 0, 0, 100, 100, -14, [7], [4, 3362121723], [4, 0], [4, 9106164], [4, 0], [0, 30, 30], 8, 9]], [0, "fbRDLl/S9I85vjZN928kJu", 1, 0], [-33.028, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "effect_hy_01_bullet_glow", 200, 2, [[11, 1, -15, [10], 11]], [0, "3cqdaLwg9B7J1ySqGt2vDp", 1, 0], [4, 4278243582], [5, 172, 157], [-13.329, 0, 0, 0, 0, 0, 1, 0.2, 0.2, 0.2]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, 3, 3, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 5, 1, 15], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 4, -1, 1, -1, 2, 1, -1, 2, 1, -1, 1], [0, 2, 0, 3, 0, 1, 4, 0, 1, 5, 0, 6]]