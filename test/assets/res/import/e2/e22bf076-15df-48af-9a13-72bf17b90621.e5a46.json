[1, ["81a9gw/KhJjYh/+/rmzKbC", "36H4MmaVpOdb9fGCswBb+p", "71qbVqAORFdphGedhuXQsv", "49r9Ivx7dIY5DrEAlsuWAX", "8aMdeBivdJGaGtu6ezUe4I", "a49bilih1HN7Zz36uAiexn", "a7KQbzxOVChowj0De6YHRx", "79GmtVT6RKv6vJNOyV0kTS", "3aEIGFiWhKtLCPt4W9pHnh", "ddNE5kkHxBN7CF0aZZDFwA", "66Gd0KznhOgbi2i0fjlI6i", "8aEzAVw5dLAI4e0y4u/sWU"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], 0, 11]], [[0, 0, 1, 2, 3, 4]], [[0, "skill02", 2.9166666666666665, 24, [{"props": {"y": [{"frame": 0, "value": -30.85, "curve": "constant"}, {"frame": 0.041666666666666664, "value": -30.10625, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -30}]}, "paths": {"futou": {"props": {"active": [{"frame": 0, "value": false}]}}}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.08333333333333333}, "value", 6, 1], [{"frame": 0.16666666666666666}, "value", 6, 2], [{"frame": 0.25}, "value", 6, 3], [{"frame": 0.2916666666666667}, "value", 6, 4], [{"frame": 0.3333333333333333}, "value", 6, 5], [{"frame": 0.375}, "value", 6, 6], [{"frame": 0.4166666666666667}, "value", 6, 7], [{"frame": 0.4583333333333333}, "value", 6, 8], [{"frame": 0.5}, "value", 6, 9], [{"frame": 0.5416666666666666}, "value", 6, 10], [{"frame": 0.5833333333333334}, "value", 6, 11], [{"frame": 0.625}, "value", 6, 12], [{"frame": 0.6666666666666666}, "value", 6, 13], [{"frame": 0.7083333333333334}, "value", 6, 14], [{"frame": 0.75}, "value", 6, 15], [{"frame": 0.7916666666666666}, "value", 6, 16], [{"frame": 0.8333333333333334}, "value", 6, 17], [{"frame": 0.875}, "value", 6, 18], [{"frame": 0.9166666666666666}, "value", 6, 19], [{"frame": 0.9583333333333334}, "value", 6, 20], [{"frame": 1}, "value", 6, 21], [{"frame": 1.0416666666666667}, "value", 6, 22], [{"frame": 1.0833333333333333}, "value", 6, 23], [{"frame": 1.125}, "value", 6, 24], [{"frame": 1.1666666666666667}, "value", 6, 25], [{"frame": 1.2083333333333333}, "value", 6, 26], [{"frame": 1.25}, "value", 6, 27], [{"frame": 1.2916666666666667}, "value", 6, 28], [{"frame": 1.3333333333333333}, "value", 6, 29], [{"frame": 1.375}, "value", 6, 30], [{"frame": 1.4166666666666667}, "value", 6, 31], [{"frame": 1.4583333333333333}, "value", 6, 32], [{"frame": 1.5}, "value", 6, 33], [{"frame": 1.5416666666666667}, "value", 6, 34], [{"frame": 1.5833333333333333}, "value", 6, 35], [{"frame": 1.625}, "value", 6, 36], [{"frame": 1.6666666666666667}, "value", 6, 37], [{"frame": 1.7083333333333333}, "value", 6, 38], [{"frame": 1.75}, "value", 6, 39], [{"frame": 1.7916666666666667}, "value", 6, 40], [{"frame": 1.8333333333333333}, "value", 6, 41], [{"frame": 1.875}, "value", 6, 42], [{"frame": 1.9166666666666667}, "value", 6, 43], [{"frame": 1.9583333333333333}, "value", 6, 44], [{"frame": 2}, "value", 6, 45], [{"frame": 2.0416666666666665}, "value", 6, 46], [{"frame": 2.0833333333333335}, "value", 6, 47], [{"frame": 2.125}, "value", 6, 48], [{"frame": 2.1666666666666665}, "value", 6, 49], [{"frame": 2.2083333333333335}, "value", 6, 50], [{"frame": 2.25}, "value", 6, 51], [{"frame": 2.2916666666666665}, "value", 6, 52], [{"frame": 2.3333333333333335}, "value", 6, 53], [{"frame": 2.375}, "value", 6, 54], [{"frame": 2.4166666666666665}, "value", 6, 55], [{"frame": 2.4583333333333335}, "value", 6, 56], [{"frame": 2.5}, "value", 6, 57], [{"frame": 2.5416666666666665}, "value", 6, 58], [{"frame": 2.625}, "value", 6, 59], [{"frame": 2.7083333333333335}, "value", 6, 60], [{"frame": 2.7916666666666665}, "value", 6, 61], [{"frame": 2.875}, "value", 6, 62]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 4, 5, 6, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 2, 1, 0, 0, 1, 7, 8, 9, 10, 11]]