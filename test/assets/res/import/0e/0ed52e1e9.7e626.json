[1, ["ecpdLyjvZBwrvm+cedCcQy", "edNhsVuepOqbJNBzzoVLuJ", "b2aHrECZ5APKGS/0d2hvT1", "b1DTyTTUFDBpwEnV06X95/", "7cAWByAEBGQqZhvYF4LQ0v", "7eHifjytVDKJdoAGQ0lwpc", "8etLBcXZFAorwchc6bRsp2", "d6K5DGMAFAFbG1uTntPpVZ", "fcaKq6gvlPlYhyZzrd3fuI", "dfk52J5mxFDJYlmEAK84xY", "a5hJI5OtNB0Yq0rp/qEfl/", "a17y/JnFdBio9pa+2aeg5/"], ["node", "_spriteFrame", "_file", "root", "data", "_mesh", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_is3DNode", "_prefab", "_components", "_parent", "_trs", "_children", "_contentSize", "_eulerAngles", "_anchorPoint", "_color"], 0, 4, 9, 1, 7, 2, 5, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "angle", "angleVar", "startSize", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "_dstBlendFactor", "endSizeVar", "rotationIsDir", "emitterMode", "radialAccel", "radialAccelVar", "startRadius", "endRadius", "rotatePerS", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "gravity", "_file", "_spriteFrame", "posVar"], -18, 1, 3, 8, 8, 8, 8, 5, 6, 6, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["node", "_materials", "_mesh"], 3, 1, 3, 6], ["cc.Mask", ["_type", "_N$alphaThreshold", "_N$inverted", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "curveData"], -1, 11]], [[2, 0, 1, 2, 2], [0, 0, 5, 4, 3, 11, 8, 6, 2], [1, 3, 4, 5, 1], [0, 0, 5, 4, 3, 8, 6, 2], [4, 0, 2], [0, 0, 1, 7, 4, 3, 3], [0, 0, 5, 7, 3, 6, 2], [0, 0, 2, 5, 7, 4, 3, 6, 9, 3], [0, 0, 5, 7, 4, 3, 8, 10, 6, 2], [0, 0, 5, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 9, 2], [5, 0, 1, 2, 3, 2], [2, 1, 2, 1], [6, 0, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 4], [1, 0, 1, 3, 4, 5, 3], [1, 2, 0, 1, 3, 4, 5, 4], [3, 12, 0, 1, 2, 3, 4, 5, 6, 7, 13, 8, 9, 10, 11, 14, 21, 22, 23, 24, 25, 26, 27, 28, 29, 16], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 15, 9, 10, 11, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 30, 27, 28, 29, 19], [8, 0, 1, 2, 3, 4, 5]], [[[[4, "buff_defense_up"], [5, "buff_defense_up", 0, [-3], [[11, true, -2, [21], 20]], [12, -1, 0]], [6, "02", 1, [-4, -5, -6, -7, -8, -9, -10], [0, "c6RMlG/VBOS5UBzyoCMYR7", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [7, "Plane", true, 2, [-12], [[13, -11, [18], 19]], [0, "88B6sg3sRJIrQ76hj2DvFT", 1, 0], [0, -10, 0, -0.49999999999999994, 0, 0, 0.8660254037844387, 1, 1, 1], [1, -60, 0, 0]], [8, "img_mask_001", 3, [-14], [[14, 2, 0.4, true, -13, [16], 17]], [0, "432ouE2KtNoaOho+0Gomdp", 1, 0], [5, 80, 140], [0, 0.5, 0.1], [0.5, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_ty_buff_defense_up_burst_1", 2, [[2, -15, [0], 1]], [0, "811F8uJ6ZEU7862oJol5YY", 1, 0], [4, 4285720573], [5, 128, 128], [0, 90, 0, 0, 0, 0, 1, 0.7, 0.7, 0.7]], [1, "effect_ty_buff_defense_up_burst_2", 2, [[2, -16, [2], 3]], [0, "f9qawIp7pAZ7ToHV3CkCUc", 1, 0], [4, 4290312703], [5, 128, 128], [0, 90, 0, 0, 0, 0, 1, 0.7, 0.7, 0.7]], [1, "effect_ty_buff_defense_up_burst_3", 2, [[2, -17, [4], 5]], [0, "3328mofBpDvbNDCbgrCBpW", 1, 0], [4, 4284015871], [5, 102, 102], [0, 90, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "effect_ty_buff_defense_up_shield01", 2, [[15, 0, false, -18, [6], 7]], [0, "4ahUkr2PpDRopbQtX2Od/Y", 1, 0], [5, 128, 160], [0, 90, 0, 0, 0, 0, 1, 0.3, 0.3, 0.3]], [3, "effect_ty_buff_defense_up_shield02", 2, [[16, 1, 0, false, -19, [8], 9]], [0, "1aWaf6X7xP3biAN0qWYRC8", 1, 0], [5, 128, 160], [0, 90, 0, 0, 0, 0, 1, 0.25, 0.25, 0.25]], [9, "effect_ty_buff_defense_up_burst_2_1", 2, [[17, 1, true, 12, 100, 0.3, 360, 360, 30, 10, 7, 1, 300, 120, 0, true, -20, [10], [4, 3446073087], [4, 838860800], [4, 845145855], [4, 335544320], [0, 0.25, 0.8600000143051147], 11, 12]], [0, "c7au9D275OF5I8XLzpguln", 1, 0], [0, 90, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "effect_ty_buff_defense_up_loop", 4, [[18, true, 4, 0, 10, 0, 0, 85, 85, 1, 1, 0, 190.7899932861328, -10, -671.0499877929688, 65.79000091552734, 65, 65, 300, -21, [13], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], [0, 30, -30], [0, 0.25, 0.8600000143051147], 14, 15]], [0, "75LAABCn5GGqjm/HhJM64b", 1, 0], [-0.5, -20, 0, 0, 0, -0.49999999999999994, 0.8660254037844387, 1, 1, 1], [1, 0, 0, -60]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 3, 0, 0, 3, 0, -1, 4, 0, 0, 4, 0, -1, 11, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 4, 1, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 5, 6, -1], [0, 1, 0, 1, 0, 4, 0, 5, 0, 6, 0, 2, 7, 0, 2, 8, 0, 9, 10, 11, 3, 3]], [[[19, "buff_defense_up", 1, 30, 0.5, [{"props": {"opacity": [{"frame": 0, "value": 255}]}}, "paths", 11, [{"02/effect_ty_buff_defense_up_burst_2_1": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.13333333333333333, "value": 0}, {"frame": 0.16666666666666666, "value": 999}, {"frame": 0.2, "value": 0}]}}, "props": {"position": []}}, "02/Plane/img_mask_001/effect_ty_buff_defense_up_loop": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.03333333333333333, "value": 0, "curve": "constant"}, {"frame": 0.06666666666666667, "value": 3.5, "curve": "constant"}, {"frame": 1, "value": 0}]}}, "props": {}}}, "02/effect_ty_buff_defense_up_burst_1", 11, [{"comps": {}}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.03333333333333333, "value": 255}, {"frame": 0.1, "value": 255}, {"frame": 0.13333333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 0.03333333333333333}, "value", 8, [1, 0.5, 0.5, 1]], [{"frame": 0.06666666666666667}, "value", 8, [1, 0.7, 0.7, 1]], [{"frame": 0.1}, "value", 8, [1, 0.4, 0.4, 1]]], 11, 11, 11]]], "02/effect_ty_buff_defense_up_shield01", 11, [{"comps": {}}, "props", 11, [{"opacity": [{"frame": 0.1, "value": 0}, {"frame": 0.13333333333333333, "value": 255}, {"frame": 0.4, "value": 255}, {"frame": 0.5666666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 0.1}, "value", 8, [1, 0, 0, 1]], [{"frame": 0.13333333333333333}, "value", 8, [1, 0.3, 0.3, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 0.5, 0.5, 1]]], 11, 11, 11]]], "02/effect_ty_buff_defense_up_shield02", 11, [{"comps": {}}, "props", 11, [{"opacity": [{"frame": 0.1, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.4, "value": 255}, {"frame": 0.5, "value": 0}]}, "scale", 12, [[[{"frame": 0.1, "curve": "linear"}, "value", 8, [1, 0, 0, 1]], [{"frame": 0.13333333333333333, "curve": "linear"}, "value", 8, [1, 0.3, 0.3, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 0.75, 0.75, 1]], [{"frame": 0.23333333333333334}, "value", 8, [1, 0.8, 0.8, 1]], [{"frame": 0.5666666666666667}, "value", 8, [1, 0.85, 0.85, 1]]], 11, 11, 11, 11, 11]]], "02/effect_ty_buff_defense_up_burst_3", 11, [{"comps": {}}, "props", 11, [{"opacity": [{"frame": 0.1, "value": 0}, {"frame": 0.13333333333333333, "value": 255}, {"frame": 0.2, "value": 200}, {"frame": 0.26666666666666666, "value": 0}]}, "scale", 12, [[[{"frame": 0.13333333333333333}, "value", 8, [1, 0.4, 0.4, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1.7, 1.7, 1]], [{"frame": 0.26666666666666666}, "value", 8, [1, 1.75, 1.75, 1]]], 11, 11, 11]]], "02/effect_ty_buff_defense_up_burst_2", 11, [{"comps": {}}, "props", 11, [{"opacity": [{"frame": 0.1, "value": 0}, {"frame": 0.13333333333333333, "value": 255}, {"frame": 0.16666666666666666, "value": 200}, {"frame": 0.23333333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.13333333333333333}, "value", 8, [1, 0.3, 0.3, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.26666666666666666}, "value", 8, [1, 1.05, 1.05, 1]]], 11, 11, 11]]]]]]], 0, 0, [], [], []]]]