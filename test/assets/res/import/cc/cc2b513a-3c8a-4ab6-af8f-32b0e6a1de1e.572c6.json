[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "62m3Q1gh1MJoQLW78yorn0", "ee72ankwpNOad4S1tgMZah", "b6xC/ncU1Itbl4wEYeSYXu", "4bpDRT34JO9paXMsTwg2HE", "47LfXTNedBhJ5sf0G+5l7j", "a2MjXRFdtLlYQ5ouAFv/+R", "edBpl4kd1GfKbPrX1Sae8q", "1664gaAX9K9aqACShiUPWt", "747b50lbFKuIdF/7h0hfaO", "3cAZ/1aL5Ip7zo7Ao5FhIa", "d7+j+ehjRC9LYeM5K9p4Rr", "41Vie/mg5GPometkqSTNNT", "abtPC9pLlIjYeRLvk3Sw2N", "70AsAHVuVKa4FslIvsMndR", "4e8RwiG+tG271grWJCedH1", "4cTX2B4YNETrOVHftLOGBE", "27HHAiVlJD1pjMWPgI8C+2", "32E+g0MbNLx6fQJrrXQiDs", "6d9D+UTi1PdZw/2Cv3xAFq", "e5/fZATxlISoMz2b6W0H0z", "42nR4TEtlLvY8iS7+6QeeK"], ["node", "_spriteFrame", "_file", "_defaultClip", "root", "_parent", "_N$file", "content", "itemParent", "itemPrefab", "btnClose", "scrollview", "labCount", "icon", "base", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_eulerAngles", "_color"], 0, 4, 9, 1, 5, 7, 2, 5, 5], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "angleVar", "startSize", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "lifeVar", "_dstBlendFactor", "endSizeVar", "angle", "startSpin", "endSpin", "startSizeVar", "endSpinVar", "radialAccel", "radialAccelVar", "rotationIsDir", "startSpinVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "_file", "_spriteFrame", "gravity", "posVar"], -20, 1, 3, 8, 8, 8, 8, 6, 6, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_right", "_top", "_bottom", "node"], -4, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Mask", ["_N$alphaThreshold", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize"], 2, 1, 2, 2, 4, 5], ["3039cCGLWFIeqCPOGQnWhje", ["scaleMin", "scaleMax", "node"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials"], -5, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["22967F8GSFOlYA7yUgvMx2H", ["node", "base", "icon", "labCount", "effectNode"], 3, 1, 1, 1, 1, 1], ["94c60/olTBPuKlGGctoaHyd", ["node", "scrollview", "btnClose", "itemPrefab", "itemParent", "content"], 3, 1, 1, 1, 1, 1, 1]], [[5, 0, 1, 2, 2], [5, 0, 1, 2], [0, 0, 5, 4, 3, 7, 2], [2, 2, 0, 3, 4, 5, 3], [0, 0, 5, 4, 3, 7, 9, 2], [0, 0, 5, 4, 3, 10, 6, 7, 2], [2, 3, 4, 5, 1], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 7, 2], [0, 0, 5, 4, 3, 6, 2], [0, 0, 1, 5, 8, 3, 3], [0, 0, 1, 5, 4, 3, 6, 7, 9, 3], [2, 0, 3, 4, 5, 2], [4, 0, 1, 2, 7, 4], [4, 0, 7, 2], [6, 0, 1, 2, 3, 2], [13, 0, 1], [1, 12, 0, 1, 2, 3, 11, 14, 4, 5, 17, 6, 13, 15, 16, 7, 8, 9, 10, 23, 24, 25, 26, 27, 28, 32, 31, 29, 30, 19], [1, 12, 0, 1, 2, 3, 11, 4, 5, 17, 6, 13, 15, 16, 7, 8, 9, 10, 23, 24, 25, 26, 27, 28, 32, 31, 29, 30, 18], [1, 12, 0, 1, 2, 3, 11, 4, 5, 17, 6, 13, 7, 8, 9, 10, 23, 24, 25, 26, 27, 28, 32, 29, 30, 16], [8, 0, 2], [0, 0, 8, 4, 3, 6, 7, 2], [0, 0, 8, 4, 3, 6, 2], [0, 0, 2, 5, 8, 4, 3, 6, 7, 3], [0, 0, 2, 8, 4, 3, 6, 3], [0, 0, 1, 5, 4, 3, 6, 3], [0, 0, 5, 4, 3, 2], [3, 0, 1, 2, 3, 4, 7, 5, 3], [3, 0, 2, 3, 4, 5, 2], [3, 0, 2, 3, 4, 5, 6, 2], [3, 0, 2, 3, 4, 5, 8, 6, 2], [9, 0, 1, 2, 3, 4, 5, 2], [2, 1, 0, 3, 4, 5, 3], [2, 0, 3, 4, 2], [4, 0, 1, 7, 3], [4, 0, 3, 4, 5, 6, 1, 2, 7, 8], [10, 0, 1, 2, 3], [5, 1, 2, 1], [6, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 5], [7, 0, 2, 3, 2], [7, 1, 2, 3, 4, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [15, 0, 1, 2, 2], [1, 0, 1, 2, 3, 11, 14, 4, 5, 6, 15, 16, 18, 7, 8, 9, 10, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 19], [1, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 23, 24, 25, 26, 27, 28, 29, 30, 13], [1, 12, 0, 1, 2, 3, 11, 14, 4, 5, 6, 13, 15, 7, 8, 9, 10, 21, 23, 24, 25, 26, 27, 28, 31, 29, 30, 18], [1, 12, 0, 1, 2, 3, 11, 14, 4, 5, 6, 13, 22, 18, 7, 8, 9, 10, 23, 24, 25, 26, 27, 28, 31, 29, 30, 18], [16, 0, 1, 2, 3, 4, 1], [17, 0, 1, 2, 3, 4, 5, 1]], [[20, "reward<PERSON>iew"], [21, "reward<PERSON>iew", [-10, -11, -12, -13, -14], [[14, 45, -2], [50, -8, -7, -6, -5, -4, -3], [38, true, -9, [63]]], [37, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "effect_ty_congratulations", 0, 1, [-15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27], [0, "45FdwEK0BNMYM8Mkdmj0Vm", 1, 0]], [24, "ui_wuping<PERSON><PERSON>o", false, [-30], [[15, true, -29, [27], 26]], [1, "a3JJawjRpGwZb1RxevkEeX", -28], [5, 100, 100]], [7, "content", 1, [-33, -34, -35], [[14, 45, -31], [15, true, -32, [9], 8]], [0, "dcwYC38ABL0oy2i0sZkeDV", 1, 0], [5, 768, 1366]], [23, "item", false, 1, [-40, -41, -42, 3], [[49, -39, -38, -37, -36, 3]], [0, "ddktSuTFRPP7zwA7U4DjJ7", 1, 0], [5, 100, 100], [-189, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "mask", 180, 1, [[[12, 0, -43, [0], 1], [13, 45, 100, 100, -44], -45], 4, 4, 1], [0, "b9dAaEAolAYIju7iC2jumz", 1, 0], [4, 4278190080], [5, 768, 1366]], [22, "view", [-48], [[40, 0, -46, [10]], [13, 45, 478, 220, -47]], [0, "14v1yYe0JIjYog/GhgnNRo", 1, 0], [5, 500, 220]], [9, "content", 7, [[39, 1, 3, 26, 20, -49, [5, 500, 100]]], [0, "a4Qi1dU0RN9Kg2tDu+dPPc", 1, 0], [5, 500, 100]], [29, "icon", 5, [[[33, 0, -50, [13]], [35, 45, 18, 18, 18, 18, 84, 72, -51], -52], 4, 4, 1], [0, "d2eg+Bml1MOoWW7X5IiAKz", 1, 0], [5, 71.11111111111111, 71.11111111111111], [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [10, "effect", 230, 3, [-53, -54, -55], [1, "675h6eMm5Gmp3qUA1ZZG99", 3]], [7, "<PERSON><PERSON><PERSON>", 10, [-57, -58], [[41, 2, -56, [21], 22]], [1, "6bJmdNNQ1E46oGvL9hl+Te", 3], [5, 100, 100]], [9, "base", 4, [[32, 1, 0, -59, [4], 5], [34, 40, 562, -60]], [0, "0cLOgt5lpLzamUxn8abBfp", 1, 0], [5, 768, 350]], [31, "scrollview", 1, [7], [-61], [0, "ad1+eiXsZNiKhYJz4fgbcs", 1, 0], [5, 500, 220]], [28, "base", 5, [[[12, 0, -62, [11], 12], -63], 4, 1], [0, "20JNJAvYFCMqZAgiTwCEf3", 1, 0], [5, 100, 100]], [30, "count", 5, [[-64, [44, 2, -65, [4, 4278190080]]], 1, 4], [0, "1fXdLEClZDWaHBvAJQmUKo", 1, 0], [5, 80, 26], [0, 1, 0.5], [44.357, -30.049, 0, 0, 0, 0, 1, 1, 1, 1]], [36, 1, 1, 6], [8, "ui_title_gonxihuode_di", 4, [[6, -66, [2], 3]], [0, "46NU1qNchG1KPoLfR1CO1V", 1, 0], [5, 389, 389], [0, 169.841, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "title", 4, [[6, -67, [6], 7]], [0, "c8IUIos5hMeqMjwTFMVi98", 1, 0], [5, 644, 150], [0, 218.116, 0, 0, 0, 0, 1, 1, 1, 1]], [42, false, 0.75, 0.23, null, null, 13, 8], [16, 14], [16, 9], [43, "99", 25, 0, false, -12, 2, 1, 2, 15, [14]], [25, "ui_itemeffet", 240, 10, [[3, 1, 0, -68, [15], 16]], [1, "4b1p46R9dMb7ZOPEn8HArk", 3], [5, 110, 110]], [11, "ui_itemeffect_saoguang01", 220, 11, [[6, -69, [17], 18]], [1, "b2687+lV1GMpWqFj0fLN4U", 3], [5, 128, 128], [-100, 100, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 0.7, 2, 1], [1, 0, 0, -45]], [11, "ui_itemeffect_saoguang02", 220, 11, [[6, -70, [19], 20]], [1, "99CMgyo2VC/IXcOzF+PNDQ", 3], [5, 128, 128], [-100, 100, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 0.7, 2, 1], [1, 0, 0, -45]], [26, "xingxing", 10, [[45, true, 20, 999, 0.5, 0.2, -90, 360, 30, 30, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 200, 10, -62.1100006, -300, 10, -71, [23], [4, 4294704895], [4, 838860800], [4, 2533359615], [4, 335544320], 24, 25]], [1, "d2mmQCBQxF574QHf7I+h/3", 3]], [2, "effect_ty_congratulations_ring", 2, [[46, 1, true, 1, 4, 0.2, 360, 100, 600, 1, 0, 0, 0, -72, [28], [4, **********], [4, 0], [4, 55295], [4, 0], 29, 30]], [0, "6a4dRp2htK2Yb9jnZP2Ivg", 1, 0], [0, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "effect_ty_congratulations_lizi_4", 2, [[17, 1, true, 3, 300, 0.45, 0.05, 180, 30, 80, 10, 60, 7, 120, 10, 1, 1020, 70, 0, -73, [31], [4, **********], [4, 822083584], [4, 822138879], [4, 167772160], [0, 50, 0], [0, 0, -2600], 32, 33]], [0, "2c4auZz9pD4avAtIX5Q6zk", 1, 0], [0, 194, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [4, "effect_ty_congratulations_lizi_3", 2, [[18, 1, true, 3, 300, 0.45, 0.05, 30, 80, 10, 60, 7, 120, 10, 1, 1020, 70, 0, -74, [34], [4, **********], [4, 822083584], [4, 822138879], [4, 167772160], [0, 0, 50], [0, -2600, 0], 35, 36]], [0, "abIyGnPixE741wO83mPgD2", 1, 0], [-30, 224, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [4, "effect_ty_congratulations_lizi_2", 2, [[18, 1, true, 3, 300, 0.45, 0.05, 30, 80, 10, 60, 7, 120, 10, 1, 1020, 70, 0, -75, [37], [4, **********], [4, 822083584], [4, 822138879], [4, 167772160], [0, 0, 50], [0, 2600, 0], 38, 39]], [0, "11gCdZSlhDJJNrZ/Q8Ci9j", 1, 0], [30, 224, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [4, "effect_ty_congratulations_lizi_1", 2, [[17, 1, true, 3, 300, 0.45, 0.05, 0, 30, 80, 10, 60, 7, 120, 10, 1, 1020, 70, 0, -76, [40], [4, **********], [4, 822083584], [4, 822138879], [4, 167772160], [0, 50, 0], [0, 0, 2600], 41, 42]], [0, "camIA72DRGkIMzdq6bXzic", 1, 0], [0, 254, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [2, "effect_ty_congratulations_lizi_5", 2, [[19, 1, true, 10, 20, 0.5, 0.2, 0, 45, 5, 80, 20, 1, 100, 0, 0, -77, [43], [4, **********], [4, 167772160], [4, 503371775], [4, 167772160], [0, 200, 60], 44, 45]], [0, "edZ5zusx5L0JGsug0rvHg6", 1, 0], [0, 186.656, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_ty_congratulations_lizi_6", 2, [[19, 1, true, 10, 20, 0.5, 0.2, 0, 45, 5, 110, 20, 1, 100, 0, 0, -78, [46], [4, **********], [4, 167772160], [4, 503371775], [4, 167772160], [0, 200, 60], 47, 48]], [0, "faGTfCOgRE15yJZ1+pQGvF", 1, 0], [0, 186.656, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_ty_congratulations_lizi_7", 2, [[47, 1, true, 30, 100, 0.2, 0.1, 360, 360, 20, 20, 10, 5, 1, 1000, 300, 0, true, -79, [49], [4, **********], [4, 0], [4, 838916095], [4, 0], [0, 0.25, 0.8600000143051147], 50, 51]], [0, "0eHa1WyJZMiKAAz1/eszRt", 1, 0], [0, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_ty_congratulations_pice", 2, [[48, 1, true, 20, 100, 0.3, 0.1, 360, 360, 20, 10, 7, 30, 60, 1, 500, 200, 0, -80, [52], [4, **********], [4, 0], [4, 838916095], [4, 0], [0, 0.25, 0.8600000143051147], 53, 54]], [0, "94DwhbQkhExbhcjhNqfp0y", 1, 0], [0, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "effect_ty_congratulations_glow_1", 2, [[3, 1, 0, -81, [55], 56]], [0, "6031r6H8xMOKUwfJJRrQeG", 1, 0], [4, 4281196031], [5, 220, 220], [0, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "effect_ty_congratulations_light_1", 2, [[3, 1, 0, -82, [57], 58]], [0, "f2vfV5tVFKPaAdIptCtPDa", 1, 0], [4, 4286639103], [5, 440, 440], [0, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "effect_ty_congratulations_light_2", 2, [[3, 1, 0, -83, [59], 60]], [0, "a16/FkGYFMa6ds999CAfXJ", 1, 0], [4, 4287557119], [5, 440, 440], [0, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "effect_ty_congratulations_glow_2", 2, [[3, 1, 0, -84, [61], 62]], [0, "64LKu8GyFFGLGcInHNseuP", 1, 0], [4, 4286901503], [5, 220, 220], [0, 224, 0, 0, 0, 0, 1, 2, 2, 2]]], 0, [0, 4, 1, 0, 0, 1, 0, 7, 4, 0, 8, 8, 0, 9, 5, 0, 10, 16, 0, 11, 19, 0, 0, 1, 0, 0, 1, 0, -1, 6, 0, -2, 4, 0, -3, 13, 0, -4, 5, 0, -5, 2, 0, -1, 27, 0, -2, 28, 0, -3, 29, 0, -4, 30, 0, -5, 31, 0, -6, 32, 0, -7, 33, 0, -8, 34, 0, -9, 35, 0, -10, 36, 0, -11, 37, 0, -12, 38, 0, -13, 39, 0, 4, 3, 0, 0, 3, 0, -1, 10, 0, 0, 4, 0, 0, 4, 0, -1, 17, 0, -2, 12, 0, -3, 18, 0, 12, 22, 0, 13, 21, 0, 14, 20, 0, 0, 5, 0, -1, 14, 0, -2, 9, 0, -3, 15, 0, 0, 6, 0, 0, 6, 0, -3, 16, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -3, 21, 0, -1, 23, 0, -2, 11, 0, -3, 26, 0, 0, 11, 0, -1, 24, 0, -2, 25, 0, 0, 12, 0, 0, 12, 0, -1, 19, 0, 0, 14, 0, -2, 20, 0, -1, 22, 0, 0, 15, 0, 0, 17, 0, 0, 18, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 15, 1, 3, 5, 5, 7, 5, 13, 84], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22], [-1, 1, -1, 1, -1, 1, -1, 1, 3, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, 3, -1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 6], [0, 7, 0, 8, 0, 9, 0, 10, 3, 3, 0, 0, 11, 0, 0, 0, 12, 0, 4, 0, 4, 0, 13, 0, 1, 14, 5, 5, 0, 1, 15, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 6, 0, 1, 6, 0, 1, 16, 0, 1, 17, 0, 18, 0, 19, 0, 20, 0, 21, 3, 22]]