[1, ["ecpdLyjvZBwrvm+cedCcQy", "e1Lk2iaehNhqbZt8ifKhCs"], ["node", "_spriteFrame", "root", "rewardContent", "rank", "icon", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_children", "_parent"], 2, 9, 4, 5, 7, 2, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 1, 1, 5], ["cc.Widget", ["_alignFlags", "_left", "_right", "_originalWidth", "node"], -1, 1], ["cc.Label", ["_string", "_fontSize", "node", "_materials"], 1, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["b60f8PptNpCX5LHxbp3vZRa", ["node", "icon", "rank", "rewardContent"], 3, 1, 1, 1, 1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 5, 1, 2, 3, 4, 2], [0, 0, 6, 1, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1], [1, 1, 2, 1], [6, 0, 1, 2, 3, 3], [7, 0, 1, 2, 3, 4, 5], [8, 0, 1, 2, 3, 3], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 3], [11, 0, 1, 2, 3, 1]], [[1, "openServerRankAwardItem"], [2, "openServerRankAwardItem", [-7, -8, -9], [[12, 1, 0, -2, [1], 2], [13, -6, -5, -4, -3]], [7, -1, 0], [5, 737, 125], [-2, -62.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "content", 1, [[8, 1, 20, -10, [5, 468.624, 100]], [9, 40, 168.376, 100, 300, -11]], [0, "67si+FgZlKSJB55Et3BE/d", 1, 0], [5, 468.624, 100], [34.18800000000002, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "rank", 1, [[-12, [11, 2, -13, [4, 4282328861]]], 1, 4], [0, "9cHI7IcMhJlKLHlYtdPon+", 1, 0], [5, 58.92, 54.4], [-287, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "imgflag", 1, [-14], [0, "17ggBZN1lAZJMvHVHPb1aJ", 1, 0], [5, 53, 61], [-289.659, 0, 0, 0, 0, 0, 1, 1, 1, 0.8]], [6, 4], [10, "4-5", 38, 3, [0]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 2, 0, 4, 6, 0, 5, 5, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 3, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, 0, 3, 0, -1, 5, 0, 6, 1, 14], [0, 0, 0], [-1, -1, 1], [0, 0, 1]]