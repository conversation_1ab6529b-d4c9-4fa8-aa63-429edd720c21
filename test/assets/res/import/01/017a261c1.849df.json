[1, ["ac5bB53tZIJJktPD1Vd0Xa", "6aend3qLxFmrTAKptLyvRK", "41cHR8rcdHqoK9HXn0o7Ug", "bbOKoyOa1N7bVa19f9nzFz", "35ScFGKIJOeYAN5YRyX0ed", "c4n8PvwFJCibGNooz4NYDQ", "72ONjbOEBNy7oiYagXRX0r", "69ESVFlmtOMJvKFMls8p2g", "a1V7sN36ZParwCly9B42OY", "01RtNtevdDGqLUXD4kY7AA", "62jonG5slAdqiLzSyzy7c6", "d2+VR4mKNFjZolxBYOdzyV", "5a40W4Lo5L544/rGEnQTbc", "06ZzwJg7NLuqlLel2maNnr", "a0twvoMy5Au4lnqQWDom9s", "daSe/IPNdLT7jruXXfFSiN", "4cN6TzZ/9EN6CdK+7POHRx", "04Kp5z5sJGvY42oCuMx1X2", "4eQA+AIqdB8Y0HQJrgnuRR", "7fwy6VM1JKMLD1f2axlMkO", "98t7kDxMZBupP5dy6NCYAG", "19IAzYYbtFDZ5NKNl35awG", "85/UztsV5MjqYHkxP0219t", "feb1HXyqxPlI6Vjaqoumhl", "c5NgyWuwNFWrVuIHwHJfg/", "f07voHKpFGJ7olgpebrNsa", "11CHJ1679COKayC9pj+ivC", "89q2zbqXJE/afhMwObyA3t", "55KRhLDgZH+Y0bTXA26Lth", "92gZqfOaJIbrKaDHQkxLID", "82CN2imIhOP6kSwJGq2Hdv", "fbJeRQK29HdJZKgpeDmj2m", "015QOhTcRLUYJs4l0uZCIj", "59pSsp9OVEGK3jAR1oJkLb", "2ekNCAxAdIDK+GO5M93OE7", "64L5eqlSNN8o4TXiUMKdcN", "f931qn1hNKwJC6MvWw8/Nk", "6epK9SDwBLa5LaPTIbU7Bb", "0fVb6c4GtNsZYyRxFPx9xb", "c7fAF4R+pLJqC84VHFJ7ho", "6f1hdzYSZAwKOOGdLOopMp", "95lTioVktDp4X8m2SeXIoQ", "6cubZ/7M1EHI7fPV1kboDY", "37wQBPXOVK1aXvmmfJvVhO", "a4eSQOlmNNq4CeNfElF6nM", "d9g4zZZAdFj5yTEn1xZscH", "b0+4r5RGlPnoyeXhQG3131", "6axrmy5bJIt4qOUjIXpVDS"], ["_textureSetter", "ssbq_attack_01", "ssbq_attack_02", "ssbq_attack_03", "ssbq_attack_04", "ssbq_attack_05", "ssbq_attack_06", "ssbq_attack_07", "ssbq_attack_08", "ssbq_die", "ssbq_gather_01", "ssbq_gather_02", "ssbq_gather_03", "ssbq_gather_04", "ssbq_gather_05", "ssbq_gather_06", "ssbq_gather_07", "ssbq_gather_08", "ssbq_gather_09", "ssbq_idle_01", "ssbq_idle_02", "ssbq_idle_03", "ssbq_idle_04", "ssbq_move_01", "ssbq_move_02", "ssbq_move_03", "ssbq_move_04", "ssbq_move_05", "ssbq_move_06", "ssbq_skill2_01", "ssbq_skill2_02", "ssbq_skill2_03", "ssbq_skill2_04", "ssbq_skill2_05", "ssbq_skill_01", "ssbq_skill_02", "ssbq_skill_03", "ssbq_skill_04", "ssbq_skill_05", "ssbq_skill_06", "ssbq_skill_07", "ssbq_skill_08", "ssbq_skill_09", "ssbq_skill_10", "ssbq_skill_11", "ssbq_skill_12", "ssbq_skill_13", "ssbq_skill_14"], ["cc.SpriteFrame", ["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[1, 0, 1, 2]], [[[{"name": "ssbq_skill_01", "rect": [744, 0, 138, 93], "offset": [-18, -18], "originalSize": [176, 155], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "1040007_ssbq.plist", [{}, "ssbq_attack_01", 6, 0, "ssbq_attack_02", 6, 1, "ssbq_attack_03", 6, 2, "ssbq_attack_04", 6, 3, "ssbq_attack_05", 6, 4, "ssbq_attack_06", 6, 5, "ssbq_attack_07", 6, 6, "ssbq_attack_08", 6, 7, "ssbq_die", 6, 8, "ssbq_gather_01", 6, 9, "ssbq_gather_02", 6, 10, "ssbq_gather_03", 6, 11, "ssbq_gather_04", 6, 12, "ssbq_gather_05", 6, 13, "ssbq_gather_06", 6, 14, "ssbq_gather_07", 6, 15, "ssbq_gather_08", 6, 16, "ssbq_gather_09", 6, 17, "ssbq_idle_01", 6, 18, "ssbq_idle_02", 6, 19, "ssbq_idle_03", 6, 20, "ssbq_idle_04", 6, 21, "ssbq_move_01", 6, 22, "ssbq_move_02", 6, 23, "ssbq_move_03", 6, 24, "ssbq_move_04", 6, 25, "ssbq_move_05", 6, 26, "ssbq_move_06", 6, 27, "ssbq_skill2_01", 6, 28, "ssbq_skill2_02", 6, 29, "ssbq_skill2_03", 6, 30, "ssbq_skill2_04", 6, 31, "ssbq_skill2_05", 6, 32, "ssbq_skill_01", 6, 33, "ssbq_skill_02", 6, 34, "ssbq_skill_03", 6, 35, "ssbq_skill_04", 6, 36, "ssbq_skill_05", 6, 37, "ssbq_skill_06", 6, 38, "ssbq_skill_07", 6, 39, "ssbq_skill_08", 6, 40, "ssbq_skill_09", 6, 41, "ssbq_skill_10", 6, 42, "ssbq_skill_11", 6, 43, "ssbq_skill_12", 6, 44, "ssbq_skill_13", 6, 45, "ssbq_skill_14", 6, 46]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47]]]]