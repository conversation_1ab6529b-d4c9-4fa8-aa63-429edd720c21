[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "e0tthesi5E+4cxVUkC74Xx", "acCsRE7BxFla+tK0jwKgap", "228oOqm3dCIoTAyowWjrYW", "47LfXTNedBhJ5sf0G+5l7j"], ["node", "_file", "_spriteFrame", "_textureSetter", "root", "data", "_defaultClip"], [["cc.Node", ["_name", "_components", "_prefab", "_children", "_parent", "_trs"], 2, 9, 4, 2, 1, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "_stopped", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "startSpin", "endSpin", "endSpinVar", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccel", "tangentialAccelVar", "radialAccelVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -19, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], "cc.SpriteFrame", ["cc.AnimationClip", ["_name", "sample", "speed", "wrapMode", "curveData"], -2], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [0, 0, 4, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 21], [4, 0, 1, 2, 3, 4, 6], [5, 0, 2], [0, 0, 3, 1, 2, 2], [0, 0, 4, 3, 1, 2, 5, 2], [6, 0, 1, 2, 3, 2], [1, 1, 2, 1], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 23]], [[[[3, "buff_cold", 24, 0.5, 2, {"paths": {"ice/buff_cold": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 20}]}}}, "ice/buff_cold2": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 20}]}}}}}]], 0, 0, [], [], []], [[{"name": "buff_ice", "rect": [0, 0, 54, 62], "offset": [0, 0], "originalSize": [54, 62], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [4]], [[[4, "buff_cold"], [5, "buff_cold", [-3], [[7, true, -2, [10], 9]], [8, -1, 0]], [6, "ice", 1, [-5, -6], [[9, 1, true, false, 200, 0, 0.20000000298023224, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -4, [6], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 7, 8]], [0, "d29nPlX35O5I7bA0sOj9b+", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [1, "buff_cold", 2, [[2, 1, true, false, 3, 0, 1.5, 0.2, 360, 360, 80, 10, 40.3199997, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 140, 20, -62.1100006, -71.0499878, -7, [0], [4, 4294956154], [4, 838860800], [4, 16766874], [4, 335544320], [0, 20, 20], [0, 0.25, 0.8600000143051147], 1, 2]], [0, "69CF4PJYJKDLm8369ja3X1", 1, 0]], [1, "buff_cold2", 2, [[2, 1, true, false, 3, 0, 1.5, 0.2, 360, 360, 80, 10, 40.3199997, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 140, 20, -62.1100006, -71.0499878, -8, [3], [4, 4294963927], [4, 838860800], [4, 16775922], [4, 335544320], [0, 20, 20], [0, 0.25, 0.8600000143051147], 4, 5]], [0, "73QwvFSKtPHqsR6vqlqbDR", 1, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, 0, 3, 0, 0, 4, 0, 5, 1, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, 2, -1, 1, 2, -1, 1, 2, 6, -1], [0, 1, 2, 0, 1, 2, 0, 1, 5, 3, 3]]]]