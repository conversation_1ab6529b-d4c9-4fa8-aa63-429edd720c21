[1, ["ecpdLyjvZBwrvm+cedCcQy", "42nR4TEtlLvY8iS7+6QeeK"], ["_N$file", "root", "node", "data"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_color", "_contentSize"], 2, 9, 4, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_spacingX", "_N$horizontalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.PrefabInfo", ["root", "asset"], 3, 1, 1]], [[0, 0, 2], [1, 0, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [3, 0, 1, 1]], [[0, "dropNum"], [1, "node", [[2, "1", 28, 20, false, false, -12, 1, 2, -2, [0], 1]], [3, -1, 0], [4, 4278255406], [5, 80, 18]]], 0, [0, 1, 1, 0, 2, 1, 0, 3, 1, 2], [0, 0], [-1, 0], [0, 1]]