[1, ["21TUECiGhFG6csbQW1F0rO", "1aiTtPpwdAAYLMeTd1ns0J", "e8JoNO2WBENoUI7+DBDtC1", "03l5abMg9JNL9COqjZLm/n", "e5kQyc0n9OfJ0oA1kcW6fc", "5ekHnmN3BLGbywAnm5lXgg", "a7WqSpQl9HuI6OrSxpIIqE", "61SHEOwiJL14zAnh6EhJnQ", "04HZM7zmBPiIY8/V2RVpkg", "9d8c5BUJhKmYdwJiQTxG6T", "e70j2XVvZOvrW4Kwvpj/OE", "26tjvekv1NmqcmrxVb8kFH", "23X/9yRKRI2bVHlRShzvmv", "fcXvJF6X9Id52kYhooa9XJ", "60jJ9UyWdJ3bbAli8rZzc9", "d4LBCid5ZIkJ7kgFB8hHxh", "1d5YuFKiVFnIDjJfh6nh86", "0cQi6dOGFNG6pWRR3U2tEb", "37/COZ6TpOKIHJD5mJYRTW", "0b0URHPbxG5YTT60zx024N", "2cl/sVfEhMuItX+C6gbCml", "31UH8hHlpLYY9T1qndm67X", "e8UX//AlBAPovKsnzZInXY", "dek0rcz/lM9IkVLTgebAN6", "adW2nGdeNHQKK127K8G5i4", "e6nkRioDFDxrnuvHRfcezs", "dcHKb9bDNHaqa8HKj0kvEA", "a3OV9NztxLW5R8izqFXBPa", "86LyE/9mVDtLYwhWs/eqtM", "a5FgJgtAFNtpWYDyy/ZpON", "0cer7E3GVPIrw+X6QeBfif", "e2maPFELlKtoOycFrmNXkp", "66yLhl+G9G6b6vWhDSqie3", "8bzYgizQdJkpX1Y2/onypo", "84izd3KENDNrFmAusaybca"], ["bwc_attack_01", "bwc_attack_02", "bwc_attack_03", "bwc_attack_04", "bwc_attack_05", "bwc_attack_06", "bwc_attack_07", "bwc_attack_08", "bwc_attack_09", "bwc_attack_10", "bwc_attack_11", "bwc_attack_12", "bwc_attack_13", "bwc_die", "bwc_gather_01", "bwc_gather_02", "bwc_gather_03", "bwc_gather_04", "bwc_gather_05", "bwc_gather_06", "bwc_gather_07", "bwc_gather_08", "bwc_gather_09", "bwc_idle_01", "bwc_idle_02", "bwc_idle_03", "bwc_idle_04", "bwc_move_01", "bwc_move_02", "bwc_move_03", "bwc_move_04", "bwc_move_05", "bwc_move_06", "bwc_move_07", "bwc_move_08"], [["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[0, 0, 1, 2]], [[0, "1020003_bwc.plist", [{}, "bwc_attack_01", 6, 0, "bwc_attack_02", 6, 1, "bwc_attack_03", 6, 2, "bwc_attack_04", 6, 3, "bwc_attack_05", 6, 4, "bwc_attack_06", 6, 5, "bwc_attack_07", 6, 6, "bwc_attack_08", 6, 7, "bwc_attack_09", 6, 8, "bwc_attack_10", 6, 9, "bwc_attack_11", 6, 10, "bwc_attack_12", 6, 11, "bwc_attack_13", 6, 12, "bwc_die", 6, 13, "bwc_gather_01", 6, 14, "bwc_gather_02", 6, 15, "bwc_gather_03", 6, 16, "bwc_gather_04", 6, 17, "bwc_gather_05", 6, 18, "bwc_gather_06", 6, 19, "bwc_gather_07", 6, 20, "bwc_gather_08", 6, 21, "bwc_gather_09", 6, 22, "bwc_idle_01", 6, 23, "bwc_idle_02", 6, 24, "bwc_idle_03", 6, 25, "bwc_idle_04", 6, 26, "bwc_move_01", 6, 27, "bwc_move_02", 6, 28, "bwc_move_03", 6, 29, "bwc_move_04", 6, 30, "bwc_move_05", 6, 31, "bwc_move_06", 6, 32, "bwc_move_07", 6, 33, "bwc_move_08", 6, 34]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34]]