[1, ["165KmSpBpLkZDfti2qJMjN", "63xfrQ4BZNIKkM1nzmPeiO", "56vMm5dxJE+JzfrRDHyH8A", "adzyDzJLtNKrAO1IPFk8bI", "be21/tDG5DW4R1WSNab8Fe", "0bfh+5D8VCeoYqOPqvd67H", "92yRzuYLNPE4IgrTMzxsl2", "f3F/CegSJIq5iJHq+oil/x", "59PnKXhClPzaFUNjK/908z", "1dzCNlwyRAQLqlWpON378q", "77pxIfKeZBsJzK9/badByA", "39qwxzxhtLbpKEeOsLuMRy", "9cnUtCRHxPo7fcEyggTpqF"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "cuttrees", 0.375, 24, 0.5, 2, [{"props": {"y": [{"frame": 0, "value": -12.5}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.041666666666666664}, "value", 6, 1], [{"frame": 0.08333333333333333}, "value", 6, 2], [{"frame": 0.125}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4], [{"frame": 0.20833333333333334}, "value", 6, 5], [{"frame": 0.25}, "value", 6, 6], [{"frame": 0.2916666666666667}, "value", 6, 7], [{"frame": 0.3333333333333333}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "paths", 11, [{}, "futou", 11, [{"props": {"active": [{"frame": 0, "value": true}], "position": [{"frame": 0, "curve": "constant", "value": [19.286, 22.618, 0]}, {"frame": 0.041666666666666664, "curve": "constant", "value": [37.27, 61.564, 0]}, {"frame": 0.08333333333333333, "curve": "constant", "value": [63.458, 73.745, 0]}, {"frame": 0.125, "curve": "constant", "value": [75.403, 63.132, 0]}, {"frame": 0.16666666666666666, "curve": "constant", "value": [77.337, 55.654, 0]}, {"frame": 0.20833333333333334, "curve": "constant", "value": [12.85, 96.164, 0]}, {"frame": 0.25, "curve": "constant", "value": [-12.096, 25.629, 0]}, {"frame": 0.2916666666666667, "curve": "constant", "value": [-10.098, 19.574, 0]}, {"frame": 0.3333333333333333, "value": [-2.632, 22.091, 0]}], "angle": [{"frame": 0, "value": 95.916, "curve": "constant"}, {"frame": 0.041666666666666664, "value": -10, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -74.961, "curve": "constant"}, {"frame": 0.125, "value": -111.61, "curve": "constant"}, {"frame": 0.16666666666666666, "value": -122.782, "curve": "constant"}, {"frame": 0.20833333333333334, "value": -6.176000000000002, "curve": "constant"}, {"frame": 0.25, "value": 88.027, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 97.942, "curve": "constant"}, {"frame": 0.3333333333333333, "value": 94.92000000000002}], "anchorX": [{"frame": 0, "value": 0.5}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 9]], 11]]]], "futou/shou", 11, [{"props": {"position": [{"frame": 0, "value": [24.626, -26.576, 0]}], "active": [{"frame": 0, "value": true}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 10]], 11]]]], "futou/wei", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.25, "value": 255, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0.20833333333333334, "value": 12.253, "curve": "constant"}, {"frame": 0.25, "value": -87.206}], "position": [{"frame": 0.20833333333333334, "curve": "constant", "value": [82.587, 8.032, 0]}, {"frame": 0.25, "value": [63.418, 18.76, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334, "curve": "constant"}, "value", 8, [1, 1.7, 1.7, 1]], [{"frame": 0.25}, "value", 8, [1, 1.916, 2.327, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 11], [{"frame": 0.25}, "value", 6, 12]], 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]