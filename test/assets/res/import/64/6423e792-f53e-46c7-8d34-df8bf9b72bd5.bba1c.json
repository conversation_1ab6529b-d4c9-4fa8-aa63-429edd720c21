[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "cfAKGH+bZIraoW9Zs/2WAX", "f16/lDriJPp5C5SNCkJwGl"], ["node", "_spriteFrame", "_file", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_prefab", "_components", "_parent", "_children", "_trs", "_contentSize", "_eulerAngles"], 1, 4, 9, 1, 2, 7, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "speed", "speedVar", "tangentialAccel", "rotationIsDir", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -11, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.RigidBody", ["_allowSleep", "_gravityScale", "enabledContactListener", "bullet", "node"], -1, 1], ["cc.PhysicsCircleCollider", ["_density", "_sensor", "_radius", "node"], 0, 1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 1, 5, 3, 2, 3], [0, 0, 1, 4, 5, 2, 3], [0, 0, 1, 4, 3, 2, 6, 3], [0, 0, 1, 4, 3, 2, 7, 6, 8, 3], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 15], [1, 1, 2, 1], [4, 0, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5], [6, 0, 1, 2, 3, 4]], [[1, "bullet_xwm_00"], [2, "bullet_xwm_00", 4, [-4], [[9, false, 0, true, true, -2], [10, 0, true, 25, -3]], [7, -1, 0]], [3, "Node", 4, 1, [-5, -6], [0, "a07nHtAAZOp6+BJyoS3gbS", 1, 0]], [4, "lizi", 4, 2, [[6, true, 50, 20, 0.25, 0.1, 0, 0, 17.5, 5, 5, 20, 0, 0, true, -7, [0], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], [0, 0, 12], [0, 0.25, 0.8600000143051147], 1, 2]], [0, "b4/yTA3tlP9bIa51oto8AF", 1, 0], [-1.854, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "effect_xwm_lianghua_002", 4, 2, [[8, -8, [3], 4]], [0, "26hMWiMaNB9Zdw0pj+54F4", 1, 0], [5, 110, 77], [0, 0, 0, 0, 0, 0.8401456206777456, 0.5423608909720591, 0.339, 0.339, 0.339], [1, 0, 0, 114.311]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, -2, 4, 0, 0, 3, 0, 0, 4, 0, 4, 1, 8], [0, 0, 0, 0, 0], [-1, 2, 1, -1, 1], [0, 1, 2, 0, 3]]