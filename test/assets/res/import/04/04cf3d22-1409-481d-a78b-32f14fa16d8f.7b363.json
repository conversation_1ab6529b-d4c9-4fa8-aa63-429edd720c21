[1, ["ecpdLyjvZBwrvm+cedCcQy", "41D7kWhyFGY7q4NDlzkazn", "a7RQJXq4RKgqXIc0XVquW5", "51hb9gFhBHeY6h3jGxeojN", "46JgCtRx9Bdb4UY9vCp7Rn", "190NQN59FP/bqe2flIrOYV", "9eoFabXbVK5LdU0blPxaoj", "a6XQlubZdDubHS4WYNI5ob", "d9QkjY3bdLG40v1vEWjgti", "4cACisC4dIuq5Xlul+wpRx", "d9dkwSdGNJFK8Jvz7lzizp", "42nR4TEtlLvY8iS7+6QeeK", "6cWIXyUw1KLLsW4sPliBHO"], ["node", "_spriteFrame", "_N$file", "root", "toggleC<PERSON><PERSON>", "todayShowToggle", "verticalLabel", "content", "<PERSON><PERSON><PERSON><PERSON>", "front<PERSON><PERSON><PERSON>", "costCount", "costIcon", "cost", "titleTxt", "cancelBtn", "okBtn", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color"], -1, 9, 4, 5, 1, 7, 2, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_children"], 0, 1, 2, 4, 5, 7, 5, 2], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_isSystemFontUsed", "_spacingX", "node", "_materials"], -4, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["cc.RichText", ["_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node"], -1, 1], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark"], 1, 1, 5, 1, 1], ["52bd9/3R4JGCqbMGvn1DEbL", ["node", "okBtn", "cancelBtn", "titleTxt", "cost", "costIcon", "costCount", "front<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "content", "verticalLabel", "todayShowToggle", "toggleC<PERSON><PERSON>"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[5, 0, 1, 2, 2], [2, 1, 0, 3, 4, 5, 3], [0, 0, 7, 4, 5, 6, 8, 2], [0, 0, 7, 9, 4, 5, 6, 8, 2], [2, 0, 3, 4, 5, 2], [13, 0, 1, 2, 3, 4, 4], [0, 0, 7, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 6, 2, 3, 4, 5, 2], [1, 0, 3, 4, 5, 6, 7, 2], [1, 0, 3, 4, 5, 8, 6, 7, 2], [2, 3, 4, 5, 1], [7, 0, 1, 2, 3, 4], [8, 0, 1], [3, 0, 1, 4, 2, 3, 7, 8, 6], [10, 0, 1], [11, 0, 1, 2, 3, 4, 5], [6, 0, 2], [0, 0, 9, 4, 5, 6, 8, 2], [0, 0, 1, 7, 4, 5, 10, 6, 3], [0, 0, 2, 7, 4, 5, 6, 8, 3], [0, 0, 3, 7, 4, 5, 6, 8, 3], [1, 0, 3, 9, 4, 5, 6, 7, 2], [1, 0, 1, 2, 3, 4, 5, 6, 7, 4], [2, 0, 2, 3, 4, 3], [5, 1, 2, 1], [3, 0, 1, 4, 5, 2, 3, 7, 8, 7], [3, 0, 1, 4, 5, 6, 2, 3, 7, 8, 8], [3, 0, 1, 2, 3, 7, 8, 5], [9, 0, 1, 2, 2], [12, 0, 1], [14, 0, 1, 2, 3, 4, 5, 3], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1]], [[17, "msgBox"], [18, "msgBoxView", [-17, -18, -19, -20, -21, -22, -23, -24, -25, -26], [[13, -2], [32, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3], [12, 45, 750, 1334, -16]], [25, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "content", 1, [-28, -29, -30], [[5, 1, 1, 1, -27, [5, 372.57, 40]]], [0, "34sm57rp9N/Lfdx9Mb+SJw", 1, 0], [5, 372.57, 40], [0, 88.963, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "cost", 2, [-32, -33], [[5, 1, 1, 1, -31, [5, 131, 40]]], [0, "2cxLBAVUVJMLrDhHXDLvon", 1, 0], [5, 131, 40], [-7.784999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "checkContainer", 1, [-35, -36], [[5, 1, 1, 20, -34, [5, 286, 50]]], [0, "e6HkTQabxP/54+oTBL3tGV", 1, 0], [5, 286, 50], [0, -4.899, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "heidi", 204, 1, [[4, 0, -37, [0], 1], [12, 45, 750, 2201, -38], [13, -39]], [0, "98vlHqEEJN2ZO/OZWG6RPY", 1, 0], [4, 4278190080], [5, 768, 1366]], [8, "btnCancel", 1, [-42], [[[1, 1, 0, -40, [9], 10], -41], 4, 1], [0, "f4dBJY6ZZBA7QZY9pssTes", 1, 0], [5, 217, 81], [-120.62100000000001, -88.541, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnOk", 1, [-45], [[[1, 1, 0, -43, [13], 14], -44], 4, 1], [0, "69oeDOSMFFiqgtQ2ygcbzC", 1, 0], [5, 217, 81], [123.136, -88.952, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "todayShowToggle", 4, [-47, -48], [-46], [0, "f3amYYKypHn50FZWhG2QJs", 1, 0], [5, 50, 50], [-118, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "title", 1, [[-49, [29, 3, -50, [4, 4281341443]]], 1, 4], [0, "31tUBBXeRPuK7WegzH6nET", 1, 0], [5, 73.97, 48.84], [0, 191.666, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "costIcon", 3, [[[4, 0, -51, [15], 16], -52], 4, 1], [0, "ccqujHcxhEpKOAJXt/cWtJ", 1, 0], [5, 38, 38], [-46.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "Background", 512, 8, [[1, 1, 0, -53, [20], 21]], [0, "0fMYcY90BDArejHsrSK8U/", 1, 0], [5, 40, 40], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "img_ldi", 1, [[1, 1, 0, -54, [2], 3]], [0, "98T66G7t5GxKVqAnHLBR1Z", 1, 0], [5, 533, 405], [0.5, 33.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ui_link_01", 1, [[4, 0, -55, [4], 5]], [0, "dbKrEuLKdNWbKrKPXmmNMp", 1, 0], [5, 510, 2], [0, -32.033, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "提示", 34, 34, false, 1, 1, 9, [6]], [6, "btn_title_quxiao", 6, [[11, -56, [7], 8]], [0, "01ERjmOWFPbItNy8zdvcHT", 1, 0], [5, 58, 26]], [15, 6], [6, "btn_title_queding2", 7, [[11, -57, [11], 12]], [0, "19XzPubHFD4a3E54uYGCaZ", 1, 0], [5, 65, 29]], [15, 7], [9, "front<PERSON><PERSON><PERSON>", 2, [-58], [0, "d8dS/s785G65M62sUBK7LT", 1, 0], [5, 112, 63], [-130.285, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "是否消耗", 1, 28, 50, 19], [30, 10], [10, "labCost", 3, [-59], [0, "18PMrrg7xKtJq1Q8asJ0ay", 1, 0], [4, 4284142327], [5, 92, 0], [19.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "12000", 28, 0, false, -12, 1, 1, 22, [17]], [10, "<PERSON><PERSON><PERSON><PERSON>", 2, [-60], [0, "03lhEyhxVB+rSXBsSnqWcS", 1, 0], [4, 4294966231], [5, 127.57, 35.28], [122.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "进行兑换?", 28, 0, 1, 1, 24, [18]], [20, "labClose", false, 1, [[14, "点击空白区域关闭界面", 36, 0, 1, 1, -61, [19]]], [0, "45mYHiRvVKr45YLnYJAf73", 1, 0], [5, 360, 45.36], [0, -222.082, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "checkmark", 512, false, 8, [-62], [0, "1bn84K7/dG659NTGLBeMNY", 1, 0], [5, 49, 41], [7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 2, false, 27, [22]], [31, 3, false, 8, [4, 4292269782], 11, 28], [2, "showTips", 4, [[28, "今天登录不在提示", 27, 1, 1, -63, [23]]], [0, "39IRwnt0hDxJbkiiVa3Z9a", 1, 0], [5, 216, 50.4], [35, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "verticalRichText", 1, [-64], [0, "1f7dtTzIhBRa3PIDfY9sOA", 1, 0], [5, 107.36, 112.99999999999999], [0, 71.358, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "<color=#00ff00>Rich</c><color=#0fffff>Text</color><br/>样式", 1, 28, 50, 31]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 4, 0, 5, 29, 0, 6, 32, 0, 7, 2, 0, 8, 25, 0, 9, 20, 0, 10, 23, 0, 11, 21, 0, 12, 3, 0, 13, 14, 0, 14, 16, 0, 15, 18, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 12, 0, -3, 13, 0, -4, 9, 0, -5, 6, 0, -6, 7, 0, -7, 2, 0, -8, 26, 0, -9, 4, 0, -10, 31, 0, 0, 2, 0, -1, 19, 0, -2, 3, 0, -3, 24, 0, 0, 3, 0, -1, 10, 0, -2, 22, 0, 0, 4, 0, -1, 8, 0, -2, 30, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -2, 16, 0, -1, 15, 0, 0, 7, 0, -2, 18, 0, -1, 17, 0, -1, 29, 0, -1, 11, 0, -2, 27, 0, -1, 14, 0, 0, 9, 0, 0, 10, 0, -2, 21, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 15, 0, 0, 17, 0, -1, 20, 0, -1, 23, 0, -1, 25, 0, 0, 26, 0, -1, 28, 0, 0, 30, 0, -1, 32, 0, 16, 1, 64], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 23, 28], [-1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 2, 2, 1], [0, 1, 0, 2, 0, 3, 0, 0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 0, 0, 0, 9, 0, 0, 10, 11, 12]]