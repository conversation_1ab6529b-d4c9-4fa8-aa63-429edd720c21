[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "787lk4Ro5EYZft+vzLmP3p", "92woK3XDZIfrvXna97eRu8", "12Zl3J2JVKjrotSjEK2dUC", "a7Gl9z16FNFrFLrB8fgJ9L", "83fxDP5ktMqpBfknXS8w/3", "c2MKClTxFHpJg3gc4b+3o6", "16iOI3BKlIer2yd0bQ/6OK", "83DfG/fFtEMILpTdy3+rY6"], ["node", "_spriteFrame", "_file", "root", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_trs", "_color", "_contentSize", "_children", "_anchorPoint"], 1, 4, 9, 1, 7, 5, 5, 2, 5], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "angleVar", "startSize", "endSize", "startSpin", "speed", "tangentialAccel", "_dstBlendFactor", "lifeVar", "angle", "speedVar", "_positionType", "endSizeVar", "rotationIsDir", "startSizeVar", "endSpin", "endSpinVar", "tangentialAccelVar", "radialAccel", "radialAccelVar", "startRadius", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "_file", "_spriteFrame", "gravity", "posVar"], -21, 1, 3, 8, 8, 8, 8, 6, 6, 5, 5], ["cc.Sprite", ["_sizeMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "curveData"], -1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[3, 0, 1, 2, 2], [0, 0, 4, 3, 2, 2], [0, 0, 4, 3, 2, 5, 2], [1, 0, 1, 2, 3, 11, 4, 5, 6, 7, 14, 8, 13, 9, 24, 25, 26, 27, 28, 29, 33, 30, 31, 14], [0, 0, 1, 4, 3, 2, 6, 7, 9, 5, 3], [2, 0, 2, 3, 4, 2], [0, 0, 4, 3, 2, 6, 7, 2], [0, 0, 1, 4, 3, 2, 6, 7, 5, 3], [1, 10, 0, 1, 2, 3, 11, 12, 4, 5, 17, 6, 7, 18, 19, 8, 13, 9, 20, 21, 22, 24, 25, 26, 27, 28, 29, 33, 32, 30, 31, 21], [2, 2, 3, 4, 1], [2, 1, 2, 3, 4, 2], [4, 0, 1, 2, 3, 4, 5], [5, 0, 2], [0, 0, 8, 3, 2, 2], [0, 0, 1, 4, 8, 2, 5, 3], [6, 0, 1, 2, 3, 2], [3, 1, 2, 1], [1, 10, 0, 1, 2, 3, 11, 12, 4, 5, 6, 15, 7, 14, 8, 9, 16, 24, 25, 26, 27, 28, 29, 32, 30, 31, 17], [1, 10, 0, 1, 2, 3, 11, 12, 4, 5, 6, 15, 7, 14, 8, 13, 9, 16, 24, 25, 26, 27, 28, 29, 32, 30, 31, 18], [1, 10, 0, 1, 2, 3, 12, 4, 5, 6, 15, 7, 14, 8, 13, 9, 16, 23, 24, 25, 26, 27, 28, 29, 32, 30, 31, 18]], [[[[11, "10006", 0.4166666666666667, 24, 0.5, [{}, "paths", 11, [{"effect_jtxn_heal/effect_jtxn_heal_lizi4": {"props": {"y": [{"frame": 0.08333333333333333, "value": -42.349}, {"frame": 0.3333333333333333, "value": 142.44}]}, "comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.16666666666666666, "value": 80}, {"frame": 0.2916666666666667, "value": 0}]}}}, "effect_jtxn_heal/effect_jtxn_heal_lizi2": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 50}, {"frame": 0.08333333333333333, "value": 0}]}}}, "effect_jtxn_heal/effect_jtxn_heal_lizi": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 50}, {"frame": 0.08333333333333333, "value": 0}]}}}, "effect_jtxn_heal/effect_jtxn_heal_lizi3": {"props": {"y": [{"frame": 0, "value": -62.235}, {"frame": 0.08333333333333333, "value": 99.207}]}, "comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 300}, {"frame": 0.08333333333333333, "value": 0}]}}}, "effect_jtxn_heal/effect_jtxn_heal_light03": {"props": {"scaleX": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.16666666666666666, "value": 0.45}, {"frame": 0.25, "value": 0}], "scaleY": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.16666666666666666, "value": 0.45}, {"frame": 0.25, "value": 0.65}], "y": [{"frame": 0.08333333333333333, "value": -52.335}, {"frame": 0.3333333333333333, "value": -43.987}]}}, "effect_jtxn_heal/effect_jtxn_heal_light04": {"props": {"scaleX": [{"frame": 0.125, "value": 0}, {"frame": 0.20833333333333334, "value": 0.3}, {"frame": 0.2916666666666667, "value": 0}], "scaleY": [{"frame": 0.125, "value": 0}, {"frame": 0.20833333333333334, "value": 0.45}, {"frame": 0.2916666666666667, "value": 0.65}], "y": [{"frame": 0.125, "value": -55.466}, {"frame": 0.375, "value": -48.161}]}}, "effect_jtxn_heal/effect_jtxn_heal_light05": {"props": {"scaleX": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 0.25, "value": 0.35}, {"frame": 0.3333333333333333, "value": 0}], "scaleY": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 0.25, "value": 0.45}, {"frame": 0.3333333333333333, "value": 0.65}], "y": [{"frame": 0.16666666666666666, "value": -55.466}, {"frame": 0.4166666666666667, "value": -49.726}]}}, "effect_jtxn_heal/effect_jtxn_heal_guangyun": {"props": {"opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.125, "value": 25}, {"frame": 0.25, "value": 25}, {"frame": 0.3333333333333333, "value": 0}]}}, "effect_jtxn_heal": {"props": {"opacity": [{"frame": 0, "value": 255}]}}, "effect_jtxn_heal/effect_ty_heal_lizi": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 100, "curve": "constant"}, {"frame": 0.08333333333333333, "value": 0}]}}}}, "effect_jtxn_heal/effect_jtxn_heal_1", 11, [{"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 50}, {"frame": 0.08333333333333333, "value": 0}]}}}, "props", 11, [{"y": [{"frame": 0.041666666666666664, "value": -15.653, "curve": "cubicOut"}, {"frame": 0.125, "value": 85.046}]}, "scale", 12, [[[{"frame": 0.041666666666666664, "curve": "cubicOut"}, "value", 8, [1, 0.5, 0.5, 1]], [{"frame": 0.125}, "value", 8, [1, 0.85, 0.85, 1]]], 11, 11]]], "effect_jtxn_heal/effect_jtxn_heal_2", 11, [{"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 50}, {"frame": 0.08333333333333333, "value": 0}]}}}, "props", 11, [{"y": [{"frame": 0.041666666666666664, "value": -15.653, "curve": "cubicOut"}, {"frame": 0.125, "value": 85.046}]}, "scale", 12, [[[{"frame": 0.041666666666666664, "curve": "cubicOut"}, "value", 8, [1, 0.5, 0.5, 1]], [{"frame": 0.125}, "value", 8, [1, 0.85, 0.85, 1]]], 11, 11]]], "effect_jtxn_heal/effect_jtxn_heal_3", 11, [{"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 50}, {"frame": 0.08333333333333333, "value": 0}]}}}, "props", 11, [{"y": [{"frame": 0.041666666666666664, "value": -15.653, "curve": "cubicOut"}, {"frame": 0.125, "value": 85.046}]}, "scale", 12, [[[{"frame": 0.041666666666666664, "curve": "cubicOut"}, "value", 8, [1, 0.5, 0.5, 1]], [{"frame": 0.125}, "value", 8, [1, 0.85, 0.85, 1]]], 11, 11]]], "effect_jtxn_heal/effect_jtxn_heal_4", 11, [{"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 50}, {"frame": 0.08333333333333333, "value": 0}]}}}, "props", 11, [{"y": [{"frame": 0.041666666666666664, "value": -15.653, "curve": "cubicOut"}, {"frame": 0.125, "value": 85.046}]}, "scale", 12, [[[{"frame": 0.041666666666666664, "curve": "cubicOut"}, "value", 8, [1, 0.7, 0.7, 1]], [{"frame": 0.125}, "value", 8, [1, 0.85, 0.85, 1]]], 11, 11]]], "effect_jtxn_heal/effect_ty_heal_Cast1", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 150}, {"frame": 0.08333333333333333, "value": 230}, {"frame": 0.125, "value": 0}]}, "scale", 12, [[[{"frame": 0.041666666666666664, "curve": "cubicOut"}, "value", 8, [1, 0.3, 0.3, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1.5, 1.5, 1]], [{"frame": 0.125}, "value", 8, [1, 1.55, 1.55, 1]]], 11, 11, 11]]], "effect_jtxn_heal/effect_ty_heal_Cast2", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.041666666666666664, "value": 150}, {"frame": 0.08333333333333333, "value": 230}, {"frame": 0.125, "value": 0}]}, "scale", 12, [[[{"frame": 0.041666666666666664, "curve": "cubicOut"}, "value", 8, [1, 0.1, 0.1, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 0.7, 0.7, 1]], [{"frame": 0.125}, "value", 8, [1, 1, 1, 1]]], 11, 11, 11]]], "effect_jtxn_heal/effect_jtxn_heal_guangyun01", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 100}, {"frame": 0.08333333333333333, "value": 100}, {"frame": 0.125, "value": 0}]}, "scale", 12, [[[{"frame": 0.041666666666666664, "curve": "cubicOut"}, "value", 8, [1, 0.2, 0.2, 1.7]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1, 1.7]], [{"frame": 0.125}, "value", 8, [1, 1.1, 1.1, 1.7]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[12, "10006"], [13, "10006", [-3], [[15, true, -2, [42], 41]], [16, -1, 0]], [14, "effect_jtxn_heal", 0, 1, [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19], [0, "e6e9wgjUZFmY5qgGheBC1z", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [1, "effect_jtxn_heal_lizi4", 2, [[8, 1, true, 10, 0, 0.2, 0.1, 360, 360, 5, 25, 12.3199997, -47.369998931884766, -47.369998931884766, -142.11000061035156, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -20, [0], [4, 4292280063], [4, 167772160], [4, 3336502527], [4, 167772160], [0, 70, 7], [0, 0.25, 1000], 1, 2]], [0, "b97XXbw5dCrKP7MCsnitTL", 1, 0]], [2, "effect_jtxn_heal_1", 2, [[3, true, 1, 0, 0.35, 0.1, 0, 35, 35, 5, 2, 0, 0, 0, -21, [3], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 10, 20], 4, 5]], [0, "6eGqTBidFPkJ6AL/Xl8cfj", 1, 0], [-20.906, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_jtxn_heal_2", 2, [[3, true, 1, 0, 0.35, 0.1, 0, 35, 35, 5, 2, 0, 0, 0, -22, [6], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 10, 20], 7, 8]], [0, "a3q54k1rNOYqEKOWIZ1ksI", 1, 0], [-59.234, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_jtxn_heal_3", 2, [[3, true, 1, 0, 0.35, 0.1, 0, 35, 35, 5, 2, 0, 0, 0, -23, [9], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 10, 20], 10, 11]], [0, "45SNjQA89PrZ0RTImp4j1t", 1, 0], [62.718, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_jtxn_heal_4", 2, [[3, true, 1, 0, 0.35, 0.1, 0, 35, 35, 5, 2, 0, 0, 0, -24, [12], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 10, 20], 13, 14]], [0, "0d+X+ReNVOdpiR0B6eabhH", 1, 0], [19.164, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_jtxn_heal_lizi2", 2, [[17, 1, true, 10, 0, 0.15, 0.05, 360, 360, 25, 10, 7, 5, 1, 400, 0, true, -25, [15], [4, 4283170667], [4, 0], [4, 842268496], [4, 0], [0, 0.25, 0.8600000143051147], 16, 17]], [0, "49T78csZtBipv13w+omzgd", 1, 0]], [1, "effect_jtxn_heal_lizi", 2, [[18, 1, true, 10, 0, 0.3, 0.1, 360, 360, 45, 20, 7, 5, 1, 220, 20, 0, true, -26, [18], [4, 4283170667], [4, 0], [4, 842268496], [4, 0], [0, 0.25, 0.8600000143051147], 19, 20]], [0, "7e+HgQBnFB36drCip+IGIl", 1, 0]], [1, "effect_jtxn_heal_lizi3", 2, [[8, 1, true, 20, 0, 0.2, 0.1, 360, 360, 3.369999885559082, 30, 15.3199997, -47.369998931884766, -47.369998931884766, -142.11000061035156, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -27, [21], [4, 4292280063], [4, 167772160], [4, 2027879679], [4, 167772160], [0, 70, 7], [0, 0.25, 1000], 22, 23]], [0, "cdMludUdNFs4jkUO4F3MKo", 1, 0]], [6, "effect_ty_heal_Cast1", 2, [[9, -28, [24], 25]], [0, "cbovLi+0hFyr4zL1Hx4v0V", 1, 0], [4, 4286054304], [5, 102, 102]], [6, "effect_ty_heal_Cast2", 2, [[9, -29, [26], 27]], [0, "05NJjvuaZEFawl0EhJHfpG", 1, 0], [4, 4286054304], [5, 102, 102]], [4, "effect_jtxn_heal_light03", 200, 2, [[5, 0, -30, [28], 29]], [0, "e25gaygh5GVb9JodR9H6AE", 1, 0], [4, 4286313215], [5, 55, 219], [0, 0.5, 0], [30.385, -47.148, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [4, "effect_jtxn_heal_light04", 200, 2, [[5, 0, -31, [30], 31]], [0, "bbxQgLhVpDFqaK6wDi5co5", 1, 0], [4, 4286313215], [5, 55, 219], [0, 0.5, 0], [-16.576, -50.498, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [4, "effect_jtxn_heal_light05", 200, 2, [[5, 0, -32, [32], 33]], [0, "f0tnJ+khxCPLrLQKsjBPu6", 1, 0], [4, 4286313215], [5, 55, 219], [0, 0.5, 0], [18.489, -50.179, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [7, "effect_jtxn_heal_guangyun", 50, 2, [[10, 1, -33, [34], 35]], [0, "bdYECZb79IrIpryHT9XvMi", 1, 0], [4, 4287496139], [5, 256, 256], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1.7]], [1, "effect_ty_heal_lizi", 2, [[19, 1, true, 4, 0, 0.1, 360, 360, 250, 40, 7, 5, 1, -1000, 20, 0, true, 50, -34, [36], [4, 4283170667], [4, 0], [4, 842268496], [4, 0], [0, 0.25, 0.8600000143051147], 37, 38]], [0, "fdJ7Kq6IdCoLP1OLEH8qb/", 1, 0]], [7, "effect_jtxn_heal_guangyun01", 50, 2, [[10, 1, -35, [39], 40]], [0, "f8iHAR0lpIoJJIcIkgoo73", 1, 0], [4, 4278583181], [5, 256, 256], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1.7]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 10, 0, -9, 11, 0, -10, 12, 0, -11, 13, 0, -12, 14, 0, -13, 15, 0, -14, 16, 0, -15, 17, 0, -16, 18, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 4, 1, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, 5, -1], [0, 1, 4, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 5, 0, 1, 5, 0, 1, 4, 0, 6, 0, 6, 0, 3, 0, 3, 0, 3, 0, 7, 0, 1, 9, 0, 7, 8, 8]]]]