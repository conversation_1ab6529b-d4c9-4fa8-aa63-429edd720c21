[1, ["ecpdLyjvZBwrvm+cedCcQy", "a1HQlrvZlAC4ZpWB3aD9q5", "98QFY4+1BNlrunZA1ZiOVp", "41D7kWhyFGY7q4NDlzkazn", "440RjU4JhEEJLGsmvEbM1v", "a7RQJXq4RKgqXIc0XVquW5", "44r356SitJOoASvgaT4a3N", "d9dkwSdGNJFK8Jvz7lzizp"], ["node", "_spriteFrame", "_parent", "_textureSetter", "root", "bg<PERSON><PERSON><PERSON>", "heroImag", "heroInfo", "<PERSON><PERSON><PERSON>", "buffInfo", "buff<PERSON><PERSON>", "closeBtn", "_N$content", "data", "buffHeroItem", "_N$file"], [["cc.Node", ["_name", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], 2, 4, 5, 9, 1, 2, 7, 5], ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_color", "_anchorPoint"], 1, 12, 4, 5, 1, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_fontSize", "_lineHeight", "_N$verticalAlign", "_N$overflow", "_string", "_N$horizontalAlign", "_isSystemFontUsed", "node", "_materials"], -4, 1, 3], ["cc.Widget", ["_alignFlags", "_originalHeight", "_originalWidth", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_resize", "node", "_layoutSize"], 0, 1, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["6ac2am37tNIi4gUs+3HjpGG", ["node", "closeBtn", "buff<PERSON><PERSON>", "buffInfo", "<PERSON><PERSON><PERSON>", "heroInfo", "heroImag", "bg<PERSON><PERSON><PERSON>", "buffHeroItem"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["<PERSON><PERSON>", ["vertical", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["3039cCGLWFIeqCPOGQnWhje", ["scaleMin", "scaleMax", "node"], 1, 1], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1]], [[5, 0, 1, 2, 2], [12, 0, 1, 2, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 4, 5, 3, 1, 2, 2], [0, 0, 4, 5, 3, 1, 2, 6, 2], [0, 0, 4, 5, 1, 2, 6, 2], [0, 0, 4, 3, 1, 2, 6, 2], [1, 0, 5, 2, 3, 4, 8, 6, 2], [3, 4, 0, 1, 2, 3, 7, 8, 6], [8, 0, 2], [0, 0, 5, 3, 1, 2, 6, 2], [0, 0, 4, 3, 1, 2, 2], [0, 0, 3, 1, 7, 2, 2], [1, 0, 1, 5, 2, 3, 7, 4, 3], [1, 0, 5, 2, 3, 7, 4, 6, 2], [1, 0, 2, 3, 4, 2], [1, 0, 5, 2, 3, 4, 6, 2], [4, 0, 1, 3, 3], [4, 0, 2, 1, 3, 4], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [5, 1, 2, 1], [2, 0, 2, 3, 4, 2], [2, 1, 0, 2, 3, 3], [2, 0, 2, 3, 2], [10, 0, 1, 2, 3, 4, 5, 6, 6], [11, 0, 1, 2, 2], [6, 0, 1, 3, 3], [6, 2, 0, 3, 4, 3], [3, 4, 0, 1, 5, 2, 3, 7, 8, 7], [3, 4, 0, 1, 6, 5, 2, 3, 7, 8, 8], [3, 0, 1, 5, 2, 3, 7, 8, 6], [13, 0, 1, 2, 3], [14, 0, 1]], [[[{"name": "ui_link_02", "rect": [0, 0, 45, 13], "offset": [0, 0], "originalSize": [45, 13], "capInsets": [14, 0, 0, 0]}], [7], 0, [0], [3], [2]], [[[9, "<PERSON><PERSON><PERSON><PERSON>"], [10, "node", [-11, -12, -13], [[17, 5, 1334, -2], [19, -10, -9, -8, -7, -6, -5, -4, -3, 18]], [20, -1, 0], [5, 750, 1366], [0, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg", 1, [-15, -16, -17, -18, -19, -20], [[2, 1, 0, -14, [14], 15]], [0, "0ckJZHcd1GpJuokaqyKb1M", 1, 0], [5, 487, 520]], [4, "ui_bar_skill_02", 2, [-22, -23, -24], [[2, 1, 0, -21, [12], 13]], [0, "455G+QpidJJLli49+5ppnG", 1, 0], [5, 419, 134], [0, -151.979, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "singleColor", 150, 1, [[[21, 0, -25, [0], 1], [18, 45, 750, 1334, -26], -27], 4, 4, 1], [0, "eeIppSGfFJqYEwvwgviNHY", 1, 0], [4, 4278190080], [5, 750, 1366]], [4, "New ScrollView", 1, [-31], [[22, 1, 0, -28, [17]], [24, false, 0.75, 0.23, null, null, -30, -29]], [0, "47FaYXLxVAHaJBJ+ggFzwp", 1, 0], [5, 400, 100], [0, -20.617, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "view", 5, [-34], [[25, 0, -32, [16]], [26, 1, 20, -33]], [0, "7a1e4aKeRIJp1wSerW8LNw", 1, 0], [5, 400, 100]], [11, "content", 6, [[27, 1, 1, -35, [5, 595, 80]]], [0, "64yj55V3lCULuQWt5O+Zj1", 1, 0], [5, 595, 80]], [14, "buff<PERSON><PERSON>", 2, [[-36, [1, 2, -37, [4, 4278190080]]], 1, 4], [0, "55aN8mvIBOpLQmFAnv7mCP", 1, 0], [4, 4294373422], [5, 224, 41.8], [0, 217.709, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "buffInfo", [[-38, [1, 2, -39, [4, 4281341443]]], 1, 4], [0, "0b9YrmvyVAS78fwUhdwDz7", 1, 0], [5, 400, 80]], [12, "skillTxt", [[28, "技能\n羁绊英雄", 22, 30, 1, 1, 3, -40, [6]], [1, 2, -41, [4, 4281341443]]], [0, "acf+Uks7ZAhJAhs2opQuru", 1, 0], [4, 4294373422], [5, 100, 71.8]], [7, "<PERSON><PERSON><PERSON>", 3, [[-42, [1, 2, -43, [4, 4281341443]]], 1, 4], [0, "e5aizB0DZG6raLMZu24Qc4", 1, 0], [5, 224, 41.8], [0, 0, 0.5], [-70, 35.792, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "heroinfo", 3, [[-44, [1, 2, -45, [4, 4281341443]]], 1, 4], [0, "cdK3W4XHFBTZrJ4VJZ4OC7", 1, 0], [5, 224, 41.8], [0, 0, 0.5], [-70, -17.53, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "heroImag", 3, [[[23, 0, -46, [11]], -47], 4, 1], [0, "8dhkH4o6FAmafun0eW7eNL", 1, 0], [5, 130, 130], [-143.78, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [31, 1, 1, 4], [29, "中毒", 30, 30, false, 1, 1, 3, 8, [2]], [5, "buffInfoNode", 2, [9], [0, "dcGEClOlNFAIKkVurgl+/n", 1, 0], [5, 400, 80], [0, 144.236, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 20, 30, 1, 1, 2, 9, [3]], [6, "icon", 2, [[2, 1, 0, -48, [4], 5]], [0, "18m9f9tZNPELOGdlt4JvxF", 1, 0], [5, 129, 13], [120, 72.041, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "txtNode", 2, [10], [0, "a13/yPk9hD2423lhMAbIZ+", 1, 0], [5, 100, 80], [0, 71.565, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "icon", 2, [[2, 1, 0, -49, [7], 8]], [0, "28cw7e751FDpgeQBq6F3uS", 1, 0], [5, 129, 13], [-120, 72.041, 0, 0, 0, 0, 1, -1, 1, 1]], [8, "李逍遥", 20, 30, 1, 3, 11, [9]], [8, "立即刷新英雄", 20, 30, 1, 3, 12, [10]], [32, 13]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 7, 0, 6, 23, 0, 7, 22, 0, 8, 21, 0, 9, 17, 0, 10, 15, 0, 11, 14, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 5, 0, 0, 2, 0, -1, 8, 0, -2, 16, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, -6, 3, 0, 0, 3, 0, -1, 11, 0, -2, 12, 0, -3, 13, 0, 0, 4, 0, 0, 4, 0, -3, 14, 0, 0, 5, 0, 12, 7, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, -1, 15, 0, 0, 8, 0, -1, 17, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -1, 21, 0, 0, 11, 0, -1, 22, 0, 0, 12, 0, 0, 13, 0, -2, 23, 0, 0, 18, 0, 0, 20, 0, 13, 1, 9, 2, 16, 10, 2, 19, 49], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15], [-1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, -1, 14, 15], [0, 3, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 4, 0, 5, 0, 0, 6, 7]]]]