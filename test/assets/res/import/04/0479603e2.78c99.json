[1, ["ecpdLyjvZBwrvm+cedCcQy", "42nR4TEtlLvY8iS7+6QeeK", "440RjU4JhEEJLGsmvEbM1v", "92izrCIHFP2KZdWvysdarZ", "aeUCSfk2xLEpz4npZEvK2c", "61skUhkeNG1aYYz515tR0E", "dbWOqmrNVOl4iLFFqhIFBA", "8erg+/aClNDZnAfwFhesk1", "afjDupM+tAu7BCBOxNsDrE", "96gwzqhMlMs4GW+98uhlEM", "8e6SWSl6tJZpio0Vc/awyU", "55Uv8jlGhOHoZG8YWkCplm", "5eiC6ChH1HhLpnoJHQAeKP", "3cdWBPsXhCx5rlPNMP3bxA", "a6XQlubZdDubHS4WYNI5ob", "8fX2NzwtFHVZ2+QcsVc1D6", "a08N8f+aVOP6wiotUdPA4Q", "d9dkwSdGNJFK8Jvz7lzizp", "ee39zLBFVLkoNIyu/tr5nY", "bc9zcfqjFKir+TWwtoLh3l", "2fy/GAtVBOEZodVNr2KsXa", "983dbR6F5JLbzpqCo+MMp3", "6edneUK7BB9q8lVQdMcJXh", "84eI2zy2BPDayj5W+fXlJh", "08slI9ML1FfoTjI69QWYS5", "f8XPWjOKtHSYs3KDO4dV3E"], ["node", "_spriteFrame", "_N$file", "_parent", "root", "_textureSetter", "upgradeNum", "upgradeIcon", "upBtn", "productTime", "maxCollect", "productNum", "titleImg", "buildingImg", "labNextLevel", "labCurrentLevel", "txt_maxLv", "closeBtn", "data", "muChangImg", "lingMaiImg", "qianZhuangImg", "muChangTitle", "lingMaiTitle", "qianZhuangTitle"], [["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color", "_anchorPoint"], 2, 12, 4, 5, 7, 1, 2, 5, 5], ["cc.Node", ["_name", "_active", "_prefab", "_components", "_contentSize", "_trs", "_parent", "_children", "_color"], 1, 4, 9, 5, 7, 1, 2, 5], ["cc.Widget", ["_alignFlags", "_left", "_bottom", "_originalHeight", "_top", "_originalWidth", "_enabled", "node"], -4, 1], ["cc.Label", ["_string", "_fontSize", "_N$verticalAlign", "_isSystemFontUsed", "_spacingX", "_lineHeight", "_N$horizontalAlign", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_N$layoutType", "_resize", "_N$paddingLeft", "_N$spacingY", "_N$horizontalDirection", "node", "_layoutSize"], -2, 1, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 2, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["ebbf3XretlKAYb6YoQy+lGU", ["node", "closeBtn", "txt_maxLv", "labCurrentLevel", "labNextLevel", "buildingImg", "titleImg", "productNum", "maxCollect", "productTime", "upBtn", "upgradeIcon", "upgradeNum", "muChangImg", "lingMaiImg", "qianZhuangImg", "muChangTitle", "lingMaiTitle", "qianZhuangTitle"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["bb9f03v0pNHF7cm1wkZxg8t", ["node", "desc", "num", "delta", "icon", "content"], 3, 1, 1, 1, 1, 3, 1]], [[5, 0, 1, 2], [5, 0, 1, 2, 2], [11, 0, 1, 2, 2], [0, 1, 0, 3, 4, 5, 3], [4, 0, 1, 5, 3, 4, 2, 7, 8, 7], [2, 0, 6, 7, 3, 2, 4, 5, 2], [1, 0, 5, 1, 2, 3, 4, 2], [2, 0, 7, 3, 2, 4, 5, 2], [2, 0, 6, 3, 2, 4, 5, 2], [2, 0, 6, 3, 2, 4, 2], [2, 0, 6, 3, 2, 8, 4, 5, 2], [1, 0, 5, 1, 2, 7, 3, 8, 4, 2], [1, 0, 5, 1, 2, 7, 3, 8, 2], [1, 0, 5, 1, 2, 3, 8, 4, 2], [7, 0, 1, 6, 2, 3, 4, 7, 5, 2], [0, 1, 0, 3, 4, 3], [0, 3, 4, 5, 1], [0, 2, 3, 4, 5, 2], [6, 0, 4, 5, 6, 3], [4, 0, 1, 2, 7, 8, 4], [14, 0, 1, 2, 3, 4, 5, 1], [1, 0, 6, 1, 2, 3, 4, 2], [7, 0, 1, 2, 3, 4, 5, 2], [0, 0, 3, 4, 5, 2], [4, 0, 1, 5, 3, 4, 6, 2, 7, 8, 8], [13, 0, 1], [9, 0, 2], [2, 0, 1, 6, 3, 2, 4, 5, 3], [2, 0, 6, 7, 2, 5, 2], [1, 0, 6, 1, 2, 3, 2], [1, 0, 5, 6, 1, 2, 3, 4, 2], [1, 0, 5, 1, 2, 7, 3, 4, 2], [3, 0, 5, 3, 7, 4], [3, 6, 0, 1, 4, 2, 3, 7, 7], [3, 0, 2, 7, 3], [3, 0, 1, 4, 7, 4], [3, 0, 1, 2, 7, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 1], [5, 1, 2, 1], [0, 1, 3, 4, 5, 2], [0, 3, 4, 1], [0, 0, 2, 3, 4, 5, 3], [0, 0, 2, 3, 4, 3], [6, 1, 0, 2, 5, 6, 4], [6, 1, 0, 3, 5, 6, 4], [4, 0, 1, 5, 3, 4, 6, 2, 7, 8, 9, 8], [4, 0, 1, 3, 4, 6, 2, 7, 8, 7], [12, 0, 1]], [[[{"name": "4", "rect": [22, 28, 71, 66], "offset": [-2, -1.5], "originalSize": [119, 119], "capInsets": [0, 0, 0, 0]}], [8], 0, [0], [5], [10]], [[[26, "homeResourceUI"], [7, "homeResourceUI", [-16, -17], [[32, 45, 750, 1334, -2], [37, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, 68, 69, 70, 71, 72, 73]], [38, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "productNum", [-21, -22, -23, -24], [[[15, 1, 0, -19, [32]], -20], 4, 1], [0, "73h98ELk1KH7W6ROxmRDJo", -18], [5, 600, 65], [0, 83, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "maxCollect", [-28, -29, -30, -31], [[[15, 1, 0, -26, [45]], -27], 4, 1], [0, "7a8d3j32VKUZfc05b5bbxN", -25], [5, 600, 65]], [21, "productTime", [-35, -36, -37, -38], [[[15, 1, 0, -33, [58]], -34], 4, 1], [0, "faQtdhpepCS74Qp/LaxqGc", -32], [5, 600, 65], [0, -83, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg", 1, [-41, -42, -43, -44, -45], [[3, 1, 0, -39, [64], 65], [33, false, 2, 33, 185, 172, 948, -40]], [1, "b6xYKkq7lCsaGWXDj7OhX2", 1, 0], [5, 735, 966], [0, -6.5, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btn_blue", 5, [-49, -50], [[[3, 1, 0, -46, [10], 11], -47, [34, 4, 44.68099999999998, -48]], 4, 1, 4], [1, "efI96kQWBIPbrXYom1nVBh", 1, 0], [5, 265, 107], [0, -384.819, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txt_lv", [-52, -53, -54, -55], [[43, 1, 1, 10, -51, [5, 177, 50]]], [1, "98ZZ1hfMhDrbB2aZAIl3OL", 1, 0], [5, 177, 50], [47.207, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 5, [2, 3, 4], [[44, 1, 2, 18, -56, [5, 601, 231]]], [1, "a825hV7SFBGbdYpwWvmEqa", 1, 0], [5, 601, 231], [0, -180.9, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ui_title_di", 5, [-59], [[23, 0, -57, [1], 2], [35, 9, -4, -15, -58]], [1, "2emiauMu9E6bSaxf/UQLJI", 1, 0], [5, 228, 76], [-257.5, 460, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "img_resources_di3", [-61, -62], [[3, 1, 0, -60, [6], 7]], [1, "2eyR1YhjtHK7y+5aU1qrq7", 1, 0], [5, 148, 31], [-0.582, 13.537, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "powerBg", 5, [-64, 7], [[3, 1, 0, -63, [20], 21]], [1, "0fN0xvaEFM545AjfaYcmKi", 1, 0], [5, 400, 61], [0, -1.512, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "txt_maxLv", false, 7, [[45, "满级", 32, 32, false, -12, 1, 1, -65, [18], 19], [2, 3, -66, [4, 4284232713]]], [1, "fcQIlBp0hLjp0cnz+5K0P0", 1, 0], [5, 70, 46.32], [88.5, 1.106, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "layout", 2, [-68, -69], [-67], [0, "a9rC0xy9FIy4lwV37OEx5y", 2], [5, 300, 50], [0, 1, 0.5], [290, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "layout", 3, [-71, -72], [-70], [0, "a9rC0xy9FIy4lwV37OEx5y", 3], [5, 300, 50], [0, 1, 0.5], [290, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "layout", 4, [-74, -75], [-73], [0, "a9rC0xy9FIy4lwV37OEx5y", 4], [5, 300, 50], [0, 1, 0.5], [290, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "closeBtn", 1, [[[23, 0, -76, [66], 67], -77, [36, 12, 30, 30, -78]], 4, 1, 4], [1, "20MlkY/eRKfq7/5vUBYLvU", 1, 0], [5, 110, 110], [-299, -598, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "img_upgradeIcon", 10, [[[39, 1, -79, [3], 4], -80], 4, 1], [1, "04J5i0eiNNs7joD+KKJls7", 1, 0], [5, 71, 66], [-64, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "numNow", 10, [[-81, [2, 3, -82, [4, 4278332289]]], 1, 4], [1, "2a9I/+PORKF6oLAkHmRqpq", 1, 0], [5, 60, 40], [6.653, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "1", 7, [[-83, [2, 3, -84, [4, 4284889868]]], 1, 4], [1, "cb5E9AeRhJP6N3VSY6Uy9x", 1, 0], [5, 70, 45], [-43.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "2", 7, [[-85, [2, 3, -86, [4, 4284889868]]], 1, 4], [1, "a3wsf8AZJI45mOsxBtaotc", 1, 0], [4, 4283430435], [5, 70, 45], [53.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_frame_jianzhu_di", 5, [-88], [[16, -87, [23], 24]], [1, "bevPewdGNJBKon+gWnp6zd", 1, 0], [5, 422, 343], [0, 212.745, 0, 0, 0, 0, 1, 1, 1, 2]], [11, "txt_des", 2, [[-89, [2, 3, -90, [4, 4281342983]]], 1, 4], [0, "9epzwq6Z1Lap+dC4cKx5/S", 2], [4, 4294442161], [5, 118, 56.4], [0, 0, 0.5], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "txt_nextNum", 13, [[-91, [2, 3, -92, [4, 4281342983]]], 1, 4], [0, "fbiJ/lXTlBjrrwsWyCFvy6", 2], [4, 4283430435], [5, 84, 30], [0, 1, 0.5]], [13, "txt_curNum", 13, [[-93, [2, 3, -94, [4, 4281342983]]], 1, 4], [0, "2fhEyxTntBz4ACmtk54LwM", 2], [5, 66, 30], [0, 1, 0.5], [-84, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "txt_des", 3, [[-95, [2, 3, -96, [4, 4281342983]]], 1, 4], [0, "9epzwq6Z1Lap+dC4cKx5/S", 3], [4, 4294442161], [5, 146, 56.4], [0, 0, 0.5], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "txt_nextNum", 14, [[-97, [2, 3, -98, [4, 4281342983]]], 1, 4], [0, "fbiJ/lXTlBjrrwsWyCFvy6", 3], [4, 4283430435], [5, 84, 30], [0, 1, 0.5]], [13, "txt_curNum", 14, [[-99, [2, 3, -100, [4, 4281342983]]], 1, 4], [0, "2fhEyxTntBz4ACmtk54LwM", 3], [5, 66, 30], [0, 1, 0.5], [-84, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "txt_des", 4, [[-101, [2, 3, -102, [4, 4281342983]]], 1, 4], [0, "9epzwq6Z1Lap+dC4cKx5/S", 4], [4, 4294442161], [5, 153.78, 56.4], [0, 0, 0.5], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "txt_nextNum", 15, [[-103, [2, 3, -104, [4, 4281342983]]], 1, 4], [0, "fbiJ/lXTlBjrrwsWyCFvy6", 4], [4, 4283430435], [5, 84, 30], [0, 1, 0.5]], [13, "txt_curNum", 15, [[-105, [2, 3, -106, [4, 4281342983]]], 1, 4], [0, "2fhEyxTntBz4ACmtk54LwM", 4], [5, 66, 30], [0, 1, 0.5], [-84, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "ui_title_muchang", 9, [-107], [1, "30tDJAjW1PT49kRPt4wjH8", 1, 0], [5, 96, 48], [-38.844, 1.222, 0, 0, 0, 0, 1, 1, 1, 1]], [40, 31, [0]], [28, "num", 6, [10], [1, "97S6huqptGT6U2J2duE3Qy", 1, 0], [3, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [47, 17], [46, "000", 28, false, -12, 1, 1, 18, [5]], [8, "btn_title_shengji", 6, [[16, -108, [8], 9]], [1, "6eawmW3UtF8Lbz2AXbM9V0", 1, 0], [5, 81, 39], [0, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [25, 6], [8, "img_zhandouli_1", 11, [[16, -109, [12], 13]], [1, "d7JTmXyJFEgbyqwKk2AVSf", 1, 0], [5, 64, 34], [-82.669, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [24, "00", 45, 45, false, -20, 1, 1, 19, [14]], [8, "img_upgrade_arrow2", 7, [[41, 2, false, -110, [15], 16]], [1, "3fknG0EIpLc54KUJkMck3j", 1, 0], [5, 27, 28], [5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "00", 45, 45, false, -20, 1, 1, 20, [17]], [22, "icon_mnjy_muchang", 21, [-111], [1, "57hQfyWEFPz67dS1c9Ue5P", 1, 0], [5, 376, 327], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.90909]], [42, 2, false, 42, [22]], [9, "box", 2, [[3, 1, 0, -112, [25], 26]], [0, "94U7x6kAJHqIqhuJ2J8bdl", 2], [5, 601, 65]], [10, "img_icon", 2, [[17, false, -113, [27], 28]], [0, "8cB4Y7jAxDIqVBL4m128cb", 2], [4, 4293387138], [5, 56, 42], [-244, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "单次产量", 28, 1, 22, [29]], [4, "+000", 30, 30, false, -12, 1, 23, [30]], [4, "000", 30, 30, false, -12, 1, 24, [31]], [18, 1, 1, 13, [5, 300, 50]], [20, 2, 46, 48, 47, [33, 34, 35, 36, 37], 49], [9, "box", 3, [[3, 1, 0, -114, [38], 39]], [0, "94U7x6kAJHqIqhuJ2J8bdl", 3], [5, 601, 63]], [10, "img_icon", 3, [[17, false, -115, [40], 41]], [0, "8cB4Y7jAxDIqVBL4m128cb", 3], [4, 4293387138], [5, 44, 41], [-244, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "最大收集量", 28, 1, 25, [42]], [4, "+000", 30, 30, false, -12, 1, 26, [43]], [4, "000", 30, 30, false, -12, 1, 27, [44]], [18, 1, 1, 14, [5, 300, 50]], [20, 3, 53, 55, 54, [46, 47, 48, 49, 50], 56], [9, "box", 4, [[3, 1, 0, -116, [51], 52]], [0, "94U7x6kAJHqIqhuJ2J8bdl", 4], [5, 601, 65]], [10, "img_icon", 4, [[17, false, -117, [53], 54]], [0, "8cB4Y7jAxDIqVBL4m128cb", 4], [4, 4293387138], [5, 38, 44], [-244, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "生产间隔/秒", 28, 1, 28, [55]], [4, "+000", 30, 30, false, -12, 1, 29, [56]], [4, "000", 30, 30, false, -12, 1, 30, [57]], [18, 1, 1, 15, [5, 300, 50]], [20, 4, 60, 62, 61, [59, 60, 61, 62, 63], 63], [25, 16]], 0, [0, 4, 1, 0, 0, 1, 0, 6, 35, 0, 7, 34, 0, 8, 37, 0, 9, 64, 0, 10, 57, 0, 11, 50, 0, 12, 32, 0, 13, 43, 0, 14, 41, 0, 15, 39, 0, 16, 12, 0, 17, 65, 0, 0, 1, 0, -1, 5, 0, -2, 16, 0, 4, 2, 0, 0, 2, 0, -2, 50, 0, -1, 44, 0, -2, 45, 0, -3, 22, 0, -4, 13, 0, 4, 3, 0, 0, 3, 0, -2, 57, 0, -1, 51, 0, -2, 52, 0, -3, 25, 0, -4, 14, 0, 4, 4, 0, 0, 4, 0, -2, 64, 0, -1, 58, 0, -2, 59, 0, -3, 28, 0, -4, 15, 0, 0, 5, 0, 0, 5, 0, -1, 9, 0, -2, 6, 0, -3, 11, 0, -4, 21, 0, -5, 8, 0, 0, 6, 0, -2, 37, 0, 0, 6, 0, -1, 33, 0, -2, 36, 0, 0, 7, 0, -1, 19, 0, -2, 40, 0, -3, 20, 0, -4, 12, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -1, 31, 0, 0, 10, 0, -1, 17, 0, -2, 18, 0, 0, 11, 0, -1, 38, 0, 0, 12, 0, 0, 12, 0, -1, 49, 0, -1, 23, 0, -2, 24, 0, -1, 56, 0, -1, 26, 0, -2, 27, 0, -1, 63, 0, -1, 29, 0, -2, 30, 0, 0, 16, 0, -2, 65, 0, 0, 16, 0, 0, 17, 0, -2, 34, 0, -1, 35, 0, 0, 18, 0, -1, 39, 0, 0, 19, 0, -1, 41, 0, 0, 20, 0, 0, 21, 0, -1, 42, 0, -1, 46, 0, 0, 22, 0, -1, 47, 0, 0, 23, 0, -1, 48, 0, 0, 24, 0, -1, 53, 0, 0, 25, 0, -1, 54, 0, 0, 26, 0, -1, 55, 0, 0, 27, 0, -1, 60, 0, 0, 28, 0, -1, 61, 0, 0, 29, 0, -1, 62, 0, 0, 30, 0, -1, 32, 0, 0, 36, 0, 0, 38, 0, 0, 40, 0, -1, 43, 0, 0, 44, 0, 0, 45, 0, 0, 51, 0, 0, 52, 0, 0, 58, 0, 0, 59, 0, 18, 1, 2, 3, 8, 3, 3, 8, 4, 3, 8, 7, 3, 11, 10, 3, 33, 117], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 35, 39, 41, 43, 47, 48, 54, 55, 61, 62], [-1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 2, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -2, -3, -4, -5, -1, 1, -1, 1, -1, -1, -1, -1, -1, -2, -3, -4, -5, -1, 1, -1, 1, -1, -1, -1, -1, -1, -2, -3, -4, -5, -1, 1, -1, 1, 19, 20, 21, 22, 23, 24, 1, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2], [0, 0, 11, 0, 12, 0, 0, 2, 0, 13, 0, 14, 0, 15, 0, 0, 16, 0, 0, 17, 0, 18, 0, 0, 19, 0, 2, 0, 3, 0, 0, 0, 0, 3, 6, 7, 4, 5, 0, 2, 0, 4, 0, 0, 0, 0, 3, 6, 7, 4, 5, 0, 2, 0, 5, 0, 0, 0, 0, 3, 6, 7, 4, 5, 0, 20, 0, 21, 8, 22, 23, 9, 24, 25, 9, 1, 1, 1, 8, 1, 1, 1, 1, 1, 1]]]]