[1, ["53140Df5pPQIPBTHsigaFh", "1fbg8PaZpGWIwo4aLCMHD/", "e9Al2F0HhNKphGKTLQvi2A", "89yCcltrtPB6i3VXjMB1Ak", "e73gRFOq1BGZO1c8TcOFOu", "546DazCdFE1JrBCNc8knPH", "e9QNTRFsJB853BxVeY5m+C", "31mM++3dBOcIe+r38ky2kR", "d3ctdE01BHi5W7b+dkxKaP", "2esMqMTulM6qkeJAUJJDEP", "daFqSzQnhFEoNjObaptbaw", "c4sXkU6y1MWKySZ6kc9MdF", "a6iJhngI5HxJGS/Q30NzSX", "fcKp5OXAZO8aIX1J288Pp2"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "curveData"], -1, 11]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "effect_7000500_01_fire", 2.0833333333333335, 24, 0.5, [{}, "paths", 11, [{}, "7000500_xl_fire/7000500_sikll_fire01", 11, [{"props": {"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 225}, {"frame": 0.5416666666666666, "value": 225}, {"frame": 0.625, "value": 0}, {"frame": 2.0833333333333335, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.041666666666666664}, "value", 6, 0], [{"frame": 0.08333333333333333}, "value", 6, 1], [{"frame": 0.125}, "value", 6, 2], [{"frame": 0.16666666666666666}, "value", 6, 3], [{"frame": 0.20833333333333334}, "value", 6, 4], [{"frame": 0.25}, "value", 6, 5], [{"frame": 0.2916666666666667}, "value", 6, 6], [{"frame": 0.3333333333333333}, "value", 6, 7], [{"frame": 0.375}, "value", 6, 8], [{"frame": 0.4166666666666667}, "value", 6, 9], [{"frame": 0.4583333333333333}, "value", 6, 10], [{"frame": 0.5}, "value", 6, 11], [{"frame": 0.5416666666666666}, "value", 6, 12], [{"frame": 0.5833333333333334}, "value", 6, 13]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "7000500_xl_fire/7000500_sikll_fire02", 11, [{"props": {"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 225}, {"frame": 0.7083333333333334, "value": 225}, {"frame": 0.7916666666666666, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 14], [{"frame": 0.25}, "value", 6, 15], [{"frame": 0.2916666666666667}, "value", 6, 16], [{"frame": 0.3333333333333333}, "value", 6, 17], [{"frame": 0.375}, "value", 6, 18], [{"frame": 0.4166666666666667}, "value", 6, 19], [{"frame": 0.4583333333333333}, "value", 6, 20], [{"frame": 0.5}, "value", 6, 21], [{"frame": 0.5416666666666666}, "value", 6, 22], [{"frame": 0.5833333333333334}, "value", 6, 23], [{"frame": 0.625}, "value", 6, 24], [{"frame": 0.6666666666666666}, "value", 6, 25], [{"frame": 0.7083333333333334}, "value", 6, 26], [{"frame": 0.75}, "value", 6, 27]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "7000500_xl_fire/7000500_sikll_fire03", 11, [{"props": {"opacity": [{"frame": 0.3333333333333333, "value": 0, "curve": "constant"}, {"frame": 0.375, "value": 225}, {"frame": 0.875, "value": 225}, {"frame": 0.9583333333333334, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.375}, "value", 6, 28], [{"frame": 0.4166666666666667}, "value", 6, 29], [{"frame": 0.4583333333333333}, "value", 6, 30], [{"frame": 0.5}, "value", 6, 31], [{"frame": 0.5416666666666666}, "value", 6, 32], [{"frame": 0.5833333333333334}, "value", 6, 33], [{"frame": 0.625}, "value", 6, 34], [{"frame": 0.6666666666666666}, "value", 6, 35], [{"frame": 0.7083333333333334}, "value", 6, 36], [{"frame": 0.75}, "value", 6, 37], [{"frame": 0.7916666666666666}, "value", 6, 38], [{"frame": 0.8333333333333334}, "value", 6, 39], [{"frame": 0.875}, "value", 6, 40], [{"frame": 0.9166666666666666}, "value", 6, 41]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "7000500_xl_fire/7000500_sikll_fire04", 11, [{"props": {"opacity": [{"frame": 0.5, "value": 0, "curve": "constant"}, {"frame": 0.5416666666666666, "value": 225}, {"frame": 1.0416666666666667, "value": 225}, {"frame": 1.125, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.5416666666666666}, "value", 6, 42], [{"frame": 0.5833333333333334}, "value", 6, 43], [{"frame": 0.625}, "value", 6, 44], [{"frame": 0.6666666666666666}, "value", 6, 45], [{"frame": 0.7083333333333334}, "value", 6, 46], [{"frame": 0.75}, "value", 6, 47], [{"frame": 0.7916666666666666}, "value", 6, 48], [{"frame": 0.8333333333333334}, "value", 6, 49], [{"frame": 0.875}, "value", 6, 50], [{"frame": 0.9166666666666666}, "value", 6, 51], [{"frame": 0.9583333333333334}, "value", 6, 52], [{"frame": 1}, "value", 6, 53], [{"frame": 1.0416666666666667}, "value", 6, 54], [{"frame": 1.0833333333333333}, "value", 6, 55]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]]