[1, ["143SP/NxVEt6YPqxNSJFrV", "ceAEngDZtIpqgK0NGpQ8YW", "93VsUD/9FG0brpDmldoAtv", "ebzRHQ+95OMIG7iiEeHQvb", "dcbisDTlhL2Zj8Rh/tclhB", "505VyCJelA8p4uVNTGLRol", "00EAqExKBAkLzZcHQ8xLbh", "2eaJtCHXhMI7n+5p7ylYb5", "65Pecf8GlDn5LoH6KY8F6D", "e2oDh1GAFN5Y8Oy26+B6sE", "77pxIfKeZBsJzK9/badByA", "39qwxzxhtLbpKEeOsLuMRy", "9cnUtCRHxPo7fcEyggTpqF"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "mine", 0.375, 24, 0.5, 2, [{"props": {"y": [{"frame": 0, "value": -20.7225}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.041666666666666664}, "value", 6, 1], [{"frame": 0.08333333333333333}, "value", 6, 2], [{"frame": 0.125}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4], [{"frame": 0.20833333333333334}, "value", 6, 5], [{"frame": 0.25}, "value", 6, 6], [{"frame": 0.2916666666666667}, "value", 6, 7], [{"frame": 0.3333333333333333}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "paths", 11, [{}, "futou", 11, [{"props": {"position": [{"frame": 0, "curve": "constant", "value": [8.328, 37.889, 0]}, {"frame": 0.041666666666666664, "curve": "constant", "value": [23.246, 64.258, 0]}, {"frame": 0.08333333333333333, "curve": "constant", "value": [47.519, 76.165, 0]}, {"frame": 0.125, "curve": "constant", "value": [57.869, 64.39, 0]}, {"frame": 0.16666666666666666, "curve": "constant", "value": [61.543, 58.502, 0]}, {"frame": 0.20833333333333334, "curve": "constant", "value": [6.216, 93.621, 0]}, {"frame": 0.25, "curve": "constant", "value": [-21.068, 29.89, 0]}, {"frame": 0.2916666666666667, "curve": "constant", "value": [-23, 36.16, 0]}, {"frame": 0.3333333333333333, "value": [-11.404, 30.558, 0]}], "angle": [{"frame": 0, "value": 54.778999999999996}, {"frame": 0.041666666666666664, "value": -10, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -74.961, "curve": "constant"}, {"frame": 0.125, "value": -111.61, "curve": "constant"}, {"frame": 0.16666666666666666, "value": -122.782, "curve": "constant"}, {"frame": 0.20833333333333334, "value": -6.176000000000002, "curve": "constant"}, {"frame": 0.25, "value": 88.027, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 61.263000000000005, "curve": "constant"}, {"frame": 0.3333333333333333, "value": 82.463}], "active": [{"frame": 0, "value": true}], "anchorX": [{"frame": 0, "value": 0.42}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 9]], 11]]]], "futou/shou", 11, [{"props": {"position": [{"frame": 0, "value": [24.626, -26.576, 0]}], "active": [{"frame": 0, "value": true}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 10]], 11]]]], "futou/wei", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.25, "value": 255, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0.20833333333333334, "value": 12.253, "curve": "constant"}, {"frame": 0.25, "value": -87.206}], "position": [{"frame": 0.20833333333333334, "curve": "constant", "value": [79.325, 25.135, 0]}, {"frame": 0.25, "value": [75.546, 32.245, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334, "curve": "constant"}, "value", 8, [1, 1.7, 1.238, 1]], [{"frame": 0.25}, "value", 8, [1, 1.2, 1.67, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 11], [{"frame": 0.25}, "value", 6, 12]], 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]