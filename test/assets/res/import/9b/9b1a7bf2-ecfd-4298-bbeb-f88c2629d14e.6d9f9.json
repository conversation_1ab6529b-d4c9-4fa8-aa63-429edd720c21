[1, ["ecpdLyjvZBwrvm+cedCcQy", "ffr6TUk+dPuLGsFMLGk6SG", "fddjE/tXJGN7lx2YNmXMIG", "a9yQSrUxRIl42OWg6Azjmt", "bcqXrYgEtBP5Tk6JLFF5EX", "08hudHWKBKB6Rr4AkEa8cb", "c2DeKu8fNAOovbaxOYqNA2", "cehPWaiQJItaI4aupyL8BK", "21wqaA7yxOCLCgujtu0g5B", "b2aHrECZ5APKGS/0d2hvT1", "47LfXTNedBhJ5sf0G+5l7j", "b08n4qcwlNJ7KoRf1M+5LH", "8agtcJcwRLoL5KPvLXtmVb", "54yjgefCBLPIJ8RNUZAz48"], ["node", "_spriteFrame", "_file", "_parent", "_defaultClip", "root", "data"], [["cc.Node", ["_name", "_opacity", "_is3DNode", "_prefab", "_components", "_parent", "_trs", "_children", "_contentSize", "_anchorPoint", "_eulerAngles", "_color"], 0, 4, 9, 1, 7, 2, 5, 5, 5, 5], ["cc.Sprite", ["_dstBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "angleVar", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "startSpinVar", "_dstBlendFactor", "lifeVar", "angle", "startSize", "startSizeVar", "startSpin", "endSpin", "endSpinVar", "tangentialAccelVar", "radialAccel", "radialAccelVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -19, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.Mask", ["_type", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[2, 0, 1, 2, 2], [1, 1, 2, 3, 1], [0, 0, 5, 4, 3, 8, 6, 10, 2], [0, 0, 5, 4, 3, 6, 2], [3, 0, 1, 2, 3, 4, 5, 10, 6, 7, 8, 9, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 12], [1, 0, 1, 2, 3, 2], [0, 0, 5, 7, 4, 3, 8, 9, 6, 2], [0, 0, 5, 4, 3, 8, 9, 6, 2], [0, 0, 7, 3, 2], [0, 0, 5, 4, 3, 8, 6, 2], [0, 0, 2, 5, 7, 3, 3], [0, 0, 5, 7, 4, 3, 11, 8, 9, 6, 2], [0, 0, 5, 4, 3, 8, 9, 2], [5, 0, 1, 2, 3, 2], [3, 11, 0, 1, 2, 3, 12, 13, 4, 14, 15, 5, 16, 17, 18, 6, 7, 8, 9, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 22], [0, 0, 5, 7, 4, 3, 6, 2], [0, 0, 5, 7, 3, 6, 2], [0, 0, 7, 4, 3, 6, 2], [4, 0, 2], [0, 0, 1, 7, 4, 3, 3], [2, 1, 2, 1], [6, 0, 1, 2, 3, 2]], [[18, "effect_7000504_01_front"], [19, "effect_7000504_01_front", 0, [-3, -4, -5, -6, -7, -8], [[21, true, -2, [386], 385]], [20, -1, 0]], [8, "yan", [-9, -10, -11, -12, -13, -14], [0, "c6Ekx235BPVZnewgFy5tIZ", 1, 0]], [8, "yan", [-15, -16, -17, -18, -19, -20], [0, "dblfsLXxlLXrJ5E6vqkVHI", 1, 0]], [8, "yan", [-21, -22, -23, -24, -25, -26], [0, "caEgDlcb9H5a4HgROX+PbC", 1, 0]], [8, "yan", [-27, -28, -29, -30, -31, -32], [0, "09sdYwjo9GIY2arqsRFY3D", 1, 0]], [8, "yan", [-33, -34, -35, -36, -37, -38], [0, "2ftW5/Ih1NQbOCj3ukHFkx", 1, 0]], [8, "yan", [-39, -40, -41, -42, -43, -44], [0, "2dx1FfxiRLs6SMHo6IZ53v", 1, 0]], [8, "yan", [-45, -46, -47, -48, -49, -50], [0, "fcFwP5OuFCWJETpFHlJ+ud", 1, 0]], [8, "yan", [-51, -52, -53, -54, -55, -56], [0, "b9GcIGKB5EILM4N1Me6QtB", 1, 0]], [8, "yan", [-57, -58, -59, -60, -61, -62], [0, "0cLIOP4HBLpKnxPkLse+0v", 1, 0]], [8, "yan", [-63, -64, -65, -66, -67, -68], [0, "afbftopTNMNobyseaXJ1uj", 1, 0]], [8, "yan", [-69, -70, -71, -72, -73, -74], [0, "81v4gVWxNCTaLiylgKw+0U", 1, 0]], [16, "dici_f_02", 1, [-75, -76, -77, -78, -79], [0, "2dNIKNayxOm6WRdyQwxAr6", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [16, "yan_002", 1, [-80, -81, -82, -83, -84], [0, "d3dx4kTkFN8rwzeq3V54s3", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [17, "yan_01", [2, -86, -87], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -85, [36], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 37, 38]], [0, "45nJznczdP4JCZ+rnZpvvH", 1, 0], [-140.258, -47.203, 0, 0, 0, 0, 1, 0.7, 0.7, 0.7]], [10, "stone", true, 15, [-88, -89, -90, -91], [0, "49oLcwARxNgJf02Vl6qqkg", 1, 0]], [17, "yan_02", [3, -93, -94], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -92, [67], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 68, 69]], [0, "8b5PHZxRpLcZdp2L1GrLzL", 1, 0], [208.135, -63.737, 0, 0, 0, 0, 1, 0.7, 0.7, 0.7]], [10, "stone", true, 17, [-95, -96, -97, -98], [0, "806eRRi19MLICvLcGQb/AS", 1, 0]], [15, "yan_01", 14, [4, -100, -101], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -99, [118], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 119, 120]], [0, "78+Nqm1oFEFLr8dijwJWTt", 1, 0], [-49.6, -172.73, 0, 0, 0, 0, 1, 0.9, 0.9, 0.7]], [10, "stone", true, 19, [-102, -103, -104, -105], [0, "2fkjYPWmtC7qFmNuGK7qzh", 1, 0]], [15, "yan_02", 14, [5, -107, -108], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -106, [149], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 150, 151]], [0, "24Dq2qssBLN5Vqv9tXwCDj", 1, 0], [161.006, -190.862, 0, 0, 0, 0, 1, 0.9, 0.9, 0.7]], [10, "stone", true, 21, [-109, -110, -111, -112], [0, "02SR6L9XtNAIYrZv5Ke+U/", 1, 0]], [15, "yan_03", 14, [6, -114, -115], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -113, [180], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 181, 182]], [0, "35MOejG4JAsJL1xStbniCN", 1, 0], [386.954, -89.046, 0, 0, 0, 0, 1, 0.9, 0.9, 0.7]], [10, "stone", true, 23, [-116, -117, -118, -119], [0, "c8hJrrkNJL4K8o1RV5AqQf", 1, 0]], [15, "yan_08", 14, [7, -121, -122], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -120, [211], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 212, 213]], [0, "cd1FpVHkhJFru4g1JZAIBg", 1, 0], [-403.693, 8.138, 0, 0, 0, 0, 1, 0.9, 0.9, 0.9]], [10, "stone", true, 25, [-123, -124, -125, -126], [0, "51tC1Mt7hFq7n0oseOfVWE", 1, 0]], [15, "yan_10", 14, [8, -128, -129], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -127, [242], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 243, 244]], [0, "0eVO9qtt9PU4cji7RZJ9Nh", 1, 0], [-286.743, -153.286, 0, 0, 0, 0, 1, 0.9, 0.9, 0.9]], [10, "stone", true, 27, [-130, -131, -132, -133], [0, "c3JDx0sOxBKZHSVxVW9UKz", 1, 0]], [16, "dici_f_03", 1, [-134, -135, -136, -137], [0, "a0E1gJog5GUZr5VmOk+CyX", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [16, "yan_003", 1, [-138, -139, -140, -141], [0, "fdDfydcihLHojCSQ6ZuaL0", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [15, "yan_01", 30, [9, -143, -144], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -142, [289], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 290, 291]], [0, "edEJZmiwdCiKZltr+gcqKr", 1, 0], [-169.548, -313.269, 0, 0, 0, 0, 1, 1.2, 1.2, 0.7]], [10, "stone", true, 31, [-145, -146, -147, -148], [0, "bfp946c1hJlpQk856doUQg", 1, 0]], [15, "yan_02", 30, [10, -150, -151], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -149, [320], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 321, 322]], [0, "f8wsLBMI1Of6GJK5p6I5Rs", 1, 0], [109.401, -336.98, 0, 0, 0, 0, 1, 1.2, 1.2, 0.7]], [10, "stone", true, 33, [-152, -153, -154, -155], [0, "f3xIsuMolJbK0P9iO/NoSO", 1, 0]], [15, "yan_03", 30, [11, -157, -158], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -156, [351], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 352, 353]], [0, "baqHd90xlHC54ksUiMLbaj", 1, 0], [441.172, -255.136, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [10, "stone", true, 35, [-159, -160, -161, -162], [0, "0a68YE2FJIOZsWXlOBudb6", 1, 0]], [15, "yan_10", 30, [12, -164, -165], [[14, 1, true, 200, 0, 0.4, 0.5, 360, 360, 3.369999885559082, 50, 30.31999969482422, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -163, [382], [4, 2740373706], [4, 3333292005], [4, 3591610797], [4, 3170498923], [0, 7, 7], [0, 0.25, 0.8600000143051147], 383, 384]], [0, "f68hOCfjBLs6qib2R7g2X5", 1, 0], [-511.153, -165.793, 0, 0, 0, 0, 1, 1.2, 1.2, 0.7]], [10, "stone", true, 37, [-166, -167, -168, -169], [0, "74+mlbFcNM64pQrPjiDKnU", 1, 0]], [16, "dici_f_01", 1, [-170, -171], [0, "50aU0OlS5E44t0J4lZ9X48", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [6, "01", 39, [-173], [[13, 2, -172, [2], 3]], [0, "99bx8+GF1PkZKctcnwvkiz", 1, 0], [5, 176, 295], [0, 0.5, 0], [-142.07, -64.99, 0, 0, 0, 0, 1, 0.4, 0.4, 0.561]], [6, "02", 39, [-175], [[13, 2, -174, [6], 7]], [0, "3bxbeB+Z9FWrnpfUEn2EZJ", 1, 0], [5, 195, 297], [0, 0.5, 0], [206.721, -82.171, 0, 0, 0, 0, 1, 0.4, 0.4, 0.561]], [16, "yan_001", 1, [15, 17], [0, "c65XJGIY9N5JSwqEyNxPsa", 1, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [11, "7000504_banyuan", 15, [-177], [[5, 1, -176, [34], 35]], [0, "2bUGozyvlHJKW0PAgMDzq9", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 17, [-179], [[5, 1, -178, [65], 66]], [0, "1dzVCB78pNZbeqpxgDeTXI", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [6, "01", 13, [-181], [[13, 2, -180, [72], 73]], [0, "3eF9T8SedMWrFUnn0Rf8/h", 1, 0], [5, 176, 295], [0, 0.5, 0], [-49.127, -200.963, 0, 0, 0, 0, 1, 0.561, 0.561, 0.561]], [6, "02", 13, [-183], [[13, 2, -182, [76], 77]], [0, "abWza6zbVPZJQZfzmDAGQ9", 1, 0], [5, 195, 297], [0, 0.5, 0], [161.962, -220.927, 0, 0, 0, 0, 1, 0.561, 0.561, 0.561]], [6, "03", 13, [-185], [[13, 2, -184, [80], 81]], [0, "cc+0KqmxdLPKxf3IjtI8Tw", 1, 0], [5, 195, 297], [0, 0.5, 0], [385.334, -118.489, 0, 0, 0, 0, 1, 0.561, 0.561, 0.561]], [6, "09", 13, [-187], [[13, 2, -186, [84], 85]], [0, "3buTQHn/BBwpetPJ89ziAl", 1, 0], [5, 216, 277], [0, 0.5, 0], [-408.696, -29.891, 0, 0, 0, 0, 1, -0.561, 0.561, 0.561]], [6, "10", 13, [-189], [[13, 2, -188, [88], 89]], [0, "f4VXpfHPlISLJwb5I+sDtr", 1, 0], [5, 195, 297], [0, 0.5, 0], [-275.748, -185.241, 0, 0, 0, 0, 1, -0.561, 0.561, 0.561]], [11, "7000504_banyuan", 19, [-191], [[5, 1, -190, [116], 117]], [0, "91ko78CrdLh4MuPcOnPbQ9", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 21, [-193], [[5, 1, -192, [147], 148]], [0, "27DFN36QdOaoVh/eEWJDMI", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 23, [-195], [[5, 1, -194, [178], 179]], [0, "2buWO1ElFFaLsUYYBhlXus", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 25, [-197], [[5, 1, -196, [209], 210]], [0, "c8mEYXDAlNmaf/jY+0vRzF", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 27, [-199], [[5, 1, -198, [240], 241]], [0, "4fsLcw9+BA1Lt9//wjLIZd", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [6, "01", 29, [-201], [[13, 2, -200, [247], 248]], [0, "a0bKhWKndD6IszM4REcn7U", 1, 0], [5, 176, 295], [0, 0.5, 0], [-171.937, -336.127, 0, 0, 0, 0, 1, -0.64, 0.64, 0.561]], [6, "02", 29, [-203], [[13, 2, -202, [251], 252]], [0, "d6ZH/KCo9C0Lq7ufclr9tV", 1, 0], [5, 176, 295], [0, 0.5, 0], [109.832, -359.035, 0, 0, 0, 0, 1, 0.64, 0.64, 0.561]], [6, "03", 29, [-205], [[13, 2, -204, [255], 256]], [0, "daMGNGrDhPvL46GfX1q6Sc", 1, 0], [5, 195, 297], [0, 0.5, 0], [431.773, -275.771, 0, 0, 0, 0, 1, 0.64, 0.64, 0.561]], [6, "10", 29, [-207], [[13, 2, -206, [259], 260]], [0, "4bOzNssYxGqZx3D8e2LCVt", 1, 0], [5, 195, 297], [0, 0.5, 0], [-505.946, -185.846, 0, 0, 0, 0, 1, -0.64, 0.64, 0.561]], [11, "7000504_banyuan", 31, [-209], [[5, 1, -208, [287], 288]], [0, "baR3MN9zBJMp2/GzyG0VFi", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 33, [-211], [[5, 1, -210, [318], 319]], [0, "e121vQKZtDHY0kr4Hrt+5D", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 35, [-213], [[5, 1, -212, [349], 350]], [0, "5bi/aCjEVKbLPlQr8bbO59", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [11, "7000504_banyuan", 37, [-215], [[5, 1, -214, [380], 381]], [0, "447p2nvW5DmYHugvpzs1si", 1, 0], [4, 4286749843], [5, 127, 102], [0, 0.5, 0.3], [0, 0, 0, 0, 0, 0, 1, 2.2, 2.2, 1]], [7, "7000504_dici_02", 40, [[1, -216, [0], 1]], [0, "e1KzOxkd1KeK6u1Wnd3IaQ", 1, 0], [5, 162, 281], [0, 0.5, 0], [0, -12.569, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_03", 41, [[1, -217, [4], 5]], [0, "3ajPc7FsRK+oNkgsZ1JK27", 1, 0], [5, 181, 283], [0, 0.5, 0], [-0.928, -11.133, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_001", 2, [[1, -218, [8], 9]], [0, "a746oJeEJM4bVVMZt4sPub", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 2, [[1, -219, [10], 11]], [0, "22TIihyUhJUKmn92c+D1bm", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 2, [[1, -220, [12], 13]], [0, "15g+c+hBJA/r5+XxlEZXGq", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 2, [[1, -221, [14], 15]], [0, "30XvWkjy9AxISlGA14/7x6", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 2, [[1, -222, [16], 17]], [0, "43df2pXHBMdYonLzHJshgO", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 2, [[1, -223, [18], 19]], [0, "1aDACicqVAy41bCW8CbbHA", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 16, [[4, true, 1, 5, 0.2, 0, 50, 300, 2, 0, 0, 0, -224, [20], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 21, 22]], [0, "b64i3HO7NDQJZL2Zn8z3Lv", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 16, [[4, true, 1, 5, 0.2, 0, 50, 300, 2, 0, 0, 0, -225, [23], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 24, 25]], [0, "b5TUPGUSJDJqDxqjdSKk8J", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 16, [[4, true, 1, 5, 0.2, 0, 50, 300, 2, 0, 0, 0, -226, [26], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 27, 28]], [0, "25CgGUeOFNQ7uybMNtCxa2", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 16, [[4, true, 1, 5, 0.2, 0, 50, 300, 2, 0, 0, 0, -227, [29], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 30, 31]], [0, "c5AfdshF5NZYDhVCAAUGdO", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 43, [[5, 1, -228, [32], 33]], [0, "7ccDvJsKNMzZUsyzjXV1J6", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 3, [[1, -229, [39], 40]], [0, "d9gO41Yb5LtIE635lDChFu", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 3, [[1, -230, [41], 42]], [0, "31saJW3e5NLY1yuOoqLgOd", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 3, [[1, -231, [43], 44]], [0, "e9qHv0znlHM6BvH77bs3IC", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 3, [[1, -232, [45], 46]], [0, "b3fXgCcKlAHrFcdq8BG0UL", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 3, [[1, -233, [47], 48]], [0, "07yJFI1hdHJIuKsf5zx9NL", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 3, [[1, -234, [49], 50]], [0, "56285+4txPkbgz0PRxT1PY", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 18, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -235, [51], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 52, 53]], [0, "cdC8YOPk9DKa+1UzjSB4in", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 18, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -236, [54], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 55, 56]], [0, "51y4bM5v1CdJ+pZ5OjgI72", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 18, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -237, [57], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 58, 59]], [0, "53c0h5c2JLdI/PO+gByowm", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 18, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -238, [60], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 61, 62]], [0, "43NnISpUNKQah2DymYlYpD", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 44, [[5, 1, -239, [63], 64]], [0, "5clC2/2TVFdKFyu5ZrcKTk", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [7, "7000504_dici_02", 45, [[1, -240, [70], 71]], [0, "f2b7cJ/4JLxISgnIUrVvEt", 1, 0], [5, 162, 281], [0, 0.5, 0], [0, -12.569, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_03", 46, [[1, -241, [74], 75]], [0, "7epwupkDhOZ7zSER7oLuiU", 1, 0], [5, 181, 283], [0, 0.5, 0], [-0.928, -11.133, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_03", 47, [[1, -242, [78], 79]], [0, "99frCTeP9ER7U68csoLz/Q", 1, 0], [5, 181, 283], [0, 0.5, 0], [-0.928, -11.133, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_01", 48, [[1, -243, [82], 83]], [0, "07KiT2FbhNq55cTnBFzs9z", 1, 0], [5, 202, 264], [0, 0.5, 0], [1.79, -3.379, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_03", 49, [[1, -244, [86], 87]], [0, "28kF32ym1FsIfodzuy0ZCP", 1, 0], [5, 181, 283], [0, 0.5, 0], [-0.928, -11.133, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_001", 4, [[1, -245, [90], 91]], [0, "8bWcZQTDlCKYrrtomXUgwT", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 4, [[1, -246, [92], 93]], [0, "c3yUfFU5pNa6SbJBp4aYf8", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 4, [[1, -247, [94], 95]], [0, "dczsOH6vFOX6RRrikU4GL9", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 4, [[1, -248, [96], 97]], [0, "cao7At3t5LfZNVlwwYKH6t", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 4, [[1, -249, [98], 99]], [0, "cb1fJVNWJAB73SoJZN14Im", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 4, [[1, -250, [100], 101]], [0, "c66Sbw0iVNt79jJXN0oW8V", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 20, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -251, [102], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 103, 104]], [0, "66wiDAQU5LSK29+6L7coMl", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 20, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -252, [105], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 106, 107]], [0, "36bUu/VdVDc5/NNIQrUrBL", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 20, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -253, [108], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 109, 110]], [0, "f1rUxjMaZOm4YVgkueUXml", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 20, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -254, [111], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 112, 113]], [0, "12SfgXOuVEubUEnApgFAeg", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 50, [[5, 1, -255, [114], 115]], [0, "31shxvrR9FJpM50d1OCXff", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 5, [[1, -256, [121], 122]], [0, "95EVOvFFdBFI4U17pbcpdw", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 5, [[1, -257, [123], 124]], [0, "01L+P0VvhNwpggDqjN7gHg", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 5, [[1, -258, [125], 126]], [0, "d3tsGgLHtMSZ2A+dJ552qM", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 5, [[1, -259, [127], 128]], [0, "90AIwoJepDcZ7bcjYO3EfU", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 5, [[1, -260, [129], 130]], [0, "adF+INWJNI8aWCvP8UkSPc", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 5, [[1, -261, [131], 132]], [0, "c85CZJeF5PYJva5jihj5he", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 22, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -262, [133], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 134, 135]], [0, "e5wCCwnOpFcIFB3jKEao8D", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 22, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -263, [136], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 137, 138]], [0, "03ejmJHAtAir7b3qnufCs2", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 22, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -264, [139], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 140, 141]], [0, "52fZkoKTZJnZGIm/HfqIIE", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 22, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -265, [142], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 143, 144]], [0, "83dx0dd7dDEIWKnPbRWVpc", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 51, [[5, 1, -266, [145], 146]], [0, "a2LLZPwltLj544pWG7/nlM", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 6, [[1, -267, [152], 153]], [0, "b4+s6ex5RIdJ6n4HnObwD6", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 6, [[1, -268, [154], 155]], [0, "6aAtwOcupIAY4U0uVNHeAl", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 6, [[1, -269, [156], 157]], [0, "98YxxLvydMsJ09yBOM+PNX", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 6, [[1, -270, [158], 159]], [0, "ed+EW3FkhBY4wGepabH4Ey", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 6, [[1, -271, [160], 161]], [0, "68lpMaMsNPNI/tdKcxIxcn", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 6, [[1, -272, [162], 163]], [0, "74MyD8pfFD0JVlz0S3FtDl", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 24, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -273, [164], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 165, 166]], [0, "1fzks8DqtLlqjAJHl9CpGZ", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 24, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -274, [167], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 168, 169]], [0, "49tgJy/T5D5YBhp3aFGR1V", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 24, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -275, [170], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 171, 172]], [0, "39aD8nMFZOapFTH1aas7V7", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 24, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -276, [173], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 174, 175]], [0, "1fad8Cj6BA1ovpuuPrn6/5", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 52, [[5, 1, -277, [176], 177]], [0, "2b6KPEj69ANo4jH/DgvOWp", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 7, [[1, -278, [183], 184]], [0, "cdznYcV5ZFvKsZNyDWmHL9", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 7, [[1, -279, [185], 186]], [0, "46MCePdTRMsqYiajyzo0p0", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 7, [[1, -280, [187], 188]], [0, "5aflyr1BtGP4fYvZy9Aazm", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 7, [[1, -281, [189], 190]], [0, "c2hO/4ddJAb6BSruJAbvgl", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 7, [[1, -282, [191], 192]], [0, "33OfKQ9yNMAIc1Xf+iRGwb", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 7, [[1, -283, [193], 194]], [0, "99qeTY8PpLA6a31Rmq8+Nz", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 26, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -284, [195], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 196, 197]], [0, "d2Bj0oDXlC/JlulRPyFgf8", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 26, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -285, [198], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 199, 200]], [0, "38tNaB9W5OCIFgsXXebkI7", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 26, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -286, [201], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 202, 203]], [0, "cd8Xbv8qxJgrEWW+/aut8q", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 26, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -287, [204], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 205, 206]], [0, "cboJ8sDLxNfIGC9eJMjRB7", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 53, [[5, 1, -288, [207], 208]], [0, "c6IHgW5V9F04L7waicvJHG", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 8, [[1, -289, [214], 215]], [0, "50cgOf1XlOpJ2qLXYgG4HB", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 8, [[1, -290, [216], 217]], [0, "b54faurmFOX7xoOXk3QWpt", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 8, [[1, -291, [218], 219]], [0, "aeQqsy/UpPTK6LToYDK4QE", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 8, [[1, -292, [220], 221]], [0, "41huqybeRIEqggeuKOCXEe", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 8, [[1, -293, [222], 223]], [0, "15dV61Ty9AEIr++LcGrKz3", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 8, [[1, -294, [224], 225]], [0, "8azpspvcJBD5BqgTN9SzOm", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 28, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -295, [226], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 227, 228]], [0, "d9Z8AuXCJFQoHjqvIZ5DC/", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 28, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -296, [229], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 230, 231]], [0, "b72n5TtgtKJ6LomwtNc8/3", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 28, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -297, [232], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 233, 234]], [0, "d41aw/GPNIBaWTFvkIA7NV", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 28, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -298, [235], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 236, 237]], [0, "4fMTfxAnJHgIL5p55JuW2f", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 54, [[5, 1, -299, [238], 239]], [0, "78YeIDhzRJWoabOkXNpvd/", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [7, "7000504_dici_02", 55, [[1, -300, [245], 246]], [0, "29HyvYNMJJ/qS34zAqrJfF", 1, 0], [5, 162, 281], [0, 0.5, 0], [0, -12.569, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_02", 56, [[1, -301, [249], 250]], [0, "fbhIqG+qZFvYIXZF2tZ94y", 1, 0], [5, 162, 281], [0, 0.5, 0], [0, -12.569, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_03", 57, [[1, -302, [253], 254]], [0, "d7L/NyVXpC76/wH+PGz/iC", 1, 0], [5, 181, 283], [0, 0.5, 0], [-0.928, -11.133, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "7000504_dici_03", 58, [[1, -303, [257], 258]], [0, "0622UEJlZOJYWzTS7JtSev", 1, 0], [5, 181, 283], [0, 0.5, 0], [-0.928, -11.133, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_001", 9, [[1, -304, [261], 262]], [0, "76adO31J1MmrbsyxAWIj5P", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 9, [[1, -305, [263], 264]], [0, "1bsZP9TGBLY7KETiIx38RJ", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 9, [[1, -306, [265], 266]], [0, "fdw39fQIJLj6K4SNws1DmO", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 9, [[1, -307, [267], 268]], [0, "c21raXjn1F16LB/c3iM+8A", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 9, [[1, -308, [269], 270]], [0, "4andVC45dER5FlKdlAyIoE", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 9, [[1, -309, [271], 272]], [0, "83gE+4B3NIM50wfzmaAujk", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 32, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -310, [273], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 274, 275]], [0, "e7zu02SVpCVp3R7Bdsvm29", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 32, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -311, [276], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 277, 278]], [0, "50HDM60dFEnKlBKjCXUhN0", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 32, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -312, [279], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 280, 281]], [0, "63oS3XLTtKIZbXXb7x6YhB", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 32, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -313, [282], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 283, 284]], [0, "0eih65+QhHDZPWeZHJyxhg", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 59, [[5, 1, -314, [285], 286]], [0, "a2JE8YrvtLoYZjJhqHIMBN", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 10, [[1, -315, [292], 293]], [0, "ff9lqCw61IP6rWMpFukr5A", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 10, [[1, -316, [294], 295]], [0, "26bIHok6RAB4w1gJeRSbY9", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 10, [[1, -317, [296], 297]], [0, "f4UQw/eb9HGp57erBWkl+E", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 10, [[1, -318, [298], 299]], [0, "e3PTKqS/xPrK4HPz8bbPWI", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 10, [[1, -319, [300], 301]], [0, "99sRNl8DNKoZHlTL4/A/Oq", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 10, [[1, -320, [302], 303]], [0, "6bzT0o1wxLe5ERAh+/jmGh", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 34, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -321, [304], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 305, 306]], [0, "dbefbhyLBDw4M2Q97QVffV", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 34, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -322, [307], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 308, 309]], [0, "3dI0n6A3dKgq7305DA8rGh", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 34, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -323, [310], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 311, 312]], [0, "31JNbirTpGGIQXWO4UdVVU", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 34, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -324, [313], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 314, 315]], [0, "226WzrzW5In7p0tBVr9kN+", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 60, [[5, 1, -325, [316], 317]], [0, "8e8JezSRdAMJUiq3MAWQm8", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 11, [[1, -326, [323], 324]], [0, "24vbzpxdNP06ReZCkhRKR0", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 11, [[1, -327, [325], 326]], [0, "76momwxhlN06v0MmXMrw36", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 11, [[1, -328, [327], 328]], [0, "6dV8I/ufBPEYqKZibEYh8n", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 11, [[1, -329, [329], 330]], [0, "bfx3JDewlI7IuhZmouxP6o", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 11, [[1, -330, [331], 332]], [0, "91z9U4Nc9DxIbF1wXDcUwP", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 11, [[1, -331, [333], 334]], [0, "6eaBGm339Psq5iNu4kzNbS", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 36, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -332, [335], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 336, 337]], [0, "901tu1mANPjJrLdVNhqi3S", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 36, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -333, [338], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 339, 340]], [0, "4dkJpiUHNDHonCL7EdjFqJ", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 36, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -334, [341], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 342, 343]], [0, "92rerCgM1Py5BOing2gF2C", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 36, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -335, [344], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 345, 346]], [0, "b5fAmCzjBCCI+rzrkmIGeZ", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 61, [[5, 1, -336, [347], 348]], [0, "07fa5HnPZGsZsHNygcw6eH", 1, 0], [5, 127, 102], [0, 0.5, 0.3]], [2, "7000504_smoke_001", 12, [[1, -337, [354], 355]], [0, "34YvDynsVKMIlNRLxFwJbQ", 1, 0], [5, 52, 71], [-22.524, 31.036, 0, 0, 0, -0.26723837607825685, 0.963630453208623, 1, 1, 1], [1, 0, 0, -31]], [9, "7000504_smoke_002", 12, [[1, -338, [356], 357]], [0, "89B/SgJzRDZZg3gc1tSlib", 1, 0], [5, 52, 71], [-29.824, -6.04, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7000504_smoke_003", 12, [[1, -339, [358], 359]], [0, "cdTFw9iDJHkbBizl7x9Cj+", 1, 0], [5, 52, 71], [-18.749, -29.669, 0, 0, 0, 0.6504826300465859, 0.7595211307183471, 1, 1, 1], [1, 0, 0, 81.156]], [2, "7000504_smoke_004", 12, [[1, -340, [360], 361]], [0, "4fCxci/wdBqrqZopS5X6aB", 1, 0], [5, 52, 71], [14.345, 26.803, 0, 0, 0, 0.40928615402951063, 0.9124060741357062, -1, 1, 1], [1, 0, 0, 48.32]], [2, "7000504_smoke_005", 12, [[1, -341, [362], 363]], [0, "9cgS/w2tFA/I43UcOBVS8K", 1, 0], [5, 52, 71], [15.855, -0.755, 0, 0, 0, 0.5591929034707469, 0.8290375725550416, -1, 1, 1], [1, 0, 0, 68]], [2, "7000504_smoke_006", 12, [[1, -342, [364], 365]], [0, "ec+O2HsOZL6oNJPNc1s8rI", 1, 0], [5, 52, 71], [13.213, -29.823, 0, 0, 0, -0.3338068592337709, 0.9426414910921784, -1, 1, 1], [1, 0, 0, -39]], [3, "stone001", 38, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -343, [366], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, -1000, -8000], 367, 368]], [0, "bfMLT1HZpKtqve2mCpGoou", 1, 0], [-81.094, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone002", 38, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -344, [369], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 370, 371]], [0, "42z5sr+KpFkKkbi5QYfRxe", 1, 0], [-31.93, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone003", 38, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -345, [372], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 0, -8000], 373, 374]], [0, "35MdE8epNJ5Ja93zefbb1M", 1, 0], [23.38, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "stone004", 38, [[4, true, 1, 0, 0.2, 0, 50, 300, 2, 0, 0, 0, -346, [375], [4, 4294967295], [4, 0], [4, 3372220415], [4, 0], [0, 20, 50], [0, 1000, -8000], 376, 377]], [0, "45ByCIyYNJv5uJyyTLzyHq", 1, 0], [66.398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7000504_banyuan", 62, [[5, 1, -347, [378], 379]], [0, "69ImBnJi5O0Izs80UsWGHN", 1, 0], [5, 127, 102], [0, 0.5, 0.3]]], 0, [0, 5, 1, 0, 0, 1, 0, -1, 39, 0, -2, 42, 0, -3, 13, 0, -4, 14, 0, -5, 29, 0, -6, 30, 0, -1, 65, 0, -2, 66, 0, -3, 67, 0, -4, 68, 0, -5, 69, 0, -6, 70, 0, -1, 76, 0, -2, 77, 0, -3, 78, 0, -4, 79, 0, -5, 80, 0, -6, 81, 0, -1, 92, 0, -2, 93, 0, -3, 94, 0, -4, 95, 0, -5, 96, 0, -6, 97, 0, -1, 103, 0, -2, 104, 0, -3, 105, 0, -4, 106, 0, -5, 107, 0, -6, 108, 0, -1, 114, 0, -2, 115, 0, -3, 116, 0, -4, 117, 0, -5, 118, 0, -6, 119, 0, -1, 125, 0, -2, 126, 0, -3, 127, 0, -4, 128, 0, -5, 129, 0, -6, 130, 0, -1, 136, 0, -2, 137, 0, -3, 138, 0, -4, 139, 0, -5, 140, 0, -6, 141, 0, -1, 151, 0, -2, 152, 0, -3, 153, 0, -4, 154, 0, -5, 155, 0, -6, 156, 0, -1, 162, 0, -2, 163, 0, -3, 164, 0, -4, 165, 0, -5, 166, 0, -6, 167, 0, -1, 173, 0, -2, 174, 0, -3, 175, 0, -4, 176, 0, -5, 177, 0, -6, 178, 0, -1, 184, 0, -2, 185, 0, -3, 186, 0, -4, 187, 0, -5, 188, 0, -6, 189, 0, -1, 45, 0, -2, 46, 0, -3, 47, 0, -4, 48, 0, -5, 49, 0, -1, 19, 0, -2, 21, 0, -3, 23, 0, -4, 25, 0, -5, 27, 0, 0, 15, 0, -2, 16, 0, -3, 43, 0, -1, 71, 0, -2, 72, 0, -3, 73, 0, -4, 74, 0, 0, 17, 0, -2, 18, 0, -3, 44, 0, -1, 82, 0, -2, 83, 0, -3, 84, 0, -4, 85, 0, 0, 19, 0, -2, 20, 0, -3, 50, 0, -1, 98, 0, -2, 99, 0, -3, 100, 0, -4, 101, 0, 0, 21, 0, -2, 22, 0, -3, 51, 0, -1, 109, 0, -2, 110, 0, -3, 111, 0, -4, 112, 0, 0, 23, 0, -2, 24, 0, -3, 52, 0, -1, 120, 0, -2, 121, 0, -3, 122, 0, -4, 123, 0, 0, 25, 0, -2, 26, 0, -3, 53, 0, -1, 131, 0, -2, 132, 0, -3, 133, 0, -4, 134, 0, 0, 27, 0, -2, 28, 0, -3, 54, 0, -1, 142, 0, -2, 143, 0, -3, 144, 0, -4, 145, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, -4, 58, 0, -1, 31, 0, -2, 33, 0, -3, 35, 0, -4, 37, 0, 0, 31, 0, -2, 32, 0, -3, 59, 0, -1, 157, 0, -2, 158, 0, -3, 159, 0, -4, 160, 0, 0, 33, 0, -2, 34, 0, -3, 60, 0, -1, 168, 0, -2, 169, 0, -3, 170, 0, -4, 171, 0, 0, 35, 0, -2, 36, 0, -3, 61, 0, -1, 179, 0, -2, 180, 0, -3, 181, 0, -4, 182, 0, 0, 37, 0, -2, 38, 0, -3, 62, 0, -1, 190, 0, -2, 191, 0, -3, 192, 0, -4, 193, 0, -1, 40, 0, -2, 41, 0, 0, 40, 0, -1, 63, 0, 0, 41, 0, -1, 64, 0, 0, 43, 0, -1, 75, 0, 0, 44, 0, -1, 86, 0, 0, 45, 0, -1, 87, 0, 0, 46, 0, -1, 88, 0, 0, 47, 0, -1, 89, 0, 0, 48, 0, -1, 90, 0, 0, 49, 0, -1, 91, 0, 0, 50, 0, -1, 102, 0, 0, 51, 0, -1, 113, 0, 0, 52, 0, -1, 124, 0, 0, 53, 0, -1, 135, 0, 0, 54, 0, -1, 146, 0, 0, 55, 0, -1, 147, 0, 0, 56, 0, -1, 148, 0, 0, 57, 0, -1, 149, 0, 0, 58, 0, -1, 150, 0, 0, 59, 0, -1, 161, 0, 0, 60, 0, -1, 172, 0, 0, 61, 0, -1, 183, 0, 0, 62, 0, -1, 194, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 90, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, 0, 109, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, 0, 113, 0, 0, 114, 0, 0, 115, 0, 0, 116, 0, 0, 117, 0, 0, 118, 0, 0, 119, 0, 0, 120, 0, 0, 121, 0, 0, 122, 0, 0, 123, 0, 0, 124, 0, 0, 125, 0, 0, 126, 0, 0, 127, 0, 0, 128, 0, 0, 129, 0, 0, 130, 0, 0, 131, 0, 0, 132, 0, 0, 133, 0, 0, 134, 0, 0, 135, 0, 0, 136, 0, 0, 137, 0, 0, 138, 0, 0, 139, 0, 0, 140, 0, 0, 141, 0, 0, 142, 0, 0, 143, 0, 0, 144, 0, 0, 145, 0, 0, 146, 0, 0, 147, 0, 0, 148, 0, 0, 149, 0, 0, 150, 0, 0, 151, 0, 0, 152, 0, 0, 153, 0, 0, 154, 0, 0, 155, 0, 0, 156, 0, 0, 157, 0, 0, 158, 0, 0, 159, 0, 0, 160, 0, 0, 161, 0, 0, 162, 0, 0, 163, 0, 0, 164, 0, 0, 165, 0, 0, 166, 0, 0, 167, 0, 0, 168, 0, 0, 169, 0, 0, 170, 0, 0, 171, 0, 0, 172, 0, 0, 173, 0, 0, 174, 0, 0, 175, 0, 0, 176, 0, 0, 177, 0, 0, 178, 0, 0, 179, 0, 0, 180, 0, 0, 181, 0, 0, 182, 0, 0, 183, 0, 0, 184, 0, 0, 185, 0, 0, 186, 0, 0, 187, 0, 0, 188, 0, 0, 189, 0, 0, 190, 0, 0, 191, 0, 0, 192, 0, 0, 193, 0, 0, 194, 0, 6, 1, 2, 3, 15, 3, 3, 17, 4, 3, 19, 5, 3, 21, 6, 3, 23, 7, 3, 25, 8, 3, 27, 9, 3, 31, 10, 3, 33, 11, 3, 35, 12, 3, 37, 15, 3, 42, 17, 3, 42, 347], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 2, 1, 4, -1], [0, 11, 0, 11, 0, 4, 0, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 11, 0, 11, 0, 4, 0, 4, 0, 4, 0, 4, 0, 12, 0, 12, 0, 4, 0, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 11, 0, 11, 0, 11, 0, 11, 0, 4, 0, 4, 0, 4, 0, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 5, 0, 2, 6, 0, 2, 7, 0, 2, 8, 0, 3, 0, 3, 0, 9, 10, 13, 13]]