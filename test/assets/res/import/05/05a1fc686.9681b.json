[1, ["a2VjE28pJDhaqqpuJdSrpv", "ecpdLyjvZBwrvm+cedCcQy", "fddjE/tXJGN7lx2YNmXMIG", "da7poFxc1ERZTRLgdmQntW", "1aKKgwgKBPUpRiKoD9HAVe"], ["node", "root", "data", "_file", "_spriteFrame", "_defaultClip", "_textureSetter"], [["cc.Node", ["_name", "_components", "_prefab", "_children", "_contentSize", "_anchorPoint", "_trs", "_parent"], 2, 9, 4, 2, 5, 5, 7, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "life", "angleVar", "startSize", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "rotationIsDir", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "_file", "_spriteFrame"], -10, 1, 3, 8, 8, 8, 8, 6, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], -1]], [[3, 0, 2], [0, 0, 3, 1, 2, 4, 5, 6, 2], [0, 0, 7, 1, 2, 2], [4, 0, 1, 2, 3, 2], [1, 1, 2, 1], [1, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 14], [6, 0, 1, 2, 3, 5]], [[[[0, "10001"], [1, "10001", [-3], [[3, true, -2, [4], 3]], [4, -1, 0], [5, 212, 153], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 1.2, 1.2, 1.7]], [2, "effect_hit_fx_light_02_an", 1, [[6, 1, true, 1, 50, 0.13, 180, 25, 100, 1, 5, 0, 0, true, -4, [0], [4, 4294967295], [4, 0], [4, 4294967295], [4, 0], 1, 2]], [5, "6eE9ROeMlOmoiOc7mudr9t", 1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 2, 1, 4], [0, 0, 0, 0, 0], [-1, 3, 4, 5, -1], [1, 2, 3, 0, 0]], [[[7, "10001", 0.5, 30, {"paths": {"effect_hit_fx_light_02_an": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.03333333333333333, "value": 50}, {"frame": 0.13333333333333333, "value": 0}, {"frame": 0.5, "value": 0}]}}}}}]], 0, 0, [], [], []], [[{"name": "fx_hit_01", "rect": [4, 4, 116, 124], "offset": [-2, -2], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [6], [4]]]]