[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "18bGKOmaFGSY01wKhCWvbF", "9c/9KYVxBNMZ0CEtB77XZu", "d6XSwgFlZPp4E9ixHhr/PE", "000YD2iNZDkYjjStRTe4Ha", "abbI4FtUxOtba/WBsXCGqj"], ["node", "_spriteFrame", "_file", "root", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_trs", "_children", "_color", "_contentSize", "_anchorPoint"], 1, 4, 9, 1, 7, 2, 5, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angleVar", "startSize", "endSize", "endSizeVar", "_positionType", "speed", "speedVar", "tangentialAccel", "angle", "startSizeVar", "startSpin", "endSpin", "endSpinVar", "radialAccel", "startSpinVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_file", "_spriteFrame", "gravity"], -17, 1, 3, 8, 8, 8, 8, 5, 6, 6, 5], ["cc.Sprite", ["_dstBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [0, 0, 4, 6, 2, 5, 2], [0, 0, 1, 4, 3, 2, 7, 8, 9, 5, 3], [0, 0, 4, 3, 2, 5, 2], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 19, 9, 10, 11, 12, 20, 21, 22, 23, 24, 25, 26, 27, 28, 15], [3, 1, 2, 3, 1], [3, 0, 1, 2, 3, 2], [4, 0, 1, 2, 3, 4, 5, 6], [5, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 4, 3, 2, 2], [0, 0, 4, 3, 2, 7, 8, 9, 5, 2], [6, 0, 1, 2, 3, 2], [1, 1, 2, 1], [2, 0, 1, 2, 3, 4, 13, 5, 6, 14, 7, 8, 15, 16, 17, 9, 10, 11, 12, 18, 20, 21, 22, 23, 24, 25, 26, 29, 27, 28, 20]], [[[[7, "effect_item_ssp", 1.2, 30, 0.5, 2, [{}, "paths", 11, [{}, "effect_item_light/Item_sp_di", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.85, 0.85, 1.313]], [{"frame": 0.4}, "value", 8, [1, 1, 1, 1.313]], [{"frame": 0.5}, "value", 8, [1, 1.02, 1.02, 1.313]], [{"frame": 0.6}, "value", 8, [1, 1, 1, 1.313]], [{"frame": 1}, "value", 8, [1, 0.85, 0.85, 1.313]], [{"frame": 1.1}, "value", 8, [1, 0.8, 0.8, 1.313]], [{"frame": 1.2}, "value", 8, [1, 0.85, 0.85, 1.313]]], 11, 11, 11, 11, 11, 11, 11]]], "effect_item_light/Item_light_02", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1.1, 1.1, 1.313]], [{"frame": 0.4}, "value", 8, [1, 1.2, 1.2, 1.313]], [{"frame": 0.5}, "value", 8, [1, 1.22, 1.22, 1.313]], [{"frame": 0.6}, "value", 8, [1, 1.2, 1.2, 1.313]], [{"frame": 1}, "value", 8, [1, 1.1, 1.1, 1.313]], [{"frame": 1.1}, "value", 8, [1, 1, 1, 1.313]], [{"frame": 1.2}, "value", 8, [1, 1.1, 1.1, 1.313]]], 11, 11, 11, 11, 11, 11, 11]]], "effect_item_light-a/Item_light_02", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0, "curve": "linear"}, "value", 8, [1, 1.1, 1.1, 1.313]], [{"frame": 0.4}, "value", 8, [1, 1.2, 1.2, 1.313]], [{"frame": 0.5}, "value", 8, [1, 1.22, 1.22, 1.313]], [{"frame": 0.6}, "value", 8, [1, 1.2, 1.2, 1.313]], [{"frame": 1}, "value", 8, [1, 1.1, 1.1, 1.313]], [{"frame": 1.1}, "value", 8, [1, 1, 1, 1.313]], [{"frame": 1.2}, "value", 8, [1, 1.1, 1.1, 1.313]]], 11, 11, 11, 11, 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[8, "effect_item_ssp"], [9, "effect_item_ssp", [-3, -4, -5, -6, -7], [[12, true, -2, [18], 17]], [13, -1, 0]], [1, "effect_item_light-a", 1, [-8, -9], [0, "c3g3bKdfhAGLtNRGxn9v9j", 1, 0], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1.5]], [1, "effect_item_light", 1, [-10, -11], [0, "aft0pwd11N9oInZaHW/Crc", 1, 0], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1.5]], [1, "effect_item__lizi", 1, [-12], [0, "6aUHMVIhFP97uUCAXjFHyA", 1, 0], [0, 0, 0, 0, 0, 0, 1, 2.5, 2.5, 2.5]], [10, "lizi", 4, [[14, true, 8, 6, 0.8, 0.05, 360, 360, 6, 0.5, 3, 1, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 30, 0, 0, -1071.0499878, -13, [0], [4, 3623878655], [4, 0], [4, 3623878655], [4, 0], [0, 12, 3], [0, 0, 1050], 1, 2]], [0, "d2NOojn2VME6zNlFC7orL0", 1, 0]], [2, "Item_sp_di", 178, 2, [[5, -14, [3], 4]], [0, "62dpqpDbpGe6dssxJhkQjD", 1, 0], [4, 4291875071], [5, 96, 67], [0, 0.5, 0.53], [0.32200000000000273, 0.9539999999999509, 0, 0, 0, 0, 1, 0.85, 0.85, 0.85]], [11, "Item_light_02", 2, [[5, -15, [5], 6]], [0, "63MUa0kcdKaqN7EsCynjeh", 1, 0], [4, 4286743274], [5, 49, 252], [0, 0.5, 0.096], [0, 6.18799999999995, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Item_sp_di", 67, 3, [[6, 1, -16, [7], 8]], [0, "c3HmaWPxxLHJgF7yOrxtNG", 1, 0], [4, 4290296055], [5, 96, 67], [0, 0.5, 0.53], [0.32200000000000273, 0.9539999999999509, 0, 0, 0, 0, 1, 0.85, 0.85, 0.85]], [2, "Item_light_02", 108, 3, [[6, 1, -17, [9], 10]], [0, "74QmBdyWNP7bufRXEqm+qC", 1, 0], [4, 4290230258], [5, 49, 252], [0, 0.5, 0.096], [0, 6.18799999999995, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "effect_item__star", 1, [[4, true, 6, 3, 0.6, 0.1, 5, 20, 10, 6, 60, 1, 210, 40, 0, -18, [11], [4, 3788558587], [4, 335544320], [4, 3101153279], [4, 167772160], [0, 15, 50], 12, 13]], [0, "6bE258SL1KTJIDmwjbhdn9", 1, 0], [21.991, 46.182, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]], [3, "effect_item__star02", 1, [[4, true, 6, 3, 0.6, 0.1, 5, 20, 10, 6, 60, 1, 210, 40, 0, -19, [14], [4, 3889682431], [4, 335544320], [4, 3788755966], [4, 167772160], [0, 15, 50], 15, 16]], [0, "05beb5K/VKmoappqC5D3Pi", 1, 0], [-29.472, 46.182, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 3, 0, -4, 10, 0, -5, 11, 0, -1, 6, 0, -2, 7, 0, -1, 8, 0, -2, 9, 0, -1, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 4, 1, 19], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 1, -1, 2, 1, 5, -1], [0, 1, 6, 0, 2, 0, 3, 0, 2, 0, 3, 0, 1, 4, 0, 1, 4, 5, 5]]]]