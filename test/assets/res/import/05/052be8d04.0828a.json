[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "75gg0cRUVIHIZezqDFKC67", "47LfXTNedBhJ5sf0G+5l7j", "08RQrqX4VDa43zX+oSCmp7", "22O2FZPYFNcronY8CSHrqR"], ["node", "_file", "_spriteFrame", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_trs", "_parent", "_children", "_contentSize", "_color", "_anchorPoint"], 1, 4, 9, 7, 1, 2, 5, 5, 5], ["cc.Sprite", ["_dstBlendFactor", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "life", "angleVar", "startSize", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "angle", "startSizeVar", "startSpin", "endSpin", "endSpinVar", "radialAccel", "lifeVar", "endSizeVar", "startSpinVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_file", "gravity", "_spriteFrame"], -18, 1, 3, 8, 8, 8, 8, 5, 6, 5, 6], "cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips"], 3, 1, 3]], [[2, 0, 1, 2, 2], [0, 0, 1, 5, 3, 2, 8, 7, 9, 4, 3], [1, 0, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 6], [6, 0, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 5, 6, 2, 4, 2], [0, 0, 5, 3, 2, 2], [0, 0, 5, 3, 2, 4, 2], [0, 0, 5, 3, 2, 7, 4, 2], [7, 0, 1, 1], [2, 1, 2, 1], [1, 0, 1, 2, 3, 4, 4], [1, 3, 4, 5, 1], [3, 0, 1, 2, 3, 4, 12, 5, 6, 13, 7, 14, 15, 16, 8, 9, 10, 11, 17, 21, 22, 23, 24, 25, 26, 27, 29, 28, 30, 19], [3, 0, 1, 2, 3, 4, 18, 5, 6, 7, 19, 20, 8, 9, 10, 11, 21, 22, 23, 24, 25, 26, 27, 28, 16]], [[[{"name": "Keyword_3", "rect": [0, 0, 128, 128], "offset": [0, 0], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [3], [2]], [[[3, "effect_item_sp", 0.7666666666666667, 30, 0.5, 2, [{}, "paths", 11, [{}, "effect_item_sp_light", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.4}, "value", 8, [1, 0.9, 0.9, 1]], [{"frame": 0.7666666666666667}, "value", 8, [1, 1, 1, 1]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[4, "effect_item_sp"], [5, "effect_item_sp", [-3, -4, -5, -6, -7, -8], [[10, -2, [10]]], [11, -1, 0], [-737.95, -535.692, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "item_light_001", 200, 1, [[2, 1, -9, [0]]], [0, "55RkrLkWVIf5qBJTTuFX8i", 1, 0], [4, 4278212607], [5, 128, 243], [0, 0.5, 0.15], [0, 43.891, 0, 0, 0, 0, 1, 0.5, 3, 1]], [1, "item_light_001", 150, 1, [[2, 1, -10, [1]]], [0, "05xYF3YrBDBZg2u2VN6d5N", 1, 0], [4, 4278212607], [5, 128, 243], [0, 0.5, 0.15], [0, -1.406, 0, 0, 0, 0, 1, 3, 2, 1]], [1, "effect_item_sp_light", 200, 1, [[12, 1, 0, false, -11, [2]]], [0, "56XKmM2/ZErZYGc7eXN/gv", 1, 0], [4, 4278212607], [5, 64, 128], [0, 0.5, 0.2], [0, 0, 0, 0, 0, 0, 1, 5, 5, 1]], [6, "effect_item_sp_lizi", 1, [-12], [0, "beWb70oOBGTpQmzTOX4WaB", 1, 0], [0, 0, 0, 0, 0, 0, 1, 5, 5, 10]], [7, "New Particle", 5, [[14, 1, true, 8, 6, 0.8, 360, 360, 55, 5, 30, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 30, 0, 0, -1071.0499878, -13, [3], [4, 2740373706], [4, 3321888768], [4, 2517868973], [4, 838860800], [0, 20, 3], [0, 0, 1000], 4, 5]], [0, "85yHSfPqJFeqQyImRt7GvW", 1, 0]], [8, "effect_item_sp_star", 1, [[15, 1, true, 6, 5, 0.4, 0.1, 5, 200, 150, 20, 60, 1, 400, 0, 0, -14, [6], [4, 1677764863], [4, 335544320], [4, 503367679], [4, 167772160], [0, 80, 80], 7]], [0, "32Hj9Dri1Aq4mOG8Ypt2eP", 1, 0], [0, 91.331, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Keyword_3", 1, [[13, -15, [8], 9]], [0, "dcDBnFAJNF9Lyi3pOdebRG", 1, 0], [5, 128, 128], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 7, 0, -6, 8, 0, 0, 2, 0, 0, 3, 0, 0, 4, 0, -1, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 5, 1, 15], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, -1, 1, 2, -1, 1, -1, 2, -1], [0, 0, 0, 0, 1, 3, 0, 1, 0, 4, 5]]]]