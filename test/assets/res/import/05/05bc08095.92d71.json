[1, ["ecpdLyjvZBwrvm+cedCcQy", "e5/fZATxlISoMz2b6W0H0z", "daTCH72YlNn5/yPLQx0el5", "b2aHrECZ5APKGS/0d2hvT1", "2dj8lMN9pFsagxTPo/FuO5", "27HHAiVlJD1pjMWPgI8C+2", "55HMwO1UFJvIg/CpddAy61"], ["node", "_spriteFrame", "root", "_file", "data", "_parent", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_prefab", "_parent", "_components", "_children", "_trs", "_contentSize", "_color", "_anchorPoint"], 1, 4, 1, 9, 2, 7, 5, 5, 5], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "angle", "angleVar", "endSize", "speed", "speedVar", "tangentialAccel", "_dstBlendFactor", "lifeVar", "startSizeVar", "_positionType", "duration", "startSize", "startSpinVar", "startSpin", "endSpin", "endSpinVar", "tangentialAccelVar", "radialAccel", "radialAccelVar", "endSizeVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "_spriteFrame", "gravity", "_file"], -21, 1, 3, 8, 8, 8, 8, 5, 6, 5, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.<PERSON>", ["node", "_offset", "_size"], 3, 1, 5, 5], ["cc.Sprite", ["_dstBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6]], [[2, 0, 1, 2], [0, 0, 3, 4, 2, 2], [6, 0, 1, 2, 3, 2], [0, 0, 1, 3, 4, 2, 8, 7, 9, 6, 3], [0, 0, 3, 4, 2, 6, 2], [1, 10, 0, 1, 14, 2, 3, 11, 4, 5, 15, 12, 6, 23, 16, 13, 7, 8, 9, 24, 25, 26, 27, 28, 29, 30, 31, 19], [4, 0, 2], [0, 0, 5, 2, 2], [0, 0, 3, 5, 2, 2], [0, 0, 3, 5, 2, 6, 2], [0, 0, 5, 4, 2, 7, 2], [0, 0, 1, 3, 4, 2, 8, 7, 6, 3], [2, 1, 2, 1], [5, 0, 1, 2, 1], [1, 10, 0, 1, 2, 3, 11, 4, 5, 12, 6, 17, 18, 19, 13, 7, 8, 9, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 31, 21], [1, 10, 0, 1, 2, 3, 11, 4, 5, 12, 6, 17, 18, 19, 7, 8, 9, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 31, 20], [1, 10, 0, 1, 14, 2, 3, 4, 5, 15, 6, 16, 13, 7, 8, 9, 24, 25, 26, 27, 28, 29, 16], [1, 0, 1, 14, 2, 3, 11, 4, 5, 15, 12, 6, 23, 16, 13, 7, 8, 9, 24, 25, 26, 27, 28, 29, 30, 31, 18]], [[[[6, "bullet_nw01"], [7, "effect_nw_01", [-2, -3], [0, "ebgTIGsVVA3pgI6okcZLL6", -1]], [8, "effect_nw_01_light_1", 1, [-4, -5, -6, -7, -8], [0, "fc6dO11BVNC40MVH9BOrmW", 1]], [9, "effect_nw_01_boom", 1, [-9, -10, -11, -12], [0, "79dQG36cNNWrwKNHu3x8SU", 1], [0, 19.476, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "ball", [1], [[13, -14, [0, -0.7, 30.3], [5, 95.2, 44]]], [12, -13, 0], [5, 43, 44]], [11, "effect_nw_01_glow", 200, 2, [[2, 1, -15, [0], 1]], [0, "5141J4XghAS5UeNk3ls2S1", 1], [4, 4281654523], [5, 256, 256], [0, 17.85, 0, 0, 0, 0, 1, 1, 0.3, 1]], [3, "effect_nw_01_light_1", 225, 2, [[2, 1, -16, [2], 3]], [0, "7a35vNKDZKDIAxgdyjlXkO", 1], [4, 4278243582], [5, 110, 638], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 1, 0.5, 1]], [3, "effect_nw_01_light_2", 150, 2, [[2, 1, -17, [4], 5]], [0, "6ejo2UFRRDOIy0V9c9hr5M", 1], [4, 4287294455], [5, 110, 638], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 0.7, 0.5, 1]], [4, "effect_nw_01_lizi_1", 2, [[14, 1, true, 25, 20, 0.5, 0.2, 360, 360, 20, 20, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 200, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -18, [6], [4, 4282503931], [4, 939524096], [4, 1006673837], [4, 452984832], [0, 50, 50], [0, 0.25, 1000], 7, 8]], [0, "5eQByyWgVP/6yy6G4961vd", 1], [0, 48.842, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "effect_nw_01_lizi_2", 2, [[15, 1, true, 25, 20, 0.5, 0.2, 360, 360, 20, 20, -47.369998931884766, -47.369998931884766, -142.11000061035156, 200, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -19, [9], [4, 4282503931], [4, 939524096], [4, 1006673837], [4, 452984832], [0, 50, 50], [0, 0.25, 1000], 10, 11]], [0, "19ewp2WhdCWb55+lA8f0k5", 1], [0, 48.842, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "effect_nw_01_flash", 3, [[16, 1, true, 2, 0.1, 500, 0.2, 0, 900, 100, 500, 10, 1, 0, 0, 0, -20, [12], [4, 4294967295], [4, 0], [4, 637534207], [4, 0]]], [0, "adM4qLNVxMQpGZRsVeAiXa", 1]], [1, "effect_nw_01_smoke_ab", 3, [[17, true, 25, 0.1, 500, 0.2, 0.1, 0, 900, 70, 10, 100, 7, 200, 1, 700, 100, 0, -21, [13], [4, 1685945725], [4, 0], [4, 9211020], [4, 0], [0, 0, 50], 14]], [0, "b2RWXx1KpPEaUsdQeyL3TZ", 1]], [1, "effect_nw_01_smoke", 3, [[5, 1, true, 25, 0.1, 500, 0.2, 0.1, 0, 900, 70, 10, 100, 7, 200, 1, 600, 100, 0, -22, [15], [4, 922746879], [4, 0], [4, 16777215], [4, 0], [0, 0, 50], 16]], [0, "ddejbVDkVJCaj28w9STUra", 1]], [1, "effect_nw_01_lizi", 3, [[5, 1, true, 50, 0.1, 500, 0.3, 0.2, 0, 900, 70, 10, 30, 7, 200, 1, 200, 600, 0, -23, [17], [4, 3359564286], [4, 0], [4, 339534588], [4, 0], [0, 0, 50], 18]], [0, "8cAx1hHv1D341tKP7hcHI6", 1]]], 0, [0, 2, 1, 0, -1, 2, 0, -2, 3, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, 2, 4, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 4, 4, 1, 5, 4, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 3, 1, -1, 3, 1, -1, -1, 1, -1, 1, -1, 1], [0, 5, 0, 2, 0, 2, 0, 3, 1, 0, 3, 1, 0, 0, 4, 0, 4, 0, 1]], [[{"name": "bingdongshexian_003", "rect": [12, 0, 110, 638], "offset": [0.5, 3.5], "originalSize": [133, 645], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [6], [6]]]]