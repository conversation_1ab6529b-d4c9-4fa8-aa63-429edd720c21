[1, ["ecpdLyjvZBwrvm+cedCcQy", "afzxBNHPhOELWzVXtfzOvW", "47OT/ciDpCGqNMacfrV7aU"], ["node", "_spriteFrame", "root", "treeCut", "body", "skin", "data"], [["cc.Node", ["_name", "_groupIndex", "_prefab", "_children", "_components", "_parent", "_contentSize", "_anchorPoint", "_trs"], 1, 4, 2, 9, 1, 5, 5, 7], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.RigidBody", ["_type", "_gravityScale", "_fixedRotation", "bullet", "node"], -1, 1], ["cc.PhysicsCircleCollider", ["_density", "_sensor", "_radius", "node", "_offset"], 0, 1, 5], ["05ac4q6CoBK1IrYUV/Cy6l3", ["node", "skin", "body", "treeCut"], 3, 1, 1, 1, 1]], [[2, 0, 1, 2, 2], [3, 0, 2], [0, 0, 1, 3, 4, 2, 3], [0, 0, 5, 3, 2, 2], [0, 0, 5, 4, 2, 6, 7, 8, 2], [4, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 1, 2, 1], [1, 0, 1, 1], [2, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5], [6, 0, 1, 2, 3, 4, 4], [7, 0, 1, 2, 3, 1]], [[1, "4000001"], [2, "tree", 3, [-8], [[9, 0, 0, true, true, -2], [10, 0, true, 8, -3, [0, 0, 8.6]], [11, -7, -6, -5, -4]], [8, -1, 0]], [3, "Body", 1, [-9, -10], [0, "6fSJ9s5uZJRKO8m2i12zj0", 1, 0]], [4, "treecut", 2, [[6, -11, [0], 1]], [0, "456g16YZlBebmCpu56tgSo", 1, 0], [5, 80, 40], [0, 0.5, 0], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "skin", 2, [-12], [0, "15InNVVkxJv6dufVc2n0kf", 1, 0], [5, 96, 73], [0, 0.5, 0], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 4, [2]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 3, 3, 0, 4, 2, 0, 5, 5, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, -2, 4, 0, 0, 3, 0, -1, 5, 0, 6, 1, 12], [0, 0, 0, 5], [-1, 1, -1, 1], [0, 1, 0, 2]]