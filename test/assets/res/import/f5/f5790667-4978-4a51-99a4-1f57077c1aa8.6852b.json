[1, ["e2oDh1GAFN5Y8Oy26+B6sE", "77pxIfKeZBsJzK9/badByA", "39qwxzxhtLbpKEeOsLuMRy", "9cnUtCRHxPo7fcEyggTpqF", "7bcF7jxz9ITay26nbwOd+e", "3ebmcclUROoKHVeXmNQ88k", "a6+qweq1dLaYU/R0n8ktCl", "b2Lc5jdGpNoJ4VAXKs/3dk", "49iXt4fJhP2L9HLaZbMRyp", "abmTsFRzRMqZxL9GMr6j2k", "37ek+C2mtMVbA7f6p1FfrX", "b41OWAPtVDAbxW3PYlflvO", "41dhT95pJFTa0rQ3mf6A52"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "mine", 0.375, 24, 0.5, 2, [{"props": {"y": [{"frame": 0, "value": -20.9}]}}, "paths", 11, [{}, "futou", 11, [{"props": {"position": [{"frame": 0, "curve": "constant", "value": [9.806, 29.218, 0]}, {"frame": 0.041666666666666664, "curve": "constant", "value": [9.419, 54.76, 0]}, {"frame": 0.08333333333333333, "curve": "constant", "value": [61.389, 61.593, 0]}, {"frame": 0.125, "curve": "constant", "value": [70.275, 46.564, 0]}, {"frame": 0.16666666666666666, "curve": "constant", "value": [72.067, 40.508, 0]}, {"frame": 0.20833333333333334, "curve": "constant", "value": [1.63, 99.489, 0]}, {"frame": 0.25, "curve": "constant", "value": [-24.309, 27.401, 0]}, {"frame": 0.2916666666666667, "curve": "constant", "value": [-32.636, 30.86, 0]}, {"frame": 0.3333333333333333, "value": [-26.139, 28.822, 0]}], "angle": [{"frame": 0, "value": 54.778999999999996, "curve": "constant"}, {"frame": 0.041666666666666664, "value": -10, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -74.961, "curve": "constant"}, {"frame": 0.125, "value": -111.61, "curve": "constant"}, {"frame": 0.16666666666666666, "value": -122.782, "curve": "constant"}, {"frame": 0.20833333333333334, "value": -6.176000000000002, "curve": "constant"}, {"frame": 0.25, "value": 88.027, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 72.42099999999999, "curve": "constant"}, {"frame": 0.3333333333333333, "value": 81.15299999999999}], "active": [{"frame": 0, "value": true}], "anchorX": [{"frame": 0, "value": 0.42}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0]], 11]]]], "futou/shou", 11, [{"props": {"position": [{"frame": 0, "value": [24.626, -26.576, 0]}], "active": [{"frame": 0, "value": true}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 1]], 11]]]], "futou/wei", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.25, "value": 255, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0.20833333333333334, "value": 12.253, "curve": "constant"}, {"frame": 0.25, "value": -87.206}], "position": [{"frame": 0.20833333333333334, "curve": "constant", "value": [79.325, 25.135, 0]}, {"frame": 0.25, "value": [75.546, 32.245, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334, "curve": "constant"}, "value", 8, [1, 1.7, 1.238, 1]], [{"frame": 0.25}, "value", 8, [1, 1.2, 1.67, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 2], [{"frame": 0.25}, "value", 6, 3]], 11, 11]]]]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 4], [{"frame": 0.041666666666666664}, "value", 6, 5], [{"frame": 0.08333333333333333}, "value", 6, 6], [{"frame": 0.125}, "value", 6, 7], [{"frame": 0.16666666666666666}, "value", 6, 8], [{"frame": 0.20833333333333334}, "value", 6, 9], [{"frame": 0.25}, "value", 6, 10], [{"frame": 0.2916666666666667}, "value", 6, 11], [{"frame": 0.3333333333333333}, "value", 6, 12]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]