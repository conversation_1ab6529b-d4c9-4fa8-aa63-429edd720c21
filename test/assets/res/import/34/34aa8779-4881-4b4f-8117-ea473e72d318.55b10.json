[1, ["ecpdLyjvZBwrvm+cedCcQy", "9eV9KiDMlLA5uVqrGXTiAj", "5eKMjzQP5CUYQZVdDj9+aw", "0d9h25SoFPWqywzH1PGF42", "30zFl0n3tFmYHnvGH9CWyO", "daFql5uMRK+blSWzJHB9x2", "d9dkwSdGNJFK8Jvz7lzizp"], ["node", "root", "_spriteFrame", "asset", "_N$file", "data", "_parent"], [["cc.Node", ["_name", "_active", "_prefab", "_parent", "_trs", "_children", "_components", "_contentSize", "_anchorPoint"], 1, 4, 1, 7, 2, 9, 5, 5], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Prefab", ["_name"], 2], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["root", "asset"], 3, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["5870fdQEepMcJmI9UjJ0MkU", ["node"], 3, 1]], [[1, 0, 2, 2], [0, 0, 3, 2, 4, 2], [3, 0, 1, 2, 3, 4, 3], [1, 0, 1, 2, 3, 3], [0, 0, 3, 6, 2, 7, 8, 4, 2], [0, 0, 3, 5, 2, 4, 2], [2, 0, 2], [0, 0, 5, 6, 2, 7, 2], [0, 0, 5, 2, 2], [0, 0, 3, 5, 2, 2], [0, 0, 1, 3, 6, 2, 7, 8, 4, 3], [0, 0, 3, 6, 2, 7, 4, 2], [4, 0, 1, 1], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [6, 0, 1, 2, 2], [7, 0, 1]], [[6, "building_teleport"], [8, "base_home_state", [-2, -3, -4, -5], [0, "57QHiiolJG1ZzaVT08PTbo", -1]], [7, "node", [1], [[15, -7]], [12, -6, 0], [5, 400, 240]], [9, "active", 1, [-8, -9, -10], [0, "53aGumP/hDko6zV9HbXZmX", 1]], [5, "disabled", 1, [-11, -12], [0, "ee5LHZ/KlCVIOLSZVMS+V/", 1], [0, -0.717, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "labBuildName", 1, [[13, "木场", 26, 45, false, 1, 1, -13, [9], 10], [14, 2, -14, [4, 4278190080]]], [0, "31qdgLUPFAkbw5esi5x3D7", 1], [5, 56, 60.7], [0, 2.667, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sp", false, 3, [[2, 2, false, -15, [0], 1]], [0, "3c8G+QZBdIfZ1tmhMlfcG0", 1], [5, 436, 346], [0, 0.5, 0], [0, -174.973, 0, 0, 0, 0, 1, 1, 1, 1.5]], [4, "di<PERSON>o", 3, [[2, 2, false, -16, [2], 3]], [0, "d5ap8mZIFB8YcCtgC1u/pG", 1], [5, 163, 130], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1.5]], [1, "base_home_active", 3, [3, "65YkZkwutIrqDdIepWq1gm", true, -17, 4], [0, 237.351, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "sp", 4, [[2, 2, false, -18, [5], 6]], [0, "b37G74JrRLppjhfZXBmSWR", 1], [5, 200, 152], [0, 0.5, 0], [0, -7.37, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "base_home_disabled", 4, [3, "99x5C87x5FvrOWhKrdkQA3", true, -19, 7], [0, 251.8, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ready", 1, [-20], [0, "f5029+1N5Eda0xP4znbj66", 1], [0, 56.977, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "base_flag", 11, [3, "a4hAiBOvBNo6QucTe5kZwL", true, -21, 8], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 1, 1, 0, -1, 3, 0, -2, 4, 0, -3, 11, 0, -4, 5, 0, 1, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -1, 9, 0, -2, 10, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 1, 8, 0, 0, 9, 0, 1, 10, 0, -1, 12, 0, 1, 12, 0, 5, 2, 1, 6, 2, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, 2, 3, -1, 2, 3, 3, -1, 4], [0, 1, 0, 1, 2, 0, 3, 4, 5, 0, 6]]