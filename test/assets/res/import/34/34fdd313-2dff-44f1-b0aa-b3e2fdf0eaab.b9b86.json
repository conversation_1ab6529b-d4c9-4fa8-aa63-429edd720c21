[1, ["51GFJFwyFEZJVu1hd9OBWe", "1emBwzd8ZElop5R/eRNEWO", "b9Mo1ID8lK26Mwi+0bB/MJ", "96Rrnbw75AQ5mQTEWbeG6a", "fa8XzmyxFO3L1xd3sqyJam"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], 0, 11]], [[0, 0, 1, 2, 3, 4]], [[0, "10015", 0.25, 24, [{}, "paths", 11, [{"hit/guangyun": {"props": {"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 100}, {"frame": 0.20833333333333334, "value": 0}]}}, "hit": {"props": {"opacity": [{"frame": 0, "value": 255}]}}}, "hit/dian", 11, [{"props": {"opacity": [{"frame": 0, "value": 0, "curve": "constant"}, {"frame": 0.041666666666666664, "value": 255}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.25, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.041666666666666664}, "value", 6, 0], [{"frame": 0.08333333333333333}, "value", 6, 1], [{"frame": 0.125}, "value", 6, 2], [{"frame": 0.16666666666666666}, "value", 6, 3], [{"frame": 0.20833333333333334}, "value", 6, 4]], 11, 11, 11, 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 1, 2, 3, 4]]