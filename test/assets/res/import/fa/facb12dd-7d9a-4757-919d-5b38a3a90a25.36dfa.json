[1, ["927OBafolJ2pkmys0RPI5+", "5bp/iJ+ZlBp4+hZIwwZqSA", "162q3OyadO5qXI69JlMQGU", "35AaBXBmtOKK/cgY2Ovmzq", "489GSdM4lGcKpXDMuGrNWn", "1c2AVMFMdCZJKZp4vxoUfI", "e0HbokNVNGLbEzDSbf3ryS", "33U2S8RShC3LbT1MBd8+7q", "f3IxQhKYNF946oLoMr5DNk", "cfarP44a1K/IrnHuRpY9nE", "b6AM56ntJPMp+xS6b6xWAF", "4eB1xyEmNP0pHJ8sgywX0N", "efMVhMIHdBg5DxqS6zKGwG", "7flCZKxhdDN6MeI/Fo2m4I", "24yw7kQYBO+I/IUn9+6X+A", "74PM8jGbtPlYDBIKO8h1PG", "deEuLhAglGmYfN86Lh9z/W", "59ZNhHz4lAVq7JA4XFdk8j", "1aYuURnCRIba+LVfo/kuLZ", "0awdSm3/hOKLlOnAv91FeI", "306RKs9ZZG/bIyLL0vKX0U", "f8rbTZIw9OpptZx5obvK6R", "fdFx4Wm9xIsakMmleOhN37", "9a5yOa+ZtN8oEaR6VUXBNh", "1asriqP41Pl42/UGym8Q6e", "07bdRJC8NP9bRQe2zQgPNu", "0duG6euKVHeK6xANNj5itd", "2eh/rAqltJXrSRfuYMb+gH", "cdSaepkhpEu5/oKtUzqdgD", "4dwFWRme1K9LBWPvKbkB9s", "e34vllDrpKnp/vh6UFwhuS", "e54jPKupdJWI0ymj/PWL2b", "e5OF0G4UVLRrdeVwkU4bVb"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "skill_xy", 3.125, 24, 2, [{}, "paths", 11, [{"effect_ty_jz_xl/effect_ty_jz_xl_lizi": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.7083333333333334, "value": 0}, {"frame": 0.75, "value": 45, "curve": "constant"}, {"frame": 1.25, "value": 0}]}}}, "effect_ty_jz_xl/effect_ty_jz_xl_lizi2": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 0.7083333333333334, "value": 0}, {"frame": 0.75, "value": 45, "curve": "constant"}, {"frame": 1.25, "value": 0}]}}}, "effect_ty_jz_xl/effect_ty_jz_xl_flash": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.2916666666666667, "value": 0}, {"frame": 1.3333333333333333, "value": 50}, {"frame": 1.375, "value": 0}]}}}, "effect_ty_jz_xl/effect_ty_jz_xl_flas_lizi": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.2916666666666667, "value": 0}, {"frame": 1.3333333333333333, "value": 1000}, {"frame": 1.375, "value": 0}]}}}, "effect_xy_02_jian_down01/effect_xy_blade_01": {"props": {"y": [{"frame": 1.625, "value": 1973.848}, {"frame": 1.7083333333333333, "value": -126.646}], "opacity": [{"frame": 1.7083333333333333, "value": 255, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 0}]}}, "effect_xy_02_jian_down02/effect_xy_blade_01": {"props": {"y": [{"frame": 1.6666666666666667, "value": 1973.848}, {"frame": 1.75, "value": -126.646}], "opacity": [{"frame": 1.75, "value": 255, "curve": "constant"}, {"frame": 1.8333333333333333, "value": 0}]}}, "effect_xy_02_jian_down03/effect_xy_blade_01": {"props": {"y": [{"frame": 1.7083333333333333, "value": 1973.848}, {"frame": 1.7916666666666667, "value": -126.646}], "opacity": [{"frame": 1.7916666666666667, "value": 255, "curve": "constant"}, {"frame": 1.875, "value": 0}]}}, "effect_xy_02_jian_down02": {"props": {"position": [{"frame": 1.9166666666666667, "value": [127.723, 154.615, 0]}]}}, "effect_xy_02_jian_down03": {"props": {"position": [{"frame": 1.875, "value": [-222.357, -227.545, 0]}]}}, "effect_xy_02_jian_down05/effect_xy_blade_01": {"props": {"y": [{"frame": 1.7083333333333333, "value": 1973.848}, {"frame": 1.7916666666666667, "value": -126.646}], "opacity": [{"frame": 1.7916666666666667, "value": 255, "curve": "constant"}, {"frame": 1.875, "value": 0}]}}, "effect_xy_02_jian_down04/effect_xy_blade_01": {"props": {"y": [{"frame": 1.625, "value": 1973.848}, {"frame": 1.7083333333333333, "value": -126.646}], "opacity": [{"frame": 1.7083333333333333, "value": 255, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 0}]}}, "effect_xy_02_henji_001": {"props": {"opacity": [{"frame": 1.625, "value": 0}, {"frame": 1.6666666666666667, "value": 255, "curve": "constant"}]}}, "effect_xy_02_henji_002": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0}, {"frame": 1.7083333333333333, "value": 255, "curve": "constant"}]}}, "effect_xy_02_henji_003": {"props": {"opacity": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 255, "curve": "constant"}]}}, "effect_xy_02_henji_004": {"props": {"opacity": [{"frame": 1.625, "value": 0}, {"frame": 1.6666666666666667, "value": 255, "curve": "constant"}]}}, "effect_xy_02_henji_005": {"props": {"opacity": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 255, "curve": "constant"}]}}, "effect_xy_02_jian_down01/effect_xy_blade_02": {"props": {"y": [{"frame": 1.7083333333333333, "value": 84.489}], "opacity": [{"frame": 1.625, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 255}, {"frame": 2.3333333333333335, "value": 255}, {"frame": 2.4583333333333335, "value": 0}]}}, "effect_xy_02_jian_down02/effect_xy_blade_02": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2.375, "value": 255}, {"frame": 2.5, "value": 0}], "y": [{"frame": 1.75, "value": 84.489}]}}, "effect_xy_02_jian_down03/effect_xy_blade_02": {"props": {"opacity": [{"frame": 1.7083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 255}, {"frame": 2.4166666666666665, "value": 255}, {"frame": 2.5416666666666665, "value": 0}], "y": [{"frame": 1.7916666666666667, "value": 84.489}]}}, "effect_xy_02_jian_down04/effect_xy_blade_02": {"props": {"opacity": [{"frame": 1.625, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 255}, {"frame": 2.3333333333333335, "value": 255}, {"frame": 2.4583333333333335, "value": 0}], "y": [{"frame": 1.7083333333333333, "value": 84.489}]}}, "effect_xy_02_jian_down05/effect_xy_blade_02": {"props": {"opacity": [{"frame": 1.7083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 255}, {"frame": 2.4166666666666665, "value": 255}, {"frame": 2.5416666666666665, "value": 0}], "y": [{"frame": 1.7916666666666667, "value": 84.489}]}}, "effect_xy_02_jian_down06/effect_xy_blade_01": {"props": {"y": [{"frame": 1.7916666666666667, "value": 1973.848}, {"frame": 1.875, "value": -230.312}], "opacity": [{"frame": 1.875, "value": 255, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "effect_xy_02_jian_down06/effect_xy_blade_02": {"props": {"opacity": [{"frame": 1.7916666666666667, "value": 0, "curve": "constant"}, {"frame": 1.875, "value": 255}, {"frame": 2.5, "value": 255}, {"frame": 2.625, "value": 0}], "y": [{"frame": 1.875, "value": 188.94}]}}, "effect_xy_02_henji_006": {"props": {"opacity": [{"frame": 1.7916666666666667, "value": 0}, {"frame": 1.8333333333333333, "value": 255, "curve": "constant"}]}}, "monster/1/New Node": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster/1/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}}, "monster/2/New Node": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster/7/New Node": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster/8/New Node": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster/2/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}}, "monster/7/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}}, "monster/8/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}}, "shanghai/1": {"props": {"position": [{"frame": 1.6666666666666667, "value": [324.556, 88.204, 0], "curve": [0.06, 0.12, 0.7035955056179776, 0.9971910112359551]}, {"frame": 1.9166666666666667, "curve": "linear", "value": [324.556, 196.96, 0]}, {"frame": 2.2083333333333335, "value": [324.556, 221.794, 0]}], "opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}]}}, "shanghai/2": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}], "position": [{"frame": 1.6666666666666667, "value": [180.352, 287.847, 0], "curve": [0.06, 0.12, 0.7007865168539327, 1.002808988764045]}, {"frame": 1.9166666666666667, "curve": "linear", "value": [217.378, 389.37, 0]}, {"frame": 2.2083333333333335, "value": [228.128, 413.258, 0]}]}}, "shanghai/3": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}], "position": [{"frame": 1.6666666666666667, "value": [197.074, -44.192, 0], "curve": [0.06, 0.12, 0.6923595505617978, 1.002808988764045]}, {"frame": 1.9166666666666667, "value": [183.936, 53.748, 0]}, {"frame": 2.2083333333333335, "value": [180.353, 80.024, 0]}]}}, "shanghai/4": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}], "position": [{"frame": 1.6666666666666667, "value": [171.992, -203.046, 0], "curve": [0.06, 0.12, 0.6923595505617978, 0.9971910112359551]}, {"frame": 1.9166666666666667, "value": [171.992, -90.774, 0]}, {"frame": 2.2083333333333335, "value": [171.992, -66.886, 0]}]}}, "shanghai/5": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}], "position": [{"frame": 1.6666666666666667, "value": [-292.624, -72.858, 0], "curve": [0.06, 0.12, 0.6951685393258428, 1]}, {"frame": 1.9166666666666667, "value": [-275.903, 31.054, 0]}, {"frame": 2.2083333333333335, "value": [-272.32, 53.747, 0]}]}}, "shanghai/6": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}], "position": [{"frame": 1.6666666666666667, "value": [-243.655, 114.661, 0], "curve": [0.06, 0.12, 0.7007865168539327, 1.002808988764045]}, {"frame": 1.9166666666666667, "value": [-243.655, 216.184, 0]}, {"frame": 2.2083333333333335, "value": [-243.655, 240.072, 0]}]}}, "shanghai/7": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}], "position": [{"frame": 1.6666666666666667, "value": [-96.745, 266.348, 0], "curve": [0.06, 0.12, 0.6867415730337079, 1]}, {"frame": 1.9166666666666667, "value": [-133.771, 376.231, 0]}, {"frame": 2.2083333333333335, "value": [-139.743, 396.536, 0]}]}}, "shanghai/8": {"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}], "position": [{"frame": 1.6666666666666667, "value": [-106.3, -166.02, 0], "curve": [0.06, 0.12, 0.6951685393258428, 0.9971910112359551]}, {"frame": 1.9166666666666667, "value": [-94.356, -57.331, 0]}, {"frame": 2.2083333333333335, "value": [-91.967, -32.249, 0]}]}}, "monster2/3/New Node": {"comps": {}, "props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster2/4/New Node": {"comps": {}, "props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster2/5/New Node": {"comps": {}, "props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster2/6/New Node": {"comps": {}, "props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200, "curve": "constant"}, {"frame": 1.9583333333333333, "value": 0}]}}, "monster2/3/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}, "props": {}}, "monster2/4/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}, "props": {}}, "monster2/5/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}, "props": {}}, "monster2/6/hit": {"comps": {"cc.ParticleSystem": {"emissionRate": [{"frame": 1.7083333333333333, "value": 0}, {"frame": 1.75, "value": 50}, {"frame": 1.7916666666666667, "value": 0}]}}, "props": {}}}, "effect_xy_blade_01", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.0416666666666667}, "value", 6, 0], [{"frame": 1.125}, "value", 6, 1], [{"frame": 1.4166666666666667}, "value", 6, 2]], 11, 11, 11]]], "props", 11, [{"y": [{"frame": 0.7083333333333334, "value": 357.454}, {"frame": 0.9583333333333334, "value": 322.498}, {"frame": 1.0416666666666667, "value": 354.392}, {"frame": 1.2916666666666667, "value": 450.073}, {"frame": 1.3333333333333333, "value": 443.099}, {"frame": 1.375, "value": 450.073}, {"frame": 1.5, "value": 1964.414}], "opacity": [{"frame": 0.7083333333333334, "value": 0}, {"frame": 0.75, "value": 255}, {"frame": 1.4583333333333333, "value": 255}, {"frame": 1.5, "value": 0}], "x": [{"frame": 1.0416666666666667, "value": -7.05}]}, "scale", 12, [[[{"frame": 1.0416666666666667}, "value", 8, [1, 0.8, 0.8, 1]], [{"frame": 1.2083333333333333}, "value", 8, [1, 1.2, 1.2, 1]]], 11, 11]]], "effect_xy_blade_01/effect_xy_02_glow", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.7916666666666666, "value": 0}, {"frame": 1.0416666666666667, "value": 255}]}, "scale", 12, [[[{"frame": 0.7916666666666666, "curve": "constant"}, "value", 8, [1, 0, 0, 1]], [{"frame": 1.0416666666666667}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.1666666666666667}, "value", 8, [1, 1.3, 1.3, 1]], [{"frame": 1.25}, "value", 8, [1, 0, 0, 1]]], 11, 11, 11, 11]]], "effect_xy_blade_01/effect_xy_02_glow/effect_xy_02_glow", 11, [{}, "props", 11, [{}, "color", 12, [[[{"frame": 1, "curve": "cubicIn"}, "value", 8, [4, 4278245375]], [{"frame": 1.0833333333333333, "curve": "cubicOut"}, "value", 8, [4, 4294967295]], [{"frame": 1.125}, "value", 8, [4, 4278190335]]], 11, 11, 11]]], "effect_xy_blade_01/effect_xy_02_glow/effect_xy_02_glow_L", 11, [{}, "props", 11, [{}, "color", 12, [[[{"frame": 1, "curve": "cubicIn"}, "value", 8, [4, 4278245375]], [{"frame": 1.0833333333333333, "curve": "cubicOut"}, "value", 8, [4, 4294967295]], [{"frame": 1.125}, "value", 8, [4, 4278190335]]], 11, 11, 11]]], "effect_xy_Skill_02_brust_001", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 255}, {"frame": 1.8333333333333333, "value": 255}, {"frame": 1.9166666666666667, "value": 0}], "position": [{"frame": 2.25, "value": [255.246, -211.975, 0]}]}, "scale", 12, [[[{"frame": 1.8333333333333333}, "value", 8, [1, 1.5, 1.5, 1]], [{"frame": 1.9166666666666667}, "value", 8, [1, 1.6, 1.6, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7083333333333333}, "value", 6, 3], [{"frame": 1.75}, "value", 6, 4], [{"frame": 1.8333333333333333}, "value", 6, 5], [{"frame": 1.875}, "value", 6, 6]], 11, 11, 11, 11]]]], "effect_xy_Skill_02_brust_003", 11, [{}, "props", 11, [{"position": [{"frame": 2.25, "value": [-206.53, -229.832, 0]}], "opacity": [{"frame": 1.75, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 255}, {"frame": 1.9166666666666667, "value": 255}, {"frame": 2, "value": 0}]}, "scale", 12, [[[{"frame": 1.9166666666666667}, "value", 8, [1, 1.5, 1.5, 1]], [{"frame": 2}, "value", 8, [1, 1.6, 1.6, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7916666666666667}, "value", 6, 7], [{"frame": 1.8333333333333333}, "value", 6, 8], [{"frame": 1.9166666666666667}, "value", 6, 9], [{"frame": 1.9583333333333333}, "value", 6, 10]], 11, 11, 11, 11]]]], "effect_xy_Skill_02_brust_002", 11, [{}, "props", 11, [{"position": [{"frame": 1.7083333333333333, "value": [125.068, 145.957, 0]}], "opacity": [{"frame": 1.7083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 255}, {"frame": 1.875, "value": 255}, {"frame": 1.9583333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 1.875}, "value", 8, [1, 1.5, 1.5, 1]], [{"frame": 1.9583333333333333}, "value", 8, [1, 1.6, 1.6, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.75}, "value", 6, 11], [{"frame": 1.7916666666666667}, "value", 6, 12], [{"frame": 1.875}, "value", 6, 13], [{"frame": 1.9166666666666667}, "value", 6, 14]], 11, 11, 11, 11]]]], "Body/Skin", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 15], [{"frame": 0.08333333333333333}, "value", 6, 16], [{"frame": 0.16666666666666666}, "value", 6, 17], [{"frame": 0.25}, "value", 6, 18], [{"frame": 0.3333333333333333}, "value", 6, 19], [{"frame": 0.4166666666666667}, "value", 6, 20], [{"frame": 0.5}, "value", 6, 21], [{"frame": 0.5833333333333334}, "value", 6, 22], [{"frame": 0.6666666666666666}, "value", 6, 23], [{"frame": 0.75}, "value", 6, 24], [{"frame": 0.8333333333333334}, "value", 6, 25], [{"frame": 0.9166666666666666}, "value", 6, 26], [{"frame": 1}, "value", 6, 27], [{"frame": 1.0833333333333333}, "value", 6, 28], [{"frame": 1.1666666666666667}, "value", 6, 29], [{"frame": 1.25}, "value", 6, 30], [{"frame": 1.3333333333333333}, "value", 6, 31], [{"frame": 1.4166666666666667}, "value", 6, 32], [{"frame": 1.5}, "value", 6, 33], [{"frame": 1.5833333333333333}, "value", 6, 34], [{"frame": 1.6666666666666667}, "value", 6, 35], [{"frame": 1.75}, "value", 6, 36], [{"frame": 1.8333333333333333}, "value", 6, 37], [{"frame": 1.9166666666666667}, "value", 6, 38], [{"frame": 2}, "value", 6, 39], [{"frame": 2.0833333333333335}, "value", 6, 40], [{"frame": 2.1666666666666665}, "value", 6, 41], [{"frame": 2.25}, "value", 6, 42], [{"frame": 2.3333333333333335}, "value", 6, 43], [{"frame": 2.4166666666666665}, "value", 6, 44], [{"frame": 2.5}, "value", 6, 45], [{"frame": 2.5833333333333335}, "value", 6, 46], [{"frame": 2.6666666666666665}, "value", 6, 47], [{"frame": 2.75}, "value", 6, 48], [{"frame": 2.8333333333333335}, "value", 6, 49], [{"frame": 2.9166666666666665}, "value", 6, 50], [{"frame": 3}, "value", 6, 51], [{"frame": 3.0833333333333335}, "value", 6, 52]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "effect_xy_02_henji_001/xy_02_xuanwo", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 200}, {"frame": 2.1666666666666665, "value": 200}, {"frame": 2.4166666666666665, "value": 0}], "angle": []}, "scale", 12, [[[{"frame": 1.6666666666666667, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.75}, "value", 8, [1, 1.3, 1.3, 1]]], 11, 11]]], "effect_xy_02_henji_001/xy_02_ring", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.625, "value": 0, "curve": "constant"}, {"frame": 1.6666666666666667, "value": 150}, {"frame": 1.7916666666666667, "value": 150}, {"frame": 1.875, "value": 0}]}, "scale", 12, [[[{"frame": 1.625}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.9166666666666667}, "value", 8, [1, 3, 3, 1]]], 11, 11]]], "shousuo_plane/shousuo", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.2916666666666667, "value": 0, "curve": "constant"}, {"frame": 1.3333333333333333, "value": 120}, {"frame": 1.5, "value": 120}, {"frame": 1.5833333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 1.2916666666666667, "curve": "cubicOut"}, "value", 8, [1, 7, 7, 1]], [{"frame": 1.5833333333333333}, "value", 8, [1, 0, 0, 1]]], 11, 11]]], "shousuo_plane/shousuo02", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.375, "value": 0, "curve": "constant"}, {"frame": 1.4166666666666667, "value": 120}, {"frame": 1.5, "value": 120}, {"frame": 1.5416666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 1.375}, "value", 8, [1, 5, 5, 1]], [{"frame": 1.5416666666666667}, "value", 8, [1, 0, 0, 1]]], 11, 11]]], "shousuo_plane/glow_0014_2", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.2916666666666667, "value": 0}, {"frame": 1.375, "value": 120}, {"frame": 1.4583333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 1.2916666666666667, "curve": "linear"}, "value", 8, [1, 5, 5, 1]], [{"frame": 1.4583333333333333}, "value", 8, [1, 0, 0, 1]]], 11, 11]]], "7000507_bz1_02", 11, [{"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 200}, {"frame": 1.8333333333333333, "value": 200}, {"frame": 2, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7083333333333333}, "value", 6, 53], [{"frame": 1.75}, "value", 6, 54], [{"frame": 1.7916666666666667}, "value", 6, 55], [{"frame": 1.8333333333333333}, "value", 6, 56], [{"frame": 1.875}, "value", 6, 57], [{"frame": 1.9166666666666667}, "value", 6, 58], [{"frame": 1.9583333333333333}, "value", 6, 59]], 11, 11, 11, 11, 11, 11, 11]]]], "effect_xy_02_henji_002/xy_02_xuanwo", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.7083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200}, {"frame": 2.2083333333333335, "value": 200}, {"frame": 2.4583333333333335, "value": 0}]}, "scale", 12, [[[{"frame": 1.7083333333333333, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.7916666666666667}, "value", 8, [1, 1.3, 1.3, 1]]], 11, 11]]], "effect_xy_02_henji_003/xy_02_xuanwo", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.75, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 200}, {"frame": 2.25, "value": 200}, {"frame": 2.5, "value": 0}]}, "scale", 12, [[[{"frame": 1.75, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.8333333333333333}, "value", 8, [1, 1.3, 1.3, 1]]], 11, 11]]], "effect_xy_02_henji_002/xy_02_ring", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 150}, {"frame": 1.8333333333333333, "value": 150}, {"frame": 1.9166666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 1.6666666666666667}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.9583333333333333}, "value", 8, [1, 3, 3, 1]]], 11, 11]]], "effect_xy_02_henji_003/xy_02_ring", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.7083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 150}, {"frame": 1.875, "value": 150}, {"frame": 1.9583333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 1.7083333333333333}, "value", 8, [1, 1, 1, 1]], [{"frame": 2}, "value", 8, [1, 3, 3, 1]]], 11, 11]]], "7000507_bz1_01", 11, [{"props": {"opacity": [{"frame": 1.7083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 200}, {"frame": 1.875, "value": 200}, {"frame": 2.0416666666666665, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.75}, "value", 6, 60], [{"frame": 1.7916666666666667}, "value", 6, 61], [{"frame": 1.8333333333333333}, "value", 6, 62], [{"frame": 1.875}, "value", 6, 63], [{"frame": 1.9166666666666667}, "value", 6, 64], [{"frame": 1.9583333333333333}, "value", 6, 65], [{"frame": 2}, "value", 6, 66]], 11, 11, 11, 11, 11, 11, 11]]]], "7000507_bz1_03", 11, [{"props": {"opacity": [{"frame": 1.75, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 200}, {"frame": 1.9166666666666667, "value": 200}, {"frame": 2.0833333333333335, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7916666666666667}, "value", 6, 67], [{"frame": 1.8333333333333333}, "value", 6, 68], [{"frame": 1.875}, "value", 6, 69], [{"frame": 1.9166666666666667}, "value", 6, 70], [{"frame": 1.9583333333333333}, "value", 6, 71], [{"frame": 2}, "value", 6, 72], [{"frame": 2.0416666666666665}, "value", 6, 73]], 11, 11, 11, 11, 11, 11, 11]]]], "effect_xy_Skill_02_brust_004", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 255}, {"frame": 1.8333333333333333, "value": 255}, {"frame": 1.9166666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 1.8333333333333333}, "value", 8, [1, 1.5, 1.5, 1]], [{"frame": 1.9166666666666667}, "value", 8, [1, 1.6, 1.6, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7083333333333333}, "value", 6, 74], [{"frame": 1.75}, "value", 6, 75], [{"frame": 1.8333333333333333}, "value", 6, 76], [{"frame": 1.875}, "value", 6, 77]], 11, 11, 11, 11]]]], "effect_xy_Skill_02_brust_005", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.75, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 255}, {"frame": 1.9166666666666667, "value": 255}, {"frame": 2, "value": 0}]}, "scale", 12, [[[{"frame": 1.9166666666666667}, "value", 8, [1, 1.5, 1.5, 1]], [{"frame": 2}, "value", 8, [1, 1.6, 1.6, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7916666666666667}, "value", 6, 78], [{"frame": 1.8333333333333333}, "value", 6, 79], [{"frame": 1.9166666666666667}, "value", 6, 80], [{"frame": 1.9583333333333333}, "value", 6, 81]], 11, 11, 11, 11]]]], "7000507_bz1_04", 11, [{"props": {"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 200}, {"frame": 1.8333333333333333, "value": 200}, {"frame": 2, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7083333333333333}, "value", 6, 82], [{"frame": 1.75}, "value", 6, 83], [{"frame": 1.7916666666666667}, "value", 6, 84], [{"frame": 1.8333333333333333}, "value", 6, 85], [{"frame": 1.875}, "value", 6, 86], [{"frame": 1.9166666666666667}, "value", 6, 87], [{"frame": 1.9583333333333333}, "value", 6, 88]], 11, 11, 11, 11, 11, 11, 11]]]], "7000507_bz1_05", 11, [{"props": {"opacity": [{"frame": 1.75, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 200}, {"frame": 1.9166666666666667, "value": 200}, {"frame": 2.0833333333333335, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.7916666666666667}, "value", 6, 89], [{"frame": 1.8333333333333333}, "value", 6, 90], [{"frame": 1.875}, "value", 6, 91], [{"frame": 1.9166666666666667}, "value", 6, 92], [{"frame": 1.9583333333333333}, "value", 6, 93], [{"frame": 2}, "value", 6, 94], [{"frame": 2.0416666666666665}, "value", 6, 95]], 11, 11, 11, 11, 11, 11, 11]]]], "effect_xy_02_henji_004/xy_02_xuanwo", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.6666666666666667, "value": 0, "curve": "constant"}, {"frame": 1.7083333333333333, "value": 200}, {"frame": 2.1666666666666665, "value": 200}, {"frame": 2.4166666666666665, "value": 0}]}, "scale", 12, [[[{"frame": 1.6666666666666667, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.75}, "value", 8, [1, 1.3, 1.3, 1]]], 11, 11]]], "effect_xy_02_henji_004/xy_02_ring", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.625, "value": 0, "curve": "constant"}, {"frame": 1.6666666666666667, "value": 150}, {"frame": 1.7916666666666667, "value": 150}, {"frame": 1.875, "value": 0}]}, "scale", 12, [[[{"frame": 1.625}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.9166666666666667}, "value", 8, [1, 3, 3, 1]]], 11, 11]]], "effect_xy_02_henji_005/xy_02_xuanwo", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.75, "value": 0, "curve": "constant"}, {"frame": 1.7916666666666667, "value": 200}, {"frame": 2.2083333333333335, "value": 200}, {"frame": 2.4583333333333335, "value": 0}]}, "scale", 12, [[[{"frame": 1.75, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.8333333333333333}, "value", 8, [1, 1.3, 1.3, 1]]], 11, 11]]], "effect_xy_02_henji_005/xy_02_ring", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.7083333333333333, "value": 0, "curve": "constant"}, {"frame": 1.75, "value": 150}, {"frame": 1.875, "value": 150}, {"frame": 1.9583333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 1.7083333333333333}, "value", 8, [1, 1, 1, 1]], [{"frame": 2}, "value", 8, [1, 3, 3, 1]]], 11, 11]]], "shousuo_plane/glow_0014_3", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.375, "value": 0}, {"frame": 1.4583333333333333, "value": 120}, {"frame": 1.5416666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 1.375, "curve": "linear"}, "value", 8, [1, 5, 5, 1]], [{"frame": 1.5416666666666667}, "value", 8, [1, 0, 0, 1]]], 11, 11]]], "7000507_bz1_06", 11, [{"props": {"opacity": [{"frame": 1.8333333333333333, "value": 0, "curve": "constant"}, {"frame": 1.875, "value": 200}, {"frame": 2, "value": 200}, {"frame": 2.1666666666666665, "value": 0}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.875}, "value", 6, 96], [{"frame": 1.9166666666666667}, "value", 6, 97], [{"frame": 1.9583333333333333}, "value", 6, 98], [{"frame": 2}, "value", 6, 99], [{"frame": 2.0416666666666665}, "value", 6, 100], [{"frame": 2.0833333333333335}, "value", 6, 101], [{"frame": 2.125}, "value", 6, 102]], 11, 11, 11, 11, 11, 11, 11]]]], "effect_xy_Skill_02_brust_006", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.8333333333333333, "value": 0, "curve": "constant"}, {"frame": 1.875, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.0833333333333335, "value": 0}], "position": []}, "scale", 12, [[[{"frame": 2}, "value", 8, [1, 3, 3, 1]], [{"frame": 2.0833333333333335}, "value", 8, [1, 3.5, 3.5, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 1.875}, "value", 6, 103], [{"frame": 1.9166666666666667}, "value", 6, 104], [{"frame": 2}, "value", 6, 105], [{"frame": 2.0416666666666665}, "value", 6, 106]], 11, 11, 11, 11]]]], "effect_xy_02_henji_006/xy_02_xuanwo", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.8333333333333333, "value": 0, "curve": "constant"}, {"frame": 1.875, "value": 200}, {"frame": 2.3333333333333335, "value": 200}, {"frame": 2.5833333333333335, "value": 0}]}, "scale", 12, [[[{"frame": 1.8333333333333333, "curve": "cubicOut"}, "value", 8, [1, 1, 1, 1]], [{"frame": 1.9166666666666667}, "value", 8, [1, 2, 2, 1]]], 11, 11]]], "effect_xy_02_henji_006/xy_02_ring", 11, [{}, "props", 11, [{"opacity": [{"frame": 1.7916666666666667, "value": 0, "curve": "constant"}, {"frame": 1.8333333333333333, "value": 150}, {"frame": 1.9583333333333333, "value": 150}, {"frame": 2.0416666666666665, "value": 0}]}, "scale", 12, [[[{"frame": 1.7916666666666667}, "value", 8, [1, 1, 1, 1]], [{"frame": 2.0833333333333335}, "value", 8, [1, 3, 3, 1]]], 11, 11]]], "monster/1", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [485.49, 63.768, 0]}, {"frame": 1.5, "value": [243.072, 11.613, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [243.072, 11.613, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [243.072, 500.573, 0]}, {"frame": 2.1666666666666665, "value": [243.072, 11.613, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 107], [{"frame": 0.08333333333333333}, "value", 6, 108], [{"frame": 0.16666666666666666}, "value", 6, 109], [{"frame": 0.25}, "value", 6, 110], [{"frame": 0.3333333333333333}, "value", 6, 111], [{"frame": 0.4166666666666667}, "value", 6, 112], [{"frame": 0.5}, "value", 6, 113], [{"frame": 0.5833333333333334}, "value", 6, 114], [{"frame": 0.6666666666666666}, "value", 6, 115], [{"frame": 0.75}, "value", 6, 116], [{"frame": 0.8333333333333334}, "value", 6, 117], [{"frame": 0.9166666666666666}, "value", 6, 118], [{"frame": 1}, "value", 6, 119], [{"frame": 1.0833333333333333}, "value", 6, 120], [{"frame": 1.1666666666666667}, "value", 6, 121], [{"frame": 1.25}, "value", 6, 122], [{"frame": 1.3333333333333333}, "value", 6, 123], [{"frame": 1.4166666666666667}, "value", 6, 124], [{"frame": 1.5}, "value", 6, 125], [{"frame": 1.5833333333333333}, "value", 6, 126], [{"frame": 1.6666666666666667}, "value", 6, 127], [{"frame": 1.75}, "value", 6, 128], [{"frame": 2.1666666666666665}, "value", 6, 129], [{"frame": 2.25}, "value", 6, 130], [{"frame": 2.3333333333333335}, "value", 6, 131], [{"frame": 2.4166666666666665}, "value", 6, 132]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "monster/2", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [339.808, 373.943, 0]}, {"frame": 1.5, "value": [140.723, 193.541, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [140.723, 193.541, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [140.723, 649.287, 0]}, {"frame": 2.1666666666666665, "value": [140.723, 193.541, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 133], [{"frame": 0.08333333333333333}, "value", 6, 134], [{"frame": 0.16666666666666666}, "value", 6, 135], [{"frame": 0.25}, "value", 6, 136], [{"frame": 0.3333333333333333}, "value", 6, 137], [{"frame": 0.4166666666666667}, "value", 6, 138], [{"frame": 0.5}, "value", 6, 139], [{"frame": 0.5833333333333334}, "value", 6, 140], [{"frame": 0.6666666666666666}, "value", 6, 141], [{"frame": 0.75}, "value", 6, 142], [{"frame": 0.8333333333333334}, "value", 6, 143], [{"frame": 0.9166666666666666}, "value", 6, 144], [{"frame": 1}, "value", 6, 145], [{"frame": 1.0833333333333333}, "value", 6, 146], [{"frame": 1.1666666666666667}, "value", 6, 147], [{"frame": 1.25}, "value", 6, 148], [{"frame": 1.3333333333333333}, "value", 6, 149], [{"frame": 1.4166666666666667}, "value", 6, 150], [{"frame": 1.5}, "value", 6, 151], [{"frame": 1.5833333333333333}, "value", 6, 152], [{"frame": 1.6666666666666667}, "value", 6, 153], [{"frame": 1.75}, "value", 6, 154], [{"frame": 2.1666666666666665}, "value", 6, 155], [{"frame": 2.25}, "value", 6, 156], [{"frame": 2.3333333333333335}, "value", 6, 157], [{"frame": 2.4166666666666665}, "value", 6, 158]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "monster/7", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [-315.956, 161.56, 0]}, {"frame": 1.5, "value": [-158.134, 54.941, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [-158.134, 54.941, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [-158.134, 476.632, 0]}, {"frame": 2.1666666666666665, "value": [-158.134, 54.941, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 159], [{"frame": 0.08333333333333333}, "value", 6, 160], [{"frame": 0.16666666666666666}, "value", 6, 161], [{"frame": 0.25}, "value", 6, 162], [{"frame": 0.3333333333333333}, "value", 6, 163], [{"frame": 0.4166666666666667}, "value", 6, 164], [{"frame": 0.5}, "value", 6, 165], [{"frame": 0.5833333333333334}, "value", 6, 166], [{"frame": 0.6666666666666666}, "value", 6, 167], [{"frame": 0.75}, "value", 6, 168], [{"frame": 0.8333333333333334}, "value", 6, 169], [{"frame": 0.9166666666666666}, "value", 6, 170], [{"frame": 1}, "value", 6, 171], [{"frame": 1.0833333333333333}, "value", 6, 172], [{"frame": 1.1666666666666667}, "value", 6, 173], [{"frame": 1.25}, "value", 6, 174], [{"frame": 1.3333333333333333}, "value", 6, 175], [{"frame": 1.4166666666666667}, "value", 6, 176], [{"frame": 1.5}, "value", 6, 177], [{"frame": 1.5833333333333333}, "value", 6, 178], [{"frame": 1.6666666666666667}, "value", 6, 179], [{"frame": 1.75}, "value", 6, 180], [{"frame": 2.1666666666666665}, "value", 6, 181], [{"frame": 2.25}, "value", 6, 182], [{"frame": 2.3333333333333335}, "value", 6, 183], [{"frame": 2.4166666666666665}, "value", 6, 184]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "monster/8", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [-139.348, 353.882, 0]}, {"frame": 1.5, "value": [-57.01, 195.095, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [-57.01, 195.095, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [-57.01, 630.265, 0]}, {"frame": 2.1666666666666665, "value": [-57.01, 195.095, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 185], [{"frame": 0.08333333333333333}, "value", 6, 186], [{"frame": 0.16666666666666666}, "value", 6, 187], [{"frame": 0.25}, "value", 6, 188], [{"frame": 0.3333333333333333}, "value", 6, 189], [{"frame": 0.4166666666666667}, "value", 6, 190], [{"frame": 0.5}, "value", 6, 191], [{"frame": 0.5833333333333334}, "value", 6, 192], [{"frame": 0.6666666666666666}, "value", 6, 193], [{"frame": 0.75}, "value", 6, 194], [{"frame": 0.8333333333333334}, "value", 6, 195], [{"frame": 0.9166666666666666}, "value", 6, 196], [{"frame": 1}, "value", 6, 197], [{"frame": 1.0833333333333333}, "value", 6, 198], [{"frame": 1.1666666666666667}, "value", 6, 199], [{"frame": 1.25}, "value", 6, 200], [{"frame": 1.3333333333333333}, "value", 6, 201], [{"frame": 1.4166666666666667}, "value", 6, 202], [{"frame": 1.5}, "value", 6, 203], [{"frame": 1.5833333333333333}, "value", 6, 204], [{"frame": 1.6666666666666667}, "value", 6, 205], [{"frame": 1.75}, "value", 6, 206], [{"frame": 2.1666666666666665}, "value", 6, 207], [{"frame": 2.25}, "value", 6, 208], [{"frame": 2.3333333333333335}, "value", 6, 209], [{"frame": 2.4166666666666665}, "value", 6, 210]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "monster2/3", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [248.909, -396.785, 0]}, {"frame": 1.5, "value": [139.345, -208.171, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [139.345, -208.171, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [139.345, 206.944, 0]}, {"frame": 2.1666666666666665, "value": [139.345, -208.171, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 211], [{"frame": 0.08333333333333333}, "value", 6, 212], [{"frame": 0.16666666666666666}, "value", 6, 213], [{"frame": 0.25}, "value", 6, 214], [{"frame": 0.3333333333333333}, "value", 6, 215], [{"frame": 0.4166666666666667}, "value", 6, 216], [{"frame": 0.5}, "value", 6, 217], [{"frame": 0.5833333333333334}, "value", 6, 218], [{"frame": 0.6666666666666666}, "value", 6, 219], [{"frame": 0.75}, "value", 6, 220], [{"frame": 0.8333333333333334}, "value", 6, 221], [{"frame": 0.9166666666666666}, "value", 6, 222], [{"frame": 1}, "value", 6, 223], [{"frame": 1.0833333333333333}, "value", 6, 224], [{"frame": 1.1666666666666667}, "value", 6, 225], [{"frame": 1.25}, "value", 6, 226], [{"frame": 1.3333333333333333}, "value", 6, 227], [{"frame": 1.4166666666666667}, "value", 6, 228], [{"frame": 1.5}, "value", 6, 229], [{"frame": 1.5833333333333333}, "value", 6, 230], [{"frame": 1.6666666666666667}, "value", 6, 231], [{"frame": 1.75}, "value", 6, 232], [{"frame": 2.1666666666666665}, "value", 6, 233], [{"frame": 2.25}, "value", 6, 234], [{"frame": 2.3333333333333335}, "value", 6, 235], [{"frame": 2.4166666666666665}, "value", 6, 236]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "monster2/4", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [350.851, -178.707, 0]}, {"frame": 1.5, "value": [150.42, -77.343, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [150.42, -77.343, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [150.42, 329.903, 0]}, {"frame": 2.1666666666666665, "value": [150.42, -77.343, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 237], [{"frame": 0.08333333333333333}, "value", 6, 238], [{"frame": 0.16666666666666666}, "value", 6, 239], [{"frame": 0.25}, "value", 6, 240], [{"frame": 0.3333333333333333}, "value", 6, 241], [{"frame": 0.4166666666666667}, "value", 6, 242], [{"frame": 0.5}, "value", 6, 243], [{"frame": 0.5833333333333334}, "value", 6, 244], [{"frame": 0.6666666666666666}, "value", 6, 245], [{"frame": 0.75}, "value", 6, 246], [{"frame": 0.8333333333333334}, "value", 6, 247], [{"frame": 0.9166666666666666}, "value", 6, 248], [{"frame": 1}, "value", 6, 249], [{"frame": 1.0833333333333333}, "value", 6, 250], [{"frame": 1.1666666666666667}, "value", 6, 251], [{"frame": 1.25}, "value", 6, 252], [{"frame": 1.3333333333333333}, "value", 6, 253], [{"frame": 1.4166666666666667}, "value", 6, 254], [{"frame": 1.5}, "value", 6, 255], [{"frame": 1.5833333333333333}, "value", 6, 256], [{"frame": 1.6666666666666667}, "value", 6, 257], [{"frame": 1.75}, "value", 6, 258], [{"frame": 2.1666666666666665}, "value", 6, 259], [{"frame": 2.25}, "value", 6, 260], [{"frame": 2.3333333333333335}, "value", 6, 261], [{"frame": 2.4166666666666665}, "value", 6, 262]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "monster2/5", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [-161.979, -352.307, 0]}, {"frame": 1.5, "value": [-70.135, -169.595, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [-70.135, -169.595, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [-70.135, 296.672, 0]}, {"frame": 2.1666666666666665, "value": [-70.135, -169.595, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 263], [{"frame": 0.08333333333333333}, "value", 6, 264], [{"frame": 0.16666666666666666}, "value", 6, 265], [{"frame": 0.25}, "value", 6, 266], [{"frame": 0.3333333333333333}, "value", 6, 267], [{"frame": 0.4166666666666667}, "value", 6, 268], [{"frame": 0.5}, "value", 6, 269], [{"frame": 0.5833333333333334}, "value", 6, 270], [{"frame": 0.6666666666666666}, "value", 6, 271], [{"frame": 0.75}, "value", 6, 272], [{"frame": 0.8333333333333334}, "value", 6, 273], [{"frame": 0.9166666666666666}, "value", 6, 274], [{"frame": 1}, "value", 6, 275], [{"frame": 1.0833333333333333}, "value", 6, 276], [{"frame": 1.1666666666666667}, "value", 6, 277], [{"frame": 1.25}, "value", 6, 278], [{"frame": 1.3333333333333333}, "value", 6, 279], [{"frame": 1.4166666666666667}, "value", 6, 280], [{"frame": 1.5}, "value", 6, 281], [{"frame": 1.5833333333333333}, "value", 6, 282], [{"frame": 1.6666666666666667}, "value", 6, 283], [{"frame": 1.75}, "value", 6, 284], [{"frame": 2.1666666666666665}, "value", 6, 285], [{"frame": 2.25}, "value", 6, 286], [{"frame": 2.3333333333333335}, "value", 6, 287], [{"frame": 2.4166666666666665}, "value", 6, 288]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "monster2/6", 11, [{"props": {"position": [{"frame": 1.375, "curve": "cubicOut", "value": [-417.637, -185.283, 0]}, {"frame": 1.5, "value": [-236.549, -104.912, 0]}, {"frame": 1.75, "curve": "cubicOut", "value": [-236.549, -104.912, 0]}, {"frame": 1.9166666666666667, "curve": "cubicIn", "value": [-236.549, 359.04, 0]}, {"frame": 2.1666666666666665, "value": [-236.549, -104.912, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 289], [{"frame": 0.08333333333333333}, "value", 6, 290], [{"frame": 0.16666666666666666}, "value", 6, 291], [{"frame": 0.25}, "value", 6, 292], [{"frame": 0.3333333333333333}, "value", 6, 293], [{"frame": 0.4166666666666667}, "value", 6, 294], [{"frame": 0.5}, "value", 6, 295], [{"frame": 0.5833333333333334}, "value", 6, 296], [{"frame": 0.6666666666666666}, "value", 6, 297], [{"frame": 0.75}, "value", 6, 298], [{"frame": 0.8333333333333334}, "value", 6, 299], [{"frame": 0.9166666666666666}, "value", 6, 300], [{"frame": 1}, "value", 6, 301], [{"frame": 1.0833333333333333}, "value", 6, 302], [{"frame": 1.1666666666666667}, "value", 6, 303], [{"frame": 1.25}, "value", 6, 304], [{"frame": 1.3333333333333333}, "value", 6, 305], [{"frame": 1.4166666666666667}, "value", 6, 306], [{"frame": 1.5}, "value", 6, 307], [{"frame": 1.5833333333333333}, "value", 6, 308], [{"frame": 1.6666666666666667}, "value", 6, 309], [{"frame": 1.75}, "value", 6, 310], [{"frame": 2.1666666666666665}, "value", 6, 311], [{"frame": 2.25}, "value", 6, 312], [{"frame": 2.3333333333333335}, "value", 6, 313], [{"frame": 2.4166666666666665}, "value", 6, 314]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [28, 29, 30, 8, 9, 10, 11, 8, 9, 10, 11, 8, 9, 10, 11, 19, 20, 21, 22, 19, 20, 21, 22, 31, 32, 25, 26, 25, 26, 25, 26, 27, 23, 24, 23, 24, 23, 24, 23, 24, 27, 19, 20, 21, 22, 19, 20, 21, 22, 19, 20, 21, 22, 12, 13, 14, 15, 16, 17, 18, 12, 13, 14, 15, 16, 17, 18, 12, 13, 14, 15, 16, 17, 18, 8, 9, 10, 11, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 12, 13, 14, 15, 16, 17, 18, 12, 13, 14, 15, 16, 17, 18, 8, 9, 10, 11, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 4, 5, 6, 7]]