[1, ["ecpdLyjvZBwrvm+cedCcQy", "11jBDjF11PMLrmkdLOqDjf", "1aXWrQnjZC2apfn1RH20tU", "12A6mniXhNV4Uwb3C3/xBM", "42nR4TEtlLvY8iS7+6QeeK"], ["_spriteFrame", "node", "_N$file", "root", "maskNode", "sprLine", "labLevel", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_children", "_trs", "_parent"], 2, 9, 4, 5, 2, 7, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name", "optimizationPolicy"], 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -4, 1, 3], ["83a44dHXxxN24DMowDnkvPJ", ["node", "colorLock", "colorUnlock", "labLevel", "sprLine", "maskNode"], 3, 1, 5, 5, 1, 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 6, 1, 2, 3, 2], [2, 2, 3, 4, 1], [4, 0, 1, 3], [0, 0, 4, 1, 2, 3, 5, 2], [1, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3, 3], [3, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [6, 0, 1, 2, 3, 4, 5, 1]], [[3, "levelItem", 1], [4, "levelItem", [-6, -7, -8, -9], [[10, -5, [4, 4280950301], [4, 4283379201], -4, -3, -2]], [8, -1, 0], [5, 60, 60], [716.234, 195.651, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_shrine_box_dengji2", 1, [[2, -10, [3], 4]], [0, "15RxTCJ+9Pv58nTMlnQatn", 1, 0], [5, 54, 60]], [5, "imgLine", 1, [-11], [0, "d9moxep75LuLJt0ikCfWWh", 1, 0], [5, 6, 120.8], [0, 0.4765717086015305, 0.9813814902793544], [0, -34.8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 1, 0, 3, [0]], [1, "img_shrine_box_dengji1", 1, [[2, -12, [1], 2]], [0, "efMHoOHDxDDrOzJ0oUuiVb", 1, 0], [5, 54, 60]], [6, "labLevel", 1, [-13], [0, "6a5lBKlBdK+p9utEp/sog8", 1, 0], [5, 38, 22]], [9, "99", 25, 22, false, -12, 1, 1, 6, [5]]], 0, [0, 3, 1, 0, 4, 2, 0, 5, 4, 0, 6, 7, 0, 1, 1, 0, -1, 3, 0, -2, 5, 0, -3, 2, 0, -4, 6, 0, 1, 2, 0, -1, 4, 0, 1, 5, 0, -1, 7, 0, 7, 1, 13], [0, 0, 0, 0, 0, 0, 4, 7], [-1, -1, 0, -1, 0, -1, 0, 2], [0, 0, 1, 0, 2, 0, 3, 4]]