[1, ["ecpdLyjvZBwrvm+cedCcQy", "8c20Sso/ZEn7NUfNSM+EBh", "39qwxzxhtLbpKEeOsLuMRy", "35wXuJ3dFB6LE4K+ErUEzn", "bduH5SZzJP/5EcdOsaWScx", "4fjkBY9TdLTb2uDzAHQ9N2", "afp65R73xAyYTY9D7AjadD", "0dlpmNs0pBta49IVf+mOpB", "9ehfcv0JlFyYqlxiby/3NM", "1dsYTPDZlDWoFAJnR+GzbP", "a9G/jvQwRNAY8cXg9XXLj2", "a2MjXRFdtLlYQ5ouAFv/+R", "c2E/qidRJHLJb0hY35vgTe"], ["node", "_spriteFrame", "_parent", "root", "rigidBody", "body", "skin", "anim", "data"], [["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_children", "_trs"], 1, 1, 9, 4, 5, 12, 7], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_groupIndex", "_prefab", "_children", "_components", "_parent", "_trs"], 1, 4, 2, 12, 1, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 2, 4, 5, 5, 7], ["cc.Node", ["_name", "_active", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 2, 9, 4, 5, 7], ["cc.Animation", ["playOnLoad", "node", "_clips"], 2, 1, 3], ["6f987iEmGpFs7oQJ+Xpz5XM", ["node"], 3, 1], ["cc.<PERSON>", ["tag", "node", "_size"], 2, 1, 5], ["e6f05YZAHRGiorL9OHBeRYw", ["node"], 3, 1], ["f6123H0uppA2Zmo5qpWWxKq", ["node", "anim", "skin", "body", "rigidBody"], 3, 1, 1, 1, 1, 1], ["cc.RigidBody", ["_allowSleep", "_gravityScale", "_fixedRotation", "enabledContactListener", "bullet", "node"], -2, 1], ["cc.PhysicsBoxCollider", ["_density", "_sensor", "node", "_offset", "_size"], 1, 1, 5, 5], ["cc.PhysicsCircleCollider", ["_density", "_friction", "_radius", "node", "_offset"], 0, 1, 5]], [[3, 0, 1, 2, 2], [2, 0, 5, 2, 6, 2], [1, 3, 4, 5, 1], [4, 0, 2], [2, 0, 1, 3, 4, 2, 3], [0, 0, 2, 6, 3, 4, 2], [0, 0, 2, 3, 4, 5, 7, 2], [0, 0, 1, 2, 3, 4, 5, 3], [0, 0, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 2], [6, 0, 1, 2, 3, 4, 5, 6, 7, 3], [1, 0, 1, 3, 4, 3], [1, 2, 0, 3, 4, 5, 3], [3, 1, 2, 1], [7, 0, 1, 2, 2], [8, 0, 1], [9, 0, 1, 2, 2], [10, 0, 1], [11, 0, 1, 2, 3, 4, 1], [12, 0, 1, 2, 3, 4, 5, 6], [13, 0, 1, 2, 3, 4, 3], [14, 0, 1, 2, 3, 4, 4]], [[3, "1030021"], [4, "Player", 2, [-10, -11], [[[18, -6, -5, -4, -3, -2], -7, [20, 0, true, -8, [0, 0, 30], [5, 30, 60]], [21, 0, 0, 20, -9, [0, 0, 30]]], 4, 1, 4, 4], [13, -1, 0]], [5, "Body", 1, [[-13, [1, "fire", -14, [0, "d2eB7M2MtExKefjkcthuSo", 1, 0], [-1, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "fire1", -15, [0, "ccW1imTshJxLGVadZv4gaz", 1, 0], [-1, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "fire2", -16, [0, "52UHTxcv5AbYlCTE45eb78", 1, 0], [-1, 64, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 4, 4, 4], [[15, -12]], [0, "bdu76iD25KvK9dn7FsTup5", 1, 0]], [9, "Skin", 2, [-19], [-17, -18], [0, "016Qk3+TtGbbQ2jhFubcDw", 1, 0], [5, 123, 118], [0, 0.5, 0], [2.5, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "futou", false, 3, [-21, -22], [[2, -20, [4], 5]], [0, "52JZH7ivxJ3qV/sWKIJ7mi", 1, 0], [5, 40, 36], [6.113, -1.34, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [8, "FootTrigger", 1, [[16, 1, -23, [5, 20, 10]], [12, false, 0, -24, [15], 16], [17, -25]], [0, "4fLzOkV/FD+6F02lqhlLMj", 1, 0], [5, 20, 10]], [6, "wei", 4, [[2, -26, [0], 1]], [0, "bbWOaD/kBFLJzeGM0a5B2O", 1, 0], [5, 72, 66], [78.664, -10.421, 0, 0, 0, 0, 1, 1.7, 1.7, 1]], [7, "shou", false, 4, [[2, -27, [2], 3]], [0, "45xZAn3rBET4J5hc5eXUG3", 1, 0], [5, 40, 36]], [11, 2, false, 3, [6]], [14, true, 3, [7, 8, 9, 10, 11, 12, 13, 14]], [19, false, 0, true, true, true, 1]], 0, [0, 3, 1, 0, 4, 10, 0, 5, 2, 0, 6, 8, 0, 7, 9, 0, 0, 1, 0, -2, 10, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 5, 0, 0, 2, 0, -1, 3, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, -1, 8, 0, -2, 9, 0, -1, 4, 0, 0, 4, 0, -1, 6, 0, -2, 7, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 8, 1, 27], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [-1, 1, -1, 1, -1, 1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -1, 1, 1], [0, 2, 0, 1, 0, 1, 0, 3, 4, 5, 6, 7, 8, 9, 10, 0, 11, 12]]