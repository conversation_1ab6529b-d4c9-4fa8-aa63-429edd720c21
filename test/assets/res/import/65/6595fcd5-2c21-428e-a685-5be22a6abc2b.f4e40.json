[1, ["ecpdLyjvZBwrvm+cedCcQy", "64tNxSl8dIR4UFr/E0Mk5V", "d1JGeBFXVFOpIh8d+zNKkT", "f0rtjA5xBGoqAWg/2bg2Q9", "f8SHJ+y9lFR5xFJFB6Sgfh", "1cGfd5IUhFPb94kKQO+0Qg", "440RjU4JhEEJLGsmvEbM1v"], ["node", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 1, 9, 4, 5, 7, 1, 2], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["85bd60p6LlEcoT/esxLXZVW", ["node"], 3, 1], ["cc.RichText", ["_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node"], -1, 1], ["3039cCGLWFIeqCPOGQnWhje", ["node"], 3, 1], ["3fde5rKebRB0onX27E9BSlz", ["node"], 3, 1]], [[2, 0, 1, 2, 2], [1, 2, 3, 4, 1], [0, 0, 6, 2, 3, 4, 5, 2], [0, 0, 6, 2, 3, 4, 2], [3, 0, 2], [0, 0, 1, 7, 2, 3, 4, 5, 3], [0, 0, 6, 7, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 4, 3], [2, 1, 2, 1], [4, 0, 1], [5, 0, 1, 2, 3, 4, 5], [6, 0, 1], [7, 0, 1]], [[4, "towerItem"], [5, "towerItem", false, [-4, -5, -6, -7, -8, -9], [[11, -2], [12, -3]], [8, -1, 0], [5, 340, 400], [-156, 252, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "towerTitleBg", 1, [-11], [[1, -10, [6], 7]], [0, "48WqPIBvlF85cxeXUY/qb3", 1, 0], [5, 55, 178], [-113.139, 60.444, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_name", 2, [[1, -12, [4], 5], [9, -13]], [0, "c2yw9ZzRNDU4Pae8I1/KmS", 1, 0], [5, 34, 132]], [2, "<PERSON><PERSON><PERSON>", 1, [[1, -14, [0], 1]], [0, "4c0ajoBPdOj6VQmRIC9p4A", 1, 0], [5, 316, 177], [-0.803, -77.902, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tower", 1, [[1, -15, [2], 3]], [0, "50IXDtNvJMOqo9bYllUys4", 1, 0], [5, 168, 203], [-7.278, 63.099, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_mask", 1, [[1, -16, [8], 9]], [0, "36caOYHZVEZo1Hxn77JdmS", 1, 0], [5, 317, 332]], [2, "conStatebg", 1, [[7, 1, 0, -17, [10], 11]], [0, "40k1oiCMhOWK6lJjTSgkWK", 1, 0], [5, 196, 70], [-10, -155, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lblState", 1, [[10, "当前层数：21<br/>星期二/五/日开启", 1, 21, 30, -18]], [0, "0epAi5AMNLOYfWNFNvXDIW", 1, 0], [5, 158.67, 67.8], [-10, -153, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 2, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 3, 1, 18], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 1, 0, 2, 0, 3, 0, 4, 0, 5, 0, 6]]