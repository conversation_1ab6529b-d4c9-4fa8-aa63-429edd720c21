[1, ["ecpdLyjvZBwrvm+cedCcQy", "42KMuhhpFHR7zAl4fRFREO", "b4BJExvOhPi6h+aQAHrz3c", "51UX53kZVLI4vfp8NOsw27", "eeT+uyRsRDMYwHgUgzfVI4", "7f1LA9ondCs4e0vuKxZZVg", "ee7MbLlKZM35wpFXRepfEF", "80MdMUs31CBp6xavq/lkhP", "d9dkwSdGNJFK8Jvz7lzizp", "1f1iT4BNFB47y3dAtt0PDj", "b2aHrECZ5APKGS/0d2hvT1", "66Q3kGGjBPgqivw1+zXu0e"], ["node", "_spriteFrame", "root", "_N$file", "_file", "_defaultClip", "selectAnim", "_parent", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_color"], 0, 4, 1, 9, 5, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab"], 2, 1, 2, 2, 4], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "endSizeVar", "startSpin", "endSpin", "endSpinVar", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccel", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -15, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.Mask", ["_type", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Animation", ["node", "_clips"], 3, 1, 3], ["6f439t3bRZFq6CSdIjM+MT4", ["node", "selectAnim"], 3, 1, 1]], [[2, 0, 1, 2, 2], [2, 0, 1, 2], [0, 0, 2, 4, 5, 3, 6, 7, 3], [1, 3, 4, 5, 1], [0, 0, 4, 5, 3, 6, 2], [0, 0, 4, 5, 3, 6, 7, 2], [1, 1, 0, 3, 4, 5, 3], [1, 0, 3, 4, 5, 2], [1, 2, 1, 0, 3, 4, 5, 4], [3, 0, 2], [0, 0, 8, 5, 3, 6, 2], [0, 0, 1, 4, 5, 3, 6, 7, 3], [0, 0, 4, 3, 7, 2], [0, 0, 4, 5, 3, 9, 6, 7, 2], [0, 0, 1, 4, 8, 5, 3, 6, 3], [0, 0, 4, 8, 3, 2], [0, 0, 4, 8, 5, 3, 6, 2], [0, 0, 4, 5, 3, 7, 2], [4, 0, 1, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 2], [2, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 19], [9, 0, 1, 2, 3, 2], [10, 0, 1, 1], [11, 0, 1, 1]], [[9, "SignItem"], [10, "SignItem", [-4, -5, -6], [[26, -3, -2]], [20, -1, 0], [5, 143, 157]], [19, "qiandao_tishi", 1, [-9], [-8], [1, "c1Bam/I4VAZ4qZ5MC+MK1X", -7]], [18, "normal", 1, [[-10, -11, -12, [12, "rewardBox", -13, [0, "1f/9PKzzhED7rENQzS40ua", 1, 0], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], -14, -15], 1, 1, 1, 4, 1, 1], [0, "39qQh4qNNCgqMdILdakHsM", 1, 0], [5, 143, 157]], [15, "Node", 2, [-16, -17, -18, -19], [1, "34QN24q3pH85Xwrmx4bE5t", 2]], [13, "txt_day", 3, [[21, "第 1 天", 23, 23, false, 1, 1, -20, [6], 7], [22, 3, -21, [4, 4280042393]]], [0, "41P7pmXSlDD7bIq4fe16/X", 1, 0], [4, 4291882746], [5, 78.86, 34.980000000000004], [0, 48.122, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "maskSelected", false, 3, [-23], [[7, 0, -22, [10], 11]], [0, "5dihlp1d5Gha29NOfIpqZ3", 1, 0], [5, 143, 157]], [16, "mask", 4, [-25], [[24, 2, -24, [23], 24]], [1, "8djl86MkREeKFHDqEwgRX1", 2], [5, 132, 152]], [4, "img_normal", 3, [[6, 1, 0, -26, [0], 1]], [0, "fdZQFLq+RBkon4HGWX1Qip", 1, 0], [5, 143, 157]], [4, "img_received", 3, [[6, 1, 0, -27, [2], 3]], [0, "3bvcTQbtZFaKOKadvUfhmV", 1, 0], [5, 143, 157]], [11, "img_guasheng", false, 3, [[3, -28, [4], 5]], [0, "afZuAnrwVMlKxCSCx8J9jy", 1, 0], [5, 26, 37], [0, 81.634, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_tick", 6, [[3, -29, [8], 9]], [0, "d5O+DuFDlAb67gduKBEg6s", 1, 0], [5, 63, 55], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_unLockMask", 1, [[7, 0, -30, [12], 13]], [0, "85qMCc3b9B1q84oLDPstcv", 1, 0], [5, 143, 157], [0, 0, 0, 0, 0, 0, 1, 1.00001, 1.00001, 1.00001]], [2, "liang", 83.10801063332025, 4, [[3, -31, [14], 15]], [1, "d40UBhq81BjLa5OUH9/Z9K", 2], [5, 165, 190], [0, 0, 0, 0, 0, 0, 1, 1.0005969794417469, 0.8467026989806073, 1.0147305271601772]], [2, "guang", 0, 4, [[8, 1, 1, 0, -32, [16], 17]], [1, "50yrC9CrJMMYcTAuWERCKa", 2], [5, 143, 157], [0, 0, 0, 0, 0, 0, 1, 1.278440000001919, 1.278440000001919, 1.5]], [2, "guang2", 74.98586542219857, 4, [[8, 1, 1, 0, -33, [18], 19]], [1, "18rlV3zOVFybTqX5ip0MwY", 2], [5, 143, 157], [0, 0, 0, 0, 0, 0, 1, 1.1351508800025945, 1.0995132800025176, 1.2586462400028706]], [17, "lizi", 7, [[23, true, 8, 0, 0.05, 360, 360, 40, 5, 40, 5, -47.369998931884766, -47.369998931884766, -142.11000061035156, 1, 30, 0, 0, -871.0499878, -34, [20], [4, 4294967295], [4, 3321888768], [4, 2533359615], [4, 838860800], [0, 30, 3], [0, 0, 950], 21, 22]], [1, "ac14xeTENEqak8bGh4D7NT", 2], [0, -101.179, 0, 0, 0, 0, 1, 1, 1, 1]], [25, 2, [25]]], 0, [0, 2, 1, 0, 6, 17, 0, 0, 1, 0, -1, 3, 0, -2, 12, 0, -3, 2, 0, 2, 2, 0, -1, 17, 0, -1, 4, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, 7, 3, 0, -5, 5, 0, -6, 6, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 7, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 11, 0, 0, 7, 0, -1, 16, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 8, 1, 34], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17], [-1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 4, 1, -1, 1, -1, 5], [0, 5, 0, 6, 0, 7, 0, 8, 0, 9, 0, 1, 0, 1, 0, 2, 0, 3, 0, 3, 0, 10, 11, 0, 2, 4, 4]]