[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "bfVT8MZ0JMR6N8ANC71gzm", "dfRaTJk4RHnqJ2YXF9KLL5", "72nRyiJWFIv5chbefvt2W6", "67F0Dl96dIe76nOyajQZfj", "1dq7lDgHJIHbDDezWkZ3F0", "c7yVewKYtF/50x0kgFkGZ2"], ["node", "_spriteFrame", "_file", "_parent", "_defaultClip", "root", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_trs", "_components", "_parent", "_children", "_contentSize", "_eulerAngles", "_anchorPoint"], 1, 4, 7, 9, 1, 2, 5, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angleVar", "startSize", "startSizeVar", "endSize", "endSizeVar", "startSpinVar", "endSpinVar", "_positionType", "emitterMode", "speed", "speedVar", "tangentialAccel", "startRadius", "startRadiusVar", "endRadius", "endRadiusVar", "rotatePerS", "rotatePerSVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "gravity", "_file", "_spriteFrame"], -20, 1, 3, 8, 8, 8, 8, 5, 6, 6], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 3], [0, 0, 5, 4, 2, 3, 2], [0, 0, 1, 5, 4, 2, 7, 3, 8, 3], [0, 0, 5, 4, 2, 7, 3, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 24], [2, 0, 2], [0, 0, 6, 4, 2, 2], [0, 0, 1, 5, 6, 2, 3, 3], [0, 0, 6, 2, 3, 2], [0, 0, 1, 5, 4, 2, 7, 3, 3], [0, 0, 6, 2, 2], [0, 0, 5, 4, 2, 7, 9, 3, 2], [1, 1, 2, 1], [5, 0, 1, 2, 3, 2]], [[6, "effect_hq_02"], [7, "effect_hq_02", [-3], [[14, true, -2, [19], 18]], [13, -1, 0]], [9, "effect_wssn_02", [-4, -5, -6, -7, -8], [0, "11RzxWE3pMtIgeZ+xRI0Cz", 1, 0], [0, 0, 0, 0, 0, 0, 1, -0.35, 0.35, -0.35]], [11, "effect_wssn_02-2", [-9, -10, -11], [0, "c1TrVw8r5ICa+cllhXmnRf", 1, 0]], [8, "02", 0, 1, [2, 3], [0, "2bkMnjEWtMLLa/RmSUGusG", 1, 0], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [2, "effect_wssn_02_cloud01", 2, [[5, true, 40, 999, 0.22, 0.03, 180, 70, 17, 20, 7, 30, 60, 1, 1, 500, 200, 0, 270, 5, 95, 5, -300, 50, -12, [0], [4, 1191182335], [4, 83886080], [4, 687865855], [4, 0], [0, 0.25, 0.8600000143051147], 1, 2]], [0, "4fWKf6b7FJfaGKCJz1Iikk", 1, 0], [-5.824, 83.492, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "effect_wssn_02_cloud02", 2, [[5, true, 35, 999, 0.16, 0.01, 180, 100, 17, 30, 7, 30, 60, 1, 1, 500, 200, 0, 350, 2, 100, 5, -300, 50, -13, [3], [4, 1191182335], [4, 83886080], [4, 687865855], [4, 0], [0, 0.25, 0.8600000143051147], 4, 5]], [0, "e4PxEYIG9GlZunnh2yZpUV", 1, 0], [-5.824, 83.492, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "effect_wssn_02_wind", 200, 2, [[1, 2, false, -14, [6], 7]], [0, "c3fU07JrhIzqju1U7S04NQ", 1, 0], [5, 512, 512], [-5.824, 83.492, 0, 0, 0, 0, 1, 2, 2, 2]], [3, "effect_wssn_02_wind02", 200, 2, [[1, 2, false, -15, [8], 9]], [0, "de0/1J3llAWYIcnRIhlojY", 1, 0], [5, 512, 512], [-5.824, 83.492, 0, 0, 0, -0.043619387365336, 0.9990482215818578, 2, 2, 2], [1, 0, 0, -5]], [3, "effect_wssn_02_wind03", 200, 2, [[1, 2, false, -16, [10], 11]], [0, "28wFJPIPJJd55U3eBJvCfS", 1, 0], [5, 512, 512], [-5.824, 83.492, 0, 0, 0, -0.043619387365336, 0.9990482215818578, 2, 2, 2], [1, 0, 0, -5]], [4, "effect_wssn_smoker01", 3, [[1, 2, false, -17, [12], 13]], [0, "7dGUaXHrJJBrUq/RnfU57Y", 1, 0], [5, 174, 174], [-37.0384, 18.7222, 0, 0, 0, 0, 1, 0.7, 0.7, 0.7]], [4, "effect_wssn_smoker02", 3, [[1, 2, false, -18, [14], 15]], [0, "836rnhMSRI66fXb4erOU/p", 1, 0], [5, 174, 174], [32.9616, 18.7222, 0, 0, 0, 0, 1, -0.7, 0.7, -0.7]], [12, "wssn_skill2_01", 3, [[1, 2, false, -19, [16], 17]], [0, "94/JQjCvhMHrYYF4S0XYez", 1, 0], [5, 170, 170], [0, 0.5, 0], [0.54145, -34.10785, 0, 0, 0, 0, 1, 1.4, 1.4, -1.4]]], 0, [0, 5, 1, 0, 0, 1, 0, -1, 4, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 6, 1, 2, 3, 4, 3, 3, 4, 19], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, 1, -1, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 4, -1], [0, 1, 2, 0, 1, 2, 0, 6, 0, 3, 0, 3, 0, 4, 0, 4, 0, 7, 5, 5]]