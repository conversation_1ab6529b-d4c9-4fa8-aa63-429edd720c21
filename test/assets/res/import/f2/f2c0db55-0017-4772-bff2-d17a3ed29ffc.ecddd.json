[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "eePwXB6PdGXqenwgoq7NKH", "18ed931PVDtZRwBhNcEkyP"], ["node", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color"], 1, 9, 4, 5, 1, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["3039cCGLWFIeqCPOGQnWhje", ["scaleMin", "scaleMax", "node"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["e66516Y7FxD8pT44eW+pR4O", ["node"], 3, 1]], [[3, 0, 1, 2, 2], [0, 0, 5, 2, 3, 4, 6, 2], [1, 1, 2, 1], [0, 0, 5, 2, 3, 4, 2], [1, 1, 2, 3, 1], [2, 0, 3, 2], [4, 0, 2], [0, 0, 7, 2, 3, 4, 6, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [0, 0, 5, 7, 2, 3, 4, 2], [0, 0, 5, 2, 3, 8, 4, 6, 2], [1, 0, 1, 2, 3, 2], [2, 0, 1, 2, 3, 4], [5, 0, 1, 2, 3], [3, 1, 2, 1], [6, 0, 1], [7, 0, 1, 2, 3, 4, 5, 6, 6], [8, 0, 1]], [[6, "wxQunView"], [7, "qqQun<PERSON>iew", [-4, -5, -6], [[5, 45, -2], [17, -3]], [14, -1, 0], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "content", 1, [-8, -9, -10, -11, -12, -13], [[5, 45, -7]], [0, "31pkJhHlRHWbqoVc4sApSO", 1, 0], [5, 768, 1366]], [8, "maskBg", 204, 1, [[11, 0, -14, [0], 1], [12, 45, 100, 100, -15], [13, 1, 1, -16]], [0, "26o1hrpuVCdZiAzsPSCNHR", 1, 0], [4, 4278190080], [5, 768, 1366]], [3, "New Node", 2, [[15, -17]], [0, "5cOKT9aENOC6pfPs0RSTfN", 1, 0], [5, 768, 500]], [3, "bg", 2, [[2, -18, [2]]], [0, "d2UkXRNUBIjY9LsoYD6vKz", 1, 0], [5, 768, 825]], [1, "img_hero", 2, [[2, -19, [3]]], [0, "f1Hs/VO29ArbrhFbHFSaWP", 1, 0], [5, 314, 380], [0, 278.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_title", 2, [[4, -20, [4], 5]], [0, "7fwaGyiN5BBqp8R6nuQkIb", 1, 0], [5, 449, 128], [0, 122.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_QrCode", 2, [[2, -21, [6]]], [0, "99sbrgtWVLfYcFFqbzXhik", 1, 0], [5, 254, 254], [0, -74.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_title_sanma", 2, [[4, -22, [7], 8]], [0, "6ax8ydtlpIobbOXhMIECaH", 1, 0], [5, 334, 54], [0, -240.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "CloseTips", 1, [[16, "点击空白区域关闭界面", 24, 0, 1, 1, -23, [9]]], [0, "d9OV6yuyBOmqtPuDZ9Oj1Q", 1, 0], [4, 4288059030], [5, 240, 30.24], [0, -409.88, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, -3, 10, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 3, 1, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, -1, 1, -1, -1, 1, -1], [0, 1, 0, 0, 0, 2, 0, 0, 3, 0]]