[1, ["ecpdLyjvZBwrvm+cedCcQy", "a5iF1genBGdrjA+oAJ7PBZ", "b3NLdOgalAqqKb5aJ6iIgb", "364AiZ+MhP9LtPsFWoTaTG", "d54h52Ei9Mgp47BCRd0BZ5", "2eLgv8AiBEbqnBrdMV6s04", "d1+zBzZYVGHr9JnrJ9wWBt"], ["node", "_spriteFrame", "_defaultClip", "root", "data"], [["cc.Node", ["_name", "_prefab", "_trs", "_parent", "_components", "_contentSize", "_children", "_color", "_anchorPoint"], 2, 4, 7, 1, 9, 5, 2, 5, 5], ["cc.Sprite", ["_dstBlendFactor", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[2, 0, 1, 2, 2], [0, 0, 3, 6, 4, 1, 2], [0, 0, 3, 4, 1, 5, 2, 2], [1, 3, 4, 5, 1], [4, 0, 1, 2, 3, 2], [3, 0, 2], [0, 0, 6, 1, 2, 2], [0, 0, 3, 4, 1, 7, 5, 2, 2], [0, 0, 3, 4, 1, 5, 8, 2, 2], [1, 0, 3, 4, 5, 2], [1, 1, 2, 3, 4, 5, 3], [2, 1, 2, 1]], [[5, "guide_dianji"], [6, "guide_dianji", [-2, -3], [11, -1, 0], [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "guide_dianji_01", 1, [-5, -6, -7], [[4, true, -4, [11], 10]], [0, "26MxOAXTBOYZPMceSx/Zu7", 1, 0]], [1, "guide_dianji_02", 1, [-9], [[4, true, -8, [3], 2]], [0, "90EpLWpRBFI5meRoUrVXFt", 1, 0]], [7, "img_glow_0014", 3, [[9, 1, -10, [0], 1]], [0, "fccKreQIpDfrslUo+W3evU", 1, 0], [4, 4284009983], [5, 224, 224], [0, 0, 0, 0, 0, 0, 1, 1.42857, 1.42857, 1.42857]], [2, "img_guanquan02", 2, [[3, -11, [4], 5]], [0, "7blaT3l5ZJCYcXY3MncINZ", 1, 0], [5, 143, 143], [1.631, -0.475, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_guanquan01", 2, [[3, -12, [6], 7]], [0, "26tmj5Dv9EhLjKDQwRTm+O", 1, 0], [5, 180, 180], [1.631, -0.475, 0, 0, 0, 0, 1, 0.55, 0.55, 0.55]], [8, "img_shouzhi", 2, [[10, 2, false, -13, [8], 9]], [0, "4dRZx1IstAgLEa1kXcaKsH", 1, 0], [5, 104, 93], [0, 0.7, 0.3], [123.297, -71.193, 0, 0, 0, 0, 1, 2, 2, 2]]], 0, [0, 3, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, 0, 3, 0, -1, 4, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 4, 1, 13], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, 2, -1, -1, 1, -1, 1, -1, 1, 2, -1], [0, 3, 1, 1, 0, 4, 0, 5, 0, 6, 2, 2]]