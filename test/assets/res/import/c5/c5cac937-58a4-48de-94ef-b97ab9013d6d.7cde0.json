[1, ["56FrJ7sDZMHL5QMKbpKkEq", "2cCTjubTxPdrORQoRKh/Na", "7aLJka+clMk7TgC/uz424G", "e58dYlxcJMp5f2fBJYQsyv", "b6S7Au+0hK8Yd5IAYtYyhN", "43Pshi0WNOi5XSt4ATxnMF", "d2vnN1NjlK97vkpaatRaUn", "fa+oIs7yxIOpQ53JFZ7Vjm", "0f0y76NlFFKodg1DLazc/A", "1dzCNlwyRAQLqlWpON378q", "39qwxzxhtLbpKEeOsLuMRy", "9cnUtCRHxPo7fcEyggTpqF", "77pxIfKeZBsJzK9/badByA"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "cuttrees", 0.375, 24, 0.5, 2, [{"props": {"position": [{"frame": 0, "curve": "constant", "value": [-18.3865, -38, 0]}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.041666666666666664}, "value", 6, 1], [{"frame": 0.08333333333333333}, "value", 6, 2], [{"frame": 0.125}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4], [{"frame": 0.20833333333333334}, "value", 6, 5], [{"frame": 0.25}, "value", 6, 6], [{"frame": 0.2916666666666667}, "value", 6, 7], [{"frame": 0.3333333333333333}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "paths", 11, [{}, "futou", 11, [{"props": {"position": [{"frame": 0, "curve": "constant", "value": [20.194, 48.716, 0]}, {"frame": 0.041666666666666664, "curve": "constant", "value": [7.584, 109.298, 0]}, {"frame": 0.08333333333333333, "curve": "constant", "value": [72.278, 107.549, 0]}, {"frame": 0.125, "curve": "constant", "value": [84.364, 94.489, 0]}, {"frame": 0.16666666666666666, "curve": "constant", "value": [86.156, 75.523, 0]}, {"frame": 0.20833333333333334, "curve": "constant", "value": [23.006, 123.194, 0]}, {"frame": 0.25, "curve": "constant", "value": [-14.724, 46.51, 0]}, {"frame": 0.2916666666666667, "curve": "constant", "value": [-8.431, 39.915, 0]}, {"frame": 0.3333333333333333, "value": [6.986, 39.374, 0]}], "angle": [{"frame": 0, "value": 54.778999999999996, "curve": "constant"}, {"frame": 0.041666666666666664, "value": -10, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -74.961, "curve": "constant"}, {"frame": 0.125, "value": -111.61, "curve": "constant"}, {"frame": 0.16666666666666666, "value": -122.782, "curve": "constant"}, {"frame": 0.20833333333333334, "value": -6.176000000000002, "curve": "constant"}, {"frame": 0.25, "value": 88.027, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 107.322, "curve": "constant"}, {"frame": 0.3333333333333333, "value": 104.943}], "active": [{"frame": 0, "value": true}], "anchorX": [{"frame": 0, "value": 0.5}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 9]], 11]]]], "futou/wei", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.25, "value": 255, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0.20833333333333334, "value": 12.253, "curve": "constant"}, {"frame": 0.25, "value": -87.206}], "position": [{"frame": 0.20833333333333334, "curve": "constant", "value": [82.587, 8.032, 0]}, {"frame": 0.25, "value": [63.418, 18.76, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334, "curve": "constant"}, "value", 8, [1, 1.7, 1.7, 1]], [{"frame": 0.25}, "value", 8, [1, 1.916, 2.327, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 10], [{"frame": 0.25}, "value", 6, 11]], 11, 11]]]], "futou/shou", 11, [{"props": {"position": [{"frame": 0, "value": [24.626, -26.576, 0]}], "active": [{"frame": 0, "value": true}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 12]], 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]