[1, ["56XDKx1cpBM47Lnl65qNjN", "b5eHaoAutMaqLwUSaFAOMU", "80ZJSDQzpJn45SjcPyehnC", "85VZvNBkVEGb3GWwkZRpDh", "b1uycbi2JOtKl9bRiaRqnu", "f8zsV0QwdH671/eoIuwUMy", "feDfJc1XxEoYiNNq+zq1Ey", "88vs2c/5NFaYS2hzZcLxCo", "93NwzPBDNAWaLn+P+qs81U", "e2oDh1GAFN5Y8Oy26+B6sE", "bf00fynJZF2J3Lp0k7mlmx", "39qwxzxhtLbpKEeOsLuMRy", "9cnUtCRHxPo7fcEyggTpqF"], ["value"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[0, 0, 1, 2, 3, 4, 5, 6]], [[0, "mine", 0.375, 24, 0.5, 2, [{"props": {"y": [{"frame": 0, "value": -6.5}], "x": [{"frame": 0, "value": 4}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.041666666666666664}, "value", 6, 1], [{"frame": 0.08333333333333333}, "value", 6, 2], [{"frame": 0.125}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4], [{"frame": 0.20833333333333334}, "value", 6, 5], [{"frame": 0.25}, "value", 6, 6], [{"frame": 0.2916666666666667}, "value", 6, 7], [{"frame": 0.3333333333333333}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "paths", 11, [{}, "futou", 11, [{"props": {"position": [{"frame": 0, "curve": "constant", "value": [-5.854, 17.563, 0]}, {"frame": 0.041666666666666664, "curve": "constant", "value": [25.933, 67.517, 0]}, {"frame": 0.08333333333333333, "curve": "constant", "value": [51.983, 71.083, 0]}, {"frame": 0.125, "curve": "constant", "value": [65.166, 59.406, 0]}, {"frame": 0.16666666666666666, "curve": "constant", "value": [72.227, 53.343, 0]}, {"frame": 0.20833333333333334, "curve": "constant", "value": [-10.6, 88.252, 0]}, {"frame": 0.25, "curve": "constant", "value": [-27.678, 3.293, 0]}, {"frame": 0.2916666666666667, "curve": "constant", "value": [-31.793, 9.465, 0]}, {"frame": 0.3333333333333333, "value": [-29.113, 7.954, 0]}], "angle": [{"frame": 0, "value": 54.778999999999996, "curve": "constant"}, {"frame": 0.041666666666666664, "value": -10, "curve": "constant"}, {"frame": 0.08333333333333333, "value": -74.961, "curve": "constant"}, {"frame": 0.125, "value": -111.61, "curve": "constant"}, {"frame": 0.16666666666666666, "value": -122.782, "curve": "constant"}, {"frame": 0.20833333333333334, "value": -6.176000000000002, "curve": "constant"}, {"frame": 0.25, "value": 88.027, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 67.319, "curve": "constant"}, {"frame": 0.3333333333333333, "value": 82.656}], "active": [{"frame": 0, "value": true}], "anchorX": [{"frame": 0, "value": 0.42}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 9]], 11]]]], "futou/shou", 11, [{"props": {"position": [{"frame": 0, "value": [24.626, -26.576, 0]}], "active": [{"frame": 0, "value": true}], "angle": [{"frame": 0, "value": -49.306}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 10]], 11]]]], "futou/wei", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "constant"}, {"frame": 0.20833333333333334, "value": 255}, {"frame": 0.25, "value": 255, "curve": "constant"}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0.20833333333333334, "value": 12.253, "curve": "constant"}, {"frame": 0.25, "value": -87.206}], "position": [{"frame": 0.20833333333333334, "curve": "constant", "value": [88.456, 27.118, 0]}, {"frame": 0.25, "value": [92.015, 33.049, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334, "curve": "constant"}, "value", 8, [1, 2.079, 1.238, 1]], [{"frame": 0.25}, "value", 8, [1, 1.2, 2.191, 1]]], 11, 11]], "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.20833333333333334}, "value", 6, 11], [{"frame": 0.25}, "value", 6, 12]], 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]