[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "bcsdk.d", "declare class BCSDK {\n  init(game_id: Number, product_key: String): Promise<any>;\n\n  /**\n   * 获取用户信息\n   */\n  getUserInfo(): Promise<any>;\n\n  /**\n   * 执行登录\n   *\n   */\n  login(): Promise<any>;\n\n  /**\n   * SDK封禁退登回调\n   */\n  logoutCallback(callback: Function): Promise<any>;\n\n  /**\n   * 退出当前登录帐号\n   *\n   */\n  logout(): Promise<any>;\n\n  /**\n   * 发起支付\n   *\n   * @param orderInfo\n   */\n  pay(orderInfo: Object): Promise<any>;\n\n  /**\n   * 上报角色数据\n   *\n   * @param action\n   * @param roleInfo\n   */\n  reportData(action: String, roleInfo: Object): Promise<any>;\n\n  /**\n   * 打开客服页面\n   */\n  openCustomer(): Promise<any>;\n\n  /**\n   * 显示/隐藏客服虚浮按钮\n   */\n  switchCustomerBtn(show: Boolean): Promise<any>;\n\n  /**\n   *\n   * @param any\n   */\n  log(any: any): void;\n\n  /**\n * 聊天内容检测\n * @param content \n */\n  msgSecCheck(content: object): Promise<any>;\n}\n\ninterface Window {\n  BCSDK: BCSDK;\n}"]], 0, 0, [], [], []]