[1, ["a8yiOukOJIzKHvATSCh6hr", "ecpdLyjvZBwrvm+cedCcQy", "1dT/qAQJJCarHQ+MHGSCKu", "f3xWAUiWtAOqu1Yh0ODSge", "5bAzMYuZRE14pjupyZDv86", "79GS/p6BlCvacMiokXIDzA", "1fl7GEX5dMR6puVIg4RJGF", "fcRelvk3pJuYP2Xvd0go26", "e8VlVZNF5AnZgTynRkNuSt", "8aQe77MNJB6ows1z9yd5vh", "b9EmMdh7VMYYSeHwefdCDj", "d8em+p/VdHDaFDOTtn6osH", "5bo5FLzeNKuIk9qmRJyIDz"], ["_textureSetter", "node", "value", "_spriteFrame", "LoadProgress", "LoadAnim", "scene", "_parent", "_defaultClip"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_id", "_components", "_contentSize", "_parent", "_trs", "_children"], 1, 9, 5, 1, 7, 2], "cc.Texture2D", ["cc.Widget", ["_alignFlags", "_originalWidth", "node"], 1, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Node", ["_name", "_parent", "_components", "_contentSize", "_trs"], 2, 1, 12, 5, 7], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["303621s+45CUpnzgnBU8n94", ["debug", "test", "channelId", "apiUrl", "gameVersion", "sdkName", "node", "LoadAnim", "LoadProgress"], -3, 1, 1, 1], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["cc.Animation", ["playOnLoad", "node", "_clips"], 2, 1, 3], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials"], -2, 1, 3], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[6, 0, 1, 2, 3, 4, 2], [3, 0, 2, 2], [5, 0, 1, 3], [1, 0, 1, 6, 2, 3, 5, 3], [1, 0, 4, 2, 3, 2], [1, 0, 4, 2, 2], [1, 0, 1, 4, 2, 3, 5, 3], [7, 0, 1, 1], [3, 0, 1, 2, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [9, 0, 1, 2, 3, 4, 3], [4, 0, 2, 3, 4, 2], [4, 0, 1, 2, 3, 4, 3], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 3, 4, 5, 6]], [[[{"name": "login_move_1", "rect": [217, 1, 108, 106], "offset": [2, 1], "originalSize": [114, 114], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "login_move_2", "rect": [645, 1, 106, 104], "offset": [2, 0], "originalSize": [114, 114], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [["1,9729,9729,33071,33071,0,0,1", -1], [2], 0, [], [], []], [[{"name": "login_move_8", "rect": [539, 1, 104, 106], "offset": [-1, 1], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "bg_denglubeijin", "rect": [0, 0, 750, 1667], "offset": [0, 0], "originalSize": [750, 1667], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [4]], [[{"name": "login_move_5", "rect": [325, 1, 108, 106], "offset": [-3, -2], "originalSize": [114, 114], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [["0,9729,9729,33071,33071,0,0,1", -1], [2], 0, [], [], []], [[{"name": "login_move_6", "rect": [751, 1, 106, 104], "offset": [-2, -1], "originalSize": [114, 114], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "login_move_7", "rect": [109, 1, 106, 108], "offset": [-2, 2], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[2, "MainScene", null], [3, "<PERSON><PERSON>", "e3W7LqzvxJrqv6aAkeNzP5", [-6, -7, -8, -9], [[7, -1, [5, 768, 1366]], [1, 45, -2], [9, "0", "1", 2, "http://118.31.11.62:9001", 10011, "SdkBase", -5, -4, -3]], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "New Node", false, [1, -10], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg", 1, [[11, 0, -11, [0], 1], [8, 40, 768, -12]], [5, 768, 1667]], [0, "loadMovie", 1, [[[12, 2, false, -13, [2], 3], -14], 4, 1], [5, 114, 114], [0, -408, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "lblProgress", 1, [[-15, [13, 2, -16, [4, 4278190080]]], 1, 4], [5, 100, 54.4], [0, -408, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Main Camera", 1, [[14, 7, -1, -17]]], [15, true, 4, [4]], [16, "0%", 28, 1, 1, 3, 5, [5]], [6, "emptyNode", "6bvfJ1LzVITbrDeKWIiTGA", 2, [[1, 45, -18]], [5, 768, 1366], [384, 683, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 1, 1, 0, 1, 1, 0, 4, 8, 0, 5, 7, 0, 1, 1, 0, -1, 6, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -2, 9, 0, 1, 3, 0, 1, 3, 0, 1, 4, 0, -2, 7, 0, -1, 8, 0, 1, 5, 0, 1, 6, 0, 1, 9, 0, 6, 2, 1, 7, 2, 18], [0, 0, 0, 0, 0, 0, 7], [-1, 3, -1, 3, -1, -1, 8], [1, 5, 1, 2, 3, 1, 3]], [[{"name": "login_move_4", "rect": [433, 1, 104, 106], "offset": [0, -3], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[17, "move", 0.3, 30, 0.3, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.03333333333333333}, "value", 6, 0], [{"frame": 0.06666666666666667}, "value", 6, 1], [{"frame": 0.1}, "value", 6, 2], [{"frame": 0.13333333333333333}, "value", 6, 3], [{"frame": 0.16666666666666666}, "value", 6, 4], [{"frame": 0.2}, "value", 6, 5], [{"frame": 0.23333333333333334}, "value", 6, 6], [{"frame": 0.26666666666666666}, "value", 6, 7]], 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2], [2, 6, 7, 8, 9, 10, 11, 12]], [[{"name": "login_move_3", "rect": [1, 1, 106, 108], "offset": [1, -3], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]