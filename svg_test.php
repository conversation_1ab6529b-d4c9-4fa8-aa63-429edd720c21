<?php
/**
 * SVG 输出测试
 */

// 包含所有必要文件
require_once 'jyotish/src/Base/Traits/OptionTrait.php';
require_once 'jyotish/src/Base/Traits/GetTrait.php';
require_once 'jyotish/src/Base/Utility.php';
require_once 'jyotish/src/Renderer/AbstractRenderer.php';
require_once 'jyotish/src/Renderer/Svg.php';
require_once 'jyotish/src/Draw.php';

use Jyotish\Draw\Draw;

try {
    // 创建 400x300 像素的 SVG
    $draw = new Draw(400, 300, Draw::RENDERER_SVG);
    
    // 绘制标题
    $draw->drawText("Jyotish SVG 测试", 200, 30, [
        'fontColor' => '#000080',
        'textAlign' => 'center'
    ]);
    
    // 绘制说明文字
    $draw->drawText("SVG 渲染演示", 50, 60, [
        'fontColor' => '#333333'
    ]);
    
    // 绘制三角形
    $trianglePoints = [
        200, 80,   // 顶点
        150, 150,  // 左下
        250, 150   // 右下
    ];
    $draw->drawPolygon($trianglePoints, [
        'strokeColor' => '#FF0000',
        'strokeWidth' => 2,
        'fillColor' => '#FFE0E0'
    ]);
    
    // 在三角形旁边添加标签
    $draw->drawText("三角形", 270, 120, [
        'fontColor' => '#FF0000'
    ]);
    
    // 绘制矩形
    $rectanglePoints = [
        50, 180,   // 左上
        350, 180,  // 右上
        350, 250,  // 右下
        50, 250    // 左下
    ];
    $draw->drawPolygon($rectanglePoints, [
        'strokeColor' => '#00AA00',
        'strokeWidth' => 3,
        'fillColor' => '#E0FFE0'
    ]);
    
    // 在矩形内部添加文字
    $draw->drawText("矩形区域", 200, 215, [
        'fontColor' => '#00AA00',
        'textAlign' => 'center'
    ]);
    
    // 绘制一个六边形
    $hexagonPoints = [
        200, 270,  // 顶
        230, 285,  // 右上
        230, 315,  // 右下
        200, 330,  // 底
        170, 315,  // 左下
        170, 285   // 左上
    ];
    $draw->drawPolygon($hexagonPoints, [
        'strokeColor' => '#8000FF',
        'strokeWidth' => 2,
        'fillColor' => '#F0E0FF'
    ]);
    
    // 添加版权信息
    $draw->drawText("© 2024 Jyotish SVG Demo", 350, 350, [
        'fontColor' => '#666666',
        'textAlign' => 'right'
    ]);
    
    // 渲染并输出 SVG
    $draw->render();
    
} catch (Exception $e) {
    // 如果出错，输出错误信息
    header('Content-Type: text/plain');
    echo "生成 SVG 时发生错误: " . $e->getMessage() . "\n";
    echo "错误详情:\n" . $e->getTraceAsString();
}
?>
