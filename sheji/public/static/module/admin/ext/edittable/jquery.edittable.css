/* Table */
table.inputtable {
	width: 100%;margin: 15px 0;border: 1px solid #ddd;border-collapse: collapse;border-spacing: 0;
	-moz-box-shadow:0 1px 3px rgba(0,0,0,.075);
	-webkit-box-shadow:0 1px 3px rgba(0,0,0,.075);
	box-shadow:0 1px 3px rgba(0,0,0,.075)
}
/* Buttons */
table.inputtable a.icon-button {
	background-color: #ccc;display: inline-block;width: 18px;height: 18px;text-decoration: none;color: #fff;font-weight: 800;line-height: 16px;text-align: center;font-size: 14px;
	-moz-border-radius: 1px;
	-webkit-border-radius: 1px;
	border-radius: 1px;
	-moz-box-shadow: 0 0 1px rgba(0,0,0,0.2);
	-webkit-box-shadow: 0 0 1px rgba(0,0,0,0.2);
	box-shadow: 0 0 1px rgba(0,0,0,0.2);
}
/* Add Buttons */
table.inputtable a.icon-button.addcol, table.inputtable a.icon-button.addrow {
	background-color: #81b71a;
}
/* Remove Buttons */
table.inputtable a.icon-button.delcol, table.inputtable a.icon-button.delrow {
	background-color: #db4a39;
}
/* Disabled Buttons */
table.inputtable a.icon-button.disabled {
	background-color: #eee;
}
/* Row last cel */
table.inputtable td:last-child, table.inputtable th:last-child{
	background-color: #f8f8f8;width: 54px;border: none;
}
/* Table single cells */
table.inputtable td, table.inputtable th {
	border: 1px solid #eee;text-align: center;height: 40px;vertical-align: middle;font-size: 14px;
}
/* Table headers single cells */
table.inputtable th {
	background-color: #f1f1f1;
	border-color: #ddd;
    border-top:none;
    border-bottom: 2px solid #ddd;
}
/* Table body inputs */
table.inputtable td input[type=text] {
	border: 0;width: 90%;height: 100%;padding: 0 5%;text-align: center;
}
/* Table body inputs on focus */
table.inputtable tr td input:focus {
	background-color: #fafafa;
}
/* First body row cells & input on table without columns header */
table.inputtable.wh tbody tr:nth-child(1), table.inputtable.wh tbody tr:nth-child(1) input {
	background-color: #fdfdfd;font-weight: 800;
}
/* Hide borders when not needed hack */
table.inputtable th:first-child, table.inputtable td:first-child {
    border-left:none;
}
table.inputtable tr:last-child td {
    border-bottom:none;
}
/* Mobile Landscape */
@media only screen and (max-width : 480px) { 
	/* Table single cells */
	table.inputtable td, table.inputtable th {
		min-width: 40px;height: 80px;
	}
	/* Buttons */
	table.inputtable a.icon-button {
		width: 40px;height: 40px;font-size: 18px;min-width: 40px;line-height: 40px;
		margin: 3px 0;
	}
	/* Table body inputs */
	table.inputtable td input {
		height: 80px;
	}
}