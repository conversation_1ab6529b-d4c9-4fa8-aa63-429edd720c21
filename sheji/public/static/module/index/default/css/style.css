
:root {
  --title-size: 13.695rem;
}
html {
  --g-s: 1;
  font-size: calc(10px * 0.84 * var(--g-s));
  -ms-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  scrollbar-width: none;
  -webkit-text-size-adjust: 100%;
  -webkit-box-direction: normal;
}
body {
  font: 2.4rem/1.200192 "pf-semibold", "PingFangSC-Semibold", "PingFang SC", "HanHei SC", "Helvetica Neue", Microsoft Yahei, "Helvetica", "STHeitiSC-Light", "Arial", sans-serif, Helvetica, sans-serif;
  letter-spacing: -0.052075rem;
  margin: 0 auto;
  color: #000;
  background-color: #fff;
  overscroll-behavior: none;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
}
.pf-sd {
  font-family: "pf-semibold";
}
.pf-re {
  font-family: "pf-regular";
}
.lc-h1 {
  line-height: 1.499826;
}
.lc-h2 {
  line-height: 1.60009;
}
.lc-h3 {
  line-height: 1.3438;
}
.lc-h4 {
  line-height: 1.40038;
}
.lc-h5 {
  line-height: 1.57113;
}
.lc-h6 {
  line-height: 1.8;
}
.lc-h7 {
  line-height: 1.9994;
}
.time-size {
  font-size: 3.9678rem;
}
.view-size {
  font-size: 2.887rem;
}
.info-size {
  font-size: 3.4726rem;
}
.leader-size {
  font-size: 1.9845rem;
}
.top {
  position: relative;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 1000;
}
.htop {
  position: fixed;
}
.navbox {
  margin: 0 1.5rem;
  overflow: hidden;
  position: relative;
  height: 5.6rem;
}
.navbox::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: #000000;
}
.nav {
  display: flex;
  color: #000000;
  margin-left: -1.5rem;
}
.nav > li {
  width: 33.33%;
  padding-left: 1.5rem;
}
.nav > li > a {
  height: 5.5rem;
  display: flex;
  align-items: center;
}
.nav a {
  color: #000000;
}
.top-fixed {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  background-color: #ffffff;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
}
.top-fixed.showon {
  pointer-events: all;
  opacity: 1;
  transition: opacity 0.15s ease;
}
.top-logo {
  border-bottom: 1px solid #000000;
}
.logo {
  display: flex;
}
.logo a {
  position: relative;
  display: block;
  line-height: 1;
  padding: 1.7rem 0;
}
.logo img {
  height: 3.8rem;
  display: block;
}
.banner {
  position: relative;
  width: 100%;
  height: 100vh;
  color: #ffffff;
  overflow: hidden;
}
.banner a {
  color: #ffffff;
}
.banner .swiper-slide {
  position: relative;
  height: 100%;
}
.banner .imgBox {
  height: 100%;
  position: relative;
  overflow: hidden;
  -webkit-mask-image: linear-gradient(to right, #000 33.3%, rgba(0, 0, 0, 0) 66.6%);
  -webkit-mask-position: left center;
  -webkit-mask-size: 300% 100%;
  -webkit-mask-repeat: no-repeat;
}
.banner .imgBox img {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  object-fit: cover;
}
.banner .swiper-slide-active .imgBox {
  animation: headerTitle 3s cubic-bezier(0.23, 1, 0.32, 1) both;
}
.banner .number {
  mix-blend-mode: difference;
  position: absolute;
  left: auto;
  right: 1.7857%;
  bottom: 1.7857%;
  width: auto;
  font-weight: bold;
  z-index: 500;
}
.mouse-pointer {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 9999999;
  pointer-events: none;
  mix-blend-mode: difference;
}
.mouse-pointer .pointer-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition-timing-function: ease;
  transition-duration: 0.23s;
  border-radius: 50%;
  background-color: #ffffff;
}
.mouse-pointer .pointer-dian {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: #666666;
  opacity: 0;
  visibility: hidden;
}
.mouse-pointer .pointer-txt {
  position: absolute;
  left: 50%;
  top: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #000;
  font-weight: 600;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: translate(-50%, -50%);
}
.mouse-pointer.hover {
  cursor: none;
}
.mouse-pointer.hover .pointer-circle {
  opacity: 1;
  width: 50px;
  height: 50px;
}
.mouse-pointer.hover .pointer-dian {
  opacity: 1;
  visibility: visible;
}
.mouse-pointer.active .pointer-circle {
  opacity: 1;
}
.mouse-pointer.drag {
  opacity: 0.9;
}
.mouse-pointer.drag .pointer-circle {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid #666666;
}
.mouse-pointer.drag .pointer-txt {
  opacity: 1;
  width: 100px;
  height: 100px;
}
.page-H {
  height: 5.7rem;
}
.section-gx {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.page-title {
  font-size: var(--title-size);
  font-weight: normal;
  line-height: 0.74;
  margin-top: 0.7rem;
  letter-spacing: 0;
  padding-bottom: 4rem;
}
.tlogo-nav-ul {
  display: flex;
  color: #000000;
  margin-left: -1.5rem;
}
.tlogo-nav-ul a {
  color: #000000;
}
.tlogo-nav-ul .item1 {
  width: 33.33%;
  padding-left: 1.5rem;
}
.tlogo-nav-ul .item2 {
  width: 66.66%;
  display: flex;
}
.work-subnav {
  width: 50%;
  padding-left: 1.5rem;
  padding-top: 1.3rem;
  padding-bottom: 4rem;
}
.work-subnav a {
  position: relative;
  display: flex;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.work-subnav a::before {
  content: "·";
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.work-subnav a.active {
  padding-left: 1.2rem;
}
.work-subnav a.active::before {
  opacity: 1;
}
.work-page {
  margin-left: calc(0.5 * 1.5rem);
  margin-right: calc(0.5 * 1.5rem);
}
.work-list {
  display: flex;
  flex-wrap: wrap;
}
.work-list a {
  color: #000000;
}
.work-list a:hover {
  color: #666666;
}
.work-list > li {
  width: 33.33%;
  padding-left: calc(0.5 * 1.5rem);
  padding-right: calc(0.5 * 1.5rem);
  margin-top: 1.5rem;
}
.work-list > li:hover .imgwh {
  transform: scale(1.1);
}
.work-list .item {
  position: relative;
}
.work-list .imgBox {
  position: relative;
  overflow: hidden;
  padding-bottom: 100%;
}
.work-list .imgBox::before,
.work-list .imgBox::after {
  content: "";
  position: absolute;
  height: 1px;
  background-color: #000000;
  left: 0;
  width: 100%;
  z-index: 1;
}
.work-list .imgBox::before {
  top: 0;
}
.work-list .imgBox::after {
  bottom: 0;
}
.work-list .imgBox .imgwh {
  position: absolute;
  left: 0;
  top: 0;
  transition: 0.5s;
  -webkit-transition: 0.5s;
}
.work-list .desc {
  padding: 1rem 0;
  border-bottom: 1px solid #000000;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.work-list .title {
  font-weight: normal;
}
.awards-list {
  display: flex;
}
.awards-list dt {
  width: 6.8rem;
}
.awards-list dd {
  flex: 1;
}
.foot {
  border-top: 1px solid #000000;
  padding: 2.6rem 0;
}
.foot-nav {
  display: flex;
  margin-left: -1.5rem;
}
.foot-nav a {
  color: #000000;
}
.foot-nav a:hover {
  color: #9e9e9e;
}
.foot-nav > li {
  width: 33.33%;
  padding-left: 1.5rem;
  display: flex;
  align-items: flex-end;
}
.foot-nav > li:last-child {
  align-items: normal;
}
.foot-contact .item {
  margin-bottom: 2.5rem;
}
.foot-contact .item:last-child {
  margin-bottom: 0;
}
.foot-erm {
  width: 10rem;
  margin-bottom: 2rem;
}
.foot-rbox {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}
.view-infoLR {
  margin-left: -1.5rem;
  display: flex;
  padding-bottom: 6.5rem;
}
.view-infoL {
  width: 33.33%;
  padding-left: 1.5rem;
  margin-top: 2.8rem;
}
.view-infoR {
  width: 66.66%;
  padding-left: 1.5rem;
  margin-top: 2.8rem;
}
.view-ititle {
  font-size: 2.887rem;
  margin-top: -0.8rem;
  position: sticky;
  top: 8.4rem;
}
.view-infoR-desc {
  display: flex;
  margin-left: -1.5rem;
  margin-top: -0.5rem;
}
.view-infoR-desc > dd {
  width: 50%;
  padding-left: 1.5rem;
}
.view-infoR-desc .view-infoR-item {
  width: 84%;
}
.view-infoR-desc .pageC {
  text-align: justify;
}
.work-infoR-type li {
  display: flex;
}
.work-infoR-type .t {
  width: 10.2rem;
  margin-right: 1rem;
}
.work-infoR-type .c {
  flex: 1;
}
.viewPage {
  margin-top: 5.1rem;
}
.imgw-w {
  margin-top: 1.5rem;
}
.imgwitem-w {
  margin-top: 1.5rem;
  display: flex;
  margin-left: -1.5rem;
}
.imgwitem-w .item {
  width: 50%;
  padding-left: 1.5rem;
}
.pager {
  display: flex;
  justify-content: space-between;
  padding-top: 1.3rem;
}
.pager a {
  color: #000000;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.pager a:hover {
  color: #9e9e9e;
  text-decoration: underline;
}
.news-list {
  overflow: hidden;
}
.news-list li {
  position: relative;
  padding: 1.5rem 0;
  display: flex;
  margin-left: -1.5rem;
  overflow: hidden;
  border-bottom: 1px solid #000000;
  align-items: flex-start;
}
.news-list li:hover .imgwh {
  transform: scale(1.1);
}
.news-list .item {
  width: 33.33%;
  margin-left: 1.5rem;
}
.news-list .time {
  font-size: 3.9678rem;
  line-height: 1;
  margin-top: 1.2rem;
}
.news-list .img-item {
  display: flex;
}
.news-list .imgBox {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 23rem;
}
.news-list .imgwh {
  transition: 0.5s;
  -webkit-transition: 0.5s;
}
.news-list .desc {
  display: flex;
}
.news-list .txt1 {
  margin-top: 1.2rem;
}
.new-infoT-txt2 {
  margin-top: 7rem;
}
.pading-box {
  height: 26rem;
}
.pading {
  display: flex;
  justify-content: center;
  font-size: 2.7rem;
  letter-spacing: 0;
  padding: calc(2 * 1.5rem) 0;
  line-height: 1;
}
.pading a {
  color: #000000;
}
.pading a:hover {
  color: #9e9e9e;
}
.pading a.active {
  color: #9e9e9e;
}
.pading .fg {
  padding: 0 1.764rem;
}
.pading .fg:last-child {
  display: none;
}
.info-swiper {
  position: relative;
  overflow: hidden;
}
.info-swiper::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background-color: #000000;
  z-index: 500;
}
.ml1 {
  margin-left: -0.9rem;
}
.info-gx {
  margin-bottom: 10rem;
}
.info-br {
  border-top: 1px solid #000000;
}
.info-desc-ul {
  display: flex;
  margin-left: -1.5rem;
}
.info-desc-ul > .info-item {
  width: 33.33%;
  margin-left: 1.5rem;
  padding-top: 2.2rem;
}
.info-desc-t {
  font-size: 3.4726rem;
  line-height: 1.57113;
  margin-bottom: -1rem;
}
.info-desc-c {
  line-height: 1.8;
  margin-bottom: -1rem;
}
.leader-page {
  background-color: #000000;
  padding: 8.7rem 0;
  color: #ffffff;
}
.leader-page a {
  color: #ffffff;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.leader-page a:hover {
  text-decoration: underline;
}
.leader-box {
  display: flex;
  margin-right: calc(-0.5*1.5rem);
  margin-left: calc(-0.5*1.5rem);
}
.leader-item1 {
  flex: 0 0 auto;
  width: 33.33333333%;
  padding-right: calc(1.5rem * 0.5);
  padding-left: calc(1.5rem * 0.5);
}
.leader-item2 {
  flex: 0 0 auto;
  width: 66.66666667%;
  padding-right: calc(1.5rem * 0.5);
  padding-left: calc(1.5rem * 0.5);
}
.leader-list {
  display: flex;
  margin-left: -1.5rem;
}
.leader-list > li {
  width: 50%;
  padding-left: 1.5rem;
}
.leader-list .imgBox {
  width: 43%;
  padding-bottom: 43%;
  margin-bottom: 8.7rem;
}
.leader-list .imgBox img {
  position: absolute;
  left: 0;
  top: 0;
}
.leader-list .desc {
  margin-top: -0.8rem;
}
.team-ul {
  display: flex;
  flex-wrap: wrap;
}
.team-ul > li {
  width: 12.5%;
}
.team-ul .imgBox {
  padding-bottom: 100%;
}
.team-ul .imgBox img {
  position: absolute;
  left: 0;
  top: 0;
}
.awards-page {
  background-color: #000000;
  color: #ffffff;
  padding-bottom: 10rem;
}
.awards-page a {
  color: #ffffff;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.awards-page a:hover {
  text-decoration: underline;
}
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(-0.5*1.5rem);
  margin-left: calc(-0.5*1.5rem);
}
.col-item {
  flex: 0 0 auto;
  padding-right: calc(1.5rem * 0.5);
  padding-left: calc(1.5rem * 0.5);
}
.col-item1 {
  width: 33.33333333%;
}
.col-item2 {
  width: 66.66666667%;
}
.awards-title {
  font-size: 3.4726rem;
  line-height: 1.57113;
}
.awards-tbox {
  border-bottom: 1px solid #ffffff;
  padding: 2.2rem 0;
}
.awards-ul {
  overflow: hidden;
}
.awards-ul > li {
  display: flex;
  margin-left: -1.5rem;
  line-height: 1.60009;
  border-top: 1px solid #ffffff;
  padding: 1rem 0;
}
.awards-ul > li:first-child {
  border-top: 0 none;
}
.awards-li {
  width: 50%;
  margin-left: 1.5rem;
}
.awards-item {
  display: flex;
  justify-content: space-between;
}
.awards-item .year {
  flex-shrink: 0;
  margin-right: 1.5rem;
}
.awards-item .jx {
  text-align: justify;
  margin-right: 1.5rem;
}
.contact-item {
  display: flex;
  border-bottom: 1px solid #000000;
  margin-top: 2rem;
  line-height: 1.8;
}
.contact-item:first-child {
  margin-top: 0;
}
.contact-item .t {
  width: 28%;
}
.contact-item .rtxt {
  flex: 1;
}
.contact-item.error {
  border-bottom-color: red;
}
.input-txt {
  border: 0 none;
  line-height: 1.8;
  font-size: 2.4rem;
  width: 100%;
}
.contact-form {
  margin-bottom: -1rem;
}
.contact-btn {
  padding: 5.8rem 0;
}
.contact-btn .item {
  display: flex;
}
.btn-mess {
  display: flex;
  align-items: center;
  color: #000000;
}
.btn-mess .txt {
  border: 1px solid #000000;
  border-radius: 2.2rem;
  display: flex;
  align-items: center;
  padding: 0 2.2rem;
  margin-right: 1.2rem;
  min-height: 4.4rem;
  line-height: 1;
}
.btn-mess .ico {
  height: 4.4rem;
  flex-shrink: 0;
}
.btn-messzh {
  font-size: 1.98rem;
}
.btn-ok {
  height: 4.4rem;
  display: block;
}
.btn-ok img {
  width: auto;
  height: 100%;
}
.logo-row {
  position: relative;
  overflow: hidden;
}
.logo-slider {
  width: 200%;
  background-size: 50%;
  will-change: transform;
  background-repeat: repeat-x;
  background-position: 0 50%;
}
.logo-slider i {
  display: block;
  position: relative;
  padding-bottom: 4.4%;
}
