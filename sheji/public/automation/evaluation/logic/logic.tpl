<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\[logic]\logic;
/**
 * [title]逻辑
 */
class [upper_name] extends LogicBase
{

      /**
       * 获取[title]搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获[title]单条信息
      */
     public function get[upper_name]Info($where = [], $field = '*')
     {

        return $this->model[upper_name]->getInfo($where, $field);
     }

    /**
     * 获取[title]列表
     */

    public function get[upper_name]List($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->model[upper_name]->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取[title]无分页列表
     */
    public function get[upper_name]Column($where = [], $field = '', $key = '')
    {
        return $this->model[upper_name]->getColumn($where, $field , $key);
    }

    /**
     * [title]单条编辑
     */
    public function [lower_name]Edit($data = [])
    {
		
        $result = $this->model[upper_name]->setInfo($data);
        
        return $result ? $result : $this->model[upper_name]->getError();
    }

    /**
     * [title]删除
     */
    public function [lower_name]Del($where = [], $is_true = false)
    {

        $result = $this->model[upper_name]->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->model[upper_name]->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取[title]搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * [title]单条编辑
     */
    public function [lower_name]AdminEdit($data = [])
    {


        $url = url('[lower_name]List');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->model[upper_name]->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '[title]' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '[title]操作成功', $url] : [RESULT_ERROR, $this->model[upper_name]->getError()];
    }

    /**
     * [title]删除
     */
    public function [lower_name]AdminDel($where = [])
    {

        $result = $this->model[upper_name]->deleteInfo($where);
        
        $result && action_log('删除', '[title]删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '[title]删除成功'] : [RESULT_ERROR, $this->model[upper_name]->getError()];
    }
}
