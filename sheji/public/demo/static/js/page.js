var isMobile = false,
	mobile = false,
	win_width = 0,
	win_height = 0,
	atH = 0,
	pnavH=0,
	navItem=0,
	$menuBtn = $('.siteNav');
var pageInit = {
	init: function () {
		pageInit.getCommon();
		pageInit.inview();
		pageInit.scollHeader();
	},
	inview:function(){
		$('.target2').on('inview', function() { 
			$(this).addClass('action');
		});
		$('.target').on('inview', function (event, visible) {
			if (visible == true) {
				$(this).addClass('action');
			} else {
				$(this).removeClass('action');
			}
		});
	},
	getCommon: function () {
		function wh() {
			atH=$(".top").height();
			win_width = ('innerWidth' in window) ? window.innerWidth : document.documentElement.clientWidth;
			win_height = ('innerHeight' in window) ? window.innerHeight : document.documentElement.clientHeight;
			if (win_width <= 1180) {
				isMobile = true;
			} else if (win_width > 1180) {
				isMobile = false;
			};
			if (win_width <= 640) {
				mobile = true;
			} else if (win_width > 640) {
				mobile = false;
			};
		};
		wh();
		$(window).on("resize",function () {
			wh();
		});
	},
	setScroll: function (anchorCur,speed,antype) {
		if ($(anchorCur).length >= 1) {
			$("html,body").animate({
				scrollTop: $(anchorCur).offset().top -(atH)
			}, speed,antype);
		}
	},
	scollHeader: function () {
		function c() {
			var s = $(window).scrollTop();
			if(s > 290){
				$(".top-fixed").addClass("showon");
			}else{
				$(".top-fixed").removeClass("showon");
			}
		}	
		$(window).on("scroll",function(){
			c();
		});
		c();	
	},
	tabswiper: function (navigation,mySwiper) {
		var slides = mySwiper.slides,
			navigationArr = [],
			navigationArrfilter = [],
			itemActive,
			navigationHtml="";
		for(let i = 0;i<slides.length;i++){
			navigationArr.push(slides[i].getAttribute('data-name'));
		};
		for(let i = 0;i<navigationArr.length;i++){
			if(navigationArrfilter.indexOf(navigationArr[i]) === -1){
				navigationArrfilter.push(navigationArr[i]);
				navigationHtml+= "<li><span class='txt'>" + navigationArr[i] + "</span></li>";
				navigation.html(navigationHtml);
			};
		};

		var item = navigation.find('li');
		itemActive = item.eq(0);

		item.eq(0).addClass("active");
		item.on('click', function () {
			item.removeClass("active");
			$(mySwiper.el).removeClass("active");
			$(this).addClass("active");
			itemActive = $(this);
			updateSliders();
		});

		var updateSliders = function(){
			var arr = Array.prototype.slice.call(slides).filter(function (slides) {
				return slides.getAttribute('data-name') == itemActive.find(".txt").html();
			});
			mySwiper.removeAllSlides();
			for (var i = 0; i < arr.length; i++) {
				mySwiper.appendSlide(arr[i]);
			};
			setTimeout(function(){
				$(mySwiper.el).addClass("active");
			},0);
		};
		updateSliders();
	},
	videoShow: function () {
		$(".video-sbtn").on("click",function() {
			var videoName = $(this).data('url');
			$(".videon").attr("src", videoName);
			$(".pvideo-bg").addClass("overlay");
		});
		$(".pv-close").on("click",function() {
			$(".pvideo-bg").removeClass("overlay");
			$(".videon").attr("src", "");
		});
		$(".vwrap .close,.vwrap .videobtg").on("click",function() {
			$(".vwrap").hide();
			$('#videobox').html("");
		});
	}
};
pageInit.init();