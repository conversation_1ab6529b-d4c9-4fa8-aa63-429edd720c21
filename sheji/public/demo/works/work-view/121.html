<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>
		中国二砂 · ERSHA ARTDIST
	</title>
	<meta name="keywords" content="河南,星河地产,二砂厂,标识设计,序东堂">
	<meta name="description" content="序东堂受星河地产委托参与到二砂厂城市更新项目之中，2021年项目得以呈现。我们通过旧物改造的方式，触发人与物之间的感情交流。">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="cache-control" content="max-age=0">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT">
<meta http-equiv="pragma" content="no-cache">
<link href="../static/image/favicon_64.ico" rel="shortcut icon" type="image/x-icon">
<link rel="stylesheet" type="text/css" href="../../static/css/common.css">
<link rel="stylesheet" type="text/css" href="../../static/css/style.css">
<link rel="stylesheet" type="text/css" href="../../static/css/responsive.css">
<script src="../../static/js/jquery.min.js"></script>
<script src="../../static/js/setrem.js"></script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?b24ee3c38abc23c7ebfeac6a860b2b22";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
</head>

<body>
	<header class="top">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			

		</ul>
	</nav>
</header>
<section class="top-fixed">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			
		</ul>
	</nav>
</section>
	<section class="top-logo section-gx">
		<ul class="tlogo-nav-ul">
			<li class="item1">
				<h1 class="logo">
					<a title="序东堂设计官网" class="cursorA" href="../../index.html">
						<img src="../../static/image/logo.png" class="vm" alt="序东堂设计官网">
						</a>
				</h1>
			</li>
		</ul>
	</section>
	<section class="section-gx view-info">
		<div class="view-infoLR">
			<article class="view-infoL">
				<div class="view-ititle lc-h1 target tY50">
					星河 · 二砂文创园<br>标识设计 & 环境图形<br>·<br>ERSHA ARTDIST<br>Sign Design
				</div>
			</article>
			<article class="view-infoR">
				<dl class="view-infoR-desc">
					<dd class="target tY50 dly1">
						<div class="view-infoR-item pageC lc-h2 pf-re">
							中国第二砂轮厂位于中国郑州，是中华人民共和国建国初最大的砂轮厂。厂区内遗留有东亚最大的包豪斯风格建筑群和大量废弃的工业器械。2020年6月由星河地产委托序东堂进行环境图形及标识设计，历时4个月完成并呈现。<br><br>我们选取工厂遗存下的旧机械零件，重新组装成标识装置，并选用具有鲜明年代感的字体与之配套组合，还原了时代记忆。通过旧物改造的方式，触发人与物之间的感情交流。
						</div>
					</dd>
					<dd class="target tY50 dly2">
						<ul class="lc-h2 work-infoR-type" id="CaseTag">

						</ul>
					</dd>
				</dl>
				<section class="pageC lc-h2 viewPage target tY50" id="worksimgs">

					
					 
				</section>
				<article class="pager">
					<p>
						<a title="麓镇 · LUXETOWN" href="123.html">Prev</a> /
						<a title="玉林 · YULIN" href="120.html">Next</a>
					</p>
					<p>
						<a class="blaclk" href="../UrbanRenewal.html">Back to List</a>
					</p>
				</article>
			</article>
		</div>
	</section>
	<footer class="foot section-gx">
	<ul class="foot-nav">
		<li>
			<article class="foot-l">
				© All rights Reserved by xdtart
			</article>
		</li>
		<li>
			<article class="foot-contact">
				<div class="item">
					<p>
					   Mail：<br><EMAIL><br><EMAIL>
					</p>				 
			  </div>
				<div class="item">
				 <p>
					   Tel:<br>18607110100
					</p>
				</div>
				<div class="item">
					 <p>
					   Address：<br>武汉市东西湖区团结激光工业园7栋C座5楼
					</p>
				</div>
			</article>
		</li>
		<li>
			<article class="foot-rbox">
				<p class="foot-erm">
					<img src="../../static/picture/b7ebf8e41850af4e.svg" class="imgw">
                    </p>
					<p><a href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2024076981号-1</a></p>
			</article>
		</li>
	</ul>
</footer>
<script src="../../static/js/plugin.js"></script>
<script src="../../static/js/page.js"></script>

	<script>
		var CaseTag="Address | 河南省郑州市中原区华山路78号".replaceAll("<br />","^").split("^");
		var Awards="2022  |  日本标识设计奖 · 入选".replaceAll("<br />","^").split("^");
		var CaseTagstr="";
		if(CaseTag.length==0)
		 {
			 if(CaseTag.split("|").length>1)
		     {
				  CaseTagstr+="<li>";
                  CaseTagstr+="<p class=\"t\">"+CaseTag.split("|")[0].trim() +"</p><div class=\"c pf-re\">"+CaseTag.split("}")[1].trim() +"</div>";
				  CaseTagstr+="</li>";
			  }
		  }
		 else
		 {                                
		 for (var arr=0;arr<CaseTag.length;arr++) {
		   CaseTagstr+="<li>";
		   if(CaseTag[arr].split("|").length>1)
			{
                CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].split("|")[0].trim()+"</p>"+"<div class=\"c pf-re\">"+CaseTag[arr].split("|")[1].trim()+"</div>";
			}
		   else
			{
				CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].trim()+"</p>"+"<div class=\"c pf-re\"></div>";
			}
			 CaseTagstr+="</li>";
            }
          }
          if(Awards!="")
		  {
            CaseTagstr+="<li>";
            if(Awards.length==0)
		    {
			 if(Awards.split("|").length>1)
		     {
				CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c pf-re\"><dl class=\"awards-list pf-re\"><dt>"+Awards.split("|")[0].trim()+"</dt><dd>"+Awards.split("|")[1].trim()+"</dd></dl></div>";	  
			 }
		   }
		   else{
			
			CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c\">";
            for (var arr=0;arr<Awards.length;arr++) {
            CaseTagstr+="<dl class=\"awards-list pf-re\">";
			
		    if(Awards[arr].split("|").length>1)
			{				 
               CaseTagstr+="<dt>"+Awards[arr].split("|")[0].trim()+"</dt><dd>"+Awards[arr].split("|")[1].trim()+"</dd>";
			}
		    else
			{
				CaseTagstr+="<dt>"+Awards[arr].trim()+"</dt><dd></dd>";
			}
			CaseTagstr+="</dl>";
		    }	
		    }
            CaseTagstr+="</li>";
		  }
		  document.getElementById("CaseTag").innerHTML=CaseTagstr;

		 const htmlString='<img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban06/01.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban06/02.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/03.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/04.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/05.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/06.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban06/07.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban06/08.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/09.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/10.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/11.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/12.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban06/13.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/14.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/15.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/16.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban06/17.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban06/18.jpg" />';
         const parser = new DOMParser();
         const doc = parser.parseFromString(htmlString, 'text/html');
         const imgTags = doc.getElementsByTagName('img');
         let imgAttributes="";
        for (let i = 0; i < imgTags.length; i++) {
        if(imgTags[i].getAttribute("tag")=="小图")
         {
         imgAttributes+="<div class=\"imgwitem-w\">";
        
        imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国二砂 · ERSHA ARTDIST\"/></p></div>";
        i++;

		if(i < imgTags.length)
         { 
			 if(imgTags[i].getAttribute("tag")=="小图")
			   imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国二砂 · ERSHA ARTDIST\" /></p></div></div>";
			 else 
			   imgAttributes+="</div><p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国二砂 · ERSHA ARTDIST\" /></p>";
		 }	 
        }
       else
       {
          imgAttributes+="<p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国二砂 · ERSHA ARTDIST\" /></p>";
       }
     }
	document.getElementById("worksimgs").innerHTML=imgAttributes;
	</script>
</body>

</html>