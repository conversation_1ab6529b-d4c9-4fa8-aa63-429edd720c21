<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>
		中国皮影博物馆 · SHADOW PLAY
	</title>
	<meta name="keywords" content="成都,中国皮影博物馆,视觉设计,序东堂">
	<meta name="description" content="序东堂参与到中国皮影博物馆的视觉升级项目之中，2021年项目得以呈现。我们以“影”作为中心创作素材，借由书法笔画风格进行抽象化描绘，并在笔画组合上融入皮影关节特点。">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="cache-control" content="max-age=0">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT">
<meta http-equiv="pragma" content="no-cache">
<link href="../static/image/favicon_64.ico" rel="shortcut icon" type="image/x-icon">
<link rel="stylesheet" type="text/css" href="../../static/css/common.css">
<link rel="stylesheet" type="text/css" href="../../static/css/style.css">
<link rel="stylesheet" type="text/css" href="../../static/css/responsive.css">
<script src="../../static/js/jquery.min.js"></script>
<script src="../../static/js/setrem.js"></script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?b24ee3c38abc23c7ebfeac6a860b2b22";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
</head>

<body>
	<header class="top">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			

		</ul>
	</nav>
</header>
<section class="top-fixed">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			
		</ul>
	</nav>
</section>
	<section class="top-logo section-gx">
		<ul class="tlogo-nav-ul">
			<li class="item1">
				<h1 class="logo">
					<a title="序东堂设计官网" class="cursorA" href="../../index.html">
						<img src="../../static/image/logo.png" class="vm" alt="序东堂设计官网">
						</a>
				</h1>
			</li>
		</ul>
	</section>
	<section class="section-gx view-info">
		<div class="view-infoLR">
			<article class="view-infoL">
				<div class="view-ititle lc-h1 target tY50">
					中国皮影博物馆<br>品牌设计<br>·<br>CHINA SHADOW PLAY MUSEUM<br>Museums
				</div>
			</article>
			<article class="view-infoR">
				<dl class="view-infoR-desc">
					<dd class="target tY50 dly1">
						<div class="view-infoR-item pageC lc-h2 pf-re">
							成都·中国皮影博物馆位于中国成都，是全国唯一一家冠名为“中国”的皮影专题博物馆。2021年5月18日，成都中国皮影博物馆的影舞万象·偶戏大千——中国皮影木偶展入选十大精品展。2021年3月，中国皮影博物馆委托序东堂进行VI设计，2021年7月完成。<br><br>我们以“影”作为中心创作素材，借由书法笔画风格进行抽象化描绘，并在笔画组合上融入皮影关节重叠连接的特点。色彩方面提取皮影中常用且区别于同类别设计的绿色和黑色。
						</div>
					</dd>
					<dd class="target tY50 dly2">
						<ul class="lc-h2 work-infoR-type" id="CaseTag">

						</ul>
					</dd>
				</dl>
				<section class="pageC lc-h2 viewPage target tY50" id="worksimgs">

					
					 
				</section>
				<article class="pager">
					<p>
						<a href="../Museums.html">Prev</a> /
						<a title="A4美术馆 · A4 ART MUSEUME" href="145.html">Next</a>
					</p>
					<p>
						<a class="blaclk" href="../Museums.html">Back to List</a>
					</p>
				</article>
			</article>
		</div>
	</section>
	<footer class="foot section-gx">
	<ul class="foot-nav">
		<li>
			<article class="foot-l">
				© All rights Reserved by xdtart
			</article>
		</li>
		<li>
			<article class="foot-contact">
				<div class="item">
					<p>
					   Mail：<br><EMAIL><br><EMAIL>
					</p>				 
			  </div>
				<div class="item">
				 <p>
					   Tel:<br>18607110100
					</p>
				</div>
				<div class="item">
					 <p>
					   Address：<br>武汉市东西湖区团结激光工业园7栋C座5楼
					</p>
				</div>
			</article>
		</li>
		<li>
			<article class="foot-rbox">
				<p class="foot-erm">
					<img src="../../static/picture/b7ebf8e41850af4e.svg" class="imgw">
                    </p>
					<p><a href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2024076981号-1</a></p>
			</article>
		</li>
	</ul>
</footer>
<script src="../../static/js/plugin.js"></script>
<script src="../../static/js/page.js"></script>

	<script>
		var CaseTag="Address |  中国成都市青羊区小河街1号".replaceAll("<br />","^").split("^");
		var Awards="2022 |  纽约TDC · 年奖<br />2022 |  东京TDC  ·入选<br />2021 |  日本字体设计协会年鉴 · 入选".replaceAll("<br />","^").split("^");
		var CaseTagstr="";
		if(CaseTag.length==0)
		 {
			 if(CaseTag.split("|").length>1)
		     {
				  CaseTagstr+="<li>";
                  CaseTagstr+="<p class=\"t\">"+CaseTag.split("|")[0].trim() +"</p><div class=\"c pf-re\">"+CaseTag.split("}")[1].trim() +"</div>";
				  CaseTagstr+="</li>";
			  }
		  }
		 else
		 {                                
		 for (var arr=0;arr<CaseTag.length;arr++) {
		   CaseTagstr+="<li>";
		   if(CaseTag[arr].split("|").length>1)
			{
                CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].split("|")[0].trim()+"</p>"+"<div class=\"c pf-re\">"+CaseTag[arr].split("|")[1].trim()+"</div>";
			}
		   else
			{
				CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].trim()+"</p>"+"<div class=\"c pf-re\"></div>";
			}
			 CaseTagstr+="</li>";
            }
          }
          if(Awards!="")
		  {
            CaseTagstr+="<li>";
            if(Awards.length==0)
		    {
			 if(Awards.split("|").length>1)
		     {
				CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c pf-re\"><dl class=\"awards-list pf-re\"><dt>"+Awards.split("|")[0].trim()+"</dt><dd>"+Awards.split("|")[1].trim()+"</dd></dl></div>";	  
			 }
		   }
		   else{
			
			CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c\">";
            for (var arr=0;arr<Awards.length;arr++) {
            CaseTagstr+="<dl class=\"awards-list pf-re\">";
			
		    if(Awards[arr].split("|").length>1)
			{				 
               CaseTagstr+="<dt>"+Awards[arr].split("|")[0].trim()+"</dt><dd>"+Awards[arr].split("|")[1].trim()+"</dd>";
			}
		    else
			{
				CaseTagstr+="<dt>"+Awards[arr].trim()+"</dt><dd></dd>";
			}
			CaseTagstr+="</dl>";
		    }	
		    }
            CaseTagstr+="</li>";
		  }
		  document.getElementById("CaseTag").innerHTML=CaseTagstr;

		 const htmlString='<img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/01.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/02.jpg" /><img type="LargeUrl" tag="" src="/images/works/Museums/Museums01/03.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/04.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/05.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/06.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/07.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/08.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/09.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/10.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/11.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/12.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/13.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/14.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/15.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/16.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/17.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/18.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Museums/Museums01/19.jpg" />';
         const parser = new DOMParser();
         const doc = parser.parseFromString(htmlString, 'text/html');
         const imgTags = doc.getElementsByTagName('img');
         let imgAttributes="";
        for (let i = 0; i < imgTags.length; i++) {
        if(imgTags[i].getAttribute("tag")=="小图")
         {
         imgAttributes+="<div class=\"imgwitem-w\">";
        
        imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国皮影博物馆 · SHADOW PLAY\"/></p></div>";
        i++;

		if(i < imgTags.length)
         { 
			 if(imgTags[i].getAttribute("tag")=="小图")
			   imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国皮影博物馆 · SHADOW PLAY\" /></p></div></div>";
			 else 
			   imgAttributes+="</div><p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国皮影博物馆 · SHADOW PLAY\" /></p>";
		 }	 
        }
       else
       {
          imgAttributes+="<p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"中国皮影博物馆 · SHADOW PLAY\" /></p>";
       }
     }
	document.getElementById("worksimgs").innerHTML=imgAttributes;
	</script>
</body>

</html>