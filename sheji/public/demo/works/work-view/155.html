<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>
		大集古镇 · DAJI ANCIENT TOWN
	</title>
	<meta name="keywords" content="河南,大集古镇,标识系统,序东堂">
	<meta name="description" content="序东堂受河南建业委托参与到大集古镇的标识设计项目之中，2019年项目得以呈现。我们融合了当地两大技班的联姻佳话，将东北庄杂技的力量技巧、生活文化融入标识系统形制结构中。">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="cache-control" content="max-age=0">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT">
<meta http-equiv="pragma" content="no-cache">
<link href="../static/image/favicon_64.ico" rel="shortcut icon" type="image/x-icon">
<link rel="stylesheet" type="text/css" href="../../static/css/common.css">
<link rel="stylesheet" type="text/css" href="../../static/css/style.css">
<link rel="stylesheet" type="text/css" href="../../static/css/responsive.css">
<script src="../../static/js/jquery.min.js"></script>
<script src="../../static/js/setrem.js"></script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?b24ee3c38abc23c7ebfeac6a860b2b22";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
</head>

<body>
	<header class="top">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			

		</ul>
	</nav>
</header>
<section class="top-fixed">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			
		</ul>
	</nav>
</section>
	<section class="top-logo section-gx">
		<ul class="tlogo-nav-ul">
			<li class="item1">
				<h1 class="logo">
					<a title="序东堂设计官网" class="cursorA" href="../../index.html">
						<img src="../../static/image/logo.png" class="vm" alt="序东堂设计官网">
						</a>
				</h1>
			</li>
		</ul>
	</section>
	<section class="section-gx view-info">
		<div class="view-infoLR">
			<article class="view-infoL">
				<div class="view-ititle lc-h1 target tY50">
					河南建业 · 大集古镇<br>标识设计<br>·<br>DAJI ANCIENT TOWN<br>Sign Design
				</div>
			</article>
			<article class="view-infoR">
				<dl class="view-infoR-desc">
					<dd class="target tY50 dly1">
						<div class="view-infoR-item pageC lc-h2 pf-re">
							2017年，河南建业集团拿出从全国各地收藏的139座具有文物保护价值的明清古建，历时46天原拆原建，创造建筑史上的“建业速度”，在位于素有中国杂技之乡美名的河南濮阳东北庄，落成全国首个国家级杂技主题小镇“大集古镇”。 2019年，序东堂承接了大集古镇核心区公共标识系统设计。从项目考察到落地验收，历时35天，刷新了公司标识设计项目的最快呈现速度。<br><br>设计上我们融合了当地乔张两大技班的联姻佳话，通过一对金童玉女将东北庄杂技的力量和技巧、生活文化融入到整个标识系统的形制结构当中，传递出“大集”古朴的艺术美感。
						</div>
					</dd>
					<dd class="target tY50 dly2">
						<ul class="lc-h2 work-infoR-type" id="CaseTag">

						</ul>
					</dd>
				</dl>
				<section class="pageC lc-h2 viewPage target tY50" id="worksimgs">

					
					 
				</section>
				<article class="pager">
					<p>
						<a title="中国院子 · CHINA COURTYARD" href="156.html">Prev</a> /
						<a title="泸州港 · LUZHOU PORT" href="150.html">Next</a>
					</p>
					<p>
						<a class="blaclk" href="../Culture&Tourism.html">Back to List</a>
					</p>
				</article>
			</article>
		</div>
	</section>
	<footer class="foot section-gx">
	<ul class="foot-nav">
		<li>
			<article class="foot-l">
				© All rights Reserved by xdtart
			</article>
		</li>
		<li>
			<article class="foot-contact">
				<div class="item">
					<p>
					   Mail：<br><EMAIL><br><EMAIL>
					</p>				 
			  </div>
				<div class="item">
				 <p>
					   Tel:<br>18607110100
					</p>
				</div>
				<div class="item">
					 <p>
					   Address：<br>武汉市东西湖区团结激光工业园7栋C座5楼
					</p>
				</div>
			</article>
		</li>
		<li>
			<article class="foot-rbox">
				<p class="foot-erm">
					<img src="../../static/picture/b7ebf8e41850af4e.svg" class="imgw">
                    </p>
					<p><a href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2024076981号-1</a></p>
			</article>
		</li>
	</ul>
</footer>
<script src="../../static/js/plugin.js"></script>
<script src="../../static/js/page.js"></script>

	<script>
		var CaseTag="Address | 中国濮阳市华龙区东北庄杂技文化园".replaceAll("<br />","^").split("^");
		var Awards="2019 | 日本标识设计奖 · 入选".replaceAll("<br />","^").split("^");
		var CaseTagstr="";
		if(CaseTag.length==0)
		 {
			 if(CaseTag.split("|").length>1)
		     {
				  CaseTagstr+="<li>";
                  CaseTagstr+="<p class=\"t\">"+CaseTag.split("|")[0].trim() +"</p><div class=\"c pf-re\">"+CaseTag.split("}")[1].trim() +"</div>";
				  CaseTagstr+="</li>";
			  }
		  }
		 else
		 {                                
		 for (var arr=0;arr<CaseTag.length;arr++) {
		   CaseTagstr+="<li>";
		   if(CaseTag[arr].split("|").length>1)
			{
                CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].split("|")[0].trim()+"</p>"+"<div class=\"c pf-re\">"+CaseTag[arr].split("|")[1].trim()+"</div>";
			}
		   else
			{
				CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].trim()+"</p>"+"<div class=\"c pf-re\"></div>";
			}
			 CaseTagstr+="</li>";
            }
          }
          if(Awards!="")
		  {
            CaseTagstr+="<li>";
            if(Awards.length==0)
		    {
			 if(Awards.split("|").length>1)
		     {
				CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c pf-re\"><dl class=\"awards-list pf-re\"><dt>"+Awards.split("|")[0].trim()+"</dt><dd>"+Awards.split("|")[1].trim()+"</dd></dl></div>";	  
			 }
		   }
		   else{
			
			CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c\">";
            for (var arr=0;arr<Awards.length;arr++) {
            CaseTagstr+="<dl class=\"awards-list pf-re\">";
			
		    if(Awards[arr].split("|").length>1)
			{				 
               CaseTagstr+="<dt>"+Awards[arr].split("|")[0].trim()+"</dt><dd>"+Awards[arr].split("|")[1].trim()+"</dd>";
			}
		    else
			{
				CaseTagstr+="<dt>"+Awards[arr].trim()+"</dt><dd></dd>";
			}
			CaseTagstr+="</dl>";
		    }	
		    }
            CaseTagstr+="</li>";
		  }
		  document.getElementById("CaseTag").innerHTML=CaseTagstr;

		 const htmlString='<img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/01.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/02.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/03.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/04.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/05.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/06.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/07.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/08.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/09.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/10.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/11.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/12.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/13.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/14.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Cultural&Tourism/Tourism02/15.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/16.jpg" /><img type="LargeUrl" tag="" src="/images/works/Cultural&Tourism/Tourism02/17.jpg" />';
         const parser = new DOMParser();
         const doc = parser.parseFromString(htmlString, 'text/html');
         const imgTags = doc.getElementsByTagName('img');
         let imgAttributes="";
        for (let i = 0; i < imgTags.length; i++) {
        if(imgTags[i].getAttribute("tag")=="小图")
         {
         imgAttributes+="<div class=\"imgwitem-w\">";
        
        imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"大集古镇 · DAJI ANCIENT TOWN\"/></p></div>";
        i++;

		if(i < imgTags.length)
         { 
			 if(imgTags[i].getAttribute("tag")=="小图")
			   imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"大集古镇 · DAJI ANCIENT TOWN\" /></p></div></div>";
			 else 
			   imgAttributes+="</div><p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"大集古镇 · DAJI ANCIENT TOWN\" /></p>";
		 }	 
        }
       else
       {
          imgAttributes+="<p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"大集古镇 · DAJI ANCIENT TOWN\" /></p>";
       }
     }
	document.getElementById("worksimgs").innerHTML=imgAttributes;
	</script>
</body>

</html>