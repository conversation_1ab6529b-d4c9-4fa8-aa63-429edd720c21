<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>
		Hi Park运动中心 · HI PARK
	</title>
	<meta name="keywords" content="成都,Hi Park,标识设计,运动公园,序东堂">
	<meta name="description" content="序东堂受成都天府新区公园城市投资发展有限公司委托参与到Hi Park标识系统设计项目之中，2021年完成并交付。我们将每一条运动轨迹，都看做对生命能量可视化、有序化的重新塑造。">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="cache-control" content="max-age=0">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT">
<meta http-equiv="pragma" content="no-cache">
<link href="../static/image/favicon_64.ico" rel="shortcut icon" type="image/x-icon">
<link rel="stylesheet" type="text/css" href="../../static/css/common.css">
<link rel="stylesheet" type="text/css" href="../../static/css/style.css">
<link rel="stylesheet" type="text/css" href="../../static/css/responsive.css">
<script src="../../static/js/jquery.min.js"></script>
<script src="../../static/js/setrem.js"></script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?b24ee3c38abc23c7ebfeac6a860b2b22";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
</head>

<body>
	<header class="top">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			

		</ul>
	</nav>
</header>
<section class="top-fixed">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			
		</ul>
	</nav>
</section>
	<section class="top-logo section-gx">
		<ul class="tlogo-nav-ul">
			<li class="item1">
				<h1 class="logo">
					<a title="序东堂设计官网" class="cursorA" href="../../index.html">
						<img src="../../static/image/logo.png" class="vm" alt="序东堂设计官网">
						</a>
				</h1>
			</li>
		</ul>
	</section>
	<section class="section-gx view-info">
		<div class="view-infoLR">
			<article class="view-infoL">
				<div class="view-ititle lc-h1 target tY50">
					天府公园 · Hi Park运动中心<br> 品牌设计 & 标识设计<br>·<br>HI PARK<br>Parks
				</div>
			</article>
			<article class="view-infoR">
				<dl class="view-infoR-desc">
					<dd class="target tY50 dly1">
						<div class="view-infoR-item pageC lc-h2 pf-re">
							成都天府公园 Hi Park 运动中心位于天府新区。以多功能复合式体验综合场馆与生态公园有机结合。Hi Park内除了⽻⽑球馆、攀岩综合馆、室内篮球馆、室外⾜球场等⼤众体育项⽬的规划，还有专业度较⾼的轮滑冰球馆等项⽬。2021年7月，序东堂受成都天府新区公园城市投资发展有限公司委托，进行VI和标识系统设计，2021年10月完成并交付。<br><br>每一条运动轨迹，都是对生命能量可视化、有序化的重新塑造。我们将这一感受，转化为具象的运动色彩和线条符号，是跑道与力量的糅合，是绿地与新生的交融，在对抗熵增的宿命中逆流而上，释放出有益的能量。
						</div>
					</dd>
					<dd class="target tY50 dly2">
						<ul class="lc-h2 work-infoR-type" id="CaseTag">

						</ul>
					</dd>
				</dl>
				<section class="pageC lc-h2 viewPage target tY50" id="worksimgs">

					
					 
				</section>
				<article class="pager">
					<p>
						<a title="穿水公园 · WATER CANYON PARK" href="129.html">Prev</a> /
						<a title="萌宠乐园 · PET PALS PARK" href="128.html">Next</a>
					</p>
					<p>
						<a class="blaclk" href="../Parks.html">Back to List</a>
					</p>
				</article>
			</article>
		</div>
	</section>
	<footer class="foot section-gx">
	<ul class="foot-nav">
		<li>
			<article class="foot-l">
				© All rights Reserved by xdtart
			</article>
		</li>
		<li>
			<article class="foot-contact">
				<div class="item">
					<p>
					   Mail：<br><EMAIL><br><EMAIL>
					</p>				 
			  </div>
				<div class="item">
				 <p>
					   Tel:<br>18607110100
					</p>
				</div>
				<div class="item">
					 <p>
					   Address：<br>武汉市东西湖区团结激光工业园7栋C座5楼
					</p>
				</div>
			</article>
		</li>
		<li>
			<article class="foot-rbox">
				<p class="foot-erm">
					<img src="../../static/picture/b7ebf8e41850af4e.svg" class="imgw">
                    </p>
					<p><a href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2024076981号-1</a></p>
			</article>
		</li>
	</ul>
</footer>
<script src="../../static/js/plugin.js"></script>
<script src="../../static/js/page.js"></script>

	<script>
		var CaseTag="Address | 中国成都市双流区".replaceAll("<br />","^").split("^");
		var Awards="".replaceAll("<br />","^").split("^");
		var CaseTagstr="";
		if(CaseTag.length==0)
		 {
			 if(CaseTag.split("|").length>1)
		     {
				  CaseTagstr+="<li>";
                  CaseTagstr+="<p class=\"t\">"+CaseTag.split("|")[0].trim() +"</p><div class=\"c pf-re\">"+CaseTag.split("}")[1].trim() +"</div>";
				  CaseTagstr+="</li>";
			  }
		  }
		 else
		 {                                
		 for (var arr=0;arr<CaseTag.length;arr++) {
		   CaseTagstr+="<li>";
		   if(CaseTag[arr].split("|").length>1)
			{
                CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].split("|")[0].trim()+"</p>"+"<div class=\"c pf-re\">"+CaseTag[arr].split("|")[1].trim()+"</div>";
			}
		   else
			{
				CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].trim()+"</p>"+"<div class=\"c pf-re\"></div>";
			}
			 CaseTagstr+="</li>";
            }
          }
          if(Awards!="")
		  {
            CaseTagstr+="<li>";
            if(Awards.length==0)
		    {
			 if(Awards.split("|").length>1)
		     {
				CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c pf-re\"><dl class=\"awards-list pf-re\"><dt>"+Awards.split("|")[0].trim()+"</dt><dd>"+Awards.split("|")[1].trim()+"</dd></dl></div>";	  
			 }
		   }
		   else{
			
			CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c\">";
            for (var arr=0;arr<Awards.length;arr++) {
            CaseTagstr+="<dl class=\"awards-list pf-re\">";
			
		    if(Awards[arr].split("|").length>1)
			{				 
               CaseTagstr+="<dt>"+Awards[arr].split("|")[0].trim()+"</dt><dd>"+Awards[arr].split("|")[1].trim()+"</dd>";
			}
		    else
			{
				CaseTagstr+="<dt>"+Awards[arr].trim()+"</dt><dd></dd>";
			}
			CaseTagstr+="</dl>";
		    }	
		    }
            CaseTagstr+="</li>";
		  }
		  document.getElementById("CaseTag").innerHTML=CaseTagstr;

		 const htmlString='<img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/01.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/02.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/03.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/04.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/05.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/06.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/07.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/08.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/09.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/10.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/11.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/12.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/13.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/14.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/15.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/16.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/17.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/18.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/19.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/Parks/Parks03/20.jpg" /><img type="LargeUrl" tag="" src="/images/works/Parks/Parks03/21.jpg" />';
         const parser = new DOMParser();
         const doc = parser.parseFromString(htmlString, 'text/html');
         const imgTags = doc.getElementsByTagName('img');
         let imgAttributes="";
        for (let i = 0; i < imgTags.length; i++) {
        if(imgTags[i].getAttribute("tag")=="小图")
         {
         imgAttributes+="<div class=\"imgwitem-w\">";
        
        imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"Hi Park运动中心 · HI PARK\"/></p></div>";
        i++;

		if(i < imgTags.length)
         { 
			 if(imgTags[i].getAttribute("tag")=="小图")
			   imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"Hi Park运动中心 · HI PARK\" /></p></div></div>";
			 else 
			   imgAttributes+="</div><p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"Hi Park运动中心 · HI PARK\" /></p>";
		 }	 
        }
       else
       {
          imgAttributes+="<p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"Hi Park运动中心 · HI PARK\" /></p>";
       }
     }
	document.getElementById("worksimgs").innerHTML=imgAttributes;
	</script>
</body>

</html>