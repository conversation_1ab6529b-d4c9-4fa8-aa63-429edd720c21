

<section class="section-gx info-gx target2 topa">
    <div class="info-swiper">
        <div class="swiper-wrapper">

            <div class="swiper-slide">
                <figure class="imgBox">
                    <img class="imgw" src="../static/picture/office01.jpg">
                </figure>
            </div>

            <div class="swiper-slide">
                <figure class="imgBox">
                    <img class="imgw" src="../static/picture/office02.jpg">
                </figure>
            </div>

        </div>
    </div>
</section>
<section class="section-gx info-gx info-br">
    <ul class="info-desc-ul">
        <li class="info-item">
        </li>
        <li class="info-item">
            <div class="info-desc-t target2 tY50">
                我们为每个项目<br>创造独特的视觉语言，<br>搭建人与环境的情感链接。
            </div>
        </li>
        <li class="info-item">
            <div class="info-desc-c target2 tY50">
                序东堂是一家以文化为基础，<br>开发环境图形和品牌视觉的创新设计团队。<br>我们构思奇妙的概念和丰富的视觉表达<br>最终输出综合的设计解决方案。<br>项目分布全国50多座城市，<br>屡获殊荣和赞誉，<br>目前已是国内一流的EGD专业设计顾问。
            </div>
        </li>
    </ul>
</section>
<section class="section-gx info-gx leader-page target2 topa">
    <div class="leader-box">
        <div class="leader-item1">

        </div>
        <div class="leader-item2">
            <ul class="leader-list">
                <li class="target2 tY50">
                    <figure class="imgBox pos-ov">
                        <img type="img1" class="imgwh" src="../static/picture/che.jpg">
                    </figure>
                    <div class="desc leader-size lc-h7">
                        車 良<br>·<br>序东堂设计创始人 ｜ 艺术总监<br>日本字体设计协会会员<br>深圳市平面设计协会会员<br>·
                    </div>
                </li>
                <li class="target2 tY50">
                    <figure class="imgBox pos-ov">
                        <img type="img2" class="imgwh" src="../static/picture/wang.jpg">
                    </figure>
                    <div class="desc leader-size lc-h7">
                        王 恪<br>·<br>序东堂设计创始人<br>创作总监<br>·
                    </div>
                </li>
            </ul>
        </div>
    </div>
</section>
<section class="section-gx info-gx info-br">
    <ul class="info-desc-ul">
        <li class="info-item">
        </li>
        <li class="info-item">
            <div class="info-desc-t target2 tY50">
                保持新鲜<br>和好奇
            </div>
        </li>
        <li class="info-item">
            <div class="info-desc-c target2 tY50">
                公司由車良和王恪创立，<br>他们将20多年的经验与年轻一代的当代视野融为一体，<br>对不同文化特征、不同专业学科始终保持新鲜和好奇，<br>使得我们成为一个具有丰富经验并重视新鲜想法的活力团队。
            </div>
        </li>
    </ul>
</section>
<section class="section-gx info-gx target2 topa">
    <ul class="team-ul" id="team-ul">
        <script>
            var teamList = [];
            var htmlString = '<img src="/images/info/team/01.jpg"><img src="/images/info/team/02.jpg"><img src="/images/info/team/03.jpg"><img src="/images/info/team/04.jpg"><img src="/images/info/team/05.jpg"><img src="/images/info/team/06.jpg"><img src="/images/info/team/07.jpg"><img src="/images/info/team/08.jpg"><img src="/images/info/team/09.jpg"><img src="/images/info/team/10.jpg"><img src="/images/info/team/11.jpg"><img src="/images/info/team/12.jpg"><img src="/images/info/team/13.jpg"><img src="/images/info/team/14.jpg"><img src="/images/info/team/15.jpg"><img src="/images/info/team/16.jpg"><img src="/images/info/team/17.jpg"><img src="/images/info/team/18.jpg"><img src="/images/info/team/19.jpg"><img src="/images/info/team/20.jpg">';
            var parser = new DOMParser();
            var doc = parser.parseFromString(htmlString, 'text/html');
            var imgTags = doc.getElementsByTagName('img');
            for (let i = 0; i < imgTags.length; i++) {
                teamList.push({src: imgTags[i].getAttribute("src")});
            }
        </script>
    </ul>
</section>
<section class="section-gx info-gx info-br">
    <ul class="info-desc-ul">
        <li class="info-item">
        </li>
        <li class="info-item">
            <div class="info-desc-t target2 tY50">
                值得信赖<br>和托付
            </div>
        </li>
        <li class="info-item">
            <div class="info-desc-c target2 tY50">
                序东堂成立至今，<br>我们的团队与众多优秀的建筑、景观、运营、开发机构一起，<br>跨地域、文化、商业类型提供最独特的项目体验。
            </div>
        </li>
    </ul>
</section>
<section class="info-gx">
    <article class="logo-row" id="logolis">
        <script>
            htmlString = '<img src="/upload/images/2024/4/4f7ddf06db780545.svg"><img src="/upload/images/2024/4/c7c72d62d411e229.svg"><img src="/upload/images/2024/4/d1b9d92f3e7c094d.svg">';
            parser = new DOMParser();
            doc = parser.parseFromString(htmlString, 'text/html');
            imgTags = doc.getElementsByTagName('img');
            let imgAttributes = "";
            for (let i = 0; i < imgTags.length; i++) {
                if (i == 0)
                    imgAttributes += "<div class=\"logo-slider\" duration=\"10\" style=\"background-image: url(" + imgTags[i].getAttribute("src") + ");\"><i></i></div>";
                else if (i == 1)
                    imgAttributes += "<div class=\"logo-slider\" duration=\"8\" style=\"background-image: url(" + imgTags[i].getAttribute("src") + ");\"><i></i></div>";
                else if (i == 2)
                    imgAttributes += "<div class=\"logo-slider\" duration=\"12\" style=\"background-image: url(" + imgTags[i].getAttribute("src") + ");\"><i></i></div>";
                else if (i == 3)
                    imgAttributes += "<div class=\"logo-slider\" duration=\"8\" style=\"background-image: url(" + imgTags[i].getAttribute("src") + ");\"><i></i></div>";
                else if (i == 4)
                    imgAttributes += "<div class=\"logo-slider\" duration=\"10\" style=\"background-image: url(" + imgTags[i].getAttribute("src") + ");\"><i></i></div>";
            }
            document.getElementById("logolis").innerHTML = imgAttributes;
        </script>
    </article>
</section>
<section class="section-gx info-gx awards-page target2 topa">
    <article class="awards-tbox">
        <div class="row">
            <div class="col-item col-item1">
            </div>
            <div class="col-item col-item2">
                <h4 class="awards-title">
                    荣誉 · AWARDS
                </h4>
            </div>
        </div>
    </article>
    <article class="awards-cbox">
        <div class="row">
            <div class="col-item col-item1">
            </div>
            <div class="col-item col-item2">
                <ul class="awards-ul" id="awardslist">
                    <script>
                        var awards = "日本标识设计奖<br />2023 | 铜奖，2022 | 入选 * 4，2020 | 铜奖、入选 * 1<br /><br />日本字体设计年鉴<br />2023 | 评委会大奖、入选 * 2，2022 | 入选，2021 | 最佳作品奖，2020 | 最佳作品奖、入选 * 3<br /><br />纽约TDC<br />2022 | 年奖<br /><br />东京TDC<br />2023 | 入选，2022 | 入选 * 3<br /><br />Award 360°<br />2023 | 入选，2022 | 最佳作品奖，2021 | TOP 100，2020 |  TOP 100<br /><br />GDC Award<br />2023 |  入选 * 4<br />2021 |  铜奖<br /><br />西安TDC<br />2023 |  入选 * 2<br /><br />香港环球设计大奖<br />2019 |  铜奖".replaceAll("<br /><br />", "&");
                        var awardslist = '';
                        if (awards.includes("&")) {
                            awards = awards.split("&")

                            if (awards.length != 0) {
                                var awards2, awards3;
                                for (var arr = 0; arr < awards.length; arr++) {

                                    awards2 = awards[arr].replaceAll("<br />", "&");

                                    if (awards2.includes("&")) {

                                        awards2 = awards2.split("&");

                                        if (awards2.length != 0 && awards2[1] != "") {
                                            awardslist += '<li><div class="awards-li"><p class="name">' + awards2[0] + '</p></div><div class="awards-li">';

                                            if (awards2[1].includes("，")) {

                                                awards3 = awards2[1].split("，");

                                                if (awards3.length != 0 && awards3[1] != "") {
                                                    for (var arr3 = 0; arr3 < awards3.length; arr3++) {

                                                        if (awards3[arr3].split("|").length != 0) {
                                                            awardslist += '<div class="awards-item"><p class="year">' + awards3[arr3].split("|")[0] + '</p><p class="jx">' + awards3[arr3].split("|")[1] + '</p></div>';
                                                        }

                                                    }
                                                }
                                            } else {
                                                if (awards2[1].includes("|") && awards2[1].split("|").length != 0) {
                                                    awardslist += '<div class="awards-item"><p class="year">' + awards2[1].split("|")[0] + '</p><p class="jx">' + awards2[1].split("|")[1] + '</p></div>';
                                                }
                                            }
                                            awardslist += '</div></li>';
                                        }

                                    }

                                }
                                document.getElementById("awardslist").innerHTML = awardslist;
                            }

                        }
                    </script>
                </ul>

            </div>
        </div>
    </article>
</section>
<section class="section-gx info-gx info-br">
    <ul class="info-desc-ul">
        <li class="info-item">
        </li>
        <li class="info-item">
            <div class="info-desc-t target2 tY50">
                联系我们 · CONTACT
            </div>
        </li>
        <li class="info-item">
            <div class="contact-form target2 tY50">
                <form name="留言反馈" id="myForm" onbeforesend="validateForm">
                    <div class="contact-item">
                        <span class="t">Name</span>
                        <p class="rtxt"><input type="text" class="input-txt" name="Name" autocomplete="off"></p>
                    </div>
                    <div class="contact-item">
                        <span class="t">Telephone</span>
                        <p class="rtxt"><input type="tel" class="input-txt" name="Mobile" autocomplete="off"></p>
                    </div>
                    <div class="contact-item">
                        <span class="t">E-mail</span>
                        <p class="rtxt"><input type="email" class="input-txt" name="Email" autocomplete="off"></p>
                    </div>
                    <div class="contact-item">
                        <span class="t">Message</span>
                        <p class="rtxt"><input type="maxlength" class="input-txt" name="Content" autocomplete="off"></p>
                    </div>
                    <div class="contact-btn">
                        <p class="item">
                            <a href="javascript:;" class="btn-mess" onclick="formSubmit()">
                                <span class="txt">Enter</span>
                                <img src="../static/picture/btn1.svg" class="ico">
                            </a>
                        </p>
                    </div>
                </form>
            </div>
        </li>
    </ul>
</section>

<footer class="foot section-gx">
    <ul class="foot-nav">
        <li>
            <article class="foot-l">
                © All rights Reserved by xdtart
            </article>
        </li>
        <li>
            <article class="foot-contact">
                <div class="item">
                    <p>
                        Mail：<br><EMAIL><br><EMAIL>
                    </p>
                </div>
                <div class="item">
                    <p>
                        Tel:<br>18607110100
                    </p>
                </div>
                <div class="item">
                    <p>
                        Address：<br>武汉市东西湖区团结激光工业园7栋C座5楼
                    </p>
                </div>
            </article>
        </li>
        <li>
            <article class="foot-rbox">
                <p class="foot-erm">
                    <img src="../static/picture/b7ebf8e41850af4e.svg" class="imgw">
                </p>
                <p><a href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2024076981号-1</a></p>
            </article>
        </li>
    </ul>
</footer>
<script src="../static/js/plugin.js"></script>
<script src="../static/js/page.js"></script>

<script src="../static/js/info.js"></script>
<script>
    function validateForm(data) {
        var success = true;
        $('.contact-item').removeClass('error');
        if (!data['Name']) {
            $('#myForm input[name="Name"]').focus().parent().parent().addClass('error');
            success = false;
            return;
        } else {
            $('#myForm input[name="Name"]').parent().parent().removeClass('error');
        }
        if (!data['Mobile'] && !data['Mobile'] || !/^1[3-9]\d{9}$/.test(data['Mobile'])) {
            $('#myForm input[name="Mobile"]').focus().parent().parent().addClass('error');
            success = false;
            return;
        } else {
            $('#myForm input[name="Mobile"]').parent().parent().removeClass('error');
        }
        if (!data['Email'] || !/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(data['Email'])) {
            $('#myForm input[name="Email"]').focus().parent().parent().addClass('error');
            success = false;
            return;
        } else {
            $('#myForm input[name="Email"]').parent().parent().removeClass('error');

        }
        if (!data['Content']) {
            $('#myForm input[name="Content"]').focus().parent().parent().addClass('error');
            success = false;
            return;
        } else {
            $('#myForm input[name="Content"]').parent().parent().removeClass('error');
        }
        return success;
    }

    jQuery(".contact-item").click(function () {
        jQuery(this).find("input").focus();
    })
    var issend = true;

    function formSubmit() {
        var formData = new FormData(document.getElementById("myForm"));
        var data2 = {};
        formData.forEach(function (value, key) {
            data2[key] = value;
        });
        if (!validateForm(data2)) return;
        if (issend) {
            $.ajax({
                url: '/api/v1/forms?siteId=1&formName=留言反馈',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data2),
                dataType: 'json',
                beforeSend: function () {
                    jQuery(".contact-btn .txt").html("Submitting...");
                    issend = false;
                },
                success: function (response) {
                    jQuery(".contact-btn").html('<p class="item"><a href="javascript:void(0);" class="btn-mess btn-messzh"><span class="txt">留言成功，我们会尽快与你联系</span><img src="/img/btn2.svg" class="ico"/></a></p>');

                },
                error: function (xhr, status, error) {
                    document.getElementById("myForm").reset();
                    jQuery(".contact-btn").html('<p class="item"><a href="javascript:void(0);" class="btn-mess btn-messzh"><span class="txt">留言失败，请重新输入</span><img src="/img/btn3.svg" class="ico"/></a></p>');
                    setTimeout(function () {
                        jQuery(".contact-btn").html('<p class="item"><a onclick="formSubmit()" href="javascript:void(0);" class="btn-mess"><span class="txt">Enter</span><img src="/img/btn1.svg" class="ico"/></a></p>');
                        issend = true;
                    }, 1200);
                }
            });
            return false;
        }
    }
</script>
</body>

</html>