<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 绑定标签逻辑
 */
class ArticleLabelAccess extends LogicBase
{

    /**
     * 获取绑定标签搜索条件
     */
    public function getWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 获绑定标签单条信息
     */
    public function getArticleLabelAccessInfo($where = [], $field = '*')
    {

        return $this->modelArticleLabelAccess->getInfo($where, $field);
    }

    /**
     * 获取绑定标签列表
     */

    public function getArticleLabelAccessList($where = [], $field = '', $order = '', $paginate = false)
    {

        $field = 'l.*,ala.article_id';

        $this->modelArticleLabelAccess->alias('ala');

        $join = [
            [SYS_DB_PREFIX . 'label l', 'l.id = ala.label_id'],
        ];

        $this->modelArticleLabelAccess->join = $join;

        return $this->modelArticleLabelAccess->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取绑定标签列表
     */

    public function getLabelAccessLabelList($where = [], $field = '', $order = '', $paginate = false)
    {
        $field = 'l.*,ala.article_id';

        $this->modelArticleLabelAccess->alias('ala');

        $join = [
            [SYS_DB_PREFIX . 'label l', 'l.id = ala.label_id'],
        ];

        $this->modelArticleLabelAccess->join = $join;

        return $this->modelArticleLabelAccess->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取绑定标签无分页列表
     */
    public function getArticleLabelAccessColumn($where = [], $field = '', $key = '')
    {
        return $this->modelArticleLabelAccess->getColumn($where, $field, $key);
    }

    /**
     * 绑定标签单条编辑
     */
    public function articleLabelAccessEdit($data = [], $where = [])
    {

        $result = $this->modelArticleLabelAccess->setInfo($data, $where);

        return $result ? $result : $this->modelArticleLabelAccess->getError();
    }

    /**
     * 绑定标签单条编辑
     */
    public function articleLabelAccessSetInc($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {

        $result = $this->modelArticleLabelAccess->setIncDecInfo($where, $field, $number, $setType);

        return $result ? $result : $this->modelArticleLabelAccess->getError();
    }

    /**
     * 绑定标签删除
     */
    public function articleLabelAccessDel($where = [], $is_true = false)
    {

        $result = $this->modelArticleLabelAccess->deleteInfo($where, $is_true);

        return $result ? $result : $this->modelArticleLabelAccess->getError();
    }


    //Admin模块操作

    /**
     * 获取绑定标签搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 绑定标签单条编辑
     */
    public function articleLabelAccessAdminEdit($data = [])
    {


        $url = url('articleLabelAccessList');

        $data['member_id'] = MEMBER_ID;

        $result = $this->modelArticleLabelAccess->setInfo($data);

        $handle_text = empty($data['id']) ? '新增' : '编辑';

        $result && action_log($handle_text, '绑定标签' . $handle_text . '，title：' . $data['title']);

        return $result ? [RESULT_SUCCESS, '绑定标签操作成功', $url] : [RESULT_ERROR, $this->modelArticleLabelAccess->getError()];
    }

    /**
     * 绑定标签删除
     */
    public function articleLabelAccessAdminDel($where = [])
    {

        $result = $this->modelArticleLabelAccess->deleteInfo($where);

        $result && action_log('删除', '绑定标签删除，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '绑定标签删除成功'] : [RESULT_ERROR, $this->modelVip->getError()];
    }
}
