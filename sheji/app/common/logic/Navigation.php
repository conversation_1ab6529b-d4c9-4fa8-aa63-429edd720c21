<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 站点导航逻辑
 */
class Navigation extends LogicBase
{

      /**
       * 获取站点导航搜索条件
      */
      public function getAdminWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获站点导航单条信息
      */
     public function getNavigationInfo($where = [], $field = '*')
     {

        return $this->modelNavigation->getInfo($where, $field);
     }

    /**
     * 获取站点导航列表
     */

    public function getNavigationList($where = [], $field = '', $order = '',$paginate = 0)
    {
        return $this->modelNavigation->getList($where, $field, $order,$paginate);
    }

    /**
     * 获取站点导航无分页列表
     */
    public function getNavigationColumn($where = [], $field = '', $key = '')
    {
        return $this->modelNavigation->getColumn($where, $field , $key);
    }

    /**
     * 站点导航单条编辑
     */
    public function navigationAdminEdit($data = [])
    {


        $url = url('navigationList');
        
        $data['member_id'] = MEMBER_ID;

        $seo = $data['seo'];
        $seo['type']=4;
        unset($data['seo']);


        $seo_id = $this->logicSeo->seoEdit($seo);
        if(!empty($seo['id'])){
            $data['seo_id']=$seo['id'];
        }else{
            $data['seo_id']=$seo_id;
        }

        $result = $this->modelNavigation->setInfo($data);
        
        $handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '站点导航' . $handle_text . '，data：' .http_build_query($data));
        
        return $result ? [RESULT_SUCCESS, '站点导航修改成功', $url] : [RESULT_ERROR, $this->modelNavigation->getError()];
    }

    /**
     * 站点导航删除
     */
    public function navigationAdminDel($where = [])
    {

        $result = $this->modelNavigation->deleteInfo($where);
        
        $result && action_log('删除', '站点导航删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '站点导航删除成功'] : [RESULT_ERROR, $this->modelNavigation->getError()];
    }
}
