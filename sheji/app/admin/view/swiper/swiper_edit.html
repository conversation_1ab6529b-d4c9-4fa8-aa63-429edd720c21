<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<div class="box-body">
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label>轮播标题</label>
						<span class="">（轮播标题名称）</span>
						<input class="form-control" name="title" placeholder="请输入文章标题名称"
						       value="{$info['title']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>轮播排序</label>
						<span class="">（越大排最前）</span>
						<input class="form-control" name="sort" value="{$info['sort']|default='1'}" type="number">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>PC封面图片</label>
						<span class="">（最佳宽高比:1.5*1  最低宽2560px）</span>
						<br/>
						{assign name="cover_pc_id" value="$info.cover_pc_id|default='0'" /}
						{:widget('file/index', ['name' => 'cover_pc_id', 'value' => $cover_pc_id, 'type' => 'img'])}
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>手机封面图片</label>
						<span class="">（最佳宽高比:1*2.2  最低宽750px）</span>
						<br/>
						{assign name="cover_web_id" value="$info.cover_web_id|default='0'" /}
						{:widget('file/index', ['name' => 'cover_web_id', 'value' => $cover_web_id, 'type' => 'img'])}
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>视频</label>
						<span class="">（分辨率越大越好，注意不要太大，加载会慢）</span>
						<br/>
						{assign name="video_id" value="$info.video_id|default='0'" /}
						{:widget('file/index', ['name' => 'video_id', 'value' => $video_id, 'type' => 'file'])}
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>位置</label>
						<span class="">（轮播跳转）</span>
						<select name="location" class="form-control">
							<option value="index">首页</option>
							<option value="info">info页面</option>
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>跳转地址</label>
						<span class="">（必须带http://）</span>
						<input class="form-control" name="url" placeholder="请输入url"
						       value="{$info['url']|default=''}" type="text">
					</div>
				</div>

			</div>
			
			<div class="box-footer">
				
				<input type="hidden" name="id" value="{$info['id']|default='0'}"/>
				
				<button type="submit" class="btn  ladda-button ajax-post" data-style="slide-up"
				        target-form="form_single">
					<span class="ladda-label">确 定</span>
				</button>
				
				<a class="btn " onclick="javascript:history.back(-1);return false;"> 返 回</a>
			</div>
		
		</div>
	</div>
</form>

<script type="text/javascript">

	ob.setValue("location", "{$info['location'] |default = 0}");
</script>