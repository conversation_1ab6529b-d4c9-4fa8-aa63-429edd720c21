<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 站点导航控制器
 */
class Navigation extends AdminBase
{

    /**
     * 站点导航列表
     */
    public function navigationList()
    {

        $where = $this->logicNavigation->getAdminWhere($this->param);

        $this->assign('list', $this->logicNavigation->getNavigationList($where, '', '',60));
        $this->assign('list_column', $this->logicNavigation->getNavigationColumn(['status'=>1]));

        return $this->fetch('navigation_list');
    }

    /**
     * 站点导航添加
     */
    public function navigationAdd()
    {
        IS_POST && $this->jump($this->logicNavigation->navigationAdminEdit($this->param));

        $this->assign('list', $this->logicNavigation->getNavigationColumn(['status'=>1,'pid'=>0]));

        return $this->fetch('navigation_edit');
    }

    /**
     * 站点导航编辑
     */
    public function navigationEdit()
    {
        IS_POST && $this->jump($this->logicNavigation->navigationAdminEdit($this->param));

        $info = $this->logicNavigation->getNavigationInfo(['id' => $this->param['id']], '*');

        $this->assign('list', $this->logicNavigation->getNavigationColumn(['status'=>1,'pid'=>0]));

        $this->assign('seo',  $this->logicSeo->getSeoInfo(['id' => $info['seo_id']]));

        $this->assign('info', $info);

        return $this->fetch('navigation_edit');
    }


    /**
     * 站点导航删除
     */
    public function navigationDel($id = 0)
    {

        $this->jump($this->logicNavigation->navigationAdminDel(['id' => $id]));
    }

    /**
     * 数据状态设置
     */
     public function setStatus()
     {

         $this->jump($this->logicAdminBase->setStatus('Navigation', $this->param));

     }

     /**
      * 排序
     */
     public function setSort()
     {

         $this->jump($this->logicAdminBase->setSort('Navigation', $this->param));
     }
}
