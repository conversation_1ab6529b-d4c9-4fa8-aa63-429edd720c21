<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

use think\Cache;

/**
 * 轮播图控制器
 */
class Redis extends AdminBase
{

    /**
     * redis列表
     */
    public function redisList()
    {
        $redis_key = Cache::store('redis')->getKey();

        $redis_title = Cache::store('redis')->get('redis_name');

        $redis_list = array();


        $list=$this->logicMember->getMemberColumn(['status'=>['gt',-2]]);



        foreach ($redis_key as $key => $value) {
            $redis_info['name'] = $value;
            if (!empty($redis_title[$value])) {
                $redis_info['title'] = $redis_title[$value];
            } else {
                $redis_info['title'] = '';
            }
            $redis_info['val']=json_encode(Cache::store('redis')->get($redis_info['name']));
//            $name_array=explode("_",$redis_info['name']);
//
//            foreach ($list as $keym=>$valuem){
//
//                if(count($name_array)==3 and $name_array[0]==$valuem['id'] and $name_array[1]=='count' and $name_array[2]>100000){
//                    $data_post['count']=$redis_info['val'];
//                    $data_post['create_time']=$name_array[2];
//                    $data_post['member_id']=$valuem['id'];
//                    $data[]=$data_post;
//                    $data_where=db('api_statistical')->where(['create_time'=>$name_array[2],'member_id'=>$valuem['id']])->find();
//                    if(!empty($data_where)){
//
//                        db('api_statistical')->where('id', $data_where["id"])->update($data_post);
//                    }else{
//                        db('api_statistical')->insert($data_post);
//                    }
//                }
//
//            }
            $redis_list[] = $redis_info;
        }

        $this->assign('list', $redis_list);

        return $this->fetch('redis_list');
    }

    /**
     * 排序
     */
    public function redisName()
    {
        $param = $this->param;
        if (isset($param)) {
            $redis_title = Cache::store('redis')->get('redis_name');
            if (empty($redis_title)) {
                $redis_title = array();
            }
            $redis_title[$param['id']] = $param['value'];
            Cache::store('redis')->set('redis_name', $redis_title);
            $this->jump([RESULT_SUCCESS, '成功啦']);
        } else {
            $this->jump([RESULT_ERROR, '异常']);
        }
    }

    /**
     * redis删除
     */
    public function redisDel($id = null)
    {
        if (isset($id)) {
            $redis_key = Cache::store('redis')->getKey();

            foreach ($redis_key as $key => $value) {
                if ($value == $id) {
                    Cache::store('redis')->rm($id);
                }
            }
            $this->jump([RESULT_SUCCESS, '删除成功']);
        } else {
            $this->jump([RESULT_ERROR, '删除异常']);
        }
    }
}
