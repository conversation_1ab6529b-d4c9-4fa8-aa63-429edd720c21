<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\internal\controller;

class Common extends ApiBase
{

    public function getAccessTokenJsdk()
    {

        return $this->apiReturn(getAppidToken($this->param['wechat'], true));
    }

    public function getShare()
    {
        $tokens = getAppidToken($this->param['wechat'], true);
        $JSSDK = get_sington_object('JSSDKEx', "exwechat\\api\\JSSDK\\JSSDK", $tokens['access_token']);

        $share['appId'] = $tokens['appid'];
        $share['timestamp'] = time();
        $share['url'] = $this->param['url'];
        $share['nonceStr'] = 'abcdefghijklmnopqrstu';
        $share['signature'] = $JSSDK->signature($tokens['ticket'], $share['nonceStr'], $share['timestamp'], $this->param['url']);
        return $this->apiReturn($share);
    }


    public function getShortenUrl()
    {

        $tokens = getAppidToken($this->param['wechat'], true);

        $shortUrl = get_sington_object('shortUrl', "exwechat\\api\\account\\shortUrl", $tokens['access_token']);

        $url = $shortUrl->create($this->param['url']);

        return $this->apiReturn($url);
    }
    /**
     * @return
     * 获取图形码
     */
    public function codevalidation()
    {
        $captcha=$this->param['code'];

        if(!captcha_check($captcha)){
            $data=['code' => 1890007, 'msg' => '图形码错误'];
        }else{
            $data=['code' => 0, 'msg' => '通过'];
        }

        return $this->apiReturn($data);
    }
    /**
     * @return
     * 获取验证码
     */
    public function getVerifyCode()
    {
        $phone = $this->param['mobile'];

        $check = '/^(1(([3-9][0-9])|(47)))\d{8}$/';
        if (!preg_match($check, $phone)) {
            return $this->apiReturn(['code' => 1890007, 'msg' => '手机号格式输入不正确']);
        }
        $data = $this->logicCommonBase->getSmsCode(['PhoneNumberSet' =>array(($phone)) ,'TemplateId' => '927867','type' => 'code','TemplateParamSet' => array((string)mt_rand(10000, 99999))]);

        return $this->apiReturn($data);
    }
    /**
     * @return
     * 检查验证码
     */
    public function validationCode()
    {
        $variables = cache('send_sms_code_' . $this->param['mobile']);

        if ($variables != $this->param['sms_code'] or $variables == '' or $variables == null) {
            return $this->apiReturn(['code' => 1010009, 'msg' => '短信验证码错误']);
        }
        return $this->apiReturn(['code' => 0, 'msg' => '短信验证码正确']);
    }

    // 获取自己验证码
    public function getYan()
    {
        $mobile = $this->param['mobile'];
        dump(getRedisHash('verifyCode', $mobile));
    }
    //获取img图列表
    public function getPictureUrl()
    {

        $img_list =$this->logicFile->getPictureColumnUrl($this->param['imgs'], $field = 'id,path,url', $key = 'id');

        return $img_list;
    }
    //获取img图列表
    public function getPictureUrl()
    {

        $img_list =$this->logicFile->getPictureColumnUrl($this->param['imgs'], $field = 'id,path,url', $key = 'id');

        return $img_list;
    }
    //微信用户授权不修改数据
    public function unH5Oauth()
    {

        $wechat = $this->param['wechat'];

        $code = $this->param['code'];

        $tokens = getAppidToken($wechat, true);

        $OAuth = get_sington_object('OAuthEx', "exwechat\\api\\OAuth\\OAuth", $tokens);

        $ret = $OAuth->getToken($code);

        if (isset($ret['errcode'])) {
            return $this->apiReturn(['code' => 1000004, 'msg' => '获取微信code码无效，请重新授权']);
            exit();
        }

        $info = $OAuth->getUserInfo($ret['access_token'], $ret['openid']);
        if (isset($info['errcode'])) {
            return $this->apiReturn(['code' => 1000004, 'msg' => '获取用户信息失败，请重新授权']);
            exit();
        }
        $channel_data=array();
        $channel_data['nickname'] = filterEmoji($info['nickname']);
        $channel_data['headimgurl'] = $info['headimgurl'];
        $channel_data['openid'] = $info['openid'];
        $channel_data['unionid'] = $info['unionid'];
        $channel_data['sex'] = $info['sex'];

        return $this->apiReturn($channel_data);
    }
    //用户授权获取
    public function userOauth()
    {

        $wechat = $this->param['wechat'];

        $code = $this->param['code'];

        $tokens = getAppidToken($wechat, true);

        $OAuth = get_sington_object('OAuthEx', "exwechat\\api\\OAuth\\OAuth", $tokens);

        $ret = $OAuth->getToken($code);

        if (isset($ret['errcode'])) {
            return $this->apiReturn(['code' => 1000004, 'msg' => '获取微信code码无效，请重新授权']);
            exit();
        }

        if (!empty($this->param['user_token'])) {

            $decoded_user_token = decoded_user_token($this->param['user_token']);

            $whereRegistered['openid'] = $this->param['openid'];

            $info = $this->logicWeiUser->getWeiUserInfo(['id' => $decoded_user_token['user_id']], 'id,mobile,openid,invite,nickname');
            $userdata['id'] = $info['id'];
        } else {
            $info = $this->logicWeiUser->getWeiUserInfo(['openid' => $ret['openid']], 'id,openid,openid,mobile,nickname');
        }
        if (empty($info) || empty($info['openid'])) {
            $info = $OAuth->getUserInfo($ret['access_token'], $ret['openid']);
            if (isset($info['errcode'])) {
                return $this->apiReturn(['code' => 1000004, 'msg' => '获取用户信息失败，请重新授权']);
                exit();
            }

            $userdata['create_time'] = time();
            $userdata['openid'] = $info['openid'];
            $userdata['nickname'] = filterEmoji($info['nickname']);
            $userdata['sex'] = $info['sex'];
            $userdata['city'] = $info['city'];
            $userdata['country'] = $info['country'];
            $userdata['province'] = $info['province'];
            $userdata['headimgurl'] = $info['headimgurl'];
            $userdata['mobile'] = 0;
            $headimgurl = './upload/headimg/' . $userdata['openid'] . '.png';

            if (!file_exists($headimgurl)) {
                downFile($userdata['headimgurl'], $headimgurl);
            }

            $re = $this->logicWeiUser->weiUserWebEdit($userdata, [], true);
            if ($re) {
                $info_rest['user_id'] = $re;
                $info_rest['openid'] = $info['openid'];
                $info_rest['mobile'] = $info['mobile'];

                if(!empty($userdata['id'])){
                    $info_rest['msg'] = '微信绑定成功';
                }else{
                    $info_rest['msg'] = '授权成功-请完善用户信息';
                }
            } else {
                return $this->apiReturn(['code' => 1010003, 'msg' => '用户注册失败，请重新授权']);
            }
        } else {
            if (!empty($info_rest['mobile'])) {
                $key = API_KEY . JWT_KEY;
                $token = [
                    "iss" => "faiJWT",         // 签发者
                    "iat" => TIME_NOW,              // 签发时间
                    "exp" => TIME_NOW + TIME_NOW,   // 过期时间
                    "aud" => 'guai',             // 接收方
                    "sub" => 'fei',             // 面向的用户
                    "user_id" => $info['id']
                ];

                $JWT = get_sington_object('OAuthEx', "\\Firebase\\JWT\\JWT", $tokens);

                $jwt = JWT::encode($token, $key);
                $info_rest['user_token'] = $jwt;
                $info_rest['msg'] = '登录成功';
            } else {
                $info_rest['msg'] = '授权成功-请完善用户信息';
            }
            $info_rest['user_id'] = $info['id'];
            $info_rest['mobile'] = $info['mobile'];
            $info_rest['openid'] = $info['openid'];
        }

        return $this->apiReturn($info_rest);
    }
    /**
     * @return
     * 文章详细
     */
    public function articleApi()
    {
        $articleInfo = $this->logicArticle->getArticleInfo(['a.status' =>['gt',-2], 'a.id' => $this->param['id']]);

        if (empty($articleInfo)) {
            return $this->apiReturn(['code' => 1050602, 'msg' => '当前资讯不存在']);

        }

        $articleInfo['cover_url'] = $this->logicFile->getPictureUrl($articleInfo['cover_id']);

        $articleInfo['name'] = html_entity_decode($articleInfo['name']);
        $articleInfo['describe'] = html_entity_decode($articleInfo['describe']);
        $articleInfo['content'] = html_entity_decode($articleInfo['content']);
        $articleInfo['thumbnail_url'] = 'https://asxup-1305125772.cos.ap-guangzhou.myqcloud.com/static/app/img/share_log.png';
        if(!empty($articleInfo['thumbnail_id'])){
            $articleInfo['thumbnail_url'] = $this->logicFile->getPictureUrl($articleInfo['thumbnail_id']);
        }

        $commentsWhere['main_id']=$this->param['id'];
        $commentsWhere['affiliated_id']=0;

        $commentsList=$this->logicComments->getCommentsList($commentsWhere,'id,affiliated_id,user_id,content,praise,create_time','id DESC',false);

        $praiseLogColumn = $this->logicPraiseLog->getPraiseLogColumn(['main_id' => $this->param['id']], 'affiliated_id');

        foreach ($commentsList as $key=>&$value){
            $user_ids[]=$value['user_id'];
            $value['is_praise']=0;
            if(in_array( $value['id'] , $praiseLogColumn)){
                $value['is_praise']=1;
            }
            $commentsTwoWhere=$commentsWhere;
            $commentsTwoWhere['affiliated_id']=$value['id'];
            $commentsTwo=$this->logicComments->getCommentsList($commentsTwoWhere,'id,affiliated_id,user_id,content,praise,create_time','id DESC',  false,5);
            if($commentsTwo){
                foreach ($commentsTwo as $keyt=>&$valuet){
                    $user_ids[]=$valuet['user_id'];
                    $valuet['is_praise']=0;
                    if(in_array( $valuet['id'] , $praiseLogColumn)){
                        $valuet['is_praise']=1;
                    }

                    $commentsThreeWhere=$commentsWhere;
                    $commentsThreeWhere['affiliated_id']=$valuet['id'];
                    $commentsThree=$this->logicComments->getCommentsList($commentsThreeWhere,'id,affiliated_id,user_id,content,praise,create_time','id DESC',  false,5);
                    if($commentsThree){
                        foreach ($commentsThree as $keytr=>&$valuetr){
                            $user_ids[]=$valuetr['user_id'];
                            $valuetr['is_praise']=0;
                            if(in_array( $valuetr['id'] , $praiseLogColumn)){
                                $valuetr['is_praise']=1;
                            }
                        }
                    }
                    $valuet['child']=$commentsThree;
                }
            }

            $value['child']=$commentsTwo;
        }

        $articleInfo['commentsList']=$commentsList;
        $UserList=array();
        if(!empty($user_ids)){
            $UserList=$this->logicUser->getUserColumn(['id'=>['in',$user_ids]],'id,nickname,headimgurl');
        }

        $articleInfo['UserList']=$UserList;

        return $this->apiReturn($articleInfo);
    }

}
