<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\internal\controller;

/**
 * 用户标签控制器
 */
class Userlabel extends AppBase
{

    /**
     * 用户标签列表
     */
    public function userLabelList()
    {

        $where = $this->logicUserLabel->getWhere($this->param_data);

        $data=$this->logicUserLabel->getUserLabelList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 用户标签无分页列表
     */
    public function userLabelColumn()
    {

        $data=$this->logicUserLabel->getUserLabelColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 用户标签添加
     */
    public function userLabelAdd()
    {
	  
	   $regit=$this->logicUserLabel->userLabelEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 用户标签删除
     */
    public function userLabelDel()
    {

       $regit=$this->logicUserLabel->userLabelDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
