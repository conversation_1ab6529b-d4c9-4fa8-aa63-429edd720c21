<script src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js" type="text/javascript" charset="utf-8"></script>
<script>
var ua = window.navigator.userAgent.toLowerCase();
$(function(){
    if(ua.match(/MicroMessenger/i) == 'micromessenger'){
            // 通过config接口注入权限验证配置
            wx.config({
                debug:      false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId:      "{$appId}",   // 必填，公众号的唯一标识
                timestamp:  "{$timestamp}", // 必填，生成签名的时间戳
                nonceStr:   "{$nonceStr}", // 必填，生成签名的随机串
                signature:  "{$signature}",// 必填，签名
                jsApiList:[
                    'onMenuShareAppMessage',
                    'onMenuShareTimeline',
                    'onMenuShareQQ',
                    'onMenuShareWeibo',
                    'onMenuShareQZone'
                ] // 必填，需要使用的JS接口列表
            });
            // 通过ready接口处理成功验证
            wx.ready(function(){
                // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。
                // 对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。

                var obj = {
                    title:  "{$share_title}", // 分享标题
                    desc:   "{$share_desc}", // 分享描述
                    link:   "{$share_link}", // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                    imgUrl: "{$share_img_url}", // 自定义图标
                    success: function () {
                        // 用户确认分享后执行的回调函数
                    },
                    cancel: function () {
                        // 用户取消分享后执行的回调函数
                    }
                };

                //分享给朋友
                wx.onMenuShareAppMessage(obj);
                //分享给朋友圈
                wx.onMenuShareTimeline(obj);
                //分享给QQ
                wx.onMenuShareQQ(obj);
                //分享给微博
                wx.onMenuShareWeibo(obj);
                //分享给QQ空间
                wx.onMenuShareQZone(obj);

            });
    }
});

</script>