<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;
use lunar\Solar;
use think\Cache;
/**
 * 前端首页控制器
 */
class Index1 extends IndexBase
{


     
    public function rps($name = '')
    {
        $rpsColumn= $this->logicRps->getRpsColumn(['Name' => ['like',"%$name%"]],'id,Name,Born_on,Born_on,Born_Time,latitude,TimeZone,Data_Source,Place');

        // 关闭布局
        $this->view->engine->layout(false);

        foreach ($rpsColumn as $key=>&$value){
            $value['longitude_latitude']=$this->DegreeToDecimal(explode(",", $value['latitude']));

            $Born_on_str='';
            // 19 October 1198 BC (-1197) Jul.Cal.
            if(strstr($value['Born_on'], "greg.")!== false and strstr($value['Born_on'], "Jul.Cal.")!== false){
                $Born_on_array = explode("Jul.Cal. (", $value['Born_on']);

                $Born_on_str=trim($Born_on_array[1],'greg.)');

                $value['timestamp']=strtotime($Born_on_str.' '.$value['Born_Time']);

            }if(strstr($value['Born_on'], "BC (")!== false and strstr($value['Born_on'], "Jul.Cal.")!== false){
                $Born_on_array = explode("BC (", $value['Born_on']);

                $value['timestamp']=strtotime($Born_on_array[0].' '.$value['Born_Time']);

            }else if(strstr($value['Born_on'], "greg.")!== false){

                $Born_on_str=trim($value['Born_on'],'(greg.)');
                $value['timestamp']=strtotime($Born_on_str.''.$value['Born_Time']);

            }else if(strstr($value['Born_on'], "Jul.Cal.")!== false){
                $Born_on_str=trim($value['Born_on'],'Jul.Cal.');
                $value['timestamp']=strtotime($Born_on_str.''.$value['Born_Time']);
            }else{
                $value['timestamp']=strtotime($value['Born_on'].' '.$value['Born_Time']);
            }

            $value['datetime']=date("Y-m-d H:i:s",$value['timestamp']);

        }

        return array_values($rpsColumn);
    }
    
    public function rpsTzT()
    {
        $location=$this->param['location'];
        $timestamp=$this->param['timestamp'];
        if($timestamp<0){
            $timestamp=1;
        }
        $rpsColumn=curl_request('https://api.map.baidu.com/timezone/v1?coord_type=wgs84ll&location='.$location.'&timestamp='.$timestamp.'&ak=aeQqEZRQ1l4Zc7XINkHIhGCI');
        $tz=json_decode($rpsColumn,true);

        return round($tz['raw_offset']/3600,2);
    }

    public function DegreeToDecimal()
    {
        $redis_array=Cache::store('redis')->get('astroCalendar');
        dump($redis_array);
    }

 
}
