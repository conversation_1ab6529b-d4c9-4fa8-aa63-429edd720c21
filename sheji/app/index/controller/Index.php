<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;

use lunar\Solar;
use astrology\SweTest as SweTest;

/**
 * 前端首页控制器
 */
class Index extends IndexBase
{


    // 首页
    public function index($cid = 0)
    {
        $navigation_list_array = $this->navigation_list(0);
        //获取所热文推荐
        $this->assign('article_placed_list', $this->logicArticle->getCleanArticleList(['status' => 1], '', 'id desc', false, 6));

        // $this->assign('category_list', $this->logicArticle->getArticleCategoryList([], true, 'create_time asc', false));


        $where_swiper['status'] = 1;
        $where_swiper['location'] = 'index';
        $swiper_column = array_values($this->logicSwiper->getSwiperList($where_swiper, '*', 'sort desc', false));
        foreach ($swiper_column as $keys => &$values) {
            $values['cover_pc_url'] = $this->logicFile->getPictureUrl($values['cover_pc_id']);
            $values['cover_web_url'] = $this->logicFile->getPictureUrl($values['cover_web_id']);
            $values['cover_type'] = 1;
            if(!empty($values['cover_pc_id'])){

            }
        }
        $this->assign('swiper_column', $swiper_column);

        // 关闭布局
        $this->view->engine->layout(false);

        return $this->fetch('index');
    }


    //公司简介

    public function info()
    {
        $param = $this->param;
        $category_id = $param['category_id'];
        $navigation_list_array = $this->navigation_list($category_id);
        unset($param['category_id']);
        $template_name = $navigation_list_array['category_name'];

        $where_swiper['status'] = 1;
        $where_swiper['location'] = 'info';
        $swiper_column = array_values($this->logicSwiper->getSwiperList($where_swiper, '*', 'sort desc', false));
        foreach ($swiper_column as $keys => &$values) {
            $values['cover_pc_url'] = $this->logicFile->getPictureUrl($values['cover_pc_id']);
            $values['cover_web_url'] = $this->logicFile->getPictureUrl($values['cover_web_id']);
        }
        $this->assign('swiper_column', $swiper_column);


        return $this->fetch($template_name);

    }

    //文章列表
    public function articlelist()
    {
        $param = $this->param;

        $category_id = $param['category_id'];
        $navigation_list_array = $this->navigation_list($category_id);
        unset($param['category_id']);
        $param['category_id'] = $navigation_list_array['category_array'];

        $where = $this->logicArticle->getIndexWhere($param);
        //dump($where);

        //如果category_id=2就正向排序
        $sort = 'sort desc';


        $article_list = $this->logicArticle->getCleanArticleList($where, '*', $sort, 30);

        foreach ($article_list as $keys => &$values) {

            $values['url'] = url('articleinfo', ['id' => $values['id']]);
            $values['name'] = nl2br($values['name']);
            $values['release_time'] = date('Y.m.d', $values['release_time']);
            $values['cover_url'] = $this->logicFile->getPictureUrl($values['cover_id']);
        }

        $this->assign('article_list', $article_list);

        //$this->right_category();

        $template_name = $navigation_list_array['category_name'];

        return $this->fetch($template_name);
    }

    //导航列表
    public function navigation_list($category_id = 0)
    {
        $navigation_list['top'] = array();
        $navigation_list['two_parent_name'] = '';
        $category_name = '';
        $navigation_list['two_list'] = array();

        $category_array[] = $category_id;
        $navigation_list_p = $this->navigation_list;
        $navigation_list_array = $navigation_list_p['list'];
        $nvation_list_tree = $navigation_list_p['tree'];

        if (!empty($nvation_list_tree[$category_id]['children'])) {
            $category_child = array_column($nvation_list_tree[$category_id]['children'], 'id');
            $category_array = array_merge($category_array, $category_child);
        }
        $navigation_list['top'] = $nvation_list_tree;
        $navigation_list['top_id'] = $category_id;
        $navigation_list['top_url'] = url('articlelist', ['category_id' => $category_id]);
        if ($category_id > 0) {
            if ($navigation_list_array[$category_id]['pid'] == 0) {
                $navigation_list['two_parent_name'] = $navigation_list_array[$category_id]['name'];
                $navigation_list['two_list'] = $nvation_list_tree[$category_id]['children'];
            } else {
                $navigation_list['two_parent_name'] = $navigation_list_array[$navigation_list_array[$category_id]['pid']]['name'];
                $navigation_list['two_list'] = $nvation_list_tree[$navigation_list_array[$category_id]['pid']]['children'];
                $navigation_list['top_id'] = $navigation_list_array[$category_id]['pid'];
                $navigation_list['top_url'] = url('articlelist', ['category_id' => $navigation_list_array[$category_id]['pid']]);
            }
            $category_name = $navigation_list_array[$category_id]['type'];
        }

        $this->assign('category_id', $category_id);

        $this->assign('navigation', $navigation_list);

        return ['category_name' => $category_name, 'category_array' => $category_array];
    }

    //文章详细

    public function articleinfo()
    {


        $info = $this->logicArticle->getArticleInfo(['id' => $this->param['id'], 'status' => 1]);


        if (empty($info)) {
            $this->error('当前文章不存在', url('empty404'));
        }

        $weher['category_id'] = $info['category_id'];
        $weher['id'] = ['in', [$this->param['id'] + 1, $this->param['id'], $this->param['id'] - 1]];

        $weher['status'] = 1;

        $article_list = $this->logicArticle->getCleanArticleList($weher, '*', 'sort,id desc', false);

        $article_list_comut = count($article_list);


        $this_zi_key = 0;


        $prev_url = '';
        $next_url = '';
        foreach ($article_list as $keuf => $csdjg) {
            if ($csdjg['id'] == $this->param['id']) {
                $this_zi_key = $keuf;
                $info = $csdjg;
            }
        }
        if ($article_list_comut == 2) {
            if ($this_zi_key == 1) {
                $prev_url = url('articleinfo', ['id' => $article_list[$this_zi_key - 1]['id']]);
            }
            if ($this_zi_key == 0) {
                $next_url = url('articleinfo', ['id' => $article_list[$this_zi_key + 1]['id']]);
            }

        } else if ($article_list_comut == 3) {
            $prev_url = url('articleinfo', ['id' => $article_list[0]['id']]);
            $next_url = url('articleinfo', ['id' => $article_list[2]['id']]);
        }

        $info['name'] = nl2br($info['name']);
        $info['release_time'] = date('Y.m.d', $info['release_time']);
        $info['describe'] = nl2br($info['describe']);
        $info['describe2'] = nl2br($info['describe2']);


        // 正则表达式匹配图片URL
        $pattern = '/(<img[^>]+src=")([^">]+)("[^>]*>)/i';
// 替换函数，在图片URL后面加上 ?x-oss-process=style/logo
        $replacement = '$1$2?x-oss-process=style/logo$3';
// 执行替换
        $result = preg_replace($pattern, $replacement, $info['content']);

// 正则表达式匹配<img>标签中的src属性
        $pattern = '/(<img[^>]+src=")(\/upload\/picture\/[^">]+)("[^>]*>)/i';
// 替换函数，为匹配到的路径加上域名
        $replacement = '$1https://xdtart.oss-cn-beijing.aliyuncs.com$2$3';
// 执行替换
        $result = preg_replace($pattern, $replacement, $result);

        $info['content'] = html_entity_decode($result);


        $category_id = $info['category_id'];
        $navigation_list_array = $this->navigation_list($category_id);

        $param['category_id'] = $navigation_list_array['category_array'];

        $this->logicArticle->articleApiEdit(['id' => $info['id'], 'view' => $info['view'] + 1]);

        $this->assign('info', $info);

        $this->assign('fanye', [
            'prev_url' => $prev_url,
            'next_url' => $next_url
        ]);


        $template_name = $navigation_list_array['category_name'];

        $template_name = str_replace("list", "info", $template_name);
        return $this->fetch($template_name);

    }

    public function feedbackadd()
    {
        $param = $this->param;
        $article_category = $this->logicFeedback->feedbackEdit($param);
        return "{'code':0,'msg':'操作成功'}";
        $this->view->engine->layout(false);
    }

    /**
     * 问题新增
     */
    public function seoInfo($id)
    {

        $seoInfo = $this->logicSeo->getSeoInfo(['id' => $id]);


        $company_info = $this->companyInfo;

        !empty($seoInfo['title']) && $company_info['title'] = $seoInfo['title'] . '|星盘接口|星盘api';

        !empty($seoInfo['keywords']) && $company_info['keywords'] = $seoInfo['keywords'] . ',星座运势,星盘接口,星盘api';

        !empty($seoInfo['description']) && $company_info['description'] = $seoInfo['description'] . ',来自免费提供专业的星盘API官网，包括本命星盘、各种推运星盘、次限盘、三限盘、合盘、比较盘等等';

        $this->assign('company_info', $company_info);
    }

    //注册

    public function empty404()
    {
        return $this->fetch('empty');
    }


}
