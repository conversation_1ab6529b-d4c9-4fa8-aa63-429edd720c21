<?php

namespace astrology;


class SweTest
{
    public $sweph = './sweph/';
    public $ay_longitude = 0;

    public function calculate($conditions, $starsCode, $planetOut = [], $ay = false)
    {
        if (empty($planetOut)) {
            $planetOut = $this->SweTest($conditions);
        }

        if (isset($ay) && $ay > -1) {
            $ay_conditions = $conditions;
            $ay_conditions['ay'] = $ay;
            $ay_conditions['p'] = '0';
            $ay_conditions['f'] = 'Tl';
            [
                'b' => '10.10.2020',
                'p' => '01',
                'house' => '121.47,31.23,K',
                'ut' => '04:14:00',
                'f' => 'Tl',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'ay' => '0',
                'head',
                'roundsec'
            ];
            $ay_planetOut = $this->SweTest($ay_conditions);

            $ay_planetinfo = explode(',', str_replace(' ', '', $ay_planetOut[0]));

            $this->ay_longitude = $this->changeToDu($ay_planetinfo[1]);
        }

        $house_data = array();
        $planet_data = array();
        $ascmcs_data = array();
        $planet_array = array();

        $house_one = $this->house_one_index($starsCode);

        $planetEnglish = planetName::$planetEnglish;
        $data['planetEnglish'] = planetName::$planetEnglish;
        $data['planetChinese'] = planetName::$planetChinese;
        $data['planetFont'] = planetName::$planetFont;

        $data['house_life'] = planetName::$house_life;
        $data['signFont'] = planetName::$signFont;
        $data['signEnglish'] = planetName::$signEnglish;
        $data['sign_guardian_index'] = planetName::$sign_guardian_index;
        $data['sign_phase'] = planetName::$sign_phase;
        $data['signChinese'] = planetName::$signChinese;

        foreach ($planetOut as $key => $value) {
            $planetinfo = explode(',', str_replace(' ', '', $value));
            if ($planetinfo[0] == 'house1') {
                $house_one = $key;
            }
            if ($house_one <= $key and $key < ($house_one + 12)) {
                $planetinfo[1] -= $this->ay_longitude;
                $planetinfo[1] = $this->crunch($planetinfo[1]);

                $longitude_array['sign'] = $this->Convert_sign_Longitude($planetinfo[1]);

                $longitude_array['longitude'] = $planetinfo[1];

                $longitude_array['house_id'] = $key - $house_one + 1;

                $house_data[] = $longitude_array;

            }
            $planet_array[$planetinfo[0]] = $planetinfo;
            if ($key >= ($house_one + 12)) {
                $planetinfo[1] -= $this->ay_longitude;
                $planetinfo[1] = $this->crunch($planetinfo[1]);

                $ascmcs_info['planet_english'] = $planetinfo[0];
                $ascmcs_info['longitude'] = $planetinfo[1];
                $ascmcs_info['sign'] = $this->Convert_sign_Longitude($planetinfo[1]);

                $ascmcs_data[] = $ascmcs_info;
            }
        }
        foreach ($starsCode as $keyCode => $lineCode) {

            if (is_array($lineCode)) {
                if ($keyCode == 'virtual') {
                    foreach ($lineCode as $keyX => $lineX) {
                        $function = 'calculate' . $lineX;
                        $longitude_array = $this->$function($planet_array, $conditions);


                        $planet_data[] = $this->planet_chinese_english($longitude_array,$data['planetEnglish'],$data['planetChinese']);

                    }
                } elseif ($keyCode == 'planet_xs') {
                    foreach ($lineCode as $keyX => $lineX) {
                        $lineX = str_replace("xs", "", $lineX);
                        $longitude_array = $this->calculateplanetXs($conditions, $lineX);
                        $planet_data[] = $this->planet_chinese_english($longitude_array,$data['planetEnglish'],$data['planetChinese']);
                    }
                } elseif ($keyCode == 'planet_xf') {
                    foreach ($lineCode as $keyX => $lineX) {

                        $data['planetEnglish'][$lineX] = planetName::$fixedEnglish[$lineX];
                        $data['planetChinese'][$lineX] = planetName::$fixedChinese[$lineX];
                        $data['planetFont'][$data['planetEnglish'][$lineX]] = planetName::$fixedFont[$data['planetEnglish'][$lineX]];

                        $longitude_array = $this->calculateplanetXf($conditions, $lineX);
                        $planet_data[] = $this->planet_chinese_english($longitude_array,$data['planetEnglish'],$data['planetChinese']);
                    }
                } elseif ($keyCode == 'planet_hel') {
                    foreach ($lineCode as $keyX => $lineX) {

                        $longitude_array = $this->calculateplanetHel($conditions, $lineX);
                        $planet_data[] = $this->planet_chinese_english($longitude_array,$data['planetEnglish'],$data['planetChinese']);
                    }
                }
            } else {
                $planet_info_english = $planetEnglish[$lineCode];

                $planet_data[] = $this->planet_chinese_english($planet_array[$planet_info_english],$data['planetEnglish'],$data['planetChinese']);
            }
        }

        if (!empty($planet_array['Ascendant'])) {
            $data['planet_ascendant'] = $this->planet_chinese_english($planet_array['Ascendant'],$data['planetEnglish'],$data['planetChinese']);
        }
        $data['planet'] = $planet_data;
        $data['ascmcs_data'] = $ascmcs_data;
        $data['house'] = $house_data;

        return $data;
    }

    public function SweTest($conditions)
    {
        $query = $this->sweph . "swetest -edir" . $this->sweph . ' ';
        if (is_array($conditions)) {
            $options = [];
            foreach ($conditions as $key => $value) {
                $options[] = is_int($key) ? '-' . $value : '-' . $key . $value;
            }
            $query .= implode(' ', $options);
        } else {
            $query .= $conditions;
        }

        exec($query, $planetOut);

        return $planetOut;
    }

    //计算宫位从第几个开始
    public function house_one_index($starsCode)
    {
        $house_one = count($starsCode);
        if (!empty($starsCode['virtual'])) {
            $house_one = $house_one - 1;
        }
        if (!empty($starsCode['planet_xs'])) {
            $house_one = $house_one - 1;
        }
        if (!empty($starsCode['planet_xf'])) {
            $house_one = $house_one - 1;
        }
        if (!empty($starsCode['planet_hel'])) {
            $house_one = $house_one - 1;
        }
        return $house_one;
    }

    //度分秒转换小数
    public function changeToDu($dfm)
    {

        $du = explode('°', $dfm);

        $d = $du[0];
        $fen = explode("'", $du[1]);

        $f = 0;
        !empty($fen[0]) && $f = $fen[0];

        $m = 0;
        !empty($fen[1]) && $m = rtrim($fen[1], ',');

        $f = floatval($f) + floatval($m) / 60;
        return floatval($f) / 60 + floatval($d);
    }

    //度数绝对值
    public function crunch($x)
    {
        if ($x >= 0) {
            $y = $x - floor($x / 360) * 360;
        } else {
            $y = 360 + ($x - ((1 + floor($x / 360)) * 360));
        }

        return $y;
    }

    //星体数据转换
    function Convert_sign_Longitude($longitude)
    {
        $longitude = $this->crunch($longitude);
        $sign_num = intval($longitude / 30);
        $pos_in_sign = $longitude - ($sign_num * 30);
        $deg = intval($pos_in_sign);
        $full_min = ($pos_in_sign - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);
        $signEnglish = planetName::$signEnglish;
        $signChinese = planetName::$signChinese;
        $signFont = planetName::$signFont;
        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec, 'sign_id' => $sign_num, 'sign_english' => $signEnglish[$sign_num], 'sign_chinese' => $signChinese[$sign_num], 'sign_font' => $signFont[$sign_num]];
    }

    //行星转换数组
    function planet_chinese_english($longitude_array,$planetEnglish,$planetChinese)
    {
        $longitude_array[1] -= $this->ay_longitude;
        $longitude_array[1] = $this->crunch($longitude_array[1]);
        $planet_info['sign'] = $this->Convert_sign_Longitude($longitude_array[1]);
        $planet_info['planet_english'] = $longitude_array[0];
        $planet_info['code_name'] = (string)array_search($planet_info['planet_english'], $planetEnglish);
        $planet_info['planet_chinese'] = $planetChinese[$planet_info['code_name']];
        $planet_info['longitude'] = $longitude_array[1];
        if (!empty($longitude_array[3])) {
            $planet_info['house_number'] = $longitude_array[3];

            $planet_info['house_id'] = floor($planet_info['house_number']);
        }
        if(empty($planet_info['house_number']) and !empty($planet_info['house_id'])){
            $planet_info['house_id']=$planet_info['house_number'];
        }
        if (!empty($longitude_array[2])) {
            $planet_info['speed'] = $longitude_array[2];
        } else {
            $planet_info['speed'] = 360;
        }
        return $planet_info;
    }

    //小行星
    function calculateplanetXs($conditions, $xs)
    {

        $conditions['p'] = 's';
        $conditions['xs'] = $xs;
        $planetOut = $this->SweTest($conditions);
        if (!empty($planetOut)) {
            return explode(',', str_replace(' ', '', $planetOut[0]));
        }

    }

    //恒星
    function calculateplanetXf($conditions, $xs)
    {
        $conditions['p'] = 'f';
        $conditions['xf'] = $xs;

        $planetOut = $this->SweTest($conditions);
        if (!empty($planetOut)) {
            $planetXf = explode(',', str_replace(' ', '', $planetOut[0]));
            unset($planetXf[1]);
            $planetXf = array_values($planetXf);
            return $planetXf;
        }
    }

    //日星法星星
    function calculateplanetHel($conditions, $xs)
    {

        $conditions['hel'] = '';
        $conditions['p'] = $xs;
        $planetOut = $this->SweTest($conditions);

        if (!empty($planetOut)) {
            return explode(',', str_replace(' ', '', $planetOut[0]));
        }
    }

    //直接拿数据
    public function calculatePlanet($conditions, $starsCode, $type = true)
    {
        $planetOut = $this->SweTest($conditions, $starsCode);

        $house_one  = $this->house_one_index($starsCode);

        foreach ($planetOut as $keyCode => $lineCode) {
            $planetinfo = explode(',', str_replace(' ', '', $lineCode));

            if ($planetinfo[0] == 'house1') {
                $house_one = $keyCode;
            }
            if ($house_one <= $keyCode and !$type) {

                $data[] = $lineCode;

            } else if ($type and $house_one > $keyCode) {
                $data[] = $lineCode;
            }

        }
        return $data;
    }

    //福点计算
    function calculatepFortune($planet_data, $conditions)
    {

        if (empty($planet_data['Sun']) or empty($planet_data['Moon']) or empty($planet_data['house1']) or empty($planet_data['house7'])) {

            return false;
        }
        $Sum = $planet_data['Sun'][1];
        $Moon = $planet_data['Moon'][1];
        $house1 = $planet_data['house1'][1];
        $house7 = $planet_data['house7'][1];

        if ($house1 > $house7) {
            if ($Sum <= $house1 And $Sum > $house7) {
                $day_chart = True;
            } else {
                $day_chart = False;
            }
        } else {
            if ($Sum > $house1 And $Sum <= $house7) {
                $day_chart = False;
            } else {
                $day_chart = True;
            }
        }
        if ($day_chart == True) {
            $Fortune_longitude = $house1 + $Moon - $Sum;
        } else {
            $Fortune_longitude = $house1 - $Moon + $Sum;
        }

        if ($Fortune_longitude >= 360) {
            $Fortune_longitude = $Fortune_longitude - 360;
        }

        if ($Fortune_longitude < 0) {
            $Fortune_longitude = $Fortune_longitude + 360;
        }
        $Fortune_longitude = $this->crunch($Fortune_longitude);
        $planet_info[0] = "PartOfFortune";
        $planet_info[1] = $Fortune_longitude;
        $planet_info[2] = "360";


        return $planet_info;
    }

    //上升

    function calculate10($planet_data, $conditions)
    {
        if (empty($planet_data['Ascendant'])) {
            return false;
        }
        $planet_info = $planet_data['Ascendant'];

        return $planet_info;
    }

    //中天
    function calculate11($planet_data, $conditions)
    {
        if (empty($planet_data['MC'])) {
            return false;
        }
        $planet_info = $planet_data['MC'];

        return $planet_info;
    }

    //宿命点

    function calculate13($planet_data, $conditions)
    {
        if (empty($planet_data['Vertex'])) {
            return false;
        }
        $planet_info = $planet_data['Vertex'];

        return $planet_info;
    }

    //东升点
    function calculate14($planet_data, $conditions)
    {
        if (empty($planet_data['equat.Asc.'])) {
            return false;
        }
        $planet_info = $planet_data['equat.Asc.'];
        $planet_info[0] = 'equatAsc';
        return $planet_info;
    }

    //下降
    function calculate18($planet_data, $conditions)
    {
        if (empty($planet_data['house7'])) {
            return false;
        }
        $planet_info = $planet_data['house7'];
        $planet_info[0] = 'Des';
        $planet_info[2] = 360;
        $planet_info[3] = '7';
        return $planet_info;
    }

    //天底
    function calculate19($planet_data, $conditions)
    {
        if (empty($planet_data['MC'])) {
            return false;
        } else {
            $true_average = $planet_data['MC'][1] + 180;
        }
        $true_average = $this->crunch($true_average);

        $planet_info[0] = 'IC';
        $planet_info[1] = $true_average;
        $planet_info[2] = 360;
        $planet_info[3] = 4;
        return $planet_info;
    }

    //日月中
    function calculate20($planet_data, $conditions)
    {
        if (empty($planet_data['Sun']) or empty($planet_data['Moon'])) {
            return false;
        }
        $Sum = (float)$planet_data['Sun'][1];
        $Moon = (float)$planet_data['Moon'][1];

        $true_average = ($Sum + $Moon) / 2;

        $diff = abs($Sum - $Moon);

        if ($diff >= 180 And Abs($true_average - $Sum) > 90 And Abs($true_average - $Moon) > 90) {
            $true_average = $true_average + 180;
        }

        $true_average = $this->crunch($true_average);

        $planet_info[0] = 'Sun-Moon';
        $planet_info[1] = $true_average;
        $planet_info[2] = 360;
        return $planet_info;
    }

    //南交
    function calculate21($planet_data, $conditions)
    {
        if (empty($planet_data['meanNode'])) {
            $conditions['p'] = 'm';
            $planetOut = $this->SweTest($conditions);
            $lineInfo = explode(',', str_replace(' ', '', $planetOut[0]));

            $true_average = $lineInfo[1] + 180;
            $span_s=$lineInfo[2];
        } else {

            $true_average = $planet_data['meanNode'][1] + 180;
            $span_s=$planet_data['meanNode'][2];
        }

        $true_average = $this->crunch($true_average);
        $planet_info[0] = 'meanSouthNode';
        $planet_info[1] = $true_average;
        $planet_info[2] = $span_s;
        return $planet_info;

    }

    //计算落入星座度分秒
    function Convert_sign_deg_min($longitude)
    {
        $longitude = $this->crunch($longitude);
        $sign_num = intval($longitude / 30);
        $pos_in_sign = $longitude - ($sign_num * 30);
        $deg = intval($pos_in_sign);
        $full_min = ($pos_in_sign - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec];
    }

    //获取度分秒
    function Convert_deg_min($longitude)
    {
        $longitude = $this->crunch($longitude);
        $deg = intval($longitude);
        $full_min = ($longitude - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec];
    }

    //推运
    function transitsProgressed($arr, $Progressed, $current_cycle)
    {

        $planetOut = $this->SweTest($arr);

        $minDell = 99999;

        $newMoon = '';

        foreach ($planetOut as $key => $line) {
            if ($key == (count($planetOut) - 1)) {
                break;
            }

            $lineInfo = explode(',', $line);
            $defug = trim($lineInfo[2], ' ');

            $lineInfo_last = explode(',', $planetOut[$key + 1]);
            $defug_last = trim($lineInfo_last[2], ' ');

            $lineInfo_cha = abs($defug - $defug_last);

            if ($defug > $defug_last) {
                if ($lineInfo_cha > 180) {
                    if ($defug <= $Progressed or $defug_last > $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                } else {
                    if ($defug >= $Progressed and $defug_last < $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                }
            }else {
                if ($lineInfo_cha > 180) {
                    if ($defug <= $Progressed or $defug_last > $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                }else {
                    if ($defug <= $Progressed and $defug_last > $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                }
            }
            //如果没有找到，计算接近那个必须是
//            $Progressed_defug = $Progressed - abs($defug);
//            if ($minDell > abs($Progressed_defug)) {
//
//                $minDell = abs($Progressed_defug);
//                $newTime = str_replace('UT', '', $lineInfo[1]);
//            }
        }
        if (!empty($newMoon)) {
            $newMoon_time = strtotime($newMoon);
            $arr['b'] = date('d.m.Y', $newMoon_time);
        }else{
            $current_cycle = '';
            $newMoon_time = '';
        }

        if ($current_cycle == 'd') {
            $arr['ut'] = '0:00:0';
            $arr['n'] = '25';
            $arr['s'] = '60m';
            $current_cycle = 'h';
        } else if ($current_cycle == 'h') {
            $arr['ut'] = date('H', $newMoon_time) . ':00:00';
            $arr['n'] = '61';
            $arr['s'] = '1m';
            $current_cycle = 'm';
        } else if ($current_cycle == 'm') {
            $arr['ut'] = date('H:i', $newMoon_time) . ':00';
            $arr['n'] = '61';
            $arr['s'] = '1s';
            $current_cycle = 's';
        } else {
            $current_cycle = '';
        }

        if ($current_cycle != '') {

            return $this->transitsProgressed($arr, $Progressed, $current_cycle);

        }
        return $newMoon_time;

    }
    ////万能计算点数数据
    function universalProgressed($arr, $P_name, $Progressed, $current_cycle)
    {
        $planetOut = $this->SweTest($arr);

        $minDell = 99999;
        $newTime = '';

        foreach ($planetOut as $key => $line) {
            $lineInfo = explode(',', str_replace(' ', '', $line));

            if ($P_name == $lineInfo[0]) {
                $defug = $lineInfo[2];

                $Progressed_defug = $Progressed - abs($defug);

                if ($minDell > abs($Progressed_defug)) {

                    $minDell = abs($Progressed_defug);
                    $newTime = str_replace('UT', '', $lineInfo[1]);
                }
            }
        }

        $newMoon_time = strtotime($newTime);

        $arr['b'] = date('d.m.Y', $newMoon_time);

        if ($current_cycle == 'd') {
            $arr['ut'] = '0:30:0';
            $arr['n'] = '24';
            $arr['s'] = '60m';
            $current_cycle = 'h';
        } else if ($current_cycle == 'h') {
            $arr['ut'] = date('H', $newMoon_time) . ':00:30';
            $arr['n'] = '60';
            $arr['s'] = '1m';
            $current_cycle = 'm';
        } else if ($current_cycle == 'm') {
            $arr['ut'] = date('H:i', $newMoon_time) . ':00';
            $arr['n'] = '60';
            $arr['s'] = '1s';
            $current_cycle = 's';
        } else {
            $current_cycle = '';
        }
        if ($current_cycle != '') {
            return $this->universalProgressed($arr, $P_name, $Progressed, $current_cycle);
        }
        return $newMoon_time;
    }

}
 