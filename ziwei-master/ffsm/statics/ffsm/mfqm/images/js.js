
// Copyright 2012 Google Inc. All rights reserved.
(function(){

var data = {
"resource": {
  "version":"1",
  "macros":[],
  "tags":[],
  "predicates":[],
  "rules":[]
},
"runtime":[
[],[]
]};

var aa=function(a,b){function c(){}c.prototype=b.prototype;a.pe=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ae=function(a,c,f){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];return b.prototype[c].apply(a,d)}};var g=function(a,b){this.s=a;this.Pc=b};g.prototype.bd=function(){return this.s};g.prototype.getType=g.prototype.bd;g.prototype.getData=function(){return this.Pc};g.prototype.getData=g.prototype.getData;var ba=function(a){return"number"===typeof a&&0<=a&&isFinite(a)&&0===a%1||"string"===typeof a&&"-"!==a[0]&&a===""+parseInt(a,10)},ca=function(){this.Z={};this.oa=!1};ca.prototype.get=function(a){return this.Z["dust."+a]};ca.prototype.set=function(a,b){!this.oa&&(this.Z["dust."+a]=b)};ca.prototype.has=function(a){return this.Z.hasOwnProperty("dust."+a)};var da=function(a){var b=[],c;for(c in a.Z)a.Z.hasOwnProperty(c)&&b.push(c.substr(5));return b};
ca.prototype.remove=function(a){!this.oa&&delete this.Z["dust."+a]};ca.prototype.D=function(){this.oa=!0};var t=function(a){this.ca=new ca;this.h=[];a=a||[];for(var b in a)a.hasOwnProperty(b)&&(ba(b)?this.h[Number(b)]=a[Number(b)]:this.ca.set(b,a[b]))};t.prototype.toString=function(){for(var a=[],b=0;b<this.h.length;b++){var c=this.h[b];null===c||void 0===c?a.push(""):a.push(c.toString())}return a.join(",")};t.prototype.set=function(a,b){if("length"==a){if(!ba(b))throw"RangeError: Length property must be a valid integer.";this.h.length=Number(b)}else ba(a)?this.h[Number(a)]=b:this.ca.set(a,b)};
t.prototype.set=t.prototype.set;t.prototype.get=function(a){return"length"==a?this.length():ba(a)?this.h[Number(a)]:this.ca.get(a)};t.prototype.get=t.prototype.get;t.prototype.length=function(){return this.h.length};t.prototype.L=function(){for(var a=da(this.ca),b=0;b<this.h.length;b++)a.push(b+"");return new t(a)};t.prototype.getKeys=t.prototype.L;t.prototype.remove=function(a){ba(a)?delete this.h[Number(a)]:this.ca.remove(a)};t.prototype.remove=t.prototype.remove;t.prototype.pop=function(){return this.h.pop()};
t.prototype.pop=t.prototype.pop;t.prototype.push=function(a){return this.h.push.apply(this.h,Array.prototype.slice.call(arguments))};t.prototype.push=t.prototype.push;t.prototype.shift=function(){return this.h.shift()};t.prototype.shift=t.prototype.shift;t.prototype.splice=function(a,b,c){return new t(this.h.splice.apply(this.h,arguments))};t.prototype.splice=t.prototype.splice;t.prototype.unshift=function(a){return this.h.unshift.apply(this.h,Array.prototype.slice.call(arguments))};
t.prototype.unshift=t.prototype.unshift;t.prototype.has=function(a){return ba(a)&&this.h.hasOwnProperty(a)||this.ca.has(a)};var ea=function(){function a(a,b){c[a]=b}function b(){c={}}var c={},d={add:a,reset:b,create:function(d){var e={add:a,request:function(a,b){return c[a]?c[a].apply(d,Array.prototype.slice.call(arguments,1)):!1},reset:b};e.add=e.add;e.request=e.request;e.reset=e.reset;return e}};d.add=d.add;d.reset=d.reset;return d};var fa=function(){function a(a,c){if(b[a]){if(b[a].Da+c>b[a].max)throw Error("Quota exceeded");b[a].Da+=c}}var b={},c=void 0,d=void 0,e={xd:function(a){c=a},Eb:function(){c&&a(c,1)},yd:function(a){d=a},O:function(b){d&&a(d,b)},Nd:function(a,c){b[a]=b[a]||{Da:0};b[a].max=c},ad:function(a){return b[a]&&b[a].Da||0},reset:function(){b={}},Jc:a};e.onFnConsume=e.xd;e.consumeFn=e.Eb;e.onStorageConsume=e.yd;e.consumeStorage=e.O;e.setMax=e.Nd;e.getConsumed=e.ad;e.reset=e.reset;e.consume=e.Jc;return e};var ha=function(a,b,c){this.F=a;this.T=b;this.S=c;this.h=new ca};ha.prototype.add=function(a,b){this.h.oa||(this.F.O(("string"===typeof a?a.length:1)+("string"===typeof b?b.length:1)),this.h.set(a,b))};ha.prototype.add=ha.prototype.add;ha.prototype.set=function(a,b){this.h.oa||(this.S&&this.S.has(a)?this.S.set(a,b):(this.F.O(("string"===typeof a?a.length:1)+("string"===typeof b?b.length:1)),this.h.set(a,b)))};ha.prototype.set=ha.prototype.set;
ha.prototype.get=function(a){return this.h.has(a)?this.h.get(a):this.S?this.S.get(a):void 0};ha.prototype.get=ha.prototype.get;ha.prototype.has=function(a){return!!this.h.has(a)||!(!this.S||!this.S.has(a))};ha.prototype.has=ha.prototype.has;ha.prototype.C=function(){return this.F};ha.prototype.D=function(){this.h.D()};var ia=function(a){return"[object Array]"==Object.prototype.toString.call(Object(a))},ja=function(a,b){if(Array.prototype.indexOf){var c=a.indexOf(b);return"number"==typeof c?c:-1}for(var d=0;d<a.length;d++)if(a[d]===b)return d;return-1};var v=function(a,b){ca.call(this);this.Qb=a;this.Zc=b};aa(v,ca);var la=function(a,b){for(var c,d=0;d<b.length&&!(c=ka(a,b[d]),c instanceof g);d++);return c},ka=function(a,b){var c=a.get(String(b[0]));if(!(c&&c instanceof v))throw"Attempting to execute non-function "+b[0]+".";return c.i.apply(c,[a].concat(b.slice(1)))};v.prototype.toString=function(){return this.Qb};v.prototype.getName=function(){return this.Qb};v.prototype.getName=v.prototype.getName;v.prototype.L=function(){return new t(da(this))};
v.prototype.getKeys=v.prototype.L;v.prototype.i=function(a,b){var c,d={A:function(){return a},evaluate:function(b){var c=a;return ia(b)?ka(c,b):b},ja:function(b){return la(a,b)},C:function(){return a.C()},ee:function(){c||(c=a.T.create(d));return c}};a.C().Eb();return this.Zc.apply(d,Array.prototype.slice.call(arguments,1))};v.prototype.invoke=v.prototype.i;var x=function(){ca.call(this)};aa(x,ca);x.prototype.L=function(){return new t(da(this))};x.prototype.getKeys=x.prototype.L;/*
 jQuery v1.9.1 (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license. */
var na=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,oa=function(a){if(null==a)return String(a);var b=na.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},pa=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},qa=function(a){if(!a||"object"!=oa(a)||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!pa(a,"constructor")&&!pa(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return void 0===
b||pa(a,b)},ra=function(a,b){var c=b||("array"==oa(a)?[]:{}),d;for(d in a)if(pa(a,d)){var e=a[d];"array"==oa(e)?("array"!=oa(c[d])&&(c[d]=[]),c[d]=ra(e,c[d])):qa(e)?(qa(c[d])||(c[d]={}),c[d]=ra(e,c[d])):c[d]=e}return c};var sa=function(a){if(a instanceof t){for(var b=[],c=a.length(),d=0;d<c;d++)a.has(d)&&(b[d]=sa(a.get(d)));return b}if(a instanceof x){for(var e={},f=a.L(),h=f.length(),k=0;k<h;k++)e[f.get(k)]=sa(a.get(f.get(k)));return e}return a instanceof v?function(){for(var b=Array.prototype.slice.call(arguments,0),c=0;c<b.length;c++)b[c]=ta(b[c]);var d=new ha(fa(),ea());return sa(a.i.apply(a,[d].concat(b)))}:a},ta=function(a){if(ia(a)){for(var b=[],c=0;c<a.length;c++)a.hasOwnProperty(c)&&(b[c]=ta(a[c]));return new t(b)}if(qa(a)){var d=
new x,e;for(e in a)a.hasOwnProperty(e)&&d.set(e,ta(a[e]));return d}if("function"===typeof a)return new v("",function(b){for(var c=Array.prototype.slice.call(arguments,0),d=0;d<c.length;d++)c[d]=sa(this.evaluate(c[d]));return ta(a.apply(a,c))});var f=typeof a;if(null===a||"string"===f||"number"===f||"boolean"===f)return a};var ua={control:function(a,b){return new g(a,this.evaluate(b))},fn:function(a,b,c){var d=this.A(),e=this.evaluate(b);if(!(e instanceof t))throw"Error: non-List value given for Fn argument names.";var f=Array.prototype.slice.call(arguments,2);this.C().O(a.length+f.length);return new v(a,function(){return function(a){for(var b=new ha(d.F,d.T,d),c=Array.prototype.slice.call(arguments,0),h=0;h<c.length;h++)if(c[h]=this.evaluate(c[h]),c[h]instanceof g)return c[h];for(var n=e.get("length"),p=0;p<n;p++)p<
c.length?b.set(e.get(p),c[p]):b.set(e.get(p),void 0);b.set("arguments",new t(c));var q=la(b,f);if(q instanceof g)return"return"===q.s?q.getData():q}}())},list:function(a){var b=this.C();b.O(arguments.length);for(var c=new t,d=0;d<arguments.length;d++){var e=this.evaluate(arguments[d]);"string"===typeof e&&b.O(e.length?e.length-1:0);c.push(e)}return c},map:function(a){for(var b=this.C(),c=new x,d=0;d<arguments.length-1;d+=2){var e=this.evaluate(arguments[d])+"",f=this.evaluate(arguments[d+1]),h=e.length;
h+="string"===typeof f?f.length:1;b.O(h);c.set(e,f)}return c},undefined:function(){}};var y=function(){this.F=fa();this.T=ea();this.la=new ha(this.F,this.T)};y.prototype.N=function(a,b){var c=new v(a,b);c.D();this.la.set(a,c)};y.prototype.addInstruction=y.prototype.N;y.prototype.Db=function(a,b){ua.hasOwnProperty(a)&&this.N(b||a,ua[a])};y.prototype.addNativeInstruction=y.prototype.Db;y.prototype.C=function(){return this.F};y.prototype.getQuota=y.prototype.C;y.prototype.Ka=function(){this.F=fa();this.la.F=this.F};y.prototype.resetQuota=y.prototype.Ka;
y.prototype.Jd=function(){this.T=ea();this.la.T=this.T};y.prototype.resetPermissions=y.prototype.Jd;y.prototype.I=function(a,b){var c=Array.prototype.slice.call(arguments,0);return this.ib(c)};y.prototype.execute=y.prototype.I;y.prototype.ib=function(a){for(var b,c=0;c<arguments.length;c++){var d=ka(this.la,arguments[c]);b=d instanceof g||d instanceof v||d instanceof t||d instanceof x||null===d||void 0===d||"string"===typeof d||"number"===typeof d||"boolean"===typeof d?d:void 0}return b};
y.prototype.run=y.prototype.ib;y.prototype.D=function(){this.la.D()};y.prototype.makeImmutable=y.prototype.D;var va=function(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var xa={Qd:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));for(d=1;d<arguments.length;d++)if(arguments[d]instanceof t)for(var e=arguments[d],f=0;f<e.length();f++)c.push(e.get(f));else c.push(arguments[d]);return new t(c)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&
!b.i(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.i(a,this.get(e),e,this)&&d.push(this.get(e));return new t(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.i(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=void 0===c?0:Number(c);0>e&&(e=Math.max(d+e,0));for(var f=e;f<d;f++)if(this.has(f)&&this.get(f)===
b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;void 0!==c&&(e=0>c?d+c:Math.min(c,e));for(var f=e;0<=f;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.i(a,this.get(e),e,this));return new t(d)},pop:function(){return this.pop()},push:function(a,b){return this.push.apply(this,Array.prototype.slice.call(arguments,
1))},reduce:function(a,b,c){var d=this.length(),e,f;if(void 0!==c)e=c,f=0;else{if(0==d)throw"TypeError: Reduce on List with no elements.";for(var h=0;h<d;h++)if(this.has(h)){e=this.get(h);f=h+1;break}if(h==d)throw"TypeError: Reduce on List with no elements.";}for(h=f;h<d;h++)this.has(h)&&(e=b.i(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f;if(void 0!==c)e=c,f=d-1;else{if(0==d)throw"TypeError: ReduceRight on List with no elements.";for(var h=1;h<=d;h++)if(this.has(d-
h)){e=this.get(d-h);f=d-(h+1);break}if(h>d)throw"TypeError: ReduceRight on List with no elements.";}for(h=f;0<=h;h--)this.has(h)&&(e=b.i(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=va(this),b=a.length-1,c=0;0<=b;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();void 0===b&&(b=0);b=0>b?Math.max(d+b,0):Math.min(b,d);c=void 0===c?d:0>c?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,
c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new t(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.i(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=va(this);void 0===b?c.sort():c.sort(function(c,d){return Number(b.i(a,c,d))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d)},splice:function(a,b,c,d){return this.splice.apply(this,Array.prototype.splice.call(arguments,1,arguments.length-1))},toString:function(){return this.toString()},
unshift:function(a,b){return this.unshift.apply(this,Array.prototype.slice.call(arguments,1))}};var z={Ob:{ADD:0,AND:1,APPLY:2,ASSIGN:3,BREAK:4,CASE:5,CONTINUE:6,CONTROL:49,CREATE_ARRAY:7,CREATE_OBJECT:8,DEFAULT:9,DEFN:50,DIVIDE:10,DO:11,EQUALS:12,EXPRESSION_LIST:13,FN:51,FOR:14,FOR_IN:47,GET:15,GET_CONTAINER_VARIABLE:48,GET_INDEX:16,GET_PROPERTY:17,GREATER_THAN:18,GREATER_THAN_EQUALS:19,IDENTITY_EQUALS:20,IDENTITY_NOT_EQUALS:21,IF:22,LESS_THAN:23,LESS_THAN_EQUALS:24,MODULUS:25,MULTIPLY:26,NEGATE:27,NOT:28,NOT_EQUALS:29,NULL:45,OR:30,PLUS_EQUALS:31,POST_DECREMENT:32,POST_INCREMENT:33,PRE_DECREMENT:34,
PRE_INCREMENT:35,QUOTE:46,RETURN:36,SET_PROPERTY:43,SUBTRACT:37,SWITCH:38,TERNARY:39,TYPEOF:40,UNDEFINED:44,VAR:41,WHILE:42}},ya="charAt concat indexOf lastIndexOf match replace search slice split substring toLowerCase toLocaleLowerCase toString toUpperCase toLocaleUpperCase trim".split(" "),za=new g("break"),Aa=new g("continue");z.add=function(a,b){return this.evaluate(a)+this.evaluate(b)};z.and=function(a,b){return this.evaluate(a)&&this.evaluate(b)};
z.apply=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);if(!(c instanceof t))throw"Error: Non-List argument given to Apply instruction.";if(null===a||void 0===a)throw"TypeError: Can't read property "+b+" of "+a+".";if("boolean"==typeof a||"number"==typeof a){if("toString"==b)return a.toString();throw"TypeError: "+a+"."+b+" is not a function.";}if("string"==typeof a){if(0<=ja(ya,b))return ta(a[b].apply(a,va(c)));throw"TypeError: "+b+" is not a function";}if(a instanceof t){if(a.has(b)){var d=
a.get(b);if(d instanceof v){var e=va(c);e.unshift(this.A());return d.i.apply(d,e)}throw"TypeError: "+b+" is not a function";}if(0<=ja(xa.Qd,b))return e=va(c),e.unshift(this.A()),xa[b].apply(a,e)}if(a instanceof v||a instanceof x){if(a.has(b)){d=a.get(b);if(d instanceof v)return e=va(c),e.unshift(this.A()),d.i.apply(d,e);throw"TypeError: "+b+" is not a function";}if("toString"==b)return a instanceof v?a.getName():a.toString();if("hasOwnProperty"==b)return a.has.apply(a,va(c))}throw"TypeError: Object has no '"+
b+"' property.";};z.assign=function(a,b){a=this.evaluate(a);if("string"!=typeof a)throw"Invalid key name given for assignment.";var c=this.A();if(!c.has(a))throw"Attempting to assign to undefined value "+b;var d=this.evaluate(b);c.set(a,d);return d};z["break"]=function(){return za};z["case"]=function(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof g)return d}};z["continue"]=function(){return Aa};
z.Qc=function(a,b,c){var d=new t;b=this.evaluate(b);for(var e=0;e<b.length;e++)d.push(b[e]);var f=[z.Ob.FN,a,d].concat(Array.prototype.splice.call(arguments,2,arguments.length-2));this.A().set(a,this.evaluate(f))};z.Tc=function(a,b){return this.evaluate(a)/this.evaluate(b)};z.Vc=function(a,b){return this.evaluate(a)==this.evaluate(b)};z.Xc=function(a){for(var b,c=0;c<arguments.length;c++)b=this.evaluate(arguments[c]);return b};
z.$c=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);var d=this.A();if("string"==typeof b)for(var e=0;e<b.length;e++){d.set(a,e);var f=this.ja(c);if(f instanceof g){if("break"==f.s)break;if("return"==f.s)return f}}else if(b instanceof x||b instanceof t||b instanceof v){var h=b.L(),k=h.length();for(e=0;e<k;e++)if(d.set(a,h.get(e)),f=this.ja(c),f instanceof g){if("break"==f.s)break;if("return"==f.s)return f}}};z.get=function(a){return this.A().get(this.evaluate(a))};
z.Mb=function(a,b){var c;a=this.evaluate(a);b=this.evaluate(b);if(void 0===a||null===a)throw"TypeError: cannot access property of "+a+".";a instanceof x||a instanceof t||a instanceof v?c=a.get(b):"string"==typeof a&&("length"==b?c=a.length:ba(b)&&(c=a[b]));return c};z.cd=function(a,b){return this.evaluate(a)>this.evaluate(b)};z.dd=function(a,b){return this.evaluate(a)>=this.evaluate(b)};z.hd=function(a,b){return this.evaluate(a)===this.evaluate(b)};z.jd=function(a,b){return this.evaluate(a)!==this.evaluate(b)};
z["if"]=function(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=this.ja(d);if(e instanceof g)return e};z.qd=function(a,b){return this.evaluate(a)<this.evaluate(b)};z.rd=function(a,b){return this.evaluate(a)<=this.evaluate(b)};z.sd=function(a,b){return this.evaluate(a)%this.evaluate(b)};z.multiply=function(a,b){return this.evaluate(a)*this.evaluate(b)};z.td=function(a){return-this.evaluate(a)};z.ud=function(a){return!this.evaluate(a)};
z.vd=function(a,b){return this.evaluate(a)!=this.evaluate(b)};z["null"]=function(){return null};z.or=function(a,b){return this.evaluate(a)||this.evaluate(b)};z.Wb=function(a,b){var c=this.evaluate(a);this.evaluate(b);return c};z.Xb=function(a){return this.evaluate(a)};z.quote=function(a){return Array.prototype.slice.apply(arguments)};z["return"]=function(a){return new g("return",this.evaluate(a))};
z.setProperty=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);if(null===a||void 0===a)throw"TypeError: Can't set property "+b+" of "+a+".";(a instanceof v||a instanceof t||a instanceof x)&&a.set(b,c);return c};z.Pd=function(a,b){return this.evaluate(a)-this.evaluate(b)};
z["switch"]=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);if(!ia(b)||!ia(c))throw"Error: Malformed switch instruction.";for(var d,e=!1,f=0;f<b.length;f++)if(e||a===this.evaluate(b[f]))if(d=this.evaluate(c[f]),d instanceof g){var h=d.s;if("break"==h)return;if("return"==h||"continue"==h)return d}else e=!0;if(c.length==b.length+1&&(d=this.evaluate(c[c.length-1]),d instanceof g&&("return"==d.s||"continue"==d.s)))return d};
z.Rd=function(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)};z["typeof"]=function(a){a=this.evaluate(a);return a instanceof v?"function":typeof a};z.undefined=function(){};z["var"]=function(a){for(var b=this.A(),c=0;c<arguments.length;c++){var d=arguments[c];"string"!=typeof d||b.add(d,void 0)}};
z["while"]=function(a,b,c,d){var e,f=this.evaluate(d);if(this.evaluate(c)&&(e=this.ja(f),e instanceof g)){if("break"==e.s)return;if("return"==e.s)return e}for(;this.evaluate(a);){e=this.ja(f);if(e instanceof g){if("break"==e.s)break;if("return"==e.s)return e}this.evaluate(b)}};var Ca=function(){this.Nb=!1;this.P=new y;Ba(this);this.Nb=!0};Ca.prototype.od=function(){return this.Nb};Ca.prototype.isInitialized=Ca.prototype.od;Ca.prototype.I=function(a){return this.P.ib(a)};Ca.prototype.execute=Ca.prototype.I;Ca.prototype.D=function(){this.P.D()};Ca.prototype.makeImmutable=Ca.prototype.D;
var Ba=function(a){function b(a,b){e.P.Db(a,String(b))}function c(a,b){e.P.N(String(d[a]),b)}var d=z.Ob,e=a;b("control",d.CONTROL);b("fn",d.FN);b("list",d.CREATE_ARRAY);b("map",d.CREATE_OBJECT);b("undefined",d.UNDEFINED);c("ADD",z.add);c("AND",z.and);c("APPLY",z.apply);c("ASSIGN",z.assign);c("BREAK",z["break"]);c("CASE",z["case"]);c("CONTINUE",z["continue"]);c("DEFAULT",z["case"]);c("DEFN",z.Qc);c("DIVIDE",z.Tc);c("EQUALS",z.Vc);c("EXPRESSION_LIST",z.Xc);c("FOR_IN",z.$c);c("GET",z.get);c("GET_INDEX",
z.Mb);c("GET_PROPERTY",z.Mb);c("GREATER_THAN",z.cd);c("GREATER_THAN_EQUALS",z.dd);c("IDENTITY_EQUALS",z.hd);c("IDENTITY_NOT_EQUALS",z.jd);c("IF",z["if"]);c("LESS_THAN",z.qd);c("LESS_THAN_EQUALS",z.rd);c("MODULUS",z.sd);c("MULTIPLY",z.multiply);c("NEGATE",z.td);c("NOT",z.ud);c("NOT_EQUALS",z.vd);c("NULL",z["null"]);c("OR",z.or);c("POST_DECREMENT",z.Wb);c("POST_INCREMENT",z.Wb);c("PRE_DECREMENT",z.Xb);c("PRE_INCREMENT",z.Xb);c("QUOTE",z.quote);c("RETURN",z["return"]);c("SET_PROPERTY",z.setProperty);
c("SUBTRACT",z.Pd);c("SWITCH",z["switch"]);c("TERNARY",z.Rd);c("TYPEOF",z["typeof"]);c("VAR",z["var"]);c("WHILE",z["while"])};Ca.prototype.N=function(a,b){this.P.N(a,b)};Ca.prototype.addInstruction=Ca.prototype.N;Ca.prototype.C=function(){return this.P.C()};Ca.prototype.getQuota=Ca.prototype.C;Ca.prototype.Ka=function(){this.P.Ka()};Ca.prototype.resetQuota=Ca.prototype.Ka;var Da=function(){this.Ha={}};Da.prototype.get=function(a){return this.Ha.hasOwnProperty(a)?this.Ha[a]:void 0};Da.prototype.add=function(a,b){if(this.Ha.hasOwnProperty(a))throw"Attempting to add a function which already exists: "+a+".";var c=new v(a,function(){for(var a=Array.prototype.slice.call(arguments,0),c=0;c<a.length;c++)a[c]=this.evaluate(a[c]);return b.apply(this,a)});c.D();this.Ha[a]=c};Da.prototype.addAll=function(a){for(var b in a)a.hasOwnProperty(b)&&this.add(b,a[b])};var B=window,C=document,Ea=function(a,b){var c=B[a];B[a]=void 0===c?b:c;return B[a]},Fa=function(a,b){b&&(a.addEventListener?a.onload=b:a.onreadystatechange=function(){a.readyState in{loaded:1,complete:1}&&(a.onreadystatechange=null,b())})},F=function(a,b,c){var d=C.createElement("script");d.type="text/javascript";d.async=!0;d.src=a;Fa(d,b);c&&(d.onerror=c);var e=C.getElementsByTagName("script")[0]||C.body||C.head;e.parentNode.insertBefore(d,e);return d},Ga=function(a,b){var c=C.createElement("iframe");
c.height="0";c.width="0";c.style.display="none";c.style.visibility="hidden";var d=C.body&&C.body.lastChild||C.body||C.head;d.parentNode.insertBefore(c,d);Fa(c,b);void 0!==a&&(c.src=a);return c},J=function(a,b,c){var d=new Image(1,1);d.onload=function(){d.onload=null;b&&b()};d.onerror=function(){d.onerror=null;c&&c()};d.src=a},Ha=function(a,b,c,d){a.addEventListener?a.addEventListener(b,c,!!d):a.attachEvent&&a.attachEvent("on"+b,c)},Ia=function(a,b,c){a.removeEventListener?a.removeEventListener(b,
c,!1):a.detachEvent&&a.detachEvent("on"+b,c)},O=function(a){B.setTimeout(a,0)},Ka=function(a){var b=C.getElementById(a);if(b&&Ja(b,"id")!=a)for(var c=1;c<document.all[a].length;c++)if(Ja(document.all[a][c],"id")==a)return document.all[a][c];return b},Ja=function(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null},La=function(a){var b=a.innerText||a.textContent||"";b&&" "!=b&&(b=b.replace(/^[\s\xa0]+|[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b},
Ma=function(a){var b=C.createElement("div");b.innerHTML="A<div>"+a+"</div>";b=b.lastChild;for(var c=[];b.firstChild;)c.push(b.removeChild(b.firstChild));return c};var Na=function(a,b){for(var c=a.split("&"),d=0;d<c.length;d++){var e=c[d].split("=");if(decodeURIComponent(e[0]).replace(/\+/g," ")==b)return decodeURIComponent(e.slice(1).join("=")).replace(/\+/g," ")}},P=function(a,b,c,d,e){var f,h=a.protocol||B.location.protocol;h=h.replace(":","").toLowerCase();b&&(b=String(b).toLowerCase());switch(b){case "protocol":f=h;break;case "host":f=(a.hostname||B.location.hostname).split(":")[0].toLowerCase();if(c){var k=/^www\d*\./.exec(f);k&&k[0]&&(f=f.substr(k[0].length))}break;
case "port":f=String(1*(a.hostname?a.port:B.location.port)||("http"==h?80:"https"==h?443:""));break;case "path":f="/"==a.pathname.substr(0,1)?a.pathname:"/"+a.pathname;var l=f.split("/");0<=ja(d||[],l[l.length-1])&&(l[l.length-1]="");f=l.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Na(f,e));break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f},Oa=function(a){var b="";a&&a.href&&(b=a.hash?a.href.replace(a.hash,""):a.href);return b},R=function(a){var b=
C.createElement("a");a&&(b.href=a);return b};var Ra=function(){this.Vb=new Ca;var a=new Da;a.addAll(Pa());Qa(this,function(b){return a.get(b)})},Pa=function(){return{callInWindow:Sa,getCurrentUrl:Ta,getInWindow:Ua,getReferrer:Va,getUrlComponent:Wa,getUrlFragment:Xa,isPlainObject:Ya,loadIframe:Za,loadJavaScript:ab,removeUrlFragment:bb,replaceAll:cb,sendTrackingBeacon:db,setInWindow:eb}};Ra.prototype.I=function(a){return this.Vb.I(a)};Ra.prototype.execute=Ra.prototype.I;var Qa=function(a,b){a.Vb.N("require",b)};
function Sa(a,b){for(var c=a.split("."),d=B,e=d[c[0]],f=1;e&&f<c.length;f++)d=e,e=e[c[f]];if("function"==oa(e)){var h=[];for(f=1;f<arguments.length;f++)h.push(sa(arguments[f]));e.apply(d,h)}}function Ta(){return B.location.href}function Ua(a,b,c){for(var d=a.split("."),e=B,f=0;f<d.length-1;f++)if(e=e[d[f]],void 0===e||null===e)return;b&&(void 0===e[d[f]]||c&&!e[d[f]])&&(e[d[f]]=sa(b));return ta(e[d[f]])}function Va(){return C.referrer}
function Wa(a,b,c,d,e){var f;if(d&&d instanceof t){f=[];for(var h=0;h<d.length();h++){var k=d.get(h);"string"==typeof k&&f.push(k)}}return P(R(a),b,c,f,e)}function Xa(a){return P(R(a),"fragment")}function Ya(a){return a instanceof x}function Za(a,b){var c=this.A();Ga(a,function(){b instanceof v&&b.i(c)})}var hb={};
function ab(a,b,c,d){var e=this.A(),f=function(){b instanceof v&&b.i(e)},h=function(){c instanceof v&&c.i(e)};d?hb[d]?(hb[d].onSuccess.push(f),hb[d].onFailure.push(h)):(hb[d]={onSuccess:[f],onFailure:[h]},f=function(){for(var a=hb[d].onSuccess,b=0;b<a.length;b++)O(a[b]);a.push=function(a){O(a);return 0}},h=function(){for(var a=hb[d].onFailure,b=0;b<a.length;b++)O(a[b]);hb[d]=null},F(a,f,h)):F(a,f,h)}function bb(a){return Oa(R(a))}function cb(a,b,c){return a.replace(new RegExp(b,"g"),c)}
function db(a,b,c){var d=this.A();J(a,function(){b instanceof v&&b.i(d)},function(){c instanceof v&&c.i(d)})}function eb(a,b,c){for(var d=a.split("."),e=B,f=0;f<d.length-1;f++)if(e=e[d[f]],void 0===e)return!1;return void 0===e[d[f]]||c?(e[d[f]]=sa(b),!0):!1};var Eb,Fb=[],Gb=[],Hb=[],Ib=[],Jb=[],Kb={},Lb,Mb,Nb,Sb=function(a){var b=a["function"];if(!b)throw"Error: No function name given for function call.";if(Kb[b]){var c={},d;for(d in a)a.hasOwnProperty(d)&&0===d.indexOf("vtp_")&&(c[d]=a[d]);return Kb[b](c)}var e=new x,f;for(f in a)a.hasOwnProperty(f)&&0===f.indexOf("vtp_")&&e.set(f.substr(4),ta(a[f]));var h=Eb([b,e]);h instanceof g&&"return"===h.s&&(h=h.getData());return sa(h)},Ub=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=
Tb(a[e],b,c));return d},Tb=function(a,b,c){if(ia(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Tb(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var h=Fb[f];if(!h||b(h))return;c[f]=!0;try{var k=Ub(h,b,c);d=Sb(k);Nb&&(d=Nb.Lc(d,k))}catch(w){d=!1}c[f]=!1;return d;case "map":d={};for(var l=1;l<a.length;l+=2)d[Tb(a[l],b,c)]=Tb(a[l+1],b,c);return d;case "template":d=[];for(var m=!1,n=1;n<a.length;n++){var p=Tb(a[n],b,c);Mb&&(m=m||p===
Mb.xa);d.push(p)}return Mb&&m?Mb.Mc(d):d.join("");case "escape":d=Tb(a[1],b,c);if(Mb&&ia(a[1])&&"macro"===a[1][0]&&Mb.pd(a))return Mb.Bd(d);d=String(d);for(var q=2;q<a.length;q++)ib[a[q]]&&(d=ib[a[q]](d));return d;case "tag":var u=a[1];if(!Ib[u])throw Error("Unable to resolve tag reference "+u+".");return d={Jb:a[2],index:u};case "zb":var r=Vb({"function":a[1],arg0:a[2],arg1:a[3],ignore_case:a[5]},b,c);a[4]&&(r=!r);return r;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");
}}return a},Vb=function(a,b,c){try{return Lb(Ub(a,b,c))}catch(d){JSON.stringify(a)}return null};var Wb=null,Zb=function(a){function b(a){for(var b=0;b<a.length;b++)d[a[b]]=!0}var c=[],d=[];Wb=Xb(a);for(var e=0;e<Gb.length;e++){var f=Gb[e],h=Yb(f);if(h){for(var k=f.add||[],l=0;l<k.length;l++)c[k[l]]=!0;b(f.block||[])}else null===h&&b(f.block||[])}var m=[];for(e=0;e<Ib.length;e++)c[e]&&!d[e]&&(m[e]=!0);return m},Yb=function(a){for(var b=a["if"]||[],c=0;c<b.length;c++){var d=Wb(b[c]);if(!d)return null===d?null:!1}var e=a.unless||[];for(c=0;c<e.length;c++){d=Wb(e[c]);if(null===d)return null;if(d)return!1}return!0};
var Xb=function(a){var b=[];return function(c){void 0===b[c]&&(b[c]=Vb(Hb[c],a));return b[c]}};/*
 Copyright (c) 2014 Derek Brans, MIT license https://github.com/krux/postscribe/blob/master/LICENSE. Portions derived from simplehtmlparser, which is licensed under the Apache License, Version 2.0 */
var bc={},cc=null;bc.m="UA-117273948-2";var dc=null,ec="//www.googletagmanager.com/a?id="+bc.m+"&cv=1",fc={},gc={};var hc=function(){},ic=function(a){return"function"==typeof a},jc=function(a){return"string"==oa(a)},kc=function(a){return"number"==oa(a)&&!isNaN(a)},lc=function(a){return Math.round(Number(a))||0},mc=function(a){return"false"==String(a).toLowerCase()?!1:!!a},nc=function(a){var b=[];if(ia(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b},oc=function(a){return a?a.replace(/^\s+|\s+$/g,""):""},pc=function(a,b){if(!kc(a)||!kc(b)||a>b)a=0,b=**********;return Math.floor(Math.random()*(b-a+1)+
a)},qc=function(){this.prefix="gtm.";this.values={}};qc.prototype.set=function(a,b){this.values[this.prefix+a]=b};qc.prototype.get=function(a){return this.values[this.prefix+a]};qc.prototype.contains=function(a){return void 0!==this.get(a)};var rc=function(){var a=cc.sequence||0;cc.sequence=a+1;return a},sc=function(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c},tc=function(a){var b=!1;return function(){if(!b)try{a()}catch(c){}b=!0}};var T=function(){var a=function(a){return{toString:function(){return a}}};return{sb:a("convert_case_to"),tb:a("convert_false_to"),ub:a("convert_null_to"),vb:a("convert_true_to"),wb:a("convert_undefined_to"),G:a("function"),cc:a("instance_name"),ec:a("live_only"),fc:a("malware_disabled"),gc:a("once_per_event"),yb:a("once_per_load"),zb:a("setup_tags"),hc:a("tag_id"),Ab:a("teardown_tags")}}();var uc=new qc,vc={},yc={set:function(a,b){ra(wc(a,b),vc)},get:function(a){return xc(a,2)},reset:function(){uc=new qc;vc={}}},xc=function(a,b){return 2!=b?uc.get(a):zc(a)},zc=function(a,b,c){var d=a.split(".");var e=function(a,b){for(var c=0;void 0!==a&&c<d.length;c++){if(null===a)return!1;a=a[d[c]]}return void 0!==a||1<c?a:b.length?e(Ac(b.pop()),b):Bc(d)};return e(vc.eventModel,[b,c]);return Bc(d)},Bc=function(a){for(var b=vc,c=0;c<a.length;c++){if(null===
b)return!1;if(void 0===b)break;b=b[a[c]]}return b};var Ac=function(a){if(a){var b=Bc(["gtag","targets",a]);return qa(b)?b:void 0}},Ec=function(a,b){function c(a){if(a)for(var b in a)a.hasOwnProperty(b)&&(d[b]=null)}var d={};c(vc);delete d.eventModel;c(Ac(a));c(Ac(b));c(vc.eventModel);var e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(f);return e};
var Fc=function(a,b){uc.set(a,b);ra(wc(a,b),vc)},wc=function(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c};var Gc=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),Hc={customPixels:["nonGooglePixels"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},Ic={customPixels:["customScripts","html"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels",
"customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},Jc=function(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c};
var Kc=function(a){var b=xc("gtm.whitelist");b="gtagua gtagaw gtagfl e v oid op cn css ew eq ge gt lc le lt re sw um".split(" ");var c=b&&Jc(nc(b),Hc),d=xc("gtm.blacklist")||
xc("tagTypeBlacklist")||[];Gc.test(B.location&&B.location.hostname)&&(d=nc(d),d.push("nonGooglePixels","nonGoogleScripts"));var e=d&&Jc(nc(d),Ic),f={};return function(h){var k=h&&h[T.G];if(!k||"string"!=typeof k)return!0;k=k.replace(/_/g,"");if(void 0!==f[k])return f[k];var l=gc[k]||[],m=a(k);if(b){var n;if(n=m)a:{if(0>ja(c,k))if(l&&0<l.length)for(var p=0;p<l.length;p++){if(0>
ja(c,l[p])){n=!1;break a}}else{n=!1;break a}n=!0}m=n}var q=!1;if(d){var u;if(!(u=0<=ja(e,k)))a:{for(var r=l||[],w=new qc,E=0;E<e.length;E++)w.set(e[E],!0);for(E=0;E<r.length;E++)if(w.get(r[E])){u=!0;break a}u=!1}q=u}return f[k]=!m||q}};var Lc={Lc:function(a,b){b[T.sb]&&"string"===typeof a&&(a=1==b[T.sb]?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(T.ub)&&null===a&&(a=b[T.ub]);b.hasOwnProperty(T.wb)&&void 0===a&&(a=b[T.wb]);b.hasOwnProperty(T.vb)&&!0===a&&(a=b[T.vb]);b.hasOwnProperty(T.tb)&&!1===a&&(a=b[T.tb]);return a}};var Mc=function(a){var b=cc.zones;!b&&a&&(b=cc.zones=a());return b},Nc={active:!0,isWhitelisted:function(){return!0}};var Oc=!1,Pc=0,Qc=[];function Rc(a){if(!Oc){var b=C.createEventObject,c="complete"==C.readyState,d="interactive"==C.readyState;if(!a||"readystatechange"!=a.type||c||!b&&d){Oc=!0;for(var e=0;e<Qc.length;e++)O(Qc[e])}Qc.push=function(){for(var a=0;a<arguments.length;a++)O(arguments[a]);return 0}}}function Sc(){if(!Oc&&140>Pc){Pc++;try{C.documentElement.doScroll("left"),Rc()}catch(a){B.setTimeout(Sc,50)}}}var Tc=function(a){Oc?a():Qc.push(a)};var Uc=!1,Vc=function(){return B.GoogleAnalyticsObject&&B[B.GoogleAnalyticsObject]};var Wc=function(a){B.GoogleAnalyticsObject||(B.GoogleAnalyticsObject=a||"ga");var b=B.GoogleAnalyticsObject;if(!B[b]){var c=function(){c.q=c.q||[];c.q.push(arguments)};c.l=Number(new Date);B[b]=c}return B[b]},Xc=function(a,b,c,d){b=String(b).replace(/\s+/g,"").split(",");var e=Vc();e(a+"require","linker");e(a+"linker:autoLink",b,c,d)};
var ad=function(){return"&tc="+Ib.filter(function(a){return a}).length},bd="0.005000">Math.random(),cd="",dd=function(){cd=[ec,"&v=3&t=t","&pid="+pc(),"&rv=4s"].join("")},ed={},fd="",gd=void 0,hd={},id={},jd=void 0,kd=2,ld=1E3,md=function(){kd=2},nd=function(){var a=gd;return void 0===a?"":[cd,ed[a]?"":"&es=1",hd[a],ad(),fd,"&z=0"].join("")},od=function(){jd&&(B.clearTimeout(jd),jd=void 0);void 0===gd||ed[gd]&&!fd||(id[gd]||0>=kd--||0>=ld--?id[gd]=!0:(J(nd()),ed[gd]=
!0,fd=""))},pd=function(a,b,c){if(bd&&!id[a]&&b){a!==gd&&(od(),gd=a);var d=c+String(b[T.G]||"").replace(/_/g,"");fd=fd?fd+"."+d:"&tr="+d;jd||(jd=B.setTimeout(od,500));2022<=nd().length&&od()}};function qd(a,b,c,d,e,f){var h=Ib[a],k=rd(a,b,c,d,e,f);if(!k)return null;var l=Tb(h[T.zb],f.R,[]);if(l&&l.length){var m=l[0];k=qd(m.index,b,k,1===m.Jb?e:k,e,f)}return k}
function rd(a,b,c,d,e,f){function h(){var b=Ub(k,f.R);b.vtp_gtmOnSuccess=function(){pd(f.id,Ib[a],"5");c()};b.vtp_gtmOnFailure=function(){pd(f.id,Ib[a],"6");d()};b.vtp_gtmTagId=k.tag_id;if(k[T.fc])d();else{pd(f.id,k,"1");try{Sb(b)}catch(E){pd(f.id,
k,"7");e()}}}var k=Ib[a];if(f.R(k))return null;var l=Tb(k[T.Ab],f.R,[]);if(l&&l.length){var m=l[0],n=qd(m.index,b,c,d,e,f);if(!n)return null;c=n;d=2===m.Jb?e:n}if(k[T.yb]||k[T.gc]){var p=k[T.yb]?Jb:b,q=c,u=d;if(!p[a]){h=tc(h);var r=sd(a,p,h);c=r.M;d=r.aa}return function(){p[a](q,u)}}return h}function sd(a,b,c){var d=[],e=[];b[a]=td(d,e,c);return{M:function(){b[a]=ud;for(var c=0;c<d.length;c++)d[c]()},aa:function(){b[a]=vd;for(var c=0;c<e.length;c++)e[c]()}}}
function td(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function ud(a){a()}function vd(a,b){b()};function wd(a){var b=0,c=0,d=!1;return{add:function(){c++;return tc(function(){b++;d&&b>=c&&a()})},sc:function(){d=!0;b>=c&&a()}}}function xd(a,b){if(!bd)return;var c=function(a){var d=b.R(Ib[a])?"3":"4",f=Tb(Ib[a][T.zb],b.R,[]);f&&f.length&&c(f[0].index);pd(b.id,Ib[a],d);var h=Tb(Ib[a][T.Ab],b.R,[]);h&&h.length&&c(h[0].index)};c(a);}var yd=!1;var zd=function(a,b){var c={};c[T.G]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);for(d in void 0)(void 0).hasOwnProperty(d)&&(c[d]=(void 0)[d]);Ib.push(c);return Ib.length-1};var Ad=/[A-Z]+/,Bd=/\s/,Cd=function(a){if(jc(a)&&(a=a.trim(),!Bd.test(a))){var b=a.indexOf("-");if(!(0>b)){var c=a.substring(0,b);if(Ad.test(c)){for(var d=a.substring(b+1).split("/"),e=0;e<d.length;e++)if(!d[e])return;return{id:a,prefix:c,containerId:c+"-"+d[0],na:d}}}}};var Dd=null,Ed={},Fd={},Gd;function Hd(){Dd=Dd||!cc.gtagRegistered;cc.gtagRegistered=!0;return Dd}var Id=function(a,b){var c={event:a};b&&(c.eventModel=ra(b,void 0),b.event_callback&&(c.eventCallback=b.event_callback),b.event_timeout&&(c.eventTimeout=b.event_timeout));return c};
function Jd(a){if(void 0===Fd[a.id]){var b;if("UA"==a.prefix)b=zd("gtagua",{trackingId:a.id});else if("AW"==a.prefix)b=zd("gtagaw",{conversionId:a});else if("DC"==a.prefix)b=zd("gtagfl",{targetId:a.id});else return;if(!Gd){var c={name:"send_to",dataLayerVersion:2},d={};d[T.G]="__v";for(var e in c)c.hasOwnProperty(e)&&(d["vtp_"+e]=c[e]);Fb.push(d);Gd=["macro",Fb.length-1]}var f={arg0:Gd,arg1:a.id,ignore_case:!1};f[T.G]="_lc";Hb.push(f);var h={"if":[Hb.length-1],add:[b]};h["if"]&&(h.add||h.block)&&
Gb.push(h);Fd[a.id]=b}}
var Sd={event:function(a){var b=a[1];if(jc(b)&&!(3<a.length)){var c;if(2<a.length){if(!qa(a[2]))return;c=a[2]}var d=Id(b,c);var e;var f=c,h=xc("gtag.fields.send_to",2);jc(h)||(h="send_to");var k=f&&f[h];void 0===k&&(k=xc(h,2),void 0===k&&(k="default"));if(jc(k)||ia(k)){for(var l,m=k.toString().replace(/\s+/g,"").split(","),n=[],p=0;p<m.length;p++)0<=m[p].indexOf("-")?n.push(m[p]):n=n.concat(Ed[m[p]]||[]);l=n;for(var q={},u=0;u<l.length;++u){var r=Cd(l[u]);r&&(q[r.id]=
r)}var w=[],E;for(E in q)if(q.hasOwnProperty(E)){var Q=q[E];"AW"===Q.prefix&&Q.na[1]&&w.push(Q.containerId)}for(var A=0;A<w.length;++A)delete q[w[A]];var M=[],D;for(D in q)q.hasOwnProperty(D)&&M.push(q[D]);e=M}else e=void 0;if(!e)return;var N=Hd();N||Rd();for(var I=[],K=0;N&&K<e.length;K++){var G=e[K];I.push(G.id);Jd(G)}d.eventModel=d.eventModel||{};0<e.length?d.eventModel.send_to=I.join():delete d.eventModel.send_to;return d}},set:function(a){var b;2==a.length&&qa(a[1])?
b=ra(a[1],void 0):3==a.length&&jc(a[1])&&(b={},b[a[1]]=a[2]);if(b)return b.eventModel=ra(b,void 0),b.event="gtag.set",b._clear=!0,b},js:function(a){if(2==a.length&&a[1].getTime)return{event:"gtm.js","gtm.start":a[1].getTime()}},config:function(a){var b=a[2]||{};if(2>a.length||!jc(a[1])||!qa(b))return;var c=Cd(a[1]);if(!c)return;Hd()?Jd(c):Rd();var d=c.id,e;for(e in Ed)if(Ed.hasOwnProperty(e)){var f=ja(Ed[e],d);0<=f&&Ed[e].splice(f,1)}var h=c.id,k=b.groups||"default";
k=k.toString().split(",");for(var l=0;l<k.length;l++)Ed[k[l]]=Ed[k[l]]||[],Ed[k[l]].push(h);delete b.groups;Fc("gtag.targets."+c.id,void 0);Fc("gtag.targets."+c.id,ra(b,void 0));return Id("gtag.config",{send_to:c.id});}},Rd=tc(function(){});var Td=!1,Ud=[];function Vd(){if(!Td){Td=!0;for(var a=0;a<Ud.length;a++)O(Ud[a])}};var Wd=[],Xd=!1,Yd=function(a){var b=a.eventCallback,c=tc(function(){ic(b)&&O(function(){b(bc.m)})}),d=a.eventTimeout;d&&B.setTimeout(c,Number(d));return c},Zd=function(){for(var a=!1;!Xd&&0<Wd.length;){Xd=!0;delete vc.eventModel;var b=Wd.shift();if(ic(b))try{b.call(yc)}catch(Kd){}else if(ia(b)){var c=b;if(jc(c[0])){var d=c[0].split("."),e=d.pop(),f=c.slice(1),h=xc(d.join("."),2);if(void 0!==h&&null!==h)try{h[e].apply(h,f)}catch(Kd){}}}else{var k=b;if(k&&("[object Arguments]"==Object.prototype.toString.call(k)||
Object.prototype.hasOwnProperty.call(k,"callee"))){a:{var l=b;if(l.length&&jc(l[0])){var m=Sd[l[0]];if(m){b=m(l);break a}}b=void 0}if(!b){Xd=!1;continue}}var n;var p=void 0,q=b,u=q._clear;for(p in q)q.hasOwnProperty(p)&&"_clear"!==p&&(u&&Fc(p,void 0),Fc(p,q[p]));var r=q.event;if(r){var w=q["gtm.uniqueEventId"];w||(w=rc(),q["gtm.uniqueEventId"]=w,Fc("gtm.uniqueEventId",w));dc=r;var E;var Q,A,M=q,D=M.event,N=M["gtm.uniqueEventId"],I=cc.zones;A=I?I.checkState(bc.m,N):Nc;if(A.active){var K=Yd(M);c:{var G=
A.isWhitelisted;if("gtm.js"==D){if(yd){Q=!1;break c}yd=!0}var L=N,H=D;if(bd&&!(0>=ld)&&gd!==L){od();gd=L;fd="";var S=hd,Z=L,ma,wa=H;ma=0===wa.indexOf("gtm.")?encodeURIComponent(wa):"*";S[Z]="&e="+ma+"&eid="+L;jd||(jd=B.setTimeout(od,500))}var fb=Kc(G),$a={id:N,name:D,Fc:K||hc,R:fb,La:Zb(fb)};for(var Cc,Pb=$a,Nd=wd(Pb.Fc),kf=[],Qb=[],gb=0;gb<Ib.length;gb++)if(Pb.La[gb]){var lf=Ib[gb];var ub=Nd.add();try{var Od=qd(gb,kf,ub,ub,ub,Pb);Od?Qb.push(Od):(xd(gb,Pb),ub())}catch(Kd){ub()}}Nd.sc();for(var Dc=0;Dc<Qb.length;Dc++)Qb[Dc]();Cc=0<Qb.length;if("gtm.js"===D||"gtm.sync"===D)d:{}if(Cc){for(var mf={__cl:!0,__evl:!0,__fsl:!0,__hl:!0,__jel:!0,__lcl:!0,__sdl:!0,__tl:!0,__ytl:!0},Rb=0;Rb<$a.La.length;Rb++)if($a.La[Rb]){var Qd=Ib[Rb];if(Qd&&!mf[Qd[T.G]]){Q=!0;break c}}Q=!1}else Q=Cc}E=Q?!0:!1}else E=!1;dc=null;n=E}else n=!1;a=n||a}Xd=!1}return!a},$d=function(){var a=Zd();try{var b=B["dataLayer"].hide;if(b&&void 0!==b[bc.m]&&b.end){b[bc.m]=!1;var c=!0,d;for(d in b)if(b.hasOwnProperty(d)&&
!0===b[d]){c=!1;break}c&&(b.end(),b.end=null)}}catch(e){}return a},ae=function(){var a=Ea("dataLayer",[]),b=Ea("google_tag_manager",{});b=b["dataLayer"]=b["dataLayer"]||{};Qc.push(function(){b.gtmDom||(b.gtmDom=!0,a.push({event:"gtm.dom"}))});Ud.push(function(){b.gtmLoad||(b.gtmLoad=!0,a.push({event:"gtm.load"}))});var c=a.push;a.push=function(){var b=[].slice.call(arguments,0);c.apply(a,b);for(Wd.push.apply(Wd,b);300<this.length;)this.shift();return Zd()};Wd.push.apply(Wd,a.slice(0));
O($d)};var be={};be.xa=new String("undefined");be.Pa={};var ce=function(a){this.resolve=function(b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]===be.xa?b:a[d]);return c.join("")}};ce.prototype.toString=function(){return this.resolve("undefined")};ce.prototype.valueOf=ce.prototype.toString;be.Mc=function(a){return new ce(a)};var de={};be.Hd=function(a,b){var c=rc();de[c]=[a,b];return c};be.Fb=function(a){var b=a?0:1;return function(a){var c=de[a];if(c&&"function"===typeof c[b])c[b]();de[a]=void 0}};
be.pd=function(a){for(var b=!1,c=!1,d=2;d<a.length;d++)b=b||8===a[d],c=c||16===a[d];return b&&c};be.Bd=function(a){if(a===be.xa)return a;var b=rc();be.Pa[b]=a;return'google_tag_manager["'+bc.m+'"].macro('+b+")"};be.ic=ce;var ee=new qc,fe=function(a,b){function c(a){var b=R(a),c=P(b,"protocol"),d=P(b,"host",!0),e=P(b,"port"),f=P(b,"path").toLowerCase().replace(/\/$/,"");if(void 0===c||"http"==c&&"80"==e||"https"==c&&"443"==e)c="web",e="default";return[c,d,e,f]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0};
function ge(a){var b=a.arg0,c=a.arg1;switch(a["function"]){case "_cn":return 0<=String(b).indexOf(String(c));case "_css":var d;a:{if(b){var e=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];try{for(var f=0;f<e.length;f++)if(b[e[f]]){d=b[e[f]](c);break a}}catch(r){}}d=!1}return d;case "_ew":var h,k;h=String(b);k=String(c);var l=h.length-k.length;return 0<=l&&h.indexOf(k,l)==l;case "_eq":return String(b)==String(c);case "_ge":return Number(b)>=Number(c);
case "_gt":return Number(b)>Number(c);case "_lc":var m;m=String(b).split(",");return 0<=ja(m,String(c));case "_le":return Number(b)<=Number(c);case "_lt":return Number(b)<Number(c);case "_re":var n;var p=a.ignore_case?"i":void 0;try{var q=String(c)+p,u=ee.get(q);u||(u=new RegExp(c,p),ee.set(q,u));n=u.test(b)}catch(r){n=!1}return n;case "_sw":return 0==String(b).indexOf(String(c));case "_um":return fe(b,c)}return!1};function he(a,b,c,d){return(d||"https:"==B.location.protocol?a:b)+c}function ie(a,b){for(var c=b||(a instanceof t?new t:new x),d=a.L(),e=0;e<d.length();e++){var f=d.get(e);if(a.has(f)){var h=a.get(f);h instanceof t?(c.get(f)instanceof t||c.set(f,new t),ie(h,c.get(f))):h instanceof x?(c.get(f)instanceof x||c.set(f,new x),ie(h,c.get(f))):c.set(f,h)}}return c}function je(){return bc.m}function ke(){return(new Date).getTime()}function le(a,b){return ta(xc(a,b||2))}function me(){return dc}
function ne(a){return Ma('<a href="'+a+'"></a>')[0].href}function oe(a){return lc(sa(a))}function pe(a){return null===a?"null":void 0===a?"undefined":a.toString()}function qe(a,b){return pc(a,b)}function re(a,b,c){if(!(a instanceof t))return null;for(var d=new x,e=!1,f=0;f<a.length();f++){var h=a.get(f);h instanceof x&&h.has(b)&&h.has(c)&&(d.set(h.get(b),h.get(c)),e=!0)}return e?d:null}
var se=function(){var a=new Da;a.addAll(Pa());a.addAll({buildSafeUrl:he,decodeHtmlUrl:ne,copy:ie,generateUniqueNumber:rc,getContainerId:je,getCurrentTime:ke,getDataLayerValue:le,getEventName:me,makeInteger:oe,makeString:pe,randomInteger:qe,tableToMap:re});return function(b){return a.get(b)}};var te=new Ra,ue=function(){var a=data.runtime||[];Eb=function(a){return te.I(a)};Lb=ge;Qa(te,se());for(var b=0;b<a.length;b++){var c=a[b];if(!ia(c)||3>c.length){if(0==c.length)continue;break}te.I(c)}};var ve=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d};var we=function(a){return encodeURIComponent(a)},xe=function(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=P(R(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;0<f&&"."!=e.charAt(0)&&(f--,e="."+e);if(0<=f&&c.indexOf(e,f)==f)return!0}}return!1};
var U=function(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null},ye=function(a,b){ra(a,b)},ze=function(a){return lc(a)},Ae=function(a,b){return ja(a,b)};var Be=function(a){var b={"gtm.element":a,"gtm.elementClasses":a.className,"gtm.elementId":a["for"]||Ja(a,"id")||"","gtm.elementTarget":a.formTarget||a.target||""};b["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||a.href||a.src||a.code||a.codebase||"";return b},Ce=function(a){cc.hasOwnProperty("autoEventsSettings")||(cc.autoEventsSettings={});var b=cc.autoEventsSettings;b.hasOwnProperty(a)||(b[a]={});return b[a]},De=function(a,b,c,d){var e=Ce(a),f=sc(e,b,d);e[b]=
c(f)},Ee=function(a,b,c){var d=Ce(a);return sc(d,b,c)};var Fe=/(^|\.)doubleclick\.net$/i,Ge=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,He=function(a,b,c){for(var d=String(b||C.cookie).split(";"),e=[],f=0;f<d.length;f++){var h=d[f].split("="),k=oc(h[0]);if(k&&k==a){var l=oc(h.slice(1).join("="));l&&!1!==c&&(l=decodeURIComponent(l));e.push(l)}}return e},Ie=function(a,b,c,d,e,f){f&&(b=encodeURIComponent(b));var h=a+"="+b+"; ";c&&(h+="path="+c+"; ");e&&(h+="expires="+e.toGMTString()+"; ");var k,l;if("auto"==d){var m=P(B.location,"host",!0).split(".");if(4==
m.length&&/^[0-9]*$/.exec(m[3]))l=["none"];else{for(var n=[],p=m.length-2;0<=p;p--)n.push(m.slice(p).join("."));n.push("none");l=n}}else l=[d||"none"];k=l;for(var q=C.cookie,u=0;u<k.length;u++){var r=h,w=k[u],E=c;if(Fe.test(B.location.hostname)||"/"==E&&Ge.test(w))break;"none"!=k[u]&&(r+="domain="+k[u]+";");C.cookie=r;if(q!=C.cookie||0<=ja(He(a),b))break}};var Je=!1;if(C.querySelectorAll)try{var Ke=C.querySelectorAll(":root");Ke&&1==Ke.length&&Ke[0]==C.documentElement&&(Je=!0)}catch(a){}var Le=Je;var Me=function(a){for(var b=[],c=C.cookie.split(";"),d=new RegExp("^\\s*"+a+"=\\s*(.*?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push(f[1])}var h=[];if(!b||0==b.length)return h;for(var k=0;k<b.length;k++){var l=b[k].split(".");3==l.length&&"GCL"==l[0]&&l[1]&&h.push(l[2])}return h};var Ne=/^\w+$/,Oe=/^[\w-]+$/,Pe=/^\d+\.fls\.doubleclick\.net$/;function Qe(a){return a&&"string"==typeof a&&a.match(Ne)?a:"_gcl"}function Re(a){if(a){if("string"==typeof a){var b=Qe(a);return{ia:b,ha:b}}if(a&&"object"==typeof a)return{ia:Qe(a.dc),ha:Qe(a.aw)}}return{ia:"_gcl",ha:"_gcl"}}function Se(a){var b=R(B.location.href),c=P(b,"host",!1);if(c&&c.match(Pe)){var d=P(b,"path").split(a+"=");if(1<d.length)return d[1].split(";")[0].split("?")[0]}}
function Te(a){return a.filter(function(a){return Oe.test(a)})}var Ve=function(a){var b=Se("gclaw");if(b)return b.split(".");var c=Re(a);if("_gcl"==c.ha){var d=Ue();if(d&&(null==d.K||"aw.ds"==d.K))return[d.ka]}return Te(Me(c.ha+"_aw"))},We=function(a){var b=Se("gcldc");if(b)return b.split(".");var c=Re(a);if("_gcl"==c.ia){var d=Ue();if(d&&("ds"==d.K||"aw.ds"==d.K))return[d.ka]}return Te(Me(c.ia+"_dc"))};
function Ue(){var a=R(B.location.href),b=P(a,"query",!1,void 0,"gclid"),c=P(a,"query",!1,void 0,"gclsrc");if(!b||!c){var d=P(a,"fragment");b=b||Na(d,"gclid");c=c||Na(d,"gclsrc")}return void 0!==b&&b.match(Oe)?{ka:b,K:c}:null}
var Xe=function(a,b,c){var d=Re(a);c=c||"auto";b=b||"/";var e=Ue();if(null!=e){var f=(new Date).getTime(),h=new Date(f+7776E6),k=["GCL",Math.round(f/1E3),e.ka].join(".");e.K&&"aw.ds"!=e.K||Ie(d.ha+"_aw",k,b,c,h,!0);"aw.ds"!=e.K&&"ds"!=e.K||Ie(d.ia+"_dc",k,b,c,h,!0)}},Ye=function(){var a=Se("gac");if(a)return decodeURIComponent(a);for(var b=[],c=C.cookie.split(";"),d=/^\s*_gac_(UA-\d+-\d+)=\s*(.+?)\s*$/,e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({lb:f[1],value:f[2]})}var h={};if(b&&b.length)for(var k=
0;k<b.length;k++){var l=b[k].value.split(".");"1"==l[0]&&3==l.length&&l[1]&&(h[b[k].lb]||(h[b[k].lb]=[]),h[b[k].lb].push({timestamp:l[1],ka:l[2]}))}var m=[],n;for(n in h)if(h.hasOwnProperty(n)){for(var p=[],q=h[n],u=0;u<q.length;u++)p.push(q[u].ka);p=Te(p);p.length&&m.push(n+":"+p.join(","))}return m.join(";")};var Ze;a:{Ze="g";break a;Ze="G"}var $e={"":"n",UA:"u",AW:"a",DC:"d",GTM:Ze},af=function(a){var b=bc.m.split("-"),c=b[0].toUpperCase();return($e[c]||"i")+"4s"+(a&&"GTM"===c?b[1]:"")};
var bf=function(a){return!(void 0===a||null===a||0===(a+"").length)},cf=function(a,b){var c;if(2===b.B)return a("ord",pc(1E11,1E13)),!0;if(3===b.B)return a("ord","1"),a("num",pc(1E11,1E13)),!0;if(4===b.B)return bf(b.sessionId)&&a("ord",b.sessionId),!0;if(5===b.B)c="1";else if(6===b.B)c=b.Yb;else return!1;bf(c)&&a("qty",c);bf(b.Ta)&&a("cost",b.Ta);bf(b.mb)&&a("ord",b.mb);return!0},df=encodeURIComponent,ef=function(a,b){function c(a,b,c){f.hasOwnProperty(a)||(b+="",e+=";"+a+"="+(c?b:df(b)))}var d=a.Va,
e=a.protocol;e+=a.Ma?"//"+d+".fls.doubleclick.net/activityi":"//ad.doubleclick.net/activity";e+=";src="+df(d)+(";type="+df(a.Wa))+(";cat="+df(a.fa));var f=a.Oc||{},h;for(h in f)f.hasOwnProperty(h)&&(e+=";"+df(h)+"="+df(f[h]+""));if(cf(c,a)){bf(a.ob)&&c("u",a.ob);bf(a.tran)&&c("tran",a.tran);c("gtm",af());if(a.Sa){var k=We(a.Ea);k&&k.length&&c("gcldc",k.join("."));var l=Ve(a.Ea);l&&l.length&&c("gclaw",l.join("."));var m=Ye();m&&c("gac",m)}bf(a.eb)&&c("prd",a.eb,!0);for(var n in a.sa)a.sa.hasOwnProperty(n)&&
c(n,a.sa[n]);e+=b||"";bf(a.Ia)&&c("~oref",a.Ia);a.Ma?Ga(e+"?",a.M):J(e+"?",a.M,a.aa)}else O(a.aa)};var ff=!!B.MutationObserver,gf=void 0,hf=function(a){if(!gf){var b=function(){var a=C.body;if(a)if(ff)(new MutationObserver(function(){for(var a=0;a<gf.length;a++)O(gf[a])})).observe(a,{childList:!0,subtree:!0});else{var b=!1;Ha(a,"DOMNodeInserted",function(){b||(b=!0,O(function(){b=!1;for(var a=0;a<gf.length;a++)O(gf[a])}))})}};gf=[];C.body?b():O(b)}gf.push(a)};var wf="www.googletagmanager.com/gtm.js";wf="www.googletagmanager.com/gtag/js";
var xf=wf,yf=function(a,b,c,d){Ha(a,b,c,d)},zf=function(a,b){return B.setTimeout(a,b)},Af=function(a,b,c){F(a,b,c)},Bf={},Cf=function(a,b,c){var d=Bf[a];if(void 0===d){var e=function(a,b){return function(){a.status=b;for(var c=2==b?a.ac:a.Ib,d=0;d<c.length;d++)B.setTimeout(c[d],0)}};d={status:1,ac:[],Ib:[],Ld:void 0};d.ne=F(a,e(d,2),e(d,3));Bf[a]=d}0===d.status&&(d.Ld(),d.status=2);2===d.status?b&&b():3===d.status?c&&c():1===d.status&&(b&&d.ac.push(b),c&&d.Ib.push(c))},Df=function(){return B.location.href},
Ef=function(a){return P(R(a),"fragment")},V=function(a,b){return xc(a,b||2)},Ff=function(a,b,c){b&&(a.eventCallback=b,c&&(a.eventTimeout=c));return B["dataLayer"].push(a)},Gf=function(a,b){B[a]=b},W=function(a,b,c){b&&(void 0===B[a]||c&&!B[a])&&(B[a]=b);return B[a]},Hf=function(a,b){var c;a:{var d;d=100;for(var e={},f=0;f<b.length;f++)e[b[f]]=!0;for(var h=a,k=0;h&&k<=d;k++){if(e[String(h.tagName).toLowerCase()]){c=h;break a}h=h.parentElement}c=null}return c},X=function(a,b,c,d){var e=!d&&"http:"==
B.location.protocol;e&&(e=2!==If());return(e?b:a)+c};
var Jf=function(a){var b=0;return b},Kf=function(a){},Lf=function(a){var b=!1;return b},Mf=function(a,b){var c;a:{if(a&&
ia(a))for(var d=0;d<a.length;d++)if(a[d]&&b(a[d])){c=a[d];break a}c=void 0}return c},Nf=function(a,b,c,d){De(a,b,c,d)},Of=function(a,b,c){return Ee(a,b,c)},Pf=function(a){return!!Ee(a,"init",!1)},Qf=function(a){Ce(a).init=!0};
var If=function(){var a=xf;a=a.toLowerCase();for(var b="https://"+a,c="http://"+a,d=1,e=C.getElementsByTagName("script"),f=0;f<e.length&&100>f;f++){var h=e[f].src;if(h){h=h.toLowerCase();if(0===h.indexOf(c))return 3;1===d&&0===h.indexOf(b)&&(d=2)}}return d};var Sf=function(a,b){return zc(a,b,void 0)};
var Tf=function(a){var b=xf+"?id="+encodeURIComponent(a)+"&l=dataLayer",c=X("https://","http://",b);F(c,void 0,void 0)};var Vf=function(a,b,c){a instanceof be.ic&&(a=a.resolve(be.Hd(b,c)),b=hc);return{Xa:a,M:b}};var Y={a:{}};

Y.a.e=["google"],function(){(function(a){Y.__e=a;Y.__e.b="e";Y.__e.g=!0})(function(){return dc})}();
Y.a.v=["google"],function(){(function(a){Y.__v=a;Y.__v.b="v";Y.__v.g=!0})(function(a){var b=V(a.vtp_name.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return void 0!==b?b:a.vtp_defaultValue})}();
Y.a.gtagaw=["google"],function(){var a=!1,b=!1,c=[],d="send_to aw_remarketing aw_remarketing_only custom_params send_page_view language value currency transaction_id user_id conversion_linker conversion_cookie_prefix page_location page_referrer phone_conversion_number phone_conversion_callback phone_conversion_css_class items aw_merchant_id aw_feed_country aw_feed_language discount disable_merchant_reported_purchases".split(" "),e=function(a){var b=W("google_trackConversion"),c=a.gtm_onFailure;"function"==
typeof b?b(a)||c():c()},f=function(){for(;0<c.length;)e(c.shift())},h=function(){a||(a=!0,Af(X("https://","http://","www.googleadservices.com/pagead/conversion_async.js"),function(){f();c={push:e}},function(){f();a=!1}))},k=function(a,c,d,e){if(c){var f=a.na[0],h=a.na[1],k=W("_googWcmImpl",function(){k.q=k.q||[];k.q.push(arguments)});W("_googWcmAk",f);b||(b=!0,Af(X("https://","http://","www.gstatic.com/wcm/loader.js")));var l={ak:f,cl:h};void 0===d&&(l.autoreplace=c);k(2,d,l,c,e,new Date,e)}},l=function(a){if(a){for(var b=
[],c=0;c<a.length;++c){var d=a[c];d&&b.push({item_id:d.id,quantity:d.quantity,value:d.price})}return b}};(function(a){Y.__gtagaw=a;Y.__gtagaw.b="gtagaw";Y.__gtagaw.g=!0})(function(a){var b=a.vtp_conversionId,e=dc,f="gtag.config"==e,m=b.na[0],r=b.na[1],w=void 0!==r,E=b.containerId,Q=w?b.id:void 0,A=function(a){return zc(a,E,Q)},M=!1!==A("conversion_linker"),D=A("conversion_cookie_prefix");f&&M&&Xe(D,void 0,void 0);if(f&&w){var N=A("phone_conversion_number"),I=A("phone_conversion_callback"),K=A("phone_conversion_css_class"),
G=A("phone_conversion_options");k(b,N,I||K,G)}var L=!1===A("aw_remarketing")||!1===A("send_page_view");if(!f||!w&&!L){!0===A("aw_remarketing_only")&&(w=!1);var H={google_conversion_id:m,google_remarketing_only:!w,onload_callback:a.vtp_gtmOnSuccess,gtm_onFailure:a.vtp_gtmOnFailure,google_conversion_format:"3",google_conversion_color:"ffffff",google_conversion_domain:"",google_conversion_label:r,google_conversion_language:A("language"),google_conversion_value:A("value"),google_conversion_currency:A("currency"),
google_conversion_order_id:A("transaction_id"),google_user_id:A("user_id"),google_conversion_page_url:A("page_location"),google_conversion_referrer_url:A("page_referrer"),google_gtm:af(void 0),google_read_gcl_cookie_opt_out:!M};M&&D&&(qa(D)?H.google_gcl_cookie_prefix=D.aw:H.google_gcl_cookie_prefix=D);var S=function(){var a=A("custom_params"),b={event:e};if(ia(a)){for(var c=0;c<a.length;++c){var f=a[c],h=A(f);void 0!==h&&(b[f]=h)}return b}var k=A("eventModel");if(!k)return null;ra(k,b);for(var l=
0;l<d.length;++l)delete b[d[l]];return b}();S&&(H.google_custom_params=S);if(w&&"purchase"==e&&A("aw_merchant_id")){H.google_conversion_merchant_id=A("aw_merchant_id");H.google_basket_feed_country=A("aw_feed_country");H.google_basket_feed_language=A("aw_feed_language");H.google_basket_discount=A("discount");H.google_basket_transaction_type=e;H.google_disable_merchant_reported_conversions=!0===A("disable_merchant_reported_purchases");var Z=l(A("items"));Z&&(H.google_conversion_items=Z)}c.push(H)}h()})}();




Y.a.gtagfl=[],function(){function a(a){var b=/^DC-(\d+)(\/([\w-]+)\/([\w-]+)\+(\w+))?$/.exec(a);if(b){var c={standard:2,unique:3,per_session:4,transactions:5,items_sold:6,"":1}[(b[5]||"").toLowerCase()];if(c)return{containerId:"DC-"+b[1],bc:b[3]?a:"",mc:b[1],kc:b[3]||"",fa:b[4]||"",B:c}}}function b(a,b){function c(b,c,e){void 0!==e&&0!==(e+"").length&&d.push(b+c+":"+a(e+""))}var d=[],e=b("items")||[];if(ia(e))for(var l=0;l<e.length;l++){var m=e[l],n=l+1;c("i",n,m.id);c("p",n,m.price);c("q",n,m.quantity);
c("c",n,b("country"));c("l",n,b("language"))}return d.join("|")}function c(a,b,c){var d=/^u([1-9]\d?|100)$/,e=a("custom_map")||{},f=Ec(b,c),m={},n={};if(qa(e))for(var p in e)if(e.hasOwnProperty(p)&&d.test(p)){var q=e[p];jc(q)&&(m[p]=q)}for(var u=0;u<f.length;u++){var r=f[u];d.test(r)&&(m[r]=r)}for(var w in m)m.hasOwnProperty(w)&&(n[w]=a(m[w]));return n}(function(a){Y.__gtagfl=a;Y.__gtagfl.b="gtagfl";Y.__gtagfl.g=!0})(function(d){var e=d.vtp_gtmOnSuccess,f=d.vtp_gtmOnFailure,h=a(d.vtp_targetId);if(h){var k=
function(a){return zc(a,h.containerId,h.bc||void 0)},l=!1!==k("conversion_linker"),m=k("conversion_cookie_prefix");if("gtag.config"===dc)l&&Xe(m,void 0,void 0),O(e);else{var n={},p=k("dc_custom_params");if(qa(p))for(var q in p)if(p.hasOwnProperty(q)){var u=p[q];void 0!==u&&null!==u&&(n[q]=u)}var r="";if(5===h.B||6===h.B)r=b(we,k);var w=c(k,h.containerId,h.bc),E=3===If(),Q=!0===k("allow_custom_scripts"),A={fa:h.fa,Sa:l,Ea:m,Ta:k("value"),B:h.B,Oc:n,Va:h.mc,Wa:h.kc,aa:f,M:e,Ia:Oa(R(Df())),eb:r,protocol:E?
"http:":"https:",Yb:k("quantity"),Ma:Q,sessionId:k("session_id"),mb:k("transaction_id"),sa:w};ef(A,void 0)}}else O(f)})}();


Y.a.gtagua=["google"],function(){var a,b={client_id:1,client_storage:"storage",cookie_name:1,cookie_domain:1,cookie_expires:1,cookie_update:1,sample_rate:1,site_speed_sample_rate:1,use_amp_client_id:1,store_gac:1,conversion_linker:"storeGac"},c={anonymize_ip:1,app_id:1,app_installer_id:1,app_name:1,app_version:1,campaign:{name:"campaignName",source:"campaignSource",medium:"campaignMedium",term:"campaignTerm",content:"campaignContent",id:"campaignId"},currency:"currencyCode",description:"exDescription",
fatal:"exFatal",language:1,non_interaction:1,page_hostname:"hostname",page_referrer:"referrer",page_path:"page",page_location:"location",page_title:"title",screen_name:1,transport_type:"transport",user_id:1},d={content_id:1,event_category:1,event_action:1,event_label:1,link_attribution:1,linker:1,method:1,name:1,send_page_view:1,value:1},e={cookie_name:1,cookie_expires:"duration",levels:1},f={anonymize_ip:1,fatal:1,non_interaction:1,use_amp_client_id:1,send_page_view:1,store_gac:1,conversion_linker:1},
h=function(a,b,c,d){if(void 0!==c)if(f[b]&&(c=mc(c)),"anonymize_ip"!=b||c||(c=void 0),1===a)d[k(b)]=c;else if(jc(a))d[a]=c;else for(var e in a)a.hasOwnProperty(e)&&void 0!==c[e]&&(d[a[e]]=c[e])},k=function(a){return a&&jc(a)?a.replace(/(_[a-z])/g,function(a){return a[1].toUpperCase()}):a},l=function(a,b,c){a.hasOwnProperty(b)||(a[b]=c)},m=function(a,e,f){var k={},m={},n={},p=Sf("custom_map",a);if(qa(p))for(var q in p)if(p.hasOwnProperty(q)&&/^(dimension|metric)\d+$/.test(q)){var r=Sf(p[q],a);void 0!==
r&&l(m,q,r)}for(var w=Ec(a,void 0),u=0;u<w.length;++u){var G=w[u],L=Sf(G,a);d.hasOwnProperty(G)?h(d[G],G,L,k):c.hasOwnProperty(G)?h(c[G],G,L,m):b.hasOwnProperty(G)?h(b[G],G,L,n):/^(dimension|metric|content_group)\d+$/.test(G)&&h(1,G,L,m)}var H=String(dc);l(n,"cookieDomain","auto");l(m,"forceSSL",!0);var S="general";0<=Ae("add_payment_info add_to_cart add_to_wishlist begin_checkout checkout_progress purchase refund remove_from_cart set_checkout_option".split(" "),H)?S="ecommerce":0<=Ae("generate_lead login search select_content share sign_up view_item view_item_list view_promotion view_search_results".split(" "),
H)?S="engagement":"exception"==H&&(S="error");l(k,"eventCategory",S);0<=Ae(["view_item","view_item_list","view_promotion","view_search_results"],H)&&l(m,"nonInteraction",!0);"login"==H||"sign_up"==H||"share"==H?l(k,"eventLabel",Sf("method",a)):"search"==H||"view_search_results"==H?l(k,"eventLabel",Sf("search_term",a)):"select_content"==H&&l(k,"eventLabel",Sf("content_type",a));var Z=k.linker||{};if(Z.accept_incoming||0!=Z.accept_incoming&&Z.domains)n.allowLinker=!0;!1===Sf("allow_display_features",
a)&&(m.displayFeaturesTask=null);n.name=e;m["&gtm"]=af(!0);m.hitCallback=f;k.J=m;k.Gb=n;return k},n=function(a){function b(a){var b=ra(a,void 0);b.list=a.list_name;b.listPosition=a.list_position;b.position=a.list_position||a.creative_slot;b.creative=a.creative_name;return b}function c(a){for(var c=[],d=0;a&&d<a.length;d++)a[d]&&c.push(b(a[d]));return c.length?c:void 0}function d(){return{id:e("transaction_id"),affiliation:e("affiliation"),revenue:e("value"),tax:e("tax"),shipping:e("shipping"),coupon:e("coupon"),
list:e("list_name")}}var e=function(b){return zc(b,a,void 0)},f=e("items"),h=e("custom_map");if(qa(h))for(var k=0;f&&k<f.length;++k){var m=f[k],n;for(n in h)h.hasOwnProperty(n)&&/^(dimension|metric)\d+$/.test(n)&&l(m,n,m[h[n]])}var p=null,q=dc,u=e("promotions");"purchase"==q||"refund"==q?p={action:q,ea:d(),ba:c(f)}:"add_to_cart"==q?p={action:"add",ba:c(f)}:"remove_from_cart"==q?p={action:"remove",ba:c(f)}:"view_item"==q?p={action:"detail",ea:d(),ba:c(f)}:"view_item_list"==q?p={action:"impressions",
kd:c(f)}:"view_promotion"==q?p={action:"promo_view",fb:c(u)}:"select_content"==q&&u&&0<u.length?p={action:"promo_click",fb:c(u)}:"select_content"==q?p={action:"click",ea:{list:e("list_name")},ba:c(f)}:"begin_checkout"==q||"checkout_progress"==q?p={action:"checkout",ba:c(f),ea:{step:"begin_checkout"==q?1:e("checkout_step"),option:e("checkout_option")}}:"set_checkout_option"==q&&(p={action:"checkout_option",ea:{step:e("checkout_step"),option:e("checkout_option")}});p&&(p.be=e("currency"));return p},
p={},q=function(a,b){var c=p[a];p[a]=ra(b,void 0);if(!c)return!1;for(var d in b)if(b.hasOwnProperty(d)&&b[d]!==c[d])return!0;for(d in c)if(c.hasOwnProperty(d)&&c[d]!==b[d])return!0;return!1};(function(a){Y.__gtagua=a;Y.__gtagua.b="gtagua";Y.__gtagua.g=!0})(function(b){var c=b.vtp_trackingId,d=Wc(void 0),f="gtag_"+c.split("-").join("_"),p=function(a){var b=[].slice.call(arguments,0);b[0]=f+"."+b[0];d.apply(window,b)},u=function(){var a=function(a,b){for(var c=0;b&&c<b.length;c++)p(a,b[c])},b=n(c);
if(b){var d=b.action;if("impressions"==d)a("ec:addImpression",b.kd);else if("promo_click"==d||"promo_view"==d){var e=b.fb;a("ec:addPromo",b.fb);e&&0<e.length&&"promo_click"==d&&p("ec:setAction",d)}else a("ec:addProduct",b.ba),p("ec:setAction",d,b.ea)}},M=function(){var a=Sf("optimize_id",c);a&&(p("require",a,{dataLayer:"dataLayer"}),p("require","render"))},D=m(c,f,b.vtp_gtmOnSuccess);q(f,D.Gb)&&d(function(){Vc()&&Vc().remove(f)});d("create",c,D.Gb);(function(){var a=Sf("custom_map",c);d(function(){if(qa(a)){var b=
D.J,c=Vc().getByName(f),d;for(d in a)if(a.hasOwnProperty(d)&&/^(dimension|metric)\d+$/.test(d)){var e=c.get(k(a[d]));l(b,d,e)}}})})();(function(a){if(a){var b={};if(qa(a))for(var c in e)e.hasOwnProperty(c)&&h(e[c],c,a[c],b);p("require","linkid",b)}})(D.linkAttribution);var N=D.linker;N&&N.domains&&Xc(f+".",N.domains,!!N.use_anchor,!!N.decorate_forms);var I=function(a,b,c){c&&(b=""+b);D.J[a]=b},K=dc;"page_view"==K?(M(),p("send","pageview",D.J)):"gtag.config"==K?(M(),0!=D.sendPageView&&p("send","pageview",
D.J)):"screen_view"==K?p("send","screenview",D.J):"timing_complete"==K?(I("timingCategory",D.eventCategory,!0),I("timingVar",D.name,!0),I("timingValue",lc(D.value)),void 0!==D.eventLabel&&I("timingLabel",D.eventLabel,!0),p("send","timing",D.J)):"exception"==K?p("send","exception",D.J):(0<=Ae("view_item_list select_content view_item add_to_cart remove_from_cart begin_checkout set_checkout_option purchase refund view_promotion checkout_progress".split(" "),K)&&(p("require","ec","ec.js"),u()),I("eventCategory",
D.eventCategory,!0),I("eventAction",D.eventAction||K,!0),void 0!==D.eventLabel&&I("eventLabel",D.eventLabel,!0),void 0!==D.value&&I("eventValue",lc(D.value)),p("send","event",D.J));a||(a=!0,Af("https://www.google-analytics.com/analytics.js",function(){Vc().loaded||b.vtp_gtmOnFailure()},b.vtp_gtmOnFailure))})}();

var Wf={macro:function(a){if(be.Pa.hasOwnProperty(a))return be.Pa[a]}};Wf.dataLayer=yc;Wf.onHtmlSuccess=be.Fb(!0);Wf.onHtmlFailure=be.Fb(!1);Wf.callback=function(a){fc.hasOwnProperty(a)&&ic(fc[a])&&fc[a]();delete fc[a]};Wf.yc=function(){cc[bc.m]=Wf;gc=Y.a;Mb=Mb||be;Nb=Lc};
Wf.ld=function(){cc=B.google_tag_manager=B.google_tag_manager||{};if(cc[bc.m]){var a=cc.zones;a&&a.unregisterChild(bc.m)}else{for(var b=data.resource||{},c=b.macros||[],d=0;d<c.length;d++)Fb.push(c[d]);for(var e=b.tags||[],f=0;f<e.length;f++)Ib.push(e[f]);for(var h=b.predicates||[],k=0;k<h.length;k++)Hb.push(h[k]);for(var l=b.rules||[],m=0;m<l.length;m++){for(var n=l[m],p={},q=0;q<n.length;q++)p[n[q][0]]=Array.prototype.slice.call(n[q],1);Gb.push(p)}Kb=Y;ue();Wf.yc();ae();Oc=!1;Pc=0;if("interactive"==
C.readyState&&!C.createEventObject||"complete"==C.readyState)Rc();else{Ha(C,"DOMContentLoaded",Rc);Ha(C,"readystatechange",Rc);if(C.createEventObject&&C.documentElement.doScroll){var u=!0;try{u=!B.frameElement}catch(w){}u&&Sc()}Ha(B,"load",Rc)}Td=!1;"complete"===C.readyState?Vd():Ha(B,"load",Vd);a:{
if(!bd)break a;dd();kd=2;gd=void 0;hd={};ed={};jd=void 0;id={};fd="";B.setInterval(dd,864E5);B.setInterval(md,1E3);}}};Wf.ld();

})()
