webpackJsonp([1],{10:function(e,t){var g={getItem:function(e){return decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null},setItem:function(e,t,g,n,A,C){if(!e||/^(?:expires|max\-age|path|domain|secure)$/i.test(e))return!1;var I="";if(g)switch(g.constructor){case Number:I=g===1/0?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+g;break;case String:I="; expires="+g;break;case Date:I="; expires="+g.toUTCString()}return document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+I+(A?"; domain="+A:"")+(n?"; path="+n:"")+(C?"; secure":""),!0},removeItem:function(e,t,g){return!(!e||!this.hasItem(e))&&(document.cookie=encodeURIComponent(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(g?"; domain="+g:"")+(t?"; path="+t:""),!0)},hasItem:function(e){return new RegExp("(?:^|;\\s*)"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie)},keys:function(){for(var e=document.cookie.replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g,"").split(/\s*(?:\=[^;]*)?;\s*/),t=0;t<e.length;t++)e[t]=decodeURIComponent(e[t]);return e}};e.exports=g;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&__REACT_HOT_LOADER__.register(g,"cookie","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonCookie.js")}()},100:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(2),i=g.n(I),r=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),o=function(e){function t(){n(this,t);var e=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.state={},e}return C(t,e),r(t,[{key:"render",value:function(){return i.a.createElement("div",{className:"common-loading-layer"},i.a.createElement("div",{className:"back"}),i.a.createElement("div",{className:"front"},i.a.createElement("div",{className:"img"},i.a.createElement("img",{src:"//zxcs.ggwan.com/xuyuandiandeng/images/loading.png",alt:"loading"})),i.a.createElement("div",{className:"text"},i.a.createElement("span",null,this.props.text||"请稍后",i.a.createElement("i",null,"...")))))}}]),t}(I.Component),a=o;t.a=a;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(o,"CommonLoadingLayer","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/common/CommonLoadingLayer.jsx"),__REACT_HOT_LOADER__.register(a,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/common/CommonLoadingLayer.jsx"))}()},13:function(e,t,g){"use strict";function n(e){var t=location.search,g={};t=decodeURIComponent(t.substring(1));var n=t.split("&"),A=n.length,C=void 0;if(A>0)for(C=0;C<A;C++){var I=n[C].split("=");g[I[0]]=I[1]}return g[e]}function A(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function C(){return/iphone|ipad|ipod/i.test(navigator.userAgent)}function I(){return/android/i.test(navigator.userAgent)}function i(e,t){return["zh-cn","zh-tw","zh-hk"].some(function(t){return e===t})?e:t||"zh-tw"}function r(e){return["swlx"].some(function(t){return t===e})}function o(e,t,g,A){var C=n(t);C&&e.setItem(g,C,A,"/")}function a(){var e=n("lang");if(g.i(L.a)()){var t=g.i(L.b)();t.flag&&(e=1==t.lang?"zh-tw":"zh-cn")}return e}function s(e,t){var g,n=e,A=!0;return function(){var e=arguments,C=this;if(A&&(n.apply(C,e),A=!1),g)return!1;g=setTimeout(function(){clearTimeout(g),g=null,n.apply(C,e)},t||500)}}function c(e){var t=document.getElementById("wrapper");return{x:t.scrollLeft,y:t.scrollTop}}function l(e){if(e=e||window,null!=e.innerWidth)return{x:e.innerWidth,y:e.innerHeight};var t=e.document;return"CSS1Compat"==document.compatMode?{x:t.documentElement.clientWidth,y:t.documentElement.clientHeight}:{x:t.body.clientWidth,y:t.body.clientHeight}}function d(e,t){var g,n;e?("string"==typeof e&&(e=document.getElementById(e)),g=e.getBoundingClientRect().left+c().x,n=e.getBoundingClientRect().top+c().y):(g=0,n=0);var A=setInterval(function(){var e=c().y,C=c().x,I=e-(e-n)/10,i=C-(C-g)/10;Math.abs(I-e)<1&&(I-e>0?I++:I--),Math.abs(i-C)<1&&(i-C>0?i++:i--),t.scrollTop=I,Math.abs(c().y-n)<=2&&(clearInterval(A),t.scrollTop=I)},10)}function h(e){var t=document.body,g=document.body.scrollTop||document.documentElement.scrollTop;g&&document.documentElement.scrollTop>0?t=document.documentElement:!g&&(document.documentElement.scrollTop=1)&&document.documentElement.scrollTop&&(t=document.documentElement);var n,A=t.scrollTop;e?("string"==typeof e&&(e=document.getElementById(e)),n=e.getBoundingClientRect().top+A):n=0;var C=setInterval(function(){var e=t.scrollTop,g=e-(e-n)/10;Math.abs(g-e)<1&&(g-e>0?g++:g--),t.scrollTop=g,Math.abs(t.scrollTop-n)<=2&&(clearInterval(C),t.scrollTop=g)},10)}function m(e,t){var g=document.body.scrollTop?document.body:document.documentElement,n=g.scrollTop,A=e-n,C=+new Date,I=function(e,t,g,n){return(e/=n/2)<1?g/2*e*e+t:(e--,-g/2*(e*(e-2)-1)+t)};!function i(){var r=+new Date,o=r-C;g.scrollTop=parseInt(I(o,n,A,t)),o<t?requestAnimationFrame(i):g.scrollTop=e}()}function u(e,t,g){return e+(/\?/.test(e)?"&":"?")+t+"="+g}function p(){return/linghit xindongqiming/.test(window.navigator.userAgent.toLowerCase())}function f(e){var t="?";for(var g in e)e.hasOwnProperty(g)&&(t+=g+"="+e[g]+"&");return t.replace(/&$/,"")}function _(){return/micromessenger/i.test(window.navigator.userAgent.toLowerCase())}function v(){var e=n("schannel")||"";try{e?N.a.setItem("_schannel",e,1/0,"/"):N.a.removeItem("_schannel","/")}catch(e){}}function y(){var e=n("wltc")||"";try{e&&N.a.setItem("_wltc",e,1/0,"/")}catch(e){}}function b(){var e=navigator.userAgent,t=/iPad|iPhone|iPod/.test(e),g=/OS 11/.test(e);return t&&g}function E(e){return!(["swchannel12","swchannel25"].indexOf(e)>=0)}function T(){return"miniprogram"===window.__wxjs_environment}function w(e,t){var g=localStorage.getItem("orders");g=g?g+","+e:e,localStorage.setItem("orders",g),t instanceof Array?localStorage.setItem("d_i",JSON.stringify(t)):localStorage.setItem("u_i",JSON.stringify(t))}function x(e){var t=localStorage.getItem("orders");t=t?t+","+e:e,localStorage.setItem("orders",t)}function D(e,t,g){var n=N.a.getItem(g+"_last_click"),A=N.a.getItem(g+"_last_close");return n=n?parseInt(n):null,A=A?parseInt(A):null,!(e-A<864e5)&&(n?e>t&&t>n&&t>e-6048e5&&(N.a.setItem(g+"_last_show",e,1/0,"/"),!0):t>e-6048e5&&(N.a.setItem(g+"_last_show",e,1/0,"/"),!0))}function R(){var e={};try{if(!(e=JSON.parse(localStorage.getItem("u_i"))))return}catch(e){}var t=e,g=t.user,n=t.gender,A=t.birthday,C=t.hour_mark,I=t.datePickerBirth,i=t.is_lunar;if(g){var r=this.props.setIndexData;"function"!=typeof r&&(r=this.props.actions.setIndexData);var o=0==C?parseInt(A.slice(-2))+1:0,a=0==C?parseInt(A.slice(-2)):-1;this.setState({gender:n,name:g}),r({datePickerBirth:I,birthTimeDataId:o,birthTimeDataValue:a,mode:i,datePickerTimeConfirm:C,datePickerBirthFormat:A})}}function S(e,t){var g=JSON.parse(localStorage.getItem("d_i"));if(!(!g instanceof Array)){var n=g[0],A=g[1],C=O(n),I=O(A);this.setState({man_name:A.user,woman_name:n.user}),e(H({datePickerBirth:A.datePickerBirth,datePickerBirthFormat:A.birthday,mode:A.is_lunar,datePickerTimeConfirm:A.hour_mark},I)),t(H({datePickerBirth:n.datePickerBirth,datePickerBirthFormat:n.birthday,mode:n.is_lunar,datePickerTimeConfirm:n.hour_mark},C))}}function O(e){var t=e.birthday,g=e.hour_mark;return{birthTimeDataId:0==g?parseInt(t.slice(-2))+1:0,birthTimeDataValue:0==g?parseInt(t.slice(-2)):-1}}function k(e){return!!/bdsem-/.test(e)}function P(e){if(!/^\d+$/.test(e))return 1;var t=parseInt(e).toString(2);return t.length<=4?parseInt(e):parseInt(t.substr(t.length-4),2)}function M(e){var t=Date.now(),g=document.createElement("IFRAME");g.src=e,g.style.position="absolute",g.style.left="-1000px",g.style.top="-1000px",g.style.width="1px",g.style.height="1px",g.style.webkitTransition="all 4s",document.body.appendChild(g),setTimeout(function(){g.addEventListener("webkitTransitionEnd",function(){document.body.removeChild(g),Date.now()-t<3e3&&(location.href=e)},!1),g.style.left="-10px"},0)}t.a=n,t.f=A,t.e=i,t.c=o,t.d=a,t.h=s,t.b=h,t.g=v;var L=g(52),z=g(10),N=g.n(z),H=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var g=arguments[t];for(var n in g)Object.prototype.hasOwnProperty.call(g,n)&&(e[n]=g[n])}return e};!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(n,"getQueryFromUrl","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(A,"trim","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(C,"isIOS","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(I,"isAndriod","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(i,"filterLang","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(r,"isSpecialChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(o,"setQueryToCookie","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(a,"textSimplifiedOrTraditionalFirstJudge","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(s,"throttle","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(c,"getScrollOffsets","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(l,"getViewPortSize","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(d,"scrollPosition","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(h,"noSetContainerScrollPosition","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(m,"scrollToElement","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(u,"urlAddQuery","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(p,"hideMenuAndFooterForXinDongQiMingApp","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(f,"convertObjectToSearch","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(_,"isWeChat","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(v,"schannelToCookie","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(y,"wltcToCookie","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(b,"isiOSandiOS11","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(E,"checkQuce","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(T,"wxMiNiProgram","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(w,"saveOrderAndUser","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(x,"saveOrdersOnly","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(D,"isInAWeek","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(R,"userInit","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(S,"doubleUserInit","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(O,"formateTimeId","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(k,"isBaiduChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(P,"getVersion","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"),__REACT_HOT_LOADER__.register(M,"callWeChatByIFrame","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonTools.js"))}()},143:function(e,t,g){"use strict";function n(e){return!/^\s*$/.test(e)}function A(e){return/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(e)}function C(e){return/^[A-Z0-9]{20,36}$/i.test(e)}function I(e){return/^[\u4e00-\u9fa5]+$/.test(e)}function i(e){return/^1[0-9]{10}$/.test(e)}function r(e){return/^[1-9]\d{5}$/.test(e)}function o(e,t){return t=t||10,new RegExp("\\d{"+t+"}").test(e)}t.a=n,t.b=A,t.d=I,t.c=i;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(n,"isNotEmpty","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonValidate.js"),__REACT_HOT_LOADER__.register(A,"isEmail","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonValidate.js"),__REACT_HOT_LOADER__.register(C,"isCorrectOrderCode","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonValidate.js"),__REACT_HOT_LOADER__.register(I,"isChinese","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonValidate.js"),__REACT_HOT_LOADER__.register(i,"isCellPhone","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonValidate.js"),__REACT_HOT_LOADER__.register(r,"isCorrectVerifyCode","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonValidate.js"),__REACT_HOT_LOADER__.register(o,"isFormatBirthday","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonValidate.js"))}()},17:function(module,exports){var ryan=function(){var O=function(){},fn=O.prototype;return fn.METHOD=["GET","POST","PUT","DELETE","OPTIONS","HEAD","TRACE","CONNECT"],fn.getXHR=function(){var e;try{e=new XMLHttpRequest}catch(t){try{e=new ActiveXObject("Msxml2.XMLHTTP")}catch(t){try{e=new ActiveXObject("Microsoft.XMLHTTP")}catch(t){throw e=!1,new Error("您的浏览器不支持 XHR 对象！")}}}return e},fn.params=function(e){var t=[];for(var g in e){var n=encodeURIComponent(g)+"="+encodeURIComponent(e[g]);t.push(n)}return t.join("&")},fn.isEmpty=function(e){return/^[\s\r\t\n]*$/.test(e)},fn.initOptions=function(e){var t={},g="rand="+Math.random();return t.url=e.url,t.type=e.type||"get",t.data=e.data||"",t.dataType=e.dataType||"json",t.async=e.async||!0,t.success=e.success||function(){},t.error=e.error||function(){},t.data=this.params(t.data),"get"===t.type.toLowerCase()&&(this.isEmpty(t.data)?t.url=-1===t.url.indexOf("?")?t.url+"?"+g:t.url+"&"+g:t.url=-1===t.url.indexOf("?")?t.url+"?"+t.data+"&"+g:t.url+"&"+t.data+"&"+g),t},fn.ajax=function(options){options=this.initOptions(options);var xhr=this.getXHR(),callback=function callback(){if(200===xhr.status){var res;if("json"===options.dataType)try{res=JSON&&JSON.parse?JSON.parse(xhr.responseText):eval("("+xhr.responseText+")")}catch(e){res=xhr.responseText}else res=xhr.responseText;options.success(res)}else{try{res=JSON&&JSON.parse?JSON.parse(xhr.responseText):eval("("+xhr.responseText+")")}catch(e){res=xhr.responseText}options.error({response:res,status:xhr.status,xhr:xhr})}};!0===options.async&&(xhr.onreadystatechange=function(){4===xhr.readyState&&callback()}),xhr.open(options.type,options.url,options.async),"post"===options.type.toLowerCase()?(xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),xhr.send(options.data)):xhr.send(null),!1===options.async&&callback()},new O}();module.exports=ryan;var _temp=function(){"undefined"!=typeof __REACT_HOT_LOADER__&&__REACT_HOT_LOADER__.register(ryan,"ryan","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonAjax.js")}()},213:function(e,t,g){"use strict";g.d(t,"a",function(){return n});var n={SET_ORDER_DATA:"SET_ORDER_DATA"};!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&__REACT_HOT_LOADER__.register(n,"ACTION_TYPES","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/constants/order.js")}()},29:function(e,t,g){"use strict";g.d(t,"i",function(){return C}),g.d(t,"f",function(){return I}),g.d(t,"g",function(){return i}),g.d(t,"n",function(){return r}),g.d(t,"e",function(){return o}),g.d(t,"b",function(){return a}),g.d(t,"c",function(){return s}),g.d(t,"a",function(){return c}),g.d(t,"m",function(){return l}),g.d(t,"l",function(){return d}),g.d(t,"k",function(){return h}),g.d(t,"j",function(){return m}),g.d(t,"h",function(){return p}),g.d(t,"d",function(){return f});var n=g(71),A="xuyuandiandeng",C="祈福点灯",I=A+"_long",i=A+"_short",r=A+"_history_orders",o=A,a="other",s=0,c="zh-cn",l="3",d=101,h=["linghit"],m="55",u="/api/v2/"+A+"/",p={LINGHit:"http://m.lingji666.com/",INDEX:"/xuyuandiandeng/index.html",ORDER:"/xuyuandiandeng/order.html",RESULT:"/xuyuandiandeng/result.html",PAY:"/xuyuandiandeng/pay.html",INDIVIDUAL_PRIVACY:"/individualPrivacy/index",MLINGHIT:"https://m.lingji666.com/",MASTER_VIDEO:"https://v.qq.com/iframe/player.html?vid=u0563byxdxo&tiny=0&auto=0",USER_PAY_CONFIRM:"/nativePayConfirm/index"},f={FETCH_PAGE_CONFIG:u+"config",REGISTER_ORDER:"/api/v2/orders/register",FETCH_ORDER_INFO:"/api/v2/orders/query",FETCH_PAY:"/api/v4/orders/pay/get",FETCH_LANTERN_LIST:u+"lantern/list",FETCH_LANTERN_DEFAULT:u+"lantern/default",FETCH_NEWEST_LANTERNS:u+"like/list/newest",FETCH_WEEK_LANTERNS:u+"like/list/week",FETCH_BRIGHTEST_LANTERNS:u+"like/list/total",FETCH_LANTERN_DETAIL:u+"lantern/detail",FETCH_RESULT:u+"result",LIKE_COUNT:u+"like/do",FETCH_MENU:"/api/v1/page/menu.json",FETCH_NEW_MENU:"/api/v1/page/newmenu.json",FETCH_LANG:"/api/v1/page/location.json",PUBLIC_ASSESS_COUPON:n.a.PUBLIC_ASSESS_COUPON,FETCH_COMMENT:"/api/v1/page/config",TRACK_INFO:n.a.TRACK_INFO,TRACK_SCAN:n.a.TRACK_SCAN,TRACK_REGISTER:n.a.TRACK_REGISTER,TRACK_PAY:n.a.TRACK_PAY};!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(A,"BASE_NAME","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(C,"APP_NAME","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register("灵机文化","COMPANY_NAME","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(I,"PAY_POINT_LONG","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(i,"PAY_POINT_SHORT","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(r,"HISTORY_ORDERS","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register("十年劫数","HISTORY_ORDERS_NAME","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(o,"MARK","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(a,"DEFAULT_CHANNEL","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(s,"DEFAULT_LAP","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(c,"DEFAULT_LANG","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register("xuyuandiandeng","FORECAST_NAME","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(l,"USER_TYPE","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(d,"APP_PAY_VERSION","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(h,"APP_UA_MARKS","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(m,"SERVER_ID","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register("/xuyuandiandeng/","pageCommonPath","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(u,"apiCommonPath","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register("/api/v2/orders/","orderPath","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(p,"PAGE_URL","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"),__REACT_HOT_LOADER__.register(f,"API_URL","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/service/config.js"))}()},321:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function I(e){return{orderData:e.orderData}}function i(e){return{actions:g.i(a.b)(c,e)}}var r=g(2),o=g.n(r),a=g(114),s=g(212),c=g(332),l=g(702),d=(g.n(l),g(29)),h=g(13),m=g(17),u=g.n(m),p=g(10),f=g.n(p),_=g(35),v=g.n(_),y=g(73),b=g(74),E=g(329),T=g(99),w=g(338),x=g(339),D=g(337),R=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),S=function(e){function t(e){n(this,t);var C=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return C.props=e,C.pageConfig=new y.a(C,f.a.getItem("_channel")),C.state={lang:g.i(h.a)("lang")||f.a.getItem("_lang")||d.a,channel:g.i(h.a)("channel")||f.a.getItem("_channel")||d.b,lap:g.i(h.a)("lap")||f.a.getItem("_lap")||d.c,sem:g.i(h.a)("sem")||f.a.getItem("_sem"),lanternId:parseInt(g.i(h.a)("lantern_id"))||1,fixedBtnDisplayFlag:!1,pageConfig:y.a.getDefaultData()},C}return C(t,e),R(t,[{key:"componentWillMount",value:function(){this.preSet()}},{key:"componentDidUpdate",value:function(){this.setLang()}},{key:"componentDidMount",value:function(){var e={url:d.d.FETCH_PAGE_CONFIG,data:{channel:this.state.channel},mark:d.e};this.pageConfig.getConfigData(e),g.i(h.g)(),this.fetchAndSetLang(),this.setLang(),this.setFixedBtnStyle(),this.wrapperOnScrollHandler(),this.fetchLanternDetailData(),g.i(b.a)()}},{key:"wrapperOnScrollHandler",value:function(){var e=this,t=document.getElementsByTagName("body")[0],n=document.getElementById("wrapper"),A=document.getElementById("popupBtn"),C=function(){var g=t.scrollTop?t:document.documentElement,C=g.scrollTop,I=n.clientWidth,i=void 0;A.style.width=I+"px",i=C>=e.fixedScrollHookPosition,e.fixedBtnDisplayFlag!==i&&(e.fixedBtnDisplayFlag=i,A.style.display=i?"block":"none")};window.addEventListener?window.addEventListener("scroll",g.i(h.h)(C,300),!1):window.attachEvent&&window.attachEvent("onscroll",g.i(h.h)(C,300))}},{key:"goToPayHook",value:function(){0!=this.state.activeNavTab?(this.setState({activeNavTab:0}),setTimeout(function(){g.i(h.b)("registry-btn-hook",document.getElementsByTagName("body")[0])},100)):g.i(h.b)("registry-btn-hook",document.getElementsByTagName("body")[0])}},{key:"setFixedBtnStyle",value:function(){if(this.state.fixedBtnDisplayFlag){var e=document.getElementById("wrapper"),t=e.clientWidth;document.getElementById("popupBtn").style.width=t+"px"}}},{key:"componentWillReceiveProps",value:function(e){var t=f.a.getItem("_lang");t=t||d.a,v.a.convertTextByLang(t)}},{key:"setLang",value:function(){var e=this.state.lang;e=e||f.a.getItem("_lang"),v.a.convertTextByLang(e)}},{key:"preSet",value:function(){var e=this.state,t=e.lap,n=e.channel;f.a.setItem("_lap",t,1/0,"/"),f.a.setItem("_channel",n,1/0,"/"),g.i(h.c)(f.a,"lang","_lang",1/0),g.i(h.c)(f.a,"code","_activity_coupon_code",1/0)}},{key:"fetchAndSetLang",value:function(){var e=g.i(h.d)()||f.a.getItem("_lang"),t=this.props.orderData,n=this.props.actions.setOrderData;e?(e=g.i(h.e)(e,d.a),n({lang:g.i(h.e)(e)}),f.a.setItem("_lang",e,86400,"/")):u.a.ajax({url:d.d.FETCH_LANG,type:"GET",success:function(t){e=g.i(h.e)(t&&t.language,d.a),n({lang:g.i(h.e)(e)}),f.a.setItem("_lang",e,86400,"/")},error:function(){e=g.i(h.e)(void 0,d.a),n({lang:g.i(h.e)(e)}),f.a.setItem("_lang",e,30,"/")}}),n({datePickerBirth:"zh-cn"!==e?t.datePickerBirthTradtion:t.datePickerBirth})}},{key:"fetchLanternDetailData",value:function(){var e=this;u.a.ajax({url:d.d.FETCH_LANTERN_DETAIL,type:"GET",data:{lantern_id:this.state.lanternId},success:function(t){t.id&&e.setState({lanternDetail:t}),t.code&&e.setTipShowAndHide(t.msg)},error:function(e){BJ_REPORT.report({msg:d.d.FETCH_LANTERN_DETAIL+":"+JSON.stringify(e),target:"Order.jsx",rowNum:185})}})}},{key:"render",value:function(){var e=this,t=this.state,g=this.state.lanternDetail,n=this.props.orderData,A=this.props.actions.setOrderData,C=t.pageConfig,I=C.handle,i=C.content;return o.a.createElement("section",null,0==t.lap&&I.header&&o.a.createElement(T.a,{channel:t.channel,forecastName:d.i}),g&&o.a.createElement("div",{className:"banner"},o.a.createElement("div",{className:"lantern"},o.a.createElement("div",{className:"lantern-on"},o.a.createElement("img",{src:g.img_light,alt:""}))),o.a.createElement("div",{className:"lantern-desc"},o.a.createElement("img",{src:g.title_img,alt:""}))),o.a.createElement("div",{className:"title-tips"},o.a.createElement("h5",null,"温馨提示：除了自己点灯，也可为亲友点灯祈福哦")),o.a.createElement("div",{id:"registry-btn-hook"}),o.a.createElement(w.a,{orderData:n,setOrderData:A}),o.a.createElement("div",{id:"fixed-scroll-hook",style:{height:"1px"},ref:function(t){t&&setTimeout(function(){e.fixedScrollHookPosition=t.offsetTop},500)}}),o.a.createElement(x.a,{lanternDetail:t.lanternDetail}),o.a.createElement(D.a,null),I.footer&&o.a.createElement("div",{dangerouslySetInnerHTML:{__html:i.footer}}),o.a.createElement(E.a,{year:"1985",month:"01",day:"01",startYear:"1910",data:n,setState:A}),o.a.createElement("div",{className:"popupBtn",id:"popupBtn",style:{display:"none"},onClick:this.goToPayHook.bind(this)}))}}]),t}(r.Component),O=g.i(s.b)(I,i)(S);t.a=O;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(S,"Order","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/containers/Order.jsx"),__REACT_HOT_LOADER__.register(I,"mapStateToProps","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/containers/Order.jsx"),__REACT_HOT_LOADER__.register(i,"mapDispatchToProps","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/containers/Order.jsx"),__REACT_HOT_LOADER__.register(O,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/containers/Order.jsx"))}()},324:function(e,t,g){"use strict";function n(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:I,t=arguments[1];switch(t.type){case C.a.SET_ORDER_DATA:return Object.assign({},e,t.orderData);default:return e}}var A=g(114),C=g(213),I={datePickerBirth:"",datePickerBirthTradition:"",datePickerDisplay:0,mode:0,datePickerTimeConfirm:1,datePickerBirthFormat:"",birthTimeValue:"时辰未知",birthTimeDataId:0,birthTimeDataValue:-1,lang:void 0},i=g.i(A.c)({orderData:n});t.a=i;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(I,"init","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/reducers/order.js"),__REACT_HOT_LOADER__.register(n,"orderData","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/reducers/order.js"),__REACT_HOT_LOADER__.register(i,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/reducers/order.js"))}()},329:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(2),i=g.n(I),r=g(330),o=g.n(r),a=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),s=i.a.createClass({displayName:"OneDateItem",render:function(){var e=this.props,t=e.s,g=e.data,n=e.arrow;return i.a.createElement("li",{className:g.className,style:Object.assign({},t.rdp_item_li,n?t["rdp_item_li_"+g.className+"_arrow"]:t["rdp_item_li_"+g.className])},g.text?g.text:"　")}});"document"in self&&("classList"in document.createElement("_")&&(!document.createElementNS||"classList"in document.createElementNS("http://www.w3.org/2000/svg","g"))||function(e){if("Element"in e){var t="classList",g="prototype",n=e.Element[g],A=Object,C=String[g].trim||function(){return this.replace(/^\s+|\s+$/g,"")},I=Array[g].indexOf||function(e){for(var t=0,g=this.length;g>t;t++)if(t in this&&this[t]===e)return t;return-1},i=function(e,t){this.name=e,this.code=DOMException[e],this.message=t},r=function(e,t){if(""===t)throw new i("SYNTAX_ERR","The token must not be empty.");if(/\s/.test(t))throw new i("INVALID_CHARACTER_ERR","The token must not contain space characters.");return I.call(e,t)},o=function(e){for(var t=C.call(e.getAttribute("class")||""),g=t?t.split(/\s+/):[],n=0,A=g.length;A>n;n++)this.push(g[n]);this._updateClassName=function(){e.setAttribute("class",this.toString())}},a=o[g]=[],s=function(){return new o(this)};if(i[g]=Error[g],a.item=function(e){return this[e]||null},a.contains=function(e){return~r(this,e+"")},a.add=function(){var e,t=arguments,g=0,n=t.length,A=!1;do{e=t[g]+"",~r(this,e)||(this.push(e),A=!0)}while(++g<n);A&&this._updateClassName()},a.remove=function(){var e,t,g=arguments,n=0,A=g.length,C=!1;do{for(e=g[n]+"",t=r(this,e);~t;)this.splice(t,1),C=!0,t=r(this,e)}while(++n<A);C&&this._updateClassName()},a.toggle=function(e,t){var g=this.contains(e),n=g?!0!==t&&"remove":!1!==t&&"add";return n&&this[n](e),!0===t||!1===t?t:!g},a.replace=function(e,t){var g=r(e+"");~g&&(this.splice(g,1,t),this._updateClassName())},a.toString=function(){return this.join(" ")},A.defineProperty){var c={get:s,enumerable:!0,configurable:!0};try{A.defineProperty(n,t,c)}catch(e){void 0!==e.number&&-2146823252!==e.number||(c.enumerable=!1,A.defineProperty(n,t,c))}}else A[g].__defineGetter__&&n.__defineGetter__(t,s)}}(self),function(){var e=document.createElement("_");if(e.classList.add("c1","c2"),!e.classList.contains("c2")){var t=function(e){var t=DOMTokenList.prototype[e];DOMTokenList.prototype[e]=function(e){var g,n=arguments.length;for(g=0;n>g;g++)e=arguments[g],t.call(this,e)}};t("add"),t("remove")}if(e.classList.toggle("c3",!1),e.classList.contains("c3")){var g=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(e,t){return 1 in arguments&&!this.contains(e)==!t?t:g.call(this,e)}}"replace"in document.createElement("_").classList||(DOMTokenList.prototype.replace=function(e,t){var g=this.toString().split(" "),n=g.indexOf(e+"");~n&&(g=g.slice(n),this.remove.apply(this,g),this.add(t),this.add.apply(this,g.slice(1)))}),e=null}());var c=function(e){function t(e){n(this,t);var g=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));g.props=e;var C=new Date,I=e.data.year||e.maxYear||C.getFullYear(),i=e.data.maxMonth||e.maxMonth||C.getMonth(),r=e.data.maxDate||e.maxDate||C.getDate(),o=new Date(parseInt(I,10),parseInt(i,10),parseInt(r,10)),a=e.data.datePickerBirthFormat||"1985070100",s=parseInt(a.substr(0,4),10)||parseInt(e.year,10),c=parseInt(a.substr(4,2),10)||parseInt(e.month,10),l=parseInt(a.substr(6,2),10)||parseInt(e.day,10),d=o.getFullYear(),h=parseInt(e.data.startYear||e.startYear,10);h=h&&h<d?h:1910;var m=s&&s>=h&&s<=d?s:h,u=c&&c>=1&&c<=12?c:1,p=g.getDaysByYearAndMonth(m,u),f=l&&l>=1&&l<=p?l:1;return g.timestamp=Date.now()+Math.floor(100*Math.random()),g.scrollTop=0,g.scrollHeight=0,g.isMoblie=function(){var e=navigator.userAgent;return["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"].some(function(t){return new RegExp(t,"i").test(e)})}(),g.state={mode:0,minYear:1890,maxYear:2100,MaxDate:o,selectedYear:m,selectedMonth:u,selectedDay:f,selectedTime:"时辰不清楚",selectedTimeValue:0,selectedTimeId:0,solarYear:m,solarMonth:u,solarDay:f,startYear:h,endYear:o.getFullYear(),yearData:[],monthData:[],dayData:[],timeData:[],iScrollInitConfig:g.isMoblie?{startY:0,defaultScrollbars:"horizontal"}:{mouseWheel:!0,mouseWheelSpeed:1,startY:0,snap:"li",defaultScrollbars:"horizontal"},yearIScroll:null,monthIScroll:null,dayIScroll:null,activeTabIndex:0},g}return C(t,e),a(t,[{key:"lunarRecord",value:function(){return[[2,1,21,22184],[0,2,9,21936],[6,1,30,9656],[0,2,17,9584],[0,2,6,21168],[5,1,26,43344],[0,2,13,59728],[0,2,2,27296],[3,1,22,44368],[0,2,10,43856],[8,1,30,19304],[0,2,19,19168],[0,2,8,42352],[5,1,29,21096],[0,2,16,53856],[0,2,4,55632],[4,1,25,27304],[0,2,13,22176],[0,2,2,39632],[2,1,22,19176],[0,2,10,19168],[6,1,30,42200],[0,2,18,42192],[0,2,6,53840],[5,1,26,54568],[0,2,14,46400],[0,2,3,54944],[2,1,23,38608],[0,2,11,38320],[7,2,1,18872],[0,2,20,18800],[0,2,8,42160],[5,1,28,45656],[0,2,16,27216],[0,2,5,27968],[4,1,24,44456],[0,2,13,11104],[0,2,2,38256],[2,1,23,18808],[0,2,10,18800],[6,1,30,25776],[0,2,17,54432],[0,2,6,59984],[5,1,26,27976],[0,2,14,23248],[0,2,4,11104],[3,1,24,37744],[0,2,11,37600],[7,1,31,51560],[0,2,19,51536],[0,2,8,54432],[6,1,27,55888],[0,2,15,46416],[0,2,5,22176],[4,1,25,43736],[0,2,13,9680],[0,2,2,37584],[2,1,22,51544],[0,2,10,43344],[7,1,29,46248],[0,2,17,27808],[0,2,6,46416],[5,1,27,21928],[0,2,14,19872],[0,2,3,42416],[3,1,24,21176],[0,2,12,21168],[8,1,31,43344],[0,2,18,59728],[0,2,8,27296],[6,1,28,44368],[0,2,15,43856],[0,2,5,19296],[4,1,25,42352],[0,2,13,42352],[0,2,2,21088],[3,1,21,59696],[0,2,9,55632],[7,1,30,23208],[0,2,17,22176],[0,2,6,38608],[5,1,27,19176],[0,2,15,19152],[0,2,3,42192],[4,1,23,53864],[0,2,11,53840],[8,1,31,54568],[0,2,18,46400],[0,2,7,46752],[6,1,28,38608],[0,2,16,38320],[0,2,5,18864],[4,1,25,42168],[0,2,13,42160],[10,2,2,45656],[0,2,20,27216],[0,2,9,27968],[6,1,29,44448],[0,2,17,43872],[0,2,6,38256],[5,1,27,18808],[0,2,15,18800],[0,2,4,25776],[3,1,23,27216],[0,2,10,59984],[8,1,31,27432],[0,2,19,23232],[0,2,7,43872],[5,1,28,37736],[0,2,16,37600],[0,2,5,51552],[4,1,24,54440],[0,2,12,54432],[0,2,1,55888],[2,1,22,23208],[0,2,9,22176],[7,1,29,43736],[0,2,18,9680],[0,2,7,37584],[5,1,26,51544],[0,2,14,43344],[0,2,3,46240],[4,1,23,46416],[0,2,10,44368],[9,1,31,21928],[0,2,19,19360],[0,2,8,42416],[6,1,28,21176],[0,2,16,21168],[0,2,5,43312],[4,1,25,29864],[0,2,12,27296],[0,2,1,44368],[2,1,22,19880],[0,2,10,19296],[6,1,29,42352],[0,2,17,42208],[0,2,6,53856],[5,1,26,59696],[0,2,13,54576],[0,2,3,23200],[3,1,23,27472],[0,2,11,38608],[11,1,31,19176],[0,2,19,19152],[0,2,8,42192],[6,1,28,53848],[0,2,15,53840],[0,2,4,54560],[5,1,24,55968],[0,2,12,46496],[0,2,1,22224],[2,1,22,19160],[0,2,10,18864],[7,1,30,42168],[0,2,17,42160],[0,2,6,43600],[5,1,26,46376],[0,2,14,27936],[0,2,2,44448],[3,1,23,21936],[0,2,11,37744],[8,2,1,18808],[0,2,19,18800],[0,2,8,25776],[6,1,28,27216],[0,2,15,59984],[0,2,4,27424],[4,1,24,43872],[0,2,12,43744],[0,2,2,37600],[3,1,21,51568],[0,2,9,51552],[7,1,29,54440],[0,2,17,54432],[0,2,5,55888],[5,1,26,23208],[0,2,14,22176],[0,2,3,42704],[4,1,23,21224],[0,2,11,21200],[8,1,31,43352],[0,2,19,43344],[0,2,7,46240],[6,1,27,46416],[0,2,15,44368],[0,2,5,21920],[4,1,24,42448],[0,2,12,42416],[0,2,2,21168],[3,1,22,43320],[0,2,9,26928],[7,1,29,29336],[0,2,17,27296],[0,2,6,44368],[5,1,26,19880],[0,2,14,19296],[0,2,3,42352],[4,1,24,21104],[0,2,10,53856],[8,1,30,59696],[0,2,18,54560],[0,2,7,55968],[6,1,27,27472],[0,2,15,22224],[0,2,5,19168],[4,1,25,42216],[0,2,12,42192],[0,2,1,53584],[2,1,21,55592],[0,2,9,54560]]}},{key:"errorCode",value:function(){return{100:"输入的年份超过了可查询范围，仅支持1891至2100年",101:"参数输入错误，请查阅文档"}}},{key:"lunarData",value:function(){return{TIAN_GAN:["甲","乙","丙","丁","戊","己","庚","辛","壬","癸"],DI_ZHI:["子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"],SHENG_XIAO:["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"],JIE_QI:["小寒","大寒","立春","雨水","惊蛰","春分","清明","谷雨","立夏","小满","芒种","夏至","小暑","大暑","立秋","处暑","白露","秋分","寒露","霜降","立冬","小雪","大雪","冬至"],NONG_LI_YUE:["正","二","三","四","五","六","七","八","九","十","十一","十二"],NONG_LI_RI:["初一","初二","初三","初四","初五","初六","初七","初八","初九","初十","十一","十二","十三","十四","十五","十六","十七","十八","十九","二十","廿一","廿二","廿三","廿四","廿五","廿六","廿七","廿八","廿九","三十","卅一"],SHI_CENG:[{id:0,value:-1,text:"时辰未知",time:"时辰未知",time_tw:"時辰未知"},{id:1,value:0,text:"00:00-00:59(早子)",time:"早子时",time_tw:"早子時"},{id:2,value:1,text:"01:00-01:59(丑)",time:"丑时",time_tw:"丑時"},{id:3,value:2,text:"02:00-02:59(丑)",time:"丑时",time_tw:"丑時"},{id:4,value:3,text:"03:00-03:59(寅)",time:"寅时",time_tw:"寅時"},{id:5,value:4,text:"04:00-04:59(寅)",time:"寅时",time_tw:"寅時"},{id:6,value:5,text:"05:00-05:59(卯)",time:"卯时",time_tw:"卯時"},{id:7,value:6,text:"06:00-06:59(卯)",time:"卯时",time_tw:"卯時"},{id:8,value:7,text:"07:00-07:59(辰)",time:"辰时",time_tw:"辰時"},{id:9,value:8,text:"08:00-08:59(辰)",time:"辰时",time_tw:"辰時"},{id:10,value:9,text:"09:00-09:59(巳)",time:"巳时",time_tw:"巳時"},{id:11,value:10,text:"10:00-10:59(巳)",time:"巳时",time_tw:"巳時"},{id:12,value:11,text:"11:00-11:59(午)",time:"午时",time_tw:"午時"},{id:13,value:12,text:"12:00-12:59(午)",time:"午时",time_tw:"午時"},{id:14,value:13,text:"13:00-13:59(未)",time:"未时",time_tw:"未時"},{id:15,value:14,text:"14:00-14:59(未)",time:"未时",time_tw:"未時"},{id:16,value:15,text:"15:00-15:59(申)",time:"申时",time_tw:"申時"},{id:17,value:16,text:"16:00-16:59(申)",time:"申时",time_tw:"申時"},{id:18,value:17,text:"17:00-17:59(酉)",time:"酉时",time_tw:"酉時"},{id:19,value:18,text:"18:00-18:59(酉)",time:"酉时",time_tw:"酉時"},{id:20,value:19,text:"19:00-19:59(戌)",time:"戌时",time_tw:"戌時"},{id:21,value:20,text:"20:00-20:59(戌)",time:"戌时",time_tw:"戌時"},{id:22,value:21,text:"21:00-21:59(亥)",time:"亥时",time_tw:"亥時"},{id:23,value:22,text:"22:00-22:59(亥)",time:"亥时",time_tw:"亥時"},{id:24,value:23,text:"23:00-23:59(晚子)",time:"晚子时",time_tw:"晚子時"}]}}}]),a(t,[{key:"getLunarLeapMonth",value:function(e){return this.lunarRecord()[e-this.state.minYear][0]}},{key:"getLunarDateByDaysBetweenFirstDayOfLunar",value:function(e,t,g){var n=this.lunarRecord()[e-this.state.minYear],A=n[1],C=n[2],I=this.getDaysBetweenTwoSolarDate(e,A-1,C,e,t,g);if(0===I)return[e,0,1];var i=I>0?e:e-1;return this.getLunarDateByBetweenDays(i,I)}},{key:"getLunarDateByBetweenDays",value:function(e,t){for(var g=this.getLunarDaysOfMonthsAndYearDays(e),n=t>0?t:g.yearDays-Math.abs(t),A=g.monthDays,C=0,I=0,i=0;i<A.length;i++)if((C+=A[i])>n){I=i,C-=A[i];break}return[e,I,n-C+1]}},{key:"getDaysBetweenTwoSolarDate",value:function(e,t,g,n,A,C){var I=new Date(e,t,g).getTime(),i=new Date(n,A,C).getTime();return Math.round((i-I)/864e5)}},{key:"getLunarDaysOfMonthsAndYearDays",value:function(e){var t=this.lunarRecord()[e-this.state.minYear],g=t[0],n=g?13:12,A=t[3].toString(2),C=A.split(""),I=16-C.length,i=void 0,r=0,o=void 0,a=[];for(i=0;i<I;i++)C.unshift(0);for(o=0;o<n;o++)0==C[o]?(r+=29,a.push(29)):(r+=30,a.push(30));return{yearDays:r,monthDays:a}}},{key:"getDaysBetweenFirstDayOfLunar",value:function(e,t,g){for(var n=this.getLunarDaysOfMonthsAndYearDays(e),A=n.monthDays,C=0,I=0;I<A.length&&I<t;I++)C+=A[I];return C+g-1}},{key:"formatDate",value:function(e,t,g){var n=new Date;return e=e?parseInt(e,10):n.getFullYear(),t=t?parseInt(t-1,10):n.getMonth(),g=g?parseInt(g,10):n.getDate(),e<this.state.minYear||e>this.state.maxYear?{error:this.errorCode()[100]}:{year:e,month:t,day:g}}},{key:"lunarToSolar",value:function(e,t,g){var n=this.formatDate(e,t,g);if(n.ERROR)return n;e=n.year,t=n.month,g=n.day;var A=this.getDaysBetweenFirstDayOfLunar(e,t,g),C=this.lunarRecord()[e-this.state.minYear],I=C[1],i=C[2],r=new Date(e,I-1,i).getTime()+864e5*A;return r=new Date(r),{year:Math.round(r.getFullYear()),month:Math.round(r.getMonth()+1),day:Math.round(r.getDate())}}},{key:"solarToLunar",value:function(e,t,g){var n=this.formatDate(e,t,g);if(n.error)return n;e=n.year,t=n.month,g=n.day;var A=this.getLunarDateByDaysBetweenFirstDayOfLunar(e,t,g),C=Math.round(A[0]),I=Math.round(A[1]),i=Math.round(A[2]),r=this.getLunarLeapMonth(C),o="",a="";return o=r>0&&r==I?"闰"+this.lunarData().NONG_LI_YUE[I-1]+"月":r>0&&I>r?this.lunarData().NONG_LI_YUE[I-1]+"月":this.lunarData().NONG_LI_YUE[I]+"月",a=this.lunarData().NONG_LI_RI[i-1],{lunarYear:Math.round(C),lunarMonth:Math.round(I+1),lunarDay:Math.round(i),lunarMonthName:o,lunarDayName:a}}},{key:"generatorYearData",value:function(e){for(var t=[],g=this.state.startYear,n=this.state.endYear,A=g;A<=n;A++){A==g&&(t.push({value:""}),t.push({value:""}));var C={};C.value=A,C.text=A,C.className=e===A?"current":"",t.push(C),A==n&&(t.push({value:""}),t.push({value:""}))}return this.setState({yearData:t}),t}},{key:"generatorTimeData",value:function(e){var t=[{id:!1,value:!1,text:""},{id:!1,value:!1,text:""}],g=this.lunarData(),n=g.SHI_CENG.map(function(t,g){return g===e&&(t.className="current"),t});t=t.concat(n,t),this.setState({timeData:t})}},{key:"generateLunarMonthsData",value:function(e,t){var g=this,n=this.getLunarDaysOfMonthsAndYearDays(e),A=this.getLunarLeapMonth(e),C=this.lunarData().NONG_LI_YUE,I=void 0;return I=n.monthDays.map(function(n,I){var i="",r=void 0;return r=g.selectedLunarMonthWhetherLargeThanCurrentLunarMonth(e,I+1)?"prevent":void 0,i=A?A===I?"闰"+C[I-1]+"月":A<I?C[I-1]+"月":C[I]+"月":C[I]+"月",t===I+1&&(r="current"),{text:i,value:I+1,className:r}}),I.unshift({}),I.unshift({}),I.push({}),I.push({}),this.setState({monthData:I}),I}},{key:"generateLunarDaysData",value:function(e,t,g){var n=this.getLunarDaysOfMonthsAndYearDays(e),A=this.lunarData().NONG_LI_RI,C=n.monthDays[t-1],I=[];C||(C=n.monthDays[t-2]);for(var i=0;i<C;i++){var r=void 0,o=void 0;g===i+1&&(o="current"),o=this.selectedLunarDayWhetherLargeThanCurrentLunarDay(e,t,i+1)?"prevent":o,r={text:A[i],value:i+1,className:o},I.push(r)}return I.unshift({}),I.unshift({}),I.push({}),I.push({}),this.setState({dayData:I}),I}},{key:"selectedYearWhetherLargeCurrentYear",value:function(e){return(e=e||this.state.selectedYear)>this.state.MaxDate.getFullYear()}},{key:"selectedLunarMonthWhetherLargeThanCurrentLunarMonth",value:function(e,t){e=e||this.state.selectedYear,t=t||this.state.selectedMonth;var g=this.state.MaxDate,n=g.getFullYear(),A=g.getMonth()+1,C=g.getDate(),I=this.solarToLunar(n,A,C);return this.selectedYearWhetherLargeCurrentYear(e)||e===I.lunarYear&&t>I.lunarMonth}},{key:"selectedLunarDayWhetherLargeThanCurrentLunarDay",value:function(e,t,g){e=e||this.state.selectedYear,t=t||this.state.selectedMonth,g=g||this.state.selectedDay;var n=this.state.MaxDate,A=n.getFullYear(),C=n.getMonth()+1,I=n.getDate(),i=this.solarToLunar(A,C,I);return this.selectedLunarMonthWhetherLargeThanCurrentLunarMonth(e,t)||e===i.lunarYear&&t===i.lunarMonth&&g>i.lunarDay}},{key:"generatorMonthData",value:function(e,t){e=parseInt(e,10),t=t||this.state.selectedYear;for(var g=[],n=this.state.MaxDate,A=n.getFullYear(),C=n.getMonth()+1,I=1;I<=12;I++){var i={};i.value=I,i.text=I,i.className=I===e?"current":"",A==t&&I>C&&(i.className="prevent"),1910===t&&I<2&&(i.className="prevent"),g.push(i)}return g.unshift({}),g.unshift({}),g.push({}),g.push({}),this.setState({monthData:g}),g}},{key:"isLeap",value:function(e){return 29===new Date(e,1,29).getDate()}},{key:"getDaysByYearAndMonth",value:function(e,t){var g=void 0;switch(t){case 1:case 3:case 5:case 7:case 8:case 10:case 12:g=31;break;case 4:case 6:case 9:case 11:g=30}return 2===t&&(g=this.isLeap(e)?29:28),g}},{key:"generatorDayData",value:function(e,t,g){e=parseInt(e,10);var n=g||this.state.selectedYear,A=t||this.state.selectedMonth,C=void 0,I=[],i=this.state.MaxDate,r=i.getFullYear(),o=i.getMonth()+1,a=i.getDate();C=this.getDaysByYearAndMonth(n,A);for(var s=1;s<=C;s++){var c={};c.value=s,c.text=s,c.className=s===e?"current":"",n==r&&A==o&&s>a&&(c.className="prevent"),1910===g&&2===t&&s<10&&(c.className="prevent"),I.push(c)}return I.unshift({}),I.unshift({}),I.push({}),I.push({}),this.setState({dayData:I}),I}},{key:"selectedMonthWhetherLargeThanCurrentMonth",value:function(e,t){e=e||this.state.selectedYear,t=t||this.state.selectedMonth;var g=this.state.MaxDate,n=g.getFullYear(),A=g.getMonth()+1;return e==n&&t>A}},{key:"selectedDayWhetherLargeThanCurrentDay",value:function(e,t,g){e=e||this.state.selectedYear,t=t||this.state.selectedMonth,g=g||this.state.selectedDay;var n=this.state.MaxDate,A=n.getFullYear(),C=n.getMonth()+1,I=n.getDate();return e==A&&t==C&&g>I}},{key:"getMaxDate",value:function(){var e=this.state.MaxDate,t=e.getFullYear(),g=e.getMonth()+1,n=e.getDate();return{name:"maxDate",maxSolar:{solarYear:t,solarMonth:g,solarDay:n},maxLunar:this.solarToLunar(t,g,n)}}},{key:"dateIScrollHandle",value:function(){var e=this,t=e.state.iScrollInitConfig,g=document.getElementById("wrapper-year-iscroll"+this.timestamp),n=new o.a(g,t),A=document.getElementById("wrapper-month-iscroll"+this.timestamp),C=new o.a(A,t),I=document.getElementById("wrapper-day-iscroll"+this.timestamp),i=new o.a(I,t),r=document.getElementById("wrapper-time-iscroll"+this.timestamp),a=new o.a(r,t);return n.on("scrollEnd",function(){var t=0;if(e.isMoblie){var g=e.scrollHeight,A=this.y,I=Math.abs(this.y%g);I-g/2>=0?A-=g-I:A+=I,t=Math.round(Math.abs(A/g))}else t=this.currentPage.pageY;var r=e.state,o=r.startYear+t,a=r.selectedMonth,s=r.selectedDay,c=e.getMaxDate();o>r.endYear&&(o=r.endYear),o<r.startYear&&(o=r.startYear);var l=r.mode,d=void 0,h=void 0,m=void 0;if(0===l)1910===o&&a<2&&(a=2),1910===o&&2===a&&s<10&&(s=10),a=e.selectedMonthWhetherLargeThanCurrentMonth(o,a)?c.maxSolar.solarMonth:a,s=e.selectedDayWhetherLargeThanCurrentDay(o,a,s)?c.maxSolar.solarDay:s;else if(1===l){a=e.selectedLunarMonthWhetherLargeThanCurrentLunarMonth(o,a)?c.maxLunar.lunarMonth:a,s=e.selectedLunarDayWhetherLargeThanCurrentLunarDay(o,a,s)?c.maxLunar.lunarDay:s;var u=c.maxLunar.lunarYear;o=o>u?u:o;var p=e.getLunarDaysOfMonthsAndYearDays(o),f=p.monthDays,_=f.length;a=a>_?_:a;var v=f[a-1];s=s>v?v:s}if(0===l){e.generatorYearData(o),e.generatorMonthData(a,o);var y=e.generatorDayData(s,a,o),b=y[y.length-1-2].value;s=b<s?b:s,d=o,h=a,m=s}else if(1===l){e.generatorYearData(o),e.generateLunarMonthsData(o,a);var E=e.generateLunarDaysData(o,a,s),T=E[E.length-1-2].value;s=T<s?T:s,e.generateLunarDaysData(o,a,s);var w=e.lunarToSolar(o,a,s);d=w.year,h=w.month,m=w.day}n.refresh(),C.refresh(),i.refresh(),e.setState({selectedYear:o,selectedMonth:a,selectedDay:s,solarYear:d,solarMonth:h,solarDay:m}),e.goTo(n,o-r.startYear),e.goTo(C,(a||r.selectedMonth)-1),e.goTo(i,(s||r.selectedDay)-1)}),C.on("scrollEnd",function(){var t=0;if(e.isMoblie){var g=e.scrollHeight,n=this.y,A=Math.abs(this.y%g);A-g/2>=0?n-=g-A:n+=A,t=Math.round(Math.abs(n/g))}else t=this.currentPage.pageY;var I=e.state,r=1+t,o=I.selectedYear,a=I.selectedDay,s=e.getMaxDate(),c=e.state.mode,l=void 0,d=void 0,h=void 0;if(0===c)r>12&&(r=12);else if(1===c){var m=e.generateLunarMonthsData(o,r),u=m.length-4;C.refresh(),r>u&&(r=u)}if(0===c)1910===o&&r<2&&(r=2),1910===o&&2===r&&a<10&&(a=10),r=e.selectedMonthWhetherLargeThanCurrentMonth(o,r)?s.maxSolar.solarMonth:r,a=e.selectedDayWhetherLargeThanCurrentDay(o,r,a)?s.maxSolar.solarDay:a;else if(1===c){r=e.selectedLunarMonthWhetherLargeThanCurrentLunarMonth(o,r)?s.maxLunar.lunarMonth:r,a=e.selectedLunarDayWhetherLargeThanCurrentLunarDay(o,r,a)?s.maxLunar.lunarDay:a;var p=e.getLunarDaysOfMonthsAndYearDays(o),f=p.monthDays,_=f[r-1];a=a>_?_:a}if(0===c){e.generatorMonthData(r,o);var v=e.generatorDayData(a,r,o),y=v[v.length-1-2].value;a=y<a?y:a,e.generatorDayData(a,r,o),l=o,d=r,h=a}else if(1===c){e.generateLunarMonthsData(o,r);var b=e.generateLunarDaysData(o,r,a),E=b[b.length-1-2].value;a=E<a?E:a,e.generateLunarDaysData(o,r,a);var T=e.lunarToSolar(o,r,a);l=T.year,d=T.month,h=T.day}C.refresh(),i.refresh(),e.goTo(C,(r||I.selectedMonth)-1),e.goTo(i,(a||I.selectedDay)-1),e.setState({selectedMonth:r,selectedDay:a,solarYear:l,solarMonth:d,solarDay:h})}),i.on("scrollEnd",function(){var t=0;if(e.isMoblie){var g=e.scrollHeight,n=this.y,A=Math.abs(this.y%g);A-g/2>=0?n-=g-A:n+=A,t=Math.round(Math.abs(n/g))}else t=this.currentPage.pageY;var C=e.state,I=1+t,r=C.selectedYear,o=C.selectedMonth,a=e.getMaxDate(),s=C.mode,c=void 0,l=void 0,d=void 0;if(0===s){var h=e.getDaysByYearAndMonth(r,o);I>h&&(I=h,e.goTo(i,h-1))}else if(1===s){var m=e.getLunarDaysOfMonthsAndYearDays(r),u=m.monthDays[o-1];I>u&&(I=u,e.goTo(i,u-1))}if(0===s?(1910===r&&o<2&&(o=2),1910===r&&2===o&&I<10&&(I=10),I=e.selectedDayWhetherLargeThanCurrentDay(r,o,I)?a.maxSolar.solarDay:I):1===s&&(I=e.selectedLunarDayWhetherLargeThanCurrentLunarDay(r,o,I)?a.maxLunar.lunarDay:I),0===s)c=r,l=o,d=I,e.generatorDayData(I,o,r);else if(1===s){var p=e.lunarToSolar(r,o,I);c=p.year,l=p.month,d=p.day,e.generateLunarDaysData(r,o,I)}i.refresh(),e.goTo(i,(I||C.selectedDay)-1),e.setState({selectedDay:I,solarYear:c,solarMonth:l,solarDay:d})}),a.on("scrollEnd",function(){var t=0,g=e.lunarData();if(e.isMoblie){var n=e.scrollHeight,A=this.y,C=Math.abs(this.y%n);C-n/2>=0?A-=n-C:A+=C,t=Math.round(Math.abs(A/n))}else(t=this.currentPage.pageY)>24&&(t=24);e.goTo(a,t);var I=parseInt(t);e.generatorTimeData(g.SHI_CENG[I].id),e.setState({selectedTime:g.SHI_CENG[I].text,selectedTimeValue:g.SHI_CENG[I].value,selectedTimeId:g.SHI_CENG[I].id})}),e.setState({yearIScroll:n,monthIScroll:C,dayIScroll:i,timeIScroll:a}),{yearIScroll:n,monthIScroll:C,dayIScroll:i,timeIScroll:a}}},{key:"solarOrLunarChangeHandle",value:function(e){var t=this.state,g=t.mode,n=e;if(n===g)return!1;var A=t.selectedYear,C=t.selectedMonth,I=t.selectedDay,i=void 0,r=void 0,o=void 0;if(0===n){var a=this.lunarToSolar(A,C,I);A=a.year,C=a.month,I=a.day,i=a.year,r=a.month,o=a.day,this.generatorYearData(A),this.generatorMonthData(C,A),this.generatorDayData(I,C,A)}else if(1===n){var s=this.solarToLunar(A,C,I);i=A,r=C,o=I,A=s.lunarYear,C=s.lunarMonth,I=s.lunarDay,this.generatorYearData(A),this.generateLunarMonthsData(A,C),this.generateLunarDaysData(A,C,I)}var c=this.state,l=c.yearIScroll,d=c.monthIScroll,h=c.dayIScroll;l.refresh(),d.refresh(),h.refresh(),this.goTo(l,A-this.state.startYear),this.goTo(d,(C||t.selectedMonth)-1),this.goTo(h,(I||t.selectedDay)-1),this.setState({mode:n,selectedYear:A,selectedMonth:C,selectedDay:I,solarYear:i,solarMonth:r,solarDay:o})}},{key:"componentWillReceiveProps",value:function(e){e.data&&e.data.datePickerDisplay&&this.pickerOpen()}},{key:"goTo",value:function(e,t,g){var n=g||this.scrollHeight;e.scrollTo(0,-t*n)}},{key:"goToPage",value:function(e,t){e.goToPage(0,t,0,o.a.utils.ease.elastic)}},{key:"destroyIScrollObject",value:function(){var e=this.state,t=e.yearIScroll,g=e.monthIScroll,n=e.dayIScroll,A=e.timeIScroll;t.destroy(),t=null,g.destroy(),g=null,n.destroy(),n=null,A.destroy(),A=null}},{key:"pickerOpen",value:function(){var e=this,t=this,g=this.props.data,n="number"==typeof g.mode?g.mode:this.state.mode,A=navigator.userAgent,C=/iphone|ipad|imac/i.test(A)&&/version\/8\./i.test(A),I=t.preventScroll();C?document.getElementsByTagName("html")[0].style.overflow="hidden":I.open();var i=g.datePickerBirthFormat||"1985070100",r=this.formateBirthday(i),o=g.birthTimeValue||"时辰未知",a=g.birthTimeDataId||0,s=parseInt(r[0],10),c=parseInt(r[1],10),l=parseInt(r[2],10),d=void 0,h=void 0,m=void 0,u=g.birthTimeDataId||0,p=parseInt(r[3],10)||g.birthTimeDataValue;if(i&&""!==i){if(n){var f=this.solarToLunar(s,c,l);d=f.lunarYear,h=f.lunarMonth,m=f.lunarDay,this.generatorYearData(d),this.generateLunarMonthsData(d,h),this.generateLunarDaysData(d,h,m),this.generatorTimeData(u)}else d=s,h=c,m=l,this.generatorYearData(d),this.generatorMonthData(h,d),this.generatorDayData(m,h,d),this.generatorTimeData(u);this.setState({mode:n,selectedYear:d,selectedMonth:h,selectedDay:m,selectedTime:o,solarYear:s,solarMonth:c,solarDay:l,selectedTimeId:u,selectedTimeValue:p}),setTimeout(function(){var t=document.querySelector(".rdp-item-container-"+e.timestamp+" li"),g=t.clientHeight,n=e.dateIScrollHandle(),A=n.yearIScroll,C=n.monthIScroll,I=n.dayIScroll,i=n.timeIScroll;e.goTo(A,d-e.state.startYear,g),e.goTo(C,h-1,g),e.goTo(I,m-1,g),e.goTo(i,a,g),e.scrollHeight=g},0)}}},{key:"formateBirthday",value:function(e){return[e.substr(0,4),e.substr(4,2),e.substr(6,2),e.substr(8,2)]}},{key:"pickerClose",value:function(){var e=this,t=this.props.setState,g=navigator.userAgent,n=/iphone|ipad|imac/i.test(g)&&/version\/8\./i.test(g),A=e.preventScroll();n?document.getElementsByTagName("html")[0].style.overflow="auto":A.close(),t({datePickerDisplay:0}),this.setState({activeTabIndex:0}),this.destroyIScrollObject()}},{key:"eventCancelClickHandle",value:function(){this.pickerClose()}},{key:"eventConfirmClickHandle",value:function(){if(1===this.state.activeTabIndex)return this.pickerClose(),!1;var e,t,g=this.state,n=this.props,A=n.setState,C=g.mode,I=this.lunarData(),i=g.selectedTimeValue,r=g.selectedTimeId,o=0===r?1:0,a="zh-cn"===n.data.lang,s=a?"公(阳)历 ":"公(陽)曆 ",c=a?"农(阴)历 ":"農(陰)歷 ",l=C?c:s,d=a?I.SHI_CENG[r].time:I.SHI_CENG[r].time_tw,h=g.solarYear,m=g.solarMonth,u=g.solarDay,p=this.solarToLunar(h,m,u),f=-1===i?"00":i<10?"0"+i:i,_=""+h+(m<10?"0"+m:m)+(u<10?"0"+u:u)+f,v=h+"年"+m+"月"+u+"日 "+d,y=p.lunarYear+"年"+p.lunarMonthName+p.lunarDayName+" "+d;C?(e=""+l+p.lunarYear+"年"+p.lunarMonthName+p.lunarDayName+" "+d,t=e):e=""+l+h+"年"+m+"月"+u+"日 "+d;try{A({datePickerBirth:e,datePickerTimeConfirm:o,datePickerBirthFormat:_,birthTimeValue:I.SHI_CENG[r].text,birthTimeDataValue:I.SHI_CENG[r].value,birthTimeDataId:I.SHI_CENG[r].id,datePickerBirthTradition:t,mode:C})}catch(e){console&&console.warn(e)}this.setState({activeTabIndex:1,solarDateText:v,lunarDateText:y})}},{key:"eventEditClickHandler",value:function(){this.pickerClose(),this.props.setState({datePickerDisplay:1})}},{key:"preventScroll",value:function(){var e=this,t=this.scrollTop,g=function(e){document.body.scrollTop=document.documentElement.scrollTop=e},n=function(){return document.body.scrollTop||document.documentElement.scrollTop};return{open:function(){0===t&&(t=n(),e.scrollTop=t),document.body.classList.add("rdp-open"),document.body.style.top=-t+"px"},close:function(){document.body.classList.remove("rdp-open"),g(t),e.scrollTop=0}}}},{key:"styles",value:function(){var e=parseFloat(document.documentElement.style.fontSize),t=function(t,g){var n=16;return"number"!=typeof e||isNaN(e)||(n=""+Math.ceil(t/32*e)),g&&"number"==typeof g&&(n*=g),n+"px"};return{ryan_datepicker_v3:{fontSize:t(30),position:"fixed",left:0,top:0,right:0,bottom:0,zIndex:8888,color:"#282828"},rdp_back:{position:"absolute",top:0,left:0,right:0,bottom:0,zIndex:9999,background:"#000",cursor:"pointer",opacity:.5},rdp_front:{lineHeight:t(80),position:"absolute",bottom:0,left:0,right:0,backgroundColor:"#fff",zIndex:1e4,color:"#bbb"},rdp_nav:{overflow:"hidden",borderTop:"1px solid #eee",borderBottom:"1px solid #eee"},rdp_left:{float:"left",width:"20%",textAlign:"center",cursor:"pointer",color:"#999"},rdp_right:{float:"right",width:"20%",textAlign:"center",cursor:"pointer",color:"#c91723"},rdp_auto:{overflow:"hidden"},rdp_switch:{width:"66.66%",margin:t(8)+" auto",border:"1px solid #c91723",borderRadius:"5px",overflow:"hidden",lineHeight:t(64)},rdp_switch_span:{display:"inline-block",verticalAlign:"top",width:"50%",textAlign:"center",boxSizing:"border-box",cursor:"pointer",color:"#c91723"},rdp_mode_active:{color:"#fff",backgroundColor:"#c91723"},rdp_body:{position:"relative"},rdp_layer_top:{position:"absolute",height:0,borderTop:"2px solid #c91723",boxShadow:"0 0 "+t(8)+" #c91723",opacity:.3,top:t(80,2),left:0,right:0,zIndex:0},rdp_layer_bottom:{position:"absolute",height:0,borderTop:"2px solid #c91723",boxShadow:"0 0 "+t(8)+" #c91723",opacity:.3,top:t(80,3),left:0,right:0,zIndex:0},rdp_container:{backgroundColor:"transparent",zIndex:1},rdp_container_li:{display:"inline-block",verticalAlign:"top",width:"20%",boxSizing:"border-box",textAlign:"center",height:t(80,5),overflow:"hidden",cursor:"default",backgroundColor:"transparent"},rdp_container_li_w40:{width:"40%",fontSize:t(24)},rdp_item:{display:"block",backgroundColor:"transparent"},rdp_item_li:{display:"block",lineHeight:t(80),backgroundColor:"transparent"},rdp_item_li_prevent:{color:"#eee"},rdp_item_li_current:{color:"#c91723",fontWeight:"bold"},rdp_item_li_current_arrow:{color:"#c91723",fontWeight:"bold",background:"transparent url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAUCAMAAACDMFxkAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAZQTFRFyRcjAAAA88e98QAAAAJ0Uk5T/wDltzBKAAAAMElEQVR42mzPsREAMAgDMWX/pdNxX5hK5wKMd0MoFAqFQqFQaKVrw7q2mo0vvgADAC1VAGWX1njpAAAAAElFTkSuQmCC) no-repeat left center"},rdp_confirm:{position:"absolute",bottom:0,left:0,right:0,backgroundColor:"#fff",zIndex:1e4,color:"#bbb",overflow:"hidden"},rdp_confirm_nav:{height:t(80),lineHeight:t(80),borderBottom:"1px solid #ddd",textAlign:"center",fontSize:t(32),color:"#a31e1a"},rdp_confirm_box:{textAlign:"center",overflow:"hidden"},rdp_confirm_title:{fontSize:t(28),color:"#333",lineHeight:t(80),marginTop:t(40)},rdp_confirm_date:{fontSize:t(30),color:"#a31e1a",lineHeight:t(60)},rdp_confirm_date_label:{color:"#333"},rdp_c_btn_box:{color:"#fff",fontSize:t(30),margin:t(44)+" 0"},rdp_c_btn_left:{float:"left",width:"50%",textAlign:"right",boxSizing:"border-box",paddingRight:t(10),verticalAlign:"top",cursor:"pointer"},rdp_cbl_text:{display:"inline-block",width:t(200),lineHeight:t(80),backgroundColor:"#b1b1b1",color:"#fff",textAlign:"center",borderRadius:t(8)},rdp_c_btn_right:{float:"right",textAlign:"left",width:"50%",boxSizing:"border-box",paddingLeft:t(10),verticalAlign:"top",cursor:"pointer"},rdp_cbr_text:{display:"inline-block",width:t(200),lineHeight:t(80),backgroundColor:"#a31e1a",color:"#fff",textAlign:"center",borderRadius:t(8)},rdp_cb_clear:{overflow:"hidden",clear:"both"}}}},{key:"render",value:function(){var e=this,t=e.state,g=e.props,n=this.styles(),A={display:g.data&&g.data.datePickerDisplay?"block":"none"};return i.a.createElement("div",{style:Object.assign({},n.ryan_datepicker_v3,A)},i.a.createElement("div",{id:"rdp-back",style:n.rdp_back,onClick:this.eventConfirmClickHandle.bind(this)}),i.a.createElement("div",{style:Object.assign({},n.rdp_confirm,{display:1===t.activeTabIndex?"block":"none"})},i.a.createElement("div",{style:n.rdp_confirm_nav},"确认时间"),i.a.createElement("div",{style:n.rdp_confirm_box},i.a.createElement("p",{style:n.rdp_confirm_title},"请确认输入的时间是否正确"),1===this.props.data.mode&&i.a.createElement("div",{style:n.rdp_confirm_date},i.a.createElement("p",null,i.a.createElement("span",{style:n.rdp_confirm_date_label},"农(阴)历："),t.lunarDateText),i.a.createElement("p",null,i.a.createElement("span",{style:n.rdp_confirm_date_label},"公(阳)历："),t.solarDateText)),0===this.props.data.mode&&i.a.createElement("div",{style:n.rdp_confirm_date},i.a.createElement("p",null,i.a.createElement("span",{style:n.rdp_confirm_date_label},"公(阳)历："),t.solarDateText),i.a.createElement("p",null,i.a.createElement("span",{style:n.rdp_confirm_date_label},"农(阴)历："),t.lunarDateText))),i.a.createElement("div",{style:n.rdp_c_btn_box},i.a.createElement("div",{style:n.rdp_c_btn_left},i.a.createElement("span",{style:n.rdp_cbl_text,onClick:this.eventEditClickHandler.bind(this)},"返回修改")),i.a.createElement("div",{style:n.rdp_c_btn_right},i.a.createElement("span",{style:n.rdp_cbr_text,onClick:this.pickerClose.bind(this)},"确认正确")),i.a.createElement("div",{style:n.rdp_cb_clear}))),i.a.createElement("div",{style:Object.assign({},n.rdp_front,{display:0===t.activeTabIndex?"block":"none"})},i.a.createElement("div",{style:n.rdp_nav},i.a.createElement("div",{style:n.rdp_left,onClick:this.eventCancelClickHandle.bind(this)},"取消"),i.a.createElement("div",{style:n.rdp_right,onClick:this.eventConfirmClickHandle.bind(this)},"完成"),i.a.createElement("div",{style:n.rdp_auto},i.a.createElement("div",{style:n.rdp_switch},i.a.createElement("span",{style:Object.assign({},n.rdp_switch_span,0===t.mode?n.rdp_mode_active:{}),onClick:this.solarOrLunarChangeHandle.bind(this,0)},"公历"),i.a.createElement("span",{style:Object.assign({},n.rdp_switch_span,1===t.mode?n.rdp_mode_active:{}),onClick:this.solarOrLunarChangeHandle.bind(this,1)},"农历")))),i.a.createElement("div",{style:n.rdp_body},i.a.createElement("ul",{style:n.rdp_container},i.a.createElement("li",{id:"wrapper-year-iscroll"+this.timestamp,style:n.rdp_container_li},i.a.createElement("ul",{className:"rdp-item-container-"+this.timestamp,style:n.rdp_item},t.yearData.map(function(e,t){return i.a.createElement(s,{key:t,data:e,s:n,arrow:!0})},this))),i.a.createElement("li",{id:"wrapper-month-iscroll"+this.timestamp,style:n.rdp_container_li},i.a.createElement("ul",{className:"rdp-item-container-"+this.timestamp,style:n.rdp_item},t.monthData.map(function(e,t){return i.a.createElement(s,{key:t,data:e,s:n})},this))),i.a.createElement("li",{id:"wrapper-day-iscroll"+this.timestamp,style:n.rdp_container_li},i.a.createElement("ul",{className:"rdp-item-container-"+this.timestamp,style:n.rdp_item},t.dayData.map(function(e,t){return i.a.createElement(s,{key:t,data:e,s:n})},this))),i.a.createElement("li",{id:"wrapper-time-iscroll"+this.timestamp,style:Object.assign({},n.rdp_container_li,n.rdp_container_li_w40)},i.a.createElement("ul",{className:"rdp-item-container-"+this.timestamp,style:n.rdp_item},t.timeData.map(function(e,t){return i.a.createElement(s,{key:t,data:e,s:n})},this)))),i.a.createElement("div",{style:n.rdp_layer_top}),i.a.createElement("div",{style:n.rdp_layer_bottom}))))}}]),t}(I.Component),l=c;t.a=l;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(s,"OneDateItem","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/RyanDatePickerV7.jsx"),__REACT_HOT_LOADER__.register(c,"RyanDatePicker","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/RyanDatePickerV7.jsx"),__REACT_HOT_LOADER__.register(l,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/RyanDatePickerV7.jsx"))}()},330:function(e,t,g){var n,A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};/*! iScroll v5.2.0-snapshot ~ (c) 2008-2017 Matteo Spinelli ~ http://cubiq.org/license */
!function(C,I,i){function r(e,t){this.wrapper="string"==typeof e?I.querySelector(e):e,this.scroller=this.wrapper.children[0],this.scrollerStyle=this.scroller.style,this.options={resizeScrollbars:!0,mouseWheelSpeed:20,snapThreshold:.334,disablePointer:!c.hasPointer,disableTouch:c.hasPointer||!c.hasTouch,disableMouse:c.hasPointer||c.hasTouch,startX:0,startY:0,scrollY:!0,directionLockThreshold:5,momentum:!0,bounce:!0,bounceTime:600,bounceEasing:"",preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:!0,useTransition:!0,useTransform:!0,bindToWrapper:void 0===C.onmousedown};for(var g in t)this.options[g]=t[g];this.translateZ=this.options.HWCompositing&&c.hasPerspective?" translateZ(0)":"",this.options.useTransition=c.hasTransition&&this.options.useTransition,this.options.useTransform=c.hasTransform&&this.options.useTransform,this.options.eventPassthrough=!0===this.options.eventPassthrough?"vertical":this.options.eventPassthrough,this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault,this.options.scrollY="vertical"!=this.options.eventPassthrough&&this.options.scrollY,this.options.scrollX="horizontal"!=this.options.eventPassthrough&&this.options.scrollX,this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough,this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold,this.options.bounceEasing="string"==typeof this.options.bounceEasing?c.ease[this.options.bounceEasing]||c.ease.circular:this.options.bounceEasing,this.options.resizePolling=void 0===this.options.resizePolling?60:this.options.resizePolling,!0===this.options.tap&&(this.options.tap="tap"),this.options.useTransition||this.options.useTransform||/relative|absolute/i.test(this.scrollerStyle.position)||(this.scrollerStyle.position="relative"),"scale"==this.options.shrinkScrollbars&&(this.options.useTransition=!1),this.options.invertWheelDirection=this.options.invertWheelDirection?-1:1,this.x=0,this.y=0,this.directionX=0,this.directionY=0,this._events={},this._init(),this.refresh(),this.scrollTo(this.options.startX,this.options.startY),this.enable()}function o(e,t,g){var n=I.createElement("div"),A=I.createElement("div");return!0===g&&(n.style.cssText="position:absolute;z-index:9999",A.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:absolute;background:rgba(0,0,0,0.5);border:1px solid rgba(255,255,255,0.9);border-radius:3px"),A.className="iScrollIndicator","h"==e?(!0===g&&(n.style.cssText+=";height:7px;left:2px;right:2px;bottom:0",A.style.height="100%"),n.className="iScrollHorizontalScrollbar"):(!0===g&&(n.style.cssText+=";width:7px;bottom:2px;top:2px;right:1px",A.style.width="100%"),n.className="iScrollVerticalScrollbar"),n.style.cssText+=";overflow:hidden",t||(n.style.pointerEvents="none"),n.appendChild(A),n}function a(e,t){this.wrapper="string"==typeof t.el?I.querySelector(t.el):t.el,this.wrapperStyle=this.wrapper.style,this.indicator=this.wrapper.children[0],this.indicatorStyle=this.indicator.style,this.scroller=e,this.options={listenX:!0,listenY:!0,interactive:!1,resize:!0,defaultScrollbars:!1,shrink:!1,fade:!1,speedRatioX:0,speedRatioY:0};for(var g in t)this.options[g]=t[g];if(this.sizeRatioX=1,this.sizeRatioY=1,this.maxPosX=0,this.maxPosY=0,this.options.interactive&&(this.options.disableTouch||(c.addEvent(this.indicator,"touchstart",this),c.addEvent(C,"touchend",this)),this.options.disablePointer||(c.addEvent(this.indicator,c.prefixPointerEvent("pointerdown"),this),c.addEvent(C,c.prefixPointerEvent("pointerup"),this)),this.options.disableMouse||(c.addEvent(this.indicator,"mousedown",this),c.addEvent(C,"mouseup",this))),this.options.fade){this.wrapperStyle[c.style.transform]=this.scroller.translateZ;var n=c.style.transitionDuration;if(!n)return;this.wrapperStyle[n]=c.isBadAndroid?"0.0001ms":"0ms";var A=this;c.isBadAndroid&&s(function(){"0.0001ms"===A.wrapperStyle[n]&&(A.wrapperStyle[n]="0s")}),this.wrapperStyle.opacity="0"}}var s=C.requestAnimationFrame||C.webkitRequestAnimationFrame||C.mozRequestAnimationFrame||C.oRequestAnimationFrame||C.msRequestAnimationFrame||function(e){C.setTimeout(e,1e3/60)},c=function(){function e(e){return!1!==n&&(""===n?e:n+e.charAt(0).toUpperCase()+e.substr(1))}var t={},g=I.createElement("div").style,n=function(){for(var e=["t","webkitT","MozT","msT","OT"],t=0,n=e.length;t<n;t++)if(e[t]+"ransform"in g)return e[t].substr(0,e[t].length-1);return!1}();t.getTime=Date.now||function(){return(new Date).getTime()},t.extend=function(e,t){for(var g in t)e[g]=t[g]},t.addEvent=function(e,t,g,n){e.addEventListener(t,g,!!n)},t.removeEvent=function(e,t,g,n){e.removeEventListener(t,g,!!n)},t.prefixPointerEvent=function(e){return C.MSPointerEvent?"MSPointer"+e.charAt(7).toUpperCase()+e.substr(8):e},t.momentum=function(e,t,g,n,A,C){var I,r,o=e-t,a=i.abs(o)/g;return C=void 0===C?6e-4:C,I=e+a*a/(2*C)*(o<0?-1:1),r=a/C,I<n?(I=A?n-A/2.5*(a/8):n,o=i.abs(I-e),r=o/a):I>0&&(I=A?A/2.5*(a/8):0,o=i.abs(e)+I,r=o/a),{destination:i.round(I),duration:r}};var r=e("transform");return t.extend(t,{hasTransform:!1!==r,hasPerspective:e("perspective")in g,hasTouch:"ontouchstart"in C,hasPointer:!(!C.PointerEvent&&!C.MSPointerEvent),hasTransition:e("transition")in g}),t.isBadAndroid=function(){var e=C.navigator.appVersion;if(/Android/.test(e)&&!/Chrome\/\d/.test(e)){var t=e.match(/Safari\/(\d+.\d)/);return!(t&&"object"===(void 0===t?"undefined":A(t))&&t.length>=2)||parseFloat(t[1])<535.19}return!1}(),t.extend(t.style={},{transform:r,transitionTimingFunction:e("transitionTimingFunction"),transitionDuration:e("transitionDuration"),transitionDelay:e("transitionDelay"),transformOrigin:e("transformOrigin"),touchAction:e("touchAction")}),t.hasClass=function(e,t){return new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},t.addClass=function(e,g){if(!t.hasClass(e,g)){var n=e.className.split(" ");n.push(g),e.className=n.join(" ")}},t.removeClass=function(e,g){if(t.hasClass(e,g)){var n=new RegExp("(^|\\s)"+g+"(\\s|$)","g");e.className=e.className.replace(n," ")}},t.offset=function(e){for(var t=-e.offsetLeft,g=-e.offsetTop;e=e.offsetParent;)t-=e.offsetLeft,g-=e.offsetTop;return{left:t,top:g}},t.preventDefaultException=function(e,t){for(var g in t)if(t[g].test(e[g]))return!0;return!1},t.extend(t.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3}),t.extend(t.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(e){return e*(2-e)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(e){return i.sqrt(1- --e*e)}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(e){return(e-=1)*e*(5*e+4)+1}},bounce:{style:"",fn:function(e){return(e/=1)<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375}},elastic:{style:"",fn:function(e){return 0===e?0:1==e?1:.4*i.pow(2,-10*e)*i.sin((e-.055)*(2*i.PI)/.22)+1}}}),t.tap=function(e,t){var g=I.createEvent("Event");g.initEvent(t,!0,!0),g.pageX=e.pageX,g.pageY=e.pageY,e.target.dispatchEvent(g)},t.click=function(e){var t,g=e.target;/(SELECT|INPUT|TEXTAREA)/i.test(g.tagName)||(t=I.createEvent(C.MouseEvent?"MouseEvents":"Event"),t.initEvent("click",!0,!0),t.view=e.view||C,t.detail=1,t.screenX=g.screenX||0,t.screenY=g.screenY||0,t.clientX=g.clientX||0,t.clientY=g.clientY||0,t.ctrlKey=!!e.ctrlKey,t.altKey=!!e.altKey,t.shiftKey=!!e.shiftKey,t.metaKey=!!e.metaKey,t.button=0,t.relatedTarget=null,t._constructed=!0,g.dispatchEvent(t))},t.getTouchAction=function(e,t){var g="none";return"vertical"===e?g="pan-y":"horizontal"===e&&(g="pan-x"),t&&"none"!=g&&(g+=" pinch-zoom"),g},t.getRect=function(e){if(e instanceof SVGElement){var t=e.getBoundingClientRect();return{top:t.top,left:t.left,width:t.width,height:t.height}}return{top:e.offsetTop,left:e.offsetLeft,width:e.offsetWidth,height:e.offsetHeight}},t}();r.prototype={version:"5.2.0-snapshot",_init:function(){this._initEvents(),(this.options.scrollbars||this.options.indicators)&&this._initIndicators(),this.options.mouseWheel&&this._initWheel(),this.options.snap&&this._initSnap(),this.options.keyBindings&&this._initKeys()},destroy:function(){this._initEvents(!0),clearTimeout(this.resizeTimeout),this.resizeTimeout=null,this._execEvent("destroy")},_transitionEnd:function(e){e.target==this.scroller&&this.isInTransition&&(this._transitionTime(),this.resetPosition(this.options.bounceTime)||(this.isInTransition=!1,this._execEvent("scrollEnd")))},_start:function(e){if(1!=c.eventType[e.type]){if(0!==(e.which?e.button:e.button<2?0:4==e.button?1:2))return}if(this.enabled&&(!this.initiated||c.eventType[e.type]===this.initiated)){!this.options.preventDefault||c.isBadAndroid||c.preventDefaultException(e.target,this.options.preventDefaultException)||e.preventDefault();var t,g=e.touches?e.touches[0]:e;this.initiated=c.eventType[e.type],this.moved=!1,this.distX=0,this.distY=0,this.directionX=0,this.directionY=0,this.directionLocked=0,this.startTime=c.getTime(),this.options.useTransition&&this.isInTransition?(this._transitionTime(),this.isInTransition=!1,t=this.getComputedPosition(),this._translate(i.round(t.x),i.round(t.y)),this._execEvent("scrollEnd")):!this.options.useTransition&&this.isAnimating&&(this.isAnimating=!1,this._execEvent("scrollEnd")),this.startX=this.x,this.startY=this.y,this.absStartX=this.x,this.absStartY=this.y,this.pointX=g.pageX,this.pointY=g.pageY,this._execEvent("beforeScrollStart")}},_move:function(e){if(this.enabled&&c.eventType[e.type]===this.initiated){this.options.preventDefault&&e.preventDefault();var t,g,n,A,C=e.touches?e.touches[0]:e,I=C.pageX-this.pointX,r=C.pageY-this.pointY,o=c.getTime();if(this.pointX=C.pageX,this.pointY=C.pageY,this.distX+=I,this.distY+=r,n=i.abs(this.distX),A=i.abs(this.distY),!(o-this.endTime>300&&n<10&&A<10)){if(this.directionLocked||this.options.freeScroll||(n>A+this.options.directionLockThreshold?this.directionLocked="h":A>=n+this.options.directionLockThreshold?this.directionLocked="v":this.directionLocked="n"),"h"==this.directionLocked){if("vertical"==this.options.eventPassthrough)e.preventDefault();else if("horizontal"==this.options.eventPassthrough)return void(this.initiated=!1);r=0}else if("v"==this.directionLocked){if("horizontal"==this.options.eventPassthrough)e.preventDefault();else if("vertical"==this.options.eventPassthrough)return void(this.initiated=!1);I=0}I=this.hasHorizontalScroll?I:0,r=this.hasVerticalScroll?r:0,t=this.x+I,g=this.y+r,(t>0||t<this.maxScrollX)&&(t=this.options.bounce?this.x+I/3:t>0?0:this.maxScrollX),(g>0||g<this.maxScrollY)&&(g=this.options.bounce?this.y+r/3:g>0?0:this.maxScrollY),this.directionX=I>0?-1:I<0?1:0,this.directionY=r>0?-1:r<0?1:0,this.moved||this._execEvent("scrollStart"),this.moved=!0,this._translate(t,g),o-this.startTime>300&&(this.startTime=o,this.startX=this.x,this.startY=this.y)}}},_end:function(e){if(this.enabled&&c.eventType[e.type]===this.initiated){this.options.preventDefault&&!c.preventDefaultException(e.target,this.options.preventDefaultException)&&e.preventDefault();var t,g,n=(e.changedTouches&&e.changedTouches[0],c.getTime()-this.startTime),A=i.round(this.x),C=i.round(this.y),I=i.abs(A-this.startX),r=i.abs(C-this.startY),o=0,a="";if(this.isInTransition=0,this.initiated=0,this.endTime=c.getTime(),!this.resetPosition(this.options.bounceTime)){if(this.scrollTo(A,C),!this.moved)return this.options.tap&&c.tap(e,this.options.tap),this.options.click&&c.click(e),void this._execEvent("scrollCancel");if(this._events.flick&&n<200&&I<100&&r<100)return void this._execEvent("flick");if(this.options.momentum&&n<300&&(t=this.hasHorizontalScroll?c.momentum(this.x,this.startX,n,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:A,duration:0},g=this.hasVerticalScroll?c.momentum(this.y,this.startY,n,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:C,duration:0},A=t.destination,C=g.destination,o=i.max(t.duration,g.duration),this.isInTransition=1),this.options.snap){var s=this._nearestSnap(A,C);this.currentPage=s,o=this.options.snapSpeed||i.max(i.max(i.min(i.abs(A-s.x),1e3),i.min(i.abs(C-s.y),1e3)),300),A=s.x,C=s.y,this.directionX=0,this.directionY=0,a=this.options.bounceEasing}if(A!=this.x||C!=this.y)return(A>0||A<this.maxScrollX||C>0||C<this.maxScrollY)&&(a=c.ease.quadratic),void this.scrollTo(A,C,o,a);this._execEvent("scrollEnd")}}},_resize:function(){var e=this;clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){e.refresh()},this.options.resizePolling)},resetPosition:function(e){var t=this.x,g=this.y;return e=e||0,!this.hasHorizontalScroll||this.x>0?t=0:this.x<this.maxScrollX&&(t=this.maxScrollX),!this.hasVerticalScroll||this.y>0?g=0:this.y<this.maxScrollY&&(g=this.maxScrollY),(t!=this.x||g!=this.y)&&(this.scrollTo(t,g,e,this.options.bounceEasing),!0)},disable:function(){this.enabled=!1},enable:function(){this.enabled=!0},refresh:function(){c.getRect(this.wrapper),this.wrapperWidth=this.wrapper.clientWidth,this.wrapperHeight=this.wrapper.clientHeight;var e=c.getRect(this.scroller);this.scrollerWidth=e.width,this.scrollerHeight=e.height,this.maxScrollX=this.wrapperWidth-this.scrollerWidth,this.maxScrollY=this.wrapperHeight-this.scrollerHeight,this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0,this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0,this.hasHorizontalScroll||(this.maxScrollX=0,this.scrollerWidth=this.wrapperWidth),this.hasVerticalScroll||(this.maxScrollY=0,this.scrollerHeight=this.wrapperHeight),this.endTime=0,this.directionX=0,this.directionY=0,c.hasPointer&&!this.options.disablePointer&&(this.wrapper.style[c.style.touchAction]=c.getTouchAction(this.options.eventPassthrough,!0),this.wrapper.style[c.style.touchAction]||(this.wrapper.style[c.style.touchAction]=c.getTouchAction(this.options.eventPassthrough,!1))),this.wrapperOffset=c.offset(this.wrapper),this._execEvent("refresh"),this.resetPosition()},on:function(e,t){this._events[e]||(this._events[e]=[]),this._events[e].push(t)},off:function(e,t){if(this._events[e]){var g=this._events[e].indexOf(t);g>-1&&this._events[e].splice(g,1)}},_execEvent:function(e){if(this._events[e]){var t=0,g=this._events[e].length;if(g)for(;t<g;t++)this._events[e][t].apply(this,[].slice.call(arguments,1))}},scrollBy:function(e,t,g,n){e=this.x+e,t=this.y+t,g=g||0,this.scrollTo(e,t,g,n)},scrollTo:function(e,t,g,n){n=n||c.ease.circular,this.isInTransition=this.options.useTransition&&g>0;var A=this.options.useTransition&&n.style;!g||A?(A&&(this._transitionTimingFunction(n.style),this._transitionTime(g)),this._translate(e,t)):this._animate(e,t,g,n.fn)},scrollToElement:function(e,t,g,n,A){if(e=e.nodeType?e:this.scroller.querySelector(e)){var C=c.offset(e);C.left-=this.wrapperOffset.left,C.top-=this.wrapperOffset.top;var I=c.getRect(e),r=c.getRect(this.wrapper);!0===g&&(g=i.round(I.width/2-r.width/2)),!0===n&&(n=i.round(I.height/2-r.height/2)),C.left-=g||0,C.top-=n||0,C.left=C.left>0?0:C.left<this.maxScrollX?this.maxScrollX:C.left,C.top=C.top>0?0:C.top<this.maxScrollY?this.maxScrollY:C.top,t=void 0===t||null===t||"auto"===t?i.max(i.abs(this.x-C.left),i.abs(this.y-C.top)):t,this.scrollTo(C.left,C.top,t,A)}},_transitionTime:function(e){if(this.options.useTransition){e=e||0;var t=c.style.transitionDuration;if(t){if(this.scrollerStyle[t]=e+"ms",!e&&c.isBadAndroid){this.scrollerStyle[t]="0.0001ms";var g=this;s(function(){"0.0001ms"===g.scrollerStyle[t]&&(g.scrollerStyle[t]="0s")})}if(this.indicators)for(var n=this.indicators.length;n--;)this.indicators[n].transitionTime(e)}}},_transitionTimingFunction:function(e){if(this.scrollerStyle[c.style.transitionTimingFunction]=e,this.indicators)for(var t=this.indicators.length;t--;)this.indicators[t].transitionTimingFunction(e)},_translate:function(e,t){if(this.options.useTransform?this.scrollerStyle[c.style.transform]="translate("+e+"px,"+t+"px)"+this.translateZ:(e=i.round(e),t=i.round(t),this.scrollerStyle.left=e+"px",this.scrollerStyle.top=t+"px"),this.x=e,this.y=t,this.indicators)for(var g=this.indicators.length;g--;)this.indicators[g].updatePosition()},_initEvents:function(e){var t=e?c.removeEvent:c.addEvent,g=this.options.bindToWrapper?this.wrapper:C;t(C,"orientationchange",this),t(C,"resize",this),this.options.click&&t(this.wrapper,"click",this,!0),this.options.disableMouse||(t(this.wrapper,"mousedown",this),t(g,"mousemove",this),t(g,"mousecancel",this),t(g,"mouseup",this)),c.hasPointer&&!this.options.disablePointer&&(t(this.wrapper,c.prefixPointerEvent("pointerdown"),this),t(g,c.prefixPointerEvent("pointermove"),this),t(g,c.prefixPointerEvent("pointercancel"),this),t(g,c.prefixPointerEvent("pointerup"),this)),c.hasTouch&&!this.options.disableTouch&&(t(this.wrapper,"touchstart",this),t(g,"touchmove",this),t(g,"touchcancel",this),t(g,"touchend",this)),t(this.scroller,"transitionend",this),t(this.scroller,"webkitTransitionEnd",this),t(this.scroller,"oTransitionEnd",this),t(this.scroller,"MSTransitionEnd",this)},getComputedPosition:function(){var e,t,g=C.getComputedStyle(this.scroller,null);return this.options.useTransform?(g=g[c.style.transform].split(")")[0].split(", "),e=+(g[12]||g[4]),t=+(g[13]||g[5])):(e=+g.left.replace(/[^-\d.]/g,""),t=+g.top.replace(/[^-\d.]/g,"")),{x:e,y:t}},_initIndicators:function(){function e(e){if(C.indicators)for(var t=C.indicators.length;t--;)e.call(C.indicators[t])}var t,g=this.options.interactiveScrollbars,n="string"!=typeof this.options.scrollbars,A=[],C=this;this.indicators=[],this.options.scrollbars&&(this.options.scrollY&&(t={el:o("v",g,this.options.scrollbars),interactive:g,defaultScrollbars:!0,customStyle:n,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenX:!1},this.wrapper.appendChild(t.el),A.push(t)),this.options.scrollX&&(t={el:o("h",g,this.options.scrollbars),interactive:g,defaultScrollbars:!0,customStyle:n,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenY:!1},this.wrapper.appendChild(t.el),A.push(t))),this.options.indicators&&(A=A.concat(this.options.indicators));for(var I=A.length;I--;)this.indicators.push(new a(this,A[I]));this.options.fadeScrollbars&&(this.on("scrollEnd",function(){e(function(){this.fade()})}),this.on("scrollCancel",function(){e(function(){this.fade()})}),this.on("scrollStart",function(){e(function(){this.fade(1)})}),this.on("beforeScrollStart",function(){e(function(){this.fade(1,!0)})})),this.on("refresh",function(){e(function(){this.refresh()})}),this.on("destroy",function(){e(function(){this.destroy()}),delete this.indicators})},_initWheel:function(){c.addEvent(this.wrapper,"wheel",this),c.addEvent(this.wrapper,"mousewheel",this),c.addEvent(this.wrapper,"DOMMouseScroll",this),this.on("destroy",function(){clearTimeout(this.wheelTimeout),this.wheelTimeout=null,c.removeEvent(this.wrapper,"wheel",this),c.removeEvent(this.wrapper,"mousewheel",this),c.removeEvent(this.wrapper,"DOMMouseScroll",this)})},_wheel:function(e){if(this.enabled){e.preventDefault();var t,g,n,A,C=this;if(void 0===this.wheelTimeout&&C._execEvent("scrollStart"),clearTimeout(this.wheelTimeout),this.wheelTimeout=setTimeout(function(){C.options.snap||C._execEvent("scrollEnd"),C.wheelTimeout=void 0},400),"deltaX"in e)1===e.deltaMode?(t=-e.deltaX*this.options.mouseWheelSpeed,g=-e.deltaY*this.options.mouseWheelSpeed):(t=-e.deltaX,g=-e.deltaY);else if("wheelDeltaX"in e)t=e.wheelDeltaX/120*this.options.mouseWheelSpeed,g=e.wheelDeltaY/120*this.options.mouseWheelSpeed;else if("wheelDelta"in e)t=g=e.wheelDelta/120*this.options.mouseWheelSpeed;else{if(!("detail"in e))return;t=g=-e.detail/3*this.options.mouseWheelSpeed}if(t*=this.options.invertWheelDirection,g*=this.options.invertWheelDirection,this.hasVerticalScroll||(t=g,g=0),this.options.snap)return n=this.currentPage.pageX,A=this.currentPage.pageY,t>0?n--:t<0&&n++,g>0?A--:g<0&&A++,void this.goToPage(n,A);n=this.x+i.round(this.hasHorizontalScroll?t:0),A=this.y+i.round(this.hasVerticalScroll?g:0),this.directionX=t>0?-1:t<0?1:0,this.directionY=g>0?-1:g<0?1:0,n>0?n=0:n<this.maxScrollX&&(n=this.maxScrollX),A>0?A=0:A<this.maxScrollY&&(A=this.maxScrollY),this.scrollTo(n,A,0)}},_initSnap:function(){this.currentPage={},"string"==typeof this.options.snap&&(this.options.snap=this.scroller.querySelectorAll(this.options.snap)),this.on("refresh",function(){var e,t,g,n,A,C,I,r=0,o=0,a=0,s=this.options.snapStepX||this.wrapperWidth,l=this.options.snapStepY||this.wrapperHeight;if(this.pages=[],this.wrapperWidth&&this.wrapperHeight&&this.scrollerWidth&&this.scrollerHeight){if(!0===this.options.snap)for(g=i.round(s/2),n=i.round(l/2);a>-this.scrollerWidth;){for(this.pages[r]=[],e=0,A=0;A>-this.scrollerHeight;)this.pages[r][e]={x:i.max(a,this.maxScrollX),y:i.max(A,this.maxScrollY),width:s,height:l,cx:a-g,cy:A-n},A-=l,e++;a-=s,r++}else for(C=this.options.snap,e=C.length,t=-1;r<e;r++)I=c.getRect(C[r]),(0===r||I.left<=c.getRect(C[r-1]).left)&&(o=0,t++),this.pages[o]||(this.pages[o]=[]),a=i.max(-I.left,this.maxScrollX),A=i.max(-I.top,this.maxScrollY),g=a-i.round(I.width/2),n=A-i.round(I.height/2),this.pages[o][t]={x:a,y:A,width:I.width,height:I.height,cx:g,cy:n},a>this.maxScrollX&&o++;this.goToPage(this.currentPage.pageX||0,this.currentPage.pageY||0,0),this.options.snapThreshold%1==0?(this.snapThresholdX=this.options.snapThreshold,this.snapThresholdY=this.options.snapThreshold):(this.snapThresholdX=i.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].width*this.options.snapThreshold),this.snapThresholdY=i.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].height*this.options.snapThreshold))}}),this.on("flick",function(){var e=this.options.snapSpeed||i.max(i.max(i.min(i.abs(this.x-this.startX),1e3),i.min(i.abs(this.y-this.startY),1e3)),300);this.goToPage(this.currentPage.pageX+this.directionX,this.currentPage.pageY+this.directionY,e)})},_nearestSnap:function(e,t){if(!this.pages.length)return{x:0,y:0,pageX:0,pageY:0};var g=0,n=this.pages.length,A=0;if(i.abs(e-this.absStartX)<this.snapThresholdX&&i.abs(t-this.absStartY)<this.snapThresholdY)return this.currentPage;for(e>0?e=0:e<this.maxScrollX&&(e=this.maxScrollX),t>0?t=0:t<this.maxScrollY&&(t=this.maxScrollY);g<n;g++)if(e>=this.pages[g][0].cx){e=this.pages[g][0].x;break}for(n=this.pages[g].length;A<n;A++)if(t>=this.pages[0][A].cy){t=this.pages[0][A].y;break}return g==this.currentPage.pageX&&(g+=this.directionX,g<0?g=0:g>=this.pages.length&&(g=this.pages.length-1),e=this.pages[g][0].x),A==this.currentPage.pageY&&(A+=this.directionY,A<0?A=0:A>=this.pages[0].length&&(A=this.pages[0].length-1),t=this.pages[0][A].y),{x:e,y:t,pageX:g,pageY:A}},goToPage:function(e,t,g,n){n=n||this.options.bounceEasing,e>=this.pages.length?e=this.pages.length-1:e<0&&(e=0),t>=this.pages[e].length?t=this.pages[e].length-1:t<0&&(t=0);var A=this.pages[e][t].x,C=this.pages[e][t].y;g=void 0===g?this.options.snapSpeed||i.max(i.max(i.min(i.abs(A-this.x),1e3),i.min(i.abs(C-this.y),1e3)),300):g,this.currentPage={x:A,y:C,pageX:e,pageY:t},this.scrollTo(A,C,g,n)},next:function(e,t){var g=this.currentPage.pageX,n=this.currentPage.pageY;g++,g>=this.pages.length&&this.hasVerticalScroll&&(g=0,n++),this.goToPage(g,n,e,t)},prev:function(e,t){var g=this.currentPage.pageX,n=this.currentPage.pageY;g--,g<0&&this.hasVerticalScroll&&(g=0,n--),this.goToPage(g,n,e,t)},_initKeys:function(e){var t,g={pageUp:33,pageDown:34,end:35,home:36,left:37,up:38,right:39,down:40};if("object"==A(this.options.keyBindings))for(t in this.options.keyBindings)"string"==typeof this.options.keyBindings[t]&&(this.options.keyBindings[t]=this.options.keyBindings[t].toUpperCase().charCodeAt(0));else this.options.keyBindings={};for(t in g)this.options.keyBindings[t]=this.options.keyBindings[t]||g[t];c.addEvent(C,"keydown",this),this.on("destroy",function(){c.removeEvent(C,"keydown",this)})},_key:function(e){if(this.enabled){var t,g=this.options.snap,n=g?this.currentPage.pageX:this.x,A=g?this.currentPage.pageY:this.y,C=c.getTime(),I=this.keyTime||0;switch(this.options.useTransition&&this.isInTransition&&(t=this.getComputedPosition(),this._translate(i.round(t.x),i.round(t.y)),this.isInTransition=!1),this.keyAcceleration=C-I<200?i.min(this.keyAcceleration+.25,50):0,e.keyCode){case this.options.keyBindings.pageUp:this.hasHorizontalScroll&&!this.hasVerticalScroll?n+=g?1:this.wrapperWidth:A+=g?1:this.wrapperHeight;break;case this.options.keyBindings.pageDown:this.hasHorizontalScroll&&!this.hasVerticalScroll?n-=g?1:this.wrapperWidth:A-=g?1:this.wrapperHeight;break;case this.options.keyBindings.end:n=g?this.pages.length-1:this.maxScrollX,A=g?this.pages[0].length-1:this.maxScrollY;break;case this.options.keyBindings.home:n=0,A=0;break;case this.options.keyBindings.left:n+=g?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.up:A+=g?1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.right:n-=g?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.down:A-=g?1:5+this.keyAcceleration>>0;break;default:return}if(g)return void this.goToPage(n,A);n>0?(n=0,this.keyAcceleration=0):n<this.maxScrollX&&(n=this.maxScrollX,this.keyAcceleration=0),A>0?(A=0,this.keyAcceleration=0):A<this.maxScrollY&&(A=this.maxScrollY,this.keyAcceleration=0),this.scrollTo(n,A,0),this.keyTime=C}},_animate:function(e,t,g,n){function A(){var a,l,d,h=c.getTime();if(h>=o)return C.isAnimating=!1,C._translate(e,t),void(C.resetPosition(C.options.bounceTime)||C._execEvent("scrollEnd"));h=(h-r)/g,d=n(h),a=(e-I)*d+I,l=(t-i)*d+i,C._translate(a,l),C.isAnimating&&s(A)}var C=this,I=this.x,i=this.y,r=c.getTime(),o=r+g;this.isAnimating=!0,A()},handleEvent:function(e){switch(e.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(e);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(e);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(e);break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(e);break;case"wheel":case"DOMMouseScroll":case"mousewheel":this._wheel(e);break;case"keydown":this._key(e);break;case"click":this.enabled&&!e._constructed&&(e.preventDefault(),e.stopPropagation())}}},a.prototype={handleEvent:function(e){switch(e.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(e);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(e);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(e)}},destroy:function(){this.options.fadeScrollbars&&(clearTimeout(this.fadeTimeout),this.fadeTimeout=null),this.options.interactive&&(c.removeEvent(this.indicator,"touchstart",this),c.removeEvent(this.indicator,c.prefixPointerEvent("pointerdown"),this),c.removeEvent(this.indicator,"mousedown",this),c.removeEvent(C,"touchmove",this),c.removeEvent(C,c.prefixPointerEvent("pointermove"),this),c.removeEvent(C,"mousemove",this),c.removeEvent(C,"touchend",this),c.removeEvent(C,c.prefixPointerEvent("pointerup"),this),c.removeEvent(C,"mouseup",this)),this.options.defaultScrollbars&&this.wrapper.parentNode&&this.wrapper.parentNode.removeChild(this.wrapper)},_start:function(e){var t=e.touches?e.touches[0]:e;e.preventDefault(),e.stopPropagation(),this.transitionTime(),this.initiated=!0,this.moved=!1,this.lastPointX=t.pageX,this.lastPointY=t.pageY,this.startTime=c.getTime(),this.options.disableTouch||c.addEvent(C,"touchmove",this),this.options.disablePointer||c.addEvent(C,c.prefixPointerEvent("pointermove"),this),this.options.disableMouse||c.addEvent(C,"mousemove",this),this.scroller._execEvent("beforeScrollStart")},_move:function(e){var t,g,n,A,C=e.touches?e.touches[0]:e;c.getTime();this.moved||this.scroller._execEvent("scrollStart"),this.moved=!0,t=C.pageX-this.lastPointX,this.lastPointX=C.pageX,g=C.pageY-this.lastPointY,this.lastPointY=C.pageY,n=this.x+t,A=this.y+g,this._pos(n,A),e.preventDefault(),e.stopPropagation()},_end:function(e){if(this.initiated){if(this.initiated=!1,e.preventDefault(),e.stopPropagation(),c.removeEvent(C,"touchmove",this),c.removeEvent(C,c.prefixPointerEvent("pointermove"),this),c.removeEvent(C,"mousemove",this),this.scroller.options.snap){var t=this.scroller._nearestSnap(this.scroller.x,this.scroller.y),g=this.options.snapSpeed||i.max(i.max(i.min(i.abs(this.scroller.x-t.x),1e3),i.min(i.abs(this.scroller.y-t.y),1e3)),300);this.scroller.x==t.x&&this.scroller.y==t.y||(this.scroller.directionX=0,this.scroller.directionY=0,this.scroller.currentPage=t,this.scroller.scrollTo(t.x,t.y,g,this.scroller.options.bounceEasing))}this.moved&&this.scroller._execEvent("scrollEnd")}},transitionTime:function(e){e=e||0;var t=c.style.transitionDuration;if(t&&(this.indicatorStyle[t]=e+"ms",!e&&c.isBadAndroid)){this.indicatorStyle[t]="0.0001ms";var g=this;s(function(){"0.0001ms"===g.indicatorStyle[t]&&(g.indicatorStyle[t]="0s")})}},transitionTimingFunction:function(e){this.indicatorStyle[c.style.transitionTimingFunction]=e},refresh:function(){this.transitionTime(),this.options.listenX&&!this.options.listenY?this.indicatorStyle.display=this.scroller.hasHorizontalScroll?"block":"none":this.options.listenY&&!this.options.listenX?this.indicatorStyle.display=this.scroller.hasVerticalScroll?"block":"none":this.indicatorStyle.display=this.scroller.hasHorizontalScroll||this.scroller.hasVerticalScroll?"block":"none",this.scroller.hasHorizontalScroll&&this.scroller.hasVerticalScroll?(c.addClass(this.wrapper,"iScrollBothScrollbars"),c.removeClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="8px":this.wrapper.style.bottom="8px")):(c.removeClass(this.wrapper,"iScrollBothScrollbars"),c.addClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="2px":this.wrapper.style.bottom="2px")),c.getRect(this.wrapper),this.options.listenX&&(this.wrapperWidth=this.wrapper.clientWidth,this.options.resize?(this.indicatorWidth=i.max(i.round(this.wrapperWidth*this.wrapperWidth/(this.scroller.scrollerWidth||this.wrapperWidth||1)),8),this.indicatorStyle.width=this.indicatorWidth+"px"):this.indicatorWidth=this.indicator.clientWidth,this.maxPosX=this.wrapperWidth-this.indicatorWidth,"clip"==this.options.shrink?(this.minBoundaryX=8-this.indicatorWidth,this.maxBoundaryX=this.wrapperWidth-8):(this.minBoundaryX=0,this.maxBoundaryX=this.maxPosX),this.sizeRatioX=this.options.speedRatioX||this.scroller.maxScrollX&&this.maxPosX/this.scroller.maxScrollX),this.options.listenY&&(this.wrapperHeight=this.wrapper.clientHeight,this.options.resize?(this.indicatorHeight=i.max(i.round(this.wrapperHeight*this.wrapperHeight/(this.scroller.scrollerHeight||this.wrapperHeight||1)),8),this.indicatorStyle.height=this.indicatorHeight+"px"):this.indicatorHeight=this.indicator.clientHeight,this.maxPosY=this.wrapperHeight-this.indicatorHeight,"clip"==this.options.shrink?(this.minBoundaryY=8-this.indicatorHeight,this.maxBoundaryY=this.wrapperHeight-8):(this.minBoundaryY=0,this.maxBoundaryY=this.maxPosY),this.maxPosY=this.wrapperHeight-this.indicatorHeight,this.sizeRatioY=this.options.speedRatioY||this.scroller.maxScrollY&&this.maxPosY/this.scroller.maxScrollY),this.updatePosition()},updatePosition:function(){var e=this.options.listenX&&i.round(this.sizeRatioX*this.scroller.x)||0,t=this.options.listenY&&i.round(this.sizeRatioY*this.scroller.y)||0;this.options.ignoreBoundaries||(e<this.minBoundaryX?("scale"==this.options.shrink&&(this.width=i.max(this.indicatorWidth+e,8),this.indicatorStyle.width=this.width+"px"),e=this.minBoundaryX):e>this.maxBoundaryX?"scale"==this.options.shrink?(this.width=i.max(this.indicatorWidth-(e-this.maxPosX),8),this.indicatorStyle.width=this.width+"px",e=this.maxPosX+this.indicatorWidth-this.width):e=this.maxBoundaryX:"scale"==this.options.shrink&&this.width!=this.indicatorWidth&&(this.width=this.indicatorWidth,this.indicatorStyle.width=this.width+"px"),t<this.minBoundaryY?("scale"==this.options.shrink&&(this.height=i.max(this.indicatorHeight+3*t,8),this.indicatorStyle.height=this.height+"px"),t=this.minBoundaryY):t>this.maxBoundaryY?"scale"==this.options.shrink?(this.height=i.max(this.indicatorHeight-3*(t-this.maxPosY),8),this.indicatorStyle.height=this.height+"px",t=this.maxPosY+this.indicatorHeight-this.height):t=this.maxBoundaryY:"scale"==this.options.shrink&&this.height!=this.indicatorHeight&&(this.height=this.indicatorHeight,this.indicatorStyle.height=this.height+"px")),this.x=e,this.y=t,this.scroller.options.useTransform?this.indicatorStyle[c.style.transform]="translate("+e+"px,"+t+"px)"+this.scroller.translateZ:(this.indicatorStyle.left=e+"px",this.indicatorStyle.top=t+"px")},_pos:function(e,t){e<0?e=0:e>this.maxPosX&&(e=this.maxPosX),t<0?t=0:t>this.maxPosY&&(t=this.maxPosY),e=this.options.listenX?i.round(e/this.sizeRatioX):this.scroller.x,t=this.options.listenY?i.round(t/this.sizeRatioY):this.scroller.y,this.scroller.scrollTo(e,t)},fade:function(e,t){if(!t||this.visible){clearTimeout(this.fadeTimeout),this.fadeTimeout=null;var g=e?250:500,n=e?0:300;e=e?"1":"0",this.wrapperStyle[c.style.transitionDuration]=g+"ms",this.fadeTimeout=setTimeout(function(e){this.wrapperStyle.opacity=e,this.visible=+e}.bind(this,e),n)}}},r.utils=c,void 0!==e&&e.exports?e.exports=r:void 0!==(n=function(){return r}.call(t,g,t,e))&&(e.exports=n)}(window,document,Math)},331:function(e,t){function g(e,t,g){return t in e?Object.defineProperty(e,t,{value:g,enumerable:!0,configurable:!0,writable:!0}):e[t]=g,e}!function(){Date.now||(Date.now=function(){return(new Date).getTime()});var t=function(){this.m_data={},this.timer=null,this.screenHeight=window.innerHeight||window.screen.height,this.nodeList=null};t.prototype={setId:function(e,t){document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+"; expires=Fri, 31 Dec 9999 23:59:59 GMT; path=/"},hasId:function(e){return new RegExp("(?:^|;\\s*)"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie)},creatId:function(){if(!this.hasId("_ma_id")){var e=(new Date).getTime(),t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var g=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?g:7&g|8).toString(16)});this.setId("_ma_id",t)}},getByClassName:function(e){var t=new Array,g=0,n=document.getElementsByTagName("*");for(var A in n)1==n[A].nodeType&&n[A].getAttribute("class")&&n[A].getAttribute("class").match(e)&&(t[g]=n[A],g++);return t},getNode:function(e){var t=this.getByClassName(e),n=[];for(var A in t){var C,I=t[A],i=I.offsetHeight,r="part"+A+"Enter",o="part"+A+"Leave";n.push({height:i,posY:0,node:I,isHeader:!1,isFooter:!1}),Object.assign(this.m_data,(C={},g(C,r,""),g(C,o,""),C))}this.nodeList=n},enterPart:function(e,t,g,n){var A=!1,C=!1;if(t>-e.height&&t<g/2&&(A=!0),e.isHeader&&A){var I="part"+n+"Enter";this.m_data[I]=Date.now()}t<g&&t>g/2&&(C=!0),e.isHeader=C},leavePart:function(e,t,g,n){var A=!1;if(t>-(e.height-g/2)&&t<g/2&&(A=!0),!e.isHeader&&e.isFooter&&!A){var C="part"+n+"Leave";this.m_data[C]=Date.now()}e.isFooter=A},recordTime:function(){var e=this;clearTimeout(this.timer);var t=this.screenHeight;this.nodeList.forEach(function(g,n){var A=g.node.getBoundingClientRect().top;e.enterPart(g,A,t,n),e.leavePart(g,A,t,n),g.posY=A}),this.timer=setTimeout(function(){e.recordTime()},300)},init:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mai_d";this.creatId(),this.getNode(e),this.m_data.enterpagetime=Date.now(),this.recordTime()},getData:function(){return this.m_data}},e.exports=new t}()},332:function(e,t,g){"use strict";function n(e){return{type:A.a.SET_ORDER_DATA,orderData:e}}Object.defineProperty(t,"__esModule",{value:!0}),t.setOrderData=n;var A=g(213);!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&__REACT_HOT_LOADER__.register(n,"setOrderData","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/actions/order.js")}()},337:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(2),i=g.n(I),r=g(29),o=g(17),a=g.n(o),s=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),c=function(e){function t(e){n(this,t);var g=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return g.state={marqueeData:"",marqueeData_default:[{user:"he***<EMAIL> 测试",content:"测试数据1"},{user:"he***<EMAIL> 测试",content:"测试数据2"},{user:"he***<EMAIL> 香港",content:"测试数据3"},{user:"he***<EMAIL> 香港",content:"测试数据4"},{user:"he***<EMAIL> 香港",content:"测试数据5"}],interval:null},g.$li=void 0,g.$ul=void 0,g}return C(t,e),s(t,[{key:"componentDidMount",value:function(){this.getComment(),this.marquee()}},{key:"componentWillUnmount",value:function(){var e=this.state.interval;e&&clearInterval(e)}},{key:"getComment",value:function(){var e=this,t={pay_point:r.f};a.a.ajax({url:r.d.FETCH_COMMENT,type:"GET",data:t,success:function(t){t.comment?e.setState({marqueeData:t.comment}):BJ_REPORT.report({msg:t.msg+"用户评论接口："+r.d.FETCH_COMMENT,target:"Comment.jsx",rowNum:36})},error:function(e){BJ_REPORT.report({msg:JSON.stringify(e)+"用户评论接口",target:"Comment.jsx",rowNum:46})}})}},{key:"marquee",value:function(){var e=this,t=setInterval(function(){var t=e.$ul;t.style.transition="",t.style.top="0px",e.moveFirstDataToTail()},2500);this.setState({interval:t})}},{key:"moveFirstDataToTail",value:function(){var e=this,t=this.state.marqueeData||this.state.marqueeData_default,g=t.shift();t.push(g),this.setState({marqueeData:t}),setTimeout(function(){e.scrollComment()},4)}},{key:"scrollComment",value:function(){var e=this.$li.offsetHeight,t=this.$ul;t.style.transition="top ease-in 2s",t.style.top=-e+"px"}},{key:"render",value:function(){var e=this,t=this.state.marqueeData;return i.a.createElement("div",{className:"new-comment mai_d"},i.a.createElement("h3",null,"◆ 用户评论 ◆"),i.a.createElement("div",{className:"marquee-wrapper"},i.a.createElement("ul",{className:"comment-list info-list-box",ref:function(t){e.$ul=t}},t&&t.map(function(t,g){return i.a.createElement("li",{key:g,ref:function(t){0===g&&t&&(e.$li=t)}},i.a.createElement("p",{className:"user"},t.user),i.a.createElement("p",{className:"detail"},t.content))}))))}}]),t}(I.Component),l=c;t.a=l;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(c,"Comment","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/order/Comment.jsx"),__REACT_HOT_LOADER__.register(l,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/order/Comment.jsx"))}()},338:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(59),i=(g.n(I),g(2)),r=g.n(i),o=g(700),a=(g.n(o),g(29)),s=g(10),c=g.n(s),l=g(13),d=g(52),h=g(17),m=g.n(h),u=g(143),p=g(331),f=g.n(p),_=g(60),v=g(100),y=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),b=function(e){function t(e){n(this,t);var C=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return C.props=e,C.state={lap:g.i(l.a)("lap")||c.a.getItem("_lap")||a.c,channel:g.i(l.a)("channel")||c.a.getItem("_channel")||a.b,schannel:g.i(l.a)("schannel")||"",lanternId:parseInt(g.i(l.a)("lantern_id"))||1,tipFlag:!1,tipText:"",loadingFlag:!1,loadingText:"",name:"",gender:1,email:"",dayLimit:0,isPublic:1,desire:""},C}return C(t,e),y(t,[{key:"componentDidMount",value:function(){var e=this;window.addEventListener("load",function(){f.a.init(),e.getInfo()})}},{key:"sendDa",value:function(e,t){m.a.ajax({url:e,data:t,success:function(){},error:function(){}})}},{key:"getInfo",value:function(){var e=c.a.getItem("_ma_id"),t=this.state.channel,g=a.f,n=Date.now(),A=navigator.userAgent,C={ma_id:e,channel:t,pay_point:g,useragent:A,enterpagetime:n};this.sendDa(a.d.TRACK_INFO,C)}},{key:"getScan",value:function(){var e=c.a.getItem("_ma_id"),t=this.state.channel,g=a.f,n=Date.now(),A=navigator.userAgent,C={ma_id:e,channel:t,useragent:A,pay_point:g,leavepagetime:n},I=f.a.getData();return Object.assign(C,I),{leavepagetime:n,useragent:A,catchTime:I}}},{key:"setTipShowAndHide",value:function(e,t){var g=this;t=t||1500,this.setState({tipFlag:!0,tipText:e}),setTimeout(function(){g.setState({tipFlag:!1,tipText:""})},t)}},{key:"clickPublicBtnHandle",value:function(e){this.setState({isPublic:e})}},{key:"validateName",value:function(){var e=this.state.name,t=u.a(e),g=u.d(e),n=e.length<=5,A=t&&g&&n,C="";return A||(t?g?n||(C="姓名长度不能超过5！"):C="姓名必须为汉字！":C="姓名不能为空！"),{name:e,flag:A,tip:A?"":C}}},{key:"validateDesire",value:function(){var e=this.state.desire,t=u.a(e),g=e.length<=140,n=t&&g,A="";return n||(t?g||(A="愿望字数不能超过140个！"):A="愿望不能为空！"),{desire:e,flag:n,tip:n?"":A}}},{key:"validateAll",value:function(){var e=this.state,t=e.channel,n=e.schannel,A=e.gender,C=e.email,I=e.dayLimit,i=e.isPublic,r=e.lanternId,o=this.props.orderData.datePickerBirthFormat,s=this.validateName(),l=this.validateDesire(),h=c.a.getItem("_ma_id"),m=this.getScan(),u=0;if(!s.flag)return void this.setTipShowAndHide(s.tip);if(!o)return this.setTipShowAndHide("请选择出生日期"),!1;if(!l.flag)return void this.setTipShowAndHide(l.tip);g.i(d.c)()&&(u=1);var p={pay_point:a.f,username:s.name,gender:A,birthday:o,day_limit:I,email:C,channel:t,schannel:n,is_public:i,lantern_id:r,desire:l.desire,ma_id:h,isApp:u},f={leavepagetime:m.leavepagetime,useragent:m.useragent};return Object.assign(p,f,m.catchTime),p}},{key:"submitClickHandle",value:function(){var e=this,n=this.state,A=this.validateAll();A&&(e.setState({loadingFlag:!0,loadingText:"点灯中",canClick:!1}),m.a.ajax({url:a.d.REGISTER_ORDER,type:"POST",data:A,success:function(C){if(C.order_id){var I=C.order_id,i={order_id:I,pay_point:A.pay_point,pay_confirm_url:location.origin+a.h.USER_PAY_CONFIRM,server_id:a.j},r={uaMarks:a.k,version:a.l},o=a.h.PAY+"?order_id="+I,s=g.i(d.d)(a.m,A,I,a.e);try{t.saveOrderIdToLocalStorage(I)}catch(e){}"shunlimmzhifu"===n.channel?location.href=a.h.PAY+"?order_id="+I:1==C.is_xcx_new_model?wx.miniProgram.navigateTo({url:C.xcx_new_model_url}):(g.i(d.e)(s),g.i(d.f)(i,o,r))}else C.msg&&(e.setTipShowAndHide(C.msg),BJ_REPORT.report({msg:C.msg+"下单接口："+a.d.REGISTER_ORDER,target:"CommonForm.jsx",rowNum:297}));e.setState({loadingFlag:!1,loadingText:"",canClick:!0})},error:function(t){e.setTipShowAndHide("您的网络不太给力，请稍后再试！"),BJ_REPORT.report({msg:a.d.REGISTER_ORDER+"(下单接口):"+JSON.stringify(t),target:"CommonForm.jsx",rowNum:311})}}))}},{key:"render",value:function(){var e=this,t=this.state,n=this.props,A=n.orderData,C=n.setOrderData;return r.a.createElement("section",null,r.a.createElement("div",{className:"m-form"},r.a.createElement("div",{className:"f-box"},r.a.createElement("ul",{className:"form"},r.a.createElement("li",{className:"clear"},r.a.createElement("div",{className:"left"},"您的姓名"),r.a.createElement("div",{className:"auto"},r.a.createElement("input",{type:"text",placeholder:"请输入",value:this.state.name,onChange:function(t){var n=g.i(l.f)(t.target.value);e.setState({name:n})}}))),r.a.createElement("li",{className:"clear"},r.a.createElement("div",{className:"left"},"出生日期"),r.a.createElement("div",{className:"auto"},r.a.createElement("input",{type:"text",value:A.datePickerBirth,onClick:function(){C({datePickerDisplay:1})},onFocus:function(e){e.target.blur()},name:"date",readOnly:"readonly",placeholder:"请选择(必填)"}))),r.a.createElement("li",{className:"textarea"},r.a.createElement("div",{className:"label"},"您的愿望:"),r.a.createElement("div",{className:"wish-text"},r.a.createElement("textarea",{type:"text",placeholder:"请输入心中愿望（140字以内）",maxLength:"140",value:this.state.desire,onChange:function(t){var n=g.i(l.f)(t.target.value);e.setState({desire:n})}}))))),r.a.createElement("div",{className:"show-square"},r.a.createElement("p",null,"在许愿广场显示：",r.a.createElement("span",{onClick:this.clickPublicBtnHandle.bind(this,1)},r.a.createElement("i",{className:1===t.isPublic?"checked":""}),"显示"),r.a.createElement("span",{onClick:this.clickPublicBtnHandle.bind(this,0)},r.a.createElement("i",{className:0===t.isPublic?"checked":""}),"不显示"))),r.a.createElement("div",{className:"show-tips"},r.a.createElement("p",null,"显示愿望至许愿点灯广场，让更多有缘人为您祈福")),r.a.createElement("div",{className:"m-btn",onClick:this.submitClickHandle.bind(this)},r.a.createElement("img",{src:"//zxcs.ggwan.com/xuyuandiandeng/images/order_btn.png?timestamp=1531895008613",alt:""})),r.a.createElement("div",{className:"m-query"},r.a.createElement("a",{href:a.h.INDEX+"?tab=3"},"查询历史订单>"))),t.tipFlag&&r.a.createElement(_.a,{text:t.tipText}),t.loadingFlag&&r.a.createElement(v.a,{text:this.state.loadingText}))}}],[{key:"saveOrderIdToLocalStorage",value:function(e){if(localStorage){var t=localStorage.getItem(a.n);t=t?t.split(","):[],t.push(e),localStorage.setItem(a.n,t.join(","))}}}]),t}(i.Component),E=b;t.a=E;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(b,"CommonForm","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/order/CommonForm.jsx"),__REACT_HOT_LOADER__.register(E,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/order/CommonForm.jsx"))}()},339:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(2),i=g.n(I),r=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),o=function(e){function t(){return n(this,t),A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this))}return C(t,e),r(t,[{key:"render",value:function(){var e=this.props.lanternDetail;return i.a.createElement("div",{className:"main-content mai_d"},i.a.createElement("div",{className:"order-top"}),e&&i.a.createElement("div",{className:"order-middle"},i.a.createElement("div",{className:"item intro"},i.a.createElement("h4",null,e.name+"介绍："),i.a.createElement("p",null,e.description)),i.a.createElement("div",{className:"item effect"},i.a.createElement("h4",null,"功效："),e.function&&i.a.createElement("ul",{className:"li-left clear"},e.function.length>0&&e.function.map(function(t,g){return i.a.createElement("li",{className:1===e.function.length?"w80":"",key:g},i.a.createElement("div",{className:"li-wrapper"},i.a.createElement("img",{src:t.img_url,alt:""}),i.a.createElement("p",null,t.name)))}))),e.face&&i.a.createElement("div",{className:"item fit"},i.a.createElement("h4",null,"适用对象："),i.a.createElement("p",null,e.face)),e.recommend&&i.a.createElement("div",{className:"item target"},i.a.createElement("h4",null,"2018推荐对象："),i.a.createElement("p",null,e.recommend)),i.a.createElement("div",{className:"item tips"},i.a.createElement("h4",null,"点灯温馨提示："),i.a.createElement("p",null,"点灯之后，在点灯期限内您的许愿灯将会一直长明在“我的许愿灯池”里，您随时可以查看。如果您选择了公开展示您的许愿灯，则许愿灯会同时出现在“众缘点灯广场”中，与其它缘主的许愿灯一齐长明。"))),i.a.createElement("div",{className:"order-bottom"}))}}]),t}(I.Component),a=o;t.a=a;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(o,"Content","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/order/Content.jsx"),__REACT_HOT_LOADER__.register(a,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/components/order/Content.jsx"))}()},341:function(e,t,g){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=g(43),A=(g.n(n),g(59)),C=(g.n(A),g(2)),I=g.n(C),i=g(70),r=(g.n(i),g(114)),o=g(212),a=g(324),s=g(321),c=g.i(r.a)(a.a);g.i(i.render)(I.a.createElement(o.a,{store:c},I.a.createElement(s.a,null)),document.getElementById("page-order"));!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&__REACT_HOT_LOADER__.register(c,"store","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/xuyuandiandeng/entry/order.js")}()},35:function(e,t){e.exports=function(){"use strict";var e=function(){},t=e.prototype,g="一七万丈三上下不与专且世业东丝丢两严丧个中丰串临为主举久么义之乎乏乐乘九乞也习乡书买乱了予争事二于亏云云互五井亚些亡交亦产享京亮亲人亿什仁仅仇今介仍从仑仓仔他付代令以们件价任份仿企伊伍休众优伙伙会伟传伤伦伯估伴伸似但位低住体何余余余佛作你佩佳佶使例供依侥侧侵便促俊俗保信俩修俱倍倒候借值倾假偏做停健偶偷偿傍储催傻像僚僻儿元兄充兆先光克免党入全八公六兰共关兴兵其具典兹养兼冀内册再写军农冠冤冬冲冲决况冷净凄准凉减凑几凡凤凯凶凶出击函凿分切刊划列刘则刚创初删判利别到制券刺刻刽剂削前剎剥剧剩割力劝办功加务劣动助努劫励劲劳势勇勒募勤勾包匈化北匹区医匿十千升午半华协单卖南博占占卡卫印危即却卵厂历历厉压厌厕厘原厢厥去县参又叉及友双反发发叔取受变口古句另只叫召可台台史右叶号司吃各合吉同名后向向吓吕吗君吝吞否吧含听启吴吵吶吸吹吻吼吾呀告员呢周周味呵呼命咀和咖咧咽品哄哈哉响哑哗哥哪哭哲唉唯唱商啊啜啦善喊喔喜喧嗯嗲嘛嘲嘴嘶嘿噢器噩噪嚼四回因团园困围固国图圆圈土圣在地场址均坏坐块坚坛坦垄型垒埋城培基堂堆堕堪堵塑塔塞填境墓墙增墟士壮声壳壹处备复夕外多夜够大天太夫央失头夸夹夺奇奉奋奏契奔奖套奠奢女奴她好如妇妈妖妙妥妨妹始姑姓委姜姨姻威娃娄娘娱娶婆婚婴媒嫉嫌子孔字存孙孤学孩孽宁它宅守安完宕宗官定宜宝实宠审客宣宪宰害家家容寂寄密富寒察寡对寻导封射将尊小少尔尚尝尤尬就尴尺尽尾局屁层居屈届屋屎展属山岁岂岗岸崇州工左巧巨差己已巴巷币市布师希帝带席帮常幕平年并幸幸幻幼广庄庇床序库应底店府庞废度座庭庶康庸廊廖延建开异弃弄弊式引弟张弥弱弹强归当录形彩彪彭彰影役彻彼往征征径径待很律徒得微德徽心必忆忍志志忘忙忠快念忽忿怀态怆怎怒怕怜思急性怨怪总恋恍恐恒恢恨恩息恰恳恶悉悔悟您悬悯悲情惊惑惕惜惨惯想愈意愚感愤愿慈慢慰憾懂懒戍戏成我或战戚截戴户房所扁扈手才扑打扔托扛扣执扩扫扬扭扮扯扰批扼找承技抄抉把抑抒抓投抗折折抚抢护报披抬抱抵押抽担拆拉拍拒拓拖拘拚招拜拟拢拥拨择括拼拿持挂指按挑挖挤挥挨挫振挺挽捅捉捐捕捞损换据掀掉掌掏掐排掘探接控推描提插握揣揪揭援搁搜搞摄摆摇摊摘摧摩撇撑撤撬播擅攒攫支收改攻放政故效敌敏救教敛敢散敬数整文斗料斥断斯新方施旁旅旋族旗无既日旦旧早时昌明昏易星春昧昨是显晓晚普景晰晴智暂暇暑暖暗暴曲更曾替最月有朋服朔望朝期木未末本术机杀杂权材村束条来杨杯杰板板极构枉析林果枯架某染查标栈栏树校样核根格框案桌档桦桶梦梨梯检棒棚椅楚楼概榜榨槛樟模横欠次欢欧欲款歌止正此步武歧死殊残段殷毁母每毒比毕毛毫民气水永求汇汉江污汰沉沙沟没沦河油治沾沿泄法泛泡波泣泥注泪泯泰泽洋洗洛津洪洲活派流浅测济浏浑浓浩浪浮海浸消涉涌涛涡涤润涨液涵淘淡深混添清渐渗渡渣渥温港游游湖湾湿源滋滑滚滞满滴漂漏演漩漫潘潜潮澡激灌火灭灯灰灵灾灿炒炫炸点烂烈烦烧热焉焦然煤照煽熊熏熟燃燥爆爱父爷爸爽片版牌牙牛牢物牲牵特牺犯状犹狂狄狗独狭狱狼猖猛猜獗率玉王玩环现珊班球理琴瑞瑟璋瓦瓶甚生用甫田由甲申电男画畅界留略番畴疆疑疗疮疲病痒痛登白百皂的皆皇皮益监盒盖盘盟目盯盲直相盼盾省看真眷眼著睛睡瞧瞭矛矣知短石矿码研破砸础硬确碍碗碣碧碰磁磨示社祖神票禁福离禽秀私种科秒秩积称移稀程稍税稚稳稻究穷空穿突窗立站竞竟章童端竹竿笑笔符笨第等筑答策筝筹签签简算管篇籍类粉粒粗粮粹精糊糕糟糟系系素索紧紫累繁纠红约级纪纯纲纳纷纸纽纾线练组绅细织终绍经结给络绝统继绩绪续绳维绵综绿缓缔编缚缝缩缴缺网罗罢罪置署羊美羞群翔翱翻翼耀耀老考者而耐耗耳耶耸耻聋职联聚肉肌肚肝股肤肩肪肯育肾胁胃胆背胚胜胞胡胳能脂脆脉脏脏脑脚脱脸腐腥腾臂自臭至致舆舍舍舒舞舟航般舰舱船良艰色艺节芦芬花苏苟若苦英范茅茍草荐荒荡荣药荼莎莫莱莲获获菜营萧落葬蒙蓄蓍蓝蔑蔓薄薪藉藏虑虚虫虽蚀蚁蚂蚊蛋蛙蛛蜂蝴蝶融蠢血行衍衡衣补表袋袖被袭裁裂装裸西要覆见观规视览觉角解触言誉警譬计订认讨让议讯记讲讳讶许论讽设访证评识诉词译试诗诚话询该详语误说请诸读课谁调谅谈谋谎谓谢谣谩谬象貌贝负财责败货质贩贪贫购贯贴贵费资赌赏赖赘赚赛赞赞赢赤赫走赴赶起超越趋趟趣足跃跋跑距跟跨路跳践踏踩踮蹲身躲车轨转轮软轰轻载较辄辆辈辉辐辑输辛辜辞辣辨辩辱边达迁迅过迈迎运近返还这进远违连迫述迷迹迹追退送适逃逆选透逐递途通逛速造逢逮逸逻遇遍遑道遗遥遭遮遵避邓那邮邻郑部郭都酋配酒酷酸酿醒采释里重野量金针钙钟钢钦钮钱钻铁铅铜铲链销锁锄错锦键锺镖镜长门闪闭问闲间闷闹闻阂阅阙队防阳阴阵阶阻阿附际陆陈陋降限院除险陪陶陷随隐隔隘障隶难雄集雇雇雏雨雳零雷需霄露霹青静非靠面革鞋鞭韩音页顶顷项顺须顾顿颂预领颇频颖题额风飞食餐饥饭饰饱饼饿馆首香马驭驱驳驼驾骂骆验骑骗骤骨髓高鬼魂魏鱼鲁鲜鸣鸭鸯鸳鹰鹿麻黄黑鼎鼓鼠齐齿龙樱剑皑蔼袄奥坝颁绊绑镑谤鲍钡狈惫绷毙贬辫鳖瘪濒滨宾摈钵铂卜蚕惭苍沧诧搀掺蝉馋谗缠阐颤肠钞尘衬惩骋痴迟驰炽踌绸橱厨闯锤绰赐聪葱囱丛窜贷郸掸惮诞挡捣岛祷盗垫淀钓迭谍叠钉锭栋冻犊镀锻缎兑吨钝鹅讹饵贰罚阀珐矾钒纺坟粪枫锋疯冯辅赋讣秆赣冈皋镐鸽阁铬龚宫巩贡钩蛊剐硅龟闺诡柜辊锅骇鹤贺鸿壶沪唤痪焕涣贿秽烩诲绘荤祸讥鸡缉蓟荚颊贾钾歼笺缄茧碱硷拣捡俭鉴贱饯溅涧浆蒋桨酱胶浇骄娇搅铰矫饺绞轿秸茎颈痉厩驹锯惧鹃绢洁诫谨晋烬荆诀钧骏颗垦抠裤侩宽旷岿窥馈溃阔蜡腊拦篮阑澜谰揽缆滥涝镭篱鲤礼丽砾沥镰涟帘炼辽镣猎鳞凛赁龄铃凌岭馏咙笼陇搂篓卢颅庐炉掳卤虏赂禄驴铝侣屡缕滤峦挛孪滦抡纶萝锣箩骡玛麦瞒馒蛮猫锚铆贸霉镁锰谜觅缅庙闽铭亩钠挠恼馁腻撵捻鸟聂啮镊镍柠狞拧泞脓疟诺鸥殴呕沤赔喷鹏飘苹凭泼铺朴谱脐讫扦钎谦钳谴堑枪呛蔷锹桥乔侨翘窍窃氢庆琼躯龋颧鹊饶绕韧纫绒锐闰洒萨鳃伞骚涩纱筛晒陕赡缮赊慑婶狮尸驶寿兽枢赎竖帅硕烁饲怂讼诵擞肃绥笋琐獭挞瘫滩谭叹汤烫绦誊锑屉厅烃涂颓蜕鸵驮椭洼袜弯顽韦潍苇伪纬纹瓮挝蜗窝呜钨乌诬芜坞雾锡铣虾辖峡侠厦锨纤咸贤衔献馅羡镶啸蝎挟携谐泻锌衅汹锈绣嘘轩癣绚勋驯训逊鸦阉烟盐颜阎艳砚彦谚疡瑶尧窑铱颐仪彝诣谊绎荫银饮缨莹萤荧蝇哟佣痈踊咏忧铀诱渔屿吁御渊辕缘钥岳粤悦郧匀陨蕴酝晕韵赃枣灶贼赠扎札轧铡闸诈斋债毡盏斩辗崭绽帐账胀赵蛰辙锗贞侦诊镇挣睁狰帧挚掷帜肿诌轴皱昼猪诛烛瞩嘱贮铸驻砖桩妆锥坠缀谆浊渍踪纵邹诅郁傥",n="一七萬丈三上下不與專且世業東絲丟兩嚴喪個中豐串臨為主舉久麼義之乎乏樂乘九乞也習鄉書買亂了予爭事二於虧雲云互五井亞些亡交亦產享京亮親人億什仁僅仇今介仍從崙倉仔他付代令以們件價任份仿企伊伍休眾優伙夥會偉傳傷倫伯估伴伸似但位低住體何余餘馀佛作你佩佳佶使例供依僥側侵便促俊俗保信倆修俱倍倒候借值傾假偏做停健偶偷償傍儲催傻像僚僻兒元兄充兆先光克免黨入全八公六蘭共關興兵其具典茲養兼冀內冊再寫軍農冠冤冬衝沖決況冷淨淒準涼減湊幾凡鳳凱兇凶出擊函鑿分切刊劃列劉則剛創初刪判利別到製券刺刻劊劑削前剎剝劇剩割力勸辦功加務劣動助努劫勵勁勞勢勇勒募勤勾包匈化北匹區醫匿十千升午半華協單賣南博占佔卡衛印危即卻卵廠歷曆厲壓厭廁釐原廂厥去縣參又叉及友雙反發髮叔取受變口古句另只叫召可台臺史右葉號司吃各合吉同名後向嚮嚇呂嗎君吝吞否吧含聽啟吳吵吶吸吹吻吼吾呀告員呢周週味呵呼命咀和咖咧咽品哄哈哉響啞譁哥哪哭哲唉唯唱商啊啜啦善喊喔喜喧嗯嗲嘛嘲嘴嘶嘿噢器噩噪嚼四回因團園困圍固國圖圓圈土聖在地場址均壞坐塊堅壇坦壟型壘埋城培基堂堆墮堪堵塑塔塞填境墓牆增墟士壯聲殼壹處備複夕外多夜夠大天太夫央失頭誇夾奪奇奉奮奏契奔獎套奠奢女奴她好如婦媽妖妙妥妨妹始姑姓委薑姨姻威娃婁娘娛娶婆婚嬰媒嫉嫌子孔字存孫孤學孩孽寧它宅守安完宕宗官定宜寶實寵審客宣憲宰害家傢容寂寄密富寒察寡對尋導封射將尊小少爾尚嘗尤尬就尷尺盡尾局屁層居屈屆屋屎展屬山歲豈崗岸崇州工左巧鉅差己已巴巷幣市布師希帝帶席幫常幕平年並幸倖幻幼廣莊庇床序庫應底店府龐廢度座庭庶康庸廊廖延建開異棄弄弊式引弟張彌弱彈強歸當錄形彩彪彭彰影役徹彼往征徵徑逕待很律徒得微德徽心必憶忍志誌忘忙忠快念忽忿懷態愴怎怒怕憐思急性怨怪總戀恍恐恆恢恨恩息恰懇惡悉悔悟您懸憫悲情驚惑惕惜慘慣想愈意愚感憤願慈慢慰憾懂懶戍戲成我或戰戚截戴戶房所扁扈手才撲打扔託扛扣執擴掃揚扭扮扯擾批扼找承技抄抉把抑抒抓投抗摺折撫搶護報披抬抱抵押抽擔拆拉拍拒拓拖拘拚招拜擬攏擁撥擇括拼拿持掛指按挑挖擠揮挨挫振挺挽捅捉捐捕撈損換據掀掉掌掏掐排掘探接控推描提插握揣揪揭援擱搜搞攝擺搖攤摘摧摩撇撐撤撬播擅攢攫支收改攻放政故效敵敏救教斂敢散敬數整文斗料斥斷斯新方施旁旅旋族旗無既日旦舊早時昌明昏易星春昧昨是顯曉晚普景晰晴智暫暇暑暖暗暴曲更曾替最月有朋服朔望朝期木未末本術機殺雜權材村束條來楊杯傑闆板極構枉析林果枯架某染查標棧欄樹校樣核根格框案桌檔樺桶夢梨梯檢棒棚椅楚樓概榜榨檻樟模橫欠次歡歐慾款歌止正此步武歧死殊殘段殷毀母每毒比畢毛毫民氣水永求彙漢江污汰沉沙溝沒淪河油治沾沿洩法泛泡波泣泥注淚泯泰澤洋洗洛津洪洲活派流淺測濟瀏渾濃浩浪浮海浸消涉湧濤渦滌潤漲液涵淘淡深混添清漸滲渡渣渥溫港遊游湖灣濕源滋滑滾滯滿滴漂漏演漩漫潘潛潮澡激灌火滅燈灰靈災燦炒炫炸點爛烈煩燒熱焉焦然煤照煽熊薰熟燃燥爆愛父爺爸爽片版牌牙牛牢物牲牽特犧犯狀猶狂狄狗獨狹獄狼猖猛猜獗率玉王玩環現珊班球理琴瑞瑟璋瓦瓶甚生用甫田由甲申電男畫暢界留略番疇疆疑療瘡疲病癢痛登白百皂的皆皇皮益監盒蓋盤盟目盯盲直相盼盾省看真眷眼著睛睡瞧瞭矛矣知短石礦碼研破砸礎硬確礙碗碣碧碰磁磨示社祖神票禁福離禽秀私種科秒秩積稱移稀程稍稅稚穩稻究窮空穿突窗立站競竟章童端竹竿笑筆符笨第等築答策箏籌簽籤簡算管篇籍類粉粒粗糧粹精糊糕蹧糟係繫素索緊紫累繁糾紅約級紀純綱納紛紙紐紓線練組紳細織終紹經結給絡絕統繼績緒續繩維綿綜綠緩締編縛縫縮繳缺網羅罷罪置署羊美羞群翔翱翻翼燿耀老考者而耐耗耳耶聳恥聾職聯聚肉肌肚肝股膚肩肪肯育腎脅胃膽背胚勝胞胡骼能脂脆脈臟髒腦腳脫臉腐腥騰臂自臭至致輿捨舍舒舞舟航般艦艙船良艱色藝節蘆芬花蘇苟若苦英範茅茍草薦荒盪榮藥荼莎莫萊蓮穫獲菜營蕭落葬蒙蓄蓍藍蔑蔓薄薪藉藏慮虛蟲雖蝕蟻螞蚊蛋蛙蛛蜂蝴蝶融蠢血行衍衡衣補錶袋袖被襲裁裂裝裸西要覆見觀規視覽覺角解觸言譽警譬計訂認討讓議訊記講諱訝許論諷設訪證評識訴詞譯試詩誠話詢該詳語誤說請諸讀課誰調諒談謀謊謂謝謠謾謬象貌貝負財責敗貨質販貪貧購貫貼貴費資賭賞賴贅賺賽贊讚贏赤赫走赴趕起超越趨趟趣足躍跋跑距跟跨路跳踐踏踩踮蹲身躲車軌轉輪軟轟輕載較輒輛輩輝輻輯輸辛辜辭辣辨辯辱邊達遷迅過邁迎運近返還這進遠違連迫述迷蹟跡追退送適逃逆選透逐遞途通逛速造逢逮逸邏遇遍遑道遺遙遭遮遵避鄧那郵鄰鄭部郭都酋配酒酷酸釀醒采釋裡重野量金針鈣鐘鋼欽鈕錢鑽鐵鉛銅鏟鏈銷鎖鋤錯錦鍵鍾鏢鏡長門閃閉問閒間悶鬧聞閡閱闕隊防陽陰陣階阻阿附際陸陳陋降限院除險陪陶陷隨隱隔隘障隸難雄集雇僱雛雨靂零雷需霄露霹青靜非靠面革鞋鞭韓音頁頂頃項順須顧頓頌預領頗頻穎題額風飛食餐饑飯飾飽餅餓館首香馬馭驅駁駝駕罵駱驗騎騙驟骨髓高鬼魂魏魚魯鮮鳴鴨鴦鴛鷹鹿麻黃黑鼎鼓鼠齊齒龍櫻劍皚藹襖奧壩頒絆綁鎊謗鮑鋇狽憊繃斃貶辮鼈癟瀕濱賓擯缽鉑卜蠶慚蒼滄詫攙摻蟬饞讒纏闡顫腸鈔塵襯懲騁癡遲馳熾躊綢櫥廚闖錘綽賜聰蔥囪叢竄貸鄲撣憚誕擋搗島禱盜墊澱釣叠諜疊釘錠棟凍犢鍍鍛緞兌噸鈍鵝訛餌貳罰閥琺礬釩紡墳糞楓鋒瘋馮輔賦訃稈贛岡臯鎬鴿閣鉻龔宮鞏貢鈎蠱剮矽龜閨詭櫃輥鍋駭鶴賀鴻壺滬喚瘓煥渙賄穢燴誨繪葷禍譏雞緝薊莢頰賈鉀殲箋緘繭堿鹼揀撿儉鑒賤餞濺澗漿蔣槳醬膠澆驕嬌攪鉸矯餃絞轎稭莖頸痙廄駒鋸懼鵑絹潔誡謹晉燼荊訣鈞駿顆墾摳褲儈寬曠巋窺饋潰闊蠟臘攔籃闌瀾讕攬纜濫澇鐳籬鯉禮麗礫瀝鐮漣簾煉遼鐐獵鱗凜賃齡鈴淩嶺餾嚨籠隴摟簍盧顱廬爐擄鹵虜賂祿驢鋁侶屢縷濾巒攣孿灤掄綸蘿鑼籮騾瑪麥瞞饅蠻貓錨鉚貿黴鎂錳謎覓緬廟閩銘畝鈉撓惱餒膩攆撚鳥聶齧鑷鎳檸獰擰濘膿瘧諾鷗毆嘔漚賠噴鵬飄蘋憑潑鋪樸譜臍訖扡釺謙鉗譴塹槍嗆薔鍬橋喬僑翹竅竊氫慶瓊軀齲顴鵲饒繞韌紉絨銳閏灑薩鰓傘騷澀紗篩曬陝贍繕賒懾嬸獅屍駛壽獸樞贖豎帥碩爍飼慫訟誦擻肅綏筍瑣獺撻癱灘譚歎湯燙縧謄銻屜廳烴塗頹蛻鴕馱橢窪襪彎頑韋濰葦僞緯紋甕撾蝸窩嗚鎢烏誣蕪塢霧錫銑蝦轄峽俠廈鍁纖鹹賢銜獻餡羨鑲嘯蠍挾攜諧瀉鋅釁洶鏽繡噓軒癬絢勳馴訓遜鴉閹煙鹽顔閻豔硯彥諺瘍瑤堯窯銥頤儀彜詣誼繹蔭銀飲纓瑩螢熒蠅喲傭癰踴詠憂鈾誘漁嶼籲禦淵轅緣鑰嶽粵悅鄖勻隕蘊醞暈韻贓棗竈賊贈紮劄軋鍘閘詐齋債氈盞斬輾嶄綻帳賬脹趙蟄轍鍺貞偵診鎮掙睜猙幀摯擲幟腫謅軸皺晝豬誅燭矚囑貯鑄駐磚樁妝錐墜綴諄濁漬蹤縱鄒詛鬱儻",A=function(e){var t="",A="string"==typeof e?e.length:0,C=void 0;for(C=0;C<A;C++)t+=e.charCodeAt(C)>1e4&&-1!=n.indexOf(e.charAt(C))?g.charAt(n.indexOf(e.charAt(C))):e.charAt(C);return t},C=function(e){var t="",A="string"==typeof e?e.length:0,C=void 0;for(C=0;C<A;C++)t+=e.charCodeAt(C)>1e4&&-1!=g.indexOf(e.charAt(C))?n.charAt(g.indexOf(e.charAt(C))):e.charAt(C);return t};return t.convert=function(e,t){return""==e||null==e?"":t?C(e):A(e)},t.convertText=function(e,t){void 0===t&&(t=!0),e=e?e.childNodes:document.body.childNodes;for(var g=0;g<e.length;g++){var n=e.item(g);"||BR|HR|TEXTAREA|".indexOf("|"+n.tagName+"|")>0||(""!=n.title&&null!=n.title&&(n.title=this.convert(n.title,t)),""!=n.alt&&null!=n.alt&&(n.alt=this.convert(n.alt,t)),""!=n.placeholder&&null!=n.placeholder&&(n.placeholder=this.convert(n.placeholder,t)),"INPUT"==n.tagName&&""!=n.value&&"text"!=n.type&&"hidden"!=n.type&&(n.value=this.convert(n.value,t)),3==n.nodeType?""!=n.data&&(n.data=this.convert(n.data,t)):this.convertText(n,t))}},t.convertTextByLang=function(e){e&&("zh-cn"===e?this.convertText(document.documentElement,!1):this.convertText(document.documentElement,!0))},new e}()},43:function(e,t){!function(){var e=function(){var e=document.documentElement.clientWidth,t=(window.devicePixelRatio,16*e/320);document.documentElement.style.fontSize=t+"px",document.querySelector('meta[name="viewport"]').setAttribute("content","width=device-width,initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover")};!function(){var t=navigator.userAgent;/android/i.test(t)||/ipad|itouch|iphone/i.test(t)||/tianqi/i.test(t)?e():(document.documentElement.style.fontSize="24px",document.getElementById("container").style.maxWidth="30rem",document.getElementById("container").style.minWidth="22rem")}()}()},52:function(e,t,g){"use strict";function n(e,t){mmc.ready(function(){if(h(e.methodVersion)){var g=mmc.client.getLastUserInfo();t(g)}})}function A(e,t){var g=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{uaMarks:["linghit"],version:200};s(g.uaMarks)&&l(g.version)&&mmc.ready(function(){var g=mmc.client.mmcOnlineGetUserInfo(e);t(g)})}function C(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{uaMarks:["linghit"],version:200};s(t.uaMarks)&&l(t.version)&&mmc.client.mmcOnlineUserInfo(e)}function I(e){try{mmc.ready(function(){var t=mmc.user.info;"function"==typeof e&&e(t)})}catch(e){console&&console.warn(e)}}function i(e,t){var g=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{uaMarks:["linghit"],version:100};s(g.uaMarks)&&l(g.version)?(location.href=e.pay_confirm_url+"?order_id="+e.order_id+"&pay_point="+e.pay_point,mmc.client.mmcOnlinePay(e)):location.href=t}function r(e,t,g){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{uaMarks:["linghit"],version:100};s(n.uaMarks)&&l(n.version)?(location.href=e.pay_confirm_url+"?order_id="+e.order_id+"&pay_point="+e.pay_point+"&mmc_devicesn="+t,mmc.client.mmcOnlinePay(e)):location.href=g}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{uaMarks:["linghit"],version:100};return s(e.uaMarks)&&l(e.version)}function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{uaMarks:["linghit"],version:200};return s(e.uaMarks)&&l(e.version)}function s(e){var t=navigator.userAgent;return e=e||["linghit"],e.some(function(e){return new RegExp(e,"i").test(t)})}function c(){var e=navigator.userAgent,t=e.match(/{[^}]+}/g),g={};return t&&t.length>0&&t.forEach(function(e){var t=e.replace(/{|}/g,"").split("/");g[t[0]]=t[1]}),g}function l(e){var t=c(),g=t.p;return e=e||100,!!g&&g>=e}function d(){var e=c(),t=e.lang||"";return/^(0|1)$/.test(t)?{flag:!0,lang:t}:{flag:!1,lang:null}}function h(e){var t=c(),g=t.zxcs_method||"";return e=e||100,!!g&&g>=e}function m(){if(s()){return 1==d().lang}return!1}function u(e){var t=e.substring(0,4),g=e.substring(4,6),n=e.substring(6,8),A=e.substring(8,10);return e=Date.parse(t+"/"+g+"/"+n+" "+A+":00:00")/1e3,{birth:e,hour:A}}function p(e,t){var g=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return{0:{userType:"0",name:t.username||t.name,birthday:u(t.birthday||"1991010100").birth,isUnHour:parseInt(t.hour_mark)?"1":"0",gender:t.gender+"",email:t.email,orderId:g,cesuanName:t.pay_point,askId:t.askId||""},1:{userType:"1",maleName:t.man_username||t.man_name,maleBirthday:u(t.man_birthday||t.man_bir||"1991010100").birth,maleIsUnHour:parseInt(t.man_hour_mark)?"1":"0",femaleName:t.woman_username||t.woman_name,femaleBirthday:u(t.woman_birthday||t.woman_bir||"1991010100").birth,femaleIsUnHour:parseInt(t.woman_hour_mark)?"1":"0",email:t.email,orderId:g,cesuanName:t.pay_point,askId:t.askId||""},2:{},3:{userType:"3",orderId:g,email:t.email,cesuanName:t.pay_point,cesuanType:n,askId:t.askId||""}}[e]}function f(e){var t=["子时","丑时","丑时","寅时","寅时","卯时","卯时","辰时","辰时","巳时","巳时","午时","午时","未时","未时","申时","申时","酉时","酉时","戌时","戌时","亥时","亥时","子时"];e=new Date(1e3*e);var g=e.getFullYear(),n=parseInt(e.getMonth()+1),A=parseInt(e.getDate()),C=parseInt(e.getHours()),I=n<10?"0"+n:n,i=A<10?"0"+A:A,r=C<10?"0"+C:C,o=t[C];return{datePickerBirth:"公(阳)历 "+g+"年"+I+"月"+i+"日 "+o,datePickerBirthFormat:""+g+I+i+r,birthTimeValue:o,birthTimeDataValue:C,birthTimeDataId:C+1}}function _(e,t){var g=["子时","丑时","丑时","寅时","寅时","卯时","卯时","辰时","辰时","巳时","巳时","午时","午时","未时","未时","申时","申时","酉时","酉时","戌时","戌时","亥时","亥时","子时"],n=e.split("-"),A=n[0],C=n[1],I=n[2],i=n[3],r=parseInt(t)?0:parseInt(i),o=parseInt(t)?"时辰未知":g[r];return{datePickerBirth:"公(阳)历 "+A+"年"+C+"月"+I+"日 "+o,datePickerBirthFormat:""+A+C+I+i,birthTimeValue:o,birthTimeDataValue:r,birthTimeDataId:parseInt(t)?0:r+1,datePickerTimeConfirm:parseInt(t)}}function v(e,t){var g=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=E.a[e],A={order_id:t.orderId,pay_point:e,pay_confirm_url:location.origin+E.b,server_id:n.SERVER_ID},I=t.paymentUrl,r={uaMarks:n.APP_UA_MARKS,version:n.APP_PAY_VERSION};if("{}"!==JSON.stringify(g)){var o=Object.assign({},g,{askId:n.ASK_ID});C(p(n.USER_TYPE,o,t.orderId,n.MARK))}i(A,I,r)}function y(e){var t=this,g=this.props.setIndexData,A={methodVersion:e};"function"!=typeof g&&(g=this.props.actions.setIndexData),n(A,function(e){var n={},A="";"string"==typeof e?e&&(n=JSON.parse(e)):n=e,n.name&&(A=_(n.birthday,n.isUnHour),t.setState({name:n.name,gender:parseInt(n.sex)}),g({datePickerBirth:A.datePickerBirth,datePickerBirthFormat:A.datePickerBirthFormat,birthTimeDataId:A.birthTimeDataId,birthTimeValue:A.birthTimeValue,birthTimeDataValue:A.birthTimeDataValue,datePickerTimeConfirm:A.datePickerTimeConfirm}))})}function b(e){s()&&mmc.client.qimingDashiMsg(e)}t.e=C,t.f=i,t.c=o,t.a=s,t.b=d,t.d=p;var E=(g(13),g(72));!function(e){e.mmc=function(){var t={v:"1.0.0-dev",json:{encode:function(e){return""==e?{}:JSON.stringify(e).replace(/"/g,"'")},decode:function(e){return""==e?"{}":JSON.parse(e.replace(/\'/gi,'"'))}},debug:!1,user:{info:{},getInfo:function(){return this.info},getId:function(){return this.getInfo().userid},getUsername:function(){return this.getInfo().username},getNickname:function(){return this.getInfo().nickname},getCountry:function(){return this.getInfo().country},getEmail:function(){return this.getInfo().email},getAvatar:function(){return this.getInfo().avatar},isMarry:function(){return 1==this.getInfo().marriagestatus},getPhone:function(){return this.getInfo().mobilephone},getScore:function(){return this.getInfo().score},isMan:function(){return 1==this.getInfo().sex},getGender:function(){return this.getInfo().sex},getWork:function(){return this.getInfo().workstatus},getBirthday:function(){return this.getInfo().birthday},login:function(g){return t.client.isAndroid()?e.lingjiWebApp.MMCLogin(null==g?"":g):MMCLogin(g)},register:function(g){return t.client.isAndroid()?e.lingjiWebApp.MMCRegist(null==g?"":g):MMCRegist(g)},isLogin:function(){return void 0!==this.getInfo().userid}},client:{info:{},ua:e.navigator.userAgent.toLowerCase(),is:function(e){return new RegExp(e).test(this.ua)},isIOS:function(){return"android"!=this.ua.match(/android/i)},isAndroid:function(){return"android"==this.ua.match(/android/i)},getInfo:function(){return this.info},getLanguage:function(){return this.getInfo().language},getCountry:function(){return this.getInfo().area},getName:function(){return client.info.module},getAppId:function(){return this.getInfo().pluginid},getUDID:function(){return this.getInfo().udid},getDeviceId:function(){return this.getInfo().deviceid},getSystemVersion:function(){return this.getInfo().systemversion},getPlatform:function(){return this.getInfo().platform},notify:function(e,g){return t.client.isAndroid()?lingjiWebApp.MMCLocalNotification(t.json.encode(e),null==g?"":g):MMCLocalNotification(e,null==g?"":g)},goto:function(e,g,n,A){var C={controller:e,extraType:n||0,extraParams:g,gotoParams:g,gotoType:n||0};return t.client.isAndroid()?lingjiWebApp.MMCGoto(t.json.encode(C),null==A?"":A):(console.log(C),MMCGoto(C,A))},share:function(e,g){var n={thumb:e.icon,title:e.title,description:e.desc,shareLink:e.link};return t.client.isAndroid()?lingjiWebApp.MMCShare(t.json.encode(n),null==g?"":g):MMCShare(n,g)},getLastUserInfo:function(){return t.client.isAndroid()?lingjiWebApp.getLastUserInfoFromApp():t.client.isIOS()?getLastUserInfoFromApp():void 0},mmcOnlinePay:function(e){t.client.isAndroid()?(e=JSON.stringify(e).replace(/"/g,"'"),lingjiWebApp.MMCOnlinePay(e,"")):t.client.isIOS()&&MMCOnlinePay(e)},mmcOnlineGetUserInfo:function(e){return t.client.isAndroid()?lingjiWebApp.MMCOnlineGetUserInfo(t.json.encode(e)):t.client.isIOS()?MMCOnlineGetUserInfo(e):void 0},mmcOnlineUserInfo:function(e){t.client.isAndroid()?lingjiWebApp.MMCOnlineUserInfo(t.json.encode(e)):t.client.isIOS()&&MMCOnlineUserInfo(e)},comment:function(){return t.client.isAndroid()?lingjiWebApp.MMCComment():MMCComment()},daily:function(e){return t.client.isAndroid()?lingjiWebApp.MMCDailySign(t.json.encode(e)):MMCDailySign(e)},openWindow:function(e,t,g){var n={gotourl:t,title:e};MMCOpenWindow(n,g)},qimingDashiMsg:function(e){t.client.isAndroid()?lingjiWebApp.QimingDashiMsg(t.json.encode(e)):t.client.isIOS()&&QimingDashiMsg(e)},QimingAskForWechat:function(e){t.client.isAndroid()?lingjiWebApp.QimingAskForWechat(e):t.client.isIOS()},QimingShowPaidTipsDialog:function(){t.client.isAndroid()?lingjiWebApp.QimingShowPaidTipsDialog():t.client.isIOS()}},alertDebug:function(e){1==this.debug&&alert(e.join(" # "))},ready_callback:[]};return t.init=function(){try{this.client.isAndroid()?this.user.info=this.json.decode(lingjiWebApp.getUserInfoOnLine()):this.user.info=getUserInfoOnLine()}catch(e){console&&console.warn(e)}this.ready_callback.forEach(function(e,t){e()})},t.ready=function(e){this.ready_callback.push(e)},t}(),e.MMCReady=function(){mmc.init()}}(window);!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(n,"getLastUserInfoFromNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(A,"getAppInfoFromNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(C,"sendUserInfoToNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(I,"getUserInfoFromNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(i,"paymentToUseNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(r,"paymentToUseNativeDSFW","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(o,"hideHeaderAndFooterForNativePay","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(a,"hideUserListFromNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(s,"whetherInOurNativeApp","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(c,"getQueryFromUA","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(l,"payVersionValidate","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(d,"pageLangValidate","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(h,"forecastMethodValidate","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(m,"hidePriceWhenPageInVersionOfGm","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(u,"transferBirthToTimestamp","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(p,"dealOrderDataForNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(f,"transferTimestampToBirth","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(_,"transferTimeFormatToBirth","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(v,"useNativePayOrSaveInfoToNative","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(y,"setAppUserInfoToState","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"),__REACT_HOT_LOADER__.register(b,"qiMingDaShiMsg","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/CommonClientJsSDK.js"))}()},547:function(e,t,g){t=e.exports=g(63)(),t.push([e.i,".m-form {\n  font-size: 0.8rem;\n  margin: 0 0.42667rem;\n}\n.m-form .form {\n  border: 2px solid #8d1f14;\n  /*no*/\n  -webkit-border-radius: .5rem;\n          border-radius: .5rem;\n  background-color: #fff;\n  overflow: hidden;\n  padding-bottom: 0.42667rem;\n}\n.m-form .form li {\n  height: 2.34667rem;\n  line-height: 2.34667rem;\n  padding: 0 0.32rem;\n  border-bottom: 1px solid #8d1f14;\n  /*no*/\n  position: relative;\n  z-index: 1;\n}\n.m-form .form li .left {\n  width: 20%;\n  color: #6f0011;\n  margin-left: 0.32rem;\n}\n.m-form .form li .left > img {\n  vertical-align: middle;\n  display: inline-block;\n}\n.m-form .form li .auto {\n  padding: 0 .75rem;\n  position: relative;\n  color: #666666;\n}\n.m-form .form li input {\n  font-size: 0.8rem;\n  border: 0;\n  -webkit-border-radius: .25rem;\n          border-radius: .25rem;\n  width: 100%;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  background-color: #fff;\n}\n.m-form .form li textarea {\n  border: 0;\n  width: 100%;\n  min-height: 4.10667rem;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  line-height: 1.38667rem;\n}\n.m-form .form li .label {\n  width: 20%;\n  color: #6f0011;\n  line-height: 2.34667rem;\n}\n.m-form .form li .go-icon {\n  width: 0.53333rem;\n  height: 0.53333rem;\n  border-top: 2px solid #666666;\n  /*no*/\n  border-right: 2px solid #666666;\n  /*no*/\n  -webkit-transform: rotate(45deg);\n      -ms-transform: rotate(45deg);\n          transform: rotate(45deg);\n  position: absolute;\n  z-index: 2;\n  right: 0.8rem;\n  top: 50%;\n  margin-top: -0.26667rem;\n}\n.m-form .form li.textarea {\n  height: 6.29333rem;\n  font-size: 0.8rem;\n  margin-left: 0.32rem;\n}\n.m-form .form li:last-child {\n  border-bottom: none;\n}\n.show-square {\n  color: #ffd800;\n  font-size: 0.8rem;\n  text-align: center;\n  margin-top: 0.42667rem;\n}\n.show-square p {\n  height: 1.17333rem;\n}\n.show-square span {\n  display: inline-block;\n  margin-right: 0.21333rem;\n}\n.show-square i {\n  display: inline-block;\n  width: 0.53333rem;\n  height: 0.53333rem;\n  background: #c1c1c1;\n  border: 0.21333rem solid #fff;\n  -webkit-border-radius: 0.53333rem;\n          border-radius: 0.53333rem;\n  vertical-align: middle;\n  margin-right: 0.21333rem;\n  text-align: center;\n}\n.show-square i.checked {\n  background: #ae2c1f;\n}\n.show-tips {\n  height: 1.38667rem;\n  line-height: 1.38667rem;\n  background: #912217;\n  -webkit-border-radius: 0.8rem;\n          border-radius: 0.8rem;\n  margin: 0.42667rem;\n}\n.show-tips p {\n  text-align: center;\n  color: #ffa198;\n  font-size: 0.74667rem;\n}\n.m-query {\n  margin-bottom: 0.42667rem;\n}\n.m-query a {\n  font-size: 0.74667rem;\n  text-align: center;\n  color: #ffd800;\n  display: block;\n}\n",""])},549:function(e,t,g){t=e.exports=g(63)(),t.push([e.i,'@charset "utf-8";\n/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */\n/**\n * 1. Set default font family to sans-serif.\n * 2. Prevent iOS and IE text size adjust after device orientation change,\n *    without disabling user zoom.\n */\nhtml {\n  font-family: sans-serif;\n  /* 1 */\n  -ms-text-size-adjust: 100%;\n  /* 2 */\n  -webkit-text-size-adjust: 100%;\n  /* 2 */\n  width: 100%;\n  min-height: 100%;\n}\n/**\n * Remove default margin.\n */\nbody {\n  margin: 0;\n  font-size: 100%;\n  font-family: Microsoft YaHei, "\\5FAE\\8F6F\\96C5\\9ED1", Helvetica, STHeiti, Droid Sans Fallback;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  width: 100%;\n  min-height: 100%;\n}\n/* HTML5 display definitions\n   ========================================================================== */\n/**\n * Correct `block` display not defined for any HTML5 element in IE 8/9.\n * Correct `block` display not defined for `details` or `summary` in IE 10/11\n * and Firefox.\n * Correct `block` display not defined for `main` in IE 11.\n */\narticle, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {\n  display: block;\n}\n/**\n * 1. Correct `inline-block` display not defined in IE 8/9.\n * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.\n */\naudio, canvas, progress, video {\n  display: inline-block;\n  /* 1 */\n  vertical-align: baseline;\n  /* 2 */\n}\n/**\n * Prevent modern browsers from displaying `audio` without controls.\n * Remove excess height in iOS 5 devices.\n */\naudio:not([controls]) {\n  display: none;\n  height: 0;\n}\n/**\n * Address `[hidden]` styling not present in IE 8/9/10.\n * Hide the `template` element in IE 8/9/10/11, Safari, and Firefox < 22.\n */\n[hidden], template {\n  display: none;\n}\n/* Links\n   ========================================================================== */\n/**\n * Remove the gray background color from active links in IE 10.\n */\na {\n  background-color: transparent;\n}\n/**\n * Improve readability of focused elements when they are also in an\n * active/hover state.\n */\na:active, a:hover {\n  outline: 0;\n}\n/* Text-level semantics\n   ========================================================================== */\n/**\n * Address styling not present in IE 8/9/10/11, Safari, and Chrome.\n */\nabbr[title] {\n  border-bottom: 1px dotted;\n  /*no*/\n}\n/**\n * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.\n */\nb, strong {\n  font-weight: bold;\n}\n/**\n * Address styling not present in Safari and Chrome.\n */\ndfn {\n  font-style: italic;\n}\n/**\n * Address variable `h1` font-size and margin within `section` and `article`\n * contexts in Firefox 4+, Safari, and Chrome.\n */\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n/**\n * Address styling not present in IE 8/9.\n */\nmark {\n  background: #ff0;\n  color: #000;\n}\n/**\n * Address inconsistent and variable font size in all browsers.\n */\nsmall {\n  font-size: 80%;\n}\n/**\n * Prevent `sub` and `sup` affecting `line-height` in all browsers.\n */\nsub, sup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\nsup {\n  top: -0.5em;\n}\nsub {\n  bottom: -0.25em;\n}\n/* Embedded content\n   ========================================================================== */\n/**\n * Remove border when inside `a` element in IE 8/9/10.\n */\nimg {\n  border: 0;\n}\n/**\n * Correct overflow not hidden in IE 9/10/11.\n */\nsvg:not(:root) {\n  overflow: hidden;\n}\n/* Grouping content\n   ========================================================================== */\n/**\n * Address margin not present in IE 8/9 and Safari.\n */\nfigure {\n  margin: 1em 1.06667rem;\n  /*no*/\n}\n/**\n * Address differences between Firefox and other browsers.\n */\nhr {\n  -webkit-box-sizing: content-box;\n          box-sizing: content-box;\n  height: 0;\n}\n/**\n * Contain overflow in all browsers.\n */\npre {\n  overflow: auto;\n}\n/**\n * Address odd `em`-unit font size rendering in all browsers.\n */\ncode, kbd, pre, samp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n/* Forms\n   ========================================================================== */\n/**\n * Known limitation: by default, Chrome and Safari on OS X allow very limited\n * styling of `select`, unless a `border` property is set.\n */\n/**\n * 1. Correct color not being inherited.\n *    Known issue: affects color of disabled elements.\n * 2. Correct font properties not being inherited.\n * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.\n */\nbutton, input, optgroup, select, textarea {\n  color: inherit;\n  /* 1 */\n  font: inherit;\n  /* 2 */\n  margin: 0;\n  /* 3 */\n}\n/**\n * Address `overflow` set to `hidden` in IE 8/9/10/11.\n */\nbutton {\n  overflow: visible;\n}\n/**\n * Address inconsistent `text-transform` inheritance for `button` and `select`.\n * All other form control elements do not inherit `text-transform` values.\n * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.\n * Correct `select` style inheritance in Firefox.\n */\nbutton, select {\n  text-transform: none;\n}\n/**\n * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`\n *    and `video` controls.\n * 2. Correct inability to style clickable `input` types in iOS.\n * 3. Improve usability and consistency of cursor style between image-type\n *    `input` and others.\n */\nbutton, html input[type="button"], input[type="reset"], input[type="submit"] {\n  -webkit-appearance: button;\n  /* 2 */\n  cursor: pointer;\n  /* 3 */\n}\n/**\n * Re-set default cursor for disabled elements.\n */\nbutton[disabled], html input[disabled] {\n  cursor: default;\n}\n/**\n * Remove inner padding and border in Firefox 4+.\n */\nbutton::-moz-focus-inner, input::-moz-focus-inner {\n  border: 0;\n  padding: 0;\n}\n/**\n * Address Firefox 4+ setting `line-height` on `input` using `!important` in\n * the UA stylesheet.\n */\ninput {\n  line-height: normal;\n}\n/**\n * It\'s recommended that you don\'t attempt to style these elements.\n * Firefox\'s implementation doesn\'t respect box-sizing, padding, or width.\n *\n * 1. Address box sizing set to `content-box` in IE 8/9/10.\n * 2. Remove excess padding in IE 8/9/10.\n */\ninput[type="checkbox"], input[type="radio"] {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  /* 1 */\n  padding: 0;\n  /* 2 */\n}\n/**\n * Fix the cursor style for Chrome\'s increment/decrement buttons. For certain\n * `font-size` values of the `input`, it causes the cursor style of the\n * decrement button to change from `default` to `text`.\n */\ninput[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {\n  height: auto;\n}\n/**\n * 1. Address `appearance` set to `searchfield` in Safari and Chrome.\n * 2. Address `box-sizing` set to `border-box` in Safari and Chrome.\n */\ninput[type="search"] {\n  -webkit-appearance: textfield;\n  /* 1 */\n  -webkit-box-sizing: content-box;\n          box-sizing: content-box;\n  /* 2 */\n}\n/**\n * Remove inner padding and search cancel button in Safari and Chrome on OS X.\n * Safari (but not Chrome) clips the cancel button when the search input has\n * padding (and `textfield` appearance).\n */\ninput[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n/**\n * Define consistent border, margin, and padding.\n */\nfieldset {\n  border: 1px solid #c0c0c0;\n  /*no*/\n  margin: 0 2px;\n  /*no*/\n  padding: 0.35em 0.625em 0.75em;\n}\n/**\n * 1. Correct `color` not being inherited in IE 8/9/10/11.\n * 2. Remove padding so people aren\'t caught out if they zero out fieldsets.\n */\nlegend {\n  border: 0;\n  /* 1 */\n  padding: 0;\n  /* 2 */\n}\n/**\n * Remove default vertical scrollbar in IE 8/9/10/11.\n */\ntextarea {\n  overflow: auto;\n}\n/**\n * Don\'t inherit the `font-weight` (applied by a rule above).\n * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.\n */\noptgroup {\n  font-weight: bold;\n}\n/* Tables\n   ========================================================================== */\n/**\n * Remove most spacing between table cells.\n */\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\ntd, th {\n  padding: 0;\n}\nul, ol {\n  list-style: none;\n}\nbody, div, ol, ul, li, h1, h2, h3, h4, h5, h6, p, th, td, dl, dd, form, iframe, input, textarea, select, label, article, aside, footer, header, menu, nav, section, time, audio, video {\n  margin: 0;\n  padding: 0;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n}\na {\n  text-decoration: none;\n}\na, input, textarea, select {\n  outline: none;\n}\n/**\n* 配置\n*/\n/**\n* 公共样式\n*/\nhtml {\n  background-color: #fff;\n}\n.container {\n  background: #fff;\n  width: 100%;\n  min-height: 100%;\n  position: relative;\n  min-width: 20rem;\n  max-width: 30rem;\n  margin: 0 auto;\n  color: #333;\n  z-index: 1;\n}\n.wrapper {\n  width: 100%;\n  min-height: 100%;\n  position: relative;\n  z-index: 1;\n}\nimg {\n  width: 100%;\n  vertical-align: top;\n}\n.clear {\n  _zoom: 1;\n  clear: both;\n}\n.clear:after {\n  content: "";\n  display: block;\n  height: 0;\n  visibility: hidden;\n  clear: both;\n}\n.left {\n  float: left;\n}\n.right {\n  float: right;\n}\n.auto {\n  overflow: hidden;\n}\n.li-left li {\n  display: block;\n  float: left;\n}\n.li-right li {\n  display: block;\n  float: right;\n}\n.hide {\n  display: none;\n}\n.rlt {\n  position: relative;\n}\n.new-header {\n  width: 100%;\n  height: 2.66667rem;\n  line-height: 2.66667rem;\n  background-color: #fff;\n  border-bottom: 1px solid #e4e4e4;\n  /*no*/\n}\n.new-header .left span, .new-header .right span {\n  display: block;\n  width: 2.34667rem;\n  height: 2.66667rem;\n}\n.new-header .icon-back {\n  background: transparent url("data:image/png;base64,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") no-repeat center;\n  background-size: 80%;\n}\n.new-header .icon-menu {\n  background: transparent url("data:image/png;base64,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") no-repeat center;\n  background-size: 80%;\n}\n.new-header .auto {\n  color: #4f4f4f;\n  margin: 0 2.75rem;\n  font-size: 0.90667rem;\n}\n.new-header .auto .color1 {\n  color: #dc8732;\n}\n.new-header .auto .icon-next {\n  margin: 0 0.42667rem 0 0.26667rem;\n  display: inline-block;\n  width: 0.48rem;\n  height: 0.48rem;\n  border-top: 2px solid #555;\n  /*no*/\n  border-right: 2px solid #555;\n  /*no*/\n  -webkit-transform: rotate(45deg);\n      -ms-transform: rotate(45deg);\n          transform: rotate(45deg);\n}\n.site-menu {\n  position: fixed;\n  top: 2.75rem;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 10000;\n  display: none;\n  margin-top: 1px;\n  /*no*/\n  opacity: 0;\n  -webkit-transition: all 0.5s ease-in;\n  overflow: auto;\n}\n.site-menu.menu-show {\n  display: block;\n  opacity: 1;\n}\n.site-menu ul {\n  margin: 0 1rem;\n}\n.site-menu li {\n  width: 33.333%;\n  margin-top: 1rem;\n}\n.site-menu a {\n  display: block;\n}\n.site-menu .icon {\n  display: block;\n  width: 3.5rem;\n  height: 3.5rem;\n  margin: 0 auto;\n}\n.site-menu .icon img {\n  width: 100%;\n  height: auto;\n}\n.site-menu p {\n  height: 1.5rem;\n  line-height: 1.5rem;\n  text-align: center;\n  font-size: 1rem;\n  overflow: hidden;\n  color: #333;\n}\n.menu-wrapper {\n  min-width: 20rem;\n  max-width: 30rem;\n  overflow: auto;\n  margin: 0 auto;\n  background-color: rgba(255, 255, 255, 0.9);\n  min-height: 100%;\n}\n.s-m-a-enter {\n  opacity: 0.01;\n  -webkit-transition: all 0.5s ease-in;\n  transition: all 0.5s ease-in;\n}\n.s-m-a-enter.s-m-a-enter-active {\n  opacity: 1;\n}\n@-webkit-keyframes menuin {\n  0% {\n    -webkit-transform: scale(3);\n    -ms-transform: scale(3);\n    transform: scale(3);\n    opacity: 0;\n  }\n  100% {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n@keyframes menuin {\n  0% {\n    -webkit-transform: scale(3);\n    -ms-transform: scale(3);\n    transform: scale(3);\n    opacity: 0;\n  }\n  100% {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n@-webkit-keyframes menuout {\n  0% {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n    opacity: 1;\n  }\n  100% {\n    -webkit-transform: scale(3);\n    -ms-transform: scale(3);\n    transform: scale(3);\n    opacity: 0;\n  }\n}\n@keyframes menuout {\n  0% {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n    opacity: 1;\n  }\n  100% {\n    -webkit-transform: scale(3);\n    -ms-transform: scale(3);\n    transform: scale(3);\n    opacity: 0;\n  }\n}\n.menuin {\n  display: block;\n  -webkit-animation-fill-mode: forwards;\n  animation-fill-mode: forwards;\n  -webkit-animation-duration: 300ms;\n  animation-duration: 300ms;\n  -webkit-animation-name: menuin;\n  animation-name: menuin;\n  -webkit-transform-origin: 50% 50%;\n}\n.menuout {\n  display: block;\n  -webkit-animation-fill-mode: forwards;\n  animation-fill-mode: forwards;\n  -webkit-animation-duration: 300ms;\n  animation-duration: 300ms;\n  -webkit-animation-name: menuout;\n  animation-name: menuout;\n  -webkit-transform-origin: 50% 50%;\n}\n.ryan-datepicker-v3 {\n  font-size: 0.8rem;\n  position: fixed;\n  left: 0;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 8888;\n  color: #282828;\n}\n.ryan-datepicker-v3 .rdp-back {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 9999;\n  background: #000;\n  cursor: pointer;\n  opacity: .5;\n}\n.ryan-datepicker-v3 .rdp-front {\n  line-height: 2.5rem;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  z-index: 10000;\n  color: #bbb;\n}\n.ryan-datepicker-v3 .rdp-nav {\n  overflow: hidden;\n  border-top: 1px solid #eee;\n  /*no*/\n  border-bottom: 1px solid #eee;\n  /*no*/\n}\n.ryan-datepicker-v3 .rdp-nav .rdp-left {\n  float: left;\n  width: 20%;\n  text-align: center;\n  cursor: pointer;\n  color: #999;\n}\n.ryan-datepicker-v3 .rdp-nav .rdp-right {\n  float: right;\n  width: 20%;\n  text-align: center;\n  cursor: pointer;\n  color: #c91723;\n}\n.ryan-datepicker-v3 .rdp-nav .rdp-auto {\n  overflow: hidden;\n}\n.ryan-datepicker-v3 .rdp-switch {\n  width: 66.66%;\n  margin: 0.25rem auto;\n  border: 1px solid #c91723;\n  /*no*/\n  -webkit-border-radius: 5px;\n          border-radius: 5px;\n  overflow: hidden;\n  line-height: 2rem;\n}\n.ryan-datepicker-v3 .rdp-switch span {\n  display: inline-block;\n  vertical-align: top;\n  width: 50%;\n  text-align: center;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  cursor: pointer;\n  color: #c91723;\n}\n.ryan-datepicker-v3 .rdp-switch .rdp-mode-active {\n  color: #fff;\n  background-color: #c91723;\n}\n.ryan-datepicker-v3 .rdp-body {\n  position: relative;\n}\n.ryan-datepicker-v3 .rdp-layer-top {\n  position: absolute;\n  height: 0 /*no*/;\n  border-top: 2px solid #c91723;\n  /*no*/\n  -webkit-box-shadow: 0 0 .25rem #c91723;\n          box-shadow: 0 0 .25rem #c91723;\n  opacity: .3;\n  top: 5rem;\n  left: 0;\n  right: 0;\n  z-index: 0;\n}\n.ryan-datepicker-v3 .rdp-layer-bottom {\n  position: absolute;\n  height: 0 /*no*/;\n  border-top: 2px solid #c91723;\n  /*no*/\n  -webkit-box-shadow: 0 0 .25rem #c91723;\n          box-shadow: 0 0 .25rem #c91723;\n  opacity: .3;\n  top: 7.5rem;\n  left: 0;\n  right: 0;\n  z-index: 0;\n}\n.ryan-datepicker-v3 .rdp-container {\n  background-color: transparent;\n  z-index: 1;\n}\n.ryan-datepicker-v3 .rdp-container > li {\n  display: inline-block;\n  vertical-align: top;\n  width: 20%;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  text-align: center;\n  height: 12.5rem;\n  overflow: hidden;\n  cursor: default;\n  background-color: transparent;\n}\n.ryan-datepicker-v3 .rdp-container > li.w40 {\n  width: 40%;\n  font-size: 0.64rem;\n}\n.ryan-datepicker-v3 .rdp-item {\n  display: block;\n  background-color: transparent;\n}\n.ryan-datepicker-v3 .rdp-item > li {\n  display: block;\n  line-height: 2.5rem;\n  background-color: transparent;\n}\n.ryan-datepicker-v3 .rdp-item > .prevent {\n  color: #eee;\n}\n.ryan-datepicker-v3 .rdp-item > .current {\n  color: #333;\n  font-weight: bold;\n}\nbody.rdp-open {\n  position: fixed;\n  width: 100%;\n}\n.shop-common-tip-layer {\n  position: fixed;\n  z-index: 19491001;\n  top: 50%;\n  left: 50%;\n  width: 16rem;\n  height: 3rem;\n  margin-top: -1.5rem;\n  margin-left: -8rem;\n}\n.shop-common-tip-layer .back {\n  width: 100%;\n  height: 100%;\n  background: #000;\n  opacity: .5;\n  -webkit-border-radius: 5px;\n          border-radius: 5px;\n  /*no*/\n  -webkit-box-shadow: 0 0 1rem #000;\n          box-shadow: 0 0 1rem #000;\n}\n.shop-common-tip-layer .front {\n  position: absolute;\n  width: 16rem;\n  top: 50%;\n  left: 0;\n  font-size: .875rem;\n  color: #fff;\n  text-align: center;\n  -webkit-transform: translateY(-50%);\n      -ms-transform: translateY(-50%);\n          transform: translateY(-50%);\n}\n.shop-common-tip-layer .front span {\n  color: #fff;\n  font-size: .875rem;\n}\n.common-loading-layer {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 9999;\n}\n.common-loading-layer .back {\n  width: 100%;\n  height: 100%;\n  background: #000;\n  opacity: .5;\n}\n.common-loading-layer .front {\n  width: 16rem;\n  height: 8rem;\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  margin-top: -4rem;\n  margin-left: -8rem;\n  background: #fff;\n  -webkit-border-radius: 0.21333rem;\n  /*no*/\n  /*no*/\n  border-radius: 0.21333rem;\n  /*no*/\n}\n.common-loading-layer .front .img {\n  text-align: center;\n  line-height: 4rem;\n  margin-top: 1rem;\n}\n.common-loading-layer .front .img img {\n  width: 3.125rem!important;\n  vertical-align: middle!important;\n  display: inline-block;\n}\n.common-loading-layer .front .text {\n  text-align: center;\n  color: #b03a3a;\n  font-size: 1rem;\n  padding-top: .25rem;\n}\n.common-loading-layer .front .text i {\n  font-style: normal;\n  -webkit-animation: loading-ani 2s infinite;\n          animation: loading-ani 2s infinite;\n}\n@-webkit-keyframes loading-ani {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n@keyframes loading-ani {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n/*评论的样式*/\n.new-comment {\n  margin: 0.8rem 0.90667rem 0.85333rem;\n  border: 1px solid #3b698f;\n  /*no*/\n  background: #fffce9;\n}\n.new-comment h3 {\n  text-align: center;\n  color: #fff;\n  height: 1.97333rem;\n  line-height: 1.97333rem;\n  font-size: 0.90667rem;\n  border-bottom: 1px solid #152954;\n  /*no*/\n  background: #3b698f url(\'//zxcs.ggwan.com/xuyuandiandeng/images/comment_tit_bg.jpg?version=01201105088920913\') no-repeat center;\n  background-size: 100%;\n}\n.new-comment .marquee-wrapper {\n  height: 12rem;\n  overflow: hidden;\n  position: relative;\n}\n.new-comment .info-list-box {\n  width: 100%;\n  position: absolute;\n  top: 0;\n  color: #5a1501;\n}\n.new-comment .info-list-box li {\n  padding: 0.37333rem 5px 0.4rem;\n  line-height: 1.06667rem;\n}\n.new-comment .info-list-box li + li {\n  border-top: 1px solid #decebb;\n  /*no*/\n}\n.new-comment .info-list-box .user {\n  font-size: 0.64rem;\n}\n.new-comment .info-list-box .detail {\n  font-size: 0.64rem;\n}\nbody {\n  padding-bottom: constant(safe-area-inset-bottom);\n  padding-bottom: env(safe-area-inset-bottom);\n}\n.wrapper, body {\n  background: #a52a1d;\n}\n.wrapper {\n  padding-bottom: 3.2rem;\n}\n/* banner */\n.banner {\n  width: 100%;\n  height: 8.8rem;\n  background: transparent url(\'//zxcs.ggwan.com/xuyuandiandeng/images/order_banner.jpg?version=09274019240506242\') no-repeat center center;\n  background-size: 100% 100%;\n  position: relative;\n}\n.banner .lantern .lantern-on {\n  width: 5.38667rem;\n  height: 6.18667rem;\n  position: absolute;\n  left: 14%;\n  bottom: 14%;\n  z-index: 2;\n}\n.banner .lantern .light {\n  width: 5.6rem;\n  height: 5.06667rem;\n  background: transparent url(\'//zxcs.ggwan.com/xuyuandiandeng/images/guang.png?version=008005915658741092\') no-repeat center center;\n  background-size: 100% 100%;\n  position: absolute;\n  z-index: 1;\n  left: 12.2%;\n  bottom: 32.6%;\n  -webkit-animation: light-ani 2s infinite;\n          animation: light-ani 2s infinite;\n  -webkit-animation-timing-function: ease-in;\n          animation-timing-function: ease-in;\n  -webkit-animation-direction: alternate;\n          animation-direction: alternate;\n}\n.banner .lantern-desc {\n  width: 8.85333rem;\n  height: 5.44rem;\n  position: absolute;\n  top: 50%;\n  right: 6%;\n  -webkit-transform: translate(0, -46%);\n      -ms-transform: translate(0, -46%);\n          transform: translate(0, -46%);\n}\n/* 闪光动画 */\n@-webkit-keyframes light-ani {\n  0% {\n    opacity: .4;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n@keyframes light-ani {\n  0% {\n    opacity: .4;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.title-tips {\n  margin-bottom: 0.8rem;\n}\n.title-tips h5 {\n  font-weight: normal;\n  font-size: 0.69333rem;\n  color: #6b572d;\n  height: 1.6rem;\n  line-height: 1.6rem;\n  background: #fbeac5;\n  text-align: center;\n}\n/* Content */\n.main-content {\n  margin-bottom: 0.74667rem;\n}\n.main-content .order-top {\n  width: 100%;\n  height: 2.13333rem;\n  background: transparent url(\'//zxcs.ggwan.com/xuyuandiandeng/images/t_order.png?version=08080616528040416\') no-repeat top center;\n  background-size: 100% 100%;\n}\n.main-content .order-middle {\n  height: auto;\n  padding: 0 10%;\n  background: transparent url(\'//zxcs.ggwan.com/xuyuandiandeng/images/m_order.png?version=04704222390128583\') repeat-y top center;\n  background-size: 100% 4px;\n}\n.main-content .order-middle .item {\n  padding-bottom: 0.8rem;\n}\n.main-content .order-middle .item:last-child {\n  padding-bottom: 0;\n}\n.main-content .order-middle .effect ul {\n  width: 100%;\n  text-align: center;\n}\n.main-content .order-middle .effect li {\n  width: 50%;\n  text-align: center;\n  margin-bottom: 0.21333rem;\n}\n.main-content .order-middle .effect li.w80 {\n  width: 100%;\n}\n.main-content .order-middle .effect li.w80 .li-wrapper {\n  width: 80%;\n  margin: 0 auto;\n}\n.main-content .order-middle h4 {\n  font-size: 0.8rem;\n  font-weight: normal;\n  color: #5a1501;\n  line-height: 1.49333rem;\n}\n.main-content .order-middle p {\n  font-size: 0.64rem;\n  color: #5a1501;\n  line-height: 1.01333rem;\n}\n.main-content .order-bottom {\n  width: 100%;\n  height: 2.13333rem;\n  background: transparent url(\'//zxcs.ggwan.com/xuyuandiandeng/images/b_order.png?version=09938634245181657\') no-repeat top center;\n  background-size: 100% 100%;\n}\n.popupBtn {\n  min-width: 20rem;\n  max-width: 30rem;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  background-color: rgba(0, 0, 0, 0.5);\n  height: 3.2rem;\n  position: fixed;\n  bottom: 0;\n  z-index: 10000;\n  background: rgba(0, 0, 0, 0.5) url(\'//zxcs.ggwan.com/xuyuandiandeng/images/order_btn.png?timestamp=1531895015529\') center center no-repeat;\n  background-size: 85% 80%;\n}\n',""])},60:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(2),i=g.n(I),r=g(10),o=g.n(r),a=g(35),s=g.n(a),c=g(13),l=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),d=function(e){function t(e){n(this,t);var C=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return C.state={lang:g.i(c.a)("lang")||o.a.getItem("_lang")},C}return C(t,e),l(t,[{key:"componentDidMount",value:function(){this.props.gameId&&this.popTipsInIframe(this.dDips,this.props.sNode),this.setLang()}},{key:"componentDidUpdate",value:function(){this.setLang()}},{key:"setLang",value:function(){var e=this.state.lang;e=e||o.a.getItem("_lang"),s.a.convertTextByLang(e),o.a.setItem("_lang",e,86400,"/")}},{key:"popTipsInIframe",value:function(e,t){var g=t.offsetTop;e.style.position="absolute",e.style.top=g-80+"px"}},{key:"render",value:function(){var e=this;return i.a.createElement("section",{className:"shop-common-tip-layer",ref:function(t){return e.dDips=t}},i.a.createElement("div",{className:"back"}),i.a.createElement("div",{className:"front"},i.a.createElement("span",null,this.props.text)))}}]),t}(I.Component),h=d;t.a=h;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(d,"CommonTipLayer","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/CommonTipLayer.jsx"),__REACT_HOT_LOADER__.register(h,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/CommonTipLayer.jsx"))}()},700:function(e,t,g){var n=g(547);"string"==typeof n&&(n=[[e.i,n,""]]);g(69)(n,{});n.locals&&(e.exports=n.locals)},702:function(e,t,g){var n=g(549);"string"==typeof n&&(n=[[e.i,n,""]]);g(69)(n,{});n.locals&&(e.exports=n.locals)},71:function(e,t,g){"use strict";g.d(t,"a",function(){return A});var n=function(e,t){var g=window.location.host;return/sandbox/i.test(g)?e:/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/.test(g)||/localhost/i.test(g)?e:t},A={PUBLIC_ASSESS_COUPON:"/api/v1/orders/hascomment",SUBMIT_ACCESS:"/api/v1/orders/comment/submit",COUPON_DETAIL:"/api/v1/orders/coupon/config",FETCH_COUPON:"/api/v1/orders/coupon",FETCH_WECHAT_OFFICIAL_PAY:n("http://sandbox.money.lingji666.com/api/v1/charge","https://money.lingji666.com/api/v1/charge"),PAY_QUERY_ORDER:n("http://sandbox.money.lingji666.com/api/v1/charge_result","https://money.lingji666.com/api/v1/charge_result"),TRACK_INFO:"/api/v1/page/collect/info",TRACK_SCAN:"/api/v1/page/collect/scan",TRACK_REGISTER:"/api/v1/page/collect/register",TRACK_PAY:"/api/v1/page/collect/pay",FETCH_RESULT_CONFIG:"/api/v1/page/result/page/config",PAY_POINT:"_default",SHOP_REGISTER_ORDER:"/api/v1/hub/shop/order.json"};!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register("/api/v1/orders/","ORDERS_API_PATH","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonConfig.js"),__REACT_HOT_LOADER__.register(n,"getUrl","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonConfig.js"),__REACT_HOT_LOADER__.register(A,"API_URL","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/commonConfig.js"))}()},72:function(e,t,g){"use strict";g.d(t,"b",function(){return n}),g.d(t,"a",function(){return A});var n="/nativePayConfirm/index",A={bazijingpi_default:{MARK:"bazijingpi",SERVER_ID:"42",ASK_ID:"2012",USER_TYPE:"0",APP_PAY_VERSION:100,APP_UA_MARKS:["linghit"]},mllyuncheng_default:{MARK:"mllyuncheng",SERVER_ID:"105",ASK_ID:"2015",USER_TYPE:"0",APP_PAY_VERSION:200,APP_UA_MARKS:["linghit"]},newhehun_default:{MARK:"newhehun",SERVER_ID:"97",ASK_ID:"2016",USER_TYPE:"1",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},gerenzhanxing_default:{MARK:"gerenzhanxing",SERVER_ID:"104",ASK_ID:"2013",USER_TYPE:"3",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},yishengcaiyun_default:{MARK:"yishengcaiyun",SERVER_ID:"103",ASK_ID:"2014",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:100},ziweicaiyun_default:{MARK:"ziweicaiyun",SERVER_ID:"60",ASK_ID:"",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:100},lunhuishu_default:{MARK:"lunhuishu",SERVER_ID:"45",ASK_ID:"2017",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:100},lianaipeidui_default:{MARK:"lianaipeidui",SERVER_ID:"64",ASK_ID:"2018",USER_TYPE:"3",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},shinianjieshu_default:{MARK:"shinianjieshu",SERVER_ID:"106",ASK_ID:"2019",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},shiyecaiyun_default:{MARK:"shiyecaiyun",SERVER_ID:"107",ASK_ID:"2020",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},xinggeceshi_default:{MARK:"xinggeceshi",SERVER_ID:"108",ASK_ID:"2021",USER_TYPE:"3",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},zeye_default:{MARK:"zeye",SERVER_ID:"44",ASK_ID:"2022",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:100},stfzfengshui_default:{MARK:"stfzfengshui",SERVER_ID:"109",ASK_ID:"2023",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},qianshiyinyuan_default:{MARK:"qianshiyinyuan",SERVER_ID:"110",ASK_ID:"2024",USER_TYPE:"1",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},ganqingyunshi_default:{MARK:"ganqingyunshi",SERVER_ID:"47",ASK_ID:"2025",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:100},lianaitaohua_default:{MARK:"lianaitaohua",SERVER_ID:"61",ASK_ID:"2026",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},baziyunshi_default:{MARK:"baziyunshi",SERVER_ID:"50",ASK_ID:"2027",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:100},zwzjingpi_default:{MARK:"zwzjingpi",SERVER_ID:"111",ASK_ID:"2028",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},xiangpi_default:{MARK:"bazixiangpi",SERVER_ID:"46",ASK_ID:"2029",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:100},xiabansheng_default:{MARK:"xiabansheng",SERVER_ID:"112",ASK_ID:"2030",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},liunianyuncheng_default:{MARK:"liunianyuncheng",SERVER_ID:"98",ASK_ID:"2031",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200},baziziwei_default:{MARK:"baziziwei",SERVER_ID:"62",ASK_ID:"2031",USER_TYPE:"0",APP_UA_MARKS:["linghit"],APP_PAY_VERSION:200}};!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(n,"USER_PAY_CONFIRM_URL","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/native_config.js"),__REACT_HOT_LOADER__.register(A,"configData","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/native_config.js"))}()},73:function(e,t,g){"use strict";function n(e,t,g){return t in e?Object.defineProperty(e,t,{value:g,enumerable:!0,configurable:!0,writable:!0}):e[t]=g,e}function A(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var C=g(17),I=g.n(C),i=g(10),r=(g.n(i),function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}()),o=function(){function e(t,g){A(this,e),this.content=t,this.cookieChannel=g}return r(e,[{key:"transformOjectValueToBoolean",value:function(e){e=JSON.parse(JSON.stringify(e));var t=e&&e.handle;if(t)for(var g in t)e.handle[g]=!!t[g];return e}},{key:"setdataToLocalStorage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"shinianjieshu",t=arguments[1];try{localStorage.setItem(e+"PageConfig",JSON.stringify(t)),localStorage.setItem(e+"PageTime",JSON.stringify(Date.now()))}catch(e){}}},{key:"getDataFromLocalStorage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"shinianjieshu",t=0,g="";try{t=localStorage.getItem(e+"PageTime")||0,g=localStorage.getItem(e+"PageConfig")}catch(e){}return{localTime:t,localConfigData:g}}},{key:"fetchDataFromApi",value:function(e){var t=this,g=this.content;I.a.ajax({url:e.url,data:e.data,dataType:"json",type:"get",success:function(n){var A=t.transformOjectValueToBoolean(n);g.setState({pageConfig:A}),localStorage&&t.setdataToLocalStorage(e.mark,A)},error:function(e){}})}},{key:"getConfigData",value:function(e,t){var g=this.content,n=this.cookieChannel,A=e.data&&e.data.channel,C=this.getDataFromLocalStorage(e.mark),I=Date.now(),i=C.localConfigData,r=C.localTime;A===n&&localStorage?!i||I-r>6e5?this.fetchDataFromApi(e):g.setState({pageConfig:JSON.parse(i)}):this.fetchDataFromApi(e)}}],[{key:"getDefaultData",value:function(){var e;return{handle:(e={header:!1,footer:!1,video:!1,index_comment:!1,index_hot:!1,index_query_order:!1,index_2018kaiyun:!0,index_yingcaier:!0,index_advance:!0,result_shop_recommend:!1,email:!1,show_email:!1,show_phone:!1,result_content:!1,result_ad:!1,default_data:!0,result_bottom_menu:!0,result_icon_url:!0,index_detain:!1,result_zw:!1,index_form:!0,index_float_button_action:!0,pay_kefu_button:!0,index_xuanfu_icon:!1},n(e,"pay_kefu_button",!0),n(e,"index_qrcode",!0),n(e,"is_go_wechat",!1),e),content:{footer:'<div>     <div style="padding: 0.625rem 1.4375rem 5rem; background: rgb(36, 20, 6); color: rgb(143, 123, 106); font-size: 0.75rem; text-align: center; line-height: 1.25rem;"><a href="https://kefu.lingji666.com?channel=test" style="color: rgb(143, 123, 106); display: block;">如需帮助点此     <img src="https://s103.ggwan.com/zxcs/kefu_03.png" alt="" style="width: 1.25rem; margin: 0px 0.25rem; display: inline-block;">     <span style="color: rgb(240, 196, 158); text-decoration: underline;">请联系专属售后客服</span></a><p></p></div></div>',banner:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAu4AAAACCAAAAAAibwbGAAAAGUlEQVQ4y2P8wjAKRsFIAUwD7YBRMAroBwCoiQD4td2YtwAAAABJRU5ErkJggg==",video:"https://v.qq.com/iframe/player.html?vid=u0563byxdxo&tiny=0&auto=0",price:"",query_banner:"",wechat_link:""}}}}]),e}(),a=o;t.a=a;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(o,"PageConfig","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/page_config.js"),__REACT_HOT_LOADER__.register(a,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/page_config.js"))}()},74:function(e,t,g){"use strict";function n(){var e=this,t=["zxcsLinghit","Tencent"];setTimeout(function(){t.forEach(function(t){A[t].apply(e)})},0)}t.a=n;var A={zxcsLinghit:function(){!function(){var e=document.createElement("script");e.src="//hm.baidu.com/hm.js?da9f609f31e08775a3c08224838230b5";var t=document.getElementsByTagName("script")[0];t.parentNode.insertBefore(e,t)}()},Tencent:function(){!function(){var e=document.createElement("script");e.src="//pingjs.qq.com/h5/stats.js?v2.0.4",e.setAttribute("name","MTAH5"),e.setAttribute("sid","500623120"),e.setAttribute("cid","500623313");var t=document.getElementsByTagName("script")[0];t.parentNode.insertBefore(e,t)}()}};!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(A,"types","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/statistics/index.js"),__REACT_HOT_LOADER__.register(n,"addStatisticsCode","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/statistics/index.js"))}()},88:function(e,t,g){"use strict";function n(e){var t=["365wnl","51wnl","91zm","az51wml","az51wnl","brzm","bwdsfsx","cwlp","ggbook","gzm","hmkj","lapwnl","lbllq","lcllq","llmfwf","milizm","mxzm","xzzj","zhwnl"],g=t.some(function(t){return e===t}),n=/^(sw)/.test(e);return g||n}function A(e){return/^(bd-xxl)/.test(e)||/^(bd-sem)/.test(e)||/^(sm-sem)/.test(e)||/^(sg-sem)/.test(e)||/^(xl-xxl)/.test(e)||/^(qtt-xxl)/.test(e)||/^(bd-1xxl)/.test(e)}function C(e){return["swazlhl","swzhlhl"].some(function(t){return e===t})}function I(e){return["51wnl","az51wnl","swwnlzfa","swwnlzfb","zhwnl","swzhwml","zhwml","swzhwnla","swzhwnlb","swzhwnlzf"].some(function(t){return e===t})}function i(e){return["baidusem"].some(function(t){return e===t})}function r(e){return["swxea","swdym"].some(function(t){return e===t})}function o(e){if("swxea"===e){var t=document.createElement("script");t.type="text/javascript",t.async=!0,t.src="https://s95.cnzz.com/z_stat.php?id=1261107865&web_id=1261107865",document.body.appendChild(t)}}function a(e){return["swazwnys","swwnys"].some(function(t){return e===t})}function s(e){return["swmzrl"].some(function(t){return e===t})}function c(e){return["swxea","swtsqxnw","swdym"].some(function(t){return e===t})}function l(e){return!e||{swmmwl:!0,swsmxzg:!0,zhwnl:!0,swzhwml:!0,zhwml:!0,swzhwnla:!0,swzhwnlb:!0,swzhwnlaios:!0}[e]}function d(e){return!e||{swmmwl:!0,sw1569:!0,swqdyj:!0}[e]}function h(e){return["swmmw9","swmmw10","swmmw11"].some(function(t){return e===t})}function m(e){return!(!/bdth-/.test(e)&&!/swxzw/.test(e))}function u(e){return!!/appzxcs_ios_1372770441/.test(e)}function p(e){return!!(/bd-/.test(e)||/gdt-/.test(e)||/oppo-/.test(e)||/jrtt-/.test(e))}function f(e){return!!/jrtt-/.test(e)}function _(e){return!!/kjyx_app_ios_/.test(e)}function v(e){return["swxmjm","swxmlq"].some(function(t){return e===t})}t.a=n,t.b=_;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(n,"isBusinessChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(A,"isTouFangChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(C,"hideNavigatorTextForTheChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(I,"showForTheWNLChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(i,"changeMasterInfoToHeHanMingForTheChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(r,"hideMasterVideoForTheChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(o,"addXiaoEnAiStatisticsCode","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(a,"hideEmailForTheChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(s,"hideHeaderForTheMeiZuChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(c,"showTheXiaoEnAiPageByChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(l,"spcialChannelHideDeal","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(d,"specialChannelHideExceptEmail","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(h,"specialChannelForCHIXIAO","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(m,"isBDTHChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(u,"isAppIOSChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(p,"isZyqmTFChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(f,"isZyqmTFJrtt","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(_,"isAppIosChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"),__REACT_HOT_LOADER__.register(v,"goWechatChannel","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/service/specialHandlingForChannel.js"))}()},98:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(2),i=g.n(I),r=g(10),o=g.n(r),a=g(17),s=g.n(a),c=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),l=function(e){function t(e){n(this,t);var g=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return g.props=e,g.state={timestamp:g.props.timestamp?g.props.timestamp:""},g}return C(t,e),c(t,[{key:"render",value:function(){return i.a.createElement("li",null,i.a.createElement("a",{href:this.props.items.link},i.a.createElement("span",{className:"icon"},i.a.createElement("img",{src:this.props.items.icon+this.state.timestamp,alt:this.props.items.title})),i.a.createElement("p",null,this.props.items.title)))}}]),t}(I.Component),d=function(e){function t(e){n(this,t);var g=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return g.props=e,g.state={channel:o.a.getItem("_channel"),items:[],className:"site-menu"},g}return C(t,e),c(t,[{key:"setMenuDataToLocalStorage",value:function(e){if(localStorage){var t=(new Date).getTime();localStorage.setItem("menu_data",JSON.stringify(e)),localStorage.setItem("menu_last_time",t+"")}}},{key:"whetherGetMenuDataFromLocalStorage",value:function(){var e,t,g,n;return localStorage?(e=localStorage.getItem("menu_last_time")||(new Date).getTime(),t=(new Date).getTime(),g=!(t-e>36e5),n=localStorage.getItem("menu_data"),n&&"string"==typeof n&&n.length>0?n=JSON.parse(n):g=!1):g=!1,{flag:g,menuData:n}}},{key:"getDataFromServer",value:function(){var e=this,t={};try{t=this.whetherGetMenuDataFromLocalStorage()}catch(e){t.flag=!1}if(t.flag)return void this.setState({items:t.menuData});s.a.ajax({url:"/api/v1/page/menu.json?channel="+e.state.channel,type:"get",success:function(t){if(t&&t.length>0&&!/DOCTYPE/i.test(t)&&"[object Array]"==Object.prototype.toString.apply(t)){e.setState({items:t});try{e.setMenuDataToLocalStorage(t)}catch(e){}}}})}},{key:"componentWillMount",value:function(){this.getDataFromServer()}},{key:"componentWillReceiveProps",value:function(e){var t=this,g=e.toggle,n=this.props.toggle,A=g!==n;g&&A?(document.getElementById("wrapper").style.overflow="hidden",this.setState({className:"site-menu menu-show menuin"})):!g&&A&&(document.getElementById("wrapper").style.overflow="auto",this.setState({className:"site-menu menuout"}),setTimeout(function(){t.setState({className:"site-menu"})},300))}},{key:"render",value:function(){var e=this;return i.a.createElement("menu",{className:this.state.className},i.a.createElement("div",{className:"menu-wrapper"},i.a.createElement("ul",{className:"li-left clear"},this.state.items.map(function(t,g){return i.a.createElement(l,{key:g,items:t,timestamp:e.props.timestamp})}))))}}]),t}(I.Component),h=d;t.a=h;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(l,"MenuItem","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/CommonSiteMenu.jsx"),__REACT_HOT_LOADER__.register(d,"CommonSiteMenu","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/CommonSiteMenu.jsx"),__REACT_HOT_LOADER__.register(h,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/CommonSiteMenu.jsx"))}()},99:function(e,t,g){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var I=g(2),i=g.n(I),r=g(13),o=g(88),a=g(10),s=g.n(a),c=g(98),l=function(){function e(e,t){for(var g=0;g<t.length;g++){var n=t[g];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,g,n){return g&&e(t.prototype,g),n&&e(t,n),t}}(),d=function(e){function t(e){n(this,t);var C=A(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return C.state={channel:g.i(r.a)("channel")||s.a.getItem("_channel")||"other",siteMenuToggle:!1},C}return C(t,e),l(t,[{key:"eventBackBtnClickHandle",value:function(){window.history.go(-1)}},{key:"eventSiteMenuClickHandle",value:function(){var e=this.state.siteMenuToggle;this.setState({siteMenuToggle:!e})}},{key:"render",value:function(){var e=this.state;return i.a.createElement("div",null,!g.i(o.a)(e.channel)&&i.a.createElement("header",{className:"new-header"},i.a.createElement("div",{className:"left",onClick:this.eventBackBtnClickHandle},i.a.createElement("span",{className:"icon-back"})),i.a.createElement("div",{className:"right",onClick:this.eventSiteMenuClickHandle.bind(this)},i.a.createElement("span",{className:"icon-menu"})),i.a.createElement("div",{className:"auto"},i.a.createElement("a",{className:"color1",href:"//touch.lingji666.com/collect?channel="+this.props.channel},"测算大全"),i.a.createElement("span",{className:"icon-next"}),i.a.createElement("span",null,this.props.forecastName))),i.a.createElement(c.a,{toggle:e.siteMenuToggle}))}}]),t}(I.Component),h=d;t.a=h;!function(){"undefined"!=typeof __REACT_HOT_LOADER__&&(__REACT_HOT_LOADER__.register(d,"NewCommonNav","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/NewCommonNav.jsx"),__REACT_HOT_LOADER__.register(h,"default","D:/zxcsnew/forecast.linghit.com.frontend/src.frontend/src/common/components/NewCommonNav.jsx"))}()}},[341]);