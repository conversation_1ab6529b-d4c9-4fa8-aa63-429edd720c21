<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"

        "http://www.w3.org/TR/html4/loose.dtd">

<html>

<head>

    <title></title>

    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>

    <script type="text/javascript" src="../internal.js"></script>

    <style type="text/css">

        .warpper{ position:relative;width: 380px; height: 100%; margin: 10px auto;}

        .tabbody{height: 160px;}

        .tabbody table{width:100%;border-collapse: separate;border-spacing: 3px;}

        .tabbody .panel{width:373px;height:100%;padding-left: 5px;position: absolute;background-color: #fff;}

        .tabbody input.int{ width:190px;height:21px;border:1px solid #d7d7d7;line-height:21px;}

        .tabbody input.btn{padding: 0 5px; text-align:center;line-height:24px; text-decoration: none;height:24px;background:url("../../themes/default/images/dialog-title-bg.png") repeat-x;border:1px solid #ccc; }

    </style>

</head>

<body>

<div class="warpper" id="searchtab">

    <div id="head" class="tabhead">

        <span  tabsrc="find" class="focus"><var id="lang_tab_search"></var></span>

        <span  tabsrc="replace" ><var id="lang_tab_replace"></var></span>

    </div>

    <div class="tabbody">

        <div class="panel" id="find">

            <table>

                <tr>

                    <td width="80"><var id="lang_search1"></var>: </td>

                    <td><input id="findtxt" type="text" class="int" /></td>

                </tr>

                <!--<tr>-->



                    <!--<td colspan="2"><span style="color:red"><var id="lang_searchReg"></var></span></td>-->

                <!--</tr>-->

                <tr>

                    <td><var id="lang_case_sensitive1"></var></td>

                    <td>

                        <input id="matchCase" type="checkbox" />

                    </td>

                </tr>

                <tr>

                    <td colspan="2">

                        <input id="nextFindBtn" type="button" class="btn" />

                        <input id="preFindBtn" type="button" class="btn" />

                    </td>

                </tr>

                <tr>

                    <td colspan="2">

                        &nbsp;

                    </td>

                </tr>

                <tr>

                    <td colspan="2">

                        <span id="search-msg" style="color:red"></span>

                    </td>

                </tr>

            </table>

        </div>

        <div class="panel" id="replace">

            <table>

                <tr>

                    <td width="80"><var id="lang_search2"></var>: </td>

                    <td><input id="findtxt1" type="text" class="int"  /></td>

                </tr>

                <!--<tr>-->



                    <!--<td colspan="2"><span style="color:red"><var id="lang_searchReg1"></var></span></td>-->

                <!--</tr>-->

                <tr>

                    <td><var id="lang_replace"></var>: </td>

                    <td><input id="replacetxt" type="text" class="int" /></td>

                </tr>

                <tr>

                    <td><var id="lang_case_sensitive2"></var></td>

                    <td>

                        <input id="matchCase1" type="checkbox" />

                    </td>

                </tr>

                <tr>

                    <td colspan="2">

                        <input id="nextReplaceBtn" type="button" class="btn" />

                        <input id="preReplaceBtn" type="button" class="btn" />

                        <input id="repalceBtn" type="button" class="btn" />

                        <input id="repalceAllBtn" type="button" class="btn" />

                    </td>

                </tr>

                <tr>

                    <td colspan="2">

                        &nbsp;

                    </td>

                </tr>

                <tr>

                    <td colspan="2">

                        <span id="replace-msg" style="color:red"></span>

                    </td>

                </tr>

            </table>

        </div>

    </div>

</div>

<script type="text/javascript" src="searchreplace.js"></script>

</body>

</html>