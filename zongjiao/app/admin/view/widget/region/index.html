<link rel="stylesheet" href="/static/widget/admin/region/region_style.css">
<div>
    <select name="province_{$widget_data.name}" id="province_{$widget_data.name}" class="form-control addon-form-group-select"></select>
    <select name="city_{$widget_data.name}" id="city_{$widget_data.name}" class="form-control addon-form-group-select"></select>
    <select name="county_{$widget_data.name}" id="county_{$widget_data.name}" class="form-control addon-form-group-select"></select>
</div>

<script type="text/javascript">

$(function(){
    
    var province_id = "{$widget_data['province']}";
    var city_id = "{$widget_data['city']}";
    var county_id = "{$widget_data['county']}";
    
    var get_options_url = '{:url("Widget/getRegionOptions")}';
    
    function changeProvince(province_id = 0, select_id = 0)
    {
        $.get(get_options_url, {upid: province_id, select_id: select_id, level : 1}, function(result){ $("#province_{$widget_data.name}").html(result); });
    }

    function changeCity(city_id = 0, select_id = 0)
    {
        $.get(get_options_url, {upid: city_id, select_id: select_id, level : 2}, function(result){ $("#city_{$widget_data.name}").html(result); });
    }

    function changeCounty(county_id = 0, select_id = 0)
    {
        $.get(get_options_url, {upid: county_id, select_id: select_id, level : 3}, function(result){ $("#county_{$widget_data.name}").html(result); });
    }
    
    changeProvince(0, province_id);
    changeCity(province_id, city_id);
    changeCounty(city_id, county_id);

    $("#province_{$widget_data.name}").change(function(){ changeCity($("#province_{$widget_data.name}").val());});
    $("#city_{$widget_data.name}").change(function(){ changeCounty($("#city_{$widget_data.name}").val());});
});
</script>