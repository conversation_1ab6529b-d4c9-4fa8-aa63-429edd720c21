<div class="box">
    <div class="box-body table-responsive">
        <table  class="table table-bordered table-hover table-striped">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>标识</th>
                    <th>描述</th>
                    <th>版本</th>
                    <th>作者</th>
                    <th>操作</th>
                </tr>
            </thead>
            {notempty name='list'}
                <tbody>
                    {volist name='list' id='vo'}
                        <tr>
                            <td>{$vo.title}</td>
                            <td>{$vo.name}</td>
                            <td>{$vo.describe}</td>
                            <td>{$vo.version}</td>
                            <td>{$vo.author}</td>
                            <td class="col-md-1 text-center">
                                {eq name='vo.is_install' value='1'}
                                    <ob_link><a class="btn confirm ajax-get"  href="{:url('addonUninstall', array('name' => $vo['name']))}"><i class="fa fa-remove"></i> 卸 载</a></ob_link>
                                    {else/}
                                    <ob_link><a class="btn confirm ajax-get"  href="{:url('addonInstall', array('name' => $vo['name']))}"><i class="fa fa-refresh"></i> 安 装</a></ob_link>
                                {/eq}
                            </td>
                        </tr>
                    {/volist}
                </tbody>
                {else/}
                <tbody>
                    <tr class="odd"><td colspan="6" class="text-center" valign="top">{:config('empty_list_describe')}</td></tr>
                </tbody>
            {/notempty}
        </table>
    </div>
</div>