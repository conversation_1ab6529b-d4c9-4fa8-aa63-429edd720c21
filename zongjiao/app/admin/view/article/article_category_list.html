<div class="box">
	<div class="box-header">
		<ob_link><a href="{:url('articleCategoryAdd')}" class="btn"><i class="fa fa-plus"></i> 新 增</a></ob_link>
		<br/>
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover table-striped">
			<thead>
			<tr>
				<th>名称</th>
				<th>描述</th>
				<th>图标</th>
				<th class="sort-th">排序</th>
				<th>操作</th>
			</tr>
			</thead>
			
			{notempty name='list'}
			<tbody>
			{volist name='list' id='vo'}
			<tr>
				<td>
					{empty name="vo.url"}
					{$vo.name}
					{else /}
					<a target="_blank" href="{$vo.url}">{$vo.name}</a>
					{/empty}
				
				</td>
				<td>{$vo.describe}</td>
				<td>
					<img class="admin-list-img-size" src="{$vo.icon|get_picture_url}"/>
				</td>
				<td><input type="text" class="sort-th sort-text" href="{:url('setCategorySort')}" id="{$vo.id}"
				           value="{$vo.sort}"/></td>
				<td class="col-md-2 text-center">
					<ob_link><a href="{:url('articleCategoryEdit', array('id' => $vo['id']))}" class="btn btn-xs"><i
							class="fa fa-edit"></i> 编辑</a></ob_link>
					<ob_link><a class="btn confirm ajax-get btn-xs"
					            href="{:url('articleCategoryDel', array('id' => $vo['id']))}"><i
							class="fa fa-trash-o"></i> 删除</a></ob_link>
				</td>
			</tr>
			{/volist}
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="4" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
		</table>
	</div>
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>
</div>