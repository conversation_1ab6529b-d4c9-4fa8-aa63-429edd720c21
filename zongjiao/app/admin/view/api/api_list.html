<div class="box">
	<div class="box-header">
		
		<div class="row search-form">
			<div class="col-sm-4">
				<ob_link><a class="btn" href="{:url('apiAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
				<a class="btn" href="/api.php" target="_blank"><i class="fa fa-book"></i> 接口文档</a>
			</div>
			<div class="col-sm-4">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">vip</button>
						</span>
                    
                    <select name="group_id" class="form-control">
                        <option value="0">不限</option>
                        {volist name='ApiGroupList' id='vo'}
                        <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
				</div>
			</div>
			
			<div class="col-sm-4">
				<div class="box-tools pull-right">
					<div class="input-group input-group-sm">
						
						<input type="text" name="search_data" style="width: 200px;" class="form-control pull-right"
						       value="{:input('search_data')}" placeholder="请输入接口名称">
						
						<div class="input-group-btn">
							<button type="button" id="search" url="{:url('apilist')}" class="btn btn-info btn-flat"><i
									class="fa fa-search"></i></button>
						</div>
					
					</div>
				</div>
			</div>
		</div>
	
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover table-striped">
			<thead>
			<tr>
				<th class="checkbox-select-all">
					<label>
						<input class="flat-grey js-checkbox-all" type="checkbox">
					</label>
				</th>
				<th>名称</th>
				<th>分组</th>
				<th>地址</th>
				<th>进度</th>
				<th>研发者</th>
				<th class="sort-th">排序</th>
				<th class="status-th">状态</th>
				<th>操作</th>
			</tr>
			</thead>
			
			{notempty name='list'}
			<tbody>
			{volist name='list' id='vo'}
			<tr>
				<td>
					<label>
						<input class="flat-grey" type="checkbox" name="ids" value="{$vo.id}">
					</label>
				</td>
				<td>{$vo.name}</td>
				<td>{$vo.group_name}</td>
				<td>{$vo.api_url}</td>
				<td>{$vo.api_status_text}</td>
				<td>{$vo.developer_text}</td>
				<td>
					<input type="text" class="sort-th sort-text" href="{:url('setSort')}" id="{$vo.id}"
					       value="{$vo.sort}"/>
				</td>
				<td>
					<ob_link><a class="ajax-get"
					            href="{:url('setStatus', array('ids' => $vo['id'], 'status' => (int)!$vo['status']))}">{$vo.status_text}</a>
					</ob_link>
				</td>
				<td class="col-md-2 text-center">
					<ob_link><a href="{:url('apiEdit', array('id' => $vo['id']))}" class="btn"><i
							class="fa fa-edit"></i> 编 辑</a></ob_link>
					<ob_link><a class="btn confirm ajax-get"
					            href="{:url('setStatus', array('ids' => $vo['id'], 'status' => $Think.DATA_DELETE))}"><i
							class="fa fa-trash-o"></i> 删 除</a></ob_link>
				</td>
			</tr>
			{/volist}
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="9" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
		</table>
		
		{include file="layout/batch_btn_group"/}
	
	</div>
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>
</div>
<script>
    $(document).ready(function () {
        ob.setValue("group_id", "{present name='$_GET["group_id"]'}{$Think.get.group_id}{else}0{/present}");
    });

</script>