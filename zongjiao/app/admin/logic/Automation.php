<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\admin\logic;

/**
 * 自动生成模块逻辑
 */
class Automation extends AdminBase
{

    /**
     * 获取会员列表搜索条件
     */
    public function getWhere($data = [])
    {

        $where = [];


        return $where;
    }

    /**
     * 获取自动生成模块列表
     */
    public function getAutomationList($where = [])
    {

        return $this->modelAutomation->getList($where, true, 'create_time desc');
    }

    /**
     * 自动生成模块删除
     */
    public function automationDel($where = [])
    {
        $info = $this->modelAutomation->getInfo($where);

        $url_prefix = '../app/' . $info['module'];

        $this->delDirAndFile($url_prefix . '/view/' . str_replace("_", "", $info['name']), true);

        unlink($url_prefix . '/controller/' . ucwords(str_replace("_", "", $info['name'])) . '.php');
        unlink($url_prefix . '/logic/' . str_replace(" ", "", ucwords(str_replace("_", " ", $info['name'])) . '.php'));
        unlink($url_prefix . '/model/' . str_replace(" ", "", ucwords(str_replace("_", " ", $info['name'])) . '.php'));
        unlink($url_prefix . '/validate/' . str_replace(" ", "", ucwords(str_replace("_", " ", $info['name'])) . '.php'));


        return $this->modelAutomation->deleteInfo($where) ? [RESULT_SUCCESS, '日志删除成功'] : [RESULT_ERROR, $this->modelAutomation->getError()];
    }

    /**
     * 自动生成后台模块添加
     */
    public function automationAdd($data = [])
    {

        $validate_result = $this->validateAutomation->scene('add')->check($data);

        if (!$validate_result) {

            return [RESULT_ERROR, $this->validateAutomation->getError()];
        }

        $url_prefix = '../app/' . $data['module'];
        $template_prefix = 'automation/' . $data['module'];


        $logic = file_get_contents($template_prefix . '/logic/logic.tpl');
        $model = file_get_contents($template_prefix . '/model/model.tpl');
        $validate = file_get_contents($template_prefix . '/validate/validate.tpl');

        $controller = file_get_contents($template_prefix . '/controller/controller.tpl');
        $view_edit = file_get_contents($template_prefix . '/view/view_edit.tpl');
        $view_list = file_get_contents($template_prefix . '/view/view_list.tpl');

        $tableName = SYS_DB_PREFIX . strtolower($data['name']);
        $my_sql = "CREATE TABLE `" . $tableName . "` (`id` int(8) UNSIGNED NOT NULL COMMENT '主键',";
        $view_edit_data = '';
        $view_list_thead = '<th>id</th>' . PHP_EOL;
        $view_list_tbody = '<td>{$vo.id}</td>' . PHP_EOL;
        foreach ($data['field'] as $key => $val) {

            if (!empty($data['is_show'][$key])) {
                $view_list_thead .= '<th>' . $data['thead'][$key] . '</th>' . PHP_EOL;
                $view_list_tbody .= '<td>{$vo.' . $data['field'][$key] . '}</td>' . PHP_EOL;
                unset($data['is_show'][$key]);
            }
            if (!empty($val)) {
                $length = (!empty($data['length'][$key]) ? '(' . $data['length'][$key] . ')' : '');
                $default = (!empty($data['default'][$key]) ? $data['default'][$key] : 'NULL');

                $file_type = 'varchar';
                if ($data['type'][$key] == 'int' or $data['type'][$key] == 'text' or $data['type'][$key] == 'float') {
                    $file_type = $data['type'][$key];
                }
                $my_sql .= "`" . $val . "` " . $file_type . "" . $length . " DEFAULT " . $default . " COMMENT '" . $data['thead'][$key] . "',";


            }

            $view_edit_data .= $this->get_feild_generate($data, $key);

        }

        $view_edit = str_replace("[field_lines]", $view_edit_data, $view_edit);

        $view_list = str_replace("[thead_data]", $view_list_thead, $view_list);

        $view_list = str_replace("[tbody_data]", $view_list_tbody, $view_list);

        $my_sql .= "`update_time` int(11) UNSIGNED NOT NULL DEFAULT '0',`create_time` int(11) UNSIGNED NOT NULL DEFAULT '0',`member_id` int(8) UNSIGNED NOT NULL DEFAULT '1' COMMENT '操作者id',`status` tinyint(1) NOT NULL DEFAULT '1') ENGINE={$data['engine']} DEFAULT CHARSET=utf8 COMMENT='{$data['title']}';";


        $mc_data['upper_name'] = str_replace(" ", "", ucwords(str_replace("_", " ", strtolower($data['name']))));
        $mc_data['lower_name'] = lcfirst($mc_data['upper_name']);
        $mc_data['title'] = $data['title'];
        $mc_data['ucwords_name'] = ucwords(strtolower($mc_data['upper_name']));

        $view_url = $url_prefix . '/view/' . strtolower($mc_data['upper_name']);

        dirs_mk($view_url, $mode = 0777);            //生成view文件夹

        //替换配置项
        foreach ($mc_data as $name => $value) {

            $controller = str_replace("[$name]", $value, $controller);
            $logic = str_replace("[$name]", $value, $logic);
            $model = str_replace("[$name]", $value, $model);
            $validate = str_replace("[$name]", $value, $validate);
            $view_list = str_replace("[$name]", $value, $view_list);
        }
        $validate = str_replace("[upper_name]", $mc_data['upper_name'], $validate);

        file_put_contents($url_prefix . '/logic/' . $mc_data['upper_name'] . '.php', $logic);
        file_put_contents($url_prefix . '/model/' . $mc_data['upper_name'] . '.php', $model);
        file_put_contents($url_prefix . '/validate/' . $mc_data['upper_name'] . '.php', $validate);

        file_put_contents($url_prefix . '/controller/' . $mc_data['ucwords_name'] . '.php', $controller);
        file_put_contents($view_url . '/' . $mc_data['lower_name'] . '_edit.html', $view_edit);
        file_put_contents($view_url . '/' . $mc_data['lower_name'] . '_list.html', $view_list);

        try {
            $this->modelAutomation->execute($my_sql);
            $this->modelAutomation->execute("ALTER TABLE `" . $tableName . "` ADD PRIMARY KEY (`id`);");
            $this->modelAutomation->execute("ALTER TABLE `" . $tableName . "` MODIFY `id` int(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';");
        } catch (Exception $e) {
            // 模拟事务操作，滚回原表
            return [RESULT_ERROR, '生成失败,原因种种'];
            throw new Exception($e->getMessage());
        }
        unset($data['is_show']);
        if (!empty($data['pid'])) {
            $menu['name'] = $data['title'];
            $menu['sort'] = 0;
            $menu['pid'] = $data['pid'];
            $menu['is_hide'] = 0;
            $menu['icon'] = 'fa-th-list';
            $menu['module'] = $data['module'];
            $menu['url'] = $mc_data['ucwords_name'] . SYS_DS_PROS . $mc_data['lower_name'] . 'List';

            $pid = $this->logicMenu->menuPureAdd($menu);

        }
        unset($data['pid']);
        $data['thead'] = implode(",", $data['thead']);
        $data['field'] = implode(",", $data['field']);
        $data['type'] = implode(",", $data['type']);

        $result = $this->modelAutomation->setInfo($data);

        $result && action_log('新增', '新增模块，id：' . $data['name']);
        return $result ? [RESULT_SUCCESS, '新增模块成功', url('automationList')] : [RESULT_ERROR, $this->modelAutomation->getError()];
    }

    public function get_feild_generate($data = [], $key = 0)
    {
        $html = '
             <div class="col-md-6">
                   <div class="form-group">
                       <label>' . $data["thead"][$key] . '</label>
                         <span class="">（' . $data["thead"][$key] . '）</span>';

        switch ($data['type'][$key]) {
            case 'varchar':
            case 'text':
            case 'float':
                $html .= '<input class="form-control" name="' . $data["field"][$key] . '" placeholder="请输入' . $data["thead"][$key] . '" value="{$info[\'' . $data["field"][$key] . '\']|default=\'\'}" type="text">';
                break;
            case 'int':
                $html .= '<input class="form-control" name="' . $data["field"][$key] . '" placeholder="请输入' . $data["thead"][$key] . '" value="{$info[\'' . $data["field"][$key] . '\']|default=\'\'}" type="number">';
                break;
            case 'img_upload':
                $html .= '{assign name="' . $data["field"][$key] . '" value="$info.' . $data["field"][$key] . '|default=\'0\'" /}
                {:widget(\'file/index\', [\'name\' => \'' . $data["field"][$key] . '\', \'value\' => $' . $data["field"][$key] . ', \'type\' => \'img\'])}';
                break;
            case 'file_upload':
                $html .= '{assign name="' . $data["field"][$key] . '" value="$info.' . $data["field"][$key] . '|default=\'0\'" /}
                {:widget(\'file/index\', [\'name\' => \'' . $data["field"][$key] . '\', \'value\' => $' . $data["field"][$key] . ', \'type\' => \'file\'])}';
                break;
            case 'imgs_upload':
                $html .= '{assign name="' . $data["field"][$key] . '" value="$info.' . $data["field"][$key] . '|default=\'\'" /}
                {:widget(\'file/index\', [\'name\' => \'' . $data["field"][$key] . '\', \'value\' => $' . $data["field"][$key] . ', \'type\' => \'imgs\'])}';
                break;
            default:
                break;
        }
        $html .= '
                    </div>
              </div>';
        return $html;
    }

    public function delDirAndFile($path, $delDir = FALSE)
    {
        if (file_exists($path)) {
            $handle = opendir($path);
            if ($handle) {
                while (false !== ($item = readdir($handle))) {
                    if ($item != "." && $item != "..")
                        is_dir("$path/$item") ? delDirAndFile("$path/$item", $delDir) : unlink("$path/$item");
                }
                closedir($handle);
                if ($delDir)
                    return rmdir($path);
            } else {
                return unlink($path);
            }
        } else {
            return FALSE;
        }
    }

    /**
     * 自动生成模块添加
     */
    public function automationApiAdd($data = [])
    {

        $validate_result = $this->validateAutomation->scene('add')->check($data);

        if (!$validate_result) {

            return [RESULT_ERROR, $this->validateAutomation->getError()];
        }

        $url_prefix = '../app/';
        $template_prefix = 'automation/' . $data['module'];

        $controller = file_get_contents($template_prefix . '/controller/controller.tpl');
        $logic = file_get_contents($template_prefix . '/logic/logic.tpl');
        $model = file_get_contents($template_prefix . '/model/model.tpl');
        $validate = file_get_contents($template_prefix . '/validate/validate.tpl');

        $admin_controller = file_get_contents('automation/admin/controller/controller.tpl');
        $view_edit = file_get_contents('automation/admin/view/view_edit.tpl');
        $view_list = file_get_contents('automation/admin/view/view_list.tpl');

        $tableName = SYS_DB_PREFIX . strtolower($data['name']);
        $ret = $this->modelAutomation->execute("SHOW TABLES LIKE '$tableName'");
        // 表存在
        if (!$ret) {


            $my_sql = "CREATE TABLE `" . $tableName . "` (`id` int(8) UNSIGNED NOT NULL COMMENT '主键',";
            $view_edit_data = '';
            $view_list_thead = '<th>id</th>' . PHP_EOL;
            $view_list_tbody = '<td>{$vo.id}</td>' . PHP_EOL;
            foreach ($data['field'] as $key => $val) {
                if (!empty($data['is_show'][$key])) {
                    $view_list_thead .= '<th>' . $data['thead'][$key] . '</th>' . PHP_EOL;
                    $view_list_tbody .= '<td>{$vo.' . $data['field'][$key] . '}</td>' . PHP_EOL;
                    unset($data['is_show'][$key]);
                }
                if (!empty($val)) {
                    $length = (!empty($data['length'][$key]) ? '(' . $data['length'][$key] . ')' : '');
                    $default = (!empty($data['default'][$key]) ? $data['default'][$key] : 'NULL');
                    $file_type = 'varchar';
                    if ($data['type'][$key] == 'int' or $data['type'][$key] == 'text' or $data['type'][$key] == 'float') {
                        $file_type = $data['type'][$key];
                    }
                    $my_sql .= "`" . $val . "` " . $file_type . "" . $length . " DEFAULT " . $default . " COMMENT '" . $data['thead'][$key] . "',";
                }
                $view_edit_data .= $this->get_feild_generate($data, $key);
            }


            $view_edit = str_replace("[field_lines]", $view_edit_data, $view_edit);

            $view_list = str_replace("[thead_data]", $view_list_thead, $view_list);

            $view_list = str_replace("[tbody_data]", $view_list_tbody, $view_list);


            $my_sql .= "`update_time` int(11) UNSIGNED NOT NULL DEFAULT '0',`create_time` int(11) UNSIGNED NOT NULL DEFAULT '0',`member_id` int(8) UNSIGNED NOT NULL DEFAULT '1' COMMENT '操作者id',`status` tinyint(1) NOT NULL DEFAULT '1') ENGINE={$data['engine']} DEFAULT CHARSET=utf8 COMMENT='{$data['title']}';";

            try {
                $this->modelAutomation->execute($my_sql);
                $this->modelAutomation->execute("ALTER TABLE `" . $tableName . "` ADD PRIMARY KEY (`id`);");
                $this->modelAutomation->execute("ALTER TABLE `" . $tableName . "` MODIFY `id` int(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键';");
            } catch (Exception $e) {
                // 模拟事务操作，滚回原表
                return [RESULT_ERROR, '生成失败,原因种种'];
                throw new Exception($e->getMessage());
            }
        }


        $mc_data['upper_name'] = str_replace(" ", "", ucwords(str_replace("_", " ", strtolower($data['name']))));
        $mc_data['lower_name'] = lcfirst($mc_data['upper_name']);
        $mc_data['title'] = $data['title'];
        $mc_data['controller'] = $data['controller'];
        $mc_data['logic'] = $data['logic'];
        $mc_data['model'] = $data['model'];
        $mc_data['validate'] = $data['validate'];
        $mc_data['ucwords_module'] = ucwords($data['module']);
        $mc_data['ucwords_name'] = ucwords(strtolower($mc_data['upper_name']));


        //替换配置项
        foreach ($mc_data as $name => $value) {

            $controller = str_replace("[$name]", $value, $controller);
            $logic = str_replace("[$name]", $value, $logic);
            $model = str_replace("[$name]", $value, $model);
            $validate = str_replace("[$name]", $value, $validate);


            $admin_controller = str_replace("[$name]", $value, $admin_controller);
            $view_list = str_replace("[$name]", $value, $view_list);
        }
        $validate = str_replace("[upper_name]", $mc_data['upper_name'], $validate);

        file_put_contents($url_prefix . $data['controller'] . '/controller/' . $mc_data['ucwords_name'] . '.php', $controller);
        file_put_contents($url_prefix . $data['logic'] . '/logic/' . $mc_data['upper_name'] . '.php', $logic);
        file_put_contents($url_prefix . $data['model'] . '/model/' . $mc_data['upper_name'] . '.php', $model);
        file_put_contents($url_prefix . $data['validate'] . '/validate/' . $mc_data['upper_name'] . '.php', $validate);

        if (!empty($data['is_admin'])) {

            $view_url = '../app/admin/view/' . strtolower($mc_data['upper_name']);
            dirs_mk($view_url, $mode = 0777);            //生成view文件夹
            file_put_contents('../app/admin/controller/' . $mc_data['ucwords_name'] . '.php', $admin_controller);
            file_put_contents($view_url . '/' . $mc_data['lower_name'] . '_edit.html', $view_edit);
            file_put_contents($view_url . '/' . $mc_data['lower_name'] . '_list.html', $view_list);

            if (!empty($data['pid'])) {
                $menu_upper_name = strtolower($mc_data['upper_name']) . SYS_DS_PROS . strtolower($mc_data['upper_name']);
                $menu['name'] = $data['title'] . '列表';
                $menu['sort'] = 0;
                $menu['pid'] = $data['pid'];
                $menu['is_hide'] = 0;
                $menu['icon'] = 'fa-th-list';
                $menu['module'] = 'admin';
                $menu['url'] = $menu_upper_name . 'list';
                $pid = $this->logicMenu->menuPureAdd($menu);
                $menu['pid'] = $pid;
                $menu['is_hide'] = 1;
                $menu['icon'] = 'fa-plus';
                $menu['url'] = $menu_upper_name . 'add';
                $this->logicMenu->menuPureAdd($menu);
                $menu['icon'] = 'fa-edit';
                $menu['url'] = $menu_upper_name . 'edit';
                $this->logicMenu->menuPureAdd($menu);
                $menu['icon'] = 'fa-trash-o';
                $menu['url'] = $menu_upper_name . 'del';
                $this->logicMenu->menuPureAdd($menu);
            }
            unset($data['pid']);
            unset($data['is_admin']);
        }


        unset($data['controller']);
        unset($data['logic']);
        unset($data['model']);
        unset($data['validate']);
        $data['thead'] = implode(",", $data['thead']);
        $data['field'] = implode(",", $data['field']);
        $data['type'] = implode(",", $data['type']);
        $data['length'] = implode(",", $data['length']);
        $data['default'] = implode(",", $data['default']);

        $result = $this->modelAutomation->setInfo($data);

        $result && action_log('新增', '新增模块，id：' . $data['name']);

        return $result ? [RESULT_SUCCESS, '新增模块成功', url('automationList')] : [RESULT_ERROR, $this->modelAutomation->getError()];
    }
}
