<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 自动生成模块控制器
 */
class Automation extends AdminBase
{
    
    /**
     * 自动生成模块列表
     */
    public function automationList()
    {
        
        $this->assign('list', $this->logicAutomation->getAutomationList([]));
        
        return $this->fetch('automation_list');
    }

    /**
     * 自动生成模块新增
     */
    public function automationAdd()
    {

        IS_POST && $this->jump($this->logicAutomation->automationAdd($this->param));

        $menu_select = $this->logicMenu->menuToSelect($this->authMenuTree);

        $this->assign('menu_select', $menu_select);
        return $this->fetch('automation_edit');
    }
    /**
     * api生成模块新增
     */
    public function automationApiAdd()
    {

        IS_POST && $this->jump($this->logicAutomation->automationApiAdd($this->param));

        $menu_select = $this->logicMenu->menuToSelect($this->authMenuTree);

        $this->assign('menu_select', $menu_select);

        return $this->fetch('automation_api_edit');
    }
    /**
     * 自动生成模块删除
     */
    public function automationDel($id = 0)
    {
        
        $this->jump($this->logicAutomation->automationDel(['id' => $id]));
    }
  
    /**
     * 自动生成模块清空
     */
    public function logClean()
    {
        
        $this->jump($this->logicAutomation->automationDel([DATA_STATUS_NAME => DATA_NORMAL]));
    }
}
