<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

use astrology\SweTestEmpty as SweTestEmpty;

/**
 * 单盘数据生成
 */
class Composite extends LogicBase
{

    /**
     * 获取站点导航搜索条件
     */
    public function plateData($param)
    {
        if (!empty($param['phase'])) {
            foreach ($param['phase'] as $key => $value) {
                $allow_degree[$key] = $value;
            }
        }

        $user_list = $param['user_list'];
        if (empty($allow_degree)) {
            $allow_degree['0'] = 5;
            $allow_degree['30'] = 5;
            $allow_degree['45'] = 5;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['180'] = 5;
        }

        $starsCode = $param['planets'];

        $planets = implode('', $starsCode);

        if (!empty($param['virtual'])) {
            $starsCode['virtual'] = $param['virtual'];
        }

        if (!empty($param['planet_xs']) and !empty($param['planet_xs'][0])) {
            $starsCode['planet_xs'] = $param['planet_xs'];
        }
        if (!empty($param['planet_xf']) and !empty($param['planet_xf'][0])) {
            $starsCode['planet_xf'] = $param['planet_xf'];
        }
        $ay = false;
        if (isset($param['ay'])) {
            $ay = $param['ay'];
        }
        $exSweTestEmpty = get_sington_object('exSweTestEmpty', SweTestEmpty::class);
        foreach ($user_list as $keyu => $valueu) {

            $birthdayToTime = strtotime($valueu['birthday']) - $valueu['tz'] * 3600;

            $utdatenow = date('d.m.Y', $birthdayToTime);

            $utnow = date('H:i:s', $birthdayToTime);

            $house = $valueu['longitude'] . ',' . $valueu['latitude'] . ',' . $param['h_sys'];

            $arr = [
                'b' => $utdatenow,
                'p' => $planets,
                'house' => $house,
                'ut' => $utnow,
                'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
                'g' => ',',
                'head',
                'roundsec',

            ];
            if ($keyu == 0) {
                $regst_one = $exSweTestEmpty->SweTest($arr, $starsCode);

            }
            if ($keyu == 1) {
                $regst_second = $exSweTestEmpty->SweTest($arr, $starsCode);
            }

        }

        $p_count = 9999;
        $regst_one_array = array();
        $regst_second_array = array();

        foreach ($regst_one as $key => $value) {
            $regst_one_array[] = explode(',', str_replace(' ', '', $value));

            $regst_second_array[] = explode(',', str_replace(' ', '', $regst_second[$key]));

        }
        foreach ($regst_one_array as $key => $value) {
            if ($value[0] == 'house1') {
                $p_count = $key;
            }
            if ($p_count <= $key and ($p_count + 12) > $key) {

                if (($key - $p_count) >= 9 and ($key - $p_count) < 12) {

                    $regst_one[$key - 9] = str_replace($regst_one_array[$key - 9][1], $value[1], $regst_one[$key - 9]);
                    $regst_second[$key - 9] = str_replace($regst_second_array[$key - 9][1], $regst_second_array[$key][1], $regst_second[$key - 9]);
                }
                if (($key - $p_count) < 9) {

                    $regst_one[$key + 3] = str_replace($regst_one_array[$key + 3][1], $value[1], $regst_one[$key + 3]);
                    $regst_second[$key + 3] = str_replace($regst_second_array[$key + 3][1], $regst_second_array[$key][1], $regst_second[$key + 3]);
                }
            }
        }

        foreach ($regst_one as $key => $value) {
            $lineInfo = explode(',', str_replace(' ', '', $value));
            if (!empty($regst_second[$key])) {
                $value_second = explode(',', str_replace(' ', '', $regst_second[$key]));

                $longitude1 = (float)$lineInfo[1];
                $longitude2 = (float)$value_second[1];

                $true_average = ($longitude1 + $longitude2) / 2;

                $diff = abs($longitude1 - $longitude2);

                if ($lineInfo[0] == 'house1') {
                    $p_count = $key;
                }
                if ($p_count <= $key and ($p_count + 12) > $key) {


                    if (abs($true_average - $longitude1) > 90 Or abs($true_average - $longitude2) > 90)
                    {
                        $true_average = $true_average + 180;
                    }
                    if ($true_average >= 360)
                    {
                        $true_average -= 360;
                    }

                    if((($key-$p_count)>=2)){

                        $lineInfoqian = explode(',', str_replace(' ', '', $regst_one[$key - 1]));
                        if (abs($true_average - $lineInfoqian[1]) > 90 And abs($true_average - $lineInfoqian[1]) < 270)
                        {

                            $true_average+=180;
                            if ($true_average >= 0)
                            {
                                $true_average = $true_average - floor($true_average / 360) * 360;
                            }
                            else
                            {
                                $true_average = 360 + ($true_average - ((1 + floor($true_average / 360)) * 360));
                            }
                        }
                    }
                } else {

                    if ($diff >= 180 And Abs($true_average - $longitude1) > 90 And Abs($true_average - $longitude2) > 90) {
                        $true_average = $true_average + 180;
                    }
                }

                $true_average = $exSweTestEmpty->crunch($true_average);

                $regst_one[$key] = str_replace($lineInfo[1], $true_average, $value);
            }
        }

        $regst_one_array = array();
        $regst_second_array = array();
        foreach ($regst_one as $key => $value) {
            $regst_one_array[] = explode(',', str_replace(' ', '', $value));

            $regst_second_array[] = explode(',', str_replace(' ', '', $regst_second[$key]));
        }
        foreach ($regst_one_array as $key => $value) {
            if ($value[0] == 'house1') {
                $p_count = $key;
            }
            if ($p_count <= $key and ($p_count + 12) > $key) {
                if (($key - $p_count) < 9) {
                    $regst_one[$key] = str_replace($regst_one_array[$key][1], $regst_one_array[$key + 3][1], $regst_one[$key]);
                    $regst_second[$key] = str_replace($regst_second_array[$key][1], $regst_second_array[$key + 3][1], $regst_second[$key]);
                }
                if (($key - $p_count) >= 9 and ($key - $p_count) < 12) {
                    $regst_one[$key] = str_replace($regst_one_array[$key][1], $regst_one_array[$key - 9][1], $regst_one[$key]);
                    $regst_second[$key] = str_replace($regst_second_array[$key][1], $regst_second_array[$key - 9][1], $regst_second[$key]);
                }
            }
        }

        $regst_one_array=array();
        foreach ($regst_one as $key => $value) {
            $regst_one_array[] = explode(',', str_replace(' ', '', $value));

        }
        foreach ($regst_one_array as $key => $value) {

            if ($value[0] == 'MC') {
                $regst_one[$key] = str_replace($value[1], $regst_one_array[$p_count+9][1], $regst_one[$key]);
            }
            if ($value[0] == 'Des') {
                $regst_one[$key] = str_replace($value[1], $regst_one_array[$p_count+6][1], $regst_one[$key]);
            }
            if ($value[0] == 'IC') {
                $regst_one[$key] = str_replace($value[1], $regst_one_array[$p_count+3][1], $regst_one[$key]);
            }
            if ($value[0] == 'Ascendant') {
                $regst_one[$key] = str_replace($value[1], $regst_one_array[$p_count][1], $regst_one[$key]);
            }
        }

        $data = $exSweTestEmpty->calculate($arr, $starsCode, $regst_one);

        $data['is_corpus'] = 1;
        if (empty($param['is_corpus'])) {
            $data['is_corpus'] = 0;
        }

        if (!isset($param['format'])) {
            $param['format'] = 1;
        }
        $planets_data['user'] = $param;
        $planets_data['house'] = $this->logicNatal->housePlanet($exSweTestEmpty, $data, $param['format']);
        $sign_attribute = $this->logicNatal->signPlanet($data);
        $planets_data['sign'] = $sign_attribute['sign'];
        $planets_data['planet'] = $this->logicNatal->planetPhase($exSweTestEmpty, $data, $allow_degree);


        if (!empty($data['is_corpus']) and !empty($data['corpus_where'])) {
            $corpus_array_where = $data['corpus_where'];
            $corpus_list = array();
            foreach ($corpus_array_where as $keys => $values) {
                $corpusConstellationWhere['chartType'] = 2;
                $corpusConstellationWhere['type'] = $values['type'];
                $corpusConstellationWhere['oneself'] = $values['oneself'];
                $corpusConstellationWhere['other'] = $values['other'];
                $corpusConstellationWhere['degree'] = $values['keyAd'];
                $corpus_info = $this->logicCorpusConstellation->getCorpusConstellationInfo($corpusConstellationWhere, 'oneself,other,type,chartType,degree,keywords,content', '', false);

                if (!empty($corpus_info)) {
                    $corpus_list[] = $corpus_info;
                }

            }
            $planets_data['corpus_list'] = $corpus_list;
        }


        if (!empty($param['svg_type']) and $param['svg_type'] == 1) {
            $planets_data['svg'] = $this->logicNatal->simpleSvg($planets_data, $data);
        } else if (!empty($param['svg_type']) and $param['svg_type'] == -1) {

        } else {
            $planets_data['svg'] = $this->logicNatal->seniorSvg($planets_data, $data);
        }

        $planets_data['attribute'] = $sign_attribute['attribute'];

        return $planets_data;
    }


}
