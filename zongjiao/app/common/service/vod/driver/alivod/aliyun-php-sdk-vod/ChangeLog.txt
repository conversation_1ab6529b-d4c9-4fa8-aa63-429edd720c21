2018-10-11 Version: 2.11.6
1, Add a new api called DeleteMezzanines to clear mezzanine infos and storages.
2, Add the field called PlayConfig to GetVideoPlayAuth and GetPlayInfo api request.
3, Add a new api called UpdateImageInfos to update image information.

2018-08-17 Version: 2.11.5
1, Add a new api called DeleteImage to clear the image resource.
2, Add the field called AdditionType and OutputType to GetMezzanineInfo api request.
3, Add the field called OutputType to GetMezzanineInfo api response.
4, Add the field called CreationTime and ModificationTime to GetPlayInfo api response.

2018-08-04 Version: 2.11.4
1, Add a new api called SetAuditSecurityIp to set audit security ip.
2, Add a new api called ListAuditSecurityIp to query audit security ip list.
3, Add a new api called UploadMediaByURL to bulk upload media based on urls.
4, Add the field called StorageLocation and TemplateGroupId to GetVideoInfo api response.
5, Add the field called StorageLocation and TemplateGroupId to GetVideoInfos api response.
6, Add the field called OutputType and Status to GetPlayInfo api response.

2018-07-10 Version: 2.11.3
1, Add new apis called "CreateAudit","GetAuditHistory" and "GetAuditResult" which support auditing feature.
2, Add a new api called GetVideoInfos to bulk query video information.
3, Add a new api called UpdateVideoInfos to update video information.
4, Add a new api called SearchMedia to get video based on the specified filter.
5, Add a new api called ListSnapshots to get the snapshot data.
6, Add the field called VideoStreamList and AudioStreamList to GetMezzanineInfo api response.

2018-07-03 Version: 2.11.2
1, Add UserData as request parameter of ProduceEditingProjectVideo API

2018-06-22 Version: 2.11.1
1, Add the field named ResultType to GetPlayInfo api request.
2, Add the field named WatermarkId to GetPlayInfo api response.

2018-05-10 Version: 2.11.0
1, Add a new api named "SubmitPreprocessJobs", which supports the user to preprocess video.
2, Add the "CreationTime" field at the ListLiveRecordVideo api.
3, Add the "StorageLocation" field to support the user to set the source station.
4, Add the field named PreprocessStatus to GetMezzanineInfo, GetVideoInfo and GetPlayInfo api response.
5, Add the field named OutputType to GetPlayInfo api request.

2018-04-01 Version: 2.10.0
1, Add the DescribePlayTopVideos  API,  to describe the top video statistics of play data.
2, Add the DescribePlayUserAvg API, to describe the average statistics of play data.
3, Add the DescribePlayUserTotal API, to describe the total statistics of play data.
4, Add the DescribePlayVideoStatis API, to describe the single video statistics of play data.
5, Add the DescribeRefreshQuota API, to get quota of refreshing cache.
6, Add the DescribeRefreshTasks API, to get status of refreshing cache.
7, Add the PushObjectCache API, to summit a job of pushing objects to cdn cache.
8, Add the RefreshObjectCaches API, to refresh cdn cache.
9, Add the SubmitTranscodeJobs API, to support the user to submit transocde job base with VideoId, TemplateGroupId, EncryptConfig and so on.
10, Add the SubmitSnapshotJob API, to support the user to submit snapshot job base with VideoId, SpecifiedOffsetTime, Width, Height and so on.
11, Update the ProduceEditingProjectVideo API, add MediaMetadata field of request to set the produced media metadata, add ProduceConfig field of request to set some customized config options, such as TemplateGroupId and so on.

2017-12-21 Version: 2.9.0
1, Add a new api named "ListLiveRecordVideo", which supports the user to query the live recorded video list based on StreamName, DomainName, AppName and so on

2017-12-07 Version: 2.8.0
1, Added the ListAIJob API, a common api to query multiple types of AI job

2017-11-29 Version: 2.7.0
1, 获取视频播放地址接口GetPlayInfo, 增加按清晰度筛选媒体流地址。
2, 刷新视频上传凭证接口RefreshUploadVideo, 返回数据增加UploadAddress。
3, 获取视频上传凭证和地址接口CreateUploadVideo, 增加模板组ID(TemplateGroupId)参数, 支持用户自定义模板组功能。
4, 获取视频信息接口GetVideoInfo, 增加指定返回数据类型(ResultTypes)参数, 返回AI处理结果。
5, 新增视频分类相关接口, 包括提交视频分类作业SubmitAIASRJob和查询视频分类作业ListAIASRJob。 
6, 新增提交AI作业的通用接口SubmitAIJob, 增加5项新的AI能力(视频标注,视频文本识别,名人识别,政治人物识别敏感人物识别), 并支持针对单个资源一次提交多个AI Feature。
7, 获取图片上传地址和凭证接口CreateUploadImage, 输入参数增加Title(标题), Tags(标签), OriginalFileName(图片原始文件名)字段, 输出参数增加ImageId(图片Id)字段。
8, 新增获取图片信息接口GetImageInfo。

2017-11-03 Version: 2.6.0
1, 获取视频播放地址接口GetPlayInfo，增加二次鉴权参数，返回结构体PlayInfo中增加JobId。
2, 获取视频播放凭证接口GetVideoPlayAuth，增加二次鉴权参数，支持用户自定义AuthInfo过期时间。
3, 新增删除流信息接口DeleteStream，根据JobId删除某一路媒体流文件及信息。
4, 新增提交截图作业接口SubmitSnapshotJob。
5, 新增云剪辑工程及视频合成相关接口，创建云剪辑工程AddEditingProject、修改云剪辑工程UpdateEditingProject、合成成品视频ProduceEditingProjectVideo、设置云剪辑工程素材  SetEditingProjectMaterials、获取云剪辑工程素材GetEditingProjectMaterials、获取单个云剪辑工程GetEditingProject、搜索云剪辑工程SearchEditingProject、删除云剪辑工程DeleteEditingProject。
6, 新增点播cdn资源监控及日志下载相关接口，日志信息－下载域名日志​DescribeCdnDomainLogs、资源监控－查询网络带宽DescribeDomainBpsData、资源监控－查询流量数据DescribeDomainFlowData。
7, 新增视频暴恐涉政鉴别(AI)相关接口，提交视频暴恐涉政鉴别作业SubmitAIVideoTerrorismRecogJob、查询视频暴恐涉政鉴别作业ListAIVideoTerrorismRecogJob。
8, 新增视频内容审核(AI)相关接口，提交视频内容审核作业SubmitAIVideoCensorJob、查询视频内容审核作业ListAIVideoCensorJob​​。

2017-10-15 Version: 2.5.0
1, 新增语音识别文本相关接口，包括提交语音识别作业SubmitAIASRJob、查询语音识别作业ListAIASRJob
2, 新增视频摘要相关接口，包括提交视频摘要作业SubmitAIVideoSummaryJob、查询视频摘要作业ListAIVideoSummaryJob
3, 视频AI智能首图和视频鉴黄相关接口SubmitAIVideoCoverJob、ListAIVideoCoverJob、SubmitAIVideoPornRecogJob、ListAIVideoPornRecogJob, 返回结构体Job中，将Id字段重命名为JobId。
4, 获取视频播放地址接口GetPlayInfo，返回结构体PlayInfo中, 增加流类型StreamType, 如果输出为视频流, 取值: video, 输出为音频流, 取值: audio。

2017-09-20 Version: 2.4.0
1, 增加了获取源文件地址接口GetMezzanineInfo接口。
2, 提交视频智能首图服务SubmitAIVideoCoverJob：请求参数增加MediaId字段，废弃Input字段（之前尚无使用），将原Input字段放在AIVideoCoverConfig之下 ；返回值中的Job参数增加MediaId字段。
3, 查询视频智能首图服务ListAIVideoCoverJob：返回参数的层级调整：AIVideoCoverJobList作为数组返回，返回值中的Job参数增加MediaId字段。
4, ​提交视频鉴黄服务SubmitAIVideoPornRecogJob：接口的请求参数，VideoId字段改名为MediaId；返回值中的Job参数增加MediaId字段。
5, 查询视频鉴黄服务ListAIVideoPornRecogJob：返回值中的Job参数增加MediaId字段。 

2017-09-01 Version: 2.3.1
1, 增加了提交视频智能首图作业SubmitAIVideoCoverJob接口。
2, 增加了获取视频智能首图作业ListAIVideoCoverJob接口。
3, 提交视频智能鉴黄作业SubmitAIVideoPornRecogJob接口。
4, 获取视频智能鉴黄作业ListAIVideoPornRecogJob接口。
5, 获取视频上传地址和凭证CreateUploadVideo接口增加TranscodeMode和UserData参数。

