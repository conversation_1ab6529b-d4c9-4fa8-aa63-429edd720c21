<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiwap\logic;

/**
 * 商城地址逻辑
 */
class ShopAddress extends IndexBase
{
    /**
     * 地址条件查询
     */
    public function getWhere($data = [])
    {

        return $data;
    }
    /**
     * 地址获取单个
     */
    public function getaddressInfo($where = [], $field = true)
    {
        return $this->modelShopAddress->getInfo($where, $field);

    }

    /**
     * 地址获取列表
     */
    public function getAddressList($where = [], $field = true, $order = '', $paginate =false)
    {
        return $this->modelShopAddress->getList($where, $field, $order, $paginate);
    }
    /**
     * 地址增加
     */
    public function addressAdd($data)
    {

        $url = url('myAddress');

        $validate_result = $this->validateShopAddress->scene('add')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateShopAddress->getError()]; endif;

        $data['user_id']  = session('user_auth')['user_id'];

        $result = $this->modelShopAddress->setInfo($data);

        return $result ? [RESULT_SUCCESS, '收货地址添加成功', $url] : [RESULT_ERROR, $this->modelShopAddress->getError()];
    }
    /**
     * 地址修改
     */
    public function addressEdit($data)
    {
        $url = url('myAddress');

        $validate_result = $this->validateShopAddress->scene('add')->check($data);

        if (!$validate_result) : return [RESULT_ERROR, $this->validateShopAddress->getError()]; endif;

        $data['user_id']  = session('user_auth')['user_id'];

        $result = $this->modelShopAddress->setInfo($data);;

        return $result ? [RESULT_SUCCESS, '收货地址修改成功', $url] : [RESULT_ERROR, $this->modelShopAddress->getError()];
    }
    /**
     * 地址删除
     */
    public function addressDel($where)
    {
        $url = url('myAddress');

        $result = $this->modelShopAddress->deleteInfo($where);

        return $result ? [RESULT_SUCCESS, '收货地址删除成功', $url] : [RESULT_ERROR, $this->modelShopAddress->getError(), $url];
    }
    /**
     * 地址默认设置
     */
    public function addressSet($where = [], $field = '', $value = '')
    {
        $url = url('myAddress');
        $result=$this->modelShopAddress->setFieldValue($where, $field, $value);
        $result=$this->modelShopAddress->setFieldValue(['id'=>array('neq', $where['id'])], $field, 0);
        return $result ? [RESULT_SUCCESS, '设置成功', $url] : [RESULT_ERROR, $this->modelShopAddress->getError()];
    }
}
