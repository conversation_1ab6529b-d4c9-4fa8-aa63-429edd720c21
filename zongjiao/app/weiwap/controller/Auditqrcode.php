<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\weiwap\controller;
use exwechat\api\OAuth\OAuth;
use exwechat\api\account\QRCode as QRCodeEx;

/**
 * 场景二维码控制器
 */
class Auditqrcode extends Oauthbase
{

    public function index()
    {
        $weiUserInfo=$this->logicWeiUser->getWeiUserInfo(['openid'=>session('openid')],'level');

        $data['audit']=0;
        if($weiUserInfo['level']>=69){
            if(!empty($this->param['order_status'])){
                $this->logicActivityJoin->activityJoinWeiUpdate(['id'=>$this->param['activityJion']],['order_status'=>3]);
            }
            $data=$this->logicActivityJoin->getActivityJoinInfo(['id'=>$this->param['activityJion']],['order_status'=>1]);
            $data['order_str']=config('ext_activity.activityType')[$data['order_status']];
            $data['url']=URL_TRUE;
            if(!empty($data['ukid'])){
                $userKanjia = $this->logicUserKanjia->getUserKanjiaInfo(['id'=>$data['ukid'],'for_id'=>$data['activity_id'],'user_id'=>$data['user_id']]);
                $this->assign('userKanjia',$userKanjia);
            }
            $data['audit']=1;
        }
        $this->assign('info',$data);
        $this->view->engine->layout(false);
        return $this->fetch('index');
    }
}
