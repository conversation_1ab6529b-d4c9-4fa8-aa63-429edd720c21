<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<title>我的推广二维码</title>
	<link rel="stylesheet" href="__STATIC__/css/main.css">
	<script src="__STATIC__/js/meta.js"></script>
	<link rel="stylesheet" href="__STATIC__/css/app.css">
	<script src="__STATIC__/js/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="__STATIC__/js/qrcode.js"></script>
	<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
	<style>
		
		.erweima {
			position: absolute;
			right: 100px;
			top: 550px;
		}
		
		.yqm_p {
			position: absolute;
			-webkit-user-select: none;
			user-select: none;
			left: 50%;
			margin-left: -150px;
			top: 1080px;
			display: block;
			z-index: 3;
			width: 300px;
			height: 90px;
			border-radius: 90px;
			text-align: center;
			border: none;
			font-size: 23px;
		}
	</style>
</head>

<body>
<div style="height: .75rem;"></div>
<!-- 头部 -->
<div class="ziye_toub">
	<a href="http://sp.liyipeixunwang.com/#/pages/personcenter/personcenter"><div class="left"></div></a>
	<p>我的推广</p>
	<a href="http://sp.liyipeixunwang.com/#/pages/personcenter/avirecord" class="zhanghu_Xq">邀请记录</a>
</div>



<div id="qrcode" style="width:300px; height:300px; display: none" id="erweima" class="erweima"></div>
<!--<p class="yqm_p">我的邀请码{$userInfo.invite}</p>-->
<div class="Wrap">
	
	<canvas id="myCanvas"></canvas>

</div>


<span class="yqm_p" style="">长按图片，保存或发送给朋友</span>


</body>
</html>
<script type="text/javascript">
    $(document).ready(function () {
        //分享管理
        var shareData = {
            title: "礼仪学习", // 分享标题
            desc: "送您大礼包", // 分享描述
            link: "{$share.url}", // 分享链接
            imgUrl: "https://dfly.codecore.club/static/module/weiwap/default/images/center_img1.png", // 分享图标
            // type: '', // 分享类型,music、video或link，不填默认为link
            // dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
            success: function (msg) {
                $("#mcover").hide();
                // 用户确认分享后执行的回调函数
            },
            cancel: function (msg) {
                // alert('no')
                // 用户取消分享后执行的回调函数
            }
        };

        wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: "{$share.appid|default=0}", // 必填，公众号的唯一标识
            timestamp: "{$share.timestamp|default=0}", // 必填，生成签名的时间戳
            nonceStr: "abcdefghijklmnopqrstu", // 必填，生成签名的随机串
            signature: "{$share.signature|default=0}",// 必填，签名，见附录1
            jsApiList: ['onMenuShareAppMessage', 'onMenuShareTimeline', 'onMenuShareQQ'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
        });
        wx.ready(function () {
            wx.onMenuShareAppMessage(shareData);//微信朋友分享
            wx.onMenuShareTimeline(shareData);
            //qq分享
            wx.onMenuShareQQ(shareData);
        });
        
        
        
        
        
        var qrcode = new QRCode(document.getElementById("qrcode"), {
            width: 300,
            height: 300
        });

        qrcode.makeCode("https://sp.liyipeixunwang.com/?invite_pid={$userInfo.invite}");


        //从 canvas 提取图片 image
        function convertCanvasToImage(canvas) {
            //新Image对象，可以理解为DOM
            var image = new Image();
            // canvas.toDataURL 返回的是一串Base64编码的URL，当然,浏览器自己肯定支持
            // 指定格式 PNG
            image.src = canvas.toDataURL("image/png");
            return image;
        }

        var mycanvas1 = document.getElementsByTagName('canvas')[0]; //获取网页中的canvas对象
        //将转换后的img标签插入到html中
        var img = convertCanvasToImage(mycanvas1);
        $('#qrcode').html("");//移除已生成的避免重复生成
        $('#qrcode').append(img);//imagQrDiv表示你要插入的容器id
        $('#qrcode img').attr("id", "tulip");
        var canvas = document.getElementById("myCanvas");
        var ctx = canvas.getContext("2d");
        //图片
        var img = new Image();
        img.src = '__STATIC__/images/yqmbg.png';
        canvas.width = 750;
        canvas.height = 1330;
        var ewm = document.getElementById("tulip");
        img.crossOrigin = "*";
        img.onload = function () { //必须等待图片加载完成
            ctx.drawImage(img, 0, 0, 750, 949); //绘制图像进行拉伸
            ctx.drawImage(ewm, 30, 750, 160, 160);
            ctx.fillStyle = "#000";
            ctx.font = "30px Microsoft YaHei"
            ctx.fillText("{$userInfo.nickName}",220, 790);
            var srcImg = new Image();
            srcImg.src = canvas.toDataURL('images/png');
            $('.Wrap').html("");//移除已生成的避免重复生成
            $('.Wrap').append(srcImg);//imagQrDiv表示你要插入的容器id
            $('.Wrap img').attr("id", "testImg");
            $('#qrcode').hide();
        }
    });

</script>
