<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\fkhp\controller;

/**
 * 界面点击统计控制器
 */
class Chart extends UserBase
{
//星盘中心数据
    public function default()
    {
        $param_data = $this->param_data;
        $archives_id = explode(",",$param_data['archives_id']);

        $user_id = $param_data['user_id'];
        $config = json_decode(html_entity_decode('{"planets":[0,1,2,3,4,5,6,7,8,9,"m"],"h_sys":"P","ay":-1,"svg_type":1,"virtual":[10,11],"phase":{"0":7,"60":3,"90":6,"120":5,"180":7}}'), true);

        if(!isset($config['format'])){
            $config['format']=1;
        }
        $Archives = $this->logicUserArchives->getUserArchivesInfo(['user_id' => $user_id, 'id' => ['in', $archives_id]]);

        if (empty($Archives)) {
            $config['birth_points'] = '121.47,31.23';
            $config['birthday'] = '1996-06-15 12:30';
            $config['time_zone'] = 8;
            $config['is_summer'] = 0;
        }else {
            $config['birth_points'] = $Archives['birth_points'];
            $config['birthday'] = $Archives['birthday'];
            $config['time_zone'] = $Archives['time_zone'];
            $config['is_summer'] = $Archives['is_summer'];
        }

        $config['svg_type']=0;
        if(!empty($param_data['is_corpus'])){
            $config['is_corpus']=$param_data['is_corpus'];
        }

        !empty($param_data['svg_type']) && $config['svg_type'] = $param_data['svg_type'];
        $config['is_corpus']=-1;
        $data = $this->chartNatal->plateData($config);

        $data['Archives'] = $Archives;
        return $this->apiReturn($data);
    }
    //星盘中心数据
    public function planet()
    {
        $param_data = $this->param_data;
        $archives_id = explode(",",$param_data['archives_id']);

        $type = $param_data['type'];
        $user_id = $param_data['user_id'];
        $config = json_decode(html_entity_decode($param_data['config']), true);

        $config['chart_type']=$type;

        if(!isset($config['format'])){
            $config['format']=1;
        }

        $infoData = $this->logicUser->getUserInfo(['id'=>$user_id], 'id,mobile,t_id');

        $Archives_where['id']=['in', $archives_id];
        if($infoData['t_id']==0){
            $Archives_where['user_id']=$user_id;
        }

        $Archives = $this->logicUserArchives->getUserArchivesColumn($Archives_where);

        if (empty($Archives)) {
            return $this->apiReturn(['code' => 1252054, 'msg' => '档案资料不完善']);
        }
        if (count($archives_id) > 1) {
            $config['user_list'][0]=$Archives[$archives_id[0]];
            $config['user_list'][1]=$Archives[$archives_id[1]];
        } else {

            $config['birth_points'] = $Archives[$archives_id[0]]['birth_points'];
            $config['birthday'] = $Archives[$archives_id[0]]['birthday'];
            $config['time_zone'] = $Archives[$archives_id[0]]['time_zone'];
            $config['is_summer'] = $Archives[$archives_id[0]]['is_summer'];
        }

        $config['svg_type']=0;
        if(!empty($param_data['is_corpus'])){
            $config['is_corpus']=$param_data['is_corpus'];
        }

        !empty($param_data['svg_type']) && $config['svg_type'] = $param_data['svg_type'];

        $data = $this->$type($config);
        $data['Archives'] = $Archives;
        return $this->apiReturn($data);
    }

    //天象盘
    public function current($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['birthday']=$this->param_data['current_date'];
        }
        $param['is_corpus']=-1;
        $data = $this->chartNatal->plateData($param);

        return $data;
    }

    //单盘
    public function natal($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['birthday']=$this->param_data['current_date'];
        }
        $data = $this->chartNatal->plateData($param);
//        $data['corpus_list'][] = ['title' => '热情第一',
//            'phase_str' =>  '热情第一热情第一热情第一热情第一热情第一',
//            'start_date' => 3,
//            'end_date' => 6,
//            'phase_situation' => '已经开始3天，6天后结束',
//            'content' => '热情第一热情第一热情第一热情第一热情第一热情第一热情第一'];
        return $data;
    }

    //行运盘
    public function transit($param)
    {

        $user_list[]=['birthday'=>$param['birthday'],'birth_points'=>$param['birth_points'],'time_zone'=>$param['time_zone'],'is_summer'=>$param['is_summer']];
        $user_list[]=['birthday'=>$this->param_data['current_date'],'birth_points'=>$param['birth_points'],'time_zone'=>$param['time_zone'],'is_summer'=>$param['is_summer']];
        $param['user_list']=$user_list;
        unset($param['birthday']);
        unset($param['birth_points']);
        unset($param['time_zone']);
        unset($param['is_summer']);
        $data = $this->chartTransit->plateData($param);

        $data['interpretation']['content']=$this->explainData($param['chart_type']);
        return $data;
    }

    //次限
    public function secondaryLimit($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $data = $this->chartSecondaryLimit->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);

        return $data;
    }

    //次限比
    public function secondaryLimitDouble($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $data = $this->chartSecondaryLimitDouble->plateData($param);
//        $data['interpretation']  = ['title' => '信心满满', 'phase_str' => '太阳1宫','planet_id'=>1,'sign_id'=>1, 'content' => '非常好，非常好非常好非常好非常好非常好非常好非常好非常好非常好'];
//        $data['corpus_list'][] = ['title' => '热情第一',
//            'phase_str' =>  '热情第一热情第一热情第一热情第一热情第一',
//            'start_date' => 3,
//            'end_date' => 6,
//            'phase_situation' => '已经开始3天，6天后结束',
//            'content' => '热情第一热情第一热情第一热情第一热情第一热情第一热情第一'];
        return $data;
    }

    //三限
    public function thirdProgressed($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $data = $this->chartThirdProgressed->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);


//        $data['interpretation']  = ['title' => '信心满满', 'phase_str' => '太阳1宫','planet_id'=>1,'sign_id'=>1, 'content' => '非常好，非常好非常好非常好非常好非常好非常好非常好非常好非常好'];
//        $data['corpus_list'][] = ['title' => '热情第一',
//            'phase_str' =>  '热情第一热情第一热情第一热情第一热情第一',
//            'start_date' => 3,
//            'end_date' => 6,
//            'phase_situation' => '已经开始3天，6天后结束',
//            'content' => '热情第一热情第一热情第一热情第一热情第一热情第一热情第一'];
        return $data;
    }

    //三限比
    public function thirdProgressedDouble($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $data = $this->chartThirdProgressedDouble->plateData($param);
//        $data['interpretation']  = ['title' => '信心满满', 'phase_str' => '太阳1宫','planet_id'=>1,'sign_id'=>1, 'content' => '非常好，非常好非常好非常好非常好非常好非常好非常好非常好非常好'];
//        $data['corpus_list'][] = ['title' => '热情第一',
//            'phase_str' =>  '热情第一热情第一热情第一热情第一热情第一',
//            'start_date' => 3,
//            'end_date' => 6,
//            'phase_situation' => '已经开始3天，6天后结束',
//            'content' => '热情第一热情第一热情第一热情第一热情第一热情第一热情第一'];
        return $data;
    }

    //发达盘
    public function developed($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['current_date']=$this->param_data['current_date'];
        }
        $param['is_corpus']=1;
        $data = $this->chartDeveloped->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']).'\n\n '.$data['interpretation']['content'];

        return $data;
    }

    //日返盘
    public function solarreturn($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['current_date']=$this->param_data['current_date'];
        }
        $data = $this->chartSolarreTurn->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);
        return $data;
    }

    //日返比盘
    public function solarreturnDouble($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['current_date']=$this->param_data['current_date'];
        }
        $param['is_corpus']=0;
        $data = $this->chartSolarreTurnDouble->plateData($param);

        return $data;
    }

    //月返盘
    public function lunarreturn($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['current_date']=$this->param_data['current_date'];
        }
        $data = $this->chartLunarreTurn->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);

        return $data;
    }

    //月返比盘
    public function lunarreturnDouble($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['current_date']=$this->param_data['current_date'];
        }
        $data = $this->chartLunarreTurnDouble->plateData($param);

        return $data;
    }

    //日弧盘
    public function solararc($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['current_date']=$this->param_data['current_date'];
        }
        $data = $this->chartSolarArc->plateData($param);

        return $data;
    }

    /**
     * 双人数据
     */
    //比较盘
    public function comparision($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['user_list'][1]['birthday']=$this->param_data['current_date'];
        }

        $data = $this->chartComparision->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);

        return $data;
    }

    //组合盘
    public function composite($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['user_list'][1]['birthday']=$this->param_data['current_date'];
        }
        $data = $this->chartComposite->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);

        return $data;
    }
    //时空中点盘
    public function timesMidPoint($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['user_list'][1]['birthday']=$this->param_data['current_date'];
        }

        $one_birth_points=explode(",",$param['user_list'][0]['birth_points']);
        $two_birth_points=explode(",",$param['user_list'][1]['birth_points']);

        $longitude = ($one_birth_points[0]+$two_birth_points[0])/2;
        $latitude = ($one_birth_points[1]+$two_birth_points[1])/2;

        $param['birth_points'] =$longitude.','.$latitude;
        $param['time_zone'] =($param['user_list'][0]['time_zone']+$param['user_list'][1]['time_zone'])/2;
        $param['is_summer'] =($param['user_list'][0]['is_summer']+$param['user_list'][1]['is_summer'])/2;
        $param['birthday'] = date('Y-m-d H:i:s', (strtotime($param['user_list'][0]['birthday'])+strtotime($param['user_list'][1]['birthday']))/2);
        unset($param['user_list']);

        $data = $this->chartTimesMidPoint->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);

        return $data;
    }

    //马克思盘
    public function marks($param)
    {
        if(!empty($this->param_data['current_date'])){
            $param['user_list'][1]['birthday']=$this->param_data['current_date'];
        }

        $one_birth_points=explode(",",$param['user_list'][0]['birth_points']);
        $two_birth_points=explode(",",$param['user_list'][1]['birth_points']);

        $midpoint_longitude  = ($one_birth_points[0]+$two_birth_points[0])/2;
        $midpoint_latitude =($one_birth_points[1]+$two_birth_points[1])/2;
        $midpoint_time_zone =($param['user_list'][0]['time_zone']+$param['user_list'][1]['time_zone'])/2;;
        $midpoint_is_summer =($param['user_list'][0]['is_summer']+$param['user_list'][1]['is_summer'])/2;;
        $midpoint_birthday= (strtotime($param['user_list'][0]['birthday'])+strtotime($param['user_list'][1]['birthday']))/2;

        $longitude = ($two_birth_points[0]+$midpoint_longitude)/2;
        $latitude = ($two_birth_points[1]+$midpoint_latitude)/2;
        $param['birth_points'] =$longitude.','.$latitude;
        $param['time_zone'] =($param['user_list'][1]['time_zone']+$midpoint_time_zone)/2;
        $param['is_summer'] =($param['user_list'][1]['is_summer']+$midpoint_is_summer)/2;
        $param['birthday'] = date('Y-m-d H:i:s', (strtotime($param['user_list'][1]['birthday'])+$midpoint_birthday)/2);

        unset($param['user_list']);
        $param['is_corpus']=1;
        $data = $this->chartMarks->plateData($param);
        $data['interpretation']['content']=$this->explainData($param['chart_type']);

        return $data;

    }

    //组合次限计算
    public function compositeSecprogr($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $param['is_corpus']=0;
        $data = $this->chartCompositeSecprogr->plateData($param);

        return $data;

    }

    //组合三限计算
    public function compositeThirprogr($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $param['is_corpus']=0;
        $data = $this->chartCompositeThirprogr->plateData($param);

        return $data;

    }


    //时空次限计算
    public function timesMidPointSecprogr($param)
    {

        $param['current_date']=$this->param_data['current_date'];

        $one_birth_points=explode(",",$param['user_list'][0]['birth_points']);
        $two_birth_points=explode(",",$param['user_list'][1]['birth_points']);

        $longitude = ($one_birth_points[0]+$two_birth_points[0])/2;
        $latitude = ($one_birth_points[1]+$two_birth_points[1])/2;

        $param['birth_points'] =$longitude.','.$latitude;
        $param['time_zone'] =($param['user_list'][0]['time_zone']+$param['user_list'][1]['time_zone'])/2;
        $param['is_summer'] =($param['user_list'][0]['is_summer']+$param['user_list'][1]['is_summer'])/2;
        $param['birthday'] = date('Y-m-d H:i:s', (strtotime($param['user_list'][0]['birthday'])+strtotime($param['user_list'][1]['birthday']))/2);
        unset($param['user_list']);
        $param['is_corpus']=0;
        $data = $this->chartSecondaryLimit->plateData($param);

        return $data;

    }


    //时空三限计算
    public function timesMidPointThirprogr($param)
    {
        $param['current_date']=$this->param_data['current_date'];

        $one_birth_points=explode(",",$param['user_list'][0]['birth_points']);
        $two_birth_points=explode(",",$param['user_list'][1]['birth_points']);

        $longitude = ($one_birth_points[0]+$two_birth_points[0])/2;
        $latitude = ($one_birth_points[1]+$two_birth_points[1])/2;

        $param['birth_points'] =$longitude.','.$latitude;
        $param['time_zone'] =($param['user_list'][0]['time_zone']+$param['user_list'][1]['time_zone'])/2;
        $param['is_summer'] =($param['user_list'][0]['is_summer']+$param['user_list'][1]['is_summer'])/2;
        $param['birthday'] = date('Y-m-d H:i:s', (strtotime($param['user_list'][0]['birthday'])+strtotime($param['user_list'][1]['birthday']))/2);
        unset($param['user_list']);
        $param['is_corpus']=0;
        $data = $this->chartThirdProgressed->plateData($param);
        return $data;

    }


    //马克次限计算
    public function marksSecprogr($param)
    {
        $param['current_date']=$this->param_data['current_date'];

        $one_birth_points=explode(",",$param['user_list'][0]['birth_points']);
        $two_birth_points=explode(",",$param['user_list'][1]['birth_points']);

        $midpoint_longitude  = ($one_birth_points[0]+$two_birth_points[0])/2;
        $midpoint_latitude =($one_birth_points[1]+$two_birth_points[1])/2;
        $midpoint_time_zone =($param['user_list'][0]['time_zone']+$param['user_list'][1]['time_zone'])/2;;
        $midpoint_is_summer =($param['user_list'][0]['is_summer']+$param['user_list'][1]['is_summer'])/2;;
        $midpoint_birthday= (strtotime($param['user_list'][0]['birthday'])+strtotime($param['user_list'][1]['birthday']))/2;

        $longitude = ($two_birth_points[0]+$midpoint_longitude)/2;
        $latitude = ($two_birth_points[1]+$midpoint_latitude)/2;
        $param['birth_points'] =$longitude.','.$latitude;
        $param['time_zone'] =($param['user_list'][1]['time_zone']+$midpoint_time_zone)/2;
        $param['is_summer'] =($param['user_list'][1]['is_summer']+$midpoint_is_summer)/2;
        $param['birthday'] = date('Y-m-d H:i:s', (strtotime($param['user_list'][1]['birthday'])+$midpoint_birthday)/2);

        unset($param['user_list']);
        $param['is_corpus']=0;
        $data = $this->chartSecondaryLimit->plateData($param);

        return $data;

    }

    //马克三限计算
    public function marksThirprogr($param)
    {
        $param['current_date']=$this->param_data['current_date'];

        $one_birth_points=explode(",",$param['user_list'][0]['birth_points']);
        $two_birth_points=explode(",",$param['user_list'][1]['birth_points']);

        $midpoint_longitude  = ($one_birth_points[0]+$two_birth_points[0])/2;
        $midpoint_latitude =($one_birth_points[1]+$two_birth_points[1])/2;
        $midpoint_time_zone =($param['user_list'][0]['time_zone']+$param['user_list'][1]['time_zone'])/2;;
        $midpoint_is_summer =($param['user_list'][0]['is_summer']+$param['user_list'][1]['is_summer'])/2;;
        $midpoint_birthday= (strtotime($param['user_list'][0]['birthday'])+strtotime($param['user_list'][1]['birthday']))/2;

        $longitude = ($two_birth_points[0]+$midpoint_longitude)/2;
        $latitude = ($two_birth_points[1]+$midpoint_latitude)/2;
        $param['birth_points'] =$longitude.','.$latitude;
        $param['time_zone'] =($param['user_list'][1]['time_zone']+$midpoint_time_zone)/2;
        $param['is_summer'] =($param['user_list'][1]['is_summer']+$midpoint_is_summer)/2;
        $param['birthday'] = date('Y-m-d H:i:s', (strtotime($param['user_list'][1]['birthday'])+$midpoint_birthday)/2);

        unset($param['user_list']);
        $param['is_corpus']=0;
        $data = $this->chartThirdProgressed->plateData($param);

        return $data;
    }

    //时空次限与时空比较计算
    public function timesMidPointSecprogrTimesDouble($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $param['is_corpus']=0;
        $data = $this->chartCompositeSecprogr->plateData($param);

        return $data;

    }

    //时空三限与时空比较计算
    public function timesMidPointThirprogrTimesDouble($param)
    {
        $param['current_date']=$this->param_data['current_date'];
        $param['is_corpus']=0;
        $data = $this->chartCompositeThirprogr->plateData($param);

        return $data;

    }


    public function explainData($chart_type='current_date'){
        $data['current']='天象盘反映的是你输入的时刻与地点，天上行星所在的星座、宫位和相位，天象盘通常用来分析天文事件，比如行星换星座，顺行逆行，通常我们看到的星座运势就是基于天象盘。';
        $data['natal']='';
        $data['transit']='行运盘，简单理解就是盘主的本命盘的基础宫位和星体在内圈，推运的星体在外圈，产生的一种新的星盘，而这个星盘主要用来推算个人运势。行运盘的好处是，可以推算出盘主在一个人为可控的时间变量内转动星盘进行的运势的推算。在这种变化的时间内看待相对应的运势，就会更加的直观和精准。';
        $data['secondaryLimit']='次限法是目前最为广泛运势推测技术，它是以一天代表一年。有两大功能：一是用来比较年与年运势的变化，二是通观人生大运的变化。';
        $data['secondaryLimitDouble']='';
        $data['thirdProgressed']='三限盘，它是以一天代表一月。有两大功能：一是看一月运势，二是推算具体事件在哪个时段发生。';
        $data['thirdProgressedDouble']='';
        $data['solarreturn']='太阳每年会在生日前后回到相同的位置，这时候我们可以利用这个时间点来判断未来一年的整体运。首先在星盘的制作上面我们先制作出生盘，然后利用出生盘找出今年太阳回到同一个度数的时刻绘制一张星盘。';
        $data['solarreturnDouble']='';
        $data['solararc']='太阳弧推运，使用出生一天等于一年的概念，约出生后太阳移动一度约等于人生一年，但只计算太阳移动的弧角度数，然后将此度数加之与本命行星和重要点，然后得到所有行星重要的位置。此办法基本概念为太阳为众星之首，太阳带着众星一起移动，太阳弧会维持与本命盘的相对距离。此方法虽非实际星体运动，但却是星动推运法中准确率最高，实用性最佳的推运方法，经常用作生时校正。';
        $data['lunarreturn']=' 月亮返照盘就是每个月，月亮回到出生位置时所绘制的星盘，暗示这个月的心理状态及需求，由行星之间的相位可以看出这个月的游戏规则，哪些人生游戏条件被设定了，要怎么照着规则玩等等。和太阳返照的不同处，除了影响时间的不同外，太阳返照显示的是重要的追求指标，而月亮返照则暗示我们毎月的情绪精神的变化，并提醒我们的需求在哪里。';
        $data['lunarreturnDouble']='';
        $data['developed']='  法达大运是古典占星中常用到的一种推运工具，它也是大运系统里面最简单使用起来最方便的一种。它跟我们以往接触的流年法或者太阳弧等推运法不同，它的重心不是看某个确切的时间点发生什么事，而是以宏观的视角把握运势起伏，看的是一种大趋势。';
        $data['comparision']='我们可以将本命盘想象一张宇宙地图，它是你出生时行星坐标的快照。现在，当我们在你们的宇宙地图上覆盖另一个个体的宇宙地图，我们可以直观地看到他们的行星在你们的关系中所处的位置-那是比较盘的基础。基本上它是叠加两个个人的出生星盘，并检查两个星盘之间的相位。从视觉上看，比较盘看起来就像星盘——一个360度的转轮分成12个部分。唯一的区别是绘制的元素数量是原来的两倍。在比较盘中，相位是两个不同星盘中行星之间的几何关系。相位包括刑相位、三分、合相以及更多。每个相位产生不同的意义。比较盘和组合盘的相似之处在于它们都探索关系。比较盘是用来检查两个人之间的关系，或者更具体地说，他们如何单独影响彼此。由此，占星家可以用星图来解释这种关系的特点和动态。';
        $data['composite']='想象一下，把两张出生盘重叠在一起，然后找到它们各自的太阳，然后在它们之间放一个新的太阳——这就是组合盘的基本方法。在更正式的术语中，组合盘表是通过计算双方的行星位置之间的中点，并创建一个全新的盘与新的行星位置和相位。比较盘和组合盘的相似之处在于，它们都探索关系。然而，组合盘表的不同之处在于它从两个单独的盘中创建一个全新的盘。组合盘是将两个个体作为一个整体来看待。因此，从视觉上看，它看起来就像一个出生盘，因为它只有一组行星。';
        $data['timesMidPoint']='时空中点星盘也称为戴维森盘（DavisonRelationshipChart）是一种组合两个星盘以获得一个第三个单独的星盘，描述了一个特定的动态关系——这张星盘有一个实际的出生日期和地点。与组合盘一样，戴维森图基于中点。但是，两者的计算方法相当不同的。戴维森图计算两个星盘两个出生日期之间的时间中点和时间以及纬度之间空间的中点。最终的结果是一个完整的出生描述两个人合并数据的星盘。戴维森关系星盘有日期、时间和位置——就像任何本命盘一样。这个代表戴维森图对于组合盘的主要优势。
时空中点盘的解读应该与本命盘类似，但区别在于仅考虑行星、相位和宫位。与组合盘一样，星座影响不大。角宫（1宫、4宫、7宫和10宫）是最重要的宫位，对于一对伴侣来说，当行星落入这些宫位时，这是一个更好的预兆。第5宫和第11宫有利于关系的发展，第2宫和第8宫有利于性欲和情感。第6到第12宫的轴有点棘手。相位的解释方式与本命盘相同，请记住，相位定义的是关系而不是个体。通常接受的容许度与本命盘中使用的容许度相同。
时空中点盘的优点与组合盘一样，是它以快速简单的方式显示关系的动态，几乎一目了然。在任何情况下，它都为星盘比较的经典合盘技术提供了额外和有趣的启示。';
        $data['marks']='马克思盘是马盘的全称，主要的作用是推测恋爱的双方在感情中彼此内心对这段感情看法的写照，以及双方对这段感情内心的真实状态。马盘能够反映一些内心的活动，例如恋爱双方对彼此的态度，对彼此的想法。';
        $data['compositeSecprogr']='组合次是用马克思盘做次限，主要的作用是推测特定时间段（适合较长时间范围，年为单位）将两个个体作为一个整体来看待，两人关系发展的状态。';
        $data['compositeThirprogr']='组合三是用马克思盘做次限，主要的作用是推测特定时间段（适合较短时间范围，月为单位）将两个个体作为一个整体来看待，两人关系发展的状态。';

        $data['timesMidPointSecprogr']='时空次是用时空盘做次限，主要的作用是推测特定时间段（适合较长时间范围，年为单位），将两个个体作为一个整体来看待，两人关系发展的状态。';
        $data['timesMidPointThirprogr']='时空三是用时空盘做次限，主要的作用是推测特定时间段（适合较短时间范围，月为单位），将两个个体作为一个整体来看待，两人关系发展的状态。';

        $data['marksSecprogr']='马盘次是用马克思盘做次限，主要的作用是推测特定时间段（适合较长时间范围，年为单位），恋爱的双方在感情中彼此内心对这段感情看法的写照。';
        $data['marksThirprogr']='马盘三是用马克思盘做次限，主要的作用是推测特定时间段（适合较短时间范围，年为单位），恋爱的双方在感情中彼此内心对这段感情看法的写照。';

        return $data[$chart_type];
    }


}
