<!-- START SECTION ABOUT -->
<link rel="stylesheet" href="__COMMON__/datetimepicker/css/datetimepicker.css">
<link rel="stylesheet" href="__COMMON__/datetimepicker/css/datetimepicker.css">
<link rel="stylesheet" href="__COMMON__/bootstrap-select/css/bootstrap-select.css">
<link href="__STATIC__/css/xingpan.css" rel="stylesheet" type="text/css">
<script src="__COMMON__/city/city.js"></script>
<script src="__COMMON__/city/picker.min.js"></script>
<script src="__COMMON__/bootstrap-select/js/bootstrap-select.js"></script>
<style>
    .dropdown-item.active,.dropdown-item:active{color:#fff;text-decoration:none;background-color:#007bff !important;}
    .bootstrap-select{border: 1px solid #ced4da !important;background-color: #ced4da !important;}
</style>
<!-- END SECTION ABOUT -->
<section class="small_pt small_pb">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <form method="post" class="form_style2" action="{:url('')}">
                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>时间<span class="required">*</span></label>
                            <input type="text" required class="form-control form_datetime" readonly name="birthday" value="">
                        </div>
                        <div class="form-group col-md-6">
                            <label>地区: </label>
                            <input class="form-control active" required type="text" readonly name="city" id="sel_city">
                        </div>
                        <div class="form-group col-md-4">
                            <label>经度: <span class="required">*</span></label>
                            <input type="text" value="" class="form-control longitude"  requiredrole="button" name="longitude" required="">
                        </div>
                        <div class="form-group col-md-4">
                            <label>纬度: <span class="required">*</span></label>
                            <input type="text" value="" class="form-control latitude" required name="latitude" required="">
                        </div>
                        <div class="form-group col-md-4">
                            <label>时区<span class="required">*</span></label>
                            <input class="form-control tz" required type="text" name="tz">
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-4">
                            <button title="查找" class="btn btn-default rounded-0 rps_name_sou" value="查找">查找</button>
                        </div>
                    </div>
                </form>
                    <div class="row mb-md-5 mb-3">
                        <div class="col-md-12">
                            <table class="table table-bordered table-hover table-striped"
                                   id="overview-section_ps">
                                <thead>
                                <tr class="success" style="font-weight:bold">
                                    <th class="header">
                                        星耀
                                    </th>
                                    <th class="header">
                                        黄道位置
                                    </th>
                                    <th class="header">
                                        白道位置
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="phase_list_html">
                                {notempty name='planetslist'}
                                {volist name="planetslist" id="vo"}
                                <tr>
                                    <td>
                                        {$vo.planet_chinese}
                                    </td>
                                    <td>
                                        {$vo['sign']['sign_chinese']}{$vo['sign']['deg']}º{$vo['sign']['deg']}
                                    </td>
                                    <td>
                                        {$vo["xiu"]['planet_chinese']}宿
                                    </td>
                                </tr>
                                {/volist}
                                {/notempty}
                                </tbody>
                            </table>
                        </div>
                    </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <h2 class="features-block-title margin-bottom-2x heading_s2"><img src="__STATIC__/images/western_astrology.png" alt="西方占星术" width="42"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">西方占星术</font></font></h2>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <div class="">
                    <h5>计算</h5>
                </div>
                <ul class="disc_list">
                    <li><span>行星位置</span></li>
                    <li><span>宫位计算</span></li>
                    <li><span>节点计算</span></li>
                    <li><span>月相计算</span></li>
                </ul>
            </div>
            <div class="col-md-3">
                <div class="">
                    <h5>流年行星计算</h5>
                </div>
                <ul class="disc_list">
                    <li><span>每日流年行星</span></li>
                    <li><span>每周流年行星</span></li>
                    <li><span>每月流年行星</span></li>
                    <li><span>流年行星逆行</span></li>
                    <li><span>本命与流年行星相位</span></li>
                </ul>
            </div>
            <div class="col-md-3">
                <div class="">
                    <h5>生活报告</h5>
                </div>
                <ul class="disc_list">
                    <li><span>上升报告</span></li>
                    <li><span>个性报告</span></li>
                    <li><span>浪漫个性报告</span></li>
                    <li><span>行星在迹象报告</span></li>
                    <li><span>房屋星球报告</span></li>
                    <li><span>友情报告</span></li>
                </ul>
            </div>
            <div class="col-md-3">
                <div class="">
                    <h5>星座运势预测</h5>
                </div>
                <ul class="disc_list">
                    <li><span>寿命预测报告</span></li>
                    <li><span>爱情预测报告</span></li>
                    <li><span>行星运输报告</span></li>
                    <li><span>情侣浪漫预测</span></li>
                    <li><span>爱的兼容性报告</span></li>
                    <li><span>每日，每周和每月预测</span></li>
                </ul>
            </div>
        </div>
    </div>
</section>
<div id="tip_sign_add">
</div>
<script src="__COMMON__/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});

        var nameEl = document.getElementById('sel_city');

        var first = []; /* 省，直辖市 */
        var second = []; /* 市 */
        var third = []; /* 镇 */

        var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */

        var checked = [0, 0, 0]; /* 已选选项 */

        function creatList(obj, list){
            obj.forEach(function(item, index, arr){
                var temp = new Object();
                temp.text = item.name;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }

        var picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });

        picker.on('picker.select', function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';

            nameEl.value = text1 + ' ' + text2 + ' ' + text3;
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0){
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
            longitude=city[selectedVal[0]]['sub'][selectedVal[1]]['sub'][selectedVal[2]]['longitude'];
            latitude=city[selectedVal[0]]['sub'][selectedVal[1]]['sub'][selectedVal[2]]['latitude'];
            tz=city[selectedVal[0]]['sub'][selectedVal[1]]['sub'][selectedVal[2]]['tz'];

            $(".longitude").val(longitude);
            $(".latitude").val(latitude);
            $(".tz").val(tz);
        });

        nameEl.addEventListener('click', function () {
            picker.show();
        });
    });
</script>