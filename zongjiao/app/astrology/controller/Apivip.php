<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\astrology\controller;

/**
 * 会员等级控制器
 */
class Apivip extends AstrologyBase
{

    /**
     * 会员等级列表
     */
    public function apiVipList()
    {

        $where = $this->logicApiVip->getWhere($this->param_data);

        $data=$this->logicApiVip->getApiVipList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 会员等级无分页列表
     */
    public function apiVipColumn()
    {

        $data=$this->logicApiVip->getApiVipColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 会员等级添加
     */
    public function apiVipAdd()
    {
	  
	   $regit=$this->logicApiVip->apiVipEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 会员等级删除
     */
    public function apiVipDel()
    {

       $regit=$this->logicApiVip->apiVipDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
