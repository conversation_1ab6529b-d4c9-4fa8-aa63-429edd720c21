<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\astrology\controller;

use app\common\error\wexc as WexcCoError;

/**
 * 基类控制器
 */
class UserBase extends ApiBase
{

    // 授权过的经纪人
    protected $authAgent = [];
    protected $param_data = [];

    /**
     * 构造方法
     */
    public function __construct()
    {
        parent::__construct();

        // 初始化模块常量
       // $this->initAdminConst();
        //$this->param['choose_appid']=explode("*#",$this->param['access_token'])[1];
        $this->param_data = $this->param;
        if(isset($this->param_data['user_token'])){
            if(is_array($this->param_data['user_token'])){
                $this->param_data['user_id']=$this->param_data['user_token']['data']->id;
            }
            unset($this->param_data['user_token']);
        }
        unset($this->param_data['access_token']);
    }

    /**
     * 初始化后台模块常量
     */
    final private function initAdminConst()
    {
        if (!agent_is_login()) {
            return WexcCoError::$UserIsNotLogin;
        }
    }

}
