<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\fkxp\logic;

use app\common\logic\LogicBase;

/**
 * Index基础逻辑
 */
class CommonBase extends LogicBase
{
    /**
     * 获取验证码
     */
    public function getSmsCode($parameter=[])
    {
        include_once EXTEND_PATH . 'Tencent/autoload.php';

        $cred = new \TencentCloud\Common\Credential("AKID1qPilYKZAUYtymtG4RqMftxnSqyf51KN", "SN8dszrS5TkBNIxNY7RWM1f4MUMxQ4Se");
        $httpProfile = new \TencentCloud\Common\Profile\HttpProfile();
        $httpProfile->setEndpoint("sms.tencentcloudapi.com");
        $clientProfile = new \TencentCloud\Common\Profile\ClientProfile();
        $clientProfile->setHttpProfile($httpProfile);
        $client = new \TencentCloud\Sms\V20210111\SmsClient($cred, "ap-guangzhou", $clientProfile);
        $req = new \TencentCloud\Sms\V20210111\Models\SendSmsRequest();
        $params = array(
            "PhoneNumberSet" =>$parameter['PhoneNumberSet'],
            "SmsSdkAppId" => "1400510357",
            "TemplateId" => $parameter['TemplateId'],
            "SignName" => "爱神星",
            "TemplateParamSet"=>$parameter['TemplateParamSet']
        );

        $req->fromJsonString(json_encode($params));

        $resp = $client->SendSms($req);

        $regi_id=$resp->serialize();

        if(!empty($regi_id['SendStatusSet']) and !empty($regi_id['SendStatusSet'][0]) and !empty($regi_id['SendStatusSet'][0]['Code'])){
            if($regi_id['SendStatusSet'][0]['Code']=='Ok'){
                if($parameter['type']=='code'){
                    cache('send_sms_code_'.$parameter['PhoneNumberSet'][0], $parameter['TemplateParamSet'][0], 300);
                }
                return ['code'=>'0','msg'=>'短信发送成功'];
            }else{
                return ['code'=>'105835','msg'=>$regi_id['SendStatusSet'][0]['Message']];
            }
        }else{
            return ['code'=>'105835','msg'=>'短信发送异常'];
        }
    }
    /**
     * 验证验证码
     */
    public function smsValidation($data)
    {
        $variables = getRedisHash('verifyCode', $data['mobile']);

        if (empty($data['sms_code']) || $variables != $data['sms_code']) {
            return [RESULT_ERROR, '短信验证码无效'];
        }
        cacheRedisHashInfoClean('verifyCode', $data['mobile']);

    }

    /**
     * 获取验证码
     */
    public function sentSmsCancelOrder($parameter=[])
    {

        // 短信发送
        $parameter['sign_name']      = '国信拍卖有限公司';
        $parameter['template_code']  = 'SMS_214225009';
        $parameter['notice']  = true;

        return $this->serviceSms->driverAlidy->sendSms($parameter);
    }
}
