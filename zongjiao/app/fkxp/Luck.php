<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\fkxp\controller;
use astrology\SweTest as SweTest;

/**
 * 翻咔公司 日月年运控制器
 */
class Luck extends ApiBase
{
    //日周月运
    public function overall()
    {
        $data['day'] = ['score' => "70", 'desc' => '你可能容易与人争论，生活中出现需要解决的事情。学会避免冲突，不要浪费精力在无用事上。独立完成任务，变得自律，保持专注。'];
        $data['week'] = ['score' => "90", 'desc' => '本周多pick对自己有利的选择，同时也要做好准备应对过往遗留的问题，并提醒自己保持乐观和坚韧。本周可能会有一些小额收入或意外之财，可以给家人、朋友购买礼物或犒劳自己。'];
        $data['month'] = ['score' => "90", 'desc' => '本月的重心会在人际的拓展上，感情会有意想不到的收获，但这需要你的敏锐细心。中下旬容易被烂桃花欺骗，对感情缺乏清晰的判断，沉溺于花言巧语中。年底可能会与前任重新建立联系。另外，需注意肌肤干燥和爆痘等皮肤问题。'];

        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $this->param['h_sys']='D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $life_birth_time = strtotime($param['birthday']);

        $param['tz'] = 8;
        $allow_degree['0'] = 5;
        $allow_degree['30'] = 2;
        $allow_degree['60'] = 5;
        $allow_degree['90'] = 5;
        $allow_degree['120'] = 2;
        $allow_degree['180'] = 5;

        $param['phase'] = $allow_degree;
        $param['planets'] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $param['virtual'] = ['10', '11'];
        $param['tz'] = 8;
        $param['svg_type'] = '-1';
        $param['h_sys'] = 'D';
        //本命
        $data1 = $this->logicNatal->plateData($param);

        if (empty($this->param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($this->param['date']);
        }

        //流年
        $param['birthday'] = date('Y-m-d H:i:s', $date_ime);
        $data2 = $this->logicNatal->plateData($param);
        // $data['data1']=$data1;
        // $data['data2']=$data2;


        $planer_day_sore[0] = 5;
        $planer_day_sore[1] = 1;
        $planer_day_sore[2] = 2;
        $planer_day_sore[3] = 4;
        $planer_day_sore[4] = 3;
        $planer_day_sore[5] = 2;
        $planer_day_sore[6] = 2;
        $planer_day_sore[7] = 1;
        $planer_day_sore[8] = 1;
        $planer_day_sore[9] = 1;

        $allow_day_sore[0] = 3;
        $allow_day_sore[60] = 1;
        $allow_day_sore[90] = -3;
        $allow_day_sore[120] = 2;
        $allow_day_sore[180] = -2;


        $planet_allow_degree_array = array();
        foreach ($data1['planet'] as $key => $value) {

            if (in_array($value['code_name'], [0, 1, 2, 3, 4])) {

                foreach ($data2['planet'] as $keyg => $valueg) {

                    $chazhi = abs($value['longitude'] - $valueg['longitude']);


                    $planet_degree_lgit = 0;

                    //如果第二星大于第一星值，相减绝对值小于180 那么就是第一追第二

                    foreach ($allow_degree as $keyAd => $valueAd) {

                        $valueAd += $planet_degree_lgit;

                        if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {


                            $planet_allow_degree = [
                                'code_name_one' => $value['code_name'],
                                'planet_english_one' => $value['planet_english'],
                                'planet_chinese_one' => $value['planet_chinese'],
                                'current_longitude_one' => $value['longitude'],

                                'code_name_two' => $valueg['code_name'],
                                'planet_english_two' => $valueg['planet_english'],
                                'planet_chinese_two' => $valueg['planet_chinese'],
                                'current_longitude_two' => $valueg['longitude'],
                                'allow' => $keyAd,
                                'cha_shu' => abs($chazhi - $keyAd)
                            ];


                            $planet_allow_degree_array[] = $planet_allow_degree;

                        }
                    }
                }
            }

        }
        array_multisort(array_column($planet_allow_degree_array, 'cha_shu'), SORT_ASC, $planet_allow_degree_array);
        //$data['planet_allow_degree']=$planet_allow_degree_array;
        $planet_allow_degree_array_two = array();
        foreach ($planet_allow_degree_array as $kefsd => $gddsf) {
            if (empty($planet_allow_degree_array_two[$gddsf['code_name_two']])) {
                $planet_allow_degree_array_two[$gddsf['code_name_two']] = $gddsf;
            }
        }
        ksort($planet_allow_degree_array_two);

        $day = date("j"); // 获取今天的日期（不带前置零的）
        $last_digit = intval(substr($day, -1)); // 获取日期的最后一位数字 
           

        $ri_yuliao = false;
        if (!empty($planet_allow_degree_array_two[1])) {
            $gddsfs = $planet_allow_degree_array_two[1];
            $title = $gddsfs['planet_chinese_two'] . $gddsfs['allow'] . $gddsfs['planet_chinese_one'].$last_digit;

            $getCorpusUserInfo = $this->logicCorpusUser->getCorpusUserInfo(['title' => $title, 'attribution_str' => '日月年运日运', 'attribution' => 'fkxp']);

            if (!empty($getCorpusUserInfo)) {
                $score = 70;
                if (!empty($allow_day_sore[$gddsfs['allow']]) and !empty($planer_day_sore[$gddsfs['code_name_one']]) and !empty($planer_day_sore[$gddsfs['code_name_two']])) {
                    $score = 70 + ($planer_day_sore[$gddsfs['code_name_one']] + $planer_day_sore[$gddsfs['code_name_two']]) * $allow_day_sore[$gddsfs['allow']];
                }
                $ri_yuliao = true;
				$score+=$last_digit;
                $data['day'] = ['score' => "$score", 'desc' => $getCorpusUserInfo['content2']];

            }
        }
        if (!$ri_yuliao) {
            foreach ($planet_allow_degree_array_two as $keyf => $vsdg) {
                $title = $vsdg['planet_chinese_two'] . $vsdg['allow'] . $vsdg['planet_chinese_one'];
                $getCorpusUserInfo = $this->logicCorpusUser->getCorpusUserInfo(['title' => $title, 'attribution_str' => '日月年运日运', 'attribution' => 'fkxp']);
                if (!empty($getCorpusUserInfo)) {
                    $score = 70;
                    if (!empty($allow_day_sore[$vsdg['allow']]) and !empty($planer_day_sore[$vsdg['code_name_one']]) and !empty($planer_day_sore[$vsdg['code_name_two']])) {
                        $score = 70 + ($planer_day_sore[$vsdg['code_name_one']] + $planer_day_sore[$vsdg['code_name_two']]) * $allow_day_sore[$vsdg['allow']];
                    }
                    $data['day'] = ['score' => "$score", 'desc' => $getCorpusUserInfo['content2']];
                }
            }
        }

//        foreach ($planet_allow_degree_array as $kefsd=>$gddsf){
//
//            if($gddsf['code_name_two']==1){
//                $title=$gddsf['planet_chinese_two'].$gddsf['allow'].$gddsf['planet_chinese_one'];
//                $getCorpusUserInfo=$this->logicCorpusUser->getCorpusUserInfo(['title'=>$title,'attribution_str'=>'日月年运日运','attribution'=>'fkxp']);
//
//                if(!empty($getCorpusUserInfo)){
//                    $score=70;
//                    if(!empty($allow_day_sore[$gddsf['allow']]) and !empty($planer_day_sore[$gddsf['code_name_one']]) and !empty($planer_day_sore[$gddsf['code_name_two']])){
//                        $score=70+($planer_day_sore[$gddsf['code_name_one']]+$planer_day_sore[$gddsf['code_name_two']])*$allow_day_sore[$gddsf['allow']];
//                    }
//
//                    $data['day']=['score'=>"$score",'desc'=>$getCorpusUserInfo['content2']];
//                    break;
//                }
//            }
//
//        }

 
        $sign_daa = $this->jisuansign(intval(date('m', $life_birth_time)), intval(date('d', $life_birth_time)));;
        //$sign_daa = $data1["planet"][0]["sign"]['sign_chinese'];

        $getCorpusUserColumn = $this->logicCorpusUser->getCorpusUserColumn(['attribution_str' => '日月年运', 'attribution' => 'fkxp']);


        foreach ($getCorpusUserColumn as $kyesf => $vdd) {
            if ($vdd['title'] == $sign_daa) {
                if ($vdd['content1'] == date('m', $date_ime) . '月月运') {
                    $data['month'] = ['score' => $vdd['content3'], 'desc' => $vdd['content2']];
                }
                $vdd['content2'] = str_replace("\n", "", $vdd['content2']);
                if (strpos($vdd['content1'], "-") !== false) {
                    $zhouyuntitle = rtrim($vdd['content1'], '周运');
                    $zhouyuntitle_array = explode("-", $zhouyuntitle);
                    $zhouyuntitle_array[0] = str_replace("月", "-", $zhouyuntitle_array[0]);
                    $zhouyuntitle_array[1] = str_replace("月", "-", $zhouyuntitle_array[1]);
                    $zhouyuntitle_array[0] = str_replace("日", "", $zhouyuntitle_array[0]);
                    $zhouyuntitle_array[1] = str_replace("日", "", $zhouyuntitle_array[1]);
                    $start_time = strtotime(date('Y') . '-' . $zhouyuntitle_array[0]);
                    $end_time = strtotime(date('Y') . '-' . $zhouyuntitle_array[1]);
                    if ($date_ime >= $start_time && $date_ime <= ($end_time+86400)) {
                        $data['week'] = ['score' => $vdd['content3'], 'desc' => $vdd['content2']];
                    }
                }
            }
        }

        // dump($sign_daa['sign_chinese']);
        // dump($getCorpusUserColumn);


        return $this->apiReturn($data);
    }
	public function jisuansign($m, $d)
    {
        $xingzuo = '';
        switch ($m) {
            case 1:
                if ($d < 20) {
                    $xingzuo = '摩羯';//
                } else {
                    $xingzuo = '水瓶'; //
                }
                break;
            case 2:
                if ($d < 19) {
                    $xingzuo = '水瓶';//
                } else {
                    $xingzuo = '双鱼';//
                }
                break;
            case 3:
                if ($d < 21) {
                    $xingzuo = '双鱼';//
                } else {
                    $xingzuo = '白羊';//
                }
                break;
            case 4:
                if ($d < 20) {
                    $xingzuo = '白羊';//
                } else {
                    $xingzuo = '金牛';//
                }
                break;
            case 5:
                if ($d < 21) {
                    $xingzuo = '金牛';//
                } else {
                    $xingzuo = '双子';//
                }
                break;
            case 6:
                if ($d < 22) {
                    $xingzuo = '双子';//
                } else {
                    $xingzuo = '巨蟹'; //
                }
                break;
            case 7:
                if ($d < 23) {
                    $xingzuo = '巨蟹'; //
                } else {
                    $xingzuo = '狮子';//
                }
                break;
            case 8:
                if ($d < 23) {
                    $xingzuo = '狮子';//
                } else {
                    $xingzuo = '处女';//
                }
                break;
            case 9:
                if ($d < 23) {
                    $xingzuo = '处女';//
                } else {
                    $xingzuo = '天秤';//
                }
                break;
            case 10:
                if ($d < 24) {
                    $xingzuo = '天秤';//
                } else {
                    $xingzuo = '天蝎'; //
                }
                break;
            case 11:
                if ($d < 23) {
                    $xingzuo = '天蝎'; //
                } else {
                    $xingzuo = '射手';//
                }
                break;
            case 12:
                if ($d < 22) {
                    $xingzuo = '射手';//
                } else {
                    $xingzuo = '摩羯'; //
                }
                break;
            default:
        }

        return $xingzuo; //

    }

    //年桃花运
    public function peach()
    {
        //开始计算流年数据


        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $this->param['h_sys']='D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        if (empty($this->param['year'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;

        $birthday = $param['birthday'];
        $time_zone = 8;
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;
        $current_year = date('Y');
        if (!empty($param['year'])) {
            $current_year = $param['year'];
        }

       $p = get_sington_object('SweTest', "astrology\\SweTest");

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 + $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arry = [
            'b' => $utdatenow,
            'p' => '0123456789m',
            'house' => $birth_points . ',D',
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $life_array = $p->calculate($life_arry,[0,1,2,3,4,5,6,7,8,9,'m']);
        $life_planet_array=$life_array['planet'];

        

        $year_arr = [
            'b' => '01.01.'.$current_year,
            'ut' => '12:30:30',
            'p' => '0123456789m',
            'n' => 365,
            'house' => $birth_points . ',D',
            's' => '1440m',
            'f' => 'PTls',  //名字 时间 度数经度 速度 宫位
            'g' => ',',
            'head',
            'roundsec',
        ];


        $life_planet_jin=$life_planet_array[3];
        $life_planet_tai=$life_planet_array[0];

        $life_planet_jin_longitude=$life_planet_jin['longitude'];

        if(intval(date('m',$life_birth_time))>=5 and intval(date('m',$life_birth_time))<=10){
            $life_planet_tai_longitude=$life_planet_tai['longitude']+180;
        }else{
            $life_planet_tai_longitude=$life_planet_tai['longitude'];
        }
        if ($life_planet_tai_longitude > 180) {
            $life_planet_tai_longitude = 360 - $life_planet_tai_longitude;
        }

        $allow_degree['0'] = 1;
        $allow_degree['60'] = 1;
        $allow_degree['90'] = 1;
        $allow_degree['120'] = 1;
        $allow_degree['180'] = 1;

        $allow_degree_sore['0'] = ['Sun'=>1,'Venus'=>1,'Mars'=>1];
        $allow_degree_sore['60'] = ['Sun'=>2,'Venus'=>3,'Mars'=>1];
        $allow_degree_sore['90'] = ['Sun'=>-3,'Venus'=>-2,'Mars'=>-4];
        $allow_degree_sore['120'] = ['Sun'=>4,'Venus'=>5,'Mars'=>3];
        $allow_degree_sore['180'] = ['Sun'=>-2,'Venus'=>-1,'Mars'=>-3];

        //火星金星120度	3
        //金星金星120度	5
        //太阳金星120度	4
        //火星金星60度	1
        //金星金星60度	3
        //太阳金星60度	2
        //火星金星0度	1
        //金星金星0度	1
        //太阳金星0度	1
        //火星金星90度	-4
        //金星金星90度	-2
        //太阳金星90度	-3
        //火星金星180度	-3
        //金星金星180度	-1
        //太阳金星180度	-2

        $planet_json_liu = $p->SweTest($year_arr);
        /// [
        //            "Sun",
        //            "01.01.202312:30:30UT",
        //            "280.8150050",
        //            "1.0190138",
        //            1672576230
        //        ]
        $fan_cha_du=9999;
        $planet_Venus_allow_degree=array();
        $planet_date_sore=array();
        foreach ($planet_json_liu as $key => $value) {
            $planetinfo = explode(',', str_replace(' ', '', $value));
            if(in_array($planetinfo[0],['Sun','Venus','Mars'])){
                $planetinfo[4] = strtotime(str_replace("UT", "UTC", $planetinfo[1]));
                $data_fff[]=$planetinfo;
                $chazhi = abs(intval($planetinfo[2]) - $life_planet_jin_longitude);
                if ($chazhi > 180) {
                    $chazhi = 360 - $chazhi;
                }
                if(abs($life_planet_tai_longitude-intval($planetinfo[2]))<$fan_cha_du){
                    $fan_cha_du=abs($life_planet_tai_longitude-intval($planetinfo[2]));
                    $tai_time=$planetinfo[4];
                }
                foreach ($allow_degree as $keyAd => $valueAd) {
                    if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {
                        $planet_allow_degree = [
                            'code_name' => $planetinfo[0],
                            'code_name_ben' => 'Venus',
                            'allow' => $keyAd,
                            'time' => $planetinfo[4]
                        ];
                        $planet_Venus_allow_degree[] = $planet_allow_degree;

                        empty($planet_date_sore[date('m',$planetinfo[4])]) && $planet_date_sore[date('m',$planetinfo[4])]=0;
                        $planet_date_sore[date('m',$planetinfo[4])]+=$allow_degree_sore[$keyAd][$planetinfo[0]];
                    }
                    if($planetinfo[0]=='Mars' and $keyAd==120){

                    }
                }
            }
        }
        asort($planet_date_sore);

        $utdatenow = date('d.m.Y', $tai_time);
        $utnow = date('H:i:s', $tai_time);
        $life_tai_arry = [
            'b' => $utdatenow,
            'p' => '01234',
            'house' => $birth_points . ',D',
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $life_tai_array = $p->calculate($life_tai_arry,[0,1,2,3,4]);
        $life_tai_planet_array=$life_tai_array['planet'];

        return $this->apiReturn($life_tai_planet_array);
    }

    //年运
    public function year()
    {
        $birthday = $this->param['birthday'];
        $time_zone = $this->param['tz'];
        $birth_points = $this->param['longitude'] . ',' . $this->param['latitude'];
        $is_summer = 0;
        $current_date = date('Y-m-d');
        if (!empty($this->param['transitday'])) {
            $current_date = date('Y-m-d', strtotime($this->param['transitday']));
        }

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 + $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789m',
            'house' => $birth_points . ',D',
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $chazhit = strtotime($current_date) - $time_zone * 3600 + $is_summer * 3600 - $life_birth_time;
        $prog_timedd = $chazhit / (365.2422 * 86400);
        $new_birth_time = $life_birth_time + $prog_timedd * 86400;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points . ',D',
            'ut' => date('H:i:s', $new_birth_time),
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $data = $this->logicYearLuck->basicData($life_arr, $new_conditions, $new_birth_time);

        $data['corpus'][] = $this->chartSolarreTurn->explainData(['birthday' => $birthday,
            'birth_points' => $birth_points,
            'is_summer' => $is_summer,
            'time_zone' => $time_zone,
            'current_date' => $current_date,
            'h_sys' => 'P']);

        return $this->apiReturn($data);
    }

    //年运
    public function constellationRandom()
    {
$data['day'] = ['score' => "70", 'desc' => '你可能容易与人争论，生活中出现需要解决的事情。学会避免冲突，不要浪费精力在无用事上。独立完成任务，变得自律，保持专注。'];
        $data['week'] = ['score' => "90", 'desc' => '本周多pick对自己有利的选择，同时也要做好准备应对过往遗留的问题，并提醒自己保持乐观和坚韧。本周可能会有一些小额收入或意外之财，可以给家人、朋友购买礼物或犒劳自己。'];
        $data['month'] = ['score' => "90", 'desc' => '本月的重心会在人际的拓展上，感情会有意想不到的收获，但这需要你的敏锐细心。中下旬容易被烂桃花欺骗，对感情缺乏清晰的判断，沉溺于花言巧语中。年底可能会与前任重新建立联系。另外，需注意肌肤干燥和爆痘等皮肤问题。'];

        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $this->param['h_sys']='D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $life_birth_time = strtotime($param['birthday']);

        $param['tz'] = 8;
        $allow_degree['0'] = 5;
        $allow_degree['30'] = 2;
        $allow_degree['60'] = 5;
        $allow_degree['90'] = 5;
        $allow_degree['120'] = 2;
        $allow_degree['180'] = 5;

        $param['phase'] = $allow_degree;
        $param['planets'] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $param['virtual'] = ['10', '11'];
        $param['tz'] = 8;
        $param['svg_type'] = '-1';
        $param['h_sys'] = 'D';
        //本命
        $data1 = $this->logicNatal->plateData($param);

        if (empty($this->param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($this->param['date']);
        }

        //流年
        $param['birthday'] = date('Y-m-d H:i:s', $date_ime);
        $data2 = $this->logicNatal->plateData($param);
        // $data['data1']=$data1;
        // $data['data2']=$data2;


        $planer_day_sore[0] = 5;
        $planer_day_sore[1] = 1;
        $planer_day_sore[2] = 2;
        $planer_day_sore[3] = 4;
        $planer_day_sore[4] = 3;
        $planer_day_sore[5] = 2;
        $planer_day_sore[6] = 2;
        $planer_day_sore[7] = 1;
        $planer_day_sore[8] = 1;
        $planer_day_sore[9] = 1;

        $allow_day_sore[0] = 3;
        $allow_day_sore[60] = 1;
        $allow_day_sore[90] = -3;
        $allow_day_sore[120] = 2;
        $allow_day_sore[180] = -2;


        $planet_allow_degree_array = array();
        foreach ($data1['planet'] as $key => $value) {

            if (in_array($value['code_name'], [0, 1, 2, 3, 4])) {

                foreach ($data2['planet'] as $keyg => $valueg) {

                    $chazhi = abs($value['longitude'] - $valueg['longitude']);


                    $planet_degree_lgit = 0;

                    //如果第二星大于第一星值，相减绝对值小于180 那么就是第一追第二

                    foreach ($allow_degree as $keyAd => $valueAd) {

                        $valueAd += $planet_degree_lgit;

                        if ($chazhi < ($keyAd + $valueAd) and $chazhi > ($keyAd - $valueAd)) {


                            $planet_allow_degree = [
                                'code_name_one' => $value['code_name'],
                                'planet_english_one' => $value['planet_english'],
                                'planet_chinese_one' => $value['planet_chinese'],
                                'current_longitude_one' => $value['longitude'],

                                'code_name_two' => $valueg['code_name'],
                                'planet_english_two' => $valueg['planet_english'],
                                'planet_chinese_two' => $valueg['planet_chinese'],
                                'current_longitude_two' => $valueg['longitude'],
                                'allow' => $keyAd,
                                'cha_shu' => abs($chazhi - $keyAd)
                            ];


                            $planet_allow_degree_array[] = $planet_allow_degree;

                        }
                    }
                }
            }

        }
        array_multisort(array_column($planet_allow_degree_array, 'cha_shu'), SORT_ASC, $planet_allow_degree_array);
        //$data['planet_allow_degree']=$planet_allow_degree_array;
        $planet_allow_degree_array_two = array();
        foreach ($planet_allow_degree_array as $kefsd => $gddsf) {
            if (empty($planet_allow_degree_array_two[$gddsf['code_name_two']])) {
                $planet_allow_degree_array_two[$gddsf['code_name_two']] = $gddsf;
            }
        }
        ksort($planet_allow_degree_array_two);

        $day = date("j",$date_ime); // 获取今天的日期（不带前置零的）
        $last_digit = intval(substr($day, -1)); // 获取日期的最后一位数字 
           

        $ri_yuliao = false;
        if (!empty($planet_allow_degree_array_two[1])) {
            $gddsfs = $planet_allow_degree_array_two[1];
            $title = $gddsfs['planet_chinese_two'] . $gddsfs['allow'] . $gddsfs['planet_chinese_one'].$last_digit;

            $getCorpusUserInfo = $this->logicCorpusUser->getCorpusUserInfo(['title' => $title, 'attribution_str' => '日月年运日运', 'attribution' => 'fkxp']);

            if (!empty($getCorpusUserInfo)) {
                $score = 70;
                if (!empty($allow_day_sore[$gddsfs['allow']]) and !empty($planer_day_sore[$gddsfs['code_name_one']]) and !empty($planer_day_sore[$gddsfs['code_name_two']])) {
                    $score = 70 + ($planer_day_sore[$gddsfs['code_name_one']] + $planer_day_sore[$gddsfs['code_name_two']]) * $allow_day_sore[$gddsfs['allow']];
                }
                $ri_yuliao = true;
				$score+=$last_digit;
                $data['day'] = ['score' => "$score", 'desc' => $getCorpusUserInfo['content2']];

            }
        }
        if (!$ri_yuliao) {
            foreach ($planet_allow_degree_array_two as $keyf => $vsdg) {
                $title = $vsdg['planet_chinese_two'] . $vsdg['allow'] . $vsdg['planet_chinese_one'];
                $getCorpusUserInfo = $this->logicCorpusUser->getCorpusUserInfo(['title' => $title, 'attribution_str' => '日月年运日运', 'attribution' => 'fkxp']);
                if (!empty($getCorpusUserInfo)) {
                    $score = 70;
                    if (!empty($allow_day_sore[$vsdg['allow']]) and !empty($planer_day_sore[$vsdg['code_name_one']]) and !empty($planer_day_sore[$vsdg['code_name_two']])) {
                        $score = 70 + ($planer_day_sore[$vsdg['code_name_one']] + $planer_day_sore[$vsdg['code_name_two']]) * $allow_day_sore[$vsdg['allow']];
                    }
                    $data['day'] = ['score' => "$score", 'desc' => $getCorpusUserInfo['content2']];
                }
            }
        }

 
 
        $sign_daa = $this->jisuansign(intval(date('m', $life_birth_time)), intval(date('d', $life_birth_time)));;
        //$sign_daa = $data1["planet"][0]["sign"]['sign_chinese'];

        $getCorpusUserInfoW = $this->logicCorpusUser->getCorpusUserInfo(['title' => $sign_daa,'content1' => intval(date("W",$date_ime)),'attribution_str' => '日月年运周运新', 'attribution' => 'fkxp']);
        $getCorpusUserInfoM = $this->logicCorpusUser->getCorpusUserInfo(['title' => $sign_daa,'content1' => intval(date("m",$date_ime)),'attribution_str' => '日月年运月运新', 'attribution' => 'fkxp']);
 
		if(!empty($getCorpusUserInfoW)){
			$data['week'] = ['score' => $getCorpusUserInfoW['content3'], 'desc' => $getCorpusUserInfoW['content2']];
		}

		if(!empty($getCorpusUserInfoM)){
			$data['month'] = ['score' => $getCorpusUserInfoM['content3'], 'desc' => $getCorpusUserInfoM['content2']];
		}
      
        return $this->apiReturn($data);

    }
}
