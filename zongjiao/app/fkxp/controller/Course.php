<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace corpus\app\controller;
/**
 * 课程控制器
 */
class Course extends UserBase
{

    /**
     * 课程分类
     */
    public function category()
    {

        $where['status']=1;

        !empty($this->param_data['category_id']) && $where['category_id'] = ['in',$this->param_data['category_id']];



        $list=$this->logicCourseCategory->getCourseCategoryList($where, 'id,title,sort,status', 'sort desc', false);

        //$list['imgs']=$this->logicFile->getPictureListUrl($data['data'],'cover_id');

		return $this->apiReturn($list);
    }
    /**
     * 课程无分页列表
     */
    public function list()
    {

        $where=array();
        $where['status']=1;

        !empty($this->param_data['category_id']) && $where['category_id'] = ['in',$this->param_data['category_id']];
        !empty($this->param_data['is_hot']) && $where['is_hot'] = $this->param_data['is_hot'];
        !empty($this->param_data['search']) && $where['title'] = ['like','%'.$this->param_data['search'].'%'];
        !empty($this->param_data['teachers']) && $where['teachers'] = $this->param_data['teachers'];

        $list=$this->logicCourse->getCourseList($where, 'id,title,cover_id,cover_long,teachers,source,describe,price,original_price,count,views,sort', 'sort asc');

        foreach ($list as $key => &$value) {
            $value['describe'] = html_entity_decode($value['describe']);
            $value['cover_url'] = $this->logicFile->getPictureUrl($value['cover_id']);
            $value['cover_long_url'] = $this->logicFile->getPictureUrl($value['cover_long']);
        }

        return $this->apiReturn($list);
    }
    /**
     * 课程信息
     */
    public function info()
    {

        $user_id=$this->param_data['user_id'];

        $data['info']=$this->logicCourse->getCourseInfo(['id'=>$this->param_data['id']],'id,title,cover_id,teachers,source,describe,detaileds,price,original_price,count,views,sort,start_time');

        if(empty($data['info'])){
            return $this->apiReturn([API_CODE_NAME => 1000011, API_MSG_NAME => '数据不存在']);
        }
        $data['info']['describe'] = html_entity_decode($data['info']['describe']);
        $data['info']['detaileds'] = html_entity_decode($data['info']['detaileds']);
        //$this->logicCourse->courseEdit(['id'=>$this->param_data['id'],'views'=>$data['course']['views']+1]);


        $data['info']['isbuy']=0;

        $course_log_id=$this->logicCoursePayLog->getCoursePayLogSingleInfo(['user_id'=>$user_id,'c_id'=>$this->param_data['id'],'order_status'=>1,'status'=>1],'id');

        if(!empty($course_log_id)){
            $data['info']['isbuy']=1;
        }

        $data['info']['cover_url']=$this->logicFile->getPictureUrl($data['info']['cover_id']);

        $directory=$this->logicCourseDirectory->getCourseDirectoryList(['c_id'=>$this->param_data['id'],'status'=>1], 'id,title,pid,sort,title,type,duration,is_free,c_id,fileId,start_time', 'sort DESC',false);

        $id_sort=-999;
        foreach ($directory as $key=>$value){
            if($value['type']==0){
                $data['course'][$value['id']]=['id'=>$value['id'],'title'=>html_entity_decode($value['title']),'sort'=>$value['sort']];


                if($value['sort']>$id_sort){
                    $id_sort=$value['sort'];
                    $data['course_index']=$value['id'];
                }
            }

        }
        foreach ($directory as $key=>$value){
            if($value['type']>0 and !empty($data['course'][$value['pid']])){
                $value['start_time']=$data['info']['start_time'];
                $value['title']=html_entity_decode($value['title']);
                $data['course'][$value['pid']]['child'][]=$value;
            }
        }
        $data['is_evaluation']=0;
        $evaluation=$this->logicEvaluation->getEvaluationColumn(['user_id'=>$user_id,'pid'=>$this->param_data['id']]);
        $data['evaluation']=['list'=>[],'average'=>5,'oneself'=>[]];

        $star1=0;
        $star2=0;
        $star3=0;
        $star4=0;
        $star5=1;
        $starCount=0;
        $starSum=5;

        if(!empty($evaluation)){
            $data['is_evaluation']=1;
            foreach ($evaluation as $key=>&$value){
                $vfddf='star'.$value['star'];
                $$vfddf++;
                $starCount++;
                $starSum+=$value['star'];

                $UserInfo =  $this->logicUser->getUserInfo(['id'=>$value['user_id']], true);

                $value['nickname']=$UserInfo['nickname'];
                $value['headimgurl']=$UserInfo['headimgurl'];


                if($user_id==$value['user_id']){
                    $data['evaluation']['oneself']=$value;
                }
            }
            $data['evaluation']['list']=$evaluation;

        }
        if($starCount==0){
            $starCount=1;
        }
        $proportion['star1']=round($star1/$starCount,4);
        $proportion['star2']=round($star2/$starCount,4);
        $proportion['star3']=round($star3/$starCount,4);
        $proportion['star4']=round($star4/$starCount,4);
        $proportion['star5']=round($star5/$starCount,4);
        $data['evaluation']['average']=round($starSum/$starCount,2);

        $data['evaluation']['proportion']=$proportion;

        $this->logicCourse->setCourseIncDec(['id' => $this->param_data['id']], 'views');

        return $this->apiReturn($data);

    }
    /**
     * 课程播放
     */
    public function play()
    {

        $user_id=$this->param_data['user_id'];


        $directoryInfo=$this->logicCourseDirectory->getCourseDirectoryInfo(['id'=>$this->param_data['id'],'status'=>1,'type'=>1], 'id,title,pid,sort,title,type,duration,is_free,c_id,fileId,video_url,create_time', 'sort DESC',false);

        if(empty($directoryInfo)){
            return $this->apiReturn([API_CODE_NAME => 1000011, API_MSG_NAME => '数据不存在']);
        }
        if($directoryInfo['is_free']==0){
            $course_log_info=$this->logicCoursePayLog->getCoursePayLogSingleInfo(['user_id'=>$user_id,'c_id'=>$directoryInfo['c_id'],'order_status'=>1,'status'=>1]);
            if(empty($course_log_info)){
                return $this->apiReturn([API_CODE_NAME => 1000012, API_MSG_NAME => '您还未购买此课程']);
            }
        }
        return $this->apiReturn(['video_url'=>$directoryInfo['video_url']]);

    }
    /**
     * 课程课程发起订单
     */
    public function pay()
    {
        $course_id['id'] = $this->param_data['id'];

        $user_id = $this->param_data['user_id'];

        $courseInfo=$this->logicCourse->getCourseInfo($course_id);
        $userData = $this->logicUser->getUserInfo(['id' => $user_id], 'id,amount');

        if ($courseInfo['status'] == -1) {
            return $this->apiReturn([API_CODE_NAME => 1030101, API_MSG_NAME => '当前课程包已过期']);
        }else if ($courseInfo['status'] == 0) {
            return $this->apiReturn([API_CODE_NAME => 1030102, API_MSG_NAME => '当前课程包正在审核']);
        }else if ($courseInfo['status'] == -2) {
            return $this->apiReturn([API_CODE_NAME => 1030103, API_MSG_NAME => '当前课程包审核不通过']);
        }

        if($userData['amount']<$courseInfo['price']){
            return $this->apiReturn([API_CODE_NAME => 1030105, API_MSG_NAME =>'您的星币不足']);
        }

        $payLogInfo=$this->logicCoursePayLog->getCoursePayLogSingleInfo(['user_id'=>$user_id,'c_id'=>$course_id['id'],'order_status'=>1]);

        if($payLogInfo){
            return $this->apiReturn([API_CODE_NAME => 1030104, API_MSG_NAME => '您已经购买过']);
        }


        $courseData['c_id']= $course_id['id'];
        $courseData['amount']= $courseInfo['price'];
        $courseData['price']= $courseInfo['price'];
        $courseData['order_status']= 1;
        $courseData['user_id']= $this->param_data['user_id'];
        $courseData['order_status']= 1;
        $payLogPayId = $this->logicCoursePayLog->coursePayLogEdit($courseData);

        $amountLog['order_number'] = 'K' . time() . 'U' . $this->param_data['user_id'] . 'R' . mt_rand(100000, 999999);
        $amountLog['type'] = 1;
        $amountLog['pay_type'] = 0;
        $amountLog['trade_type'] = 'APP';
        $amountLog['user_id'] = $user_id;
        $amountLog['money'] = $courseInfo['price'];
        $amountLog['status'] = 1;
        $amountLog['new_amount'] = $userData['amount'] + $courseInfo['price'];
        $amountLog['remark'] = config('ext_config.paylogtype')[1];
        $this->logicAmountLog->amountLogEdit($amountLog);
        $this->logicUser->setUserIncDec(['id' => $user_id], 'amount', $amountLog['money'], 'setDec');

        $this->logicCourse->setCourseIncDec(['id' => $course_id['id']], 'number_orders');

        return $this->apiReturn($courseData);

    }
    /**
     * @return mixed课程评论
     */
    public function evaluation()
    {

        $data['type'] =1;
        (!empty($this->param_data['type'])) && $data['type'] = $this->param_data['type'];

        $data['pid']=$this->param_data['c_id'];
        $data['star']=$this->param_data['star'];
        $data['user_id']=$this->param_data['user_id'];
        if(!empty($this->param_data['content'])){
            $data['content']=$this->param_data['content'];
        }

        if($data['type']==1){
            $payLogInfo=$this->logicCoursePayLog->getCoursePayLogSingleInfo(['user_id'=>$data['user_id'],'c_id'=>$this->param_data['c_id'],'order_status'=>1]);
            if(empty($payLogInfo)){
                return $this->apiReturn([API_CODE_NAME => 1030104, API_MSG_NAME => '您还未购买此课程']);
            }
            $data['order_id']=$payLogInfo['id'];
        }else{
            (!empty($this->param_data['order_id'])) && $data['order_id'] = $this->param_data['order_id'];
        }
        $EvaluationInfo=$this->logicEvaluation->getEvaluationInfo($data);
        if($EvaluationInfo){
            return $this->apiReturn([API_CODE_NAME => 1030104, API_MSG_NAME => '您已经评论过']);
        }
        $id=$this->logicEvaluation->evaluationEdit($data);

        return $this->apiReturn(['id'=>$id]);
    }

    /**
     * @return mixed我的课程
     */
    public function myList()
    {
        $user_id=$this->param_data['user_id'];
        $payLogList=$this->logicCoursePayLog->getCoursePayLogList(['user_id'=>$user_id ,'order_status'=>1]);

        foreach ($payLogList as $key => &$value) {
            $value['cover_url'] = $this->logicFile->getPictureUrl($value['cover_id']);
        }

        return $this->apiReturn($payLogList);
    }

}
